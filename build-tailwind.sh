#!/bin/bash

echo "Building Tailwind CSS for Momentum..."
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Error: Node.js is not installed or not in PATH."
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed or not in PATH."
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "Error: package.json not found."
    echo "Please run this script from the root directory of the Momentum project."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install dependencies."
        exit 1
    fi
fi

# Build Tailwind CSS
echo "Building Tailwind CSS..."
npx tailwindcss -i ./src/input.css -o ./public/css/tailwind.css --minify
if [ $? -ne 0 ]; then
    echo "Error: Failed to build Tailwind CSS."
    exit 1
fi

echo
echo "Tailwind CSS built successfully!"
echo "Output file: public/css/tailwind.css"
echo

exit 0
