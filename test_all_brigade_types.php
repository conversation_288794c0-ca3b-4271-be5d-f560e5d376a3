<?php
/**
 * Test All Brigade Types
 * 
 * This script tests the creation of all brigade types.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/Project.php';
require_once 'src/models/Task.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';
require_once 'src/models/ProjectAgentAssignment.php';
require_once 'src/models/AegisDirectorProjectManager.php';
require_once 'src/models/TaskDependency.php';

// Initialize database
$db = Database::getInstance();

// Check database connection
if ($db) {
    echo "Database connection successful\n";
    
    // Create an instance of the AegisDirectorProjectManager
    $projectManager = new AegisDirectorProjectManager();
    
    // Test all brigade types
    $brigadeTypes = [
        'content_creation' => 'Content Creation Brigade',
        'lead_generation' => 'Lead Generation Brigade',
        'customer_support' => 'Customer Support Brigade',
        'data_analysis' => 'Data Analysis Brigade'
    ];
    
    $userId = 1;
    $deadline = date('Y-m-d', strtotime('+7 days'));
    
    foreach ($brigadeTypes as $brigadeType => $brigadeTitle) {
        echo "\n=== Testing {$brigadeTitle} ===\n";
        
        $projectName = "Test {$brigadeTitle}";
        $projectDescription = "This is a test project for the {$brigadeTitle}.";
        
        try {
            $projectId = $projectManager->createAgentArmyProject(
                $userId,
                $brigadeType,
                $projectName,
                $projectDescription,
                $deadline
            );
            
            if ($projectId) {
                echo "✅ {$brigadeTitle} created successfully with ID: {$projectId}\n";
                
                // Get the tasks for the project
                $taskModel = new Task();
                $tasks = $taskModel->getProjectTasks($projectId);
                
                echo "Tasks created (" . count($tasks) . "):\n";
                foreach ($tasks as $index => $task) {
                    echo ($index + 1) . ". {$task['title']} (Priority: {$task['priority']})\n";
                }
            } else {
                echo "❌ Failed to create {$brigadeTitle}\n";
            }
        } catch (Exception $e) {
            echo "Exception caught: " . $e->getMessage() . "\n";
        }
    }
} else {
    echo "Database connection failed\n";
}
