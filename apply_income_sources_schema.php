<?php
// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Include the Database class
require_once BASE_PATH . '/src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

echo "Checking if income_sources table exists...\n";
$checkTable = $db->query("SHOW TABLES LIKE 'income_sources'");
$tableExists = $checkTable && $checkTable->rowCount() > 0;

if ($tableExists) {
    echo "income_sources table already exists.\n";
} else {
    echo "Creating income_sources table...\n";
    
    // Read the schema file
    $schema = file_get_contents(BASE_PATH . '/database/income_sources_schema.sql');
    
    // Split the schema into individual statements
    $statements = explode(';', $schema);
    
    // Execute each statement
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            $result = $db->query($statement);
            if ($result) {
                echo ".";
            } else {
                echo "x";
            }
        }
    }
    
    echo "\nSchema applied successfully!\n";
}

// Check if tables exist
$tables = [
    'income_sources',
    'income_source_transactions'
];

echo "\nChecking database tables:\n";
foreach ($tables as $table) {
    $result = $db->query("SHOW TABLES LIKE '{$table}'");
    $exists = $result && $result->rowCount() > 0;
    echo "- {$table}: " . ($exists ? "EXISTS" : "MISSING") . "\n";
    
    if ($exists) {
        // Check table structure
        $result = $db->query("DESCRIBE {$table}");
        $columns = $result->fetchAll(PDO::FETCH_COLUMN);
        echo "  Columns: " . implode(", ", $columns) . "\n";
    }
}

echo "\nDone!\n";
