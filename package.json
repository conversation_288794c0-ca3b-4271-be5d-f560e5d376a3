{"name": "momentum", "version": "1.0.0", "description": "ADHD-friendly personal management system", "main": "index.js", "scripts": {"build": "npx tailwindcss -i ./src/input.css -o ./public/css/tailwind.css", "watch": "npx tailwindcss -i ./src/input.css -o ./public/css/tailwind.css --watch", "build:prod": "npx tailwindcss -i ./src/input.css -o ./public/css/tailwind.css --minify", "js:build": "webpack --mode=development", "js:watch": "webpack --mode=development --watch", "js:prod": "webpack --mode=production", "dev": "npm run watch & npm run js:watch", "prod": "npm run build:prod && npm run js:prod"}, "keywords": ["adhd", "productivity", "management"], "author": "", "license": "ISC", "devDependencies": {"tailwindcss": "^3.3.3", "webpack": "^5.88.0", "webpack-cli": "^5.1.4", "terser-webpack-plugin": "^5.3.9", "css-loader": "^6.8.1", "mini-css-extract-plugin": "^2.7.6", "css-minimizer-webpack-plugin": "^5.0.1"}}