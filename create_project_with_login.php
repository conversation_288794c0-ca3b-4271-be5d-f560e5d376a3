<?php
/**
 * <PERSON><PERSON><PERSON> to create a project with login
 */

// Include necessary files
require_once __DIR__ . '/src/utils/Session.php';
require_once __DIR__ . '/src/utils/Database.php';
require_once __DIR__ . '/src/models/User.php';
require_once __DIR__ . '/src/models/Project.php';
require_once __DIR__ . '/src/models/Task.php';

// Start session
Session::start();

// Function to log in
function login($email, $password) {
    $userModel = new User();
    $user = $userModel->authenticate($email, $password);
    
    if ($user) {
        // Set user in session
        Session::setUser($user);
        
        // Regenerate session ID for security
        Session::regenerate();
        
        echo "Logged in successfully as " . $user['name'] . " (ID: " . $user['id'] . ")\n";
        return true;
    } else {
        echo "Login failed: Invalid email or password\n";
        return false;
    }
}

// Function to create project
function createProject($userId) {
    // Read project description from file
    $description = file_get_contents(__DIR__ . '/project_description.txt');
    
    // Prepare project data
    $projectData = [
        'user_id' => $userId,
        'name' => 'ADHD-Friendly Project Planning Guide',
        'description' => $description,
        'start_date' => date('Y-m-d'),
        'end_date' => date('Y-m-d', strtotime('+3 months')),
        'status' => 'planning',
        'is_template' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    // Create project
    $projectModel = new Project();
    $projectId = $projectModel->create($projectData);
    
    if ($projectId) {
        echo "Project created successfully with ID: " . $projectId . "\n";
        return $projectId;
    } else {
        echo "Failed to create project\n";
        return false;
    }
}

// Function to add tasks to project
function addTasksToProject($projectId, $userId) {
    $taskModel = new Task();
    
    // Define tasks based on our project plan
    $tasks = [
        [
            'title' => '1. Project Structure and Organization',
            'description' => 'Set up the initial project structure and organization framework.',
            'priority' => 'high',
            'status' => 'todo',
            'due_date' => date('Y-m-d', strtotime('+1 week'))
        ],
        [
            'title' => '2. Task Breakdown and Organization',
            'description' => 'Implement hierarchical task structure, prioritization system, and dependency management.',
            'priority' => 'high',
            'status' => 'todo',
            'due_date' => date('Y-m-d', strtotime('+2 weeks'))
        ],
        [
            'title' => '3. Time and Resource Planning',
            'description' => 'Develop realistic timelines, allocate resources, and create milestones.',
            'priority' => 'medium',
            'status' => 'todo',
            'due_date' => date('Y-m-d', strtotime('+3 weeks'))
        ],
        [
            'title' => '4. ADHD-Friendly Execution Strategies',
            'description' => 'Implement Current Focus integration, progress tracking, regular review, and distraction management.',
            'priority' => 'medium',
            'status' => 'todo',
            'due_date' => date('Y-m-d', strtotime('+4 weeks'))
        ],
        [
            'title' => '5. Documentation and Communication',
            'description' => 'Set up project documentation, team communication, and knowledge capture systems.',
            'priority' => 'low',
            'status' => 'todo',
            'due_date' => date('Y-m-d', strtotime('+5 weeks'))
        ]
    ];
    
    $taskIds = [];
    
    foreach ($tasks as $taskData) {
        // Add project and user IDs
        $taskData['project_id'] = $projectId;
        $taskData['user_id'] = $userId;
        $taskData['created_at'] = date('Y-m-d H:i:s');
        $taskData['updated_at'] = date('Y-m-d H:i:s');
        
        // Create task
        $taskId = $taskModel->create($taskData);
        
        if ($taskId) {
            echo "Task created: " . $taskData['title'] . " (ID: " . $taskId . ")\n";
            $taskIds[] = $taskId;
        } else {
            echo "Failed to create task: " . $taskData['title'] . "\n";
        }
    }
    
    return $taskIds;
}

// Main execution
echo "Starting project creation process...\n";

// Check if already logged in
if (Session::isLoggedIn()) {
    $user = Session::getUser();
    echo "Already logged in as " . $user['name'] . " (ID: " . $user['id'] . ")\n";
} else {
    // Prompt for login credentials
    echo "Please enter your login credentials:\n";
    echo "Email: ";
    $email = trim(fgets(STDIN));
    echo "Password: ";
    $password = trim(fgets(STDIN));
    
    // Attempt login
    if (!login($email, $password)) {
        echo "Exiting due to login failure.\n";
        exit(1);
    }
}

// Get user ID
$user = Session::getUser();
$userId = $user['id'];

// Create project
$projectId = createProject($userId);

if ($projectId) {
    // Add tasks to project
    $taskIds = addTasksToProject($projectId, $userId);
    
    echo "\nProject creation completed successfully!\n";
    echo "Project ID: " . $projectId . "\n";
    echo "Number of tasks created: " . count($taskIds) . "\n";
    echo "\nYou can view your project at: http://localhost/momentum/projects/view/" . $projectId . "\n";
} else {
    echo "Project creation failed.\n";
    exit(1);
}
