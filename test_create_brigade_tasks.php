<?php
/**
 * Test Create Brigade Tasks
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/Project.php';
require_once 'src/models/Task.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';
require_once 'src/models/ProjectAgentAssignment.php';
require_once 'src/models/AegisDirectorProjectManager.php';
require_once 'src/models/TaskDependency.php';

// Initialize database
$db = Database::getInstance();

// Check database connection
if ($db) {
    echo "Database connection successful\n";

    // Create a test project directly
    $projectData = [
        'user_id' => 1,
        'name' => 'Test Brigade Tasks Project',
        'description' => 'This is a test project for testing the createBrigadeTasks method.',
        'start_date' => date('Y-m-d'),
        'end_date' => date('Y-m-d', strtotime('+7 days')),
        'status' => 'planning',
        'progress' => 0,
        'brigade_type' => 'content_creation',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];

    try {
        // Enable error reporting for PDO
        $db->getConnection()->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Insert the project
        $projectId = $db->insert('projects', $projectData);

        if ($projectId) {
            echo "Project created successfully with ID: {$projectId}\n";

            // Create a custom AegisDirectorProjectManager with debugging
            class DebugAegisDirectorProjectManager extends AegisDirectorProjectManager {
                // Define models as protected so they can be accessed in this class
                protected $projectModel;
                protected $taskModel;
                protected $agentModel;
                protected $agentTaskModel;
                protected $interactionModel;
                protected $assignmentModel;

                public function __construct() {
                    parent::__construct();
                    // Initialize models again in this class since they're private in the parent
                    $this->projectModel = new Project();
                    $this->taskModel = new Task();
                    $this->agentModel = new AIAgent();
                    $this->agentTaskModel = new AIAgentTask();
                    $this->interactionModel = new AIAgentInteraction();
                    $this->assignmentModel = new ProjectAgentAssignment();
                }

                // Override createBrigadeTasks to add debugging
                public function createBrigadeTasks($projectId, $brigadeType, $userId) {
                    echo "Creating tasks for {$brigadeType} brigade...\n";

                    // Call parent method to create the tasks
                    $result = parent::createBrigadeTasks($projectId, $brigadeType, $userId);

                    // Get the tasks that were created
                    $tasks = $this->taskModel->getProjectTasks($projectId);
                    echo "Created " . count($tasks) . " tasks for the project:\n";

                    foreach ($tasks as $index => $task) {
                        echo ($index + 1) . ". {$task['title']} (Priority: {$task['priority']})\n";
                    }

                    return $result;
                }
            }

            // Create an instance of the debug project manager
            $projectManager = new DebugAegisDirectorProjectManager();

            // Call the createBrigadeTasks method
            $result = $projectManager->createBrigadeTasks($projectId, 'content_creation', 1);

            if ($result) {
                echo "Successfully created brigade tasks\n";
            } else {
                echo "Failed to create brigade tasks\n";
            }
        } else {
            echo "Failed to create project. Error info:\n";
            print_r($db->getConnection()->errorInfo());
        }
    } catch (Exception $e) {
        echo "Exception caught: " . $e->getMessage() . "\n";
        echo "Stack trace: " . $e->getTraceAsString() . "\n";
    }
} else {
    echo "Database connection failed\n";
}
