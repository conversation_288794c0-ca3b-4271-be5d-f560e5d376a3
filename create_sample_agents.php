<?php
/**
 * Create Sample AI Agents
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Load database
require_once BASE_PATH . '/src/utils/Database.php';

// Load models
require_once BASE_PATH . '/src/models/AIAgent.php';
require_once BASE_PATH . '/src/models/AIAgentCategory.php';
require_once BASE_PATH . '/src/models/AIAgentTask.php';
require_once BASE_PATH . '/src/models/AIAgentInteraction.php';

// Initialize models
$agentModel = new AIAgent();
$categoryModel = new AIAgentCategory();
$taskModel = new AIAgentTask();
$interactionModel = new AIAgentInteraction();

// Get categories
$categories = $categoryModel->getUserCategories(1);
$categoryMap = [];
foreach ($categories as $category) {
    $categoryMap[$category['name']] = $category['id'];
}

// Sample agents data
$sampleAgents = [
    [
        'name' => 'Research Assistant',
        'category' => 'Research Agents',
        'description' => 'Specialized in gathering information from various sources and providing comprehensive research reports.',
        'capabilities' => 'Web research, Data analysis, Information synthesis, Report generation',
        'personality_traits' => 'Thorough, Analytical, Detail-oriented, Objective',
        'intelligence_level' => 9,
        'efficiency_rating' => 8.5,
        'reliability_score' => 9.2,
        'status' => 'active'
    ],
    [
        'name' => 'Content Creator',
        'category' => 'Creative Agents',
        'description' => 'Creates engaging and original content for various platforms and purposes.',
        'capabilities' => 'Blog writing, Social media content, Email newsletters, Creative storytelling',
        'personality_traits' => 'Creative, Adaptable, Engaging, Expressive',
        'intelligence_level' => 8,
        'efficiency_rating' => 7.8,
        'reliability_score' => 8.5,
        'status' => 'active'
    ],
    [
        'name' => 'Task Manager',
        'category' => 'Productivity Agents',
        'description' => 'Helps organize, prioritize, and track tasks to improve productivity and time management.',
        'capabilities' => 'Task prioritization, Time management, Progress tracking, Deadline monitoring',
        'personality_traits' => 'Organized, Efficient, Proactive, Supportive',
        'intelligence_level' => 7,
        'efficiency_rating' => 9.0,
        'reliability_score' => 9.5,
        'status' => 'active'
    ],
    [
        'name' => 'Code Assistant',
        'category' => 'Technical Agents',
        'description' => 'Provides coding assistance, debugging help, and technical solutions.',
        'capabilities' => 'Code generation, Debugging, Technical documentation, Best practices guidance',
        'personality_traits' => 'Logical, Precise, Methodical, Problem-solver',
        'intelligence_level' => 9,
        'efficiency_rating' => 8.7,
        'reliability_score' => 9.0,
        'status' => 'active'
    ],
    [
        'name' => 'Email Composer',
        'category' => 'Communication Agents',
        'description' => 'Drafts professional and effective emails for various purposes and audiences.',
        'capabilities' => 'Email drafting, Professional communication, Tone adjustment, Follow-up management',
        'personality_traits' => 'Articulate, Professional, Diplomatic, Clear',
        'intelligence_level' => 8,
        'efficiency_rating' => 8.2,
        'reliability_score' => 8.8,
        'status' => 'active'
    ],
    [
        'name' => 'Data Analyst',
        'category' => 'Research Agents',
        'description' => 'Analyzes complex data sets to extract insights and create visualizations.',
        'capabilities' => 'Data analysis, Statistical modeling, Data visualization, Insight generation',
        'personality_traits' => 'Analytical, Precise, Methodical, Insightful',
        'intelligence_level' => 9,
        'efficiency_rating' => 8.9,
        'reliability_score' => 9.3,
        'status' => 'training'
    ],
    [
        'name' => 'Social Media Manager',
        'category' => 'Communication Agents',
        'description' => 'Manages social media presence, creates content, and engages with audience.',
        'capabilities' => 'Content creation, Audience engagement, Trend analysis, Platform optimization',
        'personality_traits' => 'Engaging, Trendy, Responsive, Strategic',
        'intelligence_level' => 8,
        'efficiency_rating' => 8.0,
        'reliability_score' => 8.5,
        'status' => 'inactive'
    ]
];

// Sample tasks data
$sampleTasks = [
    [
        'agent' => 'Research Assistant',
        'title' => 'Research ADHD productivity techniques',
        'description' => 'Compile a comprehensive report on the most effective productivity techniques for individuals with ADHD.',
        'priority' => 'high',
        'status' => 'in_progress',
        'due_date' => date('Y-m-d H:i:s', strtotime('+2 days'))
    ],
    [
        'agent' => 'Content Creator',
        'title' => 'Write blog post on time management',
        'description' => 'Create an engaging blog post about time management strategies for freelancers.',
        'priority' => 'medium',
        'status' => 'pending',
        'due_date' => date('Y-m-d H:i:s', strtotime('+3 days'))
    ],
    [
        'agent' => 'Task Manager',
        'title' => 'Organize project deadlines',
        'description' => 'Review all current projects and organize deadlines in priority order.',
        'priority' => 'urgent',
        'status' => 'pending',
        'due_date' => date('Y-m-d H:i:s', strtotime('+1 day'))
    ],
    [
        'agent' => 'Code Assistant',
        'title' => 'Debug authentication system',
        'description' => 'Identify and fix issues in the user authentication system.',
        'priority' => 'high',
        'status' => 'in_progress',
        'due_date' => date('Y-m-d H:i:s', strtotime('+2 days'))
    ],
    [
        'agent' => 'Email Composer',
        'title' => 'Draft client proposal email',
        'description' => 'Create a professional email to send a proposal to a potential client.',
        'priority' => 'medium',
        'status' => 'completed',
        'due_date' => date('Y-m-d H:i:s', strtotime('-1 day')),
        'completion_date' => date('Y-m-d H:i:s'),
        'success_rating' => 9
    ]
];

// Sample interactions data
$sampleInteractions = [
    [
        'agent' => 'Research Assistant',
        'type' => 'command',
        'content' => 'Research the latest studies on ADHD and productivity',
        'response' => 'I\'ve found 15 recent studies on ADHD and productivity. The most significant findings suggest that structured environments, regular breaks, and visual cues significantly improve productivity for individuals with ADHD.',
        'success' => true
    ],
    [
        'agent' => 'Content Creator',
        'type' => 'query',
        'content' => 'What are the best practices for engaging blog introductions?',
        'response' => 'The most engaging blog introductions typically use one of these approaches: ask a thought-provoking question, share a surprising statistic, tell a relevant story, or directly address a pain point. Would you like examples of each?',
        'success' => true
    ],
    [
        'agent' => 'Task Manager',
        'type' => 'command',
        'content' => 'Prioritize my tasks for today',
        'response' => 'I\'ve analyzed your tasks and prioritized them based on deadlines and importance. Your top 3 priorities for today are: 1) Complete client proposal, 2) Review project timeline, 3) Schedule team meeting.',
        'success' => true
    ],
    [
        'agent' => 'Code Assistant',
        'type' => 'query',
        'content' => 'How do I implement JWT authentication in PHP?',
        'response' => 'To implement JWT authentication in PHP, you can use the firebase/php-jwt library. Here\'s a step-by-step guide: 1) Install the library via Composer, 2) Generate a secret key, 3) Create token generation and validation functions...',
        'success' => true
    ],
    [
        'agent' => 'Email Composer',
        'type' => 'command',
        'content' => 'Draft a follow-up email to the client who hasn\'t responded in a week',
        'response' => 'Here\'s a draft follow-up email:\n\nSubject: Following Up on Our Previous Conversation\n\nDear [Client Name],\n\nI hope this email finds you well. I wanted to follow up on our discussion from last week regarding [project/proposal]. I understand you may be busy, but I wanted to check if you have any questions or if there\'s any additional information I can provide to help move forward.\n\nLooking forward to hearing from you.\n\nBest regards,\n[Your Name]',
        'success' => true
    ]
];

// Create agents
$agentIds = [];
echo "<h1>Creating Sample AI Agents</h1>";

foreach ($sampleAgents as $agentData) {
    $categoryId = $categoryMap[$agentData['category']] ?? null;
    
    $agentId = $agentModel->createAgent([
        'user_id' => 1,
        'category_id' => $categoryId,
        'name' => $agentData['name'],
        'description' => $agentData['description'],
        'capabilities' => $agentData['capabilities'],
        'personality_traits' => $agentData['personality_traits'],
        'intelligence_level' => $agentData['intelligence_level'],
        'efficiency_rating' => $agentData['efficiency_rating'],
        'reliability_score' => $agentData['reliability_score'],
        'status' => $agentData['status'],
        'last_active' => date('Y-m-d H:i:s')
    ]);
    
    if ($agentId) {
        $agentIds[$agentData['name']] = $agentId;
        echo "<p>Created agent: {$agentData['name']} (ID: $agentId)</p>";
    } else {
        echo "<p>Failed to create agent: {$agentData['name']}</p>";
    }
}

// Create tasks
echo "<h2>Creating Sample Tasks</h2>";

foreach ($sampleTasks as $taskData) {
    $agentId = $agentIds[$taskData['agent']] ?? null;
    
    if (!$agentId) {
        echo "<p>Agent not found: {$taskData['agent']}</p>";
        continue;
    }
    
    $taskId = $taskModel->createTask([
        'agent_id' => $agentId,
        'user_id' => 1,
        'title' => $taskData['title'],
        'description' => $taskData['description'],
        'priority' => $taskData['priority'],
        'status' => $taskData['status'],
        'due_date' => $taskData['due_date'],
        'completion_date' => $taskData['completion_date'] ?? null,
        'success_rating' => $taskData['success_rating'] ?? null
    ]);
    
    if ($taskId) {
        echo "<p>Created task: {$taskData['title']} (ID: $taskId)</p>";
    } else {
        echo "<p>Failed to create task: {$taskData['title']}</p>";
    }
}

// Create interactions
echo "<h2>Creating Sample Interactions</h2>";

foreach ($sampleInteractions as $interactionData) {
    $agentId = $agentIds[$interactionData['agent']] ?? null;
    
    if (!$agentId) {
        echo "<p>Agent not found: {$interactionData['agent']}</p>";
        continue;
    }
    
    $interactionId = $interactionModel->createInteraction([
        'agent_id' => $agentId,
        'user_id' => 1,
        'interaction_type' => $interactionData['type'],
        'content' => $interactionData['content'],
        'response' => $interactionData['response'],
        'success' => $interactionData['success']
    ]);
    
    if ($interactionId) {
        echo "<p>Created interaction for agent: {$interactionData['agent']} (ID: $interactionId)</p>";
    } else {
        echo "<p>Failed to create interaction for agent: {$interactionData['agent']}</p>";
    }
}

echo "<h2>Sample Data Creation Complete!</h2>";
echo "<p><a href='/momentum/dashboard'>Go to Dashboard</a></p>";
echo "<p><a href='/momentum/ai-agents'>Go to AI Agents Dashboard</a></p>";
