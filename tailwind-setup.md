# Tailwind CSS Setup for Momentum

This document explains how to set up and build Tailwind CSS for the Momentum project.

## Initial Setup

1. Make sure you have Node.js installed on your system.
2. Run the following command to install the required dependencies:

```bash
npm install
```

## Building Tailwind CSS

### Development Build

To build Tailwind CSS for development:

```bash
npm run build
```

### Watch Mode

To automatically rebuild Tailwind CSS when files change:

```bash
npm run watch
```

### Production Build

To build a minified version of Tailwind CSS for production:

```bash
npm run build:prod
```

## File Structure

- `tailwind.config.js` - Configuration file for Tailwind CSS
- `src/input.css` - Input CSS file with Tailwind directives
- `public/css/tailwind.css` - Generated output CSS file

## Updating Tailwind Configuration

If you need to modify the Tailwind configuration, edit the `tailwind.config.js` file. After making changes, rebuild the CSS using one of the commands above.

## Adding Custom Styles

To add custom styles that should be processed by Tailwind:

1. Edit the `src/input.css` file
2. Add your styles within the appropriate `@layer` directive
3. Rebuild the CSS

Example:

```css
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded;
  }
}
```

## Troubleshooting

If you encounter issues with the Tailwind build:

1. Make sure all dependencies are installed (`npm install`)
2. Check that the paths in `tailwind.config.js` are correct
3. Verify that the input and output file paths in `package.json` scripts are correct
4. Try deleting `node_modules` and reinstalling (`npm install`)

## Notes for Production

For production environments:

1. Always use the minified production build (`npm run build:prod`)
2. Consider adding cache headers to the CSS file on your server
3. Make sure the CSS file is properly referenced in your HTML
