#!/usr/bin/env python
"""
Pinterest Image Downloader Fix

This script downloads full-size images from Pinterest pins.
It takes a list of pin IDs or image URLs and downloads the full-size images.
"""

import os
import sys
import json
import time
import random
import requests
import argparse
from pathlib import Path
from urllib.parse import urlparse

def human_delay(min_seconds=0.5, max_seconds=1.5):
    """Add a human-like delay between requests"""
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)
    return delay

def convert_to_full_size_url(image_url):
    """
    Convert thumbnail URLs to full-size image URLs
    
    Pinterest uses different URL patterns for different image sizes:
    - 75x75_RS: Tiny thumbnail
    - 236x: Small thumbnail
    - 474x: Medium thumbnail
    - 736x: Large image
    - originals: Original image
    
    This function attempts to convert any URL to the largest available version
    """
    if not image_url:
        return None
        
    # Already a full-size URL
    if '/originals/' in image_url:
        return image_url
        
    # Handle 75x75_RS format
    if '75x75_RS' in image_url:
        # Extract the image ID from the URL
        parts = image_url.split('/')
        filename = parts[-1]
        
        # Construct a 736x URL (large image)
        return f"https://i.pinimg.com/736x/{filename[0:2]}/{filename[2:4]}/{filename[4:6]}/{filename}"
    
    # Handle other thumbnail formats (236x, 474x)
    for size in ['75x75', '150x150', '236x', '474x']:
        if f'/{size}/' in image_url:
            return image_url.replace(f'/{size}/', '/736x/')
    
    # If it's already a 736x image, try to get the original
    if '/736x/' in image_url:
        try:
            # Sometimes we can get the original by replacing 736x with originals
            original_url = image_url.replace('/736x/', '/originals/')
            response = requests.head(original_url, timeout=5)
            if response.status_code == 200:
                return original_url
        except:
            # If that fails, stick with the 736x version
            pass
    
    # Default to returning the original URL if we couldn't convert it
    return image_url

def download_image(image_url, output_dir, filename=None):
    """
    Download an image from a URL
    
    Args:
        image_url (str): URL of the image to download
        output_dir (str): Directory to save the image
        filename (str, optional): Filename to use for the saved image
        
    Returns:
        str: Path to the downloaded image, or None if download failed
    """
    if not image_url:
        print(json.dumps({
            "success": False,
            "message": "No image URL provided"
        }))
        return None
    
    # Convert to full-size URL
    full_size_url = convert_to_full_size_url(image_url)
    
    try:
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate filename if not provided
        if not filename:
            # Extract filename from URL or generate a timestamp-based name
            parsed_url = urlparse(full_size_url)
            url_filename = os.path.basename(parsed_url.path)
            if url_filename and '.' in url_filename:
                filename = f"{int(time.time())}_{url_filename}"
            else:
                filename = f"{int(time.time())}.jpg"
        
        # Full path to save the image
        output_path = os.path.join(output_dir, filename)
        
        # Download the image
        print(f"Downloading image from {full_size_url}")
        response = requests.get(full_size_url, stream=True, timeout=10)
        
        # Check if the request was successful
        if response.status_code == 200:
            # Get the content type to verify it's an image
            content_type = response.headers.get('Content-Type', '')
            if not content_type.startswith('image/'):
                print(json.dumps({
                    "success": False,
                    "message": f"URL does not point to an image. Content-Type: {content_type}"
                }))
                return None
            
            # Save the image
            with open(output_path, 'wb') as out_file:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        out_file.write(chunk)
            
            # Check if the file was saved and has content
            if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:  # >1KB
                print(json.dumps({
                    "success": True,
                    "message": f"Image downloaded successfully to: {output_path}",
                    "path": output_path
                }))
                return output_path
            else:
                print(json.dumps({
                    "success": False,
                    "message": f"Downloaded file is too small or empty: {os.path.getsize(output_path)} bytes"
                }))
                # Remove the empty or tiny file
                if os.path.exists(output_path):
                    os.remove(output_path)
                return None
        else:
            print(json.dumps({
                "success": False,
                "message": f"Failed to download image. Status code: {response.status_code}"
            }))
            return None
    
    except Exception as e:
        print(json.dumps({
            "success": False,
            "message": f"Error downloading image: {str(e)}"
        }))
        return None

def process_pin_data(pin_data, output_dir):
    """
    Process pin data and download images
    
    Args:
        pin_data (dict or str): Pin data as a dictionary or JSON string
        output_dir (str): Directory to save the images
        
    Returns:
        list: Paths to the downloaded images
    """
    # Parse pin data if it's a string
    if isinstance(pin_data, str):
        try:
            pins = json.loads(pin_data)
        except json.JSONDecodeError:
            print(json.dumps({
                "success": False,
                "message": "Invalid JSON data"
            }))
            return []
    else:
        pins = pin_data
    
    # Make sure pins is a list
    if not isinstance(pins, list):
        pins = [pins]
    
    # Download images for each pin
    downloaded_images = []
    for pin in pins:
        # Skip if no image URL
        if 'image_url' not in pin or not pin['image_url']:
            continue
        
        # Generate a filename based on pin ID and title
        pin_id = pin.get('pin_id', str(int(time.time())))
        title = pin.get('title', 'untitled').lower().replace(' ', '_')
        filename = f"{pin_id}_{title}.jpg"
        
        # Download the image
        image_path = download_image(pin['image_url'], output_dir, filename)
        if image_path:
            downloaded_images.append(image_path)
        
        # Add a delay between downloads
        human_delay(1.0, 2.0)
    
    return downloaded_images

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Pinterest Image Downloader Fix')
    parser.add_argument('--input', help='JSON file containing pin data or a single image URL')
    parser.add_argument('--output_dir', default='downloads', help='Directory to save the images')
    parser.add_argument('--image_url', help='URL of the image to download')
    
    args = parser.parse_args()
    
    # Create output directory
    output_dir = args.output_dir
    os.makedirs(output_dir, exist_ok=True)
    
    # Process input
    if args.image_url:
        # Download a single image
        download_image(args.image_url, output_dir)
    elif args.input:
        # Check if input is a file or a URL
        if os.path.isfile(args.input):
            # Read pin data from file
            with open(args.input, 'r') as f:
                pin_data = f.read()
            process_pin_data(pin_data, output_dir)
        else:
            # Assume input is a JSON string
            process_pin_data(args.input, output_dir)
    else:
        # Read from stdin
        pin_data = sys.stdin.read()
        process_pin_data(pin_data, output_dir)
