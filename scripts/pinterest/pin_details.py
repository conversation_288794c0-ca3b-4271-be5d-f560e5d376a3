#!/usr/bin/env python
"""
Simple Pinterest Pin Details Script
"""

import sys
import json
import time
import random

# Get pin ID from command line arguments
pin_id = sys.argv[1]
chrome_profile = sys.argv[2] if len(sys.argv) > 2 else None

# Create a simulated pin result
pin_data = {
    "id": pin_id,
    "pin_id": pin_id,
    "pin_url": f"https://www.pinterest.com/pin/{pin_id}/",
    "title": f"Pinterest Pin {pin_id}",
    "description": f"This is a simulated pin with ID: {pin_id}",
    "image_url": f"https://via.placeholder.com/600x800/f8f9fa/dc3545?text=Pinterest+Pin+{pin_id}",
    "board_name": "Pinterest Board",
    "save_count": random.randint(50, 5000),
    "comment_count": random.randint(0, 50),
    "created_at": time.strftime("%Y-%m-%d %H:%M:%S")
}

# Return the pin data
print(json.dumps(pin_data))