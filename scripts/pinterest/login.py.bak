from py3pin.Pinterest import Pinterest
import json
import sys
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# Get credentials from command line arguments
email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]
cred_root = sys.argv[4]
chrome_profile = sys.argv[5] if len(sys.argv) > 5 else None

# Configure Chrome options
chrome_options = Options()
if chrome_profile:
    # Use a specific Chrome profile if provided
    chrome_options.add_argument(f"--user-data-dir={chrome_profile}")
else:
    # Otherwise use a clean profile
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")

# Create a custom browser function that uses our Chrome options
def custom_browser():
    try:
        # Try to use the ChromeDriverManager to get the correct driver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        return driver
    except Exception as e:
        print(f"Error creating custom browser: {e}")
        # Fallback to default browser
        return None

# Create Pinterest instance with custom browser
pinterest = Pinterest(
    email=email,
    password=password,
    username=username,
    cred_root=cred_root
)

try:
    # Try to login
    pinterest.login()
    print(json.dumps({"success": True, "message": "Login successful"}))
except Exception as e:
    print(json.dumps({"success": False, "message": str(e)}))