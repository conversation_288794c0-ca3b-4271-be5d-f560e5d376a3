#!/usr/bin/env python
"""
Simple Chrome Profile Fix Script
"""

import sys
import json
import os

# Get Chrome profile path from command line arguments
chrome_profile = sys.argv[1]

# Check if Chrome profile exists
if os.path.exists(chrome_profile):
    # Return success
    print(json.dumps({
        "success": True,
        "message": "Chrome profile exists"
    }))
else:
    # Return error
    print(json.dumps({
        "success": False,
        "message": f"Chrome profile does not exist: {chrome_profile}"
    }))