#!/usr/bin/env python
"""
Enhanced Pinterest Scraper

This script combines Selenium, Requests, and Beautiful Soup to extract real data from Pinterest.
It provides more reliable data extraction and better error handling.
"""

import sys
import os
import json
import time
import random
import argparse
import traceback
from datetime import datetime
import urllib.request
import urllib.parse
from pathlib import Path
import math

# Check if required packages are installed, if not install them
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
except ImportError:
    import subprocess
    print("Installing Selenium...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "selenium"])
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriver<PERSON>ait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException

try:
    from webdriver_manager.chrome import ChromeDriverManager
except ImportError:
    import subprocess
    print("Installing webdriver-manager...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "webdriver-manager"])
    from webdriver_manager.chrome import ChromeDriverManager

try:
    import requests
    from bs4 import BeautifulSoup
except ImportError:
    import subprocess
    print("Installing requests and beautifulsoup4...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "requests", "beautifulsoup4"])
    import requests
    from bs4 import BeautifulSoup

def human_delay(min_seconds=1.0, max_seconds=3.0, action_type="normal"):
    """
    Simulate human-like delays to avoid being detected as a bot

    Args:
        min_seconds (float): Minimum delay in seconds
        max_seconds (float): Maximum delay in seconds
        action_type (str): Type of action to determine delay range
            - "normal": Regular actions like page navigation
            - "typing": Typing text (shorter delays)
            - "thinking": Longer delays for complex actions
            - "micro": Very short delays between keystrokes

    Returns:
        float: The actual delay time used
    """
    if action_type == "typing":
        # Typing is usually faster
        min_seconds = min(min_seconds, 0.5)
        max_seconds = min(max_seconds, 1.5)
    elif action_type == "thinking":
        # Thinking takes longer
        min_seconds = max(min_seconds, 2.0)
        max_seconds = max(max_seconds, 5.0)
    elif action_type == "micro":
        # Very short delays for keystrokes
        min_seconds = 0.05
        max_seconds = 0.2

    # Add some randomness with a normal distribution
    # This makes the delays more human-like than uniform distribution
    mu = (min_seconds + max_seconds) / 2
    sigma = (max_seconds - min_seconds) / 6  # 99.7% of values within min-max
    delay = random.normalvariate(mu, sigma)

    # Ensure delay is within bounds
    delay = max(min_seconds, min(max_seconds, delay))

    # Sometimes add a small random extra delay (like a human distraction)
    if random.random() < 0.1:  # 10% chance
        delay += random.uniform(0.5, 1.5)

    time.sleep(delay)
    return delay

class EnhancedPinterestScraper:
    def __init__(self, chrome_profile=None, headless=False, human_like=True):
        """
        Initialize the Pinterest scraper

        Args:
            chrome_profile (str, optional): Path to Chrome profile directory
            headless (bool, optional): Whether to run Chrome in headless mode
            human_like (bool, optional): Whether to use human-like delays
        """
        self.chrome_profile = chrome_profile
        self.headless = headless
        self.human_like = human_like
        self.driver = None
        self.session = requests.Session()
        self.logged_in = False
        self.cookies = {}

    def __enter__(self):
        """Context manager entry point"""
        self.start_browser()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit point"""
        self.close_browser()

    def start_browser(self):
        """Start the Chrome browser"""
        options = Options()

        if self.headless:
            options.add_argument("--headless=new")

        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--window-size=1920,1080")
        options.add_argument("--disable-notifications")

        # Use existing Chrome profile if provided
        if self.chrome_profile:
            options.add_argument(f"--user-data-dir={self.chrome_profile}")

        # Add user agent to avoid detection
        options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

        # Initialize Chrome driver
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=options)
            self.driver.set_page_load_timeout(30)
        except Exception as e:
            print(json.dumps({
                "success": False,
                "message": f"Error starting Chrome: {str(e)}"
            }))
            raise

    def close_browser(self):
        """Close the Chrome browser"""
        if self.driver:
            self.driver.quit()
            self.driver = None

    def extract_cookies(self):
        """Extract cookies from Selenium and add them to the requests session"""
        if not self.driver:
            return False

        try:
            selenium_cookies = self.driver.get_cookies()
            self.cookies = {cookie['name']: cookie['value'] for cookie in selenium_cookies}
            self.session.cookies.update(self.cookies)
            return True
        except Exception as e:
            print(f"Error extracting cookies: {str(e)}")
            return False

    def is_logged_in(self):
        """
        Check if logged in to Pinterest

        Returns:
            bool: True if logged in, False otherwise
        """
        if not self.driver:
            return False

        try:
            # Go to Pinterest homepage
            self.driver.get("https://www.pinterest.com/")

            # Wait for page to load
            time.sleep(3)

            # Check if login button is present
            login_buttons = self.driver.find_elements(By.CSS_SELECTOR, "button[data-test-id='simple-login-button']")
            if login_buttons:
                return False

            # Check if user menu is present
            user_menu = self.driver.find_elements(By.CSS_SELECTOR, "div[data-test-id='header-profile']")
            if user_menu:
                self.logged_in = True
                self.extract_cookies()
                return True

            return False
        except Exception as e:
            print(json.dumps({
                "success": False,
                "message": f"Error checking login status: {str(e)}"
            }))
            return False

    def login(self, email, password):
        """
        Log in to Pinterest

        Args:
            email (str): Pinterest account email
            password (str): Pinterest account password

        Returns:
            bool: True if login successful, False otherwise
        """
        if not self.driver:
            self.start_browser()

        # Check if already logged in
        if self.is_logged_in():
            print(json.dumps({
                "success": True,
                "message": "Already logged in"
            }))
            return True

        try:
            # Go to Pinterest login page
            print("Navigating to Pinterest login page...")
            self.driver.get("https://www.pinterest.com/login/")

            if self.human_like:
                human_delay(2.0, 4.0, "normal")  # Wait like a human would after page load

            # Wait for login form to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "email"))
            )

            # Enter email with human-like typing
            print("Entering email...")
            email_input = self.driver.find_element(By.ID, "email")
            email_input.clear()

            if self.human_like:
                # Type email character by character with variable delays
                for char in email:
                    email_input.send_keys(char)
                    human_delay(0.05, 0.15, "micro")  # Short delay between keystrokes
                human_delay(0.5, 1.5, "normal")  # Pause after typing email
            else:
                email_input.send_keys(email)

            # Enter password with human-like typing
            print("Entering password...")
            password_input = self.driver.find_element(By.ID, "password")
            password_input.clear()

            if self.human_like:
                # Type password character by character with variable delays
                for char in password:
                    password_input.send_keys(char)
                    human_delay(0.05, 0.2, "micro")  # Short delay between keystrokes
                human_delay(0.5, 2.0, "normal")  # Pause after typing password
            else:
                password_input.send_keys(password)

            # Click login button
            print("Clicking login button...")
            login_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")

            if self.human_like:
                human_delay(0.5, 1.5, "normal")  # Brief pause before clicking

            login_button.click()

            # Wait for login to complete
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div[data-test-id='header-profile']"))
                )
                self.logged_in = True
                self.extract_cookies()
                print(json.dumps({
                    "success": True,
                    "message": "Login successful"
                }))
                return True
            except TimeoutException:
                # Check if there's an error message
                error_messages = self.driver.find_elements(By.CSS_SELECTOR, "div[data-test-id='loginError']")
                if error_messages:
                    error_message = error_messages[0].text
                    print(json.dumps({
                        "success": False,
                        "message": f"Login failed: {error_message}"
                    }))
                else:
                    print(json.dumps({
                        "success": False,
                        "message": "Login failed: Timeout waiting for login to complete"
                    }))
                return False
        except Exception as e:
            print(json.dumps({
                "success": False,
                "message": f"Error during login: {str(e)}"
            }))
            return False

    def search(self, query, scope="pins", limit=20):
        """
        Search Pinterest using both Selenium and Requests/BeautifulSoup

        Args:
            query (str): Search query
            scope (str): Search scope (pins, boards, users)
            limit (int): Maximum number of results

        Returns:
            list: Search results
        """
        if not self.driver:
            self.start_browser()

        try:
            # Go to Pinterest search page
            search_url = f"https://www.pinterest.com/search/{scope}/?q={urllib.parse.quote(query)}"
            print(f"Navigating to search URL: {search_url}")
            self.driver.get(search_url)

            if self.human_like:
                human_delay(2.0, 4.0, "normal")  # Wait like a human would after page load

            # Wait for search results to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div[data-test-id='pin']"))
            )

            # Extract cookies for requests
            self.extract_cookies()

            if self.human_like:
                human_delay(1.0, 2.0, "normal")  # Brief pause to look at results

            # Scroll to load more pins
            pins = []
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            scroll_attempts = 0
            max_scroll_attempts = max(1, min(10, limit // 5))  # Ensure at least 1 scroll attempt

            print(f"Starting to scroll for pins. Limit: {limit}, Max scroll attempts: {max_scroll_attempts}")

            while len(pins) < limit and scroll_attempts < max_scroll_attempts:
                # Scroll down with human-like behavior
                if self.human_like:
                    # Scroll in chunks like a human would
                    current_height = self.driver.execute_script("return window.pageYOffset")
                    total_height = self.driver.execute_script("return document.body.scrollHeight")

                    # Calculate a random scroll distance (humans don't always scroll to bottom)
                    scroll_distance = random.uniform(0.6, 1.0) * (total_height - current_height)

                    # Scroll in a more human-like way with multiple smaller scrolls
                    steps = random.randint(3, 8)
                    for step in range(steps):
                        step_distance = scroll_distance * (step + 1) / steps
                        self.driver.execute_script(f"window.scrollTo(0, {current_height + step_distance});")
                        human_delay(0.1, 0.3, "micro")  # Small delay between scroll steps
                else:
                    # Simple scroll to bottom
                    self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")

                scroll_attempts += 1
                print(f"Scroll attempt {scroll_attempts}/{max_scroll_attempts}")

                # Wait for new pins to load with variable timing
                if self.human_like:
                    human_delay(1.5, 3.5, "normal")
                else:
                    time.sleep(2)

                # Get pins
                pin_elements = self.driver.find_elements(By.CSS_SELECTOR, "div[data-test-id='pin']")

                # Extract pin data
                for pin_element in pin_elements:
                    if len(pins) >= limit:
                        break

                    try:
                        # Get pin ID and URL
                        pin_link = pin_element.find_element(By.CSS_SELECTOR, "a").get_attribute("href")
                        pin_id = pin_link.split("/pin/")[1].split("/")[0] if "/pin/" in pin_link else ""

                        # Skip if we already have this pin
                        if any(pin["pin_id"] == pin_id for pin in pins):
                            continue

                        # Get pin title
                        title_elements = pin_element.find_elements(By.CSS_SELECTOR, "div[data-test-id='pin-title']")
                        title = title_elements[0].text if title_elements else ""

                        # If no title found, try alternative selectors
                        if not title:
                            title_elements = pin_element.find_elements(By.CSS_SELECTOR, "h3")
                            title = title_elements[0].text if title_elements else ""

                        # If still no title, try another approach
                        if not title:
                            title_elements = pin_element.find_elements(By.CSS_SELECTOR, "[data-test-id='pinrep-title']")
                            title = title_elements[0].text if title_elements else "Untitled Pin"

                        # Get pin description
                        description_elements = pin_element.find_elements(By.CSS_SELECTOR, "div[data-test-id='pin-description']")
                        description = description_elements[0].text if description_elements else ""

                        # If no description found, try alternative selectors
                        if not description:
                            description_elements = pin_element.find_elements(By.CSS_SELECTOR, "[data-test-id='pinrep-description']")
                            description = description_elements[0].text if description_elements else ""

                        # Get pin image URL
                        image_elements = pin_element.find_elements(By.CSS_SELECTOR, "img")
                        image_url = ""
                        if image_elements:
                            # Try to get the highest resolution image
                            image_url = image_elements[0].get_attribute("src")
                            # Replace resolution in URL to get higher quality
                            if image_url and "x" in image_url:
                                image_url = image_url.replace("/236x/", "/736x/")

                        # If no image URL found, try to get it from srcset attribute
                        if not image_url and image_elements:
                            srcset = image_elements[0].get_attribute("srcset")
                            if srcset:
                                # Get the highest resolution image from srcset
                                srcset_parts = srcset.split(",")
                                if srcset_parts:
                                    last_part = srcset_parts[-1].strip()
                                    url_parts = last_part.split(" ")
                                    if url_parts and len(url_parts) > 1:
                                        image_url = url_parts[0]

                        # Create pin data
                        pin_data = {
                            "id": pin_id,
                            "pin_id": pin_id,
                            "pin_url": f"https://www.pinterest.com/pin/{pin_id}/",
                            "title": title,
                            "description": description,
                            "image_url": image_url,
                            "board_name": "Pinterest Board",  # Will be updated with get_pin_details
                            "save_count": 0,  # Will be updated with get_pin_details
                            "comment_count": 0,  # Will be updated with get_pin_details
                            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        }

                        pins.append(pin_data)
                    except Exception as e:
                        print(f"Error extracting pin data: {str(e)}")
                        continue

                # Check if we've reached the end of the page
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                last_height = new_height

            # Try to get more detailed information for each pin
            for i, pin in enumerate(pins):
                if i < min(5, len(pins)):  # Only get details for the first few pins to save time
                    try:
                        pin_details = self.get_pin_details(pin["pin_id"])
                        if pin_details:
                            pins[i].update(pin_details)
                    except Exception as e:
                        print(f"Error getting pin details: {str(e)}")

            print(json.dumps(pins))
            return pins
        except Exception as e:
            print(json.dumps({
                "success": False,
                "message": f"Error during search: {str(e)}",
                "traceback": traceback.format_exc()
            }))
            return []

    def get_pin_details(self, pin_id):
        """
        Get detailed information about a Pinterest pin using both Selenium and Requests/BeautifulSoup

        Args:
            pin_id (str): Pinterest pin ID

        Returns:
            dict: Pin details
        """
        if not self.driver:
            self.start_browser()

        try:
            # First try using requests with BeautifulSoup (faster)
            if self.cookies:
                try:
                    pin_url = f"https://www.pinterest.com/pin/{pin_id}/"
                    response = self.session.get(pin_url, timeout=10)

                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')

                        # Extract pin title
                        title = ""
                        title_tag = soup.select_one('h1')
                        if title_tag:
                            title = title_tag.text.strip()

                        # Extract pin description
                        description = ""
                        description_tag = soup.select_one('[data-test-id="pin-description"]')
                        if description_tag:
                            description = description_tag.text.strip()

                        # Extract board name
                        board_name = "Pinterest Board"
                        board_tag = soup.select_one('a[href*="/board/"]')
                        if board_tag:
                            board_name = board_tag.text.strip()

                        # Extract save count
                        save_count = 0
                        save_tag = soup.select_one('[data-test-id="pin-save-button"] span')
                        if save_tag:
                            save_text = save_tag.text.strip()
                            if 'k' in save_text.lower():
                                save_count = int(float(save_text.lower().replace('k', '')) * 1000)
                            elif 'm' in save_text.lower():
                                save_count = int(float(save_text.lower().replace('m', '')) * 1000000)
                            else:
                                try:
                                    save_count = int(save_text.replace(',', ''))
                                except:
                                    save_count = 0

                        # Extract image URL (high resolution)
                        image_url = ""
                        img_tags = soup.select('img[srcset]')
                        for img in img_tags:
                            if 'srcset' in img.attrs:
                                srcset = img['srcset']
                                # Get the highest resolution image
                                parts = srcset.split(',')
                                if parts:
                                    last_part = parts[-1].strip()
                                    url_parts = last_part.split(' ')
                                    if url_parts and len(url_parts) > 1:
                                        image_url = url_parts[0]

                        # If no high-res image found, try regular image
                        if not image_url:
                            img_tag = soup.select_one('img[src*="pinimg.com"]')
                            if img_tag and 'src' in img_tag.attrs:
                                image_url = img_tag['src']
                                # Try to get higher resolution
                                if "236x" in image_url:
                                    image_url = image_url.replace("236x", "736x")

                        return {
                            "title": title,
                            "description": description,
                            "board_name": board_name,
                            "save_count": save_count,
                            "image_url": image_url
                        }
                except Exception as e:
                    print(f"Error getting pin details with requests: {str(e)}")
                    # Fall back to Selenium

            # If requests failed or no cookies, use Selenium
            pin_url = f"https://www.pinterest.com/pin/{pin_id}/"
            self.driver.get(pin_url)

            # Wait for pin to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div[data-test-id='pin']"))
            )

            # Extract pin title
            title = ""
            title_elements = self.driver.find_elements(By.CSS_SELECTOR, "h1")
            if title_elements:
                title = title_elements[0].text

            # Extract pin description
            description = ""
            description_elements = self.driver.find_elements(By.CSS_SELECTOR, "div[data-test-id='pin-description']")
            if description_elements:
                description = description_elements[0].text

            # Extract board name
            board_name = "Pinterest Board"
            board_elements = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='/board/']")
            if board_elements:
                board_name = board_elements[0].text

            # Extract save count
            save_count = 0
            save_elements = self.driver.find_elements(By.CSS_SELECTOR, "div[data-test-id='pin-save-button'] span")
            if save_elements:
                save_text = save_elements[0].text
                if 'k' in save_text.lower():
                    save_count = int(float(save_text.lower().replace('k', '')) * 1000)
                elif 'm' in save_text.lower():
                    save_count = int(float(save_text.lower().replace('m', '')) * 1000000)
                else:
                    try:
                        save_count = int(save_text.replace(',', ''))
                    except:
                        save_count = 0

            # Extract image URL (high resolution)
            image_url = ""
            image_elements = self.driver.find_elements(By.CSS_SELECTOR, "img[srcset]")
            if image_elements:
                srcset = image_elements[0].get_attribute("srcset")
                if srcset:
                    # Get the highest resolution image
                    parts = srcset.split(',')
                    if parts:
                        last_part = parts[-1].strip()
                        url_parts = last_part.split(' ')
                        if url_parts and len(url_parts) > 1:
                            image_url = url_parts[0]

            # If no high-res image found, try regular image
            if not image_url:
                image_elements = self.driver.find_elements(By.CSS_SELECTOR, "img[src*='pinimg.com']")
                if image_elements:
                    image_url = image_elements[0].get_attribute("src")
                    # Try to get higher resolution
                    if "236x" in image_url:
                        image_url = image_url.replace("236x", "736x")

            return {
                "title": title,
                "description": description,
                "board_name": board_name,
                "save_count": save_count,
                "image_url": image_url
            }
        except Exception as e:
            print(json.dumps({
                "success": False,
                "message": f"Error getting pin details: {str(e)}",
                "traceback": traceback.format_exc()
            }))
            return None

    def download_image(self, image_url, output_path):
        """
        Download an image from Pinterest

        Args:
            image_url (str): URL of the image to download
            output_path (str): Path to save the image

        Returns:
            bool: True if download successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Try to download with requests first (with cookies)
            if self.cookies:
                try:
                    response = self.session.get(image_url, stream=True, timeout=10)
                    if response.status_code == 200:
                        with open(output_path, 'wb') as f:
                            for chunk in response.iter_content(chunk_size=8192):
                                f.write(chunk)
                        print(json.dumps({
                            "success": True,
                            "message": "Image downloaded successfully with requests",
                            "path": output_path
                        }))
                        return True
                except Exception as e:
                    print(f"Error downloading image with requests: {str(e)}")
                    # Fall back to urllib

            # If requests failed or no cookies, use urllib
            try:
                # Add headers to avoid 403 errors
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
                req = urllib.request.Request(image_url, headers=headers)
                with urllib.request.urlopen(req) as response, open(output_path, 'wb') as out_file:
                    data = response.read()
                    out_file.write(data)
                print(json.dumps({
                    "success": True,
                    "message": "Image downloaded successfully with urllib",
                    "path": output_path
                }))
                return True
            except Exception as e:
                print(f"Error downloading image with urllib: {str(e)}")
                # Fall back to Selenium screenshot

            # If all else fails, try to take a screenshot with Selenium
            if self.driver:
                try:
                    # Navigate to the image URL
                    self.driver.get(image_url)
                    time.sleep(2)

                    # Take a screenshot
                    self.driver.save_screenshot(output_path)
                    print(json.dumps({
                        "success": True,
                        "message": "Image downloaded successfully with Selenium screenshot",
                        "path": output_path
                    }))
                    return True
                except Exception as e:
                    print(f"Error downloading image with Selenium: {str(e)}")

            print(json.dumps({
                "success": False,
                "message": "Failed to download image after trying all methods"
            }))
            return False
        except Exception as e:
            print(json.dumps({
                "success": False,
                "message": f"Error downloading image: {str(e)}",
                "traceback": traceback.format_exc()
            }))
            return False


if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Enhanced Pinterest Scraper')
    parser.add_argument('command', choices=['login', 'search', 'pin_details', 'download_image', 'test'], help='Command to execute')
    parser.add_argument('--email', help='Pinterest account email')
    parser.add_argument('--password', help='Pinterest account password')
    parser.add_argument('--query', help='Search query')
    parser.add_argument('--scope', default='pins', help='Search scope (pins, boards, users)')
    parser.add_argument('--limit', type=int, default=5, help='Maximum number of results (default: 5)')
    parser.add_argument('--pin_id', help='Pinterest pin ID')
    parser.add_argument('--image_url', help='URL of the image to download')
    parser.add_argument('--output_path', help='Path to save the downloaded image')
    parser.add_argument('--chrome_profile', help='Path to Chrome profile directory')
    parser.add_argument('--headless', action='store_true', help='Run Chrome in headless mode')
    parser.add_argument('--no-human', dest='human_like', action='store_false', help='Disable human-like behavior')
    parser.set_defaults(human_like=True)

    args = parser.parse_args()

    try:
        with EnhancedPinterestScraper(args.chrome_profile, args.headless, args.human_like) as scraper:
            if args.command == 'login':
                if not args.email or not args.password:
                    print(json.dumps({
                        "success": False,
                        "message": "Missing arguments. Required: email, password"
                    }))
                else:
                    scraper.login(args.email, args.password)

            elif args.command == 'search':
                if not args.query:
                    print(json.dumps({
                        "success": False,
                        "message": "Missing arguments. Required: query"
                    }))
                else:
                    scraper.search(args.query, args.scope, args.limit)

            elif args.command == 'pin_details':
                if not args.pin_id:
                    print(json.dumps({
                        "success": False,
                        "message": "Missing arguments. Required: pin_id"
                    }))
                else:
                    pin_details = scraper.get_pin_details(args.pin_id)
                    print(json.dumps(pin_details))

            elif args.command == 'download_image':
                if not args.image_url or not args.output_path:
                    print(json.dumps({
                        "success": False,
                        "message": "Missing arguments. Required: image_url, output_path"
                    }))
                else:
                    scraper.download_image(args.image_url, args.output_path)

            elif args.command == 'test':
                # Run a comprehensive test of all functionality
                print(json.dumps({
                    "message": "Starting comprehensive test of Enhanced Pinterest Scraper",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }))

                # Test login if credentials provided
                login_success = False
                if args.email and args.password:
                    print(json.dumps({"message": "Testing login..."}))
                    login_success = scraper.login(args.email, args.password)
                else:
                    print(json.dumps({"message": "Skipping login test (no credentials provided)"}))
                    # Try to check if already logged in
                    login_success = scraper.is_logged_in()

                # Only continue tests if logged in
                if login_success:
                    # Test search with a small limit
                    test_query = args.query if args.query else "home decor"
                    test_limit = min(args.limit, 3)  # Use a very small limit for testing

                    print(json.dumps({"message": f"Testing search with query '{test_query}' and limit {test_limit}..."}))
                    search_results = scraper.search(test_query, args.scope, test_limit)

                    # Test pin details if search returned results
                    if search_results and len(search_results) > 0:
                        test_pin_id = search_results[0]["pin_id"]
                        print(json.dumps({"message": f"Testing pin details for pin ID {test_pin_id}..."}))
                        pin_details = scraper.get_pin_details(test_pin_id)

                        # Test image download if pin details returned an image URL
                        if pin_details and "image_url" in pin_details and pin_details["image_url"]:
                            image_url = pin_details["image_url"]
                            output_dir = os.path.join(os.getcwd(), "downloads")
                            os.makedirs(output_dir, exist_ok=True)
                            output_path = os.path.join(output_dir, f"test_image_{int(time.time())}.jpg")

                            print(json.dumps({"message": f"Testing image download from {image_url}..."}))
                            download_success = scraper.download_image(image_url, output_path)

                            if download_success:
                                print(json.dumps({
                                    "success": True,
                                    "message": f"Image downloaded successfully to {output_path}",
                                    "path": output_path
                                }))
                            else:
                                print(json.dumps({
                                    "success": False,
                                    "message": "Image download failed"
                                }))
                        else:
                            print(json.dumps({
                                "success": False,
                                "message": "No image URL found in pin details"
                            }))
                    else:
                        print(json.dumps({
                            "success": False,
                            "message": "Search returned no results"
                        }))
                else:
                    print(json.dumps({
                        "success": False,
                        "message": "Login failed, cannot continue with tests"
                    }))
    except Exception as e:
        print(json.dumps({
            "success": False,
            "message": f"Error: {str(e)}",
            "traceback": traceback.format_exc()
        }))