#!/usr/bin/env python
"""
Pinterest Pin Upload Script (Dummy Version)

This script simulates uploading a pin to Pinterest for testing purposes.
"""

import sys
import json
import time
import uuid
import os

# Get arguments from command line
if len(sys.argv) < 8:
    print(json.dumps({
        "success": False,
        "message": "Missing required arguments. Usage: upload_pin_dummy.py email password username cred_root board_id image_path title [description] [link]"
    }))
    sys.exit(1)

email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]
cred_root = sys.argv[4]
board_id = sys.argv[5]
image_path = sys.argv[6]
title = sys.argv[7]
description = sys.argv[8] if len(sys.argv) > 8 else ""
link = sys.argv[9] if len(sys.argv) > 9 else ""

try:
    # Check if the image exists
    if not os.path.exists(image_path):
        print(json.dumps({
            "success": False,
            "message": f"Image file not found: {image_path}"
        }))
        sys.exit(1)
    
    # Generate a random pin ID
    pin_id = str(uuid.uuid4())
    
    # Simulate a delay
    time.sleep(1)
    
    # Return success
    print(json.dumps({
        "success": True,
        "pin_id": pin_id,
        "message": f"Pin '{title}' uploaded successfully (simulated)"
    }))
except Exception as e:
    print(json.dumps({
        "success": False,
        "message": f"Error uploading pin: {str(e)}"
    }))
