import json
import sys
import os
import subprocess

# Get search parameters from command line arguments
email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]
cred_root = sys.argv[4]
scope = sys.argv[5]
query = sys.argv[6]
limit = int(sys.argv[7]) if len(sys.argv) > 7 else 20
chrome_profile = sys.argv[8] if len(sys.argv) > 8 else None

# Call the browser cookie Pinterest implementation
args = ["python", os.path.join(os.path.dirname(__file__), "browser_cookie_pinterest.py"), "search",
        "--query", query,
        "--scope", scope,
        "--limit", str(limit)]
if chrome_profile:
    args.extend(["--chrome_profile", chrome_profile])

result = subprocess.run(args, capture_output=True, text=True)
print(result.stdout)