#!/usr/bin/env python
"""
Pinterest Image Download Script (Dummy Version)

This script simulates downloading an image from Pinterest for testing purposes.
"""

import sys
import json
import time
import os
import shutil
import random

# Get arguments from command line
if len(sys.argv) < 3:
    print(json.dumps({
        "success": False,
        "message": "Missing required arguments. Usage: download_image_dummy.py image_url output_path"
    }))
    sys.exit(1)

image_url = sys.argv[1]
output_path = sys.argv[2]

try:
    # Create the output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Simulate a delay
    time.sleep(1)

    # Create a dummy image (copy a placeholder image or create a simple one)
    # For this example, we'll create a simple colored image using PIL
    try:
        from PIL import Image

        # Create a random colored image
        img = Image.new('RGB', (800, 600), color=(
            random.randint(0, 255),
            random.randint(0, 255),
            random.randint(0, 255)
        ))
        img.save(output_path)
    except ImportError:
        # If PIL is not available, try to copy a placeholder image
        placeholder_path = os.path.join(os.path.dirname(__file__), 'placeholder.txt')
        if os.path.exists(placeholder_path):
            # Read the placeholder text
            with open(placeholder_path, 'r') as f:
                placeholder_text = f.read()

            # Create a simple text file as a last resort
            with open(output_path, 'w') as f:
                f.write(placeholder_text)
        else:
            # Create a simple text file as a last resort
            with open(output_path, 'w') as f:
                f.write("This is a dummy image file for testing purposes.")

    # Return success
    print(json.dumps({
        "success": True,
        "message": f"Image downloaded successfully (simulated) to {output_path}"
    }))
except Exception as e:
    print(json.dumps({
        "success": False,
        "message": f"Error downloading image: {str(e)}"
    }))
