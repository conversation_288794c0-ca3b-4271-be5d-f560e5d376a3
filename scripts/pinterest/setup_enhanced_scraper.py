#!/usr/bin/env python
"""
Setup script for the Enhanced Pinterest Scraper

This script installs all required packages for the Enhanced Pinterest Scraper.
"""

import sys
import subprocess
import os
import platform

def install_package(package):
    """
    Install a Python package using pip
    
    Args:
        package (str): Package name
    """
    try:
        print(f"Installing {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"Successfully installed {package}")
        return True
    except Exception as e:
        print(f"Error installing {package}: {str(e)}")
        return False

def main():
    """
    Main function to install all required packages
    """
    print("Setting up Enhanced Pinterest Scraper...")
    
    # List of required packages
    packages = [
        "selenium",
        "webdriver-manager",
        "requests",
        "beautifulsoup4",
        "browser-cookie3"
    ]
    
    # Install each package
    success = True
    for package in packages:
        if not install_package(package):
            success = False
    
    if success:
        print("\nSetup completed successfully!")
        print("You can now use the Enhanced Pinterest Scraper.")
    else:
        print("\nSetup completed with errors.")
        print("Please check the error messages above and try to install the missing packages manually.")
    
    # Check if Chrome is installed
    print("\nChecking for Chrome installation...")
    chrome_installed = False
    
    if platform.system() == "Windows":
        chrome_paths = [
            os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"), "Google\\Chrome\\Application\\chrome.exe"),
            os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"), "Google\\Chrome\\Application\\chrome.exe"),
            os.path.join(os.environ.get("LOCALAPPDATA", "C:\\Users\\<USER>\\AppData\\Local"), "Google\\Chrome\\Application\\chrome.exe")
        ]
        
        for path in chrome_paths:
            if os.path.exists(path):
                chrome_installed = True
                print(f"Chrome found at: {path}")
                break
    elif platform.system() == "Darwin":  # macOS
        if os.path.exists("/Applications/Google Chrome.app"):
            chrome_installed = True
            print("Chrome found at: /Applications/Google Chrome.app")
    elif platform.system() == "Linux":
        try:
            result = subprocess.run(["which", "google-chrome"], capture_output=True, text=True)
            if result.returncode == 0:
                chrome_installed = True
                print(f"Chrome found at: {result.stdout.strip()}")
        except:
            pass
    
    if not chrome_installed:
        print("Chrome not found. Please install Google Chrome to use the Enhanced Pinterest Scraper.")
    
    print("\nSetup complete!")

if __name__ == "__main__":
    main()
