#!/usr/bin/env python
"""
Setup Selenium Pinterest Scraper

This script installs the required packages for the Selenium-based Pinterest scraper.
"""

import sys
import subprocess
import json

def install_package(package):
    """
    Install a Python package using pip
    
    Args:
        package (str): Package name
    
    Returns:
        bool: True if installation was successful, False otherwise
    """
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """
    Main function
    """
    # Print status
    print(json.dumps({
        "status": "Installing required packages...",
        "progress": 0
    }))
    
    # Install required packages
    packages = ["selenium", "webdriver-manager"]
    total_packages = len(packages)
    installed_packages = 0
    
    for package in packages:
        if install_package(package):
            installed_packages += 1
            print(json.dumps({
                "status": f"Installed {package}",
                "progress": int((installed_packages / total_packages) * 100)
            }))
        else:
            print(json.dumps({
                "status": f"Failed to install {package}",
                "progress": int((installed_packages / total_packages) * 100),
                "error": f"Failed to install {package}"
            }))
    
    # Check if all packages were installed
    if installed_packages == total_packages:
        print(json.dumps({
            "status": "All packages installed successfully",
            "progress": 100,
            "success": True
        }))
    else:
        print(json.dumps({
            "status": f"Installed {installed_packages}/{total_packages} packages",
            "progress": int((installed_packages / total_packages) * 100),
            "success": False
        }))

if __name__ == "__main__":
    main()
