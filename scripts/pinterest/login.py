import json
import sys
import os
import subprocess

# Get credentials from command line arguments
email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]
cred_root = sys.argv[4]
chrome_profile = sys.argv[5] if len(sys.argv) > 5 else None

# Call the browser cookie Pinterest implementation
args = ["python", os.path.join(os.path.dirname(__file__), "browser_cookie_pinterest.py"), "login",
        "--email", email,
        "--password", password,
        "--username", username,
        "--cred_root", cred_root]
if chrome_profile:
    args.extend(["--chrome_profile", chrome_profile])

result = subprocess.run(args, capture_output=True, text=True)
print(result.stdout)