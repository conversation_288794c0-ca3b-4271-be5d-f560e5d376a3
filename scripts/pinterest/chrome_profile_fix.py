#!/usr/bin/env python
"""
Chrome Profile Fix for Pinterest Authentication

This script helps fix issues with Chrome profiles for Pinterest authentication.
It creates a clean Chrome profile that can be used for Pinterest login.
"""

import os
import sys
import json
import shutil
import platform
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def create_clean_profile(profile_dir):
    """Create a clean Chrome profile for Pinterest authentication"""

    # Create the profile directory if it doesn't exist
    os.makedirs(profile_dir, exist_ok=True)

    # Configure Chrome options
    chrome_options = Options()
    chrome_options.add_argument(f"--user-data-dir={profile_dir}")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")

    # Create a Chrome driver with the options
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)

        # Navigate to Pinterest login page
        driver.get("https://www.pinterest.com/login/")

        # Wait for the page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )

        # Check if we're on the login page
        if "login" in driver.current_url.lower():
            print(json.dumps({
                "success": True,
                "message": "Chrome profile created successfully. Ready for login.",
                "profile_dir": profile_dir
            }))
        else:
            print(json.dumps({
                "success": True,
                "message": "Chrome profile created and already logged in to Pinterest.",
                "profile_dir": profile_dir
            }))

        # Close the browser
        driver.quit()
        return True
    except Exception as e:
        print(json.dumps({
            "success": False,
            "message": f"Error creating Chrome profile: {str(e)}",
            "profile_dir": profile_dir
        }))
        return False

def main():
    """Main function"""

    # Get the profile directory from command line arguments
    if len(sys.argv) > 1:
        profile_dir = sys.argv[1]
    else:
        # Create a default profile directory
        if platform.system() == "Windows":
            profile_dir = os.path.join(os.environ["USERPROFILE"], "AppData", "Local", "Pinterest", "ChromeProfile")
        elif platform.system() == "Darwin":  # macOS
            profile_dir = os.path.join(os.environ["HOME"], "Library", "Application Support", "Pinterest", "ChromeProfile")
        else:  # Linux
            profile_dir = os.path.join(os.environ["HOME"], ".pinterest", "chrome_profile")

    # Create the profile
    create_clean_profile(profile_dir)

if __name__ == "__main__":
    main()