#!/usr/bin/env python
"""
Simple Pinterest Download Image Script
"""

import sys
import json
import os
import urllib.request

# Get image URL and output path from command line arguments
image_url = sys.argv[1]
output_path = sys.argv[2]

try:
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Download the image
    urllib.request.urlretrieve(image_url, output_path)
    
    # Return success
    print(json.dumps({
        "success": True,
        "message": "Image downloaded successfully"
    }))
except Exception as e:
    # Return error
    print(json.dumps({
        "success": False,
        "message": f"Error downloading image: {str(e)}"
    }))