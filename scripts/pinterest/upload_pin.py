#!/usr/bin/env python
"""
Simple Pinterest Upload Pin <PERSON>
"""

import sys
import json
import time
import random

# Get pin data from command line arguments
board_id = sys.argv[1]
title = sys.argv[2]
description = sys.argv[3]
image_path = sys.argv[4]
link = sys.argv[5] if len(sys.argv) > 5 else None
chrome_profile = sys.argv[6] if len(sys.argv) > 6 else None

# Create a simulated pin result
pin_id = f"pin_{int(time.time())}_{random.randint(1000, 9999)}"
pin_data = {
    "id": pin_id,
    "pin_id": pin_id,
    "pin_url": f"https://www.pinterest.com/pin/{pin_id}/",
    "title": title,
    "description": description,
    "image_url": f"https://via.placeholder.com/600x800/f8f9fa/dc3545?text={title}",
    "board_name": "Pinterest Board",
    "board_id": board_id,
    "save_count": 0,
    "comment_count": 0,
    "created_at": time.strftime("%Y-%m-%d %H:%M:%S")
}

# Return the pin data
print(json.dumps(pin_data))