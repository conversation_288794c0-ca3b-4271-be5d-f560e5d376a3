<?php
/**
 * Test Pinterest Unofficial API Integration
 * 
 * This script tests the Pinterest integration using the unofficial API (py3-pinterest).
 */

// Include required files
require_once 'src/utils/Environment.php';
require_once 'src/api/PinterestAPI.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load environment variables
Environment::load();

// Output HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pinterest Unofficial API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1, h2, h3 { color: #333; }
        .test-section { margin-bottom: 30px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .pin-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 20px; margin-top: 20px; }
        .pin-card { border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
        .pin-image { width: 100%; height: 200px; object-fit: cover; }
        .pin-content { padding: 10px; }
        .pin-title { font-weight: bold; margin-bottom: 5px; }
        .pin-description { font-size: 0.9em; color: #666; margin-bottom: 10px; }
        .pin-meta { font-size: 0.8em; color: #999; }
        .pin-actions { display: flex; gap: 10px; margin-top: 10px; }
        .pin-button { display: inline-block; padding: 5px 10px; background: #e60023; color: white; text-decoration: none; border-radius: 4px; font-size: 0.8em; }
        .pin-button:hover { background: #d50c22; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; max-height: 300px; }
        .config-section { background: #f8f9fa; padding: 15px; border-left: 4px solid #17a2b8; margin-bottom: 20px; }
        .step { margin-bottom: 10px; padding: 10px; background: #f8f9fa; border-left: 4px solid #28a745; }
        .step.error { border-left-color: #dc3545; }
        .step.warning { border-left-color: #ffc107; }
        .log { font-family: monospace; background: #f5f5f5; padding: 10px; margin-top: 10px; max-height: 200px; overflow: auto; }
    </style>
</head>
<body>
    <h1>Test Pinterest Unofficial API Integration</h1>';

// Display current configuration
$email = Environment::get('PINTEREST_EMAIL', '');
$password = Environment::get('PINTEREST_PASSWORD', '');
$username = Environment::get('PINTEREST_USERNAME', '');

echo '<div class="config-section">
    <h2>Current Configuration</h2>
    <p><strong>Email:</strong> ' . htmlspecialchars($email) . '</p>
    <p><strong>Username:</strong> ' . htmlspecialchars($username) . '</p>
    <p><strong>Password:</strong> ' . (empty($password) ? '<span class="error">Not set</span>' : '<span class="success">Set</span>') . '</p>
</div>';

// Test 1: Check Python Installation
echo '<div class="test-section">
    <h2>Test 1: Check Python Installation</h2>';

$pythonVersion = shell_exec('python --version 2>&1');
if ($pythonVersion) {
    echo '<div class="step">
        <p class="success">Python is installed: ' . htmlspecialchars(trim($pythonVersion)) . '</p>
    </div>';
} else {
    echo '<div class="step error">
        <p class="error">Python is not installed or not in PATH</p>
        <p>Please install Python and make sure it\'s in your PATH</p>
    </div>';
}

// Test 2: Check Chrome Installation
echo '<h2>Test 2: Check Chrome Installation</h2>';

if (file_exists('C:/Program Files/Google/Chrome/Application/chrome.exe')) {
    echo '<div class="step">
        <p class="success">Chrome is installed at: C:/Program Files/Google/Chrome/Application/chrome.exe</p>
    </div>';
} else {
    echo '<div class="step error">
        <p class="error">Chrome is not found at the expected location</p>
        <p>Please install Chrome, which is required for py3-pinterest to handle login</p>
    </div>';
}

// Test 3: Check py3-pinterest Installation
echo '<h2>Test 3: Check py3-pinterest Installation</h2>';

$py3PinterestCheck = shell_exec('python -c "try: import py3pin; print(\'py3-pinterest is installed\'); except ImportError: print(\'py3-pinterest is not installed\')" 2>&1');
if (strpos($py3PinterestCheck, 'py3-pinterest is installed') !== false) {
    echo '<div class="step">
        <p class="success">py3-pinterest is installed</p>
    </div>';
} else {
    echo '<div class="step error">
        <p class="error">py3-pinterest is not installed</p>
        <p>Please install py3-pinterest using: pip install py3-pinterest</p>
    </div>';
}

// Test 4: Initialize Pinterest API
echo '<h2>Test 4: Initialize Pinterest API</h2>';

try {
    // Initialize the API
    $api = PinterestAPI::getInstance($email, $password, $username);
    
    echo '<div class="step">
        <p class="success">API initialized successfully</p>
    </div>';
} catch (Exception $e) {
    echo '<div class="step error">
        <p class="error">Error initializing API: ' . htmlspecialchars($e->getMessage()) . '</p>
    </div>';
}

// Test 5: Login to Pinterest
echo '<h2>Test 5: Login to Pinterest</h2>';

try {
    echo '<div class="step">
        <p>Attempting to login to Pinterest...</p>
        <p>This may take a moment as it needs to launch Chrome to handle the login process.</p>
        <p>If this is the first time, it may create a browser session.</p>
    </div>';
    
    // Start output buffering to capture any output
    ob_start();
    
    // Try to login
    $loginResult = $api->login();
    
    // Get any output
    $output = ob_get_clean();
    
    if ($loginResult) {
        echo '<div class="step">
            <p class="success">Login successful</p>';
        
        if (!empty($output)) {
            echo '<div class="log">' . htmlspecialchars($output) . '</div>';
        }
        
        echo '</div>';
    } else {
        echo '<div class="step error">
            <p class="error">Login failed</p>';
        
        if (!empty($output)) {
            echo '<div class="log">' . htmlspecialchars($output) . '</div>';
        }
        
        echo '</div>';
    }
} catch (Exception $e) {
    echo '<div class="step error">
        <p class="error">Error during login: ' . htmlspecialchars($e->getMessage()) . '</p>
    </div>';
}

// Test 6: Search for pins
echo '<h2>Test 6: Search for Pins</h2>';

try {
    $searchTerm = 'digital marketing';
    
    echo '<div class="step">
        <p>Searching for "' . htmlspecialchars($searchTerm) . '"...</p>
    </div>';
    
    // Start output buffering to capture any output
    ob_start();
    
    // Search for pins
    $pins = $api->search($searchTerm, 'pins', 10);
    
    // Get any output
    $output = ob_get_clean();
    
    if (!empty($pins)) {
        echo '<div class="step">
            <p class="success">Found ' . count($pins) . ' pins for search term "' . htmlspecialchars($searchTerm) . '"</p>';
        
        if (!empty($output)) {
            echo '<div class="log">' . htmlspecialchars($output) . '</div>';
        }
        
        // Display the pins
        echo '<div class="pin-grid">';
        foreach (array_slice($pins, 0, 4) as $pin) {
            echo '<div class="pin-card">
                <img src="' . htmlspecialchars($pin['image_url'] ?? 'https://via.placeholder.com/600x800/f8f9fa/dc3545?text=No+Image') . '" alt="' . htmlspecialchars($pin['title'] ?? 'Pinterest Pin') . '" class="pin-image">
                <div class="pin-content">
                    <div class="pin-title">' . htmlspecialchars($pin['title'] ?? 'Pinterest Pin') . '</div>
                    <div class="pin-description">' . htmlspecialchars(substr($pin['description'] ?? '', 0, 100)) . '...</div>
                    <div class="pin-meta">
                        <div>Board: ' . htmlspecialchars($pin['board_name'] ?? 'Pinterest Board') . '</div>
                        <div>Saves: ' . number_format($pin['save_count'] ?? 0) . '</div>
                    </div>
                    <div class="pin-actions">
                        <a href="' . htmlspecialchars($pin['pin_url'] ?? '') . '" target="_blank" class="pin-button">View on Pinterest</a>
                    </div>
                </div>
            </div>';
        }
        echo '</div>';
        
        // Show the raw data for the first pin
        echo '<h3>Raw data for first pin:</h3>';
        echo '<pre>' . htmlspecialchars(print_r($pins[0], true)) . '</pre>';
        
        echo '</div>';
    } else {
        echo '<div class="step error">
            <p class="error">No pins found for search term "' . htmlspecialchars($searchTerm) . '"</p>';
        
        if (!empty($output)) {
            echo '<div class="log">' . htmlspecialchars($output) . '</div>';
        }
        
        echo '</div>';
    }
} catch (Exception $e) {
    echo '<div class="step error">
        <p class="error">Error searching for pins: ' . htmlspecialchars($e->getMessage()) . '</p>
    </div>';
}

// Test 7: Download an image
echo '<h2>Test 7: Download Image</h2>';

try {
    $imageUrl = '';
    $pinId = '';
    
    if (!empty($pins)) {
        // Use the first pin from the search results
        $imageUrl = $pins[0]['image_url'] ?? '';
        $pinId = $pins[0]['pin_id'] ?? '';
    } else {
        // Use a fallback image URL
        $imageUrl = 'https://via.placeholder.com/600x800/f8f9fa/dc3545?text=Pinterest+Image';
        $pinId = 'placeholder';
    }
    
    echo '<div class="step">
        <p>Downloading image from: ' . htmlspecialchars($imageUrl) . '</p>
    </div>';
    
    // Create the output directory if it doesn't exist
    $outputDir = 'public/uploads/pinterest';
    if (!file_exists($outputDir)) {
        mkdir($outputDir, 0755, true);
    }
    
    $outputPath = $outputDir . '/test_' . $pinId . '.jpg';
    
    // Start output buffering to capture any output
    ob_start();
    
    // Download the image
    $downloadResult = $api->downloadImage($imageUrl, $outputPath);
    
    // Get any output
    $output = ob_get_clean();
    
    if ($downloadResult) {
        echo '<div class="step">
            <p class="success">Successfully downloaded image to: ' . htmlspecialchars($outputPath) . '</p>';
        
        if (!empty($output)) {
            echo '<div class="log">' . htmlspecialchars($output) . '</div>';
        }
        
        echo '<img src="' . htmlspecialchars($outputPath) . '" alt="Downloaded image" style="max-width: 300px; display: block; margin: 20px auto; border: 1px solid #ddd;">';
        
        echo '</div>';
    } else {
        echo '<div class="step error">
            <p class="error">Failed to download image</p>';
        
        if (!empty($output)) {
            echo '<div class="log">' . htmlspecialchars($output) . '</div>';
        }
        
        echo '</div>';
    }
} catch (Exception $e) {
    echo '<div class="step error">
        <p class="error">Error downloading image: ' . htmlspecialchars($e->getMessage()) . '</p>
    </div>';
}

// Server information
echo '<div class="test-section">
    <h2>Server Information</h2>
    <pre>';
echo 'PHP Version: ' . phpversion() . "\n";
echo 'Server Software: ' . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo 'User Agent: ' . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "\n";
echo 'Document Root: ' . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "\n";
echo 'Python Version: ' . trim($pythonVersion ?? 'Unknown') . "\n";
echo 'Chrome Installed: ' . (file_exists('C:/Program Files/Google/Chrome/Application/chrome.exe') ? 'Yes' : 'No') . "\n";
echo '</pre>
</div>';

echo '<div class="test-section">
    <h2>Next Steps</h2>
    <p>If all tests passed, you can now use the Pinterest clone from your dashboard.</p>
    <p>The integration will use the py3-pinterest library to fetch real Pinterest data.</p>
    <p>If you encountered any errors, please check the error messages and make sure Python, Chrome, and py3-pinterest are properly installed.</p>
</div>';

echo '</body>
</html>';
