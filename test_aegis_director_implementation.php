<?php
/**
 * Test Aegis Director Implementation
 *
 * This script tests the complete Aegis Director agent implementation.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/Project.php';
require_once 'src/models/Task.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';
require_once 'src/models/ProjectAgentAssignment.php';
require_once 'src/models/AegisDirectorProjectManager.php';
require_once 'src/models/TaskDependency.php';

// Initialize database
$db = Database::getInstance();

// Check database connection
if ($db) {
    echo "Database connection successful\n";

    // Create an instance of the AegisDirectorProjectManager
    $projectManager = new AegisDirectorProjectManager();

    // Test 1: Ensure Aegis Director Agent exists
    echo "\n=== Test 1: Ensure Aegis Director Agent exists ===\n";
    $userId = 1;
    $agent = $projectManager->ensureAegisDirectorAgent($userId);

    if ($agent) {
        echo "✅ Aegis Director agent exists or was created successfully\n";
        echo "Agent ID: {$agent['id']}\n";
        echo "Agent Name: {$agent['name']}\n";
        echo "Agent Description: {$agent['description']}\n";
    } else {
        echo "❌ Failed to ensure Aegis Director agent exists\n";
    }

    // Test 2: Create an AI Agent Army project
    echo "\n=== Test 2: Create an AI Agent Army project ===\n";
    $projectName = "Test Content Creation Brigade";
    $projectDescription = "This is a test project for the Content Creation Brigade.";
    $deadline = date('Y-m-d', strtotime('+7 days'));
    $brigadeType = "content_creation";

    try {
        $projectId = $projectManager->createAgentArmyProject(
            $userId,
            $brigadeType,
            $projectName,
            $projectDescription,
            $deadline
        );
    } catch (Exception $e) {
        echo "Exception caught: " . $e->getMessage() . "\n";
        echo "Stack trace: " . $e->getTraceAsString() . "\n";
        $projectId = false;
    }

    if ($projectId) {
        echo "✅ AI Agent Army project created successfully\n";
        echo "Project ID: {$projectId}\n";

        // Get the project details
        $projectModel = new Project();
        $project = $projectModel->getProjectDetails($projectId, $userId);

        if ($project) {
            echo "Project Name: {$project['name']}\n";
            echo "Project Description: {$project['description']}\n";
            echo "Project Brigade Type: {$project['brigade_type']}\n";
            echo "Project Deadline: {$project['end_date']}\n";
        }

        // Get the tasks for the project
        $taskModel = new Task();
        $tasks = $taskModel->getProjectTasks($projectId);

        echo "\nProject Tasks (" . count($tasks) . "):\n";
        foreach ($tasks as $index => $task) {
            echo ($index + 1) . ". {$task['title']} (Priority: {$task['priority']})\n";
        }

        // Get the agent assignments for the project
        $assignmentModel = new ProjectAgentAssignment();
        $assignments = $assignmentModel->getProjectAgents($projectId);

        echo "\nProject Agent Assignments (" . count($assignments) . "):\n";
        foreach ($assignments as $assignment) {
            echo "- {$assignment['agent_name']} (Role: {$assignment['role']})\n";
        }

        // Get the agent tasks for the project
        $agentTaskModel = new AIAgentTask();
        $agentTasks = $agentTaskModel->getAgentTasks($agent['id']);

        echo "\nAegis Director Agent Tasks (" . count($agentTasks) . "):\n";
        foreach ($agentTasks as $index => $task) {
            echo ($index + 1) . ". {$task['title']}\n";
        }

        // Get the agent interactions for the project
        $interactionModel = new AIAgentInteraction();
        $interactions = $interactionModel->getAgentInteractions($agent['id']);

        echo "\nAegis Director Agent Interactions (" . count($interactions) . "):\n";
        foreach ($interactions as $index => $interaction) {
            echo ($index + 1) . ". {$interaction['content']}\n";
            echo "   Response: {$interaction['response']}\n";
        }

        // Test 3: Generate a progress report
        echo "\n=== Test 3: Generate a progress report ===\n";
        if ($projectId) {
            $report = $projectManager->generateProjectProgressReport($projectId, $userId);

            echo "Progress Report for Project ID {$projectId}:\n";
            echo $report;
        } else {
            echo "Cannot generate progress report: No project ID available.\n";
        }
    } else {
        echo "❌ Failed to create AI Agent Army project\n";
    }
} else {
    echo "Database connection failed\n";
}
