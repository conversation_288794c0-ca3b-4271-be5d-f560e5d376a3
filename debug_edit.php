<?php
require_once 'src/utils/Database.php';
require_once 'src/models/Note.php';

try {
    echo "Testing edit note functionality...\n";
    
    $noteModel = new Note();
    $noteId = 12; // The note ID from the screenshot
    
    // Test getting the note
    $note = $noteModel->find($noteId);
    if ($note) {
        echo "✓ Note found: " . $note['title'] . "\n";
        echo "  - Content length: " . strlen($note['content'] ?? '') . " characters\n";
        echo "  - Category: " . ($note['category'] ?? 'None') . "\n";
        echo "  - Tags: " . ($note['tags'] ?? 'None') . "\n";
        echo "  - Priority: " . ($note['priority_level'] ?? 'Not set') . "\n";
        echo "  - Is Pinned: " . ($note['is_pinned'] ?? 0) . "\n";
        echo "  - Is Favorite: " . ($note['is_favorite'] ?? 0) . "\n";
        echo "  - Color Code: " . ($note['color_code'] ?? 'Not set') . "\n";
    } else {
        echo "✗ Note not found\n";
    }
    
    // Test getting categories
    $categories = $noteModel->getUserNoteCategories(2); // Assuming user ID 2
    echo "✓ Found " . count($categories) . " categories\n";
    
    // Test the enhanced fields
    $sql = "DESCRIBE notes";
    $db = Database::getInstance();
    $columns = $db->fetchAll($sql);
    
    echo "\nChecking enhanced fields in database:\n";
    $enhancedFields = ['priority_level', 'is_favorite', 'color_code', 'word_count', 'reading_time'];
    $existingColumns = array_column($columns, 'Field');
    
    foreach ($enhancedFields as $field) {
        $exists = in_array($field, $existingColumns);
        echo ($exists ? '✓' : '✗') . " $field\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
