<?php
require_once 'src/utils/Database.php';
require_once 'src/models/Note.php';

try {
    echo "Testing Note model...\n";
    
    $noteModel = new Note();
    echo "Note model created successfully\n";
    
    $stats = $noteModel->getNoteStats(1);
    echo "Stats retrieved successfully: " . print_r($stats, true) . "\n";
    
    echo "Test completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
