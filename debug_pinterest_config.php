<?php
/**
 * Pinterest Configuration Debug Tool
 *
 * This script helps you set up and test your Pinterest environment variables.
 */

// Include required files
require_once 'src/utils/Environment.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load environment variables
Environment::load();

// Function to update the .env file
function updateEnvFile($key, $value) {
    $envFile = __DIR__ . '/.env';

    if (file_exists($envFile)) {
        $envContent = file_get_contents($envFile);

        // Check if key already exists
        if (preg_match('/^' . $key . '=.*$/m', $envContent)) {
            // Update existing entry
            $envContent = preg_replace('/^' . $key . '=.*$/m', $key . '=' . $value, $envContent);
        } else {
            // Add new entry
            $envContent .= "\n" . $key . '=' . $value . "\n";
        }

        // Write back to file
        file_put_contents($envFile, $envContent);
        return true;
    } else {
        // Create new .env file
        $envContent = "# Pinterest API Configuration\n";
        $envContent .= $key . '=' . $value . "\n";
        file_put_contents($envFile, $envContent);
        return true;
    }

    return false;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'update_env') {
        // Get values from form
        $email = $_POST['pinterest_email'] ?? '';
        $password = $_POST['pinterest_password'] ?? '';
        $username = $_POST['pinterest_username'] ?? '';
        $chromeProfile = $_POST['chrome_profile_path'] ?? '';

        // Update environment variables in .env file
        $updated = true;
        if (!empty($email)) {
            $updated = $updated && updateEnvFile('PINTEREST_EMAIL', $email);
        }
        if (!empty($password)) {
            $updated = $updated && updateEnvFile('PINTEREST_PASSWORD', $password);
        }
        if (!empty($username)) {
            $updated = $updated && updateEnvFile('PINTEREST_USERNAME', $username);
        }
        if (!empty($chromeProfile)) {
            $updated = $updated && updateEnvFile('CHROME_PROFILE_PATH', $chromeProfile);
        }

        // Reload environment variables
        Environment::load();

        $message = $updated ? "Environment variables updated successfully!" : "Failed to update environment variables.";
    }
}

// Get current values
$email = Environment::get('PINTEREST_EMAIL', '');
$password = Environment::get('PINTEREST_PASSWORD', '');
$username = Environment::get('PINTEREST_USERNAME', '');
$chromeProfile = Environment::get('CHROME_PROFILE_PATH', '');

// HTML header
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pinterest Configuration Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #e60023;
        }
        .container {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .success {
            color: #28a745;
            font-weight: bold;
            padding: 10px;
            background-color: #f8fff8;
            border-left: 4px solid #28a745;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .btn {
            display: inline-block;
            background-color: #e60023;
            color: white;
            padding: 10px 20px;
            border-radius: 24px;
            text-decoration: none;
            margin: 5px 0;
            cursor: pointer;
            border: none;
            font-weight: bold;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #ad081b;
        }
        .info-box {
            background-color: #e8f4fd;
            border-left: 4px solid #0078d7;
            padding: 10px 15px;
            margin-bottom: 20px;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
        .steps {
            counter-reset: step-counter;
            margin-bottom: 20px;
        }
        .step {
            margin-bottom: 15px;
            padding-left: 40px;
            position: relative;
        }
        .step:before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 0;
            background-color: #e60023;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            text-align: center;
            line-height: 25px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Pinterest Configuration Debug Tool</h1>

    <?php if (isset($message)): ?>
    <div class="success">
        <?php echo htmlspecialchars($message); ?>
    </div>
    <?php endif; ?>

    <div class="container">
        <h2>Current Environment Variables</h2>
        <p><strong>PINTEREST_EMAIL:</strong> <?php echo $email ? htmlspecialchars($email) : '<span style="color: #dc3545;">Not set</span>'; ?></p>
        <p><strong>PINTEREST_PASSWORD:</strong> <?php echo $password ? '********' : '<span style="color: #dc3545;">Not set</span>'; ?></p>
        <p><strong>PINTEREST_USERNAME:</strong> <?php echo $username ? htmlspecialchars($username) : '<span style="color: #dc3545;">Not set</span>'; ?></p>
        <p><strong>CHROME_PROFILE_PATH:</strong> <?php echo $chromeProfile ? htmlspecialchars($chromeProfile) : '<span style="color: #dc3545;">Not set</span>'; ?></p>
    </div>

    <div class="container">
        <h2>Update Environment Variables</h2>
        <form method="post" action="<?php echo $_SERVER['PHP_SELF']; ?>">
            <input type="hidden" name="action" value="update_env">

            <div class="form-group">
                <label for="pinterest_email">Pinterest Email:</label>
                <input type="text" id="pinterest_email" name="pinterest_email" value="<?php echo htmlspecialchars($email); ?>" required>
            </div>

            <div class="form-group">
                <label for="pinterest_password">Pinterest Password:</label>
                <input type="password" id="pinterest_password" name="pinterest_password" value="<?php echo htmlspecialchars($password); ?>" required>
            </div>

            <div class="form-group">
                <label for="pinterest_username">Pinterest Username:</label>
                <input type="text" id="pinterest_username" name="pinterest_username" value="<?php echo htmlspecialchars($username); ?>" required>
            </div>

            <div class="form-group">
                <label for="chrome_profile_path">Chrome Profile Path:</label>
                <input type="text" id="chrome_profile_path" name="chrome_profile_path" value="<?php echo htmlspecialchars($chromeProfile); ?>" placeholder="e.g., C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\Profile 2">
            </div>

            <button type="submit" class="btn">Update Environment Variables</button>
        </form>
    </div>

    <div class="container">
        <h2>How to Find Your Chrome Profile Path</h2>

        <div class="steps">
            <div class="step">
                <p>Open Chrome and create a new profile by clicking on your profile icon in the top-right corner and selecting "Add".</p>
            </div>

            <div class="step">
                <p>Name your profile "Pinterest Automation" or something similar and click "Done".</p>
            </div>

            <div class="step">
                <p>In your new Chrome profile, manually log in to Pinterest at <a href="https://www.pinterest.com/login/" target="_blank">https://www.pinterest.com/login/</a></p>
            </div>

            <div class="step">
                <p>After logging in, type <code>chrome://version</code> in the address bar and press Enter.</p>
            </div>

            <div class="step">
                <p>Look for "Profile Path" in the information displayed and copy this path.</p>
            </div>

            <div class="step">
                <p>Paste the path into the "Chrome Profile Path" field above and click "Update Environment Variables".</p>
            </div>
        </div>

        <div class="info-box">
            <p><strong>Note:</strong> On Windows, the path might look like:</p>
            <code>C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\Profile 2</code>

            <p><strong>Note:</strong> On macOS, the path might look like:</p>
            <code>/Users/<USER>/Library/Application Support/Google/Chrome/Profile 2</code>
        </div>
    </div>

    <div class="container">
        <h2>Next Steps</h2>
        <p>After updating your environment variables:</p>
        <ol>
            <li>Run the <a href="test_pinterest_enhanced.php">Pinterest Enhanced Test</a> to verify your configuration.</li>
            <li>Test Chrome profile fix, login, board listing, and other Pinterest operations.</li>
            <li>Once everything is working, you can integrate these features into your main application.</li>
        </ol>
    </div>
</body>
</html>
