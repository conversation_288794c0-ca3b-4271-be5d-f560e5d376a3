<?php
/**
 * Test Aegis Director Agent
 * 
 * This script tests the Aegis Director agent functionality.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';
require_once 'src/models/Project.php';
require_once 'src/models/Task.php';
require_once 'src/models/AegisDirectorProjectManager.php';
require_once 'src/models/ProjectAgentAssignment.php';

// Initialize models
$db = Database::getInstance();
$agentModel = new AIAgent();
$taskModel = new AIAgentTask();
$interactionModel = new AIAgentInteraction();
$projectModel = new Project();
$projectTaskModel = new Task();
$projectManager = new AegisDirectorProjectManager();
$assignmentModel = new ProjectAgentAssignment();

// Check database connection
if ($db) {
    echo "Database connection successful<br>";
} else {
    echo "Database connection failed<br>";
    exit;
}

// Get current user ID from session or use default
require_once 'src/utils/Session.php';
Session::start();
$currentUser = Session::getUser();
$userId = $currentUser ? $currentUser['id'] : 1; // Default to user ID 1 if not logged in

// Get the Aegis Director agent
$aegisDirector = $projectManager->getAegisDirectorAgent($userId);

if (!$aegisDirector) {
    echo "Aegis Director agent not found. Please run create_aegis_director_agent.php first.<br>";
    exit;
}

echo "Found Aegis Director agent with ID: {$aegisDirector['id']}<br>";

// Test creating a rapid implementation project
$projectName = "Test Rapid Implementation Project";
$projectDescription = "This is a test project for the 24-hour rapid implementation plan.";
$deadline = date('Y-m-d', strtotime('+1 day'));

$projectId = $projectManager->createRapidImplementationProject(
    $userId,
    $projectName,
    $projectDescription,
    $deadline
);

if ($projectId) {
    echo "Created rapid implementation project with ID: {$projectId}<br>";
    
    // Create the 24-hour implementation plan
    $success = $projectManager->create24HourImplementationPlan($projectId, $userId);
    
    if ($success) {
        echo "Created 24-hour implementation plan successfully<br>";
        
        // Get the project tasks
        $tasks = $projectTaskModel->getProjectTasks($projectId);
        
        echo "Project has " . count($tasks) . " tasks:<br>";
        foreach ($tasks as $task) {
            echo "- {$task['title']} (Priority: {$task['priority']}, Status: {$task['status']})<br>";
        }
        
        // Generate a progress report
        $report = $projectManager->generateProjectProgressReport($projectId, $userId);
        
        echo "<h3>Project Progress Report</h3>";
        echo "<pre>" . htmlspecialchars($report) . "</pre>";
    } else {
        echo "Failed to create 24-hour implementation plan<br>";
    }
} else {
    echo "Failed to create rapid implementation project<br>";
}

// Test creating an AI Agent Army project
$brigadeProjectName = "Test Content Creation Brigade";
$brigadeProjectDescription = "This is a test project for the Content Creation Brigade.";
$brigadeDeadline = date('Y-m-d', strtotime('+7 days'));
$brigadeType = "content_creation";

$brigadeProjectId = $projectManager->createAgentArmyProject(
    $userId,
    $brigadeType,
    $brigadeProjectName,
    $brigadeProjectDescription,
    $brigadeDeadline
);

if ($brigadeProjectId) {
    echo "Created AI Agent Army project with ID: {$brigadeProjectId}<br>";
    
    // Get the brigade roles
    $brigadeRoleModel = new AgentBrigadeRole();
    $roles = $brigadeRoleModel->getBrigadeRoles($brigadeType);
    
    echo "Brigade has " . count($roles) . " roles:<br>";
    foreach ($roles as $role) {
        echo "- {$role['name']}: {$role['description']}<br>";
    }
    
    // Get projects managed by Aegis Director
    $managedProjects = $projectManager->getAegisDirectorProjects($userId);
    
    echo "<h3>Projects Managed by Aegis Director</h3>";
    echo "Found " . count($managedProjects) . " projects:<br>";
    foreach ($managedProjects as $project) {
        echo "- {$project['project_name']} (Status: {$project['project_status']}, Role: {$project['role']})<br>";
    }
} else {
    echo "Failed to create AI Agent Army project<br>";
}

echo "<br>Test completed successfully!<br>";
echo "You can now view the Aegis Director interface at: <a href='/momentum/aegis-director-interface.php'>Aegis Director Interface</a><br>";
echo "You can view all agents at: <a href='/momentum/ai-agents'>AI Agents Dashboard</a><br>";
echo "You can view projects at: <a href='/momentum/projects'>Projects Dashboard</a><br>";
