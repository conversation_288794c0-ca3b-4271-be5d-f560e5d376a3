<?php
/**
 * Debug script for Income Opportunities
 * 
 * This script helps diagnose issues with the income opportunities feature.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Include required files
require_once BASE_PATH . '/src/utils/Database.php';
require_once BASE_PATH . '/src/models/IncomeOpportunity.php';
require_once BASE_PATH . '/src/utils/Session.php';

// Start session
Session::start();

// Check if user is logged in
if (!Session::isLoggedIn()) {
    echo "User is not logged in. Please log in first.\n";
    exit;
}

// Get user information
$user = Session::getUser();
$userId = $user['id'];

echo "Debug Income Opportunities\n";
echo "=========================\n\n";
echo "User ID: $userId\n\n";

// Create instance of IncomeOpportunity model
$opportunityModel = new IncomeOpportunity();

// Get database instance directly
$db = Database::getInstance();

// Check if income_opportunities table exists
$tableCheck = $db->fetchOne("SHOW TABLES LIKE 'income_opportunities'");
echo "Income Opportunities table exists: " . ($tableCheck ? "Yes" : "No") . "\n\n";

// Get all opportunities for the user
$opportunities = $opportunityModel->getUserOpportunities($userId);
echo "Number of opportunities found for user: " . ($opportunities ? count($opportunities) : 0) . "\n\n";

// Get all opportunities in the database
$allOpportunities = $db->fetchAll("SELECT * FROM income_opportunities");
echo "Total opportunities in database: " . ($allOpportunities ? count($allOpportunities) : 0) . "\n\n";

// Check if there are any opportunities for other users
$otherUserOpportunities = $db->fetchAll("SELECT * FROM income_opportunities WHERE user_id != ?", [$userId]);
echo "Opportunities for other users: " . ($otherUserOpportunities ? count($otherUserOpportunities) : 0) . "\n\n";

// Display all opportunities in the database
if ($allOpportunities && count($allOpportunities) > 0) {
    echo "All opportunities in database:\n";
    echo "-----------------------------\n";
    foreach ($allOpportunities as $opportunity) {
        echo "ID: " . $opportunity['id'] . "\n";
        echo "User ID: " . $opportunity['user_id'] . "\n";
        echo "Name: " . $opportunity['name'] . "\n";
        echo "Category: " . $opportunity['category'] . "\n";
        echo "Status: " . $opportunity['status'] . "\n";
        echo "Created: " . $opportunity['created_at'] . "\n";
        echo "Updated: " . $opportunity['updated_at'] . "\n";
        echo "-----------------------------\n";
    }
} else {
    echo "No opportunities found in the database.\n";
}

// Test creating a new opportunity
echo "\nTesting opportunity creation:\n";
echo "-----------------------------\n";

$testData = [
    'user_id' => $userId,
    'name' => 'Test Opportunity ' . date('Y-m-d H:i:s'),
    'description' => 'This is a test opportunity created by the debug script',
    'category' => 'Testing',
    'income_type' => 'active',
    'skill_level' => 'beginner',
    'startup_cost' => 'none',
    'time_commitment' => 'minimal',
    'income_frequency' => 'monthly',
    'status' => 'considering',
    'priority' => 5,
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
];

echo "Creating test opportunity...\n";
$newId = $opportunityModel->create($testData);

if ($newId) {
    echo "Success! New opportunity created with ID: $newId\n";
    
    // Verify the opportunity was created
    $newOpportunity = $opportunityModel->find($newId);
    if ($newOpportunity) {
        echo "Verified: Opportunity exists in database.\n";
    } else {
        echo "Error: Could not find the newly created opportunity.\n";
    }
    
    // Get all opportunities again to see if it's included
    $updatedOpportunities = $opportunityModel->getUserOpportunities($userId);
    echo "Updated count of opportunities: " . count($updatedOpportunities) . "\n";
    
    // Check if the new opportunity is in the list
    $found = false;
    foreach ($updatedOpportunities as $opp) {
        if ($opp['id'] == $newId) {
            $found = true;
            break;
        }
    }
    echo "New opportunity found in user's opportunities list: " . ($found ? "Yes" : "No") . "\n";
} else {
    echo "Error: Failed to create test opportunity.\n";
}

// Check database configuration
echo "\nDatabase Configuration:\n";
echo "---------------------\n";
$config = require_once BASE_PATH . '/src/config/database.php';
echo "Host: " . $config['host'] . "\n";
echo "Database: " . $config['database'] . "\n";
echo "Username: " . $config['username'] . "\n";
echo "Charset: " . $config['charset'] . "\n";

// Check for any database errors in the error log
echo "\nChecking for recent database errors in PHP error log...\n";
$errorLog = error_get_last();
if ($errorLog) {
    echo "Last error: " . print_r($errorLog, true) . "\n";
} else {
    echo "No recent PHP errors found.\n";
}

echo "\nDebug complete.\n";
