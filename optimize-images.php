<?php
/**
 * Image Optimization Script
 *
 * This script optimizes images in the public directory.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Define image directories
$imageDirectories = [
    BASE_PATH . '/public/assets/images',
    BASE_PATH . '/public/uploads',
];

// Function to optimize JPEG images
function optimizeJpeg($source, $destination, $quality = 85) {
    $image = imagecreatefromjpeg($source);
    return imagejpeg($image, $destination, $quality);
}

// Function to optimize PNG images
function optimizePng($source, $destination) {
    $image = imagecreatefrompng($source);
    
    // Set background to white for images with alpha channel
    imagealphablending($image, true);
    imagesavealpha($image, true);
    
    return imagepng($image, $destination, 9); // Maximum compression
}

// Function to optimize GIF images
function optimizeGif($source, $destination) {
    $image = imagecreatefromgif($source);
    return imagegif($image, $destination);
}

// Function to create WebP version of an image
function createWebP($source, $destination, $quality = 80) {
    $extension = pathinfo($source, PATHINFO_EXTENSION);
    
    switch (strtolower($extension)) {
        case 'jpeg':
        case 'jpg':
            $image = imagecreatefromjpeg($source);
            break;
        case 'png':
            $image = imagecreatefrompng($source);
            imagealphablending($image, true);
            imagesavealpha($image, true);
            break;
        case 'gif':
            $image = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    return imagewebp($image, $destination, $quality);
}

// Function to create responsive image versions
function createResponsiveImages($source, $destination, $sizes = []) {
    $extension = pathinfo($source, PATHINFO_EXTENSION);
    $filename = pathinfo($source, PATHINFO_FILENAME);
    $directory = pathinfo($destination, PATHINFO_DIRNAME);
    
    // Load image based on extension
    switch (strtolower($extension)) {
        case 'jpeg':
        case 'jpg':
            $image = imagecreatefromjpeg($source);
            break;
        case 'png':
            $image = imagecreatefrompng($source);
            break;
        case 'gif':
            $image = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    // Get original dimensions
    $originalWidth = imagesx($image);
    $originalHeight = imagesy($image);
    
    // Create resized versions
    foreach ($sizes as $size) {
        // Skip if original is smaller than target size
        if ($originalWidth <= $size) {
            continue;
        }
        
        // Calculate new height maintaining aspect ratio
        $newHeight = floor($originalHeight * ($size / $originalWidth));
        
        // Create resized image
        $resized = imagecreatetruecolor($size, $newHeight);
        
        // Handle transparency for PNG
        if (strtolower($extension) === 'png') {
            imagealphablending($resized, false);
            imagesavealpha($resized, true);
            $transparent = imagecolorallocatealpha($resized, 255, 255, 255, 127);
            imagefilledrectangle($resized, 0, 0, $size, $newHeight, $transparent);
        }
        
        // Resize image
        imagecopyresampled($resized, $image, 0, 0, 0, 0, $size, $newHeight, $originalWidth, $originalHeight);
        
        // Save resized image
        $resizedFilename = $filename . '-' . $size . 'w.' . $extension;
        $resizedPath = $directory . '/' . $resizedFilename;
        
        switch (strtolower($extension)) {
            case 'jpeg':
            case 'jpg':
                imagejpeg($resized, $resizedPath, 85);
                break;
            case 'png':
                imagepng($resized, $resizedPath, 9);
                break;
            case 'gif':
                imagegif($resized, $resizedPath);
                break;
        }
        
        // Create WebP version
        $webpFilename = $filename . '-' . $size . 'w.webp';
        $webpPath = $directory . '/' . $webpFilename;
        imagewebp($resized, $webpPath, 80);
        
        // Free memory
        imagedestroy($resized);
    }
    
    // Free memory
    imagedestroy($image);
    
    return true;
}

// Function to process an image
function processImage($file) {
    $extension = pathinfo($file, PATHINFO_EXTENSION);
    $filename = pathinfo($file, PATHINFO_FILENAME);
    $directory = pathinfo($file, PATHINFO_DIRNAME);
    
    // Skip already processed images
    if (preg_match('/-\d+w$/', $filename)) {
        return;
    }
    
    // Create backup
    $backupDir = $directory . '/originals';
    if (!file_exists($backupDir)) {
        mkdir($backupDir, 0755, true);
    }
    
    $backupFile = $backupDir . '/' . $filename . '.' . $extension;
    if (!file_exists($backupFile)) {
        copy($file, $backupFile);
    }
    
    // Optimize image based on type
    switch (strtolower($extension)) {
        case 'jpg':
        case 'jpeg':
            optimizeJpeg($file, $file);
            break;
        case 'png':
            optimizePng($file, $file);
            break;
        case 'gif':
            optimizeGif($file, $file);
            break;
    }
    
    // Create WebP version
    $webpFile = $directory . '/' . $filename . '.webp';
    createWebP($file, $webpFile);
    
    // Create responsive versions
    createResponsiveImages($file, $file, [320, 640, 768, 1024, 1280]);
    
    echo "Processed: $file\n";
}

// Function to scan directory recursively
function scanDirectory($dir) {
    $result = [];
    $files = scandir($dir);
    
    foreach ($files as $file) {
        if ($file === '.' || $file === '..' || $file === 'originals') {
            continue;
        }
        
        $path = $dir . '/' . $file;
        
        if (is_dir($path)) {
            $result = array_merge($result, scanDirectory($path));
        } else {
            $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
            if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                $result[] = $path;
            }
        }
    }
    
    return $result;
}

// Process all image directories
foreach ($imageDirectories as $directory) {
    if (!file_exists($directory)) {
        echo "Directory not found: $directory\n";
        continue;
    }
    
    echo "Processing directory: $directory\n";
    
    $images = scanDirectory($directory);
    $totalImages = count($images);
    
    echo "Found $totalImages images\n";
    
    foreach ($images as $index => $image) {
        echo "[$index/$totalImages] ";
        processImage($image);
    }
}

echo "Image optimization complete!\n";
