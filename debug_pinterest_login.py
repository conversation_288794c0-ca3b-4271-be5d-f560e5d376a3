#!/usr/bin/env python
"""
Debug Pinterest Login

This script helps debug Pinterest login issues by showing detailed information
about the login process.
"""

import os
import sys
import json
import time
from py3pin.Pinterest import Pinterest
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Get credentials from command line or environment variables
if len(sys.argv) > 3:
    email = sys.argv[1]
    password = sys.argv[2]
    username = sys.argv[3]
    chrome_profile = sys.argv[4] if len(sys.argv) > 4 else None
else:
    # Try to get from environment variables
    from dotenv import load_dotenv
    load_dotenv()
    email = os.getenv('PINTEREST_EMAIL')
    password = os.getenv('PINTEREST_PASSWORD')
    username = os.getenv('PINTEREST_USERNAME')
    chrome_profile = os.getenv('CHROME_PROFILE_PATH')

# Print configuration
print(f"Debug Pinterest Login")
print(f"====================")
print(f"Email: {email}")
print(f"Username: {username}")
print(f"Chrome Profile: {chrome_profile}")
print(f"====================")

# Create credentials directory
cred_root = os.path.abspath('./pinterest_creds')
os.makedirs(cred_root, exist_ok=True)
print(f"Credentials directory: {cred_root}")

# Configure Chrome options
chrome_options = Options()
if chrome_profile:
    print(f"Using Chrome profile: {chrome_profile}")
    chrome_options.add_argument(f"--user-data-dir={chrome_profile}")
else:
    print("No Chrome profile specified, using default")

chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")
chrome_options.add_argument("--window-size=1920,1080")
chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
chrome_options.add_experimental_option('useAutomationExtension', False)

# Try to create a custom browser
try:
    print("Creating Chrome driver...")
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("Chrome driver created successfully")
    
    # Navigate to Pinterest login page
    print("Navigating to Pinterest login page...")
    driver.get("https://www.pinterest.com/login/")
    
    # Wait for the page to load
    print("Waiting for page to load...")
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.TAG_NAME, "body"))
    )
    
    # Check if we're already logged in
    if "login" not in driver.current_url.lower():
        print("Already logged in!")
        print(f"Current URL: {driver.current_url}")
        
        # Take a screenshot
        screenshot_path = os.path.abspath("./pinterest_already_logged_in.png")
        driver.save_screenshot(screenshot_path)
        print(f"Screenshot saved to: {screenshot_path}")
        
        driver.quit()
        sys.exit(0)
    
    # Take a screenshot of the login page
    screenshot_path = os.path.abspath("./pinterest_login_page.png")
    driver.save_screenshot(screenshot_path)
    print(f"Login page screenshot saved to: {screenshot_path}")
    
    # Try to find the email field
    try:
        print("Looking for email field...")
        email_field = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "email"))
        )
        print("Email field found")
    except Exception as e:
        print(f"Error finding email field: {str(e)}")
        # Try alternative selectors
        try:
            email_field = driver.find_element(By.NAME, "id")
            print("Email field found by name='id'")
        except:
            try:
                email_field = driver.find_element(By.CSS_SELECTOR, "input[type='email']")
                print("Email field found by CSS selector")
            except Exception as e2:
                print(f"Could not find email field: {str(e2)}")
                # Take a screenshot
                screenshot_path = os.path.abspath("./pinterest_email_field_error.png")
                driver.save_screenshot(screenshot_path)
                print(f"Error screenshot saved to: {screenshot_path}")
                driver.quit()
                sys.exit(1)
    
    # Try to find the password field
    try:
        print("Looking for password field...")
        password_field = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "password"))
        )
        print("Password field found")
    except Exception as e:
        print(f"Error finding password field: {str(e)}")
        # Try alternative selectors
        try:
            password_field = driver.find_element(By.NAME, "password")
            print("Password field found by name='password'")
        except:
            try:
                password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
                print("Password field found by CSS selector")
            except Exception as e2:
                print(f"Could not find password field: {str(e2)}")
                # Take a screenshot
                screenshot_path = os.path.abspath("./pinterest_password_field_error.png")
                driver.save_screenshot(screenshot_path)
                print(f"Error screenshot saved to: {screenshot_path}")
                driver.quit()
                sys.exit(1)
    
    # Enter credentials
    print("Entering email...")
    email_field.clear()
    email_field.send_keys(email)
    
    print("Entering password...")
    password_field.clear()
    password_field.send_keys(password)
    
    # Take a screenshot before clicking login
    screenshot_path = os.path.abspath("./pinterest_before_login.png")
    driver.save_screenshot(screenshot_path)
    print(f"Before login screenshot saved to: {screenshot_path}")
    
    # Try to find the login button
    try:
        print("Looking for login button...")
        login_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "button[type='submit']"))
        )
        print("Login button found")
    except Exception as e:
        print(f"Error finding login button: {str(e)}")
        # Take a screenshot
        screenshot_path = os.path.abspath("./pinterest_login_button_error.png")
        driver.save_screenshot(screenshot_path)
        print(f"Error screenshot saved to: {screenshot_path}")
        driver.quit()
        sys.exit(1)
    
    # Click login
    print("Clicking login button...")
    login_button.click()
    
    # Wait for login to complete
    print("Waiting for login to complete...")
    time.sleep(5)
    
    # Take a screenshot after login
    screenshot_path = os.path.abspath("./pinterest_after_login.png")
    driver.save_screenshot(screenshot_path)
    print(f"After login screenshot saved to: {screenshot_path}")
    
    # Check if login was successful
    if "login" not in driver.current_url.lower():
        print("Login successful!")
        print(f"Current URL: {driver.current_url}")
        
        # Save cookies
        cookies = driver.get_cookies()
        cookie_file = os.path.join(cred_root, f"{username}.cookies")
        with open(cookie_file, 'w') as f:
            json.dump(cookies, f)
        print(f"Cookies saved to: {cookie_file}")
    else:
        print("Login failed!")
        print(f"Current URL: {driver.current_url}")
        
        # Check for error messages
        try:
            error_message = driver.find_element(By.CSS_SELECTOR, ".errorMessage")
            print(f"Error message: {error_message.text}")
        except:
            print("No error message found")
    
    # Close the browser
    driver.quit()
    
except Exception as e:
    print(f"Error: {str(e)}")
    sys.exit(1)
