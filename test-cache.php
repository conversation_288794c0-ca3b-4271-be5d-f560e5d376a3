<?php
/**
 * Test Cache Functionality
 *
 * This script tests the Cache class to ensure it works correctly.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include the Cache class
require_once __DIR__ . '/src/utils/Cache.php';

// Get cache instance
try {
    $cache = Cache::getInstance();
    echo "Cache instance created successfully.\n";
    
    // Test setting a value
    $cache->set('test_key', 'test_value', 60);
    echo "Value set in cache.\n";
    
    // Test getting a value
    $value = $cache->get('test_key');
    echo "Retrieved value from cache: " . $value . "\n";
    
    // Test checking if a key exists
    $exists = $cache->has('test_key');
    echo "Key exists in cache: " . ($exists ? 'Yes' : 'No') . "\n";
    
    // Test removing a key
    $cache->forget('test_key');
    echo "Key removed from cache.\n";
    
    // Test checking if a key exists after removal
    $exists = $cache->has('test_key');
    echo "Key exists in cache after removal: " . ($exists ? 'Yes' : 'No') . "\n";
    
    // Test remembering a value
    $value = $cache->remember('remember_key', 60, function() {
        return 'remembered_value';
    });
    echo "Remembered value: " . $value . "\n";
    
    // Test clearing all cache
    $cache->clear();
    echo "Cache cleared.\n";
    
    echo "\nAll tests passed successfully!\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
