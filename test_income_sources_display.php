<?php
require_once 'src/utils/Database.php';
require_once 'src/models/IncomeSource.php';
require_once 'src/utils/View.php';

// Initialize the model
$incomeSourceModel = new IncomeSource();

// Use user ID 1 (the one we updated the income sources to)
$userId = 1;

// Get all income sources for this user without filtering
$incomeSources = $incomeSourceModel->getUserIncomeSources($userId, true);

echo "=== INCOME SOURCES FROM getUserIncomeSources() ===\n";
echo "Found " . count($incomeSources) . " income sources\n\n";

if (count($incomeSources) > 0) {
    foreach ($incomeSources as $source) {
        echo "- ID: " . $source['id'] . "\n";
        echo "  Name: " . $source['name'] . "\n";
        echo "  Active: " . ($source['is_active'] ? 'Yes' : 'No') . "\n";
        echo "  Recurring: " . ($source['is_recurring'] ? 'Yes' : 'No') . "\n";
        echo "  Category: " . ($source['category'] ?? 'N/A') . "\n\n";
    }
}

// Get income sources with total income
$startDate = date('Y-m-01'); // First day of current month
$endDate = date('Y-m-t'); // Last day of current month

$sourcesWithIncome = $incomeSourceModel->getSourcesWithTotalIncome($userId, $startDate, $endDate);

echo "=== INCOME SOURCES FROM getSourcesWithTotalIncome() ===\n";
echo "Date range: $startDate to $endDate\n";
echo "Found " . count($sourcesWithIncome) . " income sources\n\n";

if (count($sourcesWithIncome) > 0) {
    foreach ($sourcesWithIncome as $source) {
        echo "- ID: " . $source['id'] . "\n";
        echo "  Name: " . $source['name'] . "\n";
        echo "  Active: " . ($source['is_active'] ? 'Yes' : 'No') . "\n";
        echo "  Recurring: " . ($source['is_recurring'] ? 'Yes' : 'No') . "\n";
        echo "  Category: " . ($source['category'] ?? 'N/A') . "\n";
        echo "  Transaction Count: " . ($source['transaction_count'] ?? 0) . "\n";
        echo "  Total Income: " . ($source['total_income'] ?? 0) . "\n\n";
    }
}

// Simulate the controller logic
echo "=== SIMULATING CONTROLLER LOGIC ===\n";
$totalIncome = 0;
foreach ($sourcesWithIncome as $source) {
    $totalIncome += (float)($source['total_income'] ?? 0);
}

echo "Total Income: $totalIncome\n\n";

// Check if the view file exists and is readable
$viewPath = 'src/views/finances/income_sources/index.php';
echo "View file exists: " . (file_exists($viewPath) ? 'Yes' : 'No') . "\n";
echo "View file is readable: " . (is_readable($viewPath) ? 'Yes' : 'No') . "\n";
