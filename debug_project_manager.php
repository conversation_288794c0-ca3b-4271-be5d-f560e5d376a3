<?php
/**
 * Debug Project Manager
 * 
 * This script debugs the AegisDirectorProjectManager class.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/Project.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/ProjectAgentAssignment.php';
require_once 'src/models/AegisDirectorProjectManager.php';

// Initialize models
$db = Database::getInstance();
$projectManager = new AegisDirectorProjectManager();

// Check database connection
if ($db) {
    echo "Database connection successful\n";
} else {
    echo "Database connection failed\n";
    exit;
}

// Create a test project using the AegisDirectorProjectManager
$userId = 1;
$brigadeType = 'content_creation';
$projectName = 'Test Agent Army Project';
$projectDescription = 'This is a test project for the Content Creation Brigade.';
$deadline = date('Y-m-d', strtotime('+7 days'));

echo "Creating Agent Army project with data:\n";
echo "User ID: {$userId}\n";
echo "Brigade Type: {$brigadeType}\n";
echo "Project Name: {$projectName}\n";
echo "Project Description: {$projectDescription}\n";
echo "Deadline: {$deadline}\n";

// Create the project using the project manager
$projectId = $projectManager->createAgentArmyProject($userId, $brigadeType, $projectName, $projectDescription, $deadline);

if ($projectId) {
    echo "Agent Army project created successfully with ID: {$projectId}\n";
} else {
    echo "Failed to create Agent Army project\n";
    
    // Try to create the project directly
    echo "\nTrying to create project directly...\n";
    
    $projectModel = new Project();
    $projectData = [
        'user_id' => $userId,
        'name' => $projectName,
        'description' => $projectDescription,
        'start_date' => date('Y-m-d'),
        'end_date' => $deadline,
        'status' => 'planning',
        'progress' => 0,
        'is_template' => false,
        'brigade_type' => $brigadeType,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    $directProjectId = $projectModel->create($projectData);
    
    if ($directProjectId) {
        echo "Project created directly with ID: {$directProjectId}\n";
    } else {
        echo "Failed to create project directly\n";
    }
}
