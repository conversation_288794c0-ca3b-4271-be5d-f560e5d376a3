<?php
/**
 * Aegis Director Interface
 * 
 * This file provides the interface for interacting with the Aegis Director agent,
 * which is designed to help users with ADHD executive functioning challenges.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentCategory.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';
require_once 'src/utils/Session.php';

// Initialize models
$db = Database::getInstance();
$agentModel = new AIAgent();
$categoryModel = new AIAgentCategory();
$taskModel = new AIAgentTask();
$interactionModel = new AIAgentInteraction();

// Start session
Session::start();

// Get current user
$currentUser = Session::getUser();
$userId = $currentUser ? $currentUser['id'] : 1; // Default to user ID 1 if not logged in

// Check if the agent exists
$agents = $agentModel->getUserAgents($userId);
$aegisDirectorExists = false;
$aegisDirectorId = null;

foreach ($agents as $agent) {
    if ($agent['name'] === 'Aegis Director') {
        $aegisDirectorExists = true;
        $aegisDirectorId = $agent['id'];
        break;
    }
}

// If agent doesn't exist, create it
if (!$aegisDirectorExists) {
    // Redirect to the creation script
    header('Location: create_aegis_director_agent.php');
    exit;
}

// Get agent details
$agentDetails = $agentModel->getAgent($aegisDirectorId, $userId);

// Get agent tasks
$agentTasks = $taskModel->getAgentTasks($aegisDirectorId);

// Get agent interactions
$agentInteractions = $interactionModel->getAgentInteractions($aegisDirectorId, 10);

// Process form submission for new interaction
$message = '';
$response = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['interaction_content']) && !empty($_POST['interaction_content'])) {
        $interactionContent = trim($_POST['interaction_content']);
        $interactionType = isset($_POST['interaction_type']) ? $_POST['interaction_type'] : 'command';
        
        // Create the interaction
        $interactionId = $interactionModel->createInteraction([
            'agent_id' => $aegisDirectorId,
            'user_id' => $userId,
            'interaction_type' => $interactionType,
            'content' => $interactionContent,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($interactionId) {
            $message = 'Interaction sent successfully';
            
            // Generate a response based on the interaction type and content
            $response = generateAegisDirectorResponse($interactionContent, $interactionType, $agentDetails);
            
            // Save the response
            $interactionModel->updateInteraction($interactionId, [
                'response' => $response
            ]);
            
            // Refresh interactions
            $agentInteractions = $interactionModel->getAgentInteractions($aegisDirectorId, 10);
        } else {
            $message = 'Failed to send interaction';
        }
    }
}

/**
 * Generate a response from Aegis Director
 * 
 * In a production environment, this would call an AI API like OpenAI's GPT
 * For this demo, we'll use a rule-based approach
 */
function generateAegisDirectorResponse($content, $type, $agentDetails) {
    // In a real implementation, this would call an AI API
    // For this demo, we'll use a simple rule-based approach
    
    // Convert content to lowercase for easier matching
    $contentLower = strtolower($content);
    
    // Check for common patterns
    if (strpos($contentLower, 'hello') !== false || strpos($contentLower, 'hi') !== false) {
        return "I am Aegis Director, your executive functioning partner. I don't engage in small talk. What project are we working on? What is the specific goal and deadline?";
    }
    
    if (strpos($contentLower, 'help') !== false) {
        return "I am designed to ensure you achieve rapid, tangible results. To begin, state your project goal in specific, measurable terms with a deadline. I will then break it down into actionable tasks and hold you accountable for completion.";
    }
    
    if (strpos($contentLower, 'new project') !== false || strpos($contentLower, 'new goal') !== false) {
        return "Initiating Strategic Pause Protocol. Before starting a new project, we must assess your current commitments. What is the specific new project you're proposing? What is its deadline? What makes it more important than your current focus?";
    }
    
    if (strpos($contentLower, 'distracted') !== false || strpos($contentLower, 'focus') !== false) {
        return "Stop. Distractions are not permitted. Return to your current task immediately. Close all unrelated applications and websites. You have committed to completing this task by [deadline]. Further deviation will jeopardize your goal.";
    }
    
    if (strpos($contentLower, 'tired') !== false || strpos($contentLower, 'break') !== false) {
        return "Scheduled breaks are acceptable only after task completion. If you're experiencing genuine fatigue that impacts performance, we can implement a 10-minute strategic rest period. After that, you will resume with renewed focus. Is this necessary?";
    }
    
    if (strpos($contentLower, 'complete') !== false || strpos($contentLower, 'finished') !== false) {
        return "Task completion acknowledged. Moving to next priority immediately. Your next task is [Next Task]. Begin now. You have [Time Period] to complete this. Progress will be evaluated at [Check-in Time].";
    }
    
    // Default response for other inputs
    return "I require clear, specific information about your project goals and tasks. Please provide concrete details about what you need to accomplish, by when, and any obstacles you anticipate. I will then create a structured plan and hold you accountable for execution.";
}

// HTML header
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aegis Director - Executive Functioning Partner</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .chat-container {
            height: 60vh;
            overflow-y: auto;
        }
        .user-message {
            background-color: #e2f0fd;
            border-radius: 1rem 1rem 0 1rem;
        }
        .agent-message {
            background-color: #f0f4f8;
            border-radius: 1rem 1rem 1rem 0;
        }
        .system-message {
            background-color: #f8f0e3;
            border-radius: 0.5rem;
            font-style: italic;
        }
        .task-card {
            transition: all 0.3s ease;
        }
        .task-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .priority-high {
            border-left: 4px solid #ef4444;
        }
        .priority-medium {
            border-left: 4px solid #f59e0b;
        }
        .priority-low {
            border-left: 4px solid #10b981;
        }
        .status-pending {
            background-color: #f3f4f6;
        }
        .status-in_progress {
            background-color: #e0f2fe;
        }
        .status-completed {
            background-color: #d1fae5;
        }
        .status-failed {
            background-color: #fee2e2;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center">
                <div class="bg-indigo-600 text-white p-3 rounded-full mr-4">
                    <i class="fas fa-shield-alt text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Aegis Director</h1>
                    <p class="text-gray-600">Your Executive Functioning Partner</p>
                </div>
            </div>
            <div class="flex items-center">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 mr-2">
                    <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Active
                </span>
                <a href="/momentum/ai-agents/view/<?= $aegisDirectorId ?>" class="text-indigo-600 hover:text-indigo-800 ml-4">
                    <i class="fas fa-cog"></i> Agent Settings
                </a>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Column: Tasks -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">Current Tasks</h2>
                        <a href="/momentum/ai-agents/tasks/create?agent_id=<?= $aegisDirectorId ?>" class="text-indigo-600 hover:text-indigo-800">
                            <i class="fas fa-plus"></i> New Task
                        </a>
                    </div>
                    
                    <div class="space-y-4">
                        <?php if (empty($agentTasks)): ?>
                            <p class="text-gray-500 italic">No tasks assigned yet.</p>
                        <?php else: ?>
                            <?php foreach ($agentTasks as $task): ?>
                                <div class="task-card bg-white border rounded-lg shadow-sm p-4 priority-<?= $task['priority'] ?> status-<?= $task['status'] ?>">
                                    <div class="flex justify-between items-start">
                                        <h3 class="font-medium text-gray-900"><?= htmlspecialchars($task['title']) ?></h3>
                                        <span class="text-xs font-medium uppercase 
                                            <?php 
                                            switch ($task['status']) {
                                                case 'pending': echo 'text-gray-600'; break;
                                                case 'in_progress': echo 'text-blue-600'; break;
                                                case 'completed': echo 'text-green-600'; break;
                                                case 'failed': echo 'text-red-600'; break;
                                                default: echo 'text-gray-600';
                                            }
                                            ?>">
                                            <?= str_replace('_', ' ', $task['status']) ?>
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1"><?= htmlspecialchars(substr($task['description'], 0, 100)) ?><?= strlen($task['description']) > 100 ? '...' : '' ?></p>
                                    <div class="flex justify-between items-center mt-3">
                                        <span class="text-xs text-gray-500">
                                            <?php if ($task['due_date']): ?>
                                                <i class="far fa-calendar-alt mr-1"></i> Due: <?= date('M j, Y', strtotime($task['due_date'])) ?>
                                            <?php endif; ?>
                                        </span>
                                        <div>
                                            <a href="/momentum/ai-agents/tasks/view/<?= $task['id'] ?>" class="text-xs text-indigo-600 hover:text-indigo-800">View</a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Right Column: Chat Interface -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Interact with Aegis Director</h2>
                    
                    <?php if (!empty($message)): ?>
                        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
                            <p><?= $message ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Chat Container -->
                    <div class="chat-container border rounded-lg p-4 mb-4" id="chat-container">
                        <?php if (empty($agentInteractions)): ?>
                            <div class="system-message p-3 mb-4 text-sm">
                                <p>Welcome to Aegis Director. I am your executive functioning partner designed to help you achieve rapid, tangible results on your projects and goals.</p>
                                <p class="mt-2">To begin, please tell me about your current project or goal. Be specific about what you want to accomplish and by when.</p>
                            </div>
                        <?php else: ?>
                            <?php foreach (array_reverse($agentInteractions) as $interaction): ?>
                                <?php if ($interaction['interaction_type'] !== 'system'): ?>
                                    <!-- User Message -->
                                    <div class="user-message p-3 mb-4 ml-8">
                                        <div class="flex items-start">
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-gray-900">You</p>
                                                <p class="text-sm text-gray-800"><?= nl2br(htmlspecialchars($interaction['content'])) ?></p>
                                                <p class="text-xs text-gray-500 mt-1"><?= date('M j, g:i a', strtotime($interaction['created_at'])) ?></p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <?php if (!empty($interaction['response'])): ?>
                                        <!-- Agent Response -->
                                        <div class="agent-message p-3 mb-4 mr-8">
                                            <div class="flex items-start">
                                                <div class="flex-1">
                                                    <p class="text-sm font-medium text-gray-900">Aegis Director</p>
                                                    <p class="text-sm text-gray-800"><?= nl2br(htmlspecialchars($interaction['response'])) ?></p>
                                                    <p class="text-xs text-gray-500 mt-1"><?= date('M j, g:i a', strtotime($interaction['created_at'])) ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        
                        <?php if (!empty($response)): ?>
                            <!-- Latest Response (if just generated) -->
                            <div class="agent-message p-3 mb-4 mr-8">
                                <div class="flex items-start">
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">Aegis Director</p>
                                        <p class="text-sm text-gray-800"><?= nl2br(htmlspecialchars($response)) ?></p>
                                        <p class="text-xs text-gray-500 mt-1"><?= date('M j, g:i a') ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Input Form -->
                    <form action="" method="POST" class="mt-4">
                        <div class="flex items-center">
                            <select name="interaction_type" class="mr-2 rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="command">Command</option>
                                <option value="query">Question</option>
                                <option value="feedback">Feedback</option>
                            </select>
                            <input type="text" name="interaction_content" placeholder="Type your message here..." required
                                class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <button type="submit" class="ml-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Send <i class="fas fa-paper-plane ml-2"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Scroll chat to bottom on load
        document.addEventListener('DOMContentLoaded', function() {
            const chatContainer = document.getElementById('chat-container');
            chatContainer.scrollTop = chatContainer.scrollHeight;
        });
    </script>
</body>
</html>
