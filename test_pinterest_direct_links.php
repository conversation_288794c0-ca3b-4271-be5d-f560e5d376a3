<?php
/**
 * Test Pinterest Direct Links
 * 
 * This script tests direct Pinterest links to ensure they work correctly.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Output HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pinterest Direct Links</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1, h2 { color: #333; }
        .link-container { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #e60023; color: white; text-decoration: none; border-radius: 4px; margin-right: 10px; }
        .btn:hover { background: #d50c22; }
        .info { background: #f8f9fa; padding: 10px; border-left: 4px solid #17a2b8; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
    </style>
    <script src="/momentum/js/pinterest-direct-access.js"></script>
</head>
<body>
    <h1>Test Pinterest Direct Links</h1>
    <p>This page tests different methods of linking to Pinterest pins to ensure they work correctly.</p>
    
    <div class="info">
        <p><strong>Note:</strong> If you see "Access Denied" errors or are redirected to an error page, the links are not working correctly.</p>
    </div>
    
    <h2>Test Links</h2>';

// Define test pin IDs
$testPinIds = [
    '573786808744734357',
    '1055599864844775',
    '4433299612528918',
    '6755468166998975'
];

// Test 1: Direct links
echo '<div class="link-container">
    <h3>Test 1: Direct Links</h3>
    <p>These links go directly to Pinterest without any redirects:</p>';

foreach ($testPinIds as $index => $pinId) {
    $directUrl = "https://www.pinterest.com/pin/{$pinId}/";
    echo '<p>
        <a href="' . $directUrl . '" target="_blank" class="btn">Direct Link ' . ($index + 1) . '</a>
        <code>' . htmlspecialchars($directUrl) . '</code>
    </p>';
}

echo '</div>';

// Test 2: Links with data attributes
echo '<div class="link-container">
    <h3>Test 2: Links with Data Attributes</h3>
    <p>These links have pin IDs in data attributes:</p>';

foreach ($testPinIds as $index => $pinId) {
    echo '<p>
        <a href="#" data-pin-id="' . $pinId . '" class="btn pinterest-pin-link">Data Attribute Link ' . ($index + 1) . '</a>
        <code>data-pin-id="' . $pinId . '"</code>
    </p>';
}

echo '</div>';

// Test 3: JavaScript-generated links
echo '<div class="link-container">
    <h3>Test 3: JavaScript-Generated Links</h3>
    <p>These links are generated by JavaScript:</p>
    <div id="js-links"></div>
</div>';

// Test 4: Manual open method
echo '<div class="link-container">
    <h3>Test 4: Manual Open Method</h3>
    <p>These buttons use window.open() to open Pinterest links:</p>';

foreach ($testPinIds as $index => $pinId) {
    $directUrl = "https://www.pinterest.com/pin/{$pinId}/";
    echo '<p>
        <button onclick="window.open(\'' . $directUrl . '\', \'_blank\')" class="btn">Open Link ' . ($index + 1) . '</button>
        <code>' . htmlspecialchars($directUrl) . '</code>
    </p>';
}

echo '</div>';

// Add JavaScript to generate links
echo '<script>
    document.addEventListener("DOMContentLoaded", function() {
        // Test pin IDs
        const testPinIds = ' . json_encode($testPinIds) . ';
        const jsLinksContainer = document.getElementById("js-links");
        
        // Generate links
        testPinIds.forEach((pinId, index) => {
            const directUrl = `https://www.pinterest.com/pin/${pinId}/`;
            const linkContainer = document.createElement("p");
            
            const link = document.createElement("a");
            link.href = directUrl;
            link.className = "btn";
            link.target = "_blank";
            link.textContent = `JS Link ${index + 1}`;
            
            const code = document.createElement("code");
            code.textContent = directUrl;
            
            linkContainer.appendChild(link);
            linkContainer.appendChild(document.createTextNode(" "));
            linkContainer.appendChild(code);
            
            jsLinksContainer.appendChild(linkContainer);
        });
        
        // Add click handlers to all Pinterest links
        document.querySelectorAll(".pinterest-pin-link").forEach(function(link) {
            link.addEventListener("click", function(e) {
                e.preventDefault();
                const pinId = this.getAttribute("data-pin-id");
                if (pinId) {
                    const directUrl = `https://www.pinterest.com/pin/${pinId}/`;
                    console.log("Opening Pinterest URL:", directUrl);
                    window.open(directUrl, "_blank");
                }
                return false;
            });
        });
    });
</script>';

// Add debugging information
echo '<h2>Debugging Information</h2>
<pre>
User Agent: ' . htmlspecialchars($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . '
Server: ' . htmlspecialchars($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . '
PHP Version: ' . phpversion() . '
</pre>';

echo '</body>
</html>';
