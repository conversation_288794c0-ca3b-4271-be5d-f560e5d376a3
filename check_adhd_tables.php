<?php
require_once 'src/utils/Database.php';

try {
    $db = Database::getInstance();

    $tables = [
        'medications',
        'medication_logs', 
        'medication_reminders',
        'adhd_triggers',
        'trigger_occurrences',
        'trigger_coping_strategies',
        'executive_function_exercises',
        'exercise_results',
        'executive_function_progress',
        'adhd_medications_reference'
    ];

    echo "Checking additional ADHD tables:\n";
    foreach ($tables as $table) {
        try {
            $result = $db->query("SHOW TABLES LIKE '{$table}'");
            $exists = $result && $result->rowCount() > 0;
            echo "- {$table}: " . ($exists ? "EXISTS" : "MISSING") . "\n";
        } catch (Exception $e) {
            echo "- {$table}: ERROR - " . $e->getMessage() . "\n";
        }
    }

    echo "\nChecking if tables have data:\n";
    $dataCheck = [
        'executive_function_exercises' => 'SELECT COUNT(*) as count FROM executive_function_exercises',
        'adhd_medications_reference' => 'SELECT COUNT(*) as count FROM adhd_medications_reference'
    ];

    foreach ($dataCheck as $table => $query) {
        try {
            $result = $db->query("SHOW TABLES LIKE '{$table}'");
            if ($result && $result->rowCount() > 0) {
                $countResult = $db->query($query);
                $count = $countResult->fetch()['count'];
                echo "- {$table}: {$count} records\n";
            }
        } catch (Exception $e) {
            echo "- {$table}: Cannot check data - " . $e->getMessage() . "\n";
        }
    }

} catch (Exception $e) {
    echo "Database connection error: " . $e->getMessage() . "\n";
}
