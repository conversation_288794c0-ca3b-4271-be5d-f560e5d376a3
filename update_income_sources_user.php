<?php
require_once 'src/utils/Database.php';

// Get the database instance
$db = Database::getInstance();

// Source user ID (the user who currently owns the income sources)
$sourceUserId = 2;

// Target user ID (the user who should own the income sources)
$targetUserId = 1;

// Get all income sources for the source user
$sources = $db->fetchAll("SELECT * FROM income_sources WHERE user_id = ?", [$sourceUserId]);
echo "Found " . count($sources) . " income sources for user ID $sourceUserId\n";

// Update each income source to belong to the target user
$updateCount = 0;
foreach ($sources as $source) {
    $result = $db->query(
        "UPDATE income_sources SET user_id = ? WHERE id = ?",
        [$targetUserId, $source['id']]
    );
    
    if ($result) {
        $updateCount++;
        echo "Updated income source ID " . $source['id'] . " (" . $source['name'] . ") to user ID $targetUserId\n";
    }
}

echo "\nUpdated $updateCount income sources to user ID $targetUserId\n";

// Verify the update
$newSources = $db->fetchAll("SELECT * FROM income_sources WHERE user_id = ?", [$targetUserId]);
echo "User ID $targetUserId now has " . count($newSources) . " income sources\n";

// List the updated sources
if (count($newSources) > 0) {
    echo "\nIncome sources for user ID $targetUserId:\n";
    foreach ($newSources as $source) {
        echo "- ID: " . $source['id'] . ", Name: " . $source['name'] . "\n";
    }
}
