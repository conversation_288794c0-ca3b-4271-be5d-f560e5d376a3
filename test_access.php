<?php
/**
 * Test Access File
 * 
 * This file is used to test if the server can access files in the root directory.
 */

// Output basic information
echo "<h1>Test Access File</h1>";
echo "<p>This file is accessible at: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>Server Name: " . $_SERVER['SERVER_NAME'] . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Script Filename: " . $_SERVER['SCRIPT_FILENAME'] . "</p>";

// Check if we can access other files
echo "<h2>File Access Tests:</h2>";
$files = [
    'aegis-director-interface.php',
    'public/aegis-director-interface.php',
    'create_aegis_director_agent.php',
    '.htaccess'
];

echo "<ul>";
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<li>{$file}: <span style='color:green'>EXISTS</span> (Size: " . filesize($file) . " bytes)</li>";
    } else {
        echo "<li>{$file}: <span style='color:red'>MISSING</span></li>";
    }
}
echo "</ul>";

// List all PHP files in the current directory
echo "<h2>PHP Files in Current Directory:</h2>";
$phpFiles = glob("*.php");
echo "<ul>";
foreach ($phpFiles as $phpFile) {
    echo "<li>{$phpFile} (Size: " . filesize($phpFile) . " bytes)</li>";
}
echo "</ul>";

// List all PHP files in the public directory
echo "<h2>PHP Files in Public Directory:</h2>";
$publicPhpFiles = glob("public/*.php");
echo "<ul>";
if (count($publicPhpFiles) > 0) {
    foreach ($publicPhpFiles as $phpFile) {
        echo "<li>{$phpFile} (Size: " . filesize($phpFile) . " bytes)</li>";
    }
} else {
    echo "<li>No PHP files found in public directory</li>";
}
echo "</ul>";

// Check .htaccess content
echo "<h2>.htaccess Content:</h2>";
if (file_exists('.htaccess')) {
    echo "<pre>" . htmlspecialchars(file_get_contents('.htaccess')) . "</pre>";
} else {
    echo "<p>No .htaccess file found</p>";
}

// Provide links to test
echo "<h2>Test Links:</h2>";
echo "<ul>";
echo "<li><a href='/momentum/test_access.php'>This file</a></li>";
echo "<li><a href='/momentum/aegis-director-interface.php'>Aegis Director Interface (Root)</a></li>";
echo "<li><a href='/momentum/public/aegis-director-interface.php'>Aegis Director Interface (Public)</a></li>";
echo "<li><a href='/momentum/create_aegis_director_agent.php'>Create Aegis Director Agent</a></li>";
echo "<li><a href='/momentum/public/aegis-director.php'>Aegis Director Redirect</a></li>";
echo "</ul>";
