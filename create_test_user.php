<?php
require_once 'src/utils/Database.php';

try {
    $db = Database::getInstance();
    $users = $db->fetchAll('SELECT id, name, email FROM users LIMIT 3');
    
    if (empty($users)) {
        echo "No users found. Creating test user...\n";
        $hashedPassword = password_hash('test123', PASSWORD_DEFAULT);
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => $hashedPassword,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        $result = $db->insert('users', $userData);
        if ($result) {
            echo "Test user created successfully!\n";
            echo "Email: <EMAIL>\n";
            echo "Password: test123\n";
        } else {
            echo "Failed to create test user.\n";
        }
    } else {
        echo "Existing users found:\n";
        foreach ($users as $user) {
            echo "- ID: {$user['id']}, Name: {$user['name']}, Email: {$user['email']}\n";
        }
        echo "\nYou can use any of these accounts to test the system.\n";
        echo "If you don't know the password, you can reset it or use the test account.\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
