<?php
// Include database utility
require_once 'src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

// Add brigade_type column if it doesn't exist
echo "Adding brigade_type column to projects table...\n";
$result = $db->query("ALTER TABLE projects ADD COLUMN IF NOT EXISTS brigade_type VARCHAR(50) NULL");

if ($result !== false) {
    echo "Successfully added or confirmed brigade_type column.\n";
} else {
    echo "Failed to add brigade_type column.\n";
}

// Add is_brigade_template column if it doesn't exist
echo "Adding is_brigade_template column to projects table...\n";
$result = $db->query("ALTER TABLE projects ADD COLUMN IF NOT EXISTS is_brigade_template BOOLEAN DEFAULT FALSE");

if ($result !== false) {
    echo "Successfully added or confirmed is_brigade_template column.\n";
} else {
    echo "Failed to add is_brigade_template column.\n";
}

// Add parent_brigade_id column if it doesn't exist
echo "Adding parent_brigade_id column to projects table...\n";
$result = $db->query("ALTER TABLE projects ADD COLUMN IF NOT EXISTS parent_brigade_id INT NULL");

if ($result !== false) {
    echo "Successfully added or confirmed parent_brigade_id column.\n";
} else {
    echo "Failed to add parent_brigade_id column.\n";
}

echo "\nTable structure update completed.\n";
