# IDE and editor files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# PHP specific
vendor/
composer.lock
.env
.env.backup
.phpunit.result.cache

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache
.cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Database files
*.sqlite
*.sqlite3
*.db

# Temporary files
tmp/
temp/
