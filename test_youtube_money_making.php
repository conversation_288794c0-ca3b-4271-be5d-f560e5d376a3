<?php
/**
 * Test YouTube Money Making Agent
 * 
 * This script tests the YouTube Money Making agent functionality.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';

// Initialize models
$db = Database::getInstance();
$agentModel = new AIAgent();
$taskModel = new AIAgentTask();
$interactionModel = new AIAgentInteraction();

// Check database connection
if ($db) {
    echo "Database connection successful<br>";
} else {
    echo "Database connection failed<br>";
    exit;
}

// Get the YouTube Browser agent
$agents = $agentModel->getUserAgents(1);
$youtubeAgentId = null;

foreach ($agents as $agent) {
    if ($agent['name'] === 'YouTube Browser') {
        $youtubeAgentId = $agent['id'];
        break;
    }
}

if (!$youtubeAgentId) {
    echo "YouTube Browser agent not found. Running create_youtube_agent.php...<br>";
    include 'create_youtube_agent.php';
    
    // Check again
    $agents = $agentModel->getUserAgents(1);
    foreach ($agents as $agent) {
        if ($agent['name'] === 'YouTube Browser') {
            $youtubeAgentId = $agent['id'];
            break;
        }
    }
    
    if (!$youtubeAgentId) {
        echo "Failed to create YouTube Browser agent.<br>";
        exit;
    }
}

echo "Found YouTube Browser agent with ID: {$youtubeAgentId}<br>";

// Test search parameters
$searchTopic = "passive income OR make money online OR side hustle";
$maxResults = 5;
$daysAgo = 30;
$minViews = 1000;

// Create a task for this search
$taskTitle = "Find YouTube videos about {$searchTopic} from the last {$daysAgo} days";
$taskDescription = "Search YouTube for videos about {$searchTopic} that were published in the last {$daysAgo} days. " .
                  "Focus on videos with at least {$minViews} views. Collect the links, titles, channel names, and " .
                  "publication dates. Analyze the content for actionable money-making techniques.";

$taskId = $taskModel->createTask([
    'agent_id' => $youtubeAgentId,
    'user_id' => 1,
    'title' => $taskTitle,
    'description' => $taskDescription,
    'priority' => 'high',
    'status' => 'in_progress',
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
]);

if ($taskId) {
    echo "Created task with ID: {$taskId}<br>";
} else {
    echo "Failed to create task<br>";
    exit;
}

// Log the start of the task
$interactionId = $interactionModel->createInteraction([
    'agent_id' => $youtubeAgentId,
    'user_id' => 1,
    'interaction_type' => 'system',
    'content' => "Starting task: {$taskTitle}",
    'created_at' => date('Y-m-d H:i:s')
]);

if ($interactionId) {
    echo "Created interaction with ID: {$interactionId}<br>";
} else {
    echo "Failed to create interaction<br>";
    exit;
}

// Run the YouTube Money Making script
echo "Running YouTube Money Making script...<br>";
ob_start();
include 'youtube_money_making.php';
$results = ob_get_clean();

echo "YouTube Money Making script executed successfully!<br>";
echo "You can view the YouTube Money Making interface at: <a href='/momentum/youtube_money_making_interface.php'>YouTube Money Making Interface</a><br>";
echo "You can view the agent at: <a href='/momentum/ai-agents/view/{$youtubeAgentId}'>YouTube Browser Agent</a><br>";
echo "You can view all agents at: <a href='/momentum/ai-agents'>AI Agents Dashboard</a><br>";
