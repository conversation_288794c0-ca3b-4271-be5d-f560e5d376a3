<?php
// Include necessary files
require_once 'src/utils/Session.php';
require_once 'src/utils/Database.php';
require_once 'src/models/IncomeSource.php';

// Start the session
Session::start();

echo "=== SESSION STATUS ===\n";
echo "Session ID: " . session_id() . "\n";
echo "Session Status: " . (session_status() === PHP_SESSION_ACTIVE ? "Active" : "Not Active") . "\n\n";

// Check if user is logged in
echo "=== LOGIN STATUS ===\n";
if (Session::isLoggedIn()) {
    $user = Session::getUser();
    echo "User is logged in\n";
    echo "User ID: " . $user['id'] . "\n";
    echo "Username: " . $user['username'] . "\n";
    
    // Use this user ID for further checks
    $userId = $user['id'];
} else {
    echo "User is not logged in\n";
    
    // Use user ID 1 for further checks since we updated the income sources to this ID
    $userId = 1;
    echo "Using default user ID: $userId for testing\n";
}

echo "\n=== DATABASE CHECKS ===\n";
$db = Database::getInstance();

// Check if the user exists
$userResult = $db->fetchOne("SELECT id, username FROM users WHERE id = ?", [$userId]);
echo "User exists in database: " . ($userResult ? "Yes" : "No") . "\n";
if ($userResult) {
    echo "Database User ID: " . $userResult['id'] . "\n";
    echo "Database Username: " . $userResult['username'] . "\n";
}

// Check income sources for this user
$sourcesResult = $db->fetchAll("SELECT * FROM income_sources WHERE user_id = ?", [$userId]);
echo "\nIncome sources for user ID $userId: " . count($sourcesResult) . "\n";
if (count($sourcesResult) > 0) {
    foreach ($sourcesResult as $source) {
        echo "- ID: " . $source['id'] . ", Name: " . $source['name'] . ", Active: " . ($source['is_active'] ? "Yes" : "No") . "\n";
    }
} else {
    echo "No income sources found for this user.\n";
}

// Check all income sources in the database
$allSources = $db->fetchAll("SELECT * FROM income_sources");
echo "\nTotal income sources in database: " . count($allSources) . "\n";
if (count($allSources) > 0) {
    foreach ($allSources as $source) {
        echo "- ID: " . $source['id'] . ", User ID: " . $source['user_id'] . ", Name: " . $source['name'] . "\n";
    }
}

echo "\n=== MODEL CHECKS ===\n";
// Test the IncomeSource model directly
$incomeSourceModel = new IncomeSource();

// Get current date range
$startDate = date('Y-m-01'); // First day of current month
$endDate = date('Y-m-t'); // Last day of current month

echo "Date range: $startDate to $endDate\n";

// Get income sources using the model
$modelSources = $incomeSourceModel->getUserIncomeSources($userId, true); // Include inactive sources
echo "\nIncome sources from getUserIncomeSources(): " . count($modelSources) . "\n";

// Get income sources with total income
$sourcesWithIncome = $incomeSourceModel->getSourcesWithTotalIncome($userId, $startDate, $endDate);
echo "\nIncome sources from getSourcesWithTotalIncome(): " . count($sourcesWithIncome) . "\n";

// Check the SQL query directly
$sql = "SELECT
            s.id,
            s.name,
            s.description,
            s.is_active,
            s.is_recurring,
            s.expected_amount,
            s.category,
            COUNT(DISTINCT f.id) as transaction_count,
            SUM(CASE WHEN f.transaction_type = 'monetary' THEN f.amount
                     WHEN f.transaction_type = 'non_monetary' THEN COALESCE(f.fair_market_value, 0)
                     ELSE 0 END) as total_income
        FROM income_sources s
        LEFT JOIN income_source_transactions ist ON s.id = ist.income_source_id
        LEFT JOIN finances f ON ist.transaction_id = f.id AND f.type = 'income'
        WHERE s.user_id = ?
        GROUP BY s.id
        ORDER BY total_income DESC";

$directQueryResults = $db->fetchAll($sql, [$userId]);
echo "\nDirect SQL query results: " . count($directQueryResults) . "\n";

echo "\n=== FILE CHECKS ===\n";
// Check if the view file exists
$viewPath = 'src/views/finances/income_sources/index.php';
echo "View file exists: " . (file_exists($viewPath) ? 'Yes' : 'No') . "\n";

// Check if the controller file exists
$controllerPath = 'src/controllers/IncomeSourceController.php';
echo "Controller file exists: " . (file_exists($controllerPath) ? 'Yes' : 'No') . "\n";

// Check if the model file exists
$modelPath = 'src/models/IncomeSource.php';
echo "Model file exists: " . (file_exists($modelPath) ? 'Yes' : 'No') . "\n";
