<?php
/**
 * PHP Optimization Script
 *
 * This script checks and optimizes PHP configuration for better performance.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Check if OPcache is enabled
if (!extension_loaded('Zend OPcache')) {
    echo "OPcache is not enabled. Please enable it in php.ini.\n";
    echo "Add the following to your php.ini file:\n";
    echo "zend_extension=opcache.so\n";
    echo "opcache.enable=1\n";
    echo "opcache.enable_cli=1\n";
} else {
    echo "OPcache is enabled.\n";
    
    // Get current OPcache configuration
    $opcacheConfig = opcache_get_configuration();
    
    // Display current configuration
    echo "Current OPcache configuration:\n";
    echo "- Memory consumption: " . round($opcacheConfig['directives']['opcache.memory_consumption'] / 1024 / 1024, 2) . " MB\n";
    echo "- Max accelerated files: " . $opcacheConfig['directives']['opcache.max_accelerated_files'] . "\n";
    echo "- Revalidate frequency: " . $opcacheConfig['directives']['opcache.revalidate_freq'] . " seconds\n";
    echo "- Save comments: " . ($opcacheConfig['directives']['opcache.save_comments'] ? 'Yes' : 'No') . "\n";
    
    // Recommended configuration
    echo "\nRecommended OPcache configuration for production:\n";
    echo "opcache.memory_consumption=128\n";
    echo "opcache.interned_strings_buffer=8\n";
    echo "opcache.max_accelerated_files=4000\n";
    echo "opcache.revalidate_freq=60\n";
    echo "opcache.fast_shutdown=1\n";
    echo "opcache.enable_cli=1\n";
    echo "opcache.save_comments=1\n";
    echo "opcache.validate_timestamps=0 (in production)\n";
}

// Check if APCu is enabled
if (!extension_loaded('apcu')) {
    echo "\nAPCu is not enabled. Consider enabling it for user data caching.\n";
    echo "Add the following to your php.ini file:\n";
    echo "extension=apcu.so\n";
    echo "apc.enabled=1\n";
    echo "apc.shm_size=32M\n";
} else {
    echo "\nAPCu is enabled.\n";
    
    // Get current APCu configuration
    $apcuInfo = apcu_cache_info();
    
    // Display current configuration
    echo "Current APCu configuration:\n";
    echo "- Memory size: " . round($apcuInfo['mem_size'] / 1024 / 1024, 2) . " MB\n";
    echo "- Entries: " . $apcuInfo['num_entries'] . "\n";
    echo "- Hits: " . $apcuInfo['num_hits'] . "\n";
    echo "- Misses: " . $apcuInfo['num_misses'] . "\n";
}

// Create optimized PHP configuration file
$phpIniPath = BASE_PATH . '/php.ini.optimized';
$phpIniContent = <<<EOT
; Optimized PHP Configuration for Momentum

; OPcache settings
zend_extension=opcache.so
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=60
opcache.fast_shutdown=1
opcache.enable_cli=1
opcache.save_comments=1
opcache.validate_timestamps=0

; APCu settings
extension=apcu.so
apc.enabled=1
apc.shm_size=32M

; General settings
memory_limit=256M
max_execution_time=30
post_max_size=8M
upload_max_filesize=2M
max_file_uploads=20
allow_url_fopen=On
display_errors=Off
display_startup_errors=Off
log_errors=On
error_log=/path/to/error.log
error_reporting=E_ALL & ~E_DEPRECATED & ~E_STRICT
default_charset="UTF-8"
date.timezone=UTC

; Session settings
session.save_handler=files
session.use_strict_mode=1
session.use_cookies=1
session.use_only_cookies=1
session.name=PHPSESSID
session.cookie_secure=On
session.cookie_httponly=On
session.cookie_samesite="Lax"
session.gc_maxlifetime=1440
session.gc_probability=1
session.gc_divisor=100

; Output buffering
output_buffering=4096
EOT;

file_put_contents($phpIniPath, $phpIniContent);
echo "\nOptimized PHP configuration saved to: $phpIniPath\n";
echo "You can use this file as a reference for your php.ini configuration.\n";

// Create a PHP performance test script
$perfTestPath = BASE_PATH . '/php-performance-test.php';
$perfTestContent = <<<EOT
<?php
/**
 * PHP Performance Test
 *
 * This script tests PHP performance with different configurations.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start time
\$startTime = microtime(true);

// Test OPcache
echo "Testing OPcache...\n";
if (extension_loaded('Zend OPcache')) {
    echo "OPcache is enabled.\n";
    
    // Test OPcache performance
    \$opcacheStartTime = microtime(true);
    
    // Run a simple test
    for (\$i = 0; \$i < 1000; \$i++) {
        \$a = 1 + 1;
    }
    
    \$opcacheEndTime = microtime(true);
    \$opcacheTime = \$opcacheEndTime - \$opcacheStartTime;
    
    echo "OPcache test completed in " . round(\$opcacheTime * 1000, 2) . " ms.\n";
} else {
    echo "OPcache is not enabled.\n";
}

// Test file operations
echo "\nTesting file operations...\n";
\$fileStartTime = microtime(true);

// Create a test file
\$testFile = __DIR__ . '/test-file.txt';
file_put_contents(\$testFile, 'Test content');

// Read the file 1000 times
for (\$i = 0; \$i < 1000; \$i++) {
    \$content = file_get_contents(\$testFile);
}

// Delete the test file
unlink(\$testFile);

\$fileEndTime = microtime(true);
\$fileTime = \$fileEndTime - \$fileStartTime;

echo "File operations test completed in " . round(\$fileTime * 1000, 2) . " ms.\n";

// Test database operations
echo "\nTesting database operations...\n";
\$dbStartTime = microtime(true);

// Connect to database
try {
    \$db = new PDO('mysql:host=localhost;dbname=momentum;charset=utf8mb4', 'root', '');
    \$db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Run a simple query 100 times
    for (\$i = 0; \$i < 100; \$i++) {
        \$stmt = \$db->query('SELECT 1');
        \$result = \$stmt->fetch();
    }
    
    \$dbEndTime = microtime(true);
    \$dbTime = \$dbEndTime - \$dbStartTime;
    
    echo "Database operations test completed in " . round(\$dbTime * 1000, 2) . " ms.\n";
} catch (PDOException \$e) {
    echo "Database connection failed: " . \$e->getMessage() . "\n";
}

// End time
\$endTime = microtime(true);
\$totalTime = \$endTime - \$startTime;

echo "\nTotal test completed in " . round(\$totalTime * 1000, 2) . " ms.\n";
EOT;

file_put_contents($perfTestPath, $perfTestContent);
echo "\nPHP performance test script saved to: $perfTestPath\n";
echo "You can run this script to test PHP performance with different configurations.\n";
