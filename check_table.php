<?php
// Include database utility
require_once 'src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

// Check projects table structure
$result = $db->query('DESCRIBE projects');
$columns = $db->fetchAll($result);

echo "Projects Table Structure:\n";
foreach ($columns as $column) {
    echo $column['Field'] . " - " . $column['Type'] . " - " . ($column['Null'] === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
}

// Check if brigade_type column exists
$hasBrigadeType = false;
foreach ($columns as $column) {
    if ($column['Field'] === 'brigade_type') {
        $hasBrigadeType = true;
        break;
    }
}

if ($hasBrigadeType) {
    echo "\nThe brigade_type column exists in the projects table.\n";
} else {
    echo "\nThe brigade_type column does NOT exist in the projects table.\n";
    
    // Add the column if it doesn't exist
    echo "Adding brigade_type column to projects table...\n";
    $result = $db->query("ALTER TABLE projects ADD COLUMN brigade_type VARCHAR(50) NULL");
    
    if ($result !== false) {
        echo "Successfully added brigade_type column.\n";
    } else {
        echo "Failed to add brigade_type column.\n";
    }
}

// Check if is_brigade_template column exists
$hasBrigadeTemplate = false;
foreach ($columns as $column) {
    if ($column['Field'] === 'is_brigade_template') {
        $hasBrigadeTemplate = true;
        break;
    }
}

if ($hasBrigadeTemplate) {
    echo "\nThe is_brigade_template column exists in the projects table.\n";
} else {
    echo "\nThe is_brigade_template column does NOT exist in the projects table.\n";
    
    // Add the column if it doesn't exist
    echo "Adding is_brigade_template column to projects table...\n";
    $result = $db->query("ALTER TABLE projects ADD COLUMN is_brigade_template BOOLEAN DEFAULT FALSE");
    
    if ($result !== false) {
        echo "Successfully added is_brigade_template column.\n";
    } else {
        echo "Failed to add is_brigade_template column.\n";
    }
}

// Check if parent_brigade_id column exists
$hasParentBrigadeId = false;
foreach ($columns as $column) {
    if ($column['Field'] === 'parent_brigade_id') {
        $hasParentBrigadeId = true;
        break;
    }
}

if ($hasParentBrigadeId) {
    echo "\nThe parent_brigade_id column exists in the projects table.\n";
} else {
    echo "\nThe parent_brigade_id column does NOT exist in the projects table.\n";
    
    // Add the column if it doesn't exist
    echo "Adding parent_brigade_id column to projects table...\n";
    $result = $db->query("ALTER TABLE projects ADD COLUMN parent_brigade_id INT NULL");
    
    if ($result !== false) {
        echo "Successfully added parent_brigade_id column.\n";
    } else {
        echo "Failed to add parent_brigade_id column.\n";
    }
}

echo "\nTable structure check completed.\n";
