<?php
// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Include the Database class
require_once BASE_PATH . '/src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

// Test data
$testData = [
    'user_id' => 2, // Assuming user ID 2 exists
    'name' => 'Test Medication',
    'dosage' => 10,
    'dosage_unit' => 'mg',
    'frequency' => 'daily',
    'instructions' => 'Take with food',
    'prescriber' => 'Dr. Test',
    'pharmacy' => 'Test Pharmacy',
    'start_date' => date('Y-m-d'),
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
];

echo "Inserting test medication...\n";
$newId = $db->insert('medications', $testData);

if ($newId) {
    echo "Success! New medication created with ID: $newId\n";
    
    // Verify the medication was created
    $newMedication = $db->fetchOne("SELECT * FROM medications WHERE id = ?", [$newId]);
    if ($newMedication) {
        echo "Verified: Medication exists in database.\n";
        echo "Medication details:\n";
        print_r($newMedication);
    } else {
        echo "Error: Could not find the newly created medication.\n";
    }
} else {
    echo "Error: Failed to create test medication.\n";
    
    // Check if there are any errors in the query
    echo "Checking for errors...\n";
    $result = $db->query("SHOW ERRORS");
    if ($result) {
        $errors = $result->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($errors)) {
            echo "Database errors:\n";
            print_r($errors);
        } else {
            echo "No database errors found.\n";
        }
    }
    
    // Check if there are any warnings
    echo "Checking for warnings...\n";
    $result = $db->query("SHOW WARNINGS");
    if ($result) {
        $warnings = $result->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($warnings)) {
            echo "Database warnings:\n";
            print_r($warnings);
        } else {
            echo "No database warnings found.\n";
        }
    }
}
