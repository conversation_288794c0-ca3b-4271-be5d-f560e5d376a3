<?php
/**
 * YouTube Agent Interface
 * 
 * This script provides a simple interface to set up and run the YouTube browsing agent.
 */

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentCategory.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';

// Initialize models
$db = Database::getInstance();
$agentModel = new AIAgent();
$categoryModel = new AIAgentCategory();
$taskModel = new AIAgentTask();
$interactionModel = new AIAgentInteraction();

// Check if the agent exists
$agents = $agentModel->getUserAgents(1);
$youtubeAgentExists = false;
$youtubeAgentId = null;

foreach ($agents as $agent) {
    if ($agent['name'] === 'YouTube Browser') {
        $youtubeAgentExists = true;
        $youtubeAgentId = $agent['id'];
        break;
    }
}

// Process form submission
$message = '';
$results = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'create_agent' && !$youtubeAgentExists) {
            // Create the agent
            include 'create_youtube_agent.php';
            $message = 'YouTube Browser agent created successfully!';
            
            // Refresh agent data
            $agents = $agentModel->getUserAgents(1);
            foreach ($agents as $agent) {
                if ($agent['name'] === 'YouTube Browser') {
                    $youtubeAgentExists = true;
                    $youtubeAgentId = $agent['id'];
                    break;
                }
            }
        } elseif ($_POST['action'] === 'run_agent' && $youtubeAgentExists) {
            // Run the agent
            ob_start();
            include 'youtube_browser.php';
            $results = ob_get_clean();
            $message = 'YouTube Browser agent executed successfully!';
        }
    }
}

// Get agent details if it exists
$agentDetails = null;
if ($youtubeAgentExists && $youtubeAgentId) {
    $agentDetails = $agentModel->getAgent($youtubeAgentId, 1);
    
    // Get agent tasks
    $agentTasks = $taskModel->getAgentTasks($youtubeAgentId);
    
    // Get agent interactions
    $agentInteractions = $interactionModel->getAgentInteractions($youtubeAgentId, 10);
}

// HTML output
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Browser Agent Interface</title>
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-6">YouTube Browser Agent Interface</h1>
        
        <?php if ($message): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
                <p><?php echo $message; ?></p>
            </div>
        <?php endif; ?>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Agent Status</h2>
            
            <?php if ($youtubeAgentExists): ?>
                <div class="flex items-center mb-4">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <p>YouTube Browser agent is set up and ready to use.</p>
                </div>
                
                <div class="mb-4">
                    <h3 class="font-medium mb-2">Agent Details:</h3>
                    <ul class="list-disc list-inside ml-4">
                        <li>Name: <?php echo $agentDetails['name']; ?></li>
                        <li>Category: <?php echo $agentDetails['category_name']; ?></li>
                        <li>Status: <?php echo ucfirst($agentDetails['status']); ?></li>
                        <li>Intelligence Level: <?php echo $agentDetails['intelligence_level']; ?>/10</li>
                        <li>Last Active: <?php echo $agentDetails['last_active']; ?></li>
                    </ul>
                </div>
                
                <form method="post" class="mb-4">
                    <input type="hidden" name="action" value="run_agent">
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded">
                        <i class="fas fa-play mr-2"></i> Run YouTube Browser Agent
                    </button>
                </form>
                
                <a href="/momentum/ai-agents/view/<?php echo $youtubeAgentId; ?>" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                    <i class="fas fa-external-link-alt mr-1"></i> View Agent in AI Agents Dashboard
                </a>
            <?php else: ?>
                <div class="flex items-center mb-4">
                    <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                    <p>YouTube Browser agent is not set up yet.</p>
                </div>
                
                <form method="post">
                    <input type="hidden" name="action" value="create_agent">
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded">
                        <i class="fas fa-plus mr-2"></i> Create YouTube Browser Agent
                    </button>
                </form>
            <?php endif; ?>
        </div>
        
        <?php if ($youtubeAgentExists && !empty($agentTasks)): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Agent Tasks</h2>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white dark:bg-gray-800">
                        <thead>
                            <tr>
                                <th class="py-2 px-4 border-b border-gray-200 dark:border-gray-700 text-left">Title</th>
                                <th class="py-2 px-4 border-b border-gray-200 dark:border-gray-700 text-left">Status</th>
                                <th class="py-2 px-4 border-b border-gray-200 dark:border-gray-700 text-left">Priority</th>
                                <th class="py-2 px-4 border-b border-gray-200 dark:border-gray-700 text-left">Due Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($agentTasks as $task): ?>
                                <tr>
                                    <td class="py-2 px-4 border-b border-gray-200 dark:border-gray-700"><?php echo $task['title']; ?></td>
                                    <td class="py-2 px-4 border-b border-gray-200 dark:border-gray-700">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            <?php 
                                                switch ($task['status']) {
                                                    case 'completed': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'; break;
                                                    case 'in_progress': echo 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'; break;
                                                    case 'failed': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'; break;
                                                    default: echo 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
                                                }
                                            ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $task['status'])); ?>
                                        </span>
                                    </td>
                                    <td class="py-2 px-4 border-b border-gray-200 dark:border-gray-700"><?php echo ucfirst($task['priority']); ?></td>
                                    <td class="py-2 px-4 border-b border-gray-200 dark:border-gray-700"><?php echo date('Y-m-d', strtotime($task['due_date'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($results)): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Agent Results</h2>
                <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded overflow-auto max-h-96">
                    <?php echo $results; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($youtubeAgentExists && !empty($agentInteractions)): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Recent Interactions</h2>
                
                <div class="space-y-4">
                    <?php foreach ($agentInteractions as $interaction): ?>
                        <div class="border-l-4 
                            <?php 
                                switch ($interaction['interaction_type']) {
                                    case 'command': echo 'border-blue-500'; break;
                                    case 'query': echo 'border-green-500'; break;
                                    case 'feedback': echo 'border-yellow-500'; break;
                                    case 'system': echo 'border-purple-500'; break;
                                    default: echo 'border-gray-500';
                                }
                            ?> pl-4 py-2">
                            <div class="flex justify-between items-start">
                                <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                    <?php echo ucfirst($interaction['interaction_type']); ?>
                                </span>
                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                    <?php echo date('Y-m-d H:i', strtotime($interaction['created_at'])); ?>
                                </span>
                            </div>
                            <div class="mt-1 text-sm whitespace-pre-wrap">
                                <?php 
                                    // Limit content length for display
                                    $content = $interaction['content'];
                                    if (strlen($content) > 200) {
                                        echo htmlspecialchars(substr($content, 0, 200)) . '...';
                                    } else {
                                        echo htmlspecialchars($content);
                                    }
                                ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="mt-6">
            <a href="/momentum/ai-agents" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                <i class="fas fa-arrow-left mr-1"></i> Back to AI Agents Dashboard
            </a>
        </div>
    </div>
</body>
</html>
