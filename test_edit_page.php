<?php
// Simple test to check if edit page renders completely
require_once 'src/utils/Database.php';
require_once 'src/models/Note.php';
require_once 'src/utils/View.php';

try {
    echo "Testing edit page rendering...\n";
    
    $noteModel = new Note();
    $noteId = 12;
    
    // Get note
    $note = $noteModel->find($noteId);
    if (!$note) {
        echo "✗ Note not found\n";
        exit;
    }
    
    // Get categories
    $categories = $noteModel->getUserNoteCategories(2);
    $categoryNames = array_column($categories, 'category');
    
    echo "✓ Note found: " . $note['title'] . "\n";
    echo "✓ Categories: " . count($categoryNames) . " found\n";
    
    // Test rendering the edit view
    ob_start();
    
    // Set up variables that the view expects
    $data = [
        'note' => $note,
        'categories' => $categoryNames
    ];
    
    // Extract variables for the view
    extract($data);
    
    // Include the view
    include 'src/views/notes/edit.php';
    
    $output = ob_get_clean();
    
    // Check if key elements are present
    $checks = [
        'ADHD-Friendly Settings' => strpos($output, 'ADHD-Friendly Settings') !== false,
        'Priority Level' => strpos($output, 'Priority Level') !== false,
        'Color Code' => strpos($output, 'Color Code') !== false,
        'Quick Save' => strpos($output, 'Quick Save') !== false,
        'Update Note' => strpos($output, 'Update Note') !== false,
        'Auto-save JavaScript' => strpos($output, 'NoteEditManager') !== false
    ];
    
    echo "\nChecking rendered elements:\n";
    foreach ($checks as $element => $found) {
        echo ($found ? '✓' : '✗') . " $element\n";
    }
    
    // Check for PHP errors
    if (strpos($output, 'Fatal error') !== false || strpos($output, 'Parse error') !== false) {
        echo "✗ PHP errors found in output\n";
    } else {
        echo "✓ No PHP errors detected\n";
    }
    
    // Check output length
    echo "✓ Output length: " . strlen($output) . " characters\n";
    
    if (strlen($output) > 10000) {
        echo "✓ Edit page appears to render completely\n";
    } else {
        echo "✗ Edit page output seems too short\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}
?>
