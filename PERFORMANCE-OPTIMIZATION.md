# Performance Optimization Guide

This guide provides instructions on how to optimize the performance of the Momentum application.

## Important Note

The performance optimizations have been implemented in a way that they don't interfere with the existing functionality. The code has been updated to handle cases where configuration files might not exist, and the BASE_PATH constant is now defined automatically if it's not already defined.

## Table of Contents

1. [Overview](#overview)
2. [Database Optimizations](#database-optimizations)
3. [Front-end Optimizations](#front-end-optimizations)
4. [Caching Implementation](#caching-implementation)
5. [Asset Loading Optimization](#asset-loading-optimization)
6. [PHP Optimizations](#php-optimizations)
7. [Running All Optimizations](#running-all-optimizations)

## Overview

The Momentum application has been optimized for better performance. The optimizations include:

- Database query caching and optimization
- JavaScript and CSS bundling and minification
- Image optimization and responsive image generation
- Browser caching with proper cache headers
- PHP performance improvements with OPcache

## Database Optimizations

The database layer has been optimized with the following improvements:

- Query caching for frequently accessed data
- Reduced debug logging in production
- Optimized model methods with caching

### Usage

The database optimizations are automatically applied when using the models. The `BaseModel` class now includes caching for all read operations.

Example:

```php
// This query will be cached
$user = $userModel->find(1);

// This query will use the cache if available
$tasks = $taskModel->findBy('user_id', 1);

// This will clear the cache for this model
$taskModel->update(1, ['title' => 'New Title']);
```

## Front-end Optimizations

The front-end has been optimized with the following improvements:

- JavaScript bundling and minification with Webpack
- CSS optimization with Tailwind's purge feature
- Deferred loading of non-critical JavaScript
- Optimized DOM manipulation

### Usage

To build the front-end assets for development:

```bash
npm run dev
```

To build the front-end assets for production:

```bash
npm run prod
```

## Caching Implementation

The application now includes a caching layer with the following features:

- In-memory caching for frequently accessed data
- File-based caching for persistent data
- Cache invalidation when data changes

### Usage

The `Cache` class provides methods for caching data:

```php
// Get cache instance
$cache = Cache::getInstance();

// Store a value in cache
$cache->set('key', 'value', 3600); // Cache for 1 hour

// Get a value from cache
$value = $cache->get('key', 'default');

// Check if a key exists in cache
if ($cache->has('key')) {
    // Do something
}

// Remove a key from cache
$cache->forget('key');

// Clear all cache
$cache->clear();
```

## Asset Loading Optimization

The asset loading has been optimized with the following improvements:

- Proper cache headers for static assets
- Content-based cache busting
- HTTP/2 Server Push for critical assets
- Lazy loading for images

### Usage

The `View` class provides methods for loading assets with proper versioning:

```php
// In a view file
<?= View::css('/css/styles.css') ?>
<?= View::js('/js/script.js') ?>
<?= View::img('/images/logo.png', 'Logo', 'logo-class') ?>
```

## PHP Optimizations

The PHP layer has been optimized with the following improvements:

- OPcache configuration for better performance
- Reduced error logging in production
- Optimized autoloading

### Usage

To apply the PHP optimizations, you need to configure your PHP environment according to the recommendations in `php.ini.optimized`.

## Running All Optimizations

To run all optimizations at once, you can use the `optimize-all.php` script:

```bash
php optimize-all.php
```

This script will:

1. Run PHP optimization
2. Optimize images
3. Add HTTP/2 Server Push
4. Install npm dependencies if needed
5. Build assets for production
6. Generate asset manifest

## Individual Optimization Scripts

You can also run individual optimization scripts:

- `php optimize-php.php` - Optimize PHP configuration
- `php optimize-images.php` - Optimize images
- `php add-server-push.php` - Add HTTP/2 Server Push
- `php build-manifest.php` - Generate asset manifest

## Monitoring Performance

To monitor the performance of your application, you can use the following tools:

- Browser DevTools Network tab
- Lighthouse in Chrome DevTools
- WebPageTest.org
- GTmetrix

## Additional Recommendations

1. Configure your web server to use HTTP/2
2. Enable OPcache in production
3. Consider using a CDN for static assets
4. Set up a caching layer (Redis or Memcached)
5. Regularly monitor and optimize database queries
