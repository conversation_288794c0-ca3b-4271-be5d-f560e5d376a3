<?php
/**
 * Final Debug Agent Army Project Creation
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/Project.php';
require_once 'src/models/Task.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';
require_once 'src/models/ProjectAgentAssignment.php';
require_once 'src/models/AegisDirectorProjectManager.php';
require_once 'src/models/TaskDependency.php';

// Initialize database
$db = Database::getInstance();

// Check database connection
if ($db) {
    echo "Database connection successful\n";

    // Check if required tables exist
    $tables = ['projects', 'tasks', 'ai_agents', 'ai_agent_tasks', 'ai_agent_interactions', 'project_agent_assignments', 'task_dependencies'];

    echo "Checking database tables:\n";
    foreach ($tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '{$table}'");
        $exists = $result && $result->rowCount() > 0;
        echo "- {$table}: " . ($exists ? "EXISTS" : "MISSING") . "\n";
    }

    // Create a test project using the AegisDirectorProjectManager
    $userId = 1;
    $brigadeType = 'content_creation';
    $projectName = 'Test Agent Army Project';
    $projectDescription = 'This is a test project for the Content Creation Brigade.';
    $deadline = date('Y-m-d', strtotime('+7 days'));

    echo "Creating Agent Army project with data:\n";
    echo "User ID: {$userId}\n";
    echo "Brigade Type: {$brigadeType}\n";
    echo "Project Name: {$projectName}\n";
    echo "Project Description: {$projectDescription}\n";
    echo "Deadline: {$deadline}\n";

    // Create a custom AegisDirectorProjectManager with debugging
    class DebugAegisDirectorProjectManager extends AegisDirectorProjectManager {
        // Override createBrigadeTasks to add debugging
        public function createBrigadeTasks($projectId, $brigadeType, $userId) {
            echo "Creating tasks for {$brigadeType} brigade...\n";

            // Get the project
            $project = $this->projectModel->getProjectDetails($projectId, $userId);

            if (!$project) {
                echo "Project not found.\n";
                return false;
            }

            echo "Found project: {$project['name']}\n";

            // Get the Aegis Director agent
            $aegisDirector = $this->getAegisDirectorAgent($userId);

            if (!$aegisDirector) {
                echo "Aegis Director agent not found.\n";
                return false;
            }

            echo "Found Aegis Director agent: {$aegisDirector['name']}\n";

            // Define tasks based on brigade type
            $tasks = [];

            switch ($brigadeType) {
                case 'content_creation':
                    echo "Creating tasks for content creation brigade...\n";
                    $tasks = [
                        [
                            'title' => '1. Define content strategy and goals',
                            'description' => 'Establish clear objectives, target audience, and key performance indicators for the content.',
                            'priority' => 'high',
                            'estimated_time' => 120, // minutes
                            'status' => 'todo'
                        ],
                        [
                            'title' => '2. Conduct content audit and gap analysis',
                            'description' => 'Analyze existing content and identify opportunities for new content.',
                            'priority' => 'high',
                            'estimated_time' => 180,
                            'status' => 'todo'
                        ]
                    ];
                    break;

                default:
                    echo "Using default tasks for unknown brigade type.\n";
                    $tasks = [
                        [
                            'title' => '1. Define brigade objectives and strategy',
                            'description' => 'Establish clear goals and approach for the brigade.',
                            'priority' => 'high',
                            'estimated_time' => 120,
                            'status' => 'todo'
                        ],
                        [
                            'title' => '2. Set up brigade infrastructure',
                            'description' => 'Implement necessary tools and systems.',
                            'priority' => 'high',
                            'estimated_time' => 180,
                            'status' => 'todo'
                        ]
                    ];
            }

            echo "Defined " . count($tasks) . " tasks to create.\n";

            // Create the tasks
            $taskIds = [];
            foreach ($tasks as $taskData) {
                $taskData['user_id'] = $userId;
                $taskData['project_id'] = $projectId;
                $taskData['created_at'] = date('Y-m-d H:i:s');
                $taskData['updated_at'] = date('Y-m-d H:i:s');

                echo "Creating task: {$taskData['title']}\n";

                try {
                    $taskId = $this->taskModel->create($taskData);
                    if ($taskId) {
                        $taskIds[] = $taskId;
                        echo "Created task with ID: {$taskId}\n";

                        // Create agent task for Aegis Director to monitor this task
                        $agentTaskData = [
                            'agent_id' => $aegisDirector['id'],
                            'user_id' => $userId,
                            'title' => "Monitor: " . $taskData['title'],
                            'description' => "Ensure completion of task: " . $taskData['description'],
                            'priority' => $taskData['priority'],
                            'status' => 'pending',
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ];

                        $agentTaskId = $this->agentTaskModel->createTask($agentTaskData);
                        if ($agentTaskId) {
                            echo "Created agent task with ID: {$agentTaskId}\n";
                        } else {
                            echo "Failed to create agent task.\n";
                        }
                    } else {
                        echo "Failed to create task.\n";
                    }
                } catch (Exception $e) {
                    echo "Exception while creating task: " . $e->getMessage() . "\n";
                }
            }

            echo "Created " . count($taskIds) . " tasks.\n";

            // Create dependencies between tasks (sequential)
            if (count($taskIds) > 1) {
                for ($i = 0; $i < count($taskIds) - 1; $i++) {
                    try {
                        $dependencyResult = $this->taskModel->addDependency($taskIds[$i + 1], $taskIds[$i]);
                        if ($dependencyResult) {
                            echo "Created dependency: Task {$taskIds[$i + 1]} depends on Task {$taskIds[$i]}\n";
                        } else {
                            echo "Failed to create dependency.\n";
                        }
                    } catch (Exception $e) {
                        echo "Exception while creating dependency: " . $e->getMessage() . "\n";
                    }
                }
            }

            // Create a system interaction to record the task creation
            try {
                $interactionData = [
                    'agent_id' => $aegisDirector['id'],
                    'user_id' => $userId,
                    'interaction_type' => 'system',
                    'content' => "Created tasks for " . ucfirst(str_replace('_', ' ', $brigadeType)) . " Brigade project: {$project['name']}",
                    'response' => "I've created " . count($tasks) . " tasks for your " . ucfirst(str_replace('_', ' ', $brigadeType)) . " Brigade. Each task has been assigned a priority. I'll monitor your progress and help keep you on track to complete this project by the deadline.",
                    'success' => true,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $interactionId = $this->interactionModel->createInteraction($interactionData);
                if ($interactionId) {
                    echo "Created system interaction with ID: {$interactionId}\n";
                } else {
                    echo "Failed to create system interaction.\n";
                }
            } catch (Exception $e) {
                echo "Exception while creating interaction: " . $e->getMessage() . "\n";
            }

            // Get the tasks that were created
            $tasks = $this->taskModel->getProjectTasks($projectId);
            echo "Final count: " . count($tasks) . " tasks for the project:\n";

            foreach ($tasks as $index => $task) {
                echo ($index + 1) . ". {$task['title']} (Priority: {$task['priority']})\n";
            }

            return count($taskIds) > 0;
        }
    }

    // Create the project directly
    $projectData = [
        'user_id' => $userId,
        'name' => $projectName,
        'description' => $projectDescription,
        'start_date' => date('Y-m-d'),
        'end_date' => $deadline,
        'status' => 'planning',
        'progress' => 0,
        'brigade_type' => $brigadeType,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];

    echo "Project data to be created:\n";
    print_r($projectData);

    try {
        // Enable error reporting for PDO
        $db->getConnection()->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Insert the project
        $projectId = $db->insert('projects', $projectData);

        if ($projectId) {
            echo "Project created successfully with ID: {$projectId}\n";

            // Get the Aegis Director agent
            $agentModel = new AIAgent();
            $agents = $agentModel->getUserAgents($userId);
            $aegisDirectorId = null;

            foreach ($agents as $agent) {
                if ($agent['name'] === 'Aegis Director') {
                    $aegisDirectorId = $agent['id'];
                    break;
                }
            }

            if ($aegisDirectorId) {
                echo "Found Aegis Director agent with ID: {$aegisDirectorId}\n";

                // Assign Aegis Director to the project
                $assignmentModel = new ProjectAgentAssignment();
                $assignmentResult = $assignmentModel->assignAgentToProject($projectId, $aegisDirectorId, 'Brigade Commander');

                if ($assignmentResult) {
                    echo "Assigned Aegis Director to the project successfully\n";

                    // Create brigade tasks
                    $projectManager = new DebugAegisDirectorProjectManager();

                    try {
                        $tasksCreated = $projectManager->createBrigadeTasks($projectId, $brigadeType, $userId);
                    } catch (Exception $e) {
                        echo "Exception caught while creating brigade tasks: " . $e->getMessage() . "\n";
                        echo "Stack trace: " . $e->getTraceAsString() . "\n";
                        $tasksCreated = false;
                    }

                    if ($tasksCreated) {
                        echo "Successfully created brigade tasks\n";
                    } else {
                        echo "Failed to create brigade tasks\n";
                    }
                } else {
                    echo "Failed to assign Aegis Director to the project\n";
                }
            } else {
                echo "Aegis Director agent not found\n";
            }
        } else {
            echo "Failed to create project. Error info:\n";
            print_r($db->getConnection()->errorInfo());
        }
    } catch (Exception $e) {
        echo "Exception caught: " . $e->getMessage() . "\n";
        echo "Stack trace: " . $e->getTraceAsString() . "\n";
    }
} else {
    echo "Database connection failed\n";
}
