<?php
// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database utility
require_once 'src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

// Read SQL file
$sql = file_get_contents('sql/ai_agent_project_integration.sql');

// Execute SQL
try {
    // Split SQL into individual statements
    $statements = explode(';', $sql);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            echo "Executing: " . substr($statement, 0, 50) . "...\n";
            $result = $db->query($statement);
            if ($result !== false) {
                echo "Success!\n";
            } else {
                echo "Failed: " . print_r($db->getConnection()->errorInfo(), true) . "\n";
            }
        }
    }
    
    echo "SQL script execution completed.\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
