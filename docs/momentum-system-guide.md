# Momentum System Documentation & Development Guide

## Table of Contents
1. [System Overview](#system-overview)
2. [Core Architecture](#core-architecture)
3. [Feature Categories](#feature-categories)
4. [Implementation Status](#implementation-status)
5. [Development Roadmap](#development-roadmap)
6. [UI/UX Guidelines](#uiux-guidelines)
7. [Database Schema](#database-schema)
8. [API Documentation](#api-documentation)
9. [Testing Guidelines](#testing-guidelines)
10. [Deployment Guidelines](#deployment-guidelines)

---

## System Overview

Momentum is an ADHD-friendly personal management system designed to help users organize their lives, increase productivity, and manage ADHD symptoms effectively. The system is built with PHP, MySQL/MariaDB, HTML5, CSS3, JavaScript (ES6+), and Tailwind CSS, making it compatible with shared hosting environments like Hostinger and local development setups like Laragon.

### Core Principles

1. **ADHD-Friendly Design**: Simple, clean, clutter-free, and well-organized interfaces
2. **Accessibility**: Easy navigation with clear visual cues and minimal cognitive load
3. **Flexibility**: Adaptable to different user needs and workflows
4. **Integration**: Seamless connection between different system components
5. **Privacy**: User data remains under their control

### Target Environment

- **Server**: PHP 7.4+ with MySQL/MariaDB
- **Client**: Modern browsers (Chrome, Firefox, Safari, Edge)
- **Deployment**: Shared hosting (Hostinger) or local server (Laragon)

---

## Core Architecture

The Momentum system follows a Model-View-Controller (MVC) architecture:

### Directory Structure

```
momentum/
├── docs/                  # Documentation
├── public/                # Publicly accessible files
│   ├── css/               # CSS files
│   ├── js/                # JavaScript files
│   ├── images/            # Image assets
│   └── index.php          # Entry point
├── src/                   # Source code
│   ├── controllers/       # Controller classes
│   ├── models/            # Model classes
│   ├── views/             # View templates
│   ├── utils/             # Utility classes
│   └── config/            # Configuration files
├── tests/                 # Test files
└── vendor/                # Dependencies
```

### Key Components

1. **Router**: Handles URL routing to appropriate controllers
2. **Controllers**: Process user requests and return responses
3. **Models**: Handle data access and business logic
4. **Views**: Present data to the user
5. **Database**: Stores user data and system settings
6. **Authentication**: Manages user sessions and permissions
7. **Utilities**: Common functions used throughout the system

---

## Feature Categories

The Momentum system is organized into the following feature categories:

### 1. Core Features

- **Dashboard**: Central hub for accessing all system features
- **Task Management**: Create, organize, and track tasks
- **Project Management**: Manage complex projects with tasks, dependencies, and timelines
- **Notes**: Capture and organize thoughts and information
- **Ideas**: Collect and develop creative ideas

### 2. ADHD Management

- **ADHD Dashboard**: Overview of ADHD management tools
- **Symptom Tracker**: Track and analyze ADHD symptoms
- **Thought Records**: CBT-based thought recording and analysis
- **Productivity Strategies**: ADHD-friendly productivity techniques
- **Mindfulness Exercises**: Guided mindfulness practices
- **Consistency Trackers**: Track daily habits and routines
- **Medication Tracker**: Monitor medication usage and effectiveness
- **Trigger Identification**: Identify and manage ADHD triggers
- **Executive Function Exercises**: Activities to strengthen executive function

### 3. Productivity Tools

- **Focus Timer**: Pomodoro and other focus techniques
- **Focus Mode**: Distraction-free work environment
- **Time Blocking**: Visual scheduling system
- **Energy Level Tracking**: Monitor energy levels for optimal task scheduling
- **Task Batching**: Group similar tasks for better focus
- **Distraction Journal**: Log and analyze focus breakers

### 4. Financial Management

- **Expense Tracking**: Monitor spending by category
- **Income Management**: Track income from multiple sources
- **Budgeting**: Create and manage budgets
- **Subscription Management**: Track recurring payments
- **Debt Tracking**: Monitor debts (payable and receivable)
- **Financial Goals**: Set and track financial objectives
- **Financial Reporting**: Analyze financial data
- **Impulse Purchase Tracker**: Monitor and reduce impulse spending

### 5. Mind Mapping & Knowledge Management

- **Mind Map Creator**: Visual organization of thoughts and projects
- **Knowledge Base**: Personal wiki for organizing information
- **Resource Directory**: Organized collection of links and resources
- **Learning Paths**: Structured approach to skill development
- **Concept Connections**: Visualize relationships between ideas

### 6. Contact & Relationship Management

- **Contact Database**: Store contact information
- **Relationship Categories**: Organize contacts by relationship type
- **Interaction History**: Track communications with contacts
- **Communication Reminders**: Maintain regular contact
- **Social Battery Tracker**: Manage social energy

### 7. Workspace & Development Environment

- **Virtual Workspace**: Customizable work environments
- **Test Lab**: Sandbox for testing ideas
- **Code Testing**: Environment for technical experiments
- **Process Workflows**: Map and optimize procedures

### 8. Health & Wellness

- **Mood Tracker**: Monitor emotional states
- **Sleep Quality Tracker**: Track sleep patterns
- **Exercise Log**: Record physical activity
- **Nutrition Tracker**: Monitor diet and nutrition
- **Symptom Journal**: Track health concerns

### 9. Learning & Skill Development

- **Skill Tracking**: Monitor progress in different areas
- **Learning Resource Library**: Organize educational materials
- **Spaced Repetition System**: Optimize learning and retention
- **Note-Taking Templates**: Structured formats for different learning styles

### 10. Help & Support

- **User Guide**: Comprehensive system documentation
- **Tutorials**: Step-by-step guides for specific features
- **FAQ**: Answers to common questions
- **Support System**: Get help with system issues

---

## Implementation Status

The following table shows the current implementation status of each feature:

| Feature | Status | Priority | Complexity | Notes |
|---------|--------|----------|------------|-------|
| **Core Features** |
| Dashboard | Implemented | High | Medium | Central navigation hub |
| Task Management | Implemented | High | High | Basic task CRUD operations |
| Project Management | Partially Implemented | High | High | Missing advanced features |
| Notes | Implemented | Medium | Low | Basic note-taking functionality |
| Ideas | Implemented | Medium | Low | Simple idea collection |
| **ADHD Management** |
| ADHD Dashboard | Implemented | High | Medium | Overview of ADHD tools |
| Symptom Tracker | Implemented | High | Medium | Basic symptom logging |
| Thought Records | Implemented | Medium | High | CBT thought recording |
| Productivity Strategies | Implemented | Medium | Medium | ADHD-friendly techniques |
| Mindfulness Exercises | Implemented | Medium | Medium | Guided mindfulness practices |
| Consistency Trackers | Implemented | High | Medium | Habit and routine tracking |
| Medication Tracker | Planned | High | Medium | Medication usage and effectiveness |
| Trigger Identification | Planned | High | Medium | Identify and manage triggers |
| Executive Function Exercises | Concept | Medium | High | Strengthen executive function |
| **Productivity Tools** |
| Focus Timer | Implemented | High | Low | Basic Pomodoro functionality |
| Focus Mode | Implemented | Medium | Medium | Distraction-free environment |
| Time Blocking | Planned | High | High | For future development |
| Energy Level Tracking | Planned | Medium | Medium | For future development |
| Task Batching | Concept | Low | Medium | Early planning stage |
| Distraction Journal | Concept | Low | Medium | Early planning stage |
| **Financial Management** |
| Expense Tracking | Implemented | High | Medium | Basic expense logging |
| Income Management | Implemented | High | Medium | Multiple income sources |
| Budgeting | Implemented | High | High | Basic budget creation |
| Subscription Management | Implemented | Medium | Medium | Recurring payment tracking |
| Debt Tracking | Implemented | Medium | Medium | Payable and receivable |
| Financial Goals | In Progress | Medium | Medium | Currently developing |
| Financial Reporting | Partially Implemented | Medium | High | Basic reports available |
| Impulse Purchase Tracker | Planned | Low | Medium | For future development |
| **Mind Mapping & Knowledge** |
| Mind Map Creator | Planned | High | High | Next major feature |
| Knowledge Base | Concept | Medium | High | Early planning stage |
| Resource Directory | Planned | High | Medium | For near-term development |
| Learning Paths | Concept | Low | High | Early planning stage |
| Concept Connections | Concept | Low | High | Early planning stage |
| **Contact Management** |
| Contact Database | Planned | High | Medium | For near-term development |
| Relationship Categories | Planned | Medium | Low | For future development |
| Interaction History | Planned | Medium | Medium | For future development |
| Communication Reminders | Planned | Low | Medium | For future development |
| Social Battery Tracker | Concept | Low | Medium | Early planning stage |
| **Workspace & Development** |
| Virtual Workspace | Planned | Medium | High | For future development |
| Test Lab | Planned | Medium | High | For future development |
| Code Testing | Concept | Low | High | Early planning stage |
| Process Workflows | Concept | Low | High | Early planning stage |
| **Health & Wellness** |
| Mood Tracker | Planned | Medium | Medium | For future development |
| Sleep Quality Tracker | Planned | Medium | Medium | For future development |
| Exercise Log | Planned | Low | Low | For future development |
| Nutrition Tracker | Concept | Low | Medium | Early planning stage |
| Symptom Journal | Concept | Low | Medium | Early planning stage |
| **Learning & Development** |
| Skill Tracking | Planned | Medium | Medium | For future development |
| Learning Resource Library | Planned | Medium | Medium | For future development |
| Spaced Repetition System | Concept | Low | High | Early planning stage |
| Note-Taking Templates | Concept | Low | Medium | Early planning stage |
| **Help & Support** |
| User Guide | In Progress | High | Medium | Currently developing |
| Tutorials | Planned | Medium | Medium | For future development |
| FAQ | Planned | Medium | Low | For future development |
| Support System | Concept | Low | Medium | Early planning stage |

---

## Development Roadmap

The development roadmap outlines the planned implementation sequence for upcoming features:

### Phase 1: Core Enhancements (Current)
- Complete Financial Goals implementation
- Enhance Project Management with task dependencies
- Improve ADHD symptom tracking analytics
- Develop comprehensive User Guide

### Phase 2: New Feature Development (Next)
- Mind Map Creator implementation
- Contact Management system
- Resource Directory for links and tools
- Workspace/Test Lab environment

### Phase 3: Advanced Features
- Time Blocking calendar
- Knowledge Base/Personal Wiki
- Health & Wellness tracking
- Learning & Skill Development center

### Phase 4: Integration & Refinement
- Cross-feature integration
- Advanced analytics and insights
- Mobile optimization
- Performance improvements

---

## UI/UX Guidelines

The Momentum system follows these UI/UX principles:

### Design Principles

1. **Simplicity**: Clean, uncluttered interfaces with clear visual hierarchy
2. **Consistency**: Uniform design patterns across all features
3. **Accessibility**: Easy-to-use interfaces with minimal cognitive load
4. **Responsiveness**: Adapts to different screen sizes and devices
5. **Feedback**: Clear indication of system status and user actions

### Color Scheme

- **Primary**: Blue (#0ea5e9) - Main actions and navigation
- **Secondary**: Purple (#8b5cf6) - Supporting elements
- **Success**: Green (#10b981) - Positive feedback and completion
- **Warning**: Orange (#f59e0b) - Alerts and cautions
- **Danger**: Red (#ef4444) - Errors and critical actions
- **Neutral**: Gray scale for text and backgrounds

### Typography

- **Headings**: Sans-serif, bold, clear hierarchy (h1-h6)
- **Body Text**: Sans-serif, readable size (16px minimum)
- **Emphasis**: Use weight and color rather than italics
- **Line Length**: Maximum 80 characters per line
- **Line Height**: 1.5 for optimal readability

### Components

- **Cards**: Contained units of information with consistent padding
- **Buttons**: Clear, actionable, with appropriate colors for function
- **Forms**: Simple, focused, with inline validation
- **Navigation**: Intuitive, consistent, with clear indicators
- **Modals**: Minimal use, focused on single tasks
- **Tables**: Clean, scannable, with appropriate spacing
- **Icons**: Meaningful, consistent style, with text labels

---

## Database Schema

The Momentum system uses a relational database with the following core tables:

### Core Tables

1. **users**: User accounts and authentication
2. **tasks**: Task management
3. **projects**: Project management
4. **notes**: Note-taking
5. **ideas**: Idea collection
6. **adhd_symptoms**: ADHD symptom tracking
7. **adhd_thoughts**: CBT thought records
8. **consistency_trackers**: Habit tracking
9. **finances_expenses**: Expense tracking
10. **finances_income**: Income tracking
11. **finances_budgets**: Budget management
12. **finances_subscriptions**: Subscription tracking
13. **finances_debts**: Debt tracking
14. **finances_goals**: Financial goals

### Relationships

- Users have many tasks, projects, notes, etc.
- Projects have many tasks
- Tasks can belong to projects
- Financial records belong to users
- ADHD records belong to users

---

## API Documentation

The Momentum system provides internal APIs for feature integration:

### Authentication

- `POST /api/auth/login`: User login
- `POST /api/auth/logout`: User logout
- `GET /api/auth/user`: Get current user

### Tasks

- `GET /api/tasks`: List tasks
- `POST /api/tasks`: Create task
- `GET /api/tasks/{id}`: Get task
- `PUT /api/tasks/{id}`: Update task
- `DELETE /api/tasks/{id}`: Delete task

### Projects

- `GET /api/projects`: List projects
- `POST /api/projects`: Create project
- `GET /api/projects/{id}`: Get project
- `PUT /api/projects/{id}`: Update project
- `DELETE /api/projects/{id}`: Delete project
- `GET /api/projects/{id}/tasks`: Get project tasks

### ADHD Management

- `GET /api/adhd/symptoms`: List symptoms
- `POST /api/adhd/symptoms`: Log symptom
- `GET /api/adhd/thoughts`: List thought records
- `POST /api/adhd/thoughts`: Create thought record

### Finances

- `GET /api/finances/expenses`: List expenses
- `POST /api/finances/expenses`: Record expense
- `GET /api/finances/income`: List income
- `POST /api/finances/income`: Record income
- `GET /api/finances/budgets`: List budgets
- `POST /api/finances/budgets`: Create budget

---

## Testing Guidelines

The Momentum system uses the following testing approaches:

### Testing Levels

1. **Unit Testing**: Test individual components in isolation
2. **Integration Testing**: Test component interactions
3. **System Testing**: Test the entire system
4. **Acceptance Testing**: Validate against user requirements

### Testing Tools

- **PHPUnit**: PHP unit testing framework
- **Jest**: JavaScript testing framework
- **Cypress**: End-to-end testing
- **Lighthouse**: Performance and accessibility testing

### Testing Process

1. Write tests before or alongside code (TDD where possible)
2. Run tests locally before committing
3. Automated testing in CI/CD pipeline
4. Manual testing for UI/UX and complex interactions

---

## Deployment Guidelines

The Momentum system can be deployed to various environments:

### Local Development (Laragon)

1. Clone repository to Laragon www directory
2. Set up local database
3. Configure environment variables
4. Run database migrations
5. Access via localhost

### Shared Hosting (Hostinger)

1. Upload files via FTP or Git
2. Set up database
3. Configure environment variables
4. Run database migrations
5. Set appropriate file permissions

### Maintenance

1. Regular backups of code and database
2. Monitor error logs
3. Apply security updates promptly
4. Performance optimization as needed
5. Regular feature updates based on roadmap
