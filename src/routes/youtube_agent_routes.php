<?php
/**
 * YouTube Agent Routes
 *
 * This file contains all routes related to the YouTube Agent.
 */

// Dashboard
$router->get('/youtube-agent', function() {
    $controller = new YouTubeAgentController();
    $controller->index();
});

// Search
$router->get('/youtube-agent/search', function() {
    $controller = new YouTubeAgentController();
    $controller->search();
});

$router->post('/youtube-agent/execute-search', function() {
    $controller = new YouTubeAgentController();
    $controller->executeSearch();
});

$router->get('/youtube-agent/search-results/:id', function($id) {
    $controller = new YouTubeAgentController();
    $controller->searchResults($id);
});

$router->get('/youtube-agent/searches', function() {
    $controller = new YouTubeAgentController();
    $controller->searches();
});

$router->get('/youtube-agent/delete-search/:id', function($id) {
    $controller = new YouTubeAgentController();
    $controller->deleteSearch($id);
});

// Videos
$router->get('/youtube-agent/view-video/:id', function($id) {
    $controller = new YouTubeAgentController();
    $controller->viewVideo($id);
});

$router->post('/youtube-agent/save-video/:id', function($id) {
    $controller = new YouTubeAgentController();
    $controller->saveVideo($id);
});

$router->get('/youtube-agent/saved-videos', function() {
    $controller = new YouTubeAgentController();
    $controller->savedVideos();
});

$router->get('/youtube-agent/delete-saved-video/:id', function($id) {
    $controller = new YouTubeAgentController();
    $controller->deleteSavedVideo($id);
});

// Analysis
$router->post('/youtube-agent/create-analysis/:id', function($id) {
    $controller = new YouTubeAgentController();
    $controller->createAnalysis($id);
});

$router->get('/youtube-agent/view-analysis/:id', function($id) {
    $controller = new YouTubeAgentController();
    $controller->viewAnalysis($id);
});

$router->get('/youtube-agent/analyses', function() {
    $controller = new YouTubeAgentController();
    $controller->analyses();
});

$router->get('/youtube-agent/delete-analysis/:id', function($id) {
    $controller = new YouTubeAgentController();
    $controller->deleteAnalysis($id);
});

// Strategies
$router->get('/youtube-agent/money-making-strategies', function() {
    $controller = new YouTubeAgentController();
    $controller->moneyMakingStrategies();
});

// Settings
$router->get('/youtube-agent/settings', function() {
    $controller = new YouTubeAgentController();
    $controller->settings();
});

$router->post('/youtube-agent/save-settings', function() {
    $controller = new YouTubeAgentController();
    $controller->saveSettings();
});

$router->get('/youtube-agent/reset-quota', function() {
    $controller = new YouTubeAgentController();
    $controller->resetQuota();
});
