<?php
/**
 * Quick Capture System Routes
 *
 * This file contains all routes related to the quick capture system (screenshots, notes, voice).
 */

// Quick Capture Dashboard
$router->get('/quick-capture', function() {
    $controller = new QuickCaptureController();
    $controller->index();
});

// Capture Gallery
$router->get('/quick-capture/gallery', function() {
    $controller = new QuickCaptureController();
    $controller->gallery();
});

// Screenshot Capture
$router->get('/quick-capture/screenshot', function() {
    $controller = new QuickCaptureController();
    $controller->screenshot();
});

$router->post('/quick-capture/screenshot/upload', function() {
    $controller = new QuickCaptureController();
    $controller->uploadScreenshot();
});

// Note Capture
$router->get('/quick-capture/note', function() {
    $controller = new QuickCaptureController();
    $controller->noteForm();
});

$router->post('/quick-capture/note/create', function() {
    $controller = new QuickCaptureController();
    $controller->createNote();
});

// Voice Capture
$router->get('/quick-capture/voice', function() {
    $controller = new QuickCaptureController();
    $controller->voiceForm();
});

$router->post('/quick-capture/voice/upload', function() {
    $controller = new QuickCaptureController();
    $controller->uploadVoice();
});

// View Capture
$router->get('/quick-capture/view/:id', function($id) {
    $controller = new QuickCaptureController();
    $controller->viewCapture($id);
});

// Edit Capture
$router->get('/quick-capture/edit/:id', function($id) {
    $controller = new QuickCaptureController();
    $controller->edit($id);
});

$router->post('/quick-capture/update/:id', function($id) {
    $controller = new QuickCaptureController();
    $controller->update($id);
});

// Delete Capture
$router->post('/quick-capture/delete/:id', function($id) {
    $controller = new QuickCaptureController();
    $controller->delete($id);
});

// Toggle Pin
$router->post('/quick-capture/toggle-pin/:id', function($id) {
    $controller = new QuickCaptureController();
    $controller->togglePin($id);
});

// Search Captures
$router->get('/quick-capture/search', function() {
    $controller = new QuickCaptureController();
    $controller->search();
});

// Bulk Operations
$router->post('/quick-capture/bulk/delete', function() {
    $controller = new QuickCaptureController();
    $controller->bulkDelete();
});

$router->post('/quick-capture/bulk/categorize', function() {
    $controller = new QuickCaptureController();
    $controller->bulkCategorize();
});

$router->post('/quick-capture/bulk/pin', function() {
    $controller = new QuickCaptureController();
    $controller->bulkPin();
});

$router->post('/quick-capture/bulk/export', function() {
    $controller = new QuickCaptureController();
    $controller->bulkExport();
});

// Annotations
$router->get('/quick-capture/:id/annotations', function($id) {
    $controller = new QuickCaptureController();
    $controller->viewAnnotations($id);
});

$router->post('/quick-capture/:id/annotations/add', function($id) {
    $controller = new QuickCaptureController();
    $controller->addAnnotation($id);
});

$router->post('/quick-capture/annotations/update/:annotationId', function($annotationId) {
    $controller = new QuickCaptureController();
    $controller->updateAnnotation($annotationId);
});

$router->post('/quick-capture/annotations/delete/:annotationId', function($annotationId) {
    $controller = new QuickCaptureController();
    $controller->deleteAnnotation($annotationId);
});

// OCR Processing
$router->post('/quick-capture/:id/ocr', function($id) {
    $controller = new QuickCaptureController();
    $controller->processOCR($id);
});

$router->get('/quick-capture/:id/ocr/results', function($id) {
    $controller = new QuickCaptureController();
    $controller->getOCRResults($id);
});

// File Operations
$router->get('/quick-capture/download/:id', function($id) {
    $controller = new QuickCaptureController();
    $controller->downloadFile($id);
});

$router->get('/quick-capture/thumbnail/:id', function($id) {
    $controller = new QuickCaptureController();
    $controller->getThumbnail($id);
});

// Categories
$router->get('/quick-capture/categories', function() {
    $controller = new QuickCaptureController();
    $controller->categories();
});

$router->post('/quick-capture/categories/create', function() {
    $controller = new QuickCaptureController();
    $controller->createCategory();
});

// Linking
$router->post('/quick-capture/:id/link/prompt/:promptId', function($id, $promptId) {
    $controller = new QuickCaptureController();
    $controller->linkToPrompt($id, $promptId);
});

$router->post('/quick-capture/:id/link/task/:taskId', function($id, $taskId) {
    $controller = new QuickCaptureController();
    $controller->linkToTask($id, $taskId);
});

$router->post('/quick-capture/:id/link/project/:projectId', function($id, $projectId) {
    $controller = new QuickCaptureController();
    $controller->linkToProject($id, $projectId);
});

$router->post('/quick-capture/:id/unlink', function($id) {
    $controller = new QuickCaptureController();
    $controller->removeLinks($id);
});

// Analytics and Reports
$router->get('/quick-capture/analytics', function() {
    $controller = new QuickCaptureController();
    $controller->analytics();
});

$router->get('/quick-capture/reports/usage', function() {
    $controller = new QuickCaptureController();
    $controller->usageReport();
});

$router->get('/quick-capture/reports/storage', function() {
    $controller = new QuickCaptureController();
    $controller->storageReport();
});

// Settings
$router->get('/quick-capture/settings', function() {
    $controller = new QuickCaptureController();
    $controller->settings();
});

$router->post('/quick-capture/settings/update', function() {
    $controller = new QuickCaptureController();
    $controller->updateSettings();
});

// API Endpoints for AJAX requests
$router->get('/api/quick-capture/recent', function() {
    $controller = new QuickCaptureController();
    $controller->getRecentCaptures();
});

$router->get('/api/quick-capture/stats', function() {
    $controller = new QuickCaptureController();
    $controller->getStats();
});

$router->post('/api/quick-capture/quick-note', function() {
    $controller = new QuickCaptureController();
    $controller->createQuickNote();
});

$router->get('/api/quick-capture/search-suggestions', function() {
    $controller = new QuickCaptureController();
    $controller->getSearchSuggestions();
});

// Widget API endpoints
$router->get('/api/quick-capture/widget-data', function() {
    $controller = new QuickCaptureController();
    $controller->getWidgetData();
});

// Export/Import
$router->get('/quick-capture/export', function() {
    $controller = new QuickCaptureController();
    $controller->export();
});

$router->post('/quick-capture/import', function() {
    $controller = new QuickCaptureController();
    $controller->import();
});

// Sharing
$router->get('/quick-capture/share/:id', function($id) {
    $controller = new QuickCaptureController();
    $controller->shareCapture($id);
});

$router->post('/quick-capture/share/:id/generate-link', function($id) {
    $controller = new QuickCaptureController();
    $controller->generateShareLink($id);
});

// Integration with other systems
$router->post('/quick-capture/:id/convert-to-task', function($id) {
    $controller = new QuickCaptureController();
    $controller->convertToTask($id);
});

$router->post('/quick-capture/:id/add-to-project/:projectId', function($id, $projectId) {
    $controller = new QuickCaptureController();
    $controller->addToProject($id, $projectId);
});

$router->post('/quick-capture/:id/create-prompt', function($id) {
    $controller = new QuickCaptureController();
    $controller->createPromptFromCapture($id);
});
