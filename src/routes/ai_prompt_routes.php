<?php
/**
 * AI Prompt System Routes
 *
 * This file contains all routes related to the AI prompt management system.
 */

// AI Prompts Dashboard
$router->get('/ai-prompts', function() {
    $controller = new AIPromptController();
    $controller->index();
});

// Prompt Library
$router->get('/ai-prompts/library', function() {
    $controller = new AIPromptController();
    $controller->library();
});

// Create Prompt
$router->get('/ai-prompts/create', function() {
    $controller = new AIPromptController();
    $controller->create();
});

$router->post('/ai-prompts/store', function() {
    $controller = new AIPromptController();
    $controller->store();
});

// View Prompt
$router->get('/ai-prompts/view/:id', function($id) {
    $controller = new AIPromptController();
    $controller->viewPrompt($id);
});

// Edit Prompt
$router->get('/ai-prompts/edit/:id', function($id) {
    $controller = new AIPromptController();
    $controller->edit($id);
});

$router->post('/ai-prompts/update/:id', function($id) {
    $controller = new AIPromptController();
    $controller->update($id);
});

// Delete Prompt
$router->post('/ai-prompts/delete/:id', function($id) {
    $controller = new AIPromptController();
    $controller->delete($id);
});

// Toggle Favorite
$router->post('/ai-prompts/toggle-favorite/:id', function($id) {
    $controller = new AIPromptController();
    $controller->toggleFavorite($id);
});

// Fork/Duplicate Prompt
$router->post('/ai-prompts/fork/:id', function($id) {
    $controller = new AIPromptController();
    $controller->fork($id);
});

// Execute Prompt
$router->get('/ai-prompts/execute/:id', function($id) {
    $controller = new AIPromptController();
    $controller->execute($id);
});

$router->post('/ai-prompts/execute/:id', function($id) {
    $controller = new AIPromptController();
    $controller->processExecution($id);
});

// Prompt History
$router->get('/ai-prompts/history', function() {
    $controller = new AIPromptController();
    $controller->history();
});

$router->get('/ai-prompts/history/:id', function($id) {
    $controller = new AIPromptController();
    $controller->viewHistory($id);
});

// Search Prompts
$router->get('/ai-prompts/search', function() {
    $controller = new AIPromptController();
    $controller->search();
});

// Export/Import Prompts
$router->get('/ai-prompts/export', function() {
    $controller = new AIPromptController();
    $controller->export();
});

$router->post('/ai-prompts/import', function() {
    $controller = new AIPromptController();
    $controller->import();
});

// Prompt Categories
$router->get('/ai-prompts/categories', function() {
    $controller = new AIPromptController();
    $controller->categories();
});

$router->get('/ai-prompts/categories/create', function() {
    $controller = new AIPromptController();
    $controller->createCategory();
});

$router->post('/ai-prompts/categories/store', function() {
    $controller = new AIPromptController();
    $controller->storeCategory();
});

$router->get('/ai-prompts/categories/edit/:id', function($id) {
    $controller = new AIPromptController();
    $controller->editCategory($id);
});

$router->post('/ai-prompts/categories/update/:id', function($id) {
    $controller = new AIPromptController();
    $controller->updateCategory($id);
});

$router->post('/ai-prompts/categories/delete/:id', function($id) {
    $controller = new AIPromptController();
    $controller->deleteCategory($id);
});

$router->post('/ai-prompts/categories/reorder', function() {
    $controller = new AIPromptController();
    $controller->reorderCategories();
});

// Prompt Templates
$router->get('/ai-prompts/templates', function() {
    $controller = new AIPromptController();
    $controller->templates();
});

$router->get('/ai-prompts/templates/:category', function($category) {
    $controller = new AIPromptController();
    $controller->templatesByCategory($category);
});

// Prompt Workflows
$router->get('/ai-prompts/workflows', function() {
    $controller = new AIPromptController();
    $controller->workflows();
});

$router->get('/ai-prompts/workflows/create', function() {
    $controller = new AIPromptController();
    $controller->createWorkflow();
});

$router->post('/ai-prompts/workflows/store', function() {
    $controller = new AIPromptController();
    $controller->storeWorkflow();
});

$router->get('/ai-prompts/workflows/edit/:id', function($id) {
    $controller = new AIPromptController();
    $controller->editWorkflow($id);
});

$router->post('/ai-prompts/workflows/update/:id', function($id) {
    $controller = new AIPromptController();
    $controller->updateWorkflow($id);
});

$router->post('/ai-prompts/workflows/delete/:id', function($id) {
    $controller = new AIPromptController();
    $controller->deleteWorkflow($id);
});

$router->post('/ai-prompts/workflows/execute/:id', function($id) {
    $controller = new AIPromptController();
    $controller->executeWorkflow($id);
});

// AI API Configuration
$router->get('/ai-prompts/settings', function() {
    $controller = new AIPromptController();
    $controller->settings();
});

$router->post('/ai-prompts/settings/update', function() {
    $controller = new AIPromptController();
    $controller->updateSettings();
});

// Analytics and Reports
$router->get('/ai-prompts/analytics', function() {
    $controller = new AIPromptController();
    $controller->analytics();
});

$router->get('/ai-prompts/reports/usage', function() {
    $controller = new AIPromptController();
    $controller->usageReport();
});

$router->get('/ai-prompts/reports/effectiveness', function() {
    $controller = new AIPromptController();
    $controller->effectivenessReport();
});

// Bulk Operations
$router->post('/ai-prompts/bulk/delete', function() {
    $controller = new AIPromptController();
    $controller->bulkDelete();
});

$router->post('/ai-prompts/bulk/categorize', function() {
    $controller = new AIPromptController();
    $controller->bulkCategorize();
});

$router->post('/ai-prompts/bulk/export', function() {
    $controller = new AIPromptController();
    $controller->bulkExport();
});

// API Endpoints for AJAX requests
$router->get('/api/ai-prompts/suggestions', function() {
    $controller = new AIPromptController();
    $controller->getSuggestions();
});

$router->post('/api/ai-prompts/validate', function() {
    $controller = new AIPromptController();
    $controller->validatePrompt();
});

$router->get('/api/ai-prompts/variables/:id', function($id) {
    $controller = new AIPromptController();
    $controller->getPromptVariables($id);
});

$router->post('/api/ai-prompts/preview', function() {
    $controller = new AIPromptController();
    $controller->previewPrompt();
});
