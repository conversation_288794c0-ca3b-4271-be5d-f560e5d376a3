<?php
/**
 * Clone Routes
 *
 * This file contains all routes related to clone research tools.
 */

// Main Clone section
$router->get('/clone', function() {
    $controller = new CloneController();
    $controller->index();
});

// Pinterest Clone routes
$router->get('/clone/pinterest', function() {
    $controller = new CloneController();
    $controller->pinterestDashboard();
});

$router->get('/clone/pinterest/scraper', function() {
    $controller = new CloneController();
    $controller->pinterestScraper();
});

$router->post('/clone/pinterest/process-scrape', function() {
    $controller = new CloneController();
    $controller->processPinterestScrape();
});

$router->get('/clone/pinterest/view-scrape/:id', function($id) {
    $controller = new CloneController();
    $controller->viewPinterestScrape($id);
});

$router->get('/clone/pinterest/trends', function() {
    $controller = new CloneController();
    $controller->pinterestTrends();
});

$router->get('/clone/pinterest/analysis', function() {
    $controller = new CloneController();
    $controller->pinterestVisualAnalysis();
});

// Pinterest Boards routes
$router->get('/clone/pinterest/boards', function() {
    $controller = new CloneController();
    $controller->pinterestBoards();
});

$router->get('/clone/pinterest/create-board', function() {
    $controller = new CloneController();
    $controller->createBoardForm();
});

$router->post('/clone/pinterest/create-board', function() {
    $controller = new CloneController();
    $controller->processCreateBoard();
});

$router->get('/clone/pinterest/upload-pin', function() {
    $controller = new CloneController();
    $controller->uploadPinForm();
});

$router->post('/clone/pinterest/upload-pin', function() {
    $controller = new CloneController();
    $controller->processUploadPin();
});

$router->get('/clone/pinterest/export-scrape/:id', function($id) {
    $controller = new CloneController();
    $controller->exportPinterestScrape($id);
});

$router->get('/clone/pinterest/analyze-scrape/:id', function($id) {
    $controller = new CloneController();
    $controller->analyzePinterestScrape($id);
});

$router->get('/clone/pinterest/clear-scrapes', function() {
    $controller = new CloneController();
    $controller->clearPinterestScrapes();
});

$router->post('/clone/pinterest/cancel-scrape', function() {
    $controller = new CloneController();
    $controller->cancelPinterestScrape();
});
