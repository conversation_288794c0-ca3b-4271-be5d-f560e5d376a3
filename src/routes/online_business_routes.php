<?php
/**
 * Online Business Dashboard Routes
 *
 * This file contains routes for the online business dashboard functionality.
 */

// Include the necessary files
require_once '../src/utils/Database.php';
require_once '../src/controllers/BaseController.php';
require_once '../src/controllers/OnlineBusinessController.php';
require_once '../src/models/BusinessVenture.php';
require_once '../src/models/BusinessMetric.php';

// Check if session is already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Create a new instance of the OnlineBusinessController
$controller = new OnlineBusinessController();

// Get the requested path
$path = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '/';

// Route the request
switch ($path) {
    case '/':
    case '/index':
        $controller->index();
        break;
    case '/create':
        $controller->create();
        break;
    case '/store':
        $controller->store();
        break;
    case '/dashboard':
        // Redirect to index if no ID is provided
        header('Location: /momentum/online-business');
        exit;
        break;
    case (preg_match('/^\/dashboard\/(\d+)$/', $path, $matches) ? true : false):
        $controller->dashboard($matches[1]);
        break;
    case (preg_match('/^\/edit\/(\d+)$/', $path, $matches) ? true : false):
        $controller->edit($matches[1]);
        break;
    case (preg_match('/^\/update\/(\d+)$/', $path, $matches) ? true : false):
        $controller->update($matches[1]);
        break;
    case (preg_match('/^\/delete\/(\d+)$/', $path, $matches) ? true : false):
        $controller->delete($matches[1]);
        break;
    case (preg_match('/^\/metrics\/(\d+)$/', $path, $matches) ? true : false):
        $controller->metrics($matches[1]);
        break;
    case (preg_match('/^\/save-metrics\/(\d+)$/', $path, $matches) ? true : false):
        $controller->saveMetrics($matches[1]);
        break;
    default:
        // Redirect to the online business dashboard home page by default
        header('Location: /momentum/online-business');
        exit;
}
