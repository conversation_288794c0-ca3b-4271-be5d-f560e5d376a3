<?php
/**
 * Brigade Template Routes
 *
 * This file contains all routes related to the AI Agent Army brigade templates.
 */

// Brigade Templates
$router->get('/brigades/templates', function() {
    $controller = new BrigadeTemplateController();
    $controller->index();
});

// View Brigade Template
$router->get('/brigades/view/:brigadeType', function($brigadeType) {
    $controller = new BrigadeTemplateController();
    $controller->viewTemplate($brigadeType);
});

// Create Brigade Project Form
$router->get('/brigades/create-project', function() {
    $controller = new BrigadeTemplateController();
    $controller->createProject();
});

// Process Brigade Project Creation
$router->post('/brigades/create-project', function() {
    $controller = new BrigadeTemplateController();
    $controller->createProject();
});

// Assign Agents to Brigade Roles Form
$router->get('/brigades/assign-agents/:projectId', function($projectId) {
    $controller = new BrigadeTemplateController();
    $controller->assignAgents($projectId);
});

// Process Agent Assignments
$router->post('/brigades/process-assignments/:projectId', function($projectId) {
    $controller = new BrigadeTemplateController();
    $controller->processAssignments($projectId);
});
