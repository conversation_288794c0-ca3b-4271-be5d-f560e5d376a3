<?php
/**
 * Database Connection Class
 *
 * Handles database connections and provides methods for common database operations.
 */

class Database {
    private static $instance = null;
    private $connection;
    private $config;
    private $appConfig;
    private $queryCache = [];
    private $isProduction = false;
    private $cacheEnabled = false;
    private $cacheTTL = 3600; // Default cache TTL (1 hour)

    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct() {
        // Load database config
        $dbConfigPath = __DIR__ . '/../config/database.php';
        if (file_exists($dbConfigPath)) {
            $this->config = require_once $dbConfigPath;
        } else {
            // Default database config
            $this->config = [
                'host' => 'localhost',
                'username' => 'root',
                'password' => '',
                'database' => 'momentum',
                'charset' => 'utf8mb4',
                'collation' => 'utf8mb4_unicode_ci',
            ];
        }

        // Load app config
        $appConfigPath = __DIR__ . '/../config/app.php';
        if (file_exists($appConfigPath)) {
            $this->appConfig = require_once $appConfigPath;

            // Set environment variables
            $this->isProduction = isset($this->appConfig['environment']) && $this->appConfig['environment'] === 'production';
            $this->cacheEnabled = isset($this->appConfig['cache']['enabled']) ? $this->appConfig['cache']['enabled'] : false;
            $this->cacheTTL = isset($this->appConfig['cache']['ttl']) ? $this->appConfig['cache']['ttl'] : 3600;
        } else {
            // Default values if config file doesn't exist
            $this->isProduction = false;
            $this->cacheEnabled = false;
            $this->cacheTTL = 3600;
        }

        $this->connect();
    }

    /**
     * Get database instance (Singleton pattern)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Connect to the database
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->config['host']};dbname={$this->config['database']};charset={$this->config['charset']}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            $this->connection = new PDO($dsn, $this->config['username'], $this->config['password'], $options);
        } catch (PDOException $e) {
            // Log error and display user-friendly message
            error_log("Database Connection Error: " . $e->getMessage());
            die("Could not connect to the database. Please try again later.");
        }
    }

    /**
     * Get the database connection
     */
    public function getConnection() {
        return $this->connection;
    }

    /**
     * Execute a query with parameters
     *
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @param bool $useCache Whether to use query caching
     * @return PDOStatement|false PDO statement on success, false on failure
     */
    public function query($sql, $params = [], $useCache = false) {
        // Generate a cache key for this query if caching is enabled
        $cacheKey = '';
        if ($useCache && $this->cacheEnabled) {
            $cacheKey = $this->generateCacheKey($sql, $params);

            // Check if we have a cached result
            if (isset($this->queryCache[$cacheKey]) && time() < $this->queryCache[$cacheKey]['expires']) {
                return $this->queryCache[$cacheKey]['statement'];
            }
        }

        try {
            $stmt = $this->connection->prepare($sql);

            // Only log queries in development mode
            if (!$this->isProduction) {
                $paramStr = '';
                foreach ($params as $key => $value) {
                    $paramStr .= (is_string($key) ? $key : "#$key") . ": " . (is_null($value) ? 'NULL' :
                        (is_string($value) ? "'$value'" :
                        (is_array($value) ? json_encode($value) : $value))) . ", ";
                }
                error_log("Executing query: $sql with params: [$paramStr]");
            }

            $result = $stmt->execute($params);

            if (!$result) {
                $errorInfo = $stmt->errorInfo();
                error_log("Query execution failed: " . print_r($errorInfo, true));
                return false;
            }

            // Cache the result if caching is enabled
            if ($useCache && $this->cacheEnabled && $cacheKey) {
                $this->queryCache[$cacheKey] = [
                    'statement' => $stmt,
                    'expires' => time() + $this->cacheTTL
                ];
            }

            return $stmt;
        } catch (PDOException $e) {
            error_log("Query Error: " . $e->getMessage());
            if (!$this->isProduction) {
                error_log("SQL: $sql");
                error_log("Params: " . print_r($params, true));
            }
            return false;
        }
    }

    /**
     * Fetch a single row
     *
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @param bool $useCache Whether to use query caching
     * @return array|false Result row or false on failure
     */
    public function fetchOne($sql, $params = [], $useCache = true) {
        $stmt = $this->query($sql, $params, $useCache);
        return $stmt ? $stmt->fetch() : false;
    }

    /**
     * Fetch a single row (alias for fetchOne for backward compatibility)
     *
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @param bool $useCache Whether to use query caching
     * @return array|false Result row or false on failure
     */
    public function fetch($sql, $params = [], $useCache = true) {
        return $this->fetchOne($sql, $params, $useCache);
    }

    /**
     * Fetch all rows
     *
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @param bool $useCache Whether to use query caching
     * @return array|false Result rows or false on failure
     */
    public function fetchAll($sql, $params = [], $useCache = true) {
        $stmt = $this->query($sql, $params, $useCache);
        return $stmt ? $stmt->fetchAll() : false;
    }

    /**
     * Insert a record and return the last insert ID
     */
    public function insert($table, $data) {
        // Clean data: convert empty strings to NULL for date fields
        $cleanData = [];
        foreach ($data as $key => $value) {
            // If value is an empty string and the column might be a date field
            if ($value === '' && (
                strpos($key, 'date') !== false ||
                strpos($key, 'time') !== false ||
                $key === 'end_date' ||
                $key === 'start_date'
            )) {
                $cleanData[$key] = null;
            } else {
                $cleanData[$key] = $value;
            }
        }

        $columns = implode(', ', array_keys($cleanData));
        $placeholders = implode(', ', array_fill(0, count($cleanData), '?'));

        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";

        $stmt = $this->query($sql, array_values($cleanData));
        return $stmt ? $this->connection->lastInsertId() : false;
    }

    /**
     * Update a record
     */
    public function update($table, $data, $where, $whereParams = []) {
        $setClauses = [];
        $params = [];

        // Clean data: convert empty strings to NULL for date fields
        $cleanData = [];
        foreach ($data as $key => $value) {
            // If value is an empty string and the column might be a date field
            if ($value === '' && (
                strpos($key, 'date') !== false ||
                strpos($key, 'time') !== false ||
                $key === 'end_date' ||
                $key === 'start_date'
            )) {
                $cleanData[$key] = null;
            } else {
                $cleanData[$key] = $value;
            }
        }

        foreach ($cleanData as $column => $value) {
            $setClauses[] = "{$column} = ?";
            $params[] = $value;
        }

        $setClause = implode(', ', $setClauses);
        $params = array_merge($params, $whereParams);

        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";

        // Log the SQL query and parameters for debugging (only in development)
        if (!$this->isProduction) {
            error_log("SQL Query: " . $sql);
            error_log("Parameters: " . print_r($params, true));
        }

        try {
            $stmt = $this->query($sql, $params);
            $rowCount = $stmt ? $stmt->rowCount() : 0;
            if (!$this->isProduction) {
                error_log("Rows affected: " . $rowCount);
            }
            return $rowCount > 0;
        } catch (PDOException $e) {
            error_log("Database update error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a record
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->rowCount() : false;
    }

    /**
     * Begin a transaction
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    /**
     * Commit a transaction
     */
    public function commit() {
        return $this->connection->commit();
    }

    /**
     * Rollback a transaction
     */
    public function rollback() {
        return $this->connection->rollBack();
    }

    /**
     * Generate a cache key for a query
     *
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @return string Cache key
     */
    private function generateCacheKey($sql, $params) {
        return md5($sql . serialize($params));
    }

    /**
     * Clear the query cache
     *
     * @param string|null $cacheKey Specific cache key to clear, or null to clear all
     * @return void
     */
    public function clearCache($cacheKey = null) {
        if ($cacheKey === null) {
            $this->queryCache = [];
        } else if (isset($this->queryCache[$cacheKey])) {
            unset($this->queryCache[$cacheKey]);
        }
    }

    /**
     * Get the current environment
     *
     * @return string Current environment ('production', 'development', etc.)
     */
    public function getEnvironment() {
        return $this->isProduction ? 'production' : 'development';
    }
}
