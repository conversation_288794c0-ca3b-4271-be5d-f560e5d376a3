@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles that should be included in the final output */
@layer components {
  /* Modal styles */
  .modal {
    @apply fixed inset-0 z-50 flex items-center justify-center overflow-auto bg-black bg-opacity-50;
  }

  .modal-content {
    @apply relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-auto p-6;
  }

  /* Dark mode specific styles */
  .dark .modal-content {
    @apply bg-gray-800 text-white;
  }

  /* Ensure modal buttons work properly */
  .modal-content button {
    @apply cursor-pointer;
  }
}
