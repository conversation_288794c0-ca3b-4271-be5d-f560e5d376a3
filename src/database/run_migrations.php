<?php
/**
 * Run database migrations
 *
 * This script runs the SQL migrations to create the checklist tables
 */

require_once __DIR__ . '/../utils/Database.php';

// Initialize the database connection
$db = Database::getInstance();
$conn = $db->getConnection();

// Read the SQL file
$sqlFile = file_get_contents(__DIR__ . '/migrations/create_checklists_tables.sql');

// Split SQL file into individual statements
$sqlStatements = preg_split('/;\s*$/m', $sqlFile);

// Execute each SQL statement
try {
    $success = true;

    foreach ($sqlStatements as $sql) {
        $sql = trim($sql);
        if (!empty($sql)) {
            try {
                $conn->exec($sql);
            } catch (PDOException $e) {
                echo "Error executing SQL: " . $e->getMessage() . "\n";
                echo "SQL statement: " . $sql . "\n";
                $success = false;
                break;
            }
        }
    }

    if ($success) {
        echo "Checklist tables created successfully!\n";
    } else {
        echo "Failed to create all checklist tables.\n";
    }
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}
