<?php
/**
 * Data Analysis Brigade Template
 * 
 * This template defines the structure, tasks, and agent roles for a Data Analysis Brigade project.
 */

return [
    'name' => 'Data Analysis Brigade',
    'description' => 'A specialized brigade for transforming raw data into actionable business insights.',
    'brigade_type' => 'data_analysis',
    'roles' => [
        'brigade_commander' => [
            'name' => 'Brigade Commander',
            'description' => 'Oversees the entire brigade and coordinates between different agent types.',
            'required_skills' => ['leadership', 'data_strategy', 'project_management'],
            'default_agent' => 'Aegis Director'
        ],
        'data_collection_agent' => [
            'name' => 'Data Collection Agent',
            'description' => 'Gathers and organizes data from various sources.',
            'required_skills' => ['data_collection', 'api_integration', 'data_scraping'],
            'default_agent' => null
        ],
        'processing_agent' => [
            'name' => 'Processing Agent',
            'description' => 'Cleans, normalizes, and prepares data for analysis.',
            'required_skills' => ['data_cleaning', 'data_transformation', 'data_normalization'],
            'default_agent' => null
        ],
        'analysis_agent' => [
            'name' => 'Analysis Agent',
            'description' => 'Identifies patterns, trends, and insights from processed data.',
            'required_skills' => ['data_analysis', 'statistical_methods', 'pattern_recognition'],
            'default_agent' => null
        ],
        'visualization_agent' => [
            'name' => 'Visualization Agent',
            'description' => 'Creates clear, compelling data visualizations.',
            'required_skills' => ['data_visualization', 'chart_creation', 'dashboard_design'],
            'default_agent' => null
        ],
        'recommendation_agent' => [
            'name' => 'Recommendation Agent',
            'description' => 'Generates actionable recommendations based on analysis.',
            'required_skills' => ['business_strategy', 'decision_support', 'recommendation_systems'],
            'default_agent' => null
        ]
    ],
    'tasks' => [
        [
            'name' => 'Business Objectives Definition',
            'description' => 'Define the key business questions and objectives for the data analysis.',
            'assigned_role' => 'brigade_commander',
            'duration' => 120, // minutes
            'priority' => 'critical',
            'status' => 'pending',
            'dependencies' => []
        ],
        [
            'name' => 'Data Source Identification',
            'description' => 'Identify all relevant data sources needed for the analysis.',
            'assigned_role' => 'data_collection_agent',
            'duration' => 120,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Business Objectives Definition']
        ],
        [
            'name' => 'Data Collection Plan Development',
            'description' => 'Develop a comprehensive plan for collecting data from all identified sources.',
            'assigned_role' => 'data_collection_agent',
            'duration' => 90,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Data Source Identification']
        ],
        [
            'name' => 'Data Collection Implementation',
            'description' => 'Implement the data collection plan and gather data from all sources.',
            'assigned_role' => 'data_collection_agent',
            'duration' => 240,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Data Collection Plan Development']
        ],
        [
            'name' => 'Data Quality Assessment',
            'description' => 'Assess the quality of collected data and identify issues.',
            'assigned_role' => 'processing_agent',
            'duration' => 120,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Data Collection Implementation']
        ],
        [
            'name' => 'Data Cleaning and Preparation',
            'description' => 'Clean, normalize, and prepare the data for analysis.',
            'assigned_role' => 'processing_agent',
            'duration' => 180,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Data Quality Assessment']
        ],
        [
            'name' => 'Exploratory Data Analysis',
            'description' => 'Conduct initial exploratory analysis to identify patterns and trends.',
            'assigned_role' => 'analysis_agent',
            'duration' => 180,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Data Cleaning and Preparation']
        ],
        [
            'name' => 'Advanced Analysis Execution',
            'description' => 'Perform advanced statistical analysis and modeling as needed.',
            'assigned_role' => 'analysis_agent',
            'duration' => 240,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Exploratory Data Analysis']
        ],
        [
            'name' => 'Data Visualization Design',
            'description' => 'Design clear, compelling visualizations to communicate insights.',
            'assigned_role' => 'visualization_agent',
            'duration' => 150,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Exploratory Data Analysis']
        ],
        [
            'name' => 'Dashboard Creation',
            'description' => 'Create interactive dashboards for ongoing monitoring and analysis.',
            'assigned_role' => 'visualization_agent',
            'duration' => 180,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Data Visualization Design', 'Advanced Analysis Execution']
        ],
        [
            'name' => 'Insights Identification',
            'description' => 'Identify key insights and their business implications.',
            'assigned_role' => 'recommendation_agent',
            'duration' => 120,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Advanced Analysis Execution']
        ],
        [
            'name' => 'Recommendations Development',
            'description' => 'Develop actionable recommendations based on the insights.',
            'assigned_role' => 'recommendation_agent',
            'duration' => 150,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Insights Identification']
        ],
        [
            'name' => 'Final Report Creation',
            'description' => 'Create a comprehensive final report with all findings and recommendations.',
            'assigned_role' => 'brigade_commander',
            'duration' => 180,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Recommendations Development', 'Dashboard Creation']
        ],
        [
            'name' => 'Implementation Plan Development',
            'description' => 'Develop a plan for implementing the recommendations.',
            'assigned_role' => 'brigade_commander',
            'duration' => 120,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Final Report Creation']
        ]
    ]
];
