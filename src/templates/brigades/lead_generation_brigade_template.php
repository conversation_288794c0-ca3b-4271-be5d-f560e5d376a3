<?php
/**
 * Lead Generation Brigade Template
 * 
 * This template defines the structure, tasks, and agent roles for a Lead Generation Brigade project.
 */

return [
    'name' => 'Lead Generation Brigade',
    'description' => 'A specialized brigade for identifying and engaging potential clients through personalized outreach.',
    'brigade_type' => 'lead_generation',
    'roles' => [
        'brigade_commander' => [
            'name' => 'Brigade Commander',
            'description' => 'Oversees the entire brigade and coordinates between different agent types.',
            'required_skills' => ['leadership', 'sales_strategy', 'project_management'],
            'default_agent' => 'Aegis Director'
        ],
        'prospect_identification_agent' => [
            'name' => 'Prospect Identification Agent',
            'description' => 'Finds potential clients matching criteria.',
            'required_skills' => ['lead_generation', 'market_research', 'data_mining'],
            'default_agent' => null
        ],
        'research_agent' => [
            'name' => 'Research Agent',
            'description' => 'Gathers detailed information about prospects.',
            'required_skills' => ['research', 'data_analysis', 'information_gathering'],
            'default_agent' => null
        ],
        'personalization_agent' => [
            'name' => 'Personalization Agent',
            'description' => 'Creates customized outreach messages.',
            'required_skills' => ['personalization', 'copywriting', 'psychology'],
            'default_agent' => null
        ],
        'engagement_agent' => [
            'name' => 'Engagement Agent',
            'description' => 'Manages follow-up sequences.',
            'required_skills' => ['follow_up', 'communication', 'relationship_building'],
            'default_agent' => null
        ],
        'analytics_agent' => [
            'name' => 'Analytics Agent',
            'description' => 'Tracks campaign performance and optimizes strategies.',
            'required_skills' => ['analytics', 'data_visualization', 'optimization'],
            'default_agent' => null
        ]
    ],
    'tasks' => [
        [
            'name' => 'Target Market Definition',
            'description' => 'Define the ideal customer profile and target market segments.',
            'assigned_role' => 'brigade_commander',
            'duration' => 120, // minutes
            'priority' => 'critical',
            'status' => 'pending',
            'dependencies' => []
        ],
        [
            'name' => 'Prospect List Building',
            'description' => 'Build a list of potential prospects matching the target criteria.',
            'assigned_role' => 'prospect_identification_agent',
            'duration' => 240,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Target Market Definition']
        ],
        [
            'name' => 'Prospect Research',
            'description' => 'Gather detailed information about each prospect for personalization.',
            'assigned_role' => 'research_agent',
            'duration' => 180,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Prospect List Building']
        ],
        [
            'name' => 'Outreach Strategy Development',
            'description' => 'Develop a multi-channel outreach strategy with messaging frameworks.',
            'assigned_role' => 'brigade_commander',
            'duration' => 120,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Target Market Definition']
        ],
        [
            'name' => 'Message Template Creation',
            'description' => 'Create customizable message templates for different channels and prospect types.',
            'assigned_role' => 'personalization_agent',
            'duration' => 120,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Outreach Strategy Development']
        ],
        [
            'name' => 'Personalized Message Generation',
            'description' => 'Generate personalized outreach messages for each prospect.',
            'assigned_role' => 'personalization_agent',
            'duration' => 180,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Message Template Creation', 'Prospect Research']
        ],
        [
            'name' => 'Initial Outreach Campaign Setup',
            'description' => 'Set up the initial outreach campaign across selected channels.',
            'assigned_role' => 'engagement_agent',
            'duration' => 120,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Personalized Message Generation']
        ],
        [
            'name' => 'Follow-up Sequence Creation',
            'description' => 'Create automated follow-up sequences for different response scenarios.',
            'assigned_role' => 'engagement_agent',
            'duration' => 120,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Initial Outreach Campaign Setup']
        ],
        [
            'name' => 'Analytics Dashboard Setup',
            'description' => 'Set up tracking and analytics to measure campaign performance.',
            'assigned_role' => 'analytics_agent',
            'duration' => 90,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Initial Outreach Campaign Setup']
        ],
        [
            'name' => 'Response Management',
            'description' => 'Monitor and manage responses from prospects.',
            'assigned_role' => 'engagement_agent',
            'duration' => 120,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Initial Outreach Campaign Setup', 'Follow-up Sequence Creation']
        ],
        [
            'name' => 'Campaign Performance Analysis',
            'description' => 'Analyze campaign performance and identify optimization opportunities.',
            'assigned_role' => 'analytics_agent',
            'duration' => 120,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Analytics Dashboard Setup', 'Response Management']
        ],
        [
            'name' => 'Strategy Optimization',
            'description' => 'Optimize the outreach strategy based on performance data.',
            'assigned_role' => 'brigade_commander',
            'duration' => 120,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Campaign Performance Analysis']
        ]
    ]
];
