<?php
/**
 * Content Creation Brigade Template
 * 
 * This template defines the structure, tasks, and agent roles for a Content Creation Brigade project.
 */

return [
    'name' => 'Content Creation Brigade',
    'description' => 'A specialized brigade for generating high-quality, SEO-optimized content at scale for businesses.',
    'brigade_type' => 'content_creation',
    'roles' => [
        'brigade_commander' => [
            'name' => 'Brigade Commander',
            'description' => 'Oversees the entire brigade and coordinates between different agent types.',
            'required_skills' => ['leadership', 'content_strategy', 'project_management'],
            'default_agent' => 'Aegis Director'
        ],
        'research_agent' => [
            'name' => 'Research Agent',
            'description' => 'Gathers information and identifies trending topics.',
            'required_skills' => ['research', 'trend_analysis', 'data_collection'],
            'default_agent' => null
        ],
        'content_planning_agent' => [
            'name' => 'Content Planning Agent',
            'description' => 'Creates content outlines and strategies.',
            'required_skills' => ['content_strategy', 'outline_creation', 'topic_clustering'],
            'default_agent' => null
        ],
        'writing_agent' => [
            'name' => 'Writing Agent',
            'description' => 'Generates actual content based on outlines.',
            'required_skills' => ['content_writing', 'storytelling', 'grammar'],
            'default_agent' => null
        ],
        'editing_agent' => [
            'name' => 'Editing Agent',
            'description' => 'Refines and improves content quality.',
            'required_skills' => ['editing', 'proofreading', 'style_improvement'],
            'default_agent' => null
        ],
        'seo_optimization_agent' => [
            'name' => 'SEO Optimization Agent',
            'description' => 'Ensures content ranks well in search engines.',
            'required_skills' => ['seo', 'keyword_research', 'content_optimization'],
            'default_agent' => null
        ]
    ],
    'tasks' => [
        [
            'name' => 'Market and Audience Research',
            'description' => 'Research the target market, audience demographics, and content preferences.',
            'assigned_role' => 'research_agent',
            'duration' => 120, // minutes
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => []
        ],
        [
            'name' => 'Competitor Content Analysis',
            'description' => 'Analyze competitor content to identify gaps and opportunities.',
            'assigned_role' => 'research_agent',
            'duration' => 120,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => []
        ],
        [
            'name' => 'Keyword Research and Analysis',
            'description' => 'Identify high-value keywords and search terms for content optimization.',
            'assigned_role' => 'seo_optimization_agent',
            'duration' => 90,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => []
        ],
        [
            'name' => 'Content Strategy Development',
            'description' => 'Develop a comprehensive content strategy based on research findings.',
            'assigned_role' => 'content_planning_agent',
            'duration' => 180,
            'priority' => 'critical',
            'status' => 'pending',
            'dependencies' => ['Market and Audience Research', 'Competitor Content Analysis', 'Keyword Research and Analysis']
        ],
        [
            'name' => 'Content Calendar Creation',
            'description' => 'Create a detailed content calendar with topics, keywords, and publishing schedule.',
            'assigned_role' => 'content_planning_agent',
            'duration' => 120,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Content Strategy Development']
        ],
        [
            'name' => 'Content Outline Development',
            'description' => 'Create detailed outlines for each piece of content.',
            'assigned_role' => 'content_planning_agent',
            'duration' => 60,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Content Calendar Creation']
        ],
        [
            'name' => 'Content Creation',
            'description' => 'Write high-quality content based on the approved outlines.',
            'assigned_role' => 'writing_agent',
            'duration' => 240,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Content Outline Development']
        ],
        [
            'name' => 'Content Editing and Refinement',
            'description' => 'Edit and refine the content for clarity, engagement, and brand voice consistency.',
            'assigned_role' => 'editing_agent',
            'duration' => 120,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Content Creation']
        ],
        [
            'name' => 'SEO Optimization',
            'description' => 'Optimize content for search engines with proper keyword placement, meta descriptions, etc.',
            'assigned_role' => 'seo_optimization_agent',
            'duration' => 90,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Content Editing and Refinement']
        ],
        [
            'name' => 'Final Review and Approval',
            'description' => 'Conduct a final review of all content and obtain approval for publication.',
            'assigned_role' => 'brigade_commander',
            'duration' => 60,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['SEO Optimization']
        ],
        [
            'name' => 'Content Performance Tracking Setup',
            'description' => 'Set up tracking and analytics to measure content performance.',
            'assigned_role' => 'seo_optimization_agent',
            'duration' => 60,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Final Review and Approval']
        ],
        [
            'name' => 'Performance Analysis and Optimization',
            'description' => 'Analyze content performance and make recommendations for optimization.',
            'assigned_role' => 'brigade_commander',
            'duration' => 120,
            'priority' => 'low',
            'status' => 'pending',
            'dependencies' => ['Content Performance Tracking Setup']
        ]
    ]
];
