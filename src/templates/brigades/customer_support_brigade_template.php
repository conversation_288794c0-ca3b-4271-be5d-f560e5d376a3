<?php
/**
 * Customer Support Brigade Template
 * 
 * This template defines the structure, tasks, and agent roles for a Customer Support Brigade project.
 */

return [
    'name' => 'Customer Support Brigade',
    'description' => 'A specialized brigade for providing 24/7 automated customer support across multiple channels.',
    'brigade_type' => 'customer_support',
    'roles' => [
        'brigade_commander' => [
            'name' => 'Brigade Commander',
            'description' => 'Oversees the entire brigade and coordinates between different agent types.',
            'required_skills' => ['leadership', 'customer_service', 'project_management'],
            'default_agent' => 'Aegis Director'
        ],
        'triage_agent' => [
            'name' => 'Triage Agent',
            'description' => 'Categorizes and prioritizes incoming queries.',
            'required_skills' => ['query_classification', 'priority_assessment', 'pattern_recognition'],
            'default_agent' => null
        ],
        'knowledge_agent' => [
            'name' => 'Knowledge Agent',
            'description' => 'Retrieves relevant information from knowledge bases.',
            'required_skills' => ['knowledge_management', 'information_retrieval', 'data_organization'],
            'default_agent' => null
        ],
        'response_agent' => [
            'name' => 'Response Agent',
            'description' => 'Generates personalized, helpful responses.',
            'required_skills' => ['communication', 'empathy', 'problem_solving'],
            'default_agent' => null
        ],
        'escalation_agent' => [
            'name' => 'Escalation Agent',
            'description' => 'Identifies when human intervention is needed.',
            'required_skills' => ['escalation_management', 'critical_thinking', 'decision_making'],
            'default_agent' => null
        ],
        'analytics_agent' => [
            'name' => 'Analytics Agent',
            'description' => 'Tracks performance and identifies improvement opportunities.',
            'required_skills' => ['analytics', 'data_visualization', 'pattern_recognition'],
            'default_agent' => null
        ]
    ],
    'tasks' => [
        [
            'name' => 'Support Needs Assessment',
            'description' => 'Assess customer support needs and requirements.',
            'assigned_role' => 'brigade_commander',
            'duration' => 120, // minutes
            'priority' => 'critical',
            'status' => 'pending',
            'dependencies' => []
        ],
        [
            'name' => 'Knowledge Base Audit',
            'description' => 'Audit existing knowledge base and identify gaps.',
            'assigned_role' => 'knowledge_agent',
            'duration' => 180,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Support Needs Assessment']
        ],
        [
            'name' => 'Query Classification System Development',
            'description' => 'Develop a system to classify and prioritize incoming queries.',
            'assigned_role' => 'triage_agent',
            'duration' => 150,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Support Needs Assessment']
        ],
        [
            'name' => 'Knowledge Base Enhancement',
            'description' => 'Enhance and organize the knowledge base for efficient retrieval.',
            'assigned_role' => 'knowledge_agent',
            'duration' => 240,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Knowledge Base Audit']
        ],
        [
            'name' => 'Response Template Creation',
            'description' => 'Create customizable response templates for common queries.',
            'assigned_role' => 'response_agent',
            'duration' => 180,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Query Classification System Development', 'Knowledge Base Enhancement']
        ],
        [
            'name' => 'Escalation Protocol Development',
            'description' => 'Develop clear protocols for when to escalate issues to human agents.',
            'assigned_role' => 'escalation_agent',
            'duration' => 120,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Support Needs Assessment']
        ],
        [
            'name' => 'Multi-Channel Integration Setup',
            'description' => 'Set up integration with multiple support channels (chat, email, social media).',
            'assigned_role' => 'brigade_commander',
            'duration' => 180,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Query Classification System Development']
        ],
        [
            'name' => 'Analytics Dashboard Creation',
            'description' => 'Create a dashboard to track support performance metrics.',
            'assigned_role' => 'analytics_agent',
            'duration' => 120,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Multi-Channel Integration Setup']
        ],
        [
            'name' => 'Automated Response System Testing',
            'description' => 'Test the automated response system with sample queries.',
            'assigned_role' => 'response_agent',
            'duration' => 150,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Response Template Creation', 'Multi-Channel Integration Setup']
        ],
        [
            'name' => 'Escalation System Testing',
            'description' => 'Test the escalation system with complex or sensitive queries.',
            'assigned_role' => 'escalation_agent',
            'duration' => 120,
            'priority' => 'high',
            'status' => 'pending',
            'dependencies' => ['Escalation Protocol Development', 'Automated Response System Testing']
        ],
        [
            'name' => 'Customer Satisfaction Measurement Setup',
            'description' => 'Set up systems to measure customer satisfaction with support interactions.',
            'assigned_role' => 'analytics_agent',
            'duration' => 90,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Automated Response System Testing']
        ],
        [
            'name' => 'Performance Analysis and Optimization',
            'description' => 'Analyze support performance and implement optimizations.',
            'assigned_role' => 'brigade_commander',
            'duration' => 150,
            'priority' => 'medium',
            'status' => 'pending',
            'dependencies' => ['Analytics Dashboard Creation', 'Customer Satisfaction Measurement Setup']
        ]
    ]
];
