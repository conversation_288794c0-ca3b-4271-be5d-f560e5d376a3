<?php
/**
 * Online Business Controller
 *
 * This controller handles the online business dashboard functionality.
 */

class OnlineBusinessController extends BaseController
{
    private $ventureModel;
    private $metricModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        // Initialize models directly
        $this->ventureModel = new BusinessVenture();
        $this->metricModel = new BusinessMetric();
    }

    /**
     * Display the main business dashboard
     */
    public function index()
    {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get pagination parameters
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 10;

        // Validate pagination parameters
        $page = max(1, $page);
        $perPage = max(5, min(50, $perPage)); // Limit between 5 and 50 items per page

        // Get business ventures with pagination
        $venturesData = $this->ventureModel->getUserVentures($userId, $filters, $page, $perPage);
        $ventures = $venturesData['ventures'];
        $pagination = $venturesData['pagination'];

        // Get venture summary
        $ventureSummary = $this->ventureModel->getVentureSummary($userId);

        $this->view('online-business/index', [
            'businessVentures' => $ventures,
            'pagination' => $pagination,
            'ventureSummary' => $ventureSummary,
            'filters' => $filters,
            'performanceMetrics' => $this->getPerformanceMetrics($userId)
        ]);
    }

    /**
     * Display the dashboard for a specific business venture
     *
     * @param int $id The venture ID
     */
    public function dashboard($id)
    {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the venture
        $venture = $this->ventureModel->getVenture($id, $userId);

        if (!$venture) {
            Session::setFlash('error', 'Business venture not found');
            $this->redirect('/online-business');
            return;
        }

        // Get date range parameters
        $startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
        $endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
        $period = isset($_GET['period']) ? $_GET['period'] : 'daily';

        // Calculate appropriate max data points based on screen size (approximate)
        // For charts, 100 data points is usually sufficient for most screens
        $maxDataPoints = 100;

        // Get metrics for the venture with data point limitation
        $metrics = $this->metricModel->getVentureMetrics($id, $period, $startDate, $endDate, $maxDataPoints);

        // Get metrics summary
        $metricsSummary = $this->metricModel->getMetricsSummary($id, $startDate, $endDate);

        // Get latest metrics
        $latestMetrics = $this->metricModel->getLatestMetrics($id);

        $this->view('online-business/view', [
            'businessVenture' => $venture,
            'metrics' => $this->getVentureMetrics($userId, $id),
            'metricsSummary' => $metricsSummary,
            'latestMetrics' => $latestMetrics,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'period' => $period
        ]);
    }

    /**
     * Show venture creation form
     */
    public function create()
    {
        $this->requireLogin();

        $this->view('online-business/create');
    }

    /**
     * Store a new business venture
     */
    public function store()
    {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        $requiredFields = ['name', 'business_type'];
        $errors = $this->validateRequired($data, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->view('online-business/create', [
                'data' => $data,
                'errors' => $errors
            ]);
            return;
        }

        // Prepare data for saving
        $ventureData = [
            'user_id' => $userId,
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'business_type' => $data['business_type'],
            'website' => $data['website'] ?? null,
            'start_date' => $data['start_date'] ?? null,
            'status' => $data['status'] ?? 'planning',
            'notes' => $data['notes'] ?? null
        ];

        // Create venture
        $ventureId = $this->ventureModel->create($ventureData);

        if ($ventureId) {
            Session::setFlash('success', 'Business venture added successfully');
            $this->redirect('/online-business/dashboard/' . $ventureId);
        } else {
            Session::setFlash('error', 'Failed to add business venture');
            $this->view('online-business/create', [
                'data' => $data
            ]);
        }
    }

    /**
     * Show venture edit form
     *
     * @param int $id The venture ID
     */
    public function edit($id)
    {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the venture
        $venture = $this->ventureModel->getVenture($id, $userId);

        if (!$venture) {
            Session::setFlash('error', 'Business venture not found');
            $this->redirect('/online-business');
            return;
        }

        $this->view('online-business/view', [
            'businessVenture' => $venture,
            'metrics' => $this->getVentureMetrics($userId, $id)
        ]);
    }

    /**
     * Update a business venture
     *
     * @param int $id The venture ID
     */
    public function update($id)
    {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the venture
        $venture = $this->ventureModel->getVenture($id, $userId);

        if (!$venture) {
            Session::setFlash('error', 'Business venture not found');
            $this->redirect('/online-business');
            return;
        }

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        $requiredFields = ['name', 'business_type'];
        $errors = $this->validateRequired($data, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->view('online-business/edit', [
                'businessVenture' => array_merge($venture, $data),
                'errors' => $errors
            ]);
            return;
        }

        // Prepare data for saving
        $ventureData = [
            'user_id' => $userId,
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'business_type' => $data['business_type'],
            'website' => $data['website'] ?? null,
            'start_date' => $data['start_date'] ?? null,
            'status' => $data['status'] ?? 'planning',
            'notes' => $data['notes'] ?? null
        ];

        // Update venture
        $success = $this->ventureModel->update($id, $ventureData);

        if ($success) {
            Session::setFlash('success', 'Business venture updated successfully');
            $this->redirect('/online-business/dashboard/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update business venture');
            $this->view('online-business/edit', [
                'businessVenture' => array_merge($venture, $data)
            ]);
        }
    }

    /**
     * Delete a business venture
     *
     * @param int $id The venture ID
     */
    public function delete($id)
    {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the venture
        $venture = $this->ventureModel->getVenture($id, $userId);

        if (!$venture) {
            Session::setFlash('error', 'Business venture not found');
            $this->redirect('/online-business');
            return;
        }

        // Delete venture
        $success = $this->ventureModel->delete($id, $userId);

        if ($success) {
            Session::setFlash('success', 'Business venture deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete business venture');
        }

        $this->redirect('/online-business');
    }

    /**
     * Show metrics form for a venture
     *
     * @param int $id The venture ID
     */
    public function metrics($id)
    {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the venture
        $venture = $this->ventureModel->getVenture($id, $userId);

        if (!$venture) {
            Session::setFlash('error', 'Business venture not found');
            $this->redirect('/online-business');
            return;
        }

        // Get latest metrics if available
        $latestMetrics = $this->metricModel->getLatestMetrics($id);

        $this->view('online-business/metrics', [
            'businessVenture' => $venture,
            'latestMetrics' => $latestMetrics
        ]);
    }

    /**
     * Get performance metrics for a user
     *
     * @param int $userId The user ID (not used in this placeholder implementation)
     * @return array The performance metrics
     */
    private function getPerformanceMetrics($userId)
    {
        // This is a placeholder. In a real implementation, this would fetch data from the database.
        return [
            'revenue' => [
                'current_month' => 1250,
                'previous_month' => 980,
                'growth' => 27.55
            ],
            'traffic' => [
                'current_month' => 5600,
                'previous_month' => 4200,
                'growth' => 33.33
            ],
            'conversion_rate' => [
                'current_month' => 2.8,
                'previous_month' => 2.5,
                'growth' => 12.00
            ],
            'customer_acquisition_cost' => [
                'current_month' => 15.20,
                'previous_month' => 18.50,
                'growth' => -17.84
            ]
        ];
    }

    /**
     * Get metrics for a specific venture
     *
     * @param int $userId The user ID (not used in this placeholder implementation)
     * @param int $id The business venture ID (not used in this placeholder implementation)
     * @return array The metrics
     */
    private function getVentureMetrics($userId, $id)
    {
        // This is a placeholder. In a real implementation, this would fetch data from the database.
        return [
            'revenue' => [
                'current_month' => 850,
                'previous_month' => 720,
                'growth' => 18.06
            ],
            'traffic' => [
                'current_month' => 3200,
                'previous_month' => 2800,
                'growth' => 14.29
            ],
            'conversion_rate' => [
                'current_month' => 3.1,
                'previous_month' => 2.8,
                'growth' => 10.71
            ]
        ];
    }

    /**
     * Save metrics for a venture
     *
     * @param int $id The venture ID
     */
    public function saveMetrics($id)
    {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the venture
        $venture = $this->ventureModel->getVenture($id, $userId);

        if (!$venture) {
            Session::setFlash('error', 'Business venture not found');
            $this->redirect('/online-business');
            return;
        }

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        $requiredFields = ['metric_date'];
        $errors = $this->validateRequired($data, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->view('online-business/metrics', [
                'businessVenture' => $venture,
                'data' => $data,
                'errors' => $errors
            ]);
            return;
        }

        // Prepare data for saving
        $metricData = [
            'venture_id' => $id,
            'metric_date' => $data['metric_date'],
            'revenue' => $data['revenue'] ?? 0,
            'expenses' => $data['expenses'] ?? 0,
            'profit' => $data['profit'] ?? 0,
            'sales_count' => $data['sales_count'] ?? 0,
            'new_customers' => $data['new_customers'] ?? 0,
            'website_visits' => $data['website_visits'] ?? null,
            'conversion_rate' => $data['conversion_rate'] ?? null,
            'average_order_value' => $data['average_order_value'] ?? null,
            'notes' => $data['notes'] ?? null
        ];

        // Save metrics
        $success = $this->metricModel->saveMetrics($metricData);

        if ($success) {
            Session::setFlash('success', 'Business metrics saved successfully');
            $this->redirect('/online-business/dashboard/' . $id);
        } else {
            Session::setFlash('error', 'Failed to save business metrics');
            $this->view('online-business/metrics', [
                'businessVenture' => $venture,
                'data' => $data
            ]);
        }
    }
}
