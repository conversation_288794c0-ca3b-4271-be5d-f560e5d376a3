<?php
/**
 * Medical Controller
 *
 * Handles medical test reports and health tracking features
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/MedicalTestReport.php';
require_once __DIR__ . '/../models/MedicationTracker.php';
require_once __DIR__ . '/../models/ADHDMedicationReference.php';
require_once __DIR__ . '/../models/PetTracker.php';
require_once __DIR__ . '/../utils/Database.php';

class MedicalController extends BaseController {
    private $reportModel;
    private $medicationModel;
    private $medicationReferenceModel;
    private $petModel;

    public function __construct() {
        $this->reportModel = new MedicalTestReport();
        $this->medicationModel = new MedicationTracker();
        $this->medicationReferenceModel = new ADHDMedicationReference();
        $this->petModel = new PetTracker();
    }

    /**
     * Show medical dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get recent reports
        $recentReports = $this->reportModel->getUserReports($userId, 5);

        // Get report counts by type
        $reportCounts = $this->reportModel->countReportsByType($userId);

        // Get recent abnormal parameters
        $abnormalParameters = $this->reportModel->getRecentAbnormalParameters($userId, 90);

        $this->view('medical/index', [
            'recentReports' => $recentReports,
            'reportCounts' => $reportCounts,
            'abnormalParameters' => $abnormalParameters
        ]);
    }

    /**
     * List all reports
     */
    public function reports() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();
        $reportType = isset($filters['type']) ? $filters['type'] : null;

        // Get reports based on filters
        if ($reportType) {
            $reports = $this->reportModel->getUserReportsByType($userId, $reportType);

            // For scan reports, use a specialized view
            if ($reportType === 'scan') {
                // Get all scan types for the filter dropdown
                $allScanTypes = $this->reportModel->getDistinctScanTypes($userId);

                // Get all scan reports for this user (for the filter dropdown)
                $allScanReports = $this->reportModel->getUserReportsByType($userId, 'scan');

                $this->view('medical/reports/scan_reports', [
                    'reports' => $reports,
                    'filters' => $filters,
                    'allScanTypes' => $allScanTypes,
                    'allScanReports' => $allScanReports
                ]);
                return;
            }
        } else {
            $reports = $this->reportModel->getUserReports($userId);
        }

        $this->view('medical/reports/index', [
            'reports' => $reports,
            'filters' => $filters
        ]);
    }

    /**
     * Show new report form
     */
    public function newReport() {
        $this->requireLogin();

        // Get report type from query string
        $reportType = $this->getQueryData('type') ?: 'blood';

        // Get common parameters for this report type
        $commonParameters = $this->reportModel->getCommonParametersByCategory($reportType);

        $this->view('medical/reports/report_form', [
            'reportType' => $reportType,
            'commonParameters' => $commonParameters
        ]);
    }

    /**
     * Save a new report
     */
    public function saveReport() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = [
            'report_type',
            'report_date',
            'report_title'
        ];

        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/reports/new');
            return;
        }

        // Handle file upload if present
        $filePath = null;
        if (isset($_FILES['report_file']) && $_FILES['report_file']['error'] === UPLOAD_ERR_OK) {
            $uploadDir = __DIR__ . '/../../public/uploads/reports/';

            // Create directory if it doesn't exist
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            $fileName = time() . '_' . basename($_FILES['report_file']['name']);
            $filePath = 'uploads/reports/' . $fileName;
            $uploadFile = $uploadDir . $fileName;

            if (move_uploaded_file($_FILES['report_file']['tmp_name'], $uploadFile)) {
                // File uploaded successfully
            } else {
                Session::setFlash('error', 'Failed to upload file');
                $this->redirect('/medical/reports/new');
                return;
            }
        }

        // Prepare report data
        $reportData = [
            'user_id' => $userId,
            'report_type' => $postData['report_type'],
            'report_date' => $postData['report_date'],
            'lab_name' => $postData['lab_name'] ?? null,
            'doctor_name' => $postData['doctor_name'] ?? null,
            'report_title' => $postData['report_title'],
            'notes' => $postData['notes'] ?? null,
            'file_path' => $filePath,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add scan_type if this is a scan report
        if ($postData['report_type'] === 'scan' && isset($postData['scan_type'])) {
            $reportData['scan_type'] = $postData['scan_type'];
        }

        // Get database instance
        $db = Database::getInstance();

        // Start transaction
        $db->beginTransaction();

        try {
            // Create report
            $reportId = $this->reportModel->createReport($reportData);

            if (!$reportId) {
                throw new Exception('Failed to create report');
            }

            // Process parameters if any
            if (isset($postData['parameters']) && is_array($postData['parameters'])) {
                foreach ($postData['parameters'] as $paramName => $paramData) {
                    // Skip if parameter value is empty
                    if (empty($paramData['value'])) {
                        continue;
                    }

                    // For custom parameters, use the name field if provided
                    $displayName = $paramName;
                    if (strpos($paramName, 'param_') === 0 && isset($paramData['name'])) {
                        $displayName = $paramData['name'];
                    }

                    // Determine if value is abnormal
                    $isAbnormal = 0;
                    if (
                        (!empty($paramData['min']) && $paramData['value'] < $paramData['min']) ||
                        (!empty($paramData['max']) && $paramData['value'] > $paramData['max'])
                    ) {
                        $isAbnormal = 1;
                    }

                    $parameterData = [
                        'report_id' => $reportId,
                        'parameter_name' => $displayName,
                        'parameter_value' => $paramData['value'],
                        'unit' => $paramData['unit'] ?? null,
                        'reference_range_min' => $paramData['min'] ?? null,
                        'reference_range_max' => $paramData['max'] ?? null,
                        'is_abnormal' => $isAbnormal,
                        'notes' => $paramData['notes'] ?? null,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    $parameterId = $this->reportModel->addParameter($parameterData);

                    if (!$parameterId) {
                        throw new Exception('Failed to add parameter: ' . $displayName);
                    }
                }
            }

            // Commit transaction
            $db->commit();

            Session::setFlash('success', 'Report saved successfully');
            $this->redirect('/medical/reports/view/' . $reportId);
        } catch (Exception $e) {
            // Rollback transaction
            $db->rollback();

            // Delete uploaded file if exists
            if ($filePath && file_exists(__DIR__ . '/../../public/' . $filePath)) {
                unlink(__DIR__ . '/../../public/' . $filePath);
            }

            Session::setFlash('error', 'Error saving report: ' . $e->getMessage());
            $this->redirect('/medical/reports/new');
        }
    }

    /**
     * View a report
     */
    public function viewReport($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get report
        $report = $this->reportModel->getReport($id);

        if (!$report || $report['user_id'] != $userId) {
            Session::setFlash('error', 'Report not found');
            $this->redirect('/medical/reports');
            return;
        }

        // Get report parameters
        $parameters = $this->reportModel->getReportParameters($id);

        $this->view('medical/reports/report_view', [
            'report' => $report,
            'parameters' => $parameters
        ]);
    }

    /**
     * Show edit report form
     */
    public function editReport($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get report
        $report = $this->reportModel->getReport($id);

        if (!$report || $report['user_id'] != $userId) {
            Session::setFlash('error', 'Report not found');
            $this->redirect('/medical/reports');
            return;
        }

        // Get report parameters
        $parameters = $this->reportModel->getReportParameters($id);

        // Get common parameters for this report type
        $commonParameters = $this->reportModel->getCommonParametersByCategory($report['report_type']);

        $this->view('medical/reports/report_edit', [
            'report' => $report,
            'parameters' => $parameters,
            'commonParameters' => $commonParameters
        ]);
    }

    /**
     * Update a report
     */
    public function updateReport($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get report
        $report = $this->reportModel->getReport($id);

        if (!$report || $report['user_id'] != $userId) {
            Session::setFlash('error', 'Report not found');
            $this->redirect('/medical/reports');
            return;
        }

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = [
            'report_type',
            'report_date',
            'report_title'
        ];

        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/reports/edit/' . $id);
            return;
        }

        // Handle file upload if present
        $filePath = $report['file_path'];
        if (isset($_FILES['report_file']) && $_FILES['report_file']['error'] === UPLOAD_ERR_OK) {
            $uploadDir = __DIR__ . '/../../public/uploads/reports/';

            // Create directory if it doesn't exist
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            $fileName = time() . '_' . basename($_FILES['report_file']['name']);
            $filePath = 'uploads/reports/' . $fileName;
            $uploadFile = $uploadDir . $fileName;

            if (move_uploaded_file($_FILES['report_file']['tmp_name'], $uploadFile)) {
                // Delete old file if exists
                if ($report['file_path'] && file_exists(__DIR__ . '/../../public/' . $report['file_path'])) {
                    unlink(__DIR__ . '/../../public/' . $report['file_path']);
                }
            } else {
                Session::setFlash('error', 'Failed to upload file');
                $this->redirect('/medical/reports/edit/' . $id);
                return;
            }
        }

        // Prepare report data
        $reportData = [
            'report_type' => $postData['report_type'],
            'report_date' => $postData['report_date'],
            'lab_name' => $postData['lab_name'] ?? null,
            'doctor_name' => $postData['doctor_name'] ?? null,
            'report_title' => $postData['report_title'],
            'notes' => $postData['notes'] ?? null,
            'file_path' => $filePath,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add scan_type if this is a scan report
        if ($postData['report_type'] === 'scan' && isset($postData['scan_type'])) {
            $reportData['scan_type'] = $postData['scan_type'];
        }

        // Get database instance
        $db = Database::getInstance();

        // Start transaction
        $db->beginTransaction();

        try {
            // Update report
            $updated = $this->reportModel->updateReport($id, $reportData);

            if (!$updated) {
                throw new Exception('Failed to update report');
            }

            // Process parameters if any
            if (isset($postData['parameters']) && is_array($postData['parameters'])) {
                foreach ($postData['parameters'] as $paramName => $paramData) {
                    // Skip if parameter value is empty
                    if (empty($paramData['value'])) {
                        continue;
                    }

                    // For custom parameters, use the name field if provided
                    $displayName = $paramName;
                    if (strpos($paramName, 'param_') === 0 && isset($paramData['name'])) {
                        $displayName = $paramData['name'];
                    }

                    // Determine if value is abnormal
                    $isAbnormal = 0;
                    if (
                        (!empty($paramData['min']) && $paramData['value'] < $paramData['min']) ||
                        (!empty($paramData['max']) && $paramData['value'] > $paramData['max'])
                    ) {
                        $isAbnormal = 1;
                    }

                    $parameterData = [
                        'parameter_value' => $paramData['value'],
                        'unit' => $paramData['unit'] ?? null,
                        'reference_range_min' => $paramData['min'] ?? null,
                        'reference_range_max' => $paramData['max'] ?? null,
                        'is_abnormal' => $isAbnormal,
                        'notes' => $paramData['notes'] ?? null,
                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    // Check if parameter already exists
                    if (isset($paramData['id']) && $paramData['id']) {
                        // Update existing parameter
                        $updated = $this->reportModel->updateParameter($paramData['id'], $parameterData);
                        if (!$updated) {
                            throw new Exception('Failed to update parameter: ' . $displayName);
                        }
                    } else {
                        // Add new parameter
                        $parameterData['report_id'] = $id;
                        $parameterData['parameter_name'] = $displayName;
                        $parameterData['created_at'] = date('Y-m-d H:i:s');

                        $parameterId = $this->reportModel->addParameter($parameterData);
                        if (!$parameterId) {
                            throw new Exception('Failed to add parameter: ' . $displayName);
                        }
                    }
                }
            }

            // Commit transaction
            $db->commit();

            Session::setFlash('success', 'Report updated successfully');
            $this->redirect('/medical/reports/view/' . $id);
        } catch (Exception $e) {
            // Rollback transaction
            $db->rollback();

            Session::setFlash('error', 'Error updating report: ' . $e->getMessage());
            $this->redirect('/medical/reports/edit/' . $id);
        }
    }

    /**
     * Delete a report
     */
    public function deleteReport($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get report
        $report = $this->reportModel->getReport($id);

        if (!$report || $report['user_id'] != $userId) {
            Session::setFlash('error', 'Report not found');
            $this->redirect('/medical/reports');
            return;
        }

        // Delete report
        $deleted = $this->reportModel->deleteReport($id);

        if ($deleted) {
            // Delete file if exists
            if ($report['file_path'] && file_exists(__DIR__ . '/../../public/' . $report['file_path'])) {
                unlink(__DIR__ . '/../../public/' . $report['file_path']);
            }

            Session::setFlash('success', 'Report deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete report');
        }

        $this->redirect('/medical/reports');
    }

    /**
     * View parameter history
     */
    public function parameterHistory($parameterName) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get parameter history
        $history = $this->reportModel->getParameterHistory($userId, $parameterName);

        $this->view('medical/reports/parameter_history', [
            'parameterName' => $parameterName,
            'history' => $history
        ]);
    }

    /**
     * Generate reports
     */
    public function generateReports() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Default to last 90 days if no date range specified
        if (!isset($filters['start_date'])) {
            $filters['start_date'] = date('Y-m-d', strtotime('-90 days'));
        }

        if (!isset($filters['end_date'])) {
            $filters['end_date'] = date('Y-m-d');
        }

        // Get reports for the date range
        $reports = $this->reportModel->getReportsForDateRange(
            $userId,
            $filters['start_date'],
            $filters['end_date']
        );

        $this->view('medical/reports/generate', [
            'reports' => $reports,
            'filters' => $filters
        ]);
    }

    /**
     * Medication Tracking Section
     */

    /**
     * Show medications list
     */
    public function medications() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all medications
        $medications = $this->medicationModel->getUserMedications($userId);

        // Get today's medication logs
        $todayLogs = $this->medicationModel->getTodayLogs($userId);

        // Get adherence rates
        $adherenceRates = [];
        foreach ($medications as $medication) {
            $adherenceRates[$medication['id']] = $this->medicationModel->getAdherenceRate($medication['id']);
        }

        $this->view('medical/medication/index', [
            'medications' => $medications,
            'todayLogs' => $todayLogs,
            'adherenceRates' => $adherenceRates
        ]);
    }

    /**
     * Show new medication form
     */
    public function newMedication() {
        $this->requireLogin();

        $this->view('medical/medication/medication_form');
    }

    /**
     * Save a new medication
     */
    public function saveMedication() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['name', 'dosage', 'dosage_unit', 'frequency'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/medication/new');
        }

        // Add user ID and timestamps
        $medicationData = [
            'user_id' => $userId,
            'name' => $postData['name'],
            'dosage' => $postData['dosage'],
            'dosage_unit' => $postData['dosage_unit'],
            'frequency' => $postData['frequency'],
            'instructions' => !empty($postData['instructions']) ? $postData['instructions'] : null,
            'prescriber' => !empty($postData['prescriber']) ? $postData['prescriber'] : null,
            'pharmacy' => !empty($postData['pharmacy']) ? $postData['pharmacy'] : null,
            'start_date' => !empty($postData['start_date']) ? $postData['start_date'] : date('Y-m-d'),
            'end_date' => !empty($postData['end_date']) ? $postData['end_date'] : null,
            'notes' => !empty($postData['notes']) ? $postData['notes'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->medicationModel->createMedication($medicationData);

        if ($result) {
            Session::setFlash('success', 'Medication added successfully');
            $this->redirect('/medical/medication');
        } else {
            Session::setFlash('error', 'Failed to add medication');
            $this->redirect('/medical/medication/new');
        }
    }

    /**
     * View medication details
     */
    public function viewMedication($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get medication
        $medication = $this->medicationModel->getMedication($id);

        // Verify medication exists and belongs to user
        if (!$medication || $medication['user_id'] != $userId) {
            Session::setFlash('error', 'Medication not found');
            $this->redirect('/medical/medication');
        }

        // Get medication logs
        $logs = $this->medicationModel->getMedicationLogsByMedication($id, 30);

        // Get medication reminders
        $reminders = $this->medicationModel->getMedicationRemindersByMedication($id);

        // Get adherence rate
        $adherenceRate = $this->medicationModel->getAdherenceRate($id);

        // Get effectiveness data for chart
        $effectivenessData = $this->medicationModel->getEffectivenessData($id, 30);

        $this->view('medical/medication/medication_view', [
            'medication' => $medication,
            'logs' => $logs,
            'reminders' => $reminders,
            'adherenceRate' => $adherenceRate,
            'effectivenessData' => $effectivenessData
        ]);
    }

    /**
     * Show edit medication form
     */
    public function editMedication($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get medication
        $medication = $this->medicationModel->getMedication($id);

        // Verify medication exists and belongs to user
        if (!$medication || $medication['user_id'] != $userId) {
            Session::setFlash('error', 'Medication not found');
            $this->redirect('/medical/medication');
        }

        $this->view('medical/medication/medication_edit', [
            'medication' => $medication
        ]);
    }

    /**
     * Update medication
     */
    public function updateMedication($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get medication
        $medication = $this->medicationModel->getMedication($id);

        // Verify medication exists and belongs to user
        if (!$medication || $medication['user_id'] != $userId) {
            Session::setFlash('error', 'Medication not found');
            $this->redirect('/medical/medication');
        }

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['name', 'dosage', 'dosage_unit', 'frequency'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/medication/edit/' . $id);
        }

        // Update medication data
        $medicationData = [
            'name' => $postData['name'],
            'dosage' => $postData['dosage'],
            'dosage_unit' => $postData['dosage_unit'],
            'frequency' => $postData['frequency'],
            'instructions' => !empty($postData['instructions']) ? $postData['instructions'] : null,
            'prescriber' => !empty($postData['prescriber']) ? $postData['prescriber'] : null,
            'pharmacy' => !empty($postData['pharmacy']) ? $postData['pharmacy'] : null,
            'start_date' => !empty($postData['start_date']) ? $postData['start_date'] : date('Y-m-d'),
            'end_date' => !empty($postData['end_date']) ? $postData['end_date'] : null,
            'notes' => !empty($postData['notes']) ? $postData['notes'] : null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->medicationModel->updateMedication($id, $medicationData);

        if ($result) {
            Session::setFlash('success', 'Medication updated successfully');
            $this->redirect('/medical/medication/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update medication');
            $this->redirect('/medical/medication/edit/' . $id);
        }
    }

    /**
     * Delete medication
     */
    public function deleteMedication($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get medication
        $medication = $this->medicationModel->getMedication($id);

        // Verify medication exists and belongs to user
        if (!$medication || $medication['user_id'] != $userId) {
            Session::setFlash('error', 'Medication not found');
            $this->redirect('/medical/medication');
        }

        $result = $this->medicationModel->deleteMedication($id);

        if ($result) {
            Session::setFlash('success', 'Medication deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete medication');
        }

        $this->redirect('/medical/medication');
    }

    /**
     * Show medication log form
     */
    public function logMedication() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all medications
        $medications = $this->medicationModel->getUserMedications($userId);

        $this->view('medical/medication/log_form', [
            'medications' => $medications
        ]);
    }

    /**
     * Show medication log form for a specific medication
     */
    public function logMedicationById($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Verify medication exists and belongs to user
        $medication = $this->medicationModel->getMedication($id);

        if (!$medication || $medication['user_id'] != $userId) {
            Session::setFlash('error', 'Medication not found');
            $this->redirect('/medical/medication');
        }

        // Get all medications for dropdown
        $medications = $this->medicationModel->getUserMedications($userId);

        $this->view('medical/medication/log_form', [
            'medications' => $medications,
            'selectedMedication' => $medication
        ]);
    }

    /**
     * Save medication log
     */
    public function saveMedicationLog() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['medication_id', 'log_date', 'log_time', 'taken'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/medication/log');
        }

        // Verify medication belongs to user
        $medication = $this->medicationModel->getMedication($postData['medication_id']);

        if (!$medication || $medication['user_id'] != $userId) {
            Session::setFlash('error', 'Medication not found');
            $this->redirect('/medical/medication/log');
        }

        // Add log data
        $logData = [
            'medication_id' => $postData['medication_id'],
            'log_date' => $postData['log_date'],
            'log_time' => $postData['log_time'],
            'taken' => $postData['taken'],
            'actual_dosage' => !empty($postData['actual_dosage']) ? $postData['actual_dosage'] : null,
            'effectiveness_rating' => !empty($postData['effectiveness_rating']) ? $postData['effectiveness_rating'] : null,
            'side_effects' => !empty($postData['side_effects']) ? $postData['side_effects'] : null,
            'side_effects_severity' => !empty($postData['side_effects_severity']) ? $postData['side_effects_severity'] : null,
            'notes' => !empty($postData['notes']) ? $postData['notes'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->medicationModel->logMedication($logData);

        if ($result) {
            Session::setFlash('success', 'Medication log saved successfully');
            $this->redirect('/medical/medication/view/' . $postData['medication_id']);
        } else {
            Session::setFlash('error', 'Failed to save medication log');
            $this->redirect('/medical/medication/log');
        }
    }

    /**
     * Show edit medication log form
     */
    public function editMedicationLog($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the log
        $log = $this->medicationModel->getMedicationLog($id);

        if (!$log) {
            Session::setFlash('error', 'Medication log not found');
            $this->redirect('/medical/medication');
        }

        // Get the medication to verify ownership
        $medication = $this->medicationModel->getMedication($log['medication_id']);

        if (!$medication || $medication['user_id'] != $userId) {
            Session::setFlash('error', 'Medication not found');
            $this->redirect('/medical/medication');
        }

        // Get all medications for dropdown
        $medications = $this->medicationModel->getUserMedications($userId);

        $this->view('medical/medication/log_edit', [
            'log' => $log,
            'medication' => $medication,
            'medications' => $medications
        ]);
    }

    /**
     * Update medication log
     */
    public function updateMedicationLog($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the log
        $log = $this->medicationModel->getMedicationLog($id);

        if (!$log) {
            Session::setFlash('error', 'Medication log not found');
            $this->redirect('/medical/medication');
        }

        // Get the medication to verify ownership
        $medication = $this->medicationModel->getMedication($log['medication_id']);

        if (!$medication || $medication['user_id'] != $userId) {
            Session::setFlash('error', 'Medication not found');
            $this->redirect('/medical/medication');
        }

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['medication_id', 'log_date', 'log_time', 'taken'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/medication/log/edit/' . $id);
        }

        // Verify new medication belongs to user if changed
        if ($postData['medication_id'] != $log['medication_id']) {
            $newMedication = $this->medicationModel->getMedication($postData['medication_id']);
            if (!$newMedication || $newMedication['user_id'] != $userId) {
                Session::setFlash('error', 'Medication not found');
                $this->redirect('/medical/medication/log/edit/' . $id);
            }
        }

        // Update log data
        $logData = [
            'medication_id' => $postData['medication_id'],
            'log_date' => $postData['log_date'],
            'log_time' => $postData['log_time'],
            'taken' => $postData['taken'],
            'actual_dosage' => !empty($postData['actual_dosage']) ? $postData['actual_dosage'] : null,
            'effectiveness_rating' => !empty($postData['effectiveness_rating']) ? $postData['effectiveness_rating'] : null,
            'side_effects' => !empty($postData['side_effects']) ? $postData['side_effects'] : null,
            'side_effects_severity' => !empty($postData['side_effects_severity']) ? $postData['side_effects_severity'] : null,
            'notes' => !empty($postData['notes']) ? $postData['notes'] : null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->medicationModel->updateMedicationLog($id, $logData);

        if ($result) {
            Session::setFlash('success', 'Medication log updated successfully');
            $this->redirect('/medical/medication/view/' . $postData['medication_id']);
        } else {
            Session::setFlash('error', 'Failed to update medication log');
            $this->redirect('/medical/medication/log/edit/' . $id);
        }
    }

    /**
     * Delete medication log
     */
    public function deleteMedicationLog($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the log
        $log = $this->medicationModel->getMedicationLog($id);

        if (!$log) {
            Session::setFlash('error', 'Medication log not found');
            $this->redirect('/medical/medication');
        }

        // Get the medication to verify ownership
        $medication = $this->medicationModel->getMedication($log['medication_id']);

        if (!$medication || $medication['user_id'] != $userId) {
            Session::setFlash('error', 'Medication not found');
            $this->redirect('/medical/medication');
        }

        $result = $this->medicationModel->deleteMedicationLog($id);

        if ($result) {
            Session::setFlash('success', 'Medication log deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete medication log');
        }

        $this->redirect('/medical/medication/view/' . $medication['id']);
    }

    /**
     * Show new medication reminder form
     */
    public function newMedicationReminder($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Verify medication exists and belongs to user
        $medication = $this->medicationModel->getMedication($id);

        if (!$medication || $medication['user_id'] != $userId) {
            Session::setFlash('error', 'Medication not found');
            $this->redirect('/medical/medication');
        }

        $this->view('medical/medication/reminder_form', [
            'medication' => $medication
        ]);
    }

    /**
     * Save medication reminder
     */
    public function saveMedicationReminder() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['medication_id', 'reminder_time'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/medication/reminder/new/' . $postData['medication_id']);
        }

        // Verify medication belongs to user
        $medication = $this->medicationModel->getMedication($postData['medication_id']);

        if (!$medication || $medication['user_id'] != $userId) {
            Session::setFlash('error', 'Medication not found');
            $this->redirect('/medical/medication');
        }

        // Add reminder data
        $reminderData = [
            'medication_id' => $postData['medication_id'],
            'reminder_time' => $postData['reminder_time'],
            'days_of_week' => isset($postData['days_of_week']) ? implode(',', $postData['days_of_week']) : null,
            'active' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->medicationModel->createReminder($reminderData);

        if ($result) {
            Session::setFlash('success', 'Reminder added successfully');
            $this->redirect('/medical/medication/view/' . $postData['medication_id']);
        } else {
            Session::setFlash('error', 'Failed to add reminder');
            $this->redirect('/medical/medication/reminder/new/' . $postData['medication_id']);
        }
    }

    /**
     * Show edit medication reminder form
     */
    public function editMedicationReminder($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the reminder
        $reminder = $this->medicationModel->getReminder($id);

        if (!$reminder) {
            Session::setFlash('error', 'Reminder not found');
            $this->redirect('/medical/medication');
        }

        // Get the medication to verify ownership
        $medication = $this->medicationModel->getMedication($reminder['medication_id']);

        if (!$medication || $medication['user_id'] != $userId) {
            Session::setFlash('error', 'Medication not found');
            $this->redirect('/medical/medication');
        }

        $this->view('medical/medication/reminder_edit', [
            'reminder' => $reminder,
            'medication' => $medication
        ]);
    }

    /**
     * Update medication reminder
     */
    public function updateMedicationReminder($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the reminder
        $reminder = $this->medicationModel->getReminder($id);

        if (!$reminder) {
            Session::setFlash('error', 'Reminder not found');
            $this->redirect('/medical/medication');
        }

        // Get the medication to verify ownership
        $medication = $this->medicationModel->getMedication($reminder['medication_id']);

        if (!$medication || $medication['user_id'] != $userId) {
            Session::setFlash('error', 'Medication not found');
            $this->redirect('/medical/medication');
        }

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['reminder_time'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/medication/reminder/edit/' . $id);
        }

        // Update reminder data
        $reminderData = [
            'reminder_time' => $postData['reminder_time'],
            'days_of_week' => isset($postData['days_of_week']) ? implode(',', $postData['days_of_week']) : null,
            'active' => isset($postData['active']) ? 1 : 0,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->medicationModel->updateReminder($id, $reminderData);

        if ($result) {
            Session::setFlash('success', 'Reminder updated successfully');
            $this->redirect('/medical/medication/view/' . $reminder['medication_id']);
        } else {
            Session::setFlash('error', 'Failed to update reminder');
            $this->redirect('/medical/medication/reminder/edit/' . $id);
        }
    }

    /**
     * Delete medication reminder
     */
    public function deleteMedicationReminder($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the reminder
        $reminder = $this->medicationModel->getReminder($id);

        if (!$reminder) {
            Session::setFlash('error', 'Reminder not found');
            $this->redirect('/medical/medication');
        }

        // Get the medication to verify ownership
        $medication = $this->medicationModel->getMedication($reminder['medication_id']);

        if (!$medication || $medication['user_id'] != $userId) {
            Session::setFlash('error', 'Medication not found');
            $this->redirect('/medical/medication');
        }

        $result = $this->medicationModel->deleteReminder($id);

        if ($result) {
            Session::setFlash('success', 'Reminder deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete reminder');
        }

        $this->redirect('/medical/medication/view/' . $reminder['medication_id']);
    }

    /**
     * Show medication reports
     */
    public function medicationReports() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Default to last 30 days if no date range specified
        if (!isset($filters['start_date'])) {
            $filters['start_date'] = date('Y-m-d', strtotime('-30 days'));
        }

        if (!isset($filters['end_date'])) {
            $filters['end_date'] = date('Y-m-d');
        }

        // Get all medications
        $medications = $this->medicationModel->getUserMedications($userId);

        // Get medication data for the date range
        $medicationData = [];
        foreach ($medications as $medication) {
            // Get logs for this medication
            $logs = $this->medicationModel->getMedicationLogsByDateRange(
                $medication['id'],
                $filters['start_date'],
                $filters['end_date']
            );

            // Get adherence rate
            $adherence = $this->medicationModel->getAdherenceRateByDateRange(
                $medication['id'],
                $filters['start_date'],
                $filters['end_date']
            );

            // Get effectiveness data
            $effectiveness = $this->medicationModel->getAverageEffectivenessByDateRange(
                $medication['id'],
                $filters['start_date'],
                $filters['end_date']
            );

            // If effectiveness is null, provide a default structure
            if (!$effectiveness) {
                $effectiveness = [
                    'avg_effectiveness' => 0,
                    'avg_side_effects' => 0
                ];
            }

            $medicationData[$medication['id']] = [
                'medication' => $medication,
                'logs' => $logs,
                'adherence' => $adherence,
                'effectiveness' => $effectiveness
            ];
        }

        $this->view('medical/medication/reports', [
            'medications' => $medications,
            'medicationData' => $medicationData,
            'filters' => $filters
        ]);
    }

    /**
     * Pet Management Section
     */

    /**
     * Show pets dashboard
     */
    public function pets() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all pets
        $pets = $this->petModel->getUserPets($userId);

        $this->view('medical/pets/index', [
            'pets' => $pets
        ]);
    }

    /**
     * Show new pet form
     */
    public function newPet() {
        $this->requireLogin();

        $this->view('medical/pets/pet_form');
    }

    /**
     * Save a new pet
     */
    public function savePet() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['name', 'species'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/new');
        }

        // Add user ID and timestamps
        $petData = [
            'user_id' => $userId,
            'name' => $postData['name'],
            'species' => $postData['species'],
            'breed' => !empty($postData['breed']) ? $postData['breed'] : null,
            'gender' => !empty($postData['gender']) ? $postData['gender'] : 'unknown',
            'birth_date' => !empty($postData['birth_date']) ? $postData['birth_date'] : null,
            'weight' => !empty($postData['weight']) ? $postData['weight'] : null,
            'weight_unit' => !empty($postData['weight_unit']) ? $postData['weight_unit'] : 'kg',
            'color' => !empty($postData['color']) ? $postData['color'] : null,
            'microchip_id' => !empty($postData['microchip_id']) ? $postData['microchip_id'] : null,
            'adoption_date' => !empty($postData['adoption_date']) ? $postData['adoption_date'] : null,
            'notes' => !empty($postData['notes']) ? $postData['notes'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->petModel->createPet($petData);

        if ($result) {
            Session::setFlash('success', 'Pet added successfully');
            $this->redirect('/medical/pets');
        } else {
            Session::setFlash('error', 'Failed to add pet');
            $this->redirect('/medical/pets/new');
        }
    }

    /**
     * View pet details
     */
    public function viewPet($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get pet
        $pet = $this->petModel->getPet($id);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Get pet medications
        $medications = $this->petModel->getPetMedications($id);

        // Get pet treatments
        $treatments = $this->petModel->getPetTreatments($id);

        // Get upcoming treatments
        $upcomingTreatments = $this->petModel->getUpcomingTreatments($id);

        // Get recent training logs
        $trainingLogs = $this->petModel->getRecentTrainingLogs($id);

        // Get recent vitals records
        $recentVitals = $this->petModel->getPetVitals($id, 1);

        // Get recent health assessment
        $recentHealthAssessment = $this->petModel->getPetHealthAssessments($id, 1);

        // Get recent vet visit
        $recentVetVisit = $this->petModel->getPetVetVisits($id, 1);

        // Get upcoming vet visits
        $upcomingVetVisits = $this->petModel->getUpcomingVetVisits($id);

        // Get upcoming reminders
        $upcomingReminders = $this->petModel->getUpcomingReminders($id, 3);

        $this->view('medical/pets/pet_view', [
            'pet' => $pet,
            'medications' => $medications,
            'treatments' => $treatments,
            'upcomingTreatments' => $upcomingTreatments,
            'trainingLogs' => $trainingLogs,
            'recentVitals' => $recentVitals,
            'recentHealthAssessment' => $recentHealthAssessment,
            'recentVetVisit' => $recentVetVisit,
            'upcomingVetVisits' => $upcomingVetVisits,
            'upcomingReminders' => $upcomingReminders
        ]);
    }

    /**
     * Show edit pet form
     */
    public function editPet($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get pet
        $pet = $this->petModel->getPet($id);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/pet_edit', [
            'pet' => $pet
        ]);
    }

    /**
     * Update pet
     */
    public function updatePet($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get pet
        $pet = $this->petModel->getPet($id);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['name', 'species'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/edit/' . $id);
        }

        // Update pet data
        $petData = [
            'name' => $postData['name'],
            'species' => $postData['species'],
            'breed' => !empty($postData['breed']) ? $postData['breed'] : null,
            'gender' => !empty($postData['gender']) ? $postData['gender'] : 'unknown',
            'birth_date' => !empty($postData['birth_date']) ? $postData['birth_date'] : null,
            'weight' => !empty($postData['weight']) ? $postData['weight'] : null,
            'weight_unit' => !empty($postData['weight_unit']) ? $postData['weight_unit'] : 'kg',
            'color' => !empty($postData['color']) ? $postData['color'] : null,
            'microchip_id' => !empty($postData['microchip_id']) ? $postData['microchip_id'] : null,
            'adoption_date' => !empty($postData['adoption_date']) ? $postData['adoption_date'] : null,
            'notes' => !empty($postData['notes']) ? $postData['notes'] : null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->petModel->updatePet($id, $petData);

        if ($result) {
            Session::setFlash('success', 'Pet updated successfully');
            $this->redirect('/medical/pets/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update pet');
            $this->redirect('/medical/pets/edit/' . $id);
        }
    }

    /**
     * Delete pet
     */
    public function deletePet($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get pet
        $pet = $this->petModel->getPet($id);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $result = $this->petModel->deletePet($id);

        if ($result) {
            Session::setFlash('success', 'Pet deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete pet');
        }

        $this->redirect('/medical/pets');
    }

    /**
     * Pet Medication Management
     */

    /**
     * Show new pet medication form
     */
    public function newPetMedication($petId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Verify pet exists and belongs to user
        $pet = $this->petModel->getPet($petId);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/medication_form', [
            'pet' => $pet
        ]);
    }

    /**
     * Save a new pet medication
     */
    public function savePetMedication() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['pet_id', 'name', 'dosage', 'dosage_unit', 'frequency'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/medication/new/' . $postData['pet_id']);
        }

        // Verify pet belongs to user
        $pet = $this->petModel->getPet($postData['pet_id']);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Add medication data
        $medicationData = [
            'pet_id' => $postData['pet_id'],
            'name' => $postData['name'],
            'dosage' => $postData['dosage'],
            'dosage_unit' => $postData['dosage_unit'],
            'frequency' => $postData['frequency'],
            'instructions' => !empty($postData['instructions']) ? $postData['instructions'] : null,
            'prescriber' => !empty($postData['prescriber']) ? $postData['prescriber'] : null,
            'start_date' => !empty($postData['start_date']) ? $postData['start_date'] : date('Y-m-d'),
            'end_date' => !empty($postData['end_date']) ? $postData['end_date'] : null,
            'notes' => !empty($postData['notes']) ? $postData['notes'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->petModel->createMedication($medicationData);

        if ($result) {
            Session::setFlash('success', 'Medication added successfully');
            $this->redirect('/medical/pets/view/' . $postData['pet_id']);
        } else {
            Session::setFlash('error', 'Failed to add medication');
            $this->redirect('/medical/pets/medication/new/' . $postData['pet_id']);
        }
    }

    /**
     * Pet Treatment Management
     */

    /**
     * Show new pet treatment form
     */
    public function newPetTreatment($petId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Verify pet exists and belongs to user
        $pet = $this->petModel->getPet($petId);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/treatment_form', [
            'pet' => $pet
        ]);
    }

    /**
     * Save a new pet treatment
     */
    public function savePetTreatment() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['pet_id', 'treatment_type', 'treatment_name', 'treatment_date'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/treatment/new/' . $postData['pet_id']);
        }

        // Verify pet belongs to user
        $pet = $this->petModel->getPet($postData['pet_id']);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Add treatment data
        $treatmentData = [
            'pet_id' => $postData['pet_id'],
            'treatment_type' => $postData['treatment_type'],
            'treatment_name' => $postData['treatment_name'],
            'treatment_date' => $postData['treatment_date'],
            'provider' => !empty($postData['provider']) ? $postData['provider'] : null,
            'cost' => !empty($postData['cost']) ? $postData['cost'] : null,
            'next_due_date' => !empty($postData['next_due_date']) ? $postData['next_due_date'] : null,
            'notes' => !empty($postData['notes']) ? $postData['notes'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->petModel->createTreatment($treatmentData);

        if ($result) {
            Session::setFlash('success', 'Treatment added successfully');
            $this->redirect('/medical/pets/view/' . $postData['pet_id']);
        } else {
            Session::setFlash('error', 'Failed to add treatment');
            $this->redirect('/medical/pets/treatment/new/' . $postData['pet_id']);
        }
    }

    /**
     * Pet Training Management
     */

    /**
     * Show new pet training log form
     */
    public function newPetTraining($petId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Verify pet exists and belongs to user
        $pet = $this->petModel->getPet($petId);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/training_form', [
            'pet' => $pet
        ]);
    }

    /**
     * Save a new pet training log
     */
    public function savePetTraining() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['pet_id', 'training_date', 'skill_name'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/training/new/' . $postData['pet_id']);
        }

        // Verify pet belongs to user
        $pet = $this->petModel->getPet($postData['pet_id']);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Add training log data
        $trainingData = [
            'pet_id' => $postData['pet_id'],
            'training_date' => $postData['training_date'],
            'skill_name' => $postData['skill_name'],
            'duration' => !empty($postData['duration']) ? $postData['duration'] : null,
            'progress_rating' => !empty($postData['progress_rating']) ? $postData['progress_rating'] : null,
            'notes' => !empty($postData['notes']) ? $postData['notes'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->petModel->createTrainingLog($trainingData);

        if ($result) {
            Session::setFlash('success', 'Training log added successfully');
            $this->redirect('/medical/pets/view/' . $postData['pet_id']);
        } else {
            Session::setFlash('error', 'Failed to add training log');
            $this->redirect('/medical/pets/training/new/' . $postData['pet_id']);
        }
    }

    /**
     * Pet Vitals Management
     */

    /**
     * Show new pet vitals form
     */
    public function newPetVitals($petId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Verify pet exists and belongs to user
        $pet = $this->petModel->getPet($petId);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/vitals_form', [
            'pet' => $pet
        ]);
    }

    /**
     * Save a new pet vitals record
     */
    public function savePetVitals() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['pet_id', 'record_date'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/vitals/new/' . $postData['pet_id']);
        }

        // Verify pet belongs to user
        $pet = $this->petModel->getPet($postData['pet_id']);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Add vitals data
        $vitalsData = [
            'pet_id' => $postData['pet_id'],
            'record_date' => $postData['record_date'],
            'weight' => !empty($postData['weight']) ? $postData['weight'] : null,
            'temperature' => !empty($postData['temperature']) ? $postData['temperature'] : null,
            'heart_rate' => !empty($postData['heart_rate']) ? $postData['heart_rate'] : null,
            'respiratory_rate' => !empty($postData['respiratory_rate']) ? $postData['respiratory_rate'] : null,
            'notes' => !empty($postData['notes']) ? $postData['notes'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->petModel->createVitalRecord($vitalsData);

        // If weight was provided, update the pet's current weight
        if (!empty($postData['weight']) && $result) {
            $petUpdateData = [
                'weight' => $postData['weight'],
                'weight_unit' => !empty($postData['weight_unit']) ? $postData['weight_unit'] : $pet['weight_unit'],
                'updated_at' => date('Y-m-d H:i:s')
            ];
            $this->petModel->updatePet($postData['pet_id'], $petUpdateData);
        }

        if ($result) {
            Session::setFlash('success', 'Vitals record added successfully');
            $this->redirect('/medical/pets/view/' . $postData['pet_id']);
        } else {
            Session::setFlash('error', 'Failed to add vitals record');
            $this->redirect('/medical/pets/vitals/new/' . $postData['pet_id']);
        }
    }

    /**
     * View pet health dashboard
     */
    public function viewPetHealthDashboard($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get pet
        $pet = $this->petModel->getPet($id);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Get pet medications with compliance data
        $medications = $this->petModel->getPetMedications($id);
        foreach ($medications as &$medication) {
            // Calculate medication compliance
            $logs = $this->petModel->getMedicationLogs($medication['id']);
            $totalDoses = 0;
            $takenDoses = 0;

            // Simple compliance calculation based on logs vs expected doses
            // This is a simplified version - in a real app, you'd calculate based on schedule
            if (!empty($logs)) {
                $totalDoses = count($logs);
                foreach ($logs as $log) {
                    if ($log['status'] === 'taken') {
                        $takenDoses++;
                    }
                }
            }

            $medication['compliance'] = $totalDoses > 0 ? round(($takenDoses / $totalDoses) * 100) : 0;
        }

        // Get weight history for chart
        $weightHistory = $this->petModel->getPetWeightHistory($id);
        $weightChartData = [
            'labels' => [],
            'values' => []
        ];

        // Current and target weight
        $currentWeight = [
            'value' => !empty($weightHistory) ? end($weightHistory)['weight'] : 0,
            'unit' => !empty($weightHistory) ? end($weightHistory)['weight_unit'] : 'kg'
        ];

        // Target weight (this would come from a health plan in a real app)
        $targetWeight = [
            'value' => !empty($pet['target_weight']) ? $pet['target_weight'] : $currentWeight['value'],
            'unit' => $currentWeight['unit']
        ];

        // Weight change calculation
        $weightChange = [
            'value' => 0,
            'class' => 'text-gray-900 dark:text-white'
        ];

        if (count($weightHistory) >= 2) {
            $firstWeight = $weightHistory[0]['weight'];
            $lastWeight = end($weightHistory)['weight'];
            $weightChange['value'] = round($lastWeight - $firstWeight, 1);

            if ($weightChange['value'] > 0) {
                $weightChange['class'] = 'text-green-600 dark:text-green-400';
                $weightChange['value'] = '+' . $weightChange['value'];
            } elseif ($weightChange['value'] < 0) {
                $weightChange['class'] = 'text-red-600 dark:text-red-400';
            }
        }

        // Format weight history for chart
        foreach ($weightHistory as $record) {
            $weightChartData['labels'][] = date('M j', strtotime($record['record_date']));
            $weightChartData['values'][] = $record['weight'];
        }

        // Get recent health assessment
        $recentHealthAssessment = $this->petModel->getPetHealthAssessments($id, 1);
        $recentHealthAssessment = !empty($recentHealthAssessment) ? $recentHealthAssessment[0] : null;

        // Get upcoming vet visits
        $upcomingVetVisits = $this->petModel->getUpcomingVetVisits($id);

        // Get treatments and vaccinations
        $treatments = $this->petModel->getPetTreatments($id);

        // Calculate comprehensive health score
        $healthScore = $this->petModel->calculateHealthScore($id);

        // Adjust health score based on available data
        if ($recentHealthAssessment) {
            switch ($recentHealthAssessment['overall_condition']) {
                case 'Excellent':
                    $healthScore['value'] = 95;
                    $healthScore['description'] = 'Your pet is in excellent health!';
                    break;
                case 'Good':
                    $healthScore['value'] = 85;
                    $healthScore['description'] = 'Your pet is in good health.';
                    break;
                case 'Fair':
                    $healthScore['value'] = 70;
                    $healthScore['class'] = 'text-yellow-600 dark:text-yellow-400';
                    $healthScore['description'] = 'Your pet\'s health is fair. Some improvements needed.';
                    break;
                case 'Poor':
                    $healthScore['value'] = 50;
                    $healthScore['class'] = 'text-red-600 dark:text-red-400';
                    $healthScore['description'] = 'Your pet\'s health needs attention.';
                    break;
                default:
                    // Keep defaults
            }
        }

        // Set health status based on health score
        $healthStatus = [
            'label' => 'Good',
            'class' => 'status-upcoming'
        ];

        if ($healthScore['value'] >= 90) {
            $healthStatus['label'] = 'Excellent';
            $healthStatus['class'] = 'status-completed';
        } elseif ($healthScore['value'] >= 75) {
            $healthStatus['label'] = 'Good';
            $healthStatus['class'] = 'status-upcoming';
        } elseif ($healthScore['value'] >= 60) {
            $healthStatus['label'] = 'Fair';
            $healthStatus['class'] = 'status-due-today';
        } else {
            $healthStatus['label'] = 'Needs Attention';
            $healthStatus['class'] = 'status-overdue';
        }

        // Compile upcoming care items (combining reminders, medications, vet visits)
        $upcomingCare = [];

        // Add upcoming reminders
        $upcomingReminders = $this->petModel->getUpcomingReminders($id);
        foreach ($upcomingReminders as $reminder) {
            $upcomingCare[] = [
                'type' => $reminder['reminder_type'],
                'title' => $reminder['title'],
                'due_date' => $reminder['due_date'],
                'action_url' => "/momentum/medical/pets/reminder/complete/{$reminder['id']}",
                'action_text' => 'Complete'
            ];
        }

        // Add upcoming vet visits
        foreach ($upcomingVetVisits as $visit) {
            $upcomingCare[] = [
                'type' => 'vet_visit',
                'title' => $visit['follow_up_reason'] ?: 'Vet Visit',
                'due_date' => $visit['follow_up_date'],
                'action_url' => "/momentum/medical/pets/vet-visit/view/{$visit['id']}",
                'action_text' => 'View Details'
            ];
        }

        // Sort upcoming care by due date
        usort($upcomingCare, function($a, $b) {
            return strtotime($a['due_date']) - strtotime($b['due_date']);
        });

        $this->view('medical/pets/health_dashboard', [
            'pet' => $pet,
            'medications' => $medications,
            'weightHistory' => $weightHistory,
            'weightChartData' => $weightChartData,
            'currentWeight' => $currentWeight,
            'targetWeight' => $targetWeight,
            'weightChange' => $weightChange,
            'recentHealthAssessment' => $recentHealthAssessment,
            'upcomingVetVisits' => $upcomingVetVisits,
            'treatments' => $treatments,
            'healthScore' => $healthScore,
            'healthStatus' => $healthStatus,
            'upcomingCare' => $upcomingCare
        ]);
    }

    /**
     * Pet Growth Tracking
     */

    /**
     * Display pet growth tracking dashboard
     */
    public function viewPetGrowthDashboard($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get pet details
        $pet = $this->petModel->getPet($id);
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Get growth metrics
        $growthMetrics = $this->petModel->getPetGrowthMetrics($id);

        // Get weight history
        $weightHistory = $this->petModel->getPetWeightHistory($id);

        // Get height history
        $heightHistory = $this->petModel->getPetHeightHistory($id);

        // Get length history
        $lengthHistory = $this->petModel->getPetLengthHistory($id);

        // Get chest girth history
        $chestGirthHistory = $this->petModel->getPetChestGirthHistory($id);

        // Get current measurements
        $currentWeight = !empty($weightHistory) ? end($weightHistory) : null;
        $currentHeight = !empty($heightHistory) ? end($heightHistory) : null;
        $currentLength = !empty($lengthHistory) ? end($lengthHistory) : null;
        $currentChestGirth = !empty($chestGirthHistory) ? end($chestGirthHistory) : null;

        // Calculate weight change
        $weightChange = null;
        if (count($weightHistory) >= 2) {
            $oldestWeight = $weightHistory[0]['weight'];
            $latestWeight = end($weightHistory)['weight'];
            $oldestDate = new DateTime($weightHistory[0]['record_date']);
            $latestDate = new DateTime(end($weightHistory)['record_date']);
            $daysDiff = $oldestDate->diff($latestDate)->days;

            if ($daysDiff > 0) {
                $weightChange = [
                    'amount' => $latestWeight - $oldestWeight,
                    'direction' => $latestWeight >= $oldestWeight ? 'increase' : 'decrease',
                    'period' => $daysDiff
                ];
            }
        }

        // Calculate growth rates
        $weightGrowthRate = $this->petModel->calculateGrowthRate($id, 'weight', 6);
        $heightGrowthRate = $this->petModel->calculateGrowthRate($id, 'height', 6);
        $lengthGrowthRate = $this->petModel->calculateGrowthRate($id, 'length', 6);
        $chestGirthGrowthRate = $this->petModel->calculateGrowthRate($id, 'chest_girth', 6);

        // Get growth milestones
        $milestones = $this->petModel->getPetGrowthMilestones($id);

        $this->view('medical/pets/growth_dashboard', [
            'pet' => $pet,
            'growthMetrics' => $growthMetrics,
            'weightHistory' => $weightHistory,
            'heightHistory' => $heightHistory,
            'lengthHistory' => $lengthHistory,
            'chestGirthHistory' => $chestGirthHistory,
            'currentWeight' => $currentWeight,
            'currentHeight' => $currentHeight,
            'currentLength' => $currentLength,
            'currentChestGirth' => $currentChestGirth,
            'weightChange' => $weightChange,
            'weightGrowthRate' => $weightGrowthRate,
            'heightGrowthRate' => $heightGrowthRate,
            'lengthGrowthRate' => $lengthGrowthRate,
            'chestGirthGrowthRate' => $chestGirthGrowthRate,
            'milestones' => $milestones
        ]);
    }

    /**
     * Display form to record pet growth
     */
    public function recordPetGrowth($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get pet details
        $pet = $this->petModel->getPet($id);
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/growth_form', [
            'pet' => $pet
        ]);
    }

    /**
     * Save pet growth metrics
     */
    public function savePetGrowth() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['pet_id', 'record_date'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/growth/record/' . $postData['pet_id']);
        }

        // Verify pet belongs to user
        $pet = $this->petModel->getPet($postData['pet_id']);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Prepare data
        $data = [
            'pet_id' => $postData['pet_id'],
            'record_date' => $postData['record_date'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add optional fields if provided
        if (!empty($postData['weight'])) {
            $data['weight'] = $postData['weight'];
            $data['weight_unit'] = $postData['weight_unit'] ?? 'kg';
        }

        if (!empty($postData['height'])) {
            $data['height'] = $postData['height'];
            $data['height_unit'] = $postData['height_unit'] ?? 'cm';
        }

        if (!empty($postData['length'])) {
            $data['length'] = $postData['length'];
            $data['length_unit'] = $postData['length_unit'] ?? 'cm';
        }

        if (!empty($postData['chest_girth'])) {
            $data['chest_girth'] = $postData['chest_girth'];
            $data['chest_girth_unit'] = $postData['chest_girth_unit'] ?? 'cm';
        }

        if (!empty($postData['neck_size'])) {
            $data['neck_size'] = $postData['neck_size'];
            $data['neck_size_unit'] = $postData['neck_size_unit'] ?? 'cm';
        }

        if (!empty($postData['notes'])) {
            $data['notes'] = $postData['notes'];
        }

        // Save growth metrics
        $result = $this->petModel->createGrowthMetric($data);

        // Update pet weight if provided
        if (!empty($postData['weight'])) {
            $this->petModel->updatePet($postData['pet_id'], [
                'weight' => $postData['weight'],
                'weight_unit' => $postData['weight_unit'] ?? 'kg',
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }

        if ($result) {
            Session::setFlash('success', 'Growth metrics recorded successfully');
        } else {
            Session::setFlash('error', 'Failed to record growth metrics');
        }

        $this->redirect('/medical/pets/growth/' . $postData['pet_id']);
    }

    /**
     * Display form to edit pet growth metrics
     */
    public function editPetGrowth($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get growth metric details
        $growthMetric = $this->petModel->getGrowthMetric($id);
        if (!$growthMetric) {
            Session::setFlash('error', 'Growth metric not found');
            $this->redirect('/medical/pets');
        }

        // Get pet details
        $pet = $this->petModel->getPet($growthMetric['pet_id']);
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/growth_form', [
            'pet' => $pet,
            'growthMetric' => $growthMetric
        ]);
    }

    /**
     * Update pet growth metrics
     */
    public function updatePetGrowth() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['id', 'pet_id', 'record_date'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/growth/edit/' . $postData['id']);
        }

        // Verify pet belongs to user
        $pet = $this->petModel->getPet($postData['pet_id']);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Prepare data
        $data = [
            'record_date' => $postData['record_date'],
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add optional fields if provided
        if (isset($postData['weight'])) {
            $data['weight'] = $postData['weight'] !== '' ? $postData['weight'] : null;
            $data['weight_unit'] = $postData['weight_unit'] ?? 'kg';
        }

        if (isset($postData['height'])) {
            $data['height'] = $postData['height'] !== '' ? $postData['height'] : null;
            $data['height_unit'] = $postData['height_unit'] ?? 'cm';
        }

        if (isset($postData['length'])) {
            $data['length'] = $postData['length'] !== '' ? $postData['length'] : null;
            $data['length_unit'] = $postData['length_unit'] ?? 'cm';
        }

        if (isset($postData['chest_girth'])) {
            $data['chest_girth'] = $postData['chest_girth'] !== '' ? $postData['chest_girth'] : null;
            $data['chest_girth_unit'] = $postData['chest_girth_unit'] ?? 'cm';
        }

        if (isset($postData['neck_size'])) {
            $data['neck_size'] = $postData['neck_size'] !== '' ? $postData['neck_size'] : null;
            $data['neck_size_unit'] = $postData['neck_size_unit'] ?? 'cm';
        }

        if (isset($postData['notes'])) {
            $data['notes'] = $postData['notes'];
        }

        // Update growth metrics
        $result = $this->petModel->updateGrowthMetric($postData['id'], $data);

        // Update pet weight if provided and it's the most recent record
        if (isset($postData['weight']) && $postData['weight'] !== '') {
            $latestGrowthMetric = $this->petModel->getPetGrowthMetrics($postData['pet_id'], 1);
            if (!empty($latestGrowthMetric) && $latestGrowthMetric[0]['id'] == $postData['id']) {
                $this->petModel->updatePet($postData['pet_id'], [
                    'weight' => $postData['weight'],
                    'weight_unit' => $postData['weight_unit'] ?? 'kg',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        }

        if ($result) {
            Session::setFlash('success', 'Growth metrics updated successfully');
        } else {
            Session::setFlash('error', 'Failed to update growth metrics');
        }

        $this->redirect('/medical/pets/growth/' . $postData['pet_id']);
    }

    /**
     * Delete pet growth metrics
     */
    public function deletePetGrowth($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get growth metric details
        $growthMetric = $this->petModel->getGrowthMetric($id);
        if (!$growthMetric) {
            Session::setFlash('error', 'Growth metric not found');
            $this->redirect('/medical/pets');
        }

        // Get pet details
        $pet = $this->petModel->getPet($growthMetric['pet_id']);
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Delete growth metric
        $result = $this->petModel->deleteGrowthMetric($id);

        if ($result) {
            Session::setFlash('success', 'Growth metric deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete growth metric');
        }

        $this->redirect('/medical/pets/growth/' . $growthMetric['pet_id']);
    }

    /**
     * Display form to add pet growth milestone
     */
    public function newPetGrowthMilestone($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get pet details
        $pet = $this->petModel->getPet($id);
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/growth_milestone_form', [
            'pet' => $pet
        ]);
    }

    /**
     * Save pet growth milestone
     */
    public function savePetGrowthMilestone() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['pet_id', 'milestone_date', 'milestone_type', 'milestone_name'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/growth/milestone/new/' . $postData['pet_id']);
        }

        // Verify pet belongs to user
        $pet = $this->petModel->getPet($postData['pet_id']);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Prepare data
        $data = [
            'pet_id' => $postData['pet_id'],
            'milestone_date' => $postData['milestone_date'],
            'milestone_type' => $postData['milestone_type'],
            'milestone_name' => $postData['milestone_name'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add optional fields if provided
        if (!empty($postData['milestone_value'])) {
            $data['milestone_value'] = $postData['milestone_value'];
        }

        if (!empty($postData['milestone_unit'])) {
            $data['milestone_unit'] = $postData['milestone_unit'];
        }

        if (!empty($postData['notes'])) {
            $data['notes'] = $postData['notes'];
        }

        // Save growth milestone
        $result = $this->petModel->createGrowthMilestone($data);

        if ($result) {
            Session::setFlash('success', 'Growth milestone recorded successfully');
        } else {
            Session::setFlash('error', 'Failed to record growth milestone');
        }

        $this->redirect('/medical/pets/growth/' . $postData['pet_id']);
    }

    /**
     * Display form to edit pet growth milestone
     */
    public function editPetGrowthMilestone($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get milestone details
        $milestone = $this->petModel->getGrowthMilestone($id);
        if (!$milestone) {
            Session::setFlash('error', 'Growth milestone not found');
            $this->redirect('/medical/pets');
        }

        // Get pet details
        $pet = $this->petModel->getPet($milestone['pet_id']);
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/growth_milestone_form', [
            'pet' => $pet,
            'milestone' => $milestone
        ]);
    }

    /**
     * Update pet growth milestone
     */
    public function updatePetGrowthMilestone() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['id', 'pet_id', 'milestone_date', 'milestone_type', 'milestone_name'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/growth/milestone/edit/' . $postData['id']);
        }

        // Verify pet belongs to user
        $pet = $this->petModel->getPet($postData['pet_id']);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Prepare data
        $data = [
            'milestone_date' => $postData['milestone_date'],
            'milestone_type' => $postData['milestone_type'],
            'milestone_name' => $postData['milestone_name'],
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add optional fields if provided
        if (isset($postData['milestone_value'])) {
            $data['milestone_value'] = $postData['milestone_value'] !== '' ? $postData['milestone_value'] : null;
        }

        if (isset($postData['milestone_unit'])) {
            $data['milestone_unit'] = $postData['milestone_unit'];
        }

        if (isset($postData['notes'])) {
            $data['notes'] = $postData['notes'];
        }

        // Update growth milestone
        $result = $this->petModel->updateGrowthMilestone($postData['id'], $data);

        if ($result) {
            Session::setFlash('success', 'Growth milestone updated successfully');
        } else {
            Session::setFlash('error', 'Failed to update growth milestone');
        }

        $this->redirect('/medical/pets/growth/' . $postData['pet_id']);
    }

    /**
     * Delete pet growth milestone
     */
    public function deletePetGrowthMilestone($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get milestone details
        $milestone = $this->petModel->getGrowthMilestone($id);
        if (!$milestone) {
            Session::setFlash('error', 'Growth milestone not found');
            $this->redirect('/medical/pets');
        }

        // Get pet details
        $pet = $this->petModel->getPet($milestone['pet_id']);
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Delete milestone
        $result = $this->petModel->deleteGrowthMilestone($id);

        if ($result) {
            Session::setFlash('success', 'Growth milestone deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete growth milestone');
        }

        $this->redirect('/medical/pets/growth/' . $milestone['pet_id']);
    }

    /**
     * View pet vitals history
     */
    public function viewPetVitals($petId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get pet
        $pet = $this->petModel->getPet($petId);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Get pet vitals records
        $vitals = $this->petModel->getPetVitals($petId);

        // Get weight history for chart
        $weightHistory = $this->petModel->getPetWeightHistory($petId);

        $this->view('medical/pets/vitals_view', [
            'pet' => $pet,
            'vitals' => $vitals,
            'weightHistory' => $weightHistory
        ]);
    }

    /**
     * Pet Health Assessment Management
     */

    /**
     * Show new pet health assessment form
     */
    public function newPetHealthAssessment($petId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Verify pet exists and belongs to user
        $pet = $this->petModel->getPet($petId);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/health_assessment_form', [
            'pet' => $pet
        ]);
    }

    /**
     * Save a new pet health assessment
     */
    public function savePetHealthAssessment() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['pet_id', 'assessment_date', 'overall_health'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/health-assessment/new/' . $postData['pet_id']);
        }

        // Verify pet belongs to user
        $pet = $this->petModel->getPet($postData['pet_id']);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Add health assessment data
        $assessmentData = [
            'pet_id' => $postData['pet_id'],
            'assessment_date' => $postData['assessment_date'],
            'overall_health' => $postData['overall_health'],
            'coat_condition' => !empty($postData['coat_condition']) ? $postData['coat_condition'] : null,
            'skin_condition' => !empty($postData['skin_condition']) ? $postData['skin_condition'] : null,
            'eye_condition' => !empty($postData['eye_condition']) ? $postData['eye_condition'] : null,
            'ear_condition' => !empty($postData['ear_condition']) ? $postData['ear_condition'] : null,
            'dental_health' => !empty($postData['dental_health']) ? $postData['dental_health'] : null,
            'nail_condition' => !empty($postData['nail_condition']) ? $postData['nail_condition'] : null,
            'energy_level' => !empty($postData['energy_level']) ? $postData['energy_level'] : null,
            'appetite' => !empty($postData['appetite']) ? $postData['appetite'] : null,
            'hydration' => !empty($postData['hydration']) ? $postData['hydration'] : null,
            'mobility' => !empty($postData['mobility']) ? $postData['mobility'] : null,
            'notes' => !empty($postData['notes']) ? $postData['notes'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->petModel->createHealthAssessment($assessmentData);

        if ($result) {
            Session::setFlash('success', 'Health assessment added successfully');
            $this->redirect('/medical/pets/view/' . $postData['pet_id']);
        } else {
            Session::setFlash('error', 'Failed to add health assessment');
            $this->redirect('/medical/pets/health-assessment/new/' . $postData['pet_id']);
        }
    }

    /**
     * View pet health assessments
     */
    public function viewPetHealthAssessments($petId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get pet
        $pet = $this->petModel->getPet($petId);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Get pet health assessments
        $healthAssessments = $this->petModel->getPetHealthAssessments($petId);

        $this->view('medical/pets/health_assessments_view', [
            'pet' => $pet,
            'healthAssessments' => $healthAssessments
        ]);
    }

    /**
     * Pet Vet Visit Management
     */

    /**
     * Show new pet vet visit form
     */
    public function newPetVetVisit($petId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Verify pet exists and belongs to user
        $pet = $this->petModel->getPet($petId);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/vet_visit_form', [
            'pet' => $pet
        ]);
    }

    /**
     * Save a new pet vet visit
     */
    public function savePetVetVisit() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['pet_id', 'visit_date', 'visit_type'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/vet-visit/new/' . $postData['pet_id']);
        }

        // Verify pet belongs to user
        $pet = $this->petModel->getPet($postData['pet_id']);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Add vet visit data
        $vetVisitData = [
            'pet_id' => $postData['pet_id'],
            'visit_date' => $postData['visit_date'],
            'visit_time' => !empty($postData['visit_time']) ? $postData['visit_time'] : null,
            'vet_name' => !empty($postData['vet_name']) ? $postData['vet_name'] : null,
            'clinic_name' => !empty($postData['clinic_name']) ? $postData['clinic_name'] : null,
            'visit_reason' => !empty($postData['reason']) ? $postData['reason'] : $postData['visit_type'],
            'diagnosis' => !empty($postData['diagnosis']) ? $postData['diagnosis'] : null,
            'treatment' => !empty($postData['treatment']) ? $postData['treatment'] : null,
            'follow_up_date' => !empty($postData['follow_up_date']) ? $postData['follow_up_date'] : null,
            'cost' => !empty($postData['cost']) ? $postData['cost'] : null,
            'notes' => !empty($postData['notes']) ? $postData['notes'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->petModel->createVetVisit($vetVisitData);

        if ($result) {
            Session::setFlash('success', 'Vet visit recorded successfully');
            $this->redirect('/medical/pets/view/' . $postData['pet_id']);
        } else {
            Session::setFlash('error', 'Failed to record vet visit');
            $this->redirect('/medical/pets/vet-visit/new/' . $postData['pet_id']);
        }
    }

    /**
     * View pet vet visits
     */
    public function viewPetVetVisits($petId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get pet
        $pet = $this->petModel->getPet($petId);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Get pet vet visits
        $vetVisits = $this->petModel->getPetVetVisits($petId);

        // Get upcoming vet visits
        $upcomingVisits = $this->petModel->getUpcomingVetVisits($petId);

        $this->view('medical/pets/vet_visits_view', [
            'pet' => $pet,
            'vetVisits' => $vetVisits,
            'upcomingVisits' => $upcomingVisits
        ]);
    }

    /**
     * View a single vet visit
     */
    public function viewPetVetVisit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get vet visit
        $vetVisit = $this->petModel->getVetVisit($id);

        if (!$vetVisit) {
            Session::setFlash('error', 'Vet visit not found');
            $this->redirect('/medical/pets');
        }

        // Get pet
        $pet = $this->petModel->getPet($vetVisit['pet_id']);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/vet_visit_view', [
            'pet' => $pet,
            'vetVisit' => $vetVisit
        ]);
    }

    /**
     * Show edit pet vet visit form
     */
    public function editPetVetVisit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get vet visit
        $vetVisit = $this->petModel->getVetVisit($id);

        if (!$vetVisit) {
            Session::setFlash('error', 'Vet visit not found');
            $this->redirect('/medical/pets');
        }

        // Get pet
        $pet = $this->petModel->getPet($vetVisit['pet_id']);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/vet_visit_edit_form', [
            'pet' => $pet,
            'vetVisit' => $vetVisit
        ]);
    }

    /**
     * Update a pet vet visit
     */
    public function updatePetVetVisit() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['id', 'pet_id', 'visit_date', 'visit_reason'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/vet-visit/edit/' . $postData['id']);
        }

        // Get vet visit
        $vetVisit = $this->petModel->getVetVisit($postData['id']);

        if (!$vetVisit) {
            Session::setFlash('error', 'Vet visit not found');
            $this->redirect('/medical/pets');
        }

        // Verify pet belongs to user
        $pet = $this->petModel->getPet($postData['pet_id']);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Update vet visit data
        $vetVisitData = [
            'visit_date' => $postData['visit_date'],
            'visit_time' => !empty($postData['visit_time']) ? $postData['visit_time'] : null,
            'vet_name' => !empty($postData['vet_name']) ? $postData['vet_name'] : null,
            'clinic_name' => !empty($postData['clinic_name']) ? $postData['clinic_name'] : null,
            'visit_reason' => $postData['visit_reason'],
            'diagnosis' => !empty($postData['diagnosis']) ? $postData['diagnosis'] : null,
            'treatment' => !empty($postData['treatment']) ? $postData['treatment'] : null,
            'follow_up_date' => !empty($postData['follow_up_date']) ? $postData['follow_up_date'] : null,
            'cost' => !empty($postData['cost']) ? $postData['cost'] : null,
            'notes' => !empty($postData['notes']) ? $postData['notes'] : null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->petModel->updateVetVisit($postData['id'], $vetVisitData);

        if ($result) {
            Session::setFlash('success', 'Vet visit updated successfully');
            $this->redirect('/medical/pets/vet-visits/' . $postData['pet_id']);
        } else {
            Session::setFlash('error', 'Failed to update vet visit');
            $this->redirect('/medical/pets/vet-visit/edit/' . $postData['id']);
        }
    }

    /**
     * Delete a pet vet visit
     */
    public function deletePetVetVisit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get vet visit
        $vetVisit = $this->petModel->getVetVisit($id);

        if (!$vetVisit) {
            Session::setFlash('error', 'Vet visit not found');
            $this->redirect('/medical/pets');
        }

        // Get pet
        $pet = $this->petModel->getPet($vetVisit['pet_id']);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $result = $this->petModel->deleteVetVisit($id);

        if ($result) {
            Session::setFlash('success', 'Vet visit deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete vet visit');
        }

        $this->redirect('/medical/pets/vet-visits/' . $vetVisit['pet_id']);
    }

    /**
     * Pet Reminder Management
     */

    /**
     * Show new pet reminder form
     */
    public function newPetReminder($petId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Verify pet exists and belongs to user
        $pet = $this->petModel->getPet($petId);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/reminder_form', [
            'pet' => $pet
        ]);
    }

    /**
     * Save a new pet reminder
     */
    public function savePetReminder() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['pet_id', 'title', 'reminder_type', 'due_date'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/reminder/new/' . $postData['pet_id']);
        }

        // Verify pet belongs to user
        $pet = $this->petModel->getPet($postData['pet_id']);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Add reminder data
        $reminderData = [
            'pet_id' => $postData['pet_id'],
            'title' => $postData['title'],
            'description' => !empty($postData['description']) ? $postData['description'] : null,
            'reminder_type' => $postData['reminder_type'],
            'priority' => !empty($postData['priority']) ? $postData['priority'] : 'normal',
            'due_date' => $postData['due_date'],
            'due_time' => !empty($postData['due_time']) ? $postData['due_time'] : null,
            'recurrence_type' => !empty($postData['recurrence_type']) ? $postData['recurrence_type'] : 'none',
            'recurrence_interval' => !empty($postData['recurrence_interval']) ? $postData['recurrence_interval'] : null,
            'recurrence_end_date' => !empty($postData['recurrence_end_date']) ? $postData['recurrence_end_date'] : null,
            'notification_type' => !empty($postData['notification_type']) ? $postData['notification_type'] : 'app',
            'notification_time' => !empty($postData['notification_time']) ? $postData['notification_time'] : 0,
            'is_completed' => 0,
            'completed_at' => null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->petModel->createReminder($reminderData);

        if ($result) {
            Session::setFlash('success', 'Reminder created successfully');
            $this->redirect('/medical/pets/view/' . $postData['pet_id']);
        } else {
            Session::setFlash('error', 'Failed to create reminder');
            $this->redirect('/medical/pets/reminder/new/' . $postData['pet_id']);
        }
    }

    /**
     * View pet reminders
     */
    public function viewPetReminders($petId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get pet
        $pet = $this->petModel->getPet($petId);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Get upcoming reminders
        $upcomingReminders = $this->petModel->getUpcomingReminders($petId);

        // Get completed reminders
        $completedReminders = $this->petModel->getCompletedReminders($petId);

        $this->view('medical/pets/reminders_view', [
            'pet' => $pet,
            'upcomingReminders' => $upcomingReminders,
            'completedReminders' => $completedReminders
        ]);
    }

    /**
     * View pet vaccination schedule
     */
    public function viewPetVaccinationSchedule($petId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get pet
        $pet = $this->petModel->getPet($petId);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Get vaccination history (treatments with type 'vaccination')
        $vaccinationHistory = $this->petModel->getPetTreatmentsByType($petId, 'vaccination');

        // Get upcoming vaccinations
        $upcomingVaccinations = $this->petModel->getUpcomingTreatmentsByType($petId, 'vaccination');

        // Calculate vaccination statistics
        $vaccinationStats = [
            'total' => count($vaccinationHistory),
            'up_to_date' => 0,
            'due_soon' => 0
        ];

        // Determine vaccination status
        $vaccinationStatus = [
            'label' => 'Unknown',
            'class' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
        ];

        // Count up-to-date and due soon vaccinations
        foreach ($vaccinationHistory as $vaccination) {
            if (empty($vaccination['next_due_date'])) {
                // One-time vaccination with no next due date
                $vaccinationStats['up_to_date']++;
            } else {
                $nextDueDate = new DateTime($vaccination['next_due_date']);
                $today = new DateTime();

                if ($nextDueDate > $today) {
                    $vaccinationStats['up_to_date']++;
                } else {
                    $vaccinationStats['due_soon']++;
                }
            }
        }

        // Set vaccination status based on statistics
        if ($vaccinationStats['total'] === 0) {
            $vaccinationStatus = [
                'label' => 'No Vaccinations',
                'class' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
            ];
        } else if ($vaccinationStats['due_soon'] > 0) {
            $vaccinationStatus = [
                'label' => 'Vaccinations Due',
                'class' => 'bg-orange-100 text-orange-800 dark:bg-orange-700 dark:text-orange-300'
            ];
        } else {
            $vaccinationStatus = [
                'label' => 'Up to Date',
                'class' => 'bg-green-100 text-green-800 dark:bg-green-700 dark:text-green-300'
            ];
        }

        $this->view('medical/pets/vaccination_schedule', [
            'pet' => $pet,
            'vaccinationHistory' => $vaccinationHistory,
            'upcomingVaccinations' => $upcomingVaccinations,
            'vaccinationStats' => $vaccinationStats,
            'vaccinationStatus' => $vaccinationStatus
        ]);
    }

    /**
     * Show edit pet reminder form
     */
    public function editPetReminder($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get reminder
        $reminder = $this->petModel->getReminder($id);

        if (!$reminder) {
            Session::setFlash('error', 'Reminder not found');
            $this->redirect('/medical/pets');
        }

        // Get pet
        $pet = $this->petModel->getPet($reminder['pet_id']);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $this->view('medical/pets/reminder_edit_form', [
            'pet' => $pet,
            'reminder' => $reminder
        ]);
    }

    /**
     * Update a pet reminder
     */
    public function updatePetReminder() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['id', 'pet_id', 'title', 'reminder_type', 'due_date'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/medical/pets/reminder/edit/' . $postData['id']);
        }

        // Get reminder
        $reminder = $this->petModel->getReminder($postData['id']);

        if (!$reminder) {
            Session::setFlash('error', 'Reminder not found');
            $this->redirect('/medical/pets');
        }

        // Verify pet belongs to user
        $pet = $this->petModel->getPet($postData['pet_id']);

        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Update reminder data
        $reminderData = [
            'title' => $postData['title'],
            'description' => !empty($postData['description']) ? $postData['description'] : null,
            'reminder_type' => $postData['reminder_type'],
            'priority' => !empty($postData['priority']) ? $postData['priority'] : 'normal',
            'due_date' => $postData['due_date'],
            'due_time' => !empty($postData['due_time']) ? $postData['due_time'] : null,
            'recurrence_type' => !empty($postData['recurrence_type']) ? $postData['recurrence_type'] : 'none',
            'recurrence_interval' => !empty($postData['recurrence_interval']) ? $postData['recurrence_interval'] : null,
            'recurrence_end_date' => !empty($postData['recurrence_end_date']) ? $postData['recurrence_end_date'] : null,
            'notification_type' => !empty($postData['notification_type']) ? $postData['notification_type'] : 'app',
            'notification_time' => !empty($postData['notification_time']) ? $postData['notification_time'] : 0,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->petModel->updateReminder($postData['id'], $reminderData);

        if ($result) {
            Session::setFlash('success', 'Reminder updated successfully');
            $this->redirect('/medical/pets/reminders/' . $postData['pet_id']);
        } else {
            Session::setFlash('error', 'Failed to update reminder');
            $this->redirect('/medical/pets/reminder/edit/' . $postData['id']);
        }
    }

    /**
     * Complete a pet reminder
     */
    public function completePetReminder($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get reminder
        $reminder = $this->petModel->getReminder($id);

        if (!$reminder) {
            Session::setFlash('error', 'Reminder not found');
            $this->redirect('/medical/pets');
        }

        // Get pet
        $pet = $this->petModel->getPet($reminder['pet_id']);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        // Update reminder data
        $reminderData = [
            'is_completed' => 1,
            'completed_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // If this is a recurring reminder, create the next occurrence
        if ($reminder['recurrence_type'] !== 'none') {
            $this->createNextRecurringReminder($reminder);
        }

        $result = $this->petModel->updateReminder($id, $reminderData);

        if ($result) {
            Session::setFlash('success', 'Reminder marked as completed');
        } else {
            Session::setFlash('error', 'Failed to complete reminder');
        }

        $this->redirect('/medical/pets/reminders/' . $reminder['pet_id']);
    }

    /**
     * Delete a pet reminder
     */
    public function deletePetReminder($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get reminder
        $reminder = $this->petModel->getReminder($id);

        if (!$reminder) {
            Session::setFlash('error', 'Reminder not found');
            $this->redirect('/medical/pets');
        }

        // Get pet
        $pet = $this->petModel->getPet($reminder['pet_id']);

        // Verify pet exists and belongs to user
        if (!$pet || $pet['user_id'] != $userId) {
            Session::setFlash('error', 'Pet not found');
            $this->redirect('/medical/pets');
        }

        $result = $this->petModel->deleteReminder($id);

        if ($result) {
            Session::setFlash('success', 'Reminder deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete reminder');
        }

        $this->redirect('/medical/pets/reminders/' . $reminder['pet_id']);
    }

    /**
     * Create the next occurrence of a recurring reminder
     */
    private function createNextRecurringReminder($reminder) {
        // Calculate the next due date based on recurrence type
        $nextDueDate = null;
        $dueDate = new DateTime($reminder['due_date']);

        switch ($reminder['recurrence_type']) {
            case 'daily':
                $interval = !empty($reminder['recurrence_interval']) ? $reminder['recurrence_interval'] : 1;
                $nextDueDate = $dueDate->modify("+{$interval} days");
                break;
            case 'weekly':
                $interval = !empty($reminder['recurrence_interval']) ? $reminder['recurrence_interval'] : 1;
                $nextDueDate = $dueDate->modify("+{$interval} weeks");
                break;
            case 'monthly':
                $interval = !empty($reminder['recurrence_interval']) ? $reminder['recurrence_interval'] : 1;
                $nextDueDate = $dueDate->modify("+{$interval} months");
                break;
            case 'yearly':
                $interval = !empty($reminder['recurrence_interval']) ? $reminder['recurrence_interval'] : 1;
                $nextDueDate = $dueDate->modify("+{$interval} years");
                break;
            case 'custom':
                $interval = !empty($reminder['recurrence_interval']) ? $reminder['recurrence_interval'] : 1;
                $nextDueDate = $dueDate->modify("+{$interval} days");
                break;
            default:
                return; // Not a recurring reminder
        }

        // Check if we've reached the end date
        if (!empty($reminder['recurrence_end_date'])) {
            $endDate = new DateTime($reminder['recurrence_end_date']);
            if ($nextDueDate > $endDate) {
                return; // We've reached the end date, don't create a new reminder
            }
        }

        // Create a new reminder for the next occurrence
        $newReminderData = [
            'pet_id' => $reminder['pet_id'],
            'title' => $reminder['title'],
            'description' => $reminder['description'],
            'reminder_type' => $reminder['reminder_type'],
            'priority' => $reminder['priority'],
            'due_date' => $nextDueDate->format('Y-m-d'),
            'due_time' => $reminder['due_time'],
            'recurrence_type' => $reminder['recurrence_type'],
            'recurrence_interval' => $reminder['recurrence_interval'],
            'recurrence_end_date' => $reminder['recurrence_end_date'],
            'notification_type' => $reminder['notification_type'],
            'notification_time' => $reminder['notification_time'],
            'is_completed' => 0,
            'completed_at' => null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $this->petModel->createReminder($newReminderData);
    }
}
