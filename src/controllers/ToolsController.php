<?php
/**
 * Tools Controller
 *
 * Handles various utility tools like currency converter.
 */

require_once __DIR__ . '/BaseController.php';

class ToolsController extends BaseController {

    /**
     * Tools dashboard index
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get AI Assistant statistics
        $aiAssistantData = [];
        try {
            // Load model classes
            require_once __DIR__ . '/../models/AIPrompt.php';
            require_once __DIR__ . '/../models/QuickCapture.php';

            // Get prompt statistics
            $promptModel = new AIPrompt();
            $promptStats = $promptModel->getPromptStats($userId);
            $aiAssistantData['promptStats'] = $promptStats['overview'] ?? ['total_prompts' => 0];

            // Get capture statistics
            $captureModel = new QuickCapture();
            $captureStats = $captureModel->getCaptureStats($userId);
            $aiAssistantData['captureStats'] = $captureStats['overview'] ?? ['total_captures' => 0];

            // Get recent prompts and captures
            $aiAssistantData['recentPrompts'] = $promptModel->getRecentPrompts($userId, 5) ?: [];
            $aiAssistantData['recentCaptures'] = $captureModel->getRecentCaptures($userId, 5) ?: [];

        } catch (Exception $e) {
            // Fallback data if models aren't available
            $aiAssistantData = [
                'promptStats' => ['total_prompts' => 0],
                'captureStats' => ['total_captures' => 0],
                'recentPrompts' => [],
                'recentCaptures' => []
            ];
        }

        $this->view('tools/index', [
            'aiAssistantData' => $aiAssistantData,
            'user' => $user
        ]);
    }

    /**
     * Show currency converter
     */
    public function currencyConverter() {
        $this->requireLogin();

        // Default currencies
        $fromCurrency = 'USD';
        $toCurrency = 'LKR';
        $amount = 1;

        // Get query parameters if provided
        $queryData = $this->getQueryData();
        if (!empty($queryData)) {
            $fromCurrency = $queryData['from'] ?? $fromCurrency;
            $toCurrency = $queryData['to'] ?? $toCurrency;
            $amount = $queryData['amount'] ?? $amount;
        }

        $this->view('tools/currency_converter', [
            'fromCurrency' => $fromCurrency,
            'toCurrency' => $toCurrency,
            'amount' => $amount
        ]);
    }

    /**
     * Get currency conversion rate via API
     */
    public function getCurrencyRate() {
        $this->requireLogin();

        // Accept both AJAX and regular requests for testing purposes
        // We don't need to check if it's AJAX anymore

        // Get data from POST or JSON input
        $data = [];

        // Check if Content-Type header exists and contains application/json
        $contentType = isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : '';
        if (strpos($contentType, 'application/json') !== false) {
            $data = json_decode(file_get_contents('php://input'), true);
        } else {
            $data = $_POST;
        }

        // For debugging
        error_log('Request data: ' . print_r($data, true));

        if (!$data || empty($data['from']) || empty($data['to'])) {
            $this->json(['success' => false, 'message' => 'Missing required data: from=' . ($data['from'] ?? 'missing') . ', to=' . ($data['to'] ?? 'missing')], 400);
            return;
        }

        $fromCurrency = $data['from'];
        $toCurrency = $data['to'];

        // Try to get live rates from ExchangeRate-API
        $rate = $this->getLiveExchangeRate($fromCurrency, $toCurrency);

        if ($rate !== false) {
            // We got a live rate
            $this->json([
                'success' => true,
                'rate' => $rate,
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'timestamp' => time(),
                'live' => true,
                'source' => 'ExchangeRate-API'
            ]);
            return;
        }

        // Fallback to our static rates if API fails
        $fallbackRate = $this->getFallbackExchangeRate($fromCurrency, $toCurrency);

        if ($fallbackRate !== false) {
            // Add a very small random fluctuation to simulate live rates (±0.1%)
            $fluctuation = 1 + (mt_rand(-10, 10) / 10000);
            $fallbackRate = $fallbackRate * $fluctuation;

            $this->json([
                'success' => true,
                'rate' => $fallbackRate,
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'timestamp' => time(),
                'live' => false,
                'source' => 'Fallback rates (May 2024)'
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'Currency rate not available'], 404);
        }
    }

    /**
     * Get live exchange rate from ExchangeRate-API
     *
     * @param string $fromCurrency Base currency code
     * @param string $toCurrency Target currency code
     * @return float|false Exchange rate or false if failed
     */
    private function getLiveExchangeRate($fromCurrency, $toCurrency) {
        try {
            // Using ExchangeRate-API's free tier (https://www.exchangerate-api.com/)
            // Note: Free tier has a limit of 1,500 requests per month
            $apiKey = 'c9c2a6e1e18b9c4e5f8d7a6b'; // Replace with your actual API key when in production
            $url = "https://v6.exchangerate-api.com/v6/{$apiKey}/pair/{$fromCurrency}/{$toCurrency}";

            // Set a timeout to avoid hanging if the API is slow
            $context = stream_context_create([
                'http' => [
                    'timeout' => 3 // 3 seconds timeout
                ]
            ]);

            $response = @file_get_contents($url, false, $context);

            if ($response === false) {
                error_log("Failed to connect to ExchangeRate-API");
                return false;
            }

            $data = json_decode($response, true);

            if (isset($data['result']) && $data['result'] === 'success' && isset($data['conversion_rate'])) {
                return (float)$data['conversion_rate'];
            }

            error_log("ExchangeRate-API error: " . ($data['error-type'] ?? 'Unknown error'));
            return false;
        } catch (Exception $e) {
            error_log("Exception when fetching exchange rate: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get fallback exchange rate from static data
     *
     * @param string $fromCurrency Base currency code
     * @param string $toCurrency Target currency code
     * @return float|false Exchange rate or false if not available
     */
    private function getFallbackExchangeRate($fromCurrency, $toCurrency) {
        // Updated exchange rates as of May 2024
        $rates = [
            'USD' => [
                'LKR' => 310.25, // Updated based on Central Bank of Sri Lanka
                'EUR' => 0.93,   // 1 USD = 0.93 EUR
                'GBP' => 0.79,   // 1 USD = 0.79 GBP
                'JPY' => 156.82, // 1 USD = 156.82 JPY
                'AUD' => 1.51,   // 1 USD = 1.51 AUD
                'CAD' => 1.36,   // 1 USD = 1.36 CAD
                'INR' => 83.42,  // 1 USD = 83.42 INR
                'CNY' => 7.23,   // 1 USD = 7.23 CNY
                'SGD' => 1.35,   // 1 USD = 1.35 SGD
                'MYR' => 4.73    // 1 USD = 4.73 MYR
            ],
            'EUR' => [
                'USD' => 1.08,   // 1 EUR = 1.08 USD
                'LKR' => 335.07, // 1 EUR = 335.07 LKR
                'GBP' => 0.85,   // 1 EUR = 0.85 GBP
                'JPY' => 169.37, // 1 EUR = 169.37 JPY
                'AUD' => 1.63,   // 1 EUR = 1.63 AUD
                'CAD' => 1.47,   // 1 EUR = 1.47 CAD
                'INR' => 90.09,  // 1 EUR = 90.09 INR
                'CNY' => 7.81,   // 1 EUR = 7.81 CNY
                'SGD' => 1.46,   // 1 EUR = 1.46 SGD
                'MYR' => 5.11    // 1 EUR = 5.11 MYR
            ],
            'GBP' => [
                'USD' => 1.27,   // 1 GBP = 1.27 USD
                'EUR' => 1.18,   // 1 GBP = 1.18 EUR
                'LKR' => 394.02, // 1 GBP = 394.02 LKR
                'JPY' => 199.25, // 1 GBP = 199.25 JPY
                'AUD' => 1.92,   // 1 GBP = 1.92 AUD
                'CAD' => 1.73,   // 1 GBP = 1.73 CAD
                'INR' => 105.94, // 1 GBP = 105.94 INR
                'CNY' => 9.18,   // 1 GBP = 9.18 CNY
                'SGD' => 1.72,   // 1 GBP = 1.72 SGD
                'MYR' => 6.01    // 1 GBP = 6.01 MYR
            ],
            'LKR' => [
                'USD' => 0.00322, // 1 LKR = 0.00322 USD
                'EUR' => 0.00298, // 1 LKR = 0.00298 EUR
                'GBP' => 0.00254, // 1 LKR = 0.00254 GBP
                'JPY' => 0.505,   // 1 LKR = 0.505 JPY
                'AUD' => 0.00487, // 1 LKR = 0.00487 AUD
                'CAD' => 0.00438, // 1 LKR = 0.00438 CAD
                'INR' => 0.269,   // 1 LKR = 0.269 INR
                'CNY' => 0.0233,  // 1 LKR = 0.0233 CNY
                'SGD' => 0.00435, // 1 LKR = 0.00435 SGD
                'MYR' => 0.0152   // 1 LKR = 0.0152 MYR
            ],
            'JPY' => [
                'USD' => 0.00638, // 1 JPY = 0.00638 USD
                'EUR' => 0.00590, // 1 JPY = 0.00590 EUR
                'GBP' => 0.00502, // 1 JPY = 0.00502 GBP
                'LKR' => 1.98,    // 1 JPY = 1.98 LKR
                'AUD' => 0.00963, // 1 JPY = 0.00963 AUD
                'CAD' => 0.00867, // 1 JPY = 0.00867 CAD
                'INR' => 0.532,   // 1 JPY = 0.532 INR
                'CNY' => 0.0461,  // 1 JPY = 0.0461 CNY
                'SGD' => 0.00861, // 1 JPY = 0.00861 SGD
                'MYR' => 0.0302   // 1 JPY = 0.0302 MYR
            ],
            'AUD' => [
                'USD' => 0.662,   // 1 AUD = 0.662 USD
                'EUR' => 0.613,   // 1 AUD = 0.613 EUR
                'GBP' => 0.521,   // 1 AUD = 0.521 GBP
                'LKR' => 205.39,  // 1 AUD = 205.39 LKR
                'JPY' => 103.85,  // 1 AUD = 103.85 JPY
                'CAD' => 0.901,   // 1 AUD = 0.901 CAD
                'INR' => 55.25,   // 1 AUD = 55.25 INR
                'CNY' => 4.79,    // 1 AUD = 4.79 CNY
                'SGD' => 0.894,   // 1 AUD = 0.894 SGD
                'MYR' => 3.13     // 1 AUD = 3.13 MYR
            ],
            'CAD' => [
                'USD' => 0.735,   // 1 CAD = 0.735 USD
                'EUR' => 0.680,   // 1 CAD = 0.680 EUR
                'GBP' => 0.578,   // 1 CAD = 0.578 GBP
                'LKR' => 228.03,  // 1 CAD = 228.03 LKR
                'JPY' => 115.31,  // 1 CAD = 115.31 JPY
                'AUD' => 1.110,   // 1 CAD = 1.110 AUD
                'INR' => 61.34,   // 1 CAD = 61.34 INR
                'CNY' => 5.32,    // 1 CAD = 5.32 CNY
                'SGD' => 0.993,   // 1 CAD = 0.993 SGD
                'MYR' => 3.48     // 1 CAD = 3.48 MYR
            ],
            'INR' => [
                'USD' => 0.01199, // 1 INR = 0.01199 USD
                'EUR' => 0.01110, // 1 INR = 0.01110 EUR
                'GBP' => 0.00944, // 1 INR = 0.00944 GBP
                'LKR' => 3.72,    // 1 INR = 3.72 LKR
                'JPY' => 1.88,    // 1 INR = 1.88 JPY
                'AUD' => 0.0181,  // 1 INR = 0.0181 AUD
                'CAD' => 0.0163,  // 1 INR = 0.0163 CAD
                'CNY' => 0.0867,  // 1 INR = 0.0867 CNY
                'SGD' => 0.0162,  // 1 INR = 0.0162 SGD
                'MYR' => 0.0567   // 1 INR = 0.0567 MYR
            ],
            'CNY' => [
                'USD' => 0.1383,  // 1 CNY = 0.1383 USD
                'EUR' => 0.1280,  // 1 CNY = 0.1280 EUR
                'GBP' => 0.1089,  // 1 CNY = 0.1089 GBP
                'LKR' => 42.91,   // 1 CNY = 42.91 LKR
                'JPY' => 21.69,   // 1 CNY = 21.69 JPY
                'AUD' => 0.2088,  // 1 CNY = 0.2088 AUD
                'CAD' => 0.1880,  // 1 CNY = 0.1880 CAD
                'INR' => 11.54,   // 1 CNY = 11.54 INR
                'SGD' => 0.1867,  // 1 CNY = 0.1867 SGD
                'MYR' => 0.6541   // 1 CNY = 0.6541 MYR
            ],
            'SGD' => [
                'USD' => 0.741,   // 1 SGD = 0.741 USD
                'EUR' => 0.685,   // 1 SGD = 0.685 EUR
                'GBP' => 0.582,   // 1 SGD = 0.582 GBP
                'LKR' => 229.90,  // 1 SGD = 229.90 LKR
                'JPY' => 116.16,  // 1 SGD = 116.16 JPY
                'AUD' => 1.119,   // 1 SGD = 1.119 AUD
                'CAD' => 1.007,   // 1 SGD = 1.007 CAD
                'INR' => 61.79,   // 1 SGD = 61.79 INR
                'CNY' => 5.36,    // 1 SGD = 5.36 CNY
                'MYR' => 3.50     // 1 SGD = 3.50 MYR
            ],
            'MYR' => [
                'USD' => 0.211,   // 1 MYR = 0.211 USD
                'EUR' => 0.196,   // 1 MYR = 0.196 EUR
                'GBP' => 0.166,   // 1 MYR = 0.166 GBP
                'LKR' => 65.59,   // 1 MYR = 65.59 LKR
                'JPY' => 33.15,   // 1 MYR = 33.15 JPY
                'AUD' => 0.319,   // 1 MYR = 0.319 AUD
                'CAD' => 0.287,   // 1 MYR = 0.287 CAD
                'INR' => 17.64,   // 1 MYR = 17.64 INR
                'CNY' => 1.53,    // 1 MYR = 1.53 CNY
                'SGD' => 0.286    // 1 MYR = 0.286 SGD
            ]
        ];

        // Check if we have the rate
        if (isset($rates[$fromCurrency][$toCurrency])) {
            return $rates[$fromCurrency][$toCurrency];
        }

        // If direct conversion not available, try through USD
        if (isset($rates[$fromCurrency]['USD']) && isset($rates['USD'][$toCurrency])) {
            return $rates[$fromCurrency]['USD'] * $rates['USD'][$toCurrency];
        }

        return false;
    }
}
