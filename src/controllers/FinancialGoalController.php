<?php
/**
 * Financial Goal Controller
 *
 * Handles financial goals-related functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/FinancialGoal.php';
require_once __DIR__ . '/../models/Finance.php';
require_once __DIR__ . '/../utils/Session.php';

class FinancialGoalController extends BaseController {
    private $goalModel;
    private $financeModel;

    public function __construct() {
        $this->goalModel = new FinancialGoal();
        $this->financeModel = new Finance();
    }

    /**
     * Show financial goals dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get goals based on filters
        $goals = $this->goalModel->getUserGoals($userId, $filters);

        // Get goals summary
        $summary = $this->goalModel->getGoalsSummary($userId);

        // Get unique categories for filter dropdown
        $categories = $this->goalModel->getUniqueCategories($userId);

        $this->view('finances/goals/index', [
            'goals' => $goals,
            'summary' => $summary,
            'filters' => $filters,
            'categories' => $categories
        ]);
    }

    /**
     * Show goal creation form
     */
    public function create() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get expense categories for dropdown
        $categories = $this->financeModel->getUniqueCategories($userId);

        $this->view('finances/goals/create', [
            'categories' => $categories
        ]);
    }

    /**
     * Process goal creation
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['name', 'target_amount', 'start_date', 'target_date']);

        if (!empty($errors)) {
            $categories = $this->financeModel->getUniqueCategories($userId);
            $this->view('finances/goals/create', [
                'errors' => $errors,
                'data' => $data,
                'categories' => $categories
            ]);
            return;
        }

        // Validate amount
        if (!is_numeric($data['target_amount']) || $data['target_amount'] <= 0) {
            $errors['target_amount'] = 'Target amount must be a positive number';
            $categories = $this->financeModel->getUniqueCategories($userId);
            $this->view('finances/goals/create', [
                'errors' => $errors,
                'data' => $data,
                'categories' => $categories
            ]);
            return;
        }

        // Validate dates
        if (strtotime($data['start_date']) > strtotime($data['target_date'])) {
            $errors['target_date'] = 'Target date must be after start date';
            $categories = $this->financeModel->getUniqueCategories($userId);
            $this->view('finances/goals/create', [
                'errors' => $errors,
                'data' => $data,
                'categories' => $categories
            ]);
            return;
        }

        // Prepare goal data
        $goalData = [
            'user_id' => $userId,
            'name' => $data['name'],
            'target_amount' => $data['target_amount'],
            'current_amount' => $data['current_amount'] ?? 0,
            'start_date' => $data['start_date'],
            'target_date' => $data['target_date'],
            'category' => $data['category'] ?? null,
            'priority' => $data['priority'] ?? 'medium',
            'status' => 'active',
            'description' => $data['description'] ?? null,
            'icon' => $data['icon'] ?? 'fa-piggy-bank',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create goal
        $goalId = $this->goalModel->create($goalData);

        if ($goalId) {
            // Create milestones if provided
            if (isset($data['milestone_names']) && is_array($data['milestone_names'])) {
                foreach ($data['milestone_names'] as $index => $name) {
                    if (!empty($name) && isset($data['milestone_amounts'][$index]) && is_numeric($data['milestone_amounts'][$index])) {
                        $milestoneData = [
                            'goal_id' => $goalId,
                            'name' => $name,
                            'target_amount' => $data['milestone_amounts'][$index],
                            'target_date' => $data['milestone_dates'][$index] ?? null,
                            'is_reached' => false,
                            'notes' => $data['milestone_notes'][$index] ?? null,
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ];
                        $this->goalModel->addMilestone($milestoneData);
                    }
                }
            }

            Session::setFlash('success', 'Financial goal created successfully');
            $this->redirect('/finances/goals');
        } else {
            Session::setFlash('error', 'Failed to create financial goal');
            $categories = $this->financeModel->getUniqueCategories($userId);
            $this->view('finances/goals/create', [
                'errors' => ['general' => 'Failed to create goal. Please try again.'],
                'data' => $data,
                'categories' => $categories
            ]);
        }
    }

    /**
     * Show goal details
     */
    public function viewGoal($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get goal details
        $goal = $this->goalModel->getGoalDetails($id);

        // Check if goal exists and belongs to user
        if (!$goal || $goal['user_id'] != $userId) {
            Session::setFlash('error', 'Goal not found');
            $this->redirect('/finances/goals');
            return;
        }

        $this->view('finances/goals/view', [
            'goal' => $goal
        ]);
    }

    /**
     * Show goal edit form
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get goal
        $goal = $this->goalModel->getGoalDetails($id);

        // Check if goal exists and belongs to user
        if (!$goal || $goal['user_id'] != $userId) {
            Session::setFlash('error', 'Goal not found');
            $this->redirect('/finances/goals');
            return;
        }

        // Get expense categories for dropdown
        $categories = $this->financeModel->getUniqueCategories($userId);

        $this->view('finances/goals/edit', [
            'goal' => $goal,
            'categories' => $categories
        ]);
    }

    /**
     * Process goal update
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get goal
        $goal = $this->goalModel->find($id);

        // Check if goal exists and belongs to user
        if (!$goal || $goal['user_id'] != $userId) {
            Session::setFlash('error', 'Goal not found');
            $this->redirect('/finances/goals');
            return;
        }

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['name', 'target_amount', 'start_date', 'target_date']);

        if (!empty($errors)) {
            $goal = array_merge($goal, $data);
            $categories = $this->financeModel->getUniqueCategories($userId);
            $this->view('finances/goals/edit', [
                'errors' => $errors,
                'goal' => $goal,
                'categories' => $categories
            ]);
            return;
        }

        // Validate amount
        if (!is_numeric($data['target_amount']) || $data['target_amount'] <= 0) {
            $errors['target_amount'] = 'Target amount must be a positive number';
            $goal = array_merge($goal, $data);
            $categories = $this->financeModel->getUniqueCategories($userId);
            $this->view('finances/goals/edit', [
                'errors' => $errors,
                'goal' => $goal,
                'categories' => $categories
            ]);
            return;
        }

        // Validate dates
        if (strtotime($data['start_date']) > strtotime($data['target_date'])) {
            $errors['target_date'] = 'Target date must be after start date';
            $goal = array_merge($goal, $data);
            $categories = $this->financeModel->getUniqueCategories($userId);
            $this->view('finances/goals/edit', [
                'errors' => $errors,
                'goal' => $goal,
                'categories' => $categories
            ]);
            return;
        }

        // Prepare goal data
        $goalData = [
            'name' => $data['name'],
            'target_amount' => $data['target_amount'],
            'start_date' => $data['start_date'],
            'target_date' => $data['target_date'],
            'category' => $data['category'] ?? null,
            'priority' => $data['priority'] ?? 'medium',
            'description' => $data['description'] ?? null,
            'icon' => $data['icon'] ?? 'fa-piggy-bank',
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update status if provided
        if (isset($data['status'])) {
            $goalData['status'] = $data['status'];
        }

        // Update goal
        $result = $this->goalModel->update($id, $goalData);

        if ($result) {
            Session::setFlash('success', 'Financial goal updated successfully');
            $this->redirect('/finances/goals/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update financial goal');
            $goal = array_merge($goal, $data);
            $categories = $this->financeModel->getUniqueCategories($userId);
            $this->view('finances/goals/edit', [
                'errors' => ['general' => 'Failed to update goal. Please try again.'],
                'goal' => $goal,
                'categories' => $categories
            ]);
        }
    }

    /**
     * Process goal deletion
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get goal
        $goal = $this->goalModel->find($id);

        // Check if goal exists and belongs to user
        if (!$goal || $goal['user_id'] != $userId) {
            Session::setFlash('error', 'Goal not found');
            $this->redirect('/finances/goals');
            return;
        }

        // Delete goal
        $result = $this->goalModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Financial goal deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete financial goal');
        }

        $this->redirect('/finances/goals');
    }

    /**
     * Show contribution form
     */
    public function createContribution($goalId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get goal
        $goal = $this->goalModel->find($goalId);

        // Check if goal exists and belongs to user
        if (!$goal || $goal['user_id'] != $userId) {
            Session::setFlash('error', 'Goal not found');
            $this->redirect('/finances/goals');
            return;
        }

        $this->view('finances/goals/create_contribution', [
            'goal' => $goal
        ]);
    }

    /**
     * Process contribution creation
     */
    public function storeContribution($goalId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get goal
        $goal = $this->goalModel->find($goalId);

        // Check if goal exists and belongs to user
        if (!$goal || $goal['user_id'] != $userId) {
            Session::setFlash('error', 'Goal not found');
            $this->redirect('/finances/goals');
            return;
        }

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['amount', 'contribution_date']);

        if (!empty($errors)) {
            $this->view('finances/goals/create_contribution', [
                'errors' => $errors,
                'data' => $data,
                'goal' => $goal
            ]);
            return;
        }

        // Validate amount
        if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
            $errors['amount'] = 'Amount must be a positive number';
            $this->view('finances/goals/create_contribution', [
                'errors' => $errors,
                'data' => $data,
                'goal' => $goal
            ]);
            return;
        }

        // Prepare contribution data
        $contributionData = [
            'goal_id' => $goalId,
            'amount' => $data['amount'],
            'contribution_date' => $data['contribution_date'],
            'notes' => $data['notes'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add contribution
        $result = $this->goalModel->addContribution($goalId, $contributionData);

        if ($result) {
            Session::setFlash('success', 'Contribution added successfully');
            $this->redirect('/finances/goals/view/' . $goalId);
        } else {
            Session::setFlash('error', 'Failed to add contribution');
            $this->view('finances/goals/create_contribution', [
                'errors' => ['general' => 'Failed to add contribution. Please try again.'],
                'data' => $data,
                'goal' => $goal
            ]);
        }
    }
}
