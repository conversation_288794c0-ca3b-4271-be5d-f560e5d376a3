<?php
/**
 * Budget Controller
 *
 * Handles budget-related functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Budget.php';
require_once __DIR__ . '/../models/Finance.php';

class BudgetController extends BaseController {
    private $budgetModel;
    private $financeModel;

    public function __construct() {
        $this->budgetModel = new Budget();
        $this->financeModel = new Finance();
    }

    /**
     * Show budget dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get active budget
        $activeBudget = $this->budgetModel->getActiveBudget($userId);

        // Get budget progress if active budget exists
        $budgetProgress = null;
        if ($activeBudget) {
            $budgetProgress = $this->budgetModel->getBudgetProgress($activeBudget['id']);
        }

        // Get all budgets
        $budgets = $this->budgetModel->getUserBudgets($userId);

        // Get categories for dropdown
        $categories = $this->financeModel->getUniqueCategories($userId);

        $this->view('finances/budgets/index', [
            'activeBudget' => $activeBudget,
            'budgetProgress' => $budgetProgress,
            'budgets' => $budgets,
            'categories' => $categories
        ]);
    }

    /**
     * Show budget creation form
     */
    public function createBudget() {
        $this->requireLogin();

        // Get categories for dropdown
        $user = Session::getUser();
        $userId = $user['id'];
        $categories = $this->financeModel->getUniqueCategories($userId);

        $this->view('finances/budgets/create', [
            'categories' => $categories
        ]);
    }

    /**
     * Process budget creation
     */
    public function storeBudget() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['name', 'start_date', 'end_date']);

        if (!empty($errors)) {
            $this->view('finances/budgets/create', [
                'errors' => $errors,
                'data' => $data,
                'categories' => $this->financeModel->getUniqueCategories($userId)
            ]);
            return;
        }

        // Validate date range
        if (strtotime($data['start_date']) > strtotime($data['end_date'])) {
            $errors['end_date'] = 'End date must be after start date';
            $this->view('finances/budgets/create', [
                'errors' => $errors,
                'data' => $data,
                'categories' => $this->financeModel->getUniqueCategories($userId)
            ]);
            return;
        }

        // Prepare budget data
        $budgetData = [
            'user_id' => $userId,
            'name' => $data['name'],
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'description' => $data['description'] ?? null,
            'is_active' => isset($data['is_active']) ? 1 : 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create budget
        $budgetId = $this->budgetModel->createBudget($budgetData);

        if (!$budgetId) {
            Session::setFlash('error', 'Failed to create budget');
            $this->redirect('/finances/budgets/create');
            return;
        }

        // Process budget categories
        if (isset($data['categories']) && is_array($data['categories']) &&
            isset($data['amounts']) && is_array($data['amounts'])) {

            $categories = $data['categories'];
            $amounts = $data['amounts'];

            for ($i = 0; $i < count($categories); $i++) {
                if (empty($categories[$i]) || !isset($amounts[$i]) || !is_numeric($amounts[$i])) {
                    continue;
                }

                $categoryData = [
                    'budget_id' => $budgetId,
                    'category' => $categories[$i],
                    'amount' => (float)$amounts[$i],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $categoryId = $this->budgetModel->createBudgetCategory($categoryData);

                // Create alert at 80% threshold if alert is enabled
                if ($categoryId && isset($data['enable_alerts']) && $data['enable_alerts'] == 1) {

                    $alertData = [
                        'budget_category_id' => $categoryId,
                        'threshold_percentage' => 80, // Default threshold
                        'is_triggered' => false,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    $this->budgetModel->createBudgetAlert($alertData);
                }
            }
        }

        Session::setFlash('success', 'Budget created successfully');
        $this->redirect('/finances/budgets');
    }

    /**
     * Show budget details
     */
    public function viewBudget($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get budget
        $budget = $this->budgetModel->find($id);

        // Verify budget exists and belongs to user
        if (!$budget || $budget['user_id'] != $userId) {
            Session::setFlash('error', 'Budget not found');
            $this->redirect('/finances/budgets');
        }

        // Get budget progress
        $budgetProgress = $this->budgetModel->getBudgetProgress($id);

        $this->view('finances/budgets/view', [
            'budget' => $budget,
            'budgetProgress' => $budgetProgress
        ]);
    }

    /**
     * Show budget edit form
     */
    public function editBudget($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get budget
        $budget = $this->budgetModel->find($id);

        // Verify budget exists and belongs to user
        if (!$budget || $budget['user_id'] != $userId) {
            Session::setFlash('error', 'Budget not found');
            $this->redirect('/finances/budgets');
        }

        // Get budget categories
        $categories = $this->budgetModel->getBudgetCategories($id);

        // Get all categories for dropdown
        $allCategories = $this->financeModel->getUniqueCategories($userId);

        $this->view('finances/budgets/edit', [
            'budget' => $budget,
            'budgetCategories' => $categories,
            'allCategories' => $allCategories
        ]);
    }

    /**
     * Process budget update
     */
    public function updateBudget($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get budget
        $budget = $this->budgetModel->find($id);

        // Verify budget exists and belongs to user
        if (!$budget || $budget['user_id'] != $userId) {
            Session::setFlash('error', 'Budget not found');
            $this->redirect('/finances/budgets');
        }

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['name', 'start_date', 'end_date']);

        if (!empty($errors)) {
            $categories = $this->budgetModel->getBudgetCategories($id);
            $allCategories = $this->financeModel->getUniqueCategories($userId);

            $this->view('finances/budgets/edit', [
                'errors' => $errors,
                'budget' => array_merge($budget, $data),
                'budgetCategories' => $categories,
                'allCategories' => $allCategories
            ]);
            return;
        }

        // Validate date range
        if (strtotime($data['start_date']) > strtotime($data['end_date'])) {
            $errors['end_date'] = 'End date must be after start date';

            $categories = $this->budgetModel->getBudgetCategories($id);
            $allCategories = $this->financeModel->getUniqueCategories($userId);

            $this->view('finances/budgets/edit', [
                'errors' => $errors,
                'budget' => array_merge($budget, $data),
                'budgetCategories' => $categories,
                'allCategories' => $allCategories
            ]);
            return;
        }

        // Prepare budget data
        $budgetData = [
            'name' => $data['name'],
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'description' => $data['description'] ?? null,
            'is_active' => isset($data['is_active']) ? 1 : 0,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update budget
        $result = $this->budgetModel->update($id, $budgetData);

        if (!$result) {
            Session::setFlash('error', 'Failed to update budget');
            $this->redirect('/finances/budgets/edit/' . $id);
            return;
        }

        Session::setFlash('success', 'Budget updated successfully');
        $this->redirect('/finances/budgets');
    }

    /**
     * Add budget category
     */
    public function addBudgetCategory($budgetId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get budget
        $budget = $this->budgetModel->find($budgetId);

        // Verify budget exists and belongs to user
        if (!$budget || $budget['user_id'] != $userId) {
            $this->json(['success' => false, 'message' => 'Budget not found'], 404);
            return;
        }

        $data = $this->getPostData();

        // Validate required fields
        if (empty($data['category']) || !isset($data['amount']) || !is_numeric($data['amount'])) {
            $this->json(['success' => false, 'message' => 'Invalid category data'], 400);
            return;
        }

        $categoryData = [
            'budget_id' => $budgetId,
            'category' => $data['category'],
            'amount' => (float)$data['amount'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $categoryId = $this->budgetModel->createBudgetCategory($categoryData);

        if (!$categoryId) {
            $this->json(['success' => false, 'message' => 'Failed to add category'], 500);
            return;
        }

        // Create alert if enabled
        if (isset($data['enable_alert']) && $data['enable_alert'] == 1) {

            $alertData = [
                'budget_category_id' => $categoryId,
                'threshold_percentage' => $data['threshold'] ?? 80,
                'is_triggered' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->budgetModel->createBudgetAlert($alertData);
        }

        $this->json(['success' => true, 'message' => 'Category added successfully']);
    }

    /**
     * Update budget category
     */
    public function updateBudgetCategory($categoryId) {
        $this->requireLogin();

        // Get category
        $category = $this->budgetModel->getBudgetCategory($categoryId);
        if (!$category) {
            $this->json(['success' => false, 'message' => 'Category not found'], 404);
            return;
        }

        // Get budget to verify ownership
        $budget = $this->budgetModel->find($category['budget_id']);
        $user = Session::getUser();
        $userId = $user['id'];

        if (!$budget || $budget['user_id'] != $userId) {
            $this->json(['success' => false, 'message' => 'Unauthorized'], 403);
            return;
        }

        $data = $this->getPostData();

        // Validate required fields
        if (empty($data['category']) || !isset($data['amount']) || !is_numeric($data['amount'])) {
            $this->json(['success' => false, 'message' => 'Invalid category data'], 400);
            return;
        }

        $categoryData = [
            'category' => $data['category'],
            'amount' => (float)$data['amount'],
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->budgetModel->updateBudgetCategory($categoryId, $categoryData);

        if (!$result) {
            $this->json(['success' => false, 'message' => 'Failed to update category'], 500);
            return;
        }

        $this->json(['success' => true, 'message' => 'Category updated successfully']);
    }

    /**
     * Delete budget category
     */
    public function deleteBudgetCategory($categoryId) {
        $this->requireLogin();

        // Get category
        $category = $this->budgetModel->getBudgetCategory($categoryId);
        if (!$category) {
            $this->json(['success' => false, 'message' => 'Category not found'], 404);
            return;
        }

        // Get budget to verify ownership
        $budget = $this->budgetModel->find($category['budget_id']);
        $user = Session::getUser();
        $userId = $user['id'];

        if (!$budget || $budget['user_id'] != $userId) {
            $this->json(['success' => false, 'message' => 'Unauthorized'], 403);
            return;
        }

        $result = $this->budgetModel->deleteBudgetCategory($categoryId);

        if (!$result) {
            $this->json(['success' => false, 'message' => 'Failed to delete category'], 500);
            return;
        }

        $this->json(['success' => true, 'message' => 'Category deleted successfully']);
    }

    /**
     * Delete budget
     */
    public function deleteBudget($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get budget
        $budget = $this->budgetModel->find($id);

        // Verify budget exists and belongs to user
        if (!$budget || $budget['user_id'] != $userId) {
            Session::setFlash('error', 'Budget not found');
            $this->redirect('/finances/budgets');
            return;
        }

        // Delete budget
        $result = $this->budgetModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Budget deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete budget');
        }

        $this->redirect('/finances/budgets');
    }

    /**
     * Show budget reports
     */
    public function reports() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get historical budget data
        $historicalData = $this->budgetModel->getHistoricalBudgetData($userId);

        // Get category spending trends
        $spendingTrends = $this->budgetModel->getCategorySpendingTrends($userId);

        // Get budget performance metrics
        $performanceMetrics = $this->budgetModel->getBudgetPerformanceMetrics($userId);

        // Get active budget
        $activeBudget = $this->budgetModel->getActiveBudget($userId);
        $activeBudgetProgress = null;

        if ($activeBudget) {
            $activeBudgetProgress = $this->budgetModel->getBudgetProgress($activeBudget['id']);
        }

        $this->view('finances/budgets/reports', [
            'historicalData' => $historicalData,
            'spendingTrends' => $spendingTrends,
            'performanceMetrics' => $performanceMetrics,
            'activeBudget' => $activeBudget,
            'activeBudgetProgress' => $activeBudgetProgress
        ]);
    }
}
