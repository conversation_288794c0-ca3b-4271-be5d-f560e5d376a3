<?php
/**
 * Brigade Template Controller
 *
 * Handles the management of brigade templates for the AI Agent Army
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/BrigadeTemplateManager.php';
require_once __DIR__ . '/../models/AegisDirectorProjectManager.php';
require_once __DIR__ . '/../models/AIAgent.php';
require_once __DIR__ . '/../models/Project.php';
require_once __DIR__ . '/../models/ProjectAgentAssignment.php';
require_once __DIR__ . '/../utils/Session.php';

class BrigadeTemplateController extends BaseController {
    protected $brigadeTemplateManager;
    protected $aegisDirectorProjectManager;
    protected $agentModel;
    protected $projectModel;
    protected $assignmentModel;

    public function __construct() {
        parent::__construct();
        $this->brigadeTemplateManager = new BrigadeTemplateManager();
        $this->aegisDirectorProjectManager = new AegisDirectorProjectManager();
        $this->agentModel = new AIAgent();
        $this->projectModel = new Project();
        $this->assignmentModel = new ProjectAgentAssignment();
    }

    /**
     * Display the list of available brigade templates
     */
    public function index() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        // Get all available brigade templates
        $templates = $this->brigadeTemplateManager->getAllBrigadeTemplates();

        // Load the view
        $this->view('brigades/templates', [
            'templates' => $templates
        ]);
    }

    /**
     * Display the details of a specific brigade template
     *
     * @param string $brigadeType Brigade type
     */
    public function viewTemplate($brigadeType) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        // Get the template details
        $template = $this->brigadeTemplateManager->getBrigadeTemplate($brigadeType);

        if (!$template) {
            $this->setFlashMessage('error', 'Brigade template not found');
            $this->redirect('/brigades/templates');
            return;
        }

        // Get user's agents for role assignment
        $userAgents = $this->agentModel->getUserAgents($userId);

        // Load the view
        $this->view('brigades/template_details', [
            'template' => $template,
            'agents' => $userAgents
        ]);
    }

    /**
     * Create a new project from a brigade template
     */
    public function createProject() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $brigadeType = $_POST['brigade_type'] ?? '';
            $projectName = $_POST['project_name'] ?? '';
            $projectDescription = $_POST['project_description'] ?? '';
            $deadline = $_POST['deadline'] ?? date('Y-m-d', strtotime('+1 week'));
            $roleAssignments = $_POST['role_assignments'] ?? [];

            if (empty($brigadeType) || empty($projectName)) {
                Session::setFlash('error', 'Brigade type and project name are required');
                $this->redirect('/brigades/templates');
                return;
            }

            // Create the project
            $projectId = $this->aegisDirectorProjectManager->createAgentArmyProject(
                $userId,
                $brigadeType,
                $projectName,
                $projectDescription,
                $deadline
            );

            if ($projectId) {
                // If role assignments were provided, update them
                if (!empty($roleAssignments)) {
                    $this->brigadeTemplateManager->assignAgentsToBrigadeRoles(
                        $projectId,
                        $brigadeType,
                        $userId,
                        $roleAssignments
                    );
                }

                Session::setFlash('success', 'Brigade project created successfully');
                $this->redirect('/projects/view/' . $projectId);
            } else {
                Session::setFlash('error', 'Failed to create brigade project');
                $this->redirect('/brigades/templates');
            }
        } else {
            // Get available brigade types
            $brigadeTypes = $this->aegisDirectorProjectManager->getAvailableBrigadeTypes();

            // Load the view
            $this->view('brigades/create_project', [
                'brigadeTypes' => $brigadeTypes
            ]);
        }
    }

    /**
     * Display the form for assigning agents to brigade roles
     *
     * @param int $projectId Project ID
     */
    public function assignAgents($projectId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        // Get the project details
        $project = $this->projectModel->getProjectDetails($projectId, $userId);

        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/projects');
            return;
        }

        $brigadeType = $project['brigade_type'] ?? '';

        if (empty($brigadeType)) {
            Session::setFlash('error', 'This is not a brigade project');
            $this->redirect('/projects/view/' . $projectId);
            return;
        }

        // Get the template details
        $template = $this->brigadeTemplateManager->getBrigadeTemplate($brigadeType);

        if (!$template) {
            Session::setFlash('error', 'Brigade template not found');
            $this->redirect('/projects/view/' . $projectId);
            return;
        }

        // Get user's agents for role assignment
        $userAgents = $this->agentModel->getUserAgents($userId);

        // Get current agent assignments
        $currentAssignments = $this->assignmentModel->getProjectAgents($projectId);

        // Load the view
        $this->view('brigades/assign_agents', [
            'project' => $project,
            'template' => $template,
            'agents' => $userAgents,
            'currentAssignments' => $currentAssignments
        ]);
    }

    /**
     * Process agent assignments for a brigade project
     *
     * @param int $projectId Project ID
     */
    public function processAssignments($projectId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $roleAssignments = $_POST['role_assignments'] ?? [];

            // Get the project details
            $project = $this->projectModel->getProjectDetails($projectId, $userId);

            if (!$project) {
                Session::setFlash('error', 'Project not found');
                $this->redirect('/projects');
                return;
            }

            $brigadeType = $project['brigade_type'] ?? '';

            if (empty($brigadeType)) {
                Session::setFlash('error', 'This is not a brigade project');
                $this->redirect('/projects/view/' . $projectId);
                return;
            }

            // Update agent assignments
            $result = $this->brigadeTemplateManager->assignAgentsToBrigadeRoles(
                $projectId,
                $brigadeType,
                $userId,
                $roleAssignments
            );

            if ($result) {
                Session::setFlash('success', 'Agent assignments updated successfully');
            } else {
                Session::setFlash('error', 'Failed to update agent assignments');
            }

            $this->redirect('/projects/view/' . $projectId);
        } else {
            $this->redirect('/brigades/assign-agents/' . $projectId);
        }
    }
}
