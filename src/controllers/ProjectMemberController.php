<?php
/**
 * Project Member Controller
 *
 * Handles project member-related functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Project.php';
require_once __DIR__ . '/../models/User.php';

class ProjectMemberController extends BaseController {
    private $projectModel;
    private $userModel;

    public function __construct() {
        $this->projectModel = new Project();
        $this->userModel = new User();
    }

    /**
     * Add member to project
     */
    public function addMember() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data || empty($data['project_id']) || empty($data['email']) || empty($data['role'])) {
            $this->json(['success' => false, 'message' => 'Missing required fields'], 400);
            return;
        }

        $projectId = $data['project_id'];
        $email = $data['email'];
        $role = $data['role'];

        // Verify project exists and user is owner or admin
        $project = $this->projectModel->find($projectId);
        $projectMembers = $this->projectModel->getProjectMembers($projectId);
        
        $isOwnerOrAdmin = false;
        foreach ($projectMembers as $member) {
            if ($member['user_id'] == $userId && ($member['role'] === 'owner' || $member['role'] === 'admin')) {
                $isOwnerOrAdmin = true;
                break;
            }
        }

        if (!$project || !$isOwnerOrAdmin) {
            $this->json(['success' => false, 'message' => 'Project not found or access denied'], 404);
            return;
        }

        // Find user by email
        $memberUser = $this->userModel->findByEmail($email);

        if (!$memberUser) {
            $this->json(['success' => false, 'message' => 'User not found with this email'], 404);
            return;
        }

        // Check if user is already a member
        foreach ($projectMembers as $member) {
            if ($member['user_id'] == $memberUser['id']) {
                $this->json(['success' => false, 'message' => 'User is already a member of this project'], 400);
                return;
            }
        }

        // Validate role
        $validRoles = ['member', 'admin'];
        if (!in_array($role, $validRoles)) {
            $this->json(['success' => false, 'message' => 'Invalid role'], 400);
            return;
        }

        // Add member to project
        $memberData = [
            'project_id' => $projectId,
            'user_id' => $memberUser['id'],
            'role' => $role,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $db = Database::getInstance();
        $result = $db->insert('project_members', $memberData);

        if ($result) {
            $this->json(['success' => true, 'message' => 'Member added successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to add member'], 500);
        }
    }

    /**
     * Change member role
     */
    public function changeRole() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data || empty($data['member_id']) || empty($data['role'])) {
            $this->json(['success' => false, 'message' => 'Missing required fields'], 400);
            return;
        }

        $memberId = $data['member_id'];
        $role = $data['role'];

        // Get member details
        $db = Database::getInstance();
        $member = $db->fetchOne("SELECT * FROM project_members WHERE id = ?", [$memberId]);

        if (!$member) {
            $this->json(['success' => false, 'message' => 'Member not found'], 404);
            return;
        }

        $projectId = $member['project_id'];

        // Verify user is owner or admin
        $projectMembers = $this->projectModel->getProjectMembers($projectId);
        
        $isOwner = false;
        foreach ($projectMembers as $projectMember) {
            if ($projectMember['user_id'] == $userId && $projectMember['role'] === 'owner') {
                $isOwner = true;
                break;
            }
        }

        if (!$isOwner) {
            $this->json(['success' => false, 'message' => 'Only the project owner can change roles'], 403);
            return;
        }

        // Validate role
        $validRoles = ['member', 'admin'];
        if (!in_array($role, $validRoles)) {
            $this->json(['success' => false, 'message' => 'Invalid role'], 400);
            return;
        }

        // Update member role
        $result = $db->update('project_members', $memberId, [
            'role' => $role,
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            $this->json(['success' => true, 'message' => 'Role updated successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to update role'], 500);
        }
    }

    /**
     * Remove member from project
     */
    public function removeMember() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data || empty($data['member_id'])) {
            $this->json(['success' => false, 'message' => 'Missing required fields'], 400);
            return;
        }

        $memberId = $data['member_id'];

        // Get member details
        $db = Database::getInstance();
        $member = $db->fetchOne("SELECT * FROM project_members WHERE id = ?", [$memberId]);

        if (!$member) {
            $this->json(['success' => false, 'message' => 'Member not found'], 404);
            return;
        }

        $projectId = $member['project_id'];

        // Verify user is owner or admin
        $projectMembers = $this->projectModel->getProjectMembers($projectId);
        
        $isOwnerOrAdmin = false;
        foreach ($projectMembers as $projectMember) {
            if ($projectMember['user_id'] == $userId && ($projectMember['role'] === 'owner' || $projectMember['role'] === 'admin')) {
                $isOwnerOrAdmin = true;
                break;
            }
        }

        if (!$isOwnerOrAdmin) {
            $this->json(['success' => false, 'message' => 'Only project owners and admins can remove members'], 403);
            return;
        }

        // Cannot remove the owner
        if ($member['role'] === 'owner') {
            $this->json(['success' => false, 'message' => 'Cannot remove the project owner'], 400);
            return;
        }

        // Remove member
        $result = $db->delete('project_members', $memberId);

        if ($result) {
            $this->json(['success' => true, 'message' => 'Member removed successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to remove member'], 500);
        }
    }
}
