<?php
/**
 * Help Controller
 *
 * This controller handles the help center functionality.
 */

// Simple controller without extending BaseController

class HelpController
{
    /**
     * Display the help center home page
     */
    public function index()
    {
        // Set the title
        $title = 'Help Center';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/index.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the user guide
     */
    public function userGuide()
    {
        // Set the title
        $title = 'User Guide';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/user-guide.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the feature overview
     */
    public function featureOverview()
    {
        // Set the title
        $title = 'Feature Overview';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get the implementation status of all features
        $features = $this->getFeatureStatus();

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/feature-overview.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the tutorials page
     */
    public function tutorials()
    {
        // Set the title
        $title = 'Tutorials';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get all tutorials
        $tutorials = $this->getTutorials();

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/tutorials.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the FAQ page
     */
    public function faq()
    {
        // Set the title
        $title = 'Frequently Asked Questions';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get all FAQs
        $faqs = $this->getFaqs();

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/faq.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the troubleshooting page
     */
    public function troubleshooting()
    {
        // Set the title
        $title = 'Troubleshooting';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get all troubleshooting topics
        $topics = $this->getTroubleshootingTopics();

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/troubleshooting.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the contact support page
     */
    public function contactSupport()
    {
        // Set the title
        $title = 'Contact Support';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/contact-support.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the ADHD Project Planning Guide
     */
    public function adhdProjectPlanningGuide()
    {
        // Set the title
        $title = 'ADHD-Friendly Project Planning Guide';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/adhd-project-planning-guide.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Serve the ADHD Project Planning Guide markdown file
     */
    public function adhdProjectPlanningGuideMd()
    {
        // Set the content type to markdown
        header('Content-Type: text/markdown');

        // Output the markdown file
        readfile(dirname(dirname(__DIR__)) . '/docs/adhd-friendly-project-planning-guide.md');
        exit;
    }

    /**
     * Display the feature request page
     */
    public function featureRequest()
    {
        // Set the title
        $title = 'Feature Request';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/feature-request.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the dashboard redesign plan
     */
    public function dashboardRedesignPlan()
    {
        // Set the title
        $title = 'Dashboard Redesign Plan';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/dashboard-redesign-plan.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the mind mapping features page
     */
    public function mindMappingFeatures()
    {
        // Set the title
        $title = 'Mind Mapping Features';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get feature data
        $allFeatures = $this->getFeatureStatus();

        // Set category information
        $categoryTitle = 'Mind Mapping';
        $categoryDescription = 'Mind mapping features help you visually organize your thoughts, ideas, and projects. These tools are designed to work with your ADHD brain, making it easier to see connections and structure your thinking.';

        // Filter features for this category
        $features = array_filter($allFeatures['new_features'], function($feature) {
            return strpos($feature['name'], 'Mind Map') !== false;
        });

        // Add more detailed information to features
        foreach ($features as &$feature) {
            if ($feature['name'] === 'Mind Mapping Tool') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Create visual maps of ideas, projects, and concepts</li>
                        <li>Drag-and-drop interface for easy organization</li>
                        <li>Color-coding and icons for visual differentiation</li>
                        <li>Export maps as images or PDFs</li>
                        <li>Collaborate with others on shared mind maps</li>
                    </ul>
                ';
            }
        }

        // Related categories
        $relatedCategories = [
            [
                'title' => 'ADHD Management',
                'description' => 'Tools for symptom tracking, CBT, and executive function',
                'url' => '/momentum/help/adhd-features'
            ],
            [
                'title' => 'Productivity Tools',
                'description' => 'Time blocking, energy tracking, and focus enhancement',
                'url' => '/momentum/help/productivity-features'
            ],
            [
                'title' => 'Knowledge Management',
                'description' => 'Note-taking templates and resource organization',
                'url' => '/momentum/help/knowledge-features'
            ]
        ];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/feature-category.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the ADHD management features page
     */
    public function adhdFeatures()
    {
        // Set the title
        $title = 'ADHD Management Features';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get feature data
        $allFeatures = $this->getFeatureStatus();

        // Set category information
        $categoryTitle = 'ADHD Management';
        $categoryDescription = 'These features are specifically designed to help manage ADHD symptoms, track medication effectiveness, identify triggers, and strengthen executive function skills.';

        // Get implemented ADHD features
        $implementedFeatures = $allFeatures['adhd'];

        // Filter features for upcoming ADHD features
        $upcomingFeatures = array_filter($allFeatures['new_features'], function($feature) {
            return strpos($feature['name'], 'ADHD-Friendly Reading') !== false;
        });

        // Add more detailed information to implemented features
        foreach ($implementedFeatures as &$feature) {
            if ($feature['name'] === 'ADHD Dashboard') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Overview of all ADHD management tools</li>
                        <li>Quick access to symptom tracking and CBT tools</li>
                        <li>View recent symptom logs and trends</li>
                        <li>Track consistency streaks</li>
                        <li>Get personalized recommendations</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Symptom Tracker') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Log daily ADHD symptoms with rating scales</li>
                        <li>Track focus, productivity, consistency, and more</li>
                        <li>Add context notes about your day</li>
                        <li>View trends and patterns over time</li>
                        <li>Generate reports for healthcare providers</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Thought Records') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Record situations that trigger ADHD symptoms</li>
                        <li>Document automatic thoughts and emotions</li>
                        <li>Identify cognitive distortions</li>
                        <li>Create balanced alternative thoughts</li>
                        <li>Track emotional intensity before and after</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Productivity Strategies') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Browse ADHD-friendly productivity techniques</li>
                        <li>Filter strategies by symptom or situation</li>
                        <li>View detailed instructions for each strategy</li>
                        <li>Mark favorites for quick access</li>
                        <li>Log which strategies work best for you</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Mindfulness Exercises') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Access guided mindfulness exercises</li>
                        <li>Follow step-by-step instructions</li>
                        <li>Set timers for practice sessions</li>
                        <li>Log your experience after completion</li>
                        <li>Track mindfulness practice statistics</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Consistency Trackers') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Set up trackers for important daily habits</li>
                        <li>Check in daily to mark completion</li>
                        <li>Build streaks for consistent behavior</li>
                        <li>View patterns and insights over time</li>
                        <li>Get reminders for habit completion</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Medication Tracker') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Track medication timing, dosage, and effectiveness</li>
                        <li>Set reminders for medication times</li>
                        <li>Record side effects and discuss with healthcare provider</li>
                        <li>Visualize medication effectiveness over time</li>
                        <li>Export reports for healthcare appointments</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Trigger Identification Tool') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Log situations that trigger ADHD symptoms</li>
                        <li>Identify patterns in environmental, emotional, and physical triggers</li>
                        <li>Develop strategies to manage or avoid triggers</li>
                        <li>Track effectiveness of coping strategies</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Executive Function Exercises') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Targeted exercises for working memory, task initiation, planning, and organization</li>
                        <li>Adaptive difficulty based on your performance</li>
                        <li>Track progress over time</li>
                        <li>Personalized recommendations based on your symptom profile</li>
                        <li>Integration with daily tasks and projects</li>
                    </ul>
                ';
            }
        }

        // Add more detailed information to upcoming features
        foreach ($upcomingFeatures as &$feature) {
            if ($feature['name'] === 'Medication Tracker') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Track medication timing, dosage, and effectiveness</li>
                        <li>Set reminders for medication times</li>
                        <li>Record side effects and discuss with healthcare provider</li>
                        <li>Visualize medication effectiveness over time</li>
                        <li>Export reports for healthcare appointments</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Trigger Identification Tool') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Log situations that trigger ADHD symptoms</li>
                        <li>Identify patterns in environmental, emotional, and physical triggers</li>
                        <li>Develop strategies to manage or avoid triggers</li>
                        <li>Track effectiveness of coping strategies</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Executive Function Exercises') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Targeted exercises for working memory, task initiation, planning, and organization</li>
                        <li>Adaptive difficulty based on your performance</li>
                        <li>Track progress over time</li>
                        <li>Personalized recommendations based on your symptom profile</li>
                        <li>Integration with daily tasks and projects</li>
                    </ul>
                ';
            }
        }

        // Combine implemented and upcoming features
        $features = array_merge($implementedFeatures, $upcomingFeatures);

        // Related categories
        $relatedCategories = [
            [
                'title' => 'Mind Mapping',
                'description' => 'Visual organization tools for thoughts and projects',
                'url' => '/momentum/help/mind-mapping-features'
            ],
            [
                'title' => 'Productivity Tools',
                'description' => 'Time blocking, energy tracking, and focus enhancement',
                'url' => '/momentum/help/productivity-features'
            ],
            [
                'title' => 'Wellness Integration',
                'description' => 'Mood tracking, sleep quality, and exercise logging',
                'url' => '/momentum/help/wellness-features'
            ]
        ];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/feature-category.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the productivity features page
     */
    public function productivityFeatures()
    {
        // Set the title
        $title = 'Productivity Features';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get feature data
        $allFeatures = $this->getFeatureStatus();

        // Set category information
        $categoryTitle = 'Productivity Tools';
        $categoryDescription = 'These productivity tools are designed specifically for ADHD brains, helping you manage time, energy, and focus more effectively.';

        // Filter features for this category
        $features = array_filter($allFeatures['new_features'], function($feature) {
            return strpos($feature['name'], 'Time Blocking') !== false ||
                   strpos($feature['name'], 'Energy Level') !== false ||
                   strpos($feature['name'], 'Task Batching') !== false ||
                   strpos($feature['name'], 'Distraction Journal') !== false;
        });

        // Add more detailed information to features
        foreach ($features as &$feature) {
            if ($feature['name'] === 'Time Blocking Calendar') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Visually plan your day in blocks of time</li>
                        <li>Color-code different types of activities</li>
                        <li>Set realistic time estimates for tasks</li>
                        <li>Include buffer time between activities</li>
                        <li>Adjust blocks easily when plans change</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Energy Level Tracking') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Track your energy levels throughout the day</li>
                        <li>Identify your peak productivity times</li>
                        <li>Match tasks to appropriate energy levels</li>
                        <li>Analyze patterns to optimize your schedule</li>
                    </ul>
                ';
            }
        }

        // Related categories
        $relatedCategories = [
            [
                'title' => 'ADHD Management',
                'description' => 'Tools for symptom tracking, CBT, and executive function',
                'url' => '/momentum/help/adhd-features'
            ],
            [
                'title' => 'Mind Mapping',
                'description' => 'Visual organization tools for thoughts and projects',
                'url' => '/momentum/help/mind-mapping-features'
            ],
            [
                'title' => 'Wellness Integration',
                'description' => 'Mood tracking, sleep quality, and exercise logging',
                'url' => '/momentum/help/wellness-features'
            ]
        ];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/feature-category.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the wellness features page
     */
    public function wellnessFeatures()
    {
        // Set the title
        $title = 'Wellness Features';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get feature data
        $allFeatures = $this->getFeatureStatus();

        // Set category information
        $categoryTitle = 'Wellness Integration';
        $categoryDescription = 'These wellness features help you track and improve aspects of your physical and emotional well-being that can significantly impact ADHD symptoms.';

        // Filter features for this category
        $features = array_filter($allFeatures['new_features'], function($feature) {
            return strpos($feature['name'], 'Mood Tracker') !== false ||
                   strpos($feature['name'], 'Sleep Quality') !== false ||
                   strpos($feature['name'], 'Exercise') !== false ||
                   strpos($feature['name'], 'Nutrition') !== false;
        });

        // Add more detailed information to features
        foreach ($features as &$feature) {
            if ($feature['name'] === 'Mood Tracker') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Track your emotional state throughout the day</li>
                        <li>Identify patterns and triggers for mood changes</li>
                        <li>Correlate mood with productivity and ADHD symptoms</li>
                        <li>Develop strategies for emotional regulation</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Sleep Quality Tracker') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Log sleep duration and quality</li>
                        <li>Track factors that affect sleep (caffeine, screen time, etc.)</li>
                        <li>Analyze how sleep impacts your ADHD symptoms</li>
                        <li>Receive personalized recommendations for better sleep</li>
                    </ul>
                ';
            }
        }

        // Related categories
        $relatedCategories = [
            [
                'title' => 'ADHD Management',
                'description' => 'Tools for symptom tracking, CBT, and executive function',
                'url' => '/momentum/help/adhd-features'
            ],
            [
                'title' => 'Productivity Tools',
                'description' => 'Time blocking, energy tracking, and focus enhancement',
                'url' => '/momentum/help/productivity-features'
            ],
            [
                'title' => 'Health Features',
                'description' => 'Symptom journal, health metrics, and medical records',
                'url' => '/momentum/help/health-features'
            ]
        ];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/feature-category.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the financial features page
     */
    public function financialFeatures()
    {
        // Set the title
        $title = 'Financial Features';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get feature data
        $allFeatures = $this->getFeatureStatus();

        // Set category information
        $categoryTitle = 'Financial Management';
        $categoryDescription = 'These financial tools are designed with ADHD-friendly interfaces to help you manage money more effectively, reduce impulsive spending, and achieve financial goals.';

        // Filter features for this category
        $features = array_filter($allFeatures['new_features'], function($feature) {
            return strpos($feature['name'], 'Visual Budget') !== false ||
                   strpos($feature['name'], 'Impulse Purchase') !== false ||
                   strpos($feature['name'], 'Financial Goal') !== false ||
                   strpos($feature['name'], 'Bill Payment') !== false;
        });

        // Add more detailed information to features
        foreach ($features as &$feature) {
            if ($feature['name'] === 'Visual Budget Planner') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Create visually engaging budgets</li>
                        <li>Use color-coding and graphics to make financial data more accessible</li>
                        <li>Interactive elements for adjusting budget categories</li>
                        <li>Visual alerts for budget limits and overspending</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Financial Goal Visualization') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Set financial goals with visual progress tracking</li>
                        <li>Break down large goals into manageable milestones</li>
                        <li>Celebrate progress with visual rewards</li>
                        <li>Adjust goals as needed with flexible planning tools</li>
                    </ul>
                ';
            }
        }

        // Related categories
        $relatedCategories = [
            [
                'title' => 'ADHD Management',
                'description' => 'Tools for symptom tracking, CBT, and executive function',
                'url' => '/momentum/help/adhd-features'
            ],
            [
                'title' => 'Productivity Tools',
                'description' => 'Time blocking, energy tracking, and focus enhancement',
                'url' => '/momentum/help/productivity-features'
            ],
            [
                'title' => 'Analytics & Insights',
                'description' => 'Pattern recognition, productivity analytics, and progress visualization',
                'url' => '/momentum/help/analytics-features'
            ]
        ];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/feature-category.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the knowledge management features page
     */
    public function knowledgeFeatures()
    {
        // Set the title
        $title = 'Knowledge Management Features';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get feature data
        $allFeatures = $this->getFeatureStatus();

        // Set category information
        $categoryTitle = 'Knowledge Management';
        $categoryDescription = 'These knowledge management tools help you capture, organize, and retrieve information in ways that work with your ADHD brain.';

        // Filter features for this category
        $features = array_filter($allFeatures['new_features'], function($feature) {
            return strpos($feature['name'], 'Spaced Repetition') !== false ||
                   strpos($feature['name'], 'Note-Taking') !== false ||
                   strpos($feature['name'], 'Resource Library') !== false ||
                   strpos($feature['name'], 'Concept Connection') !== false ||
                   strpos($feature['name'], 'Personal Wiki') !== false ||
                   strpos($feature['name'], 'Documentation') !== false ||
                   strpos($feature['name'], 'Version History') !== false ||
                   strpos($feature['name'], 'Tag-Based') !== false;
        });

        // Add more detailed information to features
        foreach ($features as &$feature) {
            if ($feature['name'] === 'Note-Taking Templates') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Structured templates for different types of information</li>
                        <li>Visual note-taking formats for visual thinkers</li>
                        <li>Audio recording and transcription for verbal processors</li>
                        <li>Flexible organization with tags and categories</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Personal Wiki') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Create your own interconnected knowledge base</li>
                        <li>Link related concepts and information</li>
                        <li>Visual navigation of your knowledge network</li>
                        <li>Powerful search and filtering capabilities</li>
                    </ul>
                ';
            }
        }

        // Related categories
        $relatedCategories = [
            [
                'title' => 'Mind Mapping',
                'description' => 'Visual organization tools for thoughts and projects',
                'url' => '/momentum/help/mind-mapping-features'
            ],
            [
                'title' => 'Resource Management',
                'description' => 'Bookmark system, link validation, and reading list',
                'url' => '/momentum/help/resource-features'
            ],
            [
                'title' => 'Digital Asset Management',
                'description' => 'File organization, media library, and document versioning',
                'url' => '/momentum/help/digital-asset-features'
            ]
        ];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/feature-category.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the online income strategies guide
     */
    public function onlineIncomeStrategiesGuide()
    {
        // Set the title
        $title = 'Online Income Strategies Guide';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/online-income-strategies-guide.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the income stream evaluator guide
     */
    public function incomeStreamEvaluatorGuide()
    {
        // Set the title
        $title = 'Income Stream Evaluator Guide';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/income-stream-evaluator-guide.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Serve the Online Income Strategies Guide markdown file
     */
    public function onlineIncomeStrategiesGuideMd()
    {
        // Set the content type to markdown
        header('Content-Type: text/markdown');

        // Output the markdown file
        readfile(dirname(dirname(__DIR__)) . '/docs/online-income-strategies-guide.md');
        exit;
    }

    /**
     * Serve the Income Stream Evaluator Guide markdown file
     */
    public function incomeStreamEvaluatorGuideMd()
    {
        // Set the content type to markdown
        header('Content-Type: text/markdown');

        // Output the markdown file
        readfile(dirname(dirname(__DIR__)) . '/docs/income-stream-evaluator-guide.md');
        exit;
    }

    /**
     * Display the online income features page
     */
    public function onlineIncomeFeatures()
    {
        // Set the title
        $title = 'Online Income Features';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/online-income-features.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the online income strategies features page (category view)
     */
    public function onlineIncomeFeaturesCategory()
    {
        // Set the title
        $title = 'Online Income Strategies';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get feature data
        $allFeatures = $this->getFeatureStatus();

        // Set category information
        $categoryTitle = 'Online Income Strategies';
        $categoryDescription = 'These features help you discover, evaluate, and manage various online income opportunities in an ADHD-friendly way, making it easier to identify and pursue the most suitable options for your skills and interests.';

        // Filter features for this category
        $features = array_filter($allFeatures['new_features'], function($feature) {
            return strpos($feature['name'], 'Income Opportunity') !== false ||
                   strpos($feature['name'], 'Passive Income') !== false ||
                   strpos($feature['name'], 'Freelance') !== false ||
                   strpos($feature['name'], 'Online Business') !== false ||
                   strpos($feature['name'], 'Income Tracker') !== false;
        });

        // Add more detailed information to features
        foreach ($features as &$feature) {
            if ($feature['name'] === 'Income Opportunity Explorer') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Browse categorized online income methods</li>
                        <li>Filter opportunities by time commitment, skill level, and startup costs</li>
                        <li>Access detailed guides for each income method</li>
                        <li>Save opportunities to your personal shortlist</li>
                        <li>Track progress with different income streams</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Passive Income Portfolio') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Track multiple passive income streams</li>
                        <li>Visualize growth and performance over time</li>
                        <li>Set income goals and milestones</li>
                        <li>Receive suggestions for diversification</li>
                        <li>Monitor maintenance tasks for each income stream</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Freelance Project Manager') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Organize client information and projects</li>
                        <li>Track project deadlines and deliverables</li>
                        <li>Manage invoices and payments</li>
                        <li>Store portfolio samples and testimonials</li>
                        <li>Calculate hourly rates and project profitability</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Online Business Dashboard') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Monitor key business metrics in one place</li>
                        <li>Track revenue, expenses, and profit margins</li>
                        <li>Manage product/service inventory</li>
                        <li>Schedule marketing activities and content</li>
                        <li>Analyze customer data and trends</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Income Stream Evaluator') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Compare different income opportunities</li>
                        <li>Analyze ROI based on time and money invested</li>
                        <li>Assess opportunities against your personal skills and interests</li>
                        <li>Prioritize opportunities based on customizable criteria</li>
                        <li>Create action plans for pursuing selected opportunities</li>
                    </ul>
                ';
            }
        }

        // Related categories
        $relatedCategories = [
            [
                'title' => 'Financial Management',
                'description' => 'Visual budgeting, impulse control, and financial goals',
                'url' => '/momentum/help/financial-features'
            ],
            [
                'title' => 'Productivity Tools',
                'description' => 'Time blocking, energy tracking, and focus enhancement',
                'url' => '/momentum/help/productivity-features'
            ],
            [
                'title' => 'Analytics & Insights',
                'description' => 'Pattern recognition, productivity analytics, and progress visualization',
                'url' => '/momentum/help/analytics-features'
            ]
        ];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/feature-category.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the medical reports help page
     */
    public function medicalReports()
    {
        // Set the title
        $title = 'Medical Test Reports';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/medical-reports.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Get the implementation status of all features
     *
     * @return array The features with their implementation status
     */
    private function getFeatureStatus()
    {
        return [
            'core' => [
                [
                    'name' => 'Dashboard',
                    'description' => 'Central hub for accessing all system features',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Task Management',
                    'description' => 'Create, organize, and track tasks',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Project Management',
                    'description' => 'Manage complex projects with tasks and dependencies',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Notes',
                    'description' => 'Capture and organize thoughts and information',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Ideas',
                    'description' => 'Collect and develop creative ideas',
                    'status' => 'implemented'
                ]
            ],
            'adhd' => [
                [
                    'name' => 'ADHD Dashboard',
                    'description' => 'Overview of ADHD management tools',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Symptom Tracker',
                    'description' => 'Track and analyze ADHD symptoms',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Thought Records',
                    'description' => 'CBT-based thought recording and analysis',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Productivity Strategies',
                    'description' => 'ADHD-friendly productivity techniques',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Mindfulness Exercises',
                    'description' => 'Guided mindfulness practices',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Consistency Trackers',
                    'description' => 'Track daily habits and routines',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Medication Tracker',
                    'description' => 'Monitor medication usage and effectiveness',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Trigger Identification',
                    'description' => 'Identify and manage ADHD triggers',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Executive Function Exercises',
                    'description' => 'Activities to strengthen executive function',
                    'status' => 'implemented'
                ]
            ],
            'productivity' => [
                [
                    'name' => 'Focus Timer',
                    'description' => 'Pomodoro and other focus techniques',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Focus Mode',
                    'description' => 'Distraction-free work environment',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Time Blocking',
                    'description' => 'Visual scheduling system',
                    'status' => 'in-progress'
                ],
                [
                    'name' => 'Energy Level Tracking',
                    'description' => 'Monitor energy levels for optimal task scheduling',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Task Batching',
                    'description' => 'Group similar tasks for better focus',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Distraction Journal',
                    'description' => 'Log and analyze focus breakers',
                    'status' => 'concept'
                ]
            ],
            'finances' => [
                [
                    'name' => 'Expense Tracking',
                    'description' => 'Monitor spending by category',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Income Management',
                    'description' => 'Track income from multiple sources',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Budgeting',
                    'description' => 'Create and manage budgets',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Subscription Management',
                    'description' => 'Track recurring payments',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Debt Tracking',
                    'description' => 'Monitor debts (payable and receivable)',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Financial Goals',
                    'description' => 'Set and track financial objectives',
                    'status' => 'in-progress'
                ],
                [
                    'name' => 'Financial Reporting',
                    'description' => 'Analyze financial data',
                    'status' => 'in-progress'
                ],
                [
                    'name' => 'Impulse Purchase Tracker',
                    'description' => 'Monitor and reduce impulse spending',
                    'status' => 'planned'
                ]
            ],
            'new_features' => [
                // Mind Mapping
                [
                    'name' => 'Mind Mapping Tool',
                    'description' => 'Interactive mind map creator for visual organization of thoughts and projects',
                    'status' => 'planned'
                ],

                // Enhanced ADHD Management
                [
                    'name' => 'ADHD-Friendly Reading Mode',
                    'description' => 'Optimized reading experience for documents and articles',
                    'status' => 'concept'
                ],

                // Advanced Productivity Features
                [
                    'name' => 'Time Blocking Calendar',
                    'description' => 'Visual scheduling system for optimal time management',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Energy Level Tracking',
                    'description' => 'Monitor energy levels for optimal task scheduling',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Task Batching Assistant',
                    'description' => 'Group similar tasks for better focus and efficiency',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Distraction Journal',
                    'description' => 'Log and analyze what breaks your focus',
                    'status' => 'concept'
                ],

                // Wellness Integration
                [
                    'name' => 'Mood Tracker',
                    'description' => 'Monitor emotional states and correlate with productivity',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Sleep Quality Tracker',
                    'description' => 'Track sleep patterns and their effect on ADHD symptoms',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Exercise Logging',
                    'description' => 'Record physical activity with emphasis on ADHD benefits',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Nutrition Tracker',
                    'description' => 'Monitor diet with focus on brain-supporting foods',
                    'status' => 'concept'
                ],

                // Learning & Knowledge Management
                [
                    'name' => 'Spaced Repetition System',
                    'description' => 'Optimize learning and retention with timed review intervals',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Note-Taking Templates',
                    'description' => 'Structured formats designed for different learning styles',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Resource Library',
                    'description' => 'Organize articles, videos, and learning materials',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Concept Connection Visualizer',
                    'description' => 'Link related ideas across different topics',
                    'status' => 'concept'
                ],

                // Social & Relationship Management
                [
                    'name' => 'Social Battery Tracker',
                    'description' => 'Monitor and manage social energy levels',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Conversation Preparation Tools',
                    'description' => 'Prepare for important meetings and discussions',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Relationship Maintenance Reminders',
                    'description' => 'Stay connected with important people in your life',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Social Commitment Calendar',
                    'description' => 'Track social events with preparation reminders',
                    'status' => 'concept'
                ],

                // Advanced Financial Tools
                [
                    'name' => 'Visual Budget Planner',
                    'description' => 'ADHD-friendly visual interfaces for budget management',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Impulse Purchase Tracker',
                    'description' => 'Monitor impulse spending with cooling-off period reminders',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Financial Goal Visualization',
                    'description' => 'Visual progress tracking for financial objectives',
                    'status' => 'in-progress'
                ],
                [
                    'name' => 'Bill Payment System',
                    'description' => 'Manage bills with early reminders and auto-scheduling',
                    'status' => 'planned'
                ],

                // Environmental Management
                [
                    'name' => 'Home Organization Tracker',
                    'description' => 'Maintain organized spaces with visual tracking',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Cleaning Schedule Generator',
                    'description' => 'Create manageable cleaning tasks based on your needs',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Digital Decluttering Assistant',
                    'description' => 'Organize files, emails, and online accounts',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Environment Optimization',
                    'description' => 'Suggestions for creating ADHD-friendly spaces',
                    'status' => 'concept'
                ],

                // AI-Powered Assistance
                [
                    'name' => 'Personalized Strategy Recommendations',
                    'description' => 'AI-generated suggestions based on your patterns',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Task Prioritization Assistant',
                    'description' => 'AI help with prioritizing tasks based on your work style',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Predictive Reminders',
                    'description' => 'Smart reminders that anticipate when you might forget',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Natural Language Task Entry',
                    'description' => 'Create tasks using natural language input',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Smart Notification System',
                    'description' => 'Context-aware notifications that know when to alert you',
                    'status' => 'planned'
                ],

                // Advanced Analytics & Insights
                [
                    'name' => 'Pattern Recognition',
                    'description' => 'Identify trends across all your data',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Productivity Analytics',
                    'description' => 'Detailed analysis with personalized recommendations',
                    'status' => 'concept'
                ],
                [
                    'name' => 'ADHD Symptom Correlation',
                    'description' => 'Connect symptoms with activities, diet, sleep, etc.',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Progress Visualization',
                    'description' => 'Visual tracking across all areas of life management',
                    'status' => 'planned'
                ],

                // Gamification Elements
                [
                    'name' => 'Achievement System',
                    'description' => 'Earn achievements for building consistent habits',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Streak Tracking',
                    'description' => 'Visual rewards for maintaining consistent behavior',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Challenge System',
                    'description' => 'Gradually improve executive function through challenges',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Points and Rewards',
                    'description' => 'Earn points for completing difficult tasks',
                    'status' => 'concept'
                ],

                // Integration & Connectivity
                [
                    'name' => 'Calendar Integration',
                    'description' => 'Connect with popular calendar services',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Email Integration',
                    'description' => 'Create tasks from emails automatically',
                    'status' => 'concept'
                ],
                [
                    'name' => 'API Connections',
                    'description' => 'Connect with other productivity tools',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Data Import/Export',
                    'description' => 'Portability for all your important data',
                    'status' => 'planned'
                ],

                // Contact Management
                [
                    'name' => 'Contact Database',
                    'description' => 'Comprehensive storage for contact information',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Relationship Categorization',
                    'description' => 'Organize contacts by relationship type',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Contact Interaction History',
                    'description' => 'Track when you last communicated with someone',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Contact Groups',
                    'description' => 'Organize contacts by project, family, or other categories',
                    'status' => 'planned'
                ],

                // Resource Directory & Link Management
                [
                    'name' => 'Bookmark System',
                    'description' => 'Organized bookmarks with categories and tags',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Link Validation',
                    'description' => 'Check if saved links are still working',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Resource Rating',
                    'description' => 'Mark and sort resources by usefulness',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Reading List',
                    'description' => 'Prioritized list of resources to read/watch',
                    'status' => 'planned'
                ],

                // Workspace & Test Lab
                [
                    'name' => 'Virtual Workspace',
                    'description' => 'Customizable environments for different types of work',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Sandbox Environment',
                    'description' => 'Safe space for testing ideas and concepts',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Virtual Whiteboard',
                    'description' => 'Visual thinking and brainstorming tool',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Process Workflow Designer',
                    'description' => 'Map out and optimize procedures',
                    'status' => 'concept'
                ],

                // Knowledge Base & Documentation
                [
                    'name' => 'Personal Wiki',
                    'description' => 'Create your own knowledge base',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Documentation Templates',
                    'description' => 'Consistent formats for information capture',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Version History',
                    'description' => 'Track changes to your documents over time',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Tag-Based Organization',
                    'description' => 'Cross-reference information with flexible tagging',
                    'status' => 'planned'
                ],

                // Meeting & Communication Hub
                [
                    'name' => 'Meeting Preparation',
                    'description' => 'Templates and tools for effective meetings',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Meeting Notes System',
                    'description' => 'Capture and organize meeting notes with action items',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Follow-up Reminders',
                    'description' => 'Track commitments made in meetings',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Communication Scripts',
                    'description' => 'Templates for difficult or important conversations',
                    'status' => 'concept'
                ],

                // Digital Asset Management
                [
                    'name' => 'File Organization',
                    'description' => 'ADHD-friendly visual interfaces for file management',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Media Library',
                    'description' => 'Organize photos, videos, and audio files',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Document Versioning',
                    'description' => 'Track document changes with comparison tools',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Duplicate Finder',
                    'description' => 'Identify and remove duplicate files',
                    'status' => 'concept'
                ],

                // Health & Wellness Command Center
                [
                    'name' => 'Symptom Journal',
                    'description' => 'Track health concerns and patterns',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Health Metrics Dashboard',
                    'description' => 'Visualize trends in key health indicators',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Medical Record Organizer',
                    'description' => 'Keep important health information accessible',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Treatment Comparison',
                    'description' => 'Evaluate different approaches to health concerns',
                    'status' => 'concept'
                ],

                // Home & Environment Management
                [
                    'name' => 'Home Maintenance Scheduler',
                    'description' => 'Track seasonal tasks and home maintenance',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Inventory Management',
                    'description' => 'Track household items and supplies',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Service Provider Directory',
                    'description' => 'Organize contacts for home repairs and services',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Home Improvement Planner',
                    'description' => 'Plan and track home projects with budgeting',
                    'status' => 'concept'
                ],

                // Travel & Location Management
                [
                    'name' => 'Trip Planner',
                    'description' => 'Plan trips with itinerary building and sharing',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Packing List Generator',
                    'description' => 'Create packing lists based on trip details',
                    'status' => 'planned'
                ],
                [
                    'name' => 'Travel Document Organizer',
                    'description' => 'Keep important travel information accessible',
                    'status' => 'concept'
                ],
                [
                    'name' => 'Route Optimization',
                    'description' => 'Plan efficient routes for errands and trips',
                    'status' => 'concept'
                ],

                // Online Income Strategies
                [
                    'name' => 'Income Opportunity Explorer',
                    'description' => 'Browse and filter various online income methods',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Passive Income Portfolio',
                    'description' => 'Track and manage passive income streams',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Freelance Project Manager',
                    'description' => 'Organize freelance clients, projects, and payments',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Online Business Dashboard',
                    'description' => 'Monitor key metrics for online business ventures',
                    'status' => 'implemented'
                ],
                [
                    'name' => 'Income Stream Evaluator',
                    'description' => 'Compare and prioritize income opportunities',
                    'status' => 'implemented'
                ]
            ]
        ];
    }

    /**
     * Get all tutorials
     *
     * @return array The tutorials
     */
    private function getTutorials()
    {
        return [
            [
                'title' => 'Getting Started with Momentum',
                'description' => 'Learn the basics of using Momentum',
                'video_url' => '/momentum/public/videos/getting-started.mp4',
                'duration' => '5:30'
            ],
            [
                'title' => 'Task Management Essentials',
                'description' => 'Master the task management system',
                'video_url' => '/momentum/public/videos/task-management.mp4',
                'duration' => '8:45'
            ],
            [
                'title' => 'ADHD Symptom Tracking',
                'description' => 'How to effectively track and analyze ADHD symptoms',
                'video_url' => '/momentum/public/videos/symptom-tracking.mp4',
                'duration' => '7:15'
            ],
            [
                'title' => 'Financial Management Basics',
                'description' => 'Set up your financial tracking system',
                'video_url' => '/momentum/public/videos/financial-basics.mp4',
                'duration' => '10:20'
            ]
        ];
    }

    /**
     * Get all FAQs
     *
     * @return array The FAQs
     */
    private function getFaqs()
    {
        return [
            [
                'question' => 'How do I reset my password?',
                'answer' => 'Go to the login page and click "Forgot Password". Enter your email address and follow the instructions sent to your email.'
            ],
            [
                'question' => 'Can I use Momentum on my mobile device?',
                'answer' => 'Yes, Momentum is responsive and works on mobile devices. For the best experience, use a modern browser like Chrome, Firefox, or Safari.'
            ],
            [
                'question' => 'How do I export my data?',
                'answer' => 'Go to Settings > Data Management > Export Data. You can choose which data to export and in what format (CSV, JSON, or PDF).'
            ],
            [
                'question' => 'Is my data secure?',
                'answer' => 'Yes, Momentum uses encryption for all sensitive data and follows best practices for security. Your data is stored securely and is not shared with third parties.'
            ]
        ];
    }

    /**
     * Get all troubleshooting topics
     *
     * @return array The troubleshooting topics
     */
    private function getTroubleshootingTopics()
    {
        return [
            [
                'title' => 'Login Issues',
                'description' => 'Problems with logging in or accessing your account',
                'solutions' => [
                    'Check that you\'re using the correct email address and password',
                    'Try resetting your password',
                    'Clear your browser cache and cookies',
                    'Try using a different browser'
                ]
            ],
            [
                'title' => 'Data Not Saving',
                'description' => 'Issues with data not being saved properly',
                'solutions' => [
                    'Make sure you\'re clicking the Save button after making changes',
                    'Check your internet connection',
                    'Try refreshing the page',
                    'Clear your browser cache'
                ]
            ],
            [
                'title' => 'Features Not Loading',
                'description' => 'Problems with features or pages not loading correctly',
                'solutions' => [
                    'Refresh the page',
                    'Clear your browser cache',
                    'Try using a different browser',
                    'Check your internet connection'
                ]
            ],
            [
                'title' => 'Performance Issues',
                'description' => 'Slow performance or lag when using the system',
                'solutions' => [
                    'Close unused browser tabs',
                    'Clear your browser cache',
                    'Try using a different browser',
                    'Check your internet connection speed'
                ]
            ]
        ];
    }

    /**
     * Display the resource management features page
     */
    public function resourceFeatures()
    {
        // Set the title
        $title = 'Resource Management Features';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get feature data
        $allFeatures = $this->getFeatureStatus();

        // Set category information
        $categoryTitle = 'Resource Management';
        $categoryDescription = 'These resource management tools help you organize, access, and maintain your digital resources efficiently, designed with ADHD-friendly interfaces.';

        // Filter features for this category
        $features = array_filter($allFeatures['new_features'], function($feature) {
            return strpos($feature['name'], 'Bookmark') !== false ||
                   strpos($feature['name'], 'Link') !== false ||
                   strpos($feature['name'], 'Reading List') !== false ||
                   strpos($feature['name'], 'Resource Library') !== false;
        });

        // Add more detailed information to features
        foreach ($features as &$feature) {
            if ($feature['name'] === 'Bookmark System') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Organize bookmarks with categories and tags</li>
                        <li>Visual bookmark tiles with previews</li>
                        <li>Search and filter capabilities</li>
                        <li>Quick access to frequently used resources</li>
                        <li>Browser extension for easy saving</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Link Validation Tool') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Automatically check for broken links</li>
                        <li>Receive notifications about dead links</li>
                        <li>Find alternative sources for broken links</li>
                        <li>Archive important web pages locally</li>
                    </ul>
                ';
            }
        }

        // Related categories
        $relatedCategories = [
            [
                'title' => 'Knowledge Management',
                'description' => 'Note-taking templates and resource organization',
                'url' => '/momentum/help/knowledge-features'
            ],
            [
                'title' => 'Digital Asset Management',
                'description' => 'File organization, media library, and document versioning',
                'url' => '/momentum/help/digital-asset-features'
            ],
            [
                'title' => 'Mind Mapping',
                'description' => 'Visual organization tools for thoughts and projects',
                'url' => '/momentum/help/mind-mapping-features'
            ]
        ];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/feature-category.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the digital asset management features page
     */
    public function digitalAssetFeatures()
    {
        // Set the title
        $title = 'Digital Asset Management Features';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get feature data
        $allFeatures = $this->getFeatureStatus();

        // Set category information
        $categoryTitle = 'Digital Asset Management';
        $categoryDescription = 'These digital asset management tools help you organize, find, and maintain your files and media with ADHD-friendly interfaces that reduce overwhelm.';

        // Filter features for this category
        $features = array_filter($allFeatures['new_features'], function($feature) {
            return strpos($feature['name'], 'File Organization') !== false ||
                   strpos($feature['name'], 'Media Library') !== false ||
                   strpos($feature['name'], 'Document Version') !== false ||
                   strpos($feature['name'], 'Digital Asset') !== false;
        });

        // Add more detailed information to features
        foreach ($features as &$feature) {
            if ($feature['name'] === 'File Organization System') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Visual file organization with thumbnails</li>
                        <li>Tag-based system that works with your brain</li>
                        <li>Powerful search capabilities</li>
                        <li>Automatic file categorization</li>
                        <li>Duplicate file detection</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Media Library') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Organize images, videos, and audio files</li>
                        <li>Preview media without downloading</li>
                        <li>Tag and categorize for easy retrieval</li>
                        <li>Integration with note-taking and projects</li>
                    </ul>
                ';
            }
        }

        // Related categories
        $relatedCategories = [
            [
                'title' => 'Knowledge Management',
                'description' => 'Note-taking templates and resource organization',
                'url' => '/momentum/help/knowledge-features'
            ],
            [
                'title' => 'Resource Management',
                'description' => 'Bookmark system, link validation, and reading list',
                'url' => '/momentum/help/resource-features'
            ],
            [
                'title' => 'Project Management',
                'description' => 'Task dependencies, project templates, and collaboration',
                'url' => '/momentum/help/project-features'
            ]
        ];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/feature-category.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the health features page
     */
    public function healthFeatures()
    {
        // Set the title
        $title = 'Health Features';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get feature data
        $allFeatures = $this->getFeatureStatus();

        // Set category information
        $categoryTitle = 'Health Features';
        $categoryDescription = 'These health management tools help you track symptoms, manage medical information, and maintain health metrics in an ADHD-friendly way.';

        // Filter features for this category
        $features = array_filter($allFeatures['new_features'], function($feature) {
            return strpos($feature['name'], 'Symptom Journal') !== false ||
                   strpos($feature['name'], 'Health Metric') !== false ||
                   strpos($feature['name'], 'Medical Record') !== false ||
                   strpos($feature['name'], 'Treatment Comparison') !== false;
        });

        // Add more detailed information to features
        foreach ($features as &$feature) {
            if ($feature['name'] === 'Symptom Journal') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Track physical and mental health symptoms</li>
                        <li>Identify patterns and triggers</li>
                        <li>Generate reports for healthcare providers</li>
                        <li>Set reminders for symptom check-ins</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Health Metrics Dashboard') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Track key health indicators</li>
                        <li>Visualize trends over time</li>
                        <li>Set goals and track progress</li>
                        <li>Receive insights about health patterns</li>
                    </ul>
                ';
            }
        }

        // Related categories
        $relatedCategories = [
            [
                'title' => 'ADHD Management',
                'description' => 'Tools for symptom tracking, CBT, and executive function',
                'url' => '/momentum/help/adhd-features'
            ],
            [
                'title' => 'Wellness Integration',
                'description' => 'Mood tracking, sleep quality, and exercise logging',
                'url' => '/momentum/help/wellness-features'
            ],
            [
                'title' => 'Analytics & Insights',
                'description' => 'Pattern recognition, productivity analytics, and progress visualization',
                'url' => '/momentum/help/analytics-features'
            ]
        ];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/feature-category.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Show freelance features page
     */
    public function freelanceFeatures()
    {
        // Set the title
        $title = 'Freelance Project Management';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/freelance-features.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Show online business dashboard features page
     */
    public function onlineBusinessFeatures()
    {
        // Set the title
        $title = 'Online Business Dashboard';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/online-business-features.php';
        $content = ob_get_clean();

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the analytics features page
     */
    public function analyticsFeatures()
    {
        // Set the title
        $title = 'Analytics & Insights Features';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get feature data
        $allFeatures = $this->getFeatureStatus();

        // Set category information
        $categoryTitle = 'Analytics & Insights';
        $categoryDescription = 'These analytics tools help you identify patterns, visualize progress, and gain insights across all aspects of your life, designed with ADHD-friendly interfaces.';

        // Filter features for this category
        $features = array_filter($allFeatures['new_features'], function($feature) {
            return strpos($feature['name'], 'Pattern Recognition') !== false ||
                   strpos($feature['name'], 'Productivity Analytics') !== false ||
                   strpos($feature['name'], 'Progress Visualization') !== false ||
                   strpos($feature['name'], 'Insight') !== false;
        });

        // Add more detailed information to features
        foreach ($features as &$feature) {
            if ($feature['name'] === 'Pattern Recognition') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Identify trends across all your data</li>
                        <li>Discover connections between different areas of your life</li>
                        <li>Receive personalized insights</li>
                        <li>Visualize patterns with interactive charts</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Productivity Analytics') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Track productivity metrics over time</li>
                        <li>Identify your most productive times and conditions</li>
                        <li>Compare different productivity strategies</li>
                        <li>Set goals based on data-driven insights</li>
                    </ul>
                ';
            }
        }

        // Related categories
        $relatedCategories = [
            [
                'title' => 'ADHD Management',
                'description' => 'Tools for symptom tracking, CBT, and executive function',
                'url' => '/momentum/help/adhd-features'
            ],
            [
                'title' => 'Financial Management',
                'description' => 'Visual budgeting, impulse control, and financial goals',
                'url' => '/momentum/help/financial-features'
            ],
            [
                'title' => 'Health Features',
                'description' => 'Symptom journal, health metrics, and medical records',
                'url' => '/momentum/help/health-features'
            ]
        ];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/feature-category.php';
        $content = ob_get_clean();

        // Extract variables to be used in the layout
        extract([
            'title' => $title,
            'darkMode' => $darkMode,
            'content' => $content,
            'categoryTitle' => $categoryTitle,
            'categoryDescription' => $categoryDescription,
            'features' => $features,
            'relatedCategories' => $relatedCategories
        ]);

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the AI assistance features page
     */
    public function aiAssistanceFeatures()
    {
        // Set the title
        $title = 'AI Assistance Features';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get feature data
        $allFeatures = $this->getFeatureStatus();

        // Set category information
        $categoryTitle = 'AI-Powered Assistance';
        $categoryDescription = 'These AI-powered tools provide personalized assistance, helping you make better decisions, prioritize tasks, and identify patterns in your data, all designed with ADHD-friendly interfaces.';

        // Filter features for this category
        $features = array_filter($allFeatures['new_features'], function($feature) {
            return strpos($feature['name'], 'AI') !== false ||
                   strpos($feature['name'], 'Machine Learning') !== false ||
                   strpos($feature['name'], 'Personalized') !== false ||
                   strpos($feature['name'], 'Smart') !== false ||
                   strpos($feature['name'], 'Intelligent') !== false ||
                   strpos($feature['name'], 'Recommendation') !== false ||
                   strpos($feature['name'], 'Pattern Recognition') !== false;
        });

        // Add more detailed information to features
        foreach ($features as &$feature) {
            if ($feature['name'] === 'Personalized Strategy Recommendations') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>AI-generated suggestions based on your patterns</li>
                        <li>Personalized productivity strategies</li>
                        <li>Adaptive recommendations that evolve with you</li>
                        <li>Evidence-based approaches for ADHD management</li>
                        <li>Customized workflow optimization</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Task Prioritization Assistant') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>AI help with prioritizing tasks based on your work style</li>
                        <li>Smart deadline management</li>
                        <li>Energy level-based task scheduling</li>
                        <li>Context-aware task suggestions</li>
                        <li>Automatic identification of high-impact tasks</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Pattern Recognition') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Identify trends across all your data</li>
                        <li>Discover connections between different areas of your life</li>
                        <li>Receive personalized insights</li>
                        <li>Visualize patterns with interactive charts</li>
                        <li>Early detection of productivity blockers</li>
                    </ul>
                ';
            } else if ($feature['name'] === 'Smart Notification System') {
                $feature['details'] = '
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Context-aware notifications that know when to alert you</li>
                        <li>Adaptive reminder timing based on your responsiveness</li>
                        <li>Priority-based notification filtering</li>
                        <li>Distraction-minimizing delivery methods</li>
                        <li>Gentle escalation for important reminders</li>
                    </ul>
                ';
            }
        }

        // If no features were found, add some manually
        if (empty($features)) {
            $features = [
                [
                    'name' => 'Personalized Strategy Recommendations',
                    'description' => 'AI-generated suggestions based on your patterns',
                    'status' => 'concept',
                    'details' => '
                        <ul class="list-disc pl-5 space-y-1">
                            <li>AI-generated suggestions based on your patterns</li>
                            <li>Personalized productivity strategies</li>
                            <li>Adaptive recommendations that evolve with you</li>
                            <li>Evidence-based approaches for ADHD management</li>
                            <li>Customized workflow optimization</li>
                        </ul>
                    '
                ],
                [
                    'name' => 'Task Prioritization Assistant',
                    'description' => 'AI help with prioritizing tasks based on your work style',
                    'status' => 'concept',
                    'details' => '
                        <ul class="list-disc pl-5 space-y-1">
                            <li>AI help with prioritizing tasks based on your work style</li>
                            <li>Smart deadline management</li>
                            <li>Energy level-based task scheduling</li>
                            <li>Context-aware task suggestions</li>
                            <li>Automatic identification of high-impact tasks</li>
                        </ul>
                    '
                ],
                [
                    'name' => 'Pattern Recognition',
                    'description' => 'Identify trends across all your data',
                    'status' => 'concept',
                    'details' => '
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Identify trends across all your data</li>
                            <li>Discover connections between different areas of your life</li>
                            <li>Receive personalized insights</li>
                            <li>Visualize patterns with interactive charts</li>
                            <li>Early detection of productivity blockers</li>
                        </ul>
                    '
                ],
                [
                    'name' => 'Smart Notification System',
                    'description' => 'Context-aware notifications that know when to alert you',
                    'status' => 'planned',
                    'details' => '
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Context-aware notifications that know when to alert you</li>
                            <li>Adaptive reminder timing based on your responsiveness</li>
                            <li>Priority-based notification filtering</li>
                            <li>Distraction-minimizing delivery methods</li>
                            <li>Gentle escalation for important reminders</li>
                        </ul>
                    '
                ]
            ];
        }

        // Related categories
        $relatedCategories = [
            [
                'title' => 'ADHD Management',
                'description' => 'Tools for symptom tracking, CBT, and executive function',
                'url' => '/momentum/help/adhd-features'
            ],
            [
                'title' => 'Productivity',
                'description' => 'Task management, time tracking, and focus tools',
                'url' => '/momentum/help/productivity-features'
            ],
            [
                'title' => 'Analytics & Insights',
                'description' => 'Pattern recognition, productivity analytics, and progress visualization',
                'url' => '/momentum/help/analytics-features'
            ]
        ];

        // Include the view file directly
        ob_start();
        include dirname(__DIR__) . '/views/help/feature-category.php';
        $content = ob_get_clean();

        // Extract variables to be used in the layout
        extract([
            'title' => $title,
            'darkMode' => $darkMode,
            'content' => $content,
            'categoryTitle' => $categoryTitle,
            'categoryDescription' => $categoryDescription,
            'features' => $features,
            'relatedCategories' => $relatedCategories
        ]);

        // Include the layout
        include dirname(__DIR__) . '/views/layouts/default.php';
    }


}
