<?php
/**
 * Report Controller
 *
 * Handles project reporting functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Project.php';
require_once __DIR__ . '/../models/Task.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Category.php';

class ReportController extends BaseController {
    private $projectModel;
    private $taskModel;
    private $userModel;
    private $categoryModel;

    public function __construct() {
        $this->projectModel = new Project();
        $this->taskModel = new Task();
        $this->userModel = new User();
        $this->categoryModel = new Category();
    }

    /**
     * Show project reports dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get active projects for the user
        $activeProjects = $this->projectModel->getUserProjects($userId, [
            'status' => ['planning', 'in_progress', 'on_hold'],
            'is_template' => 0
        ]);

        $this->view('reports/index', [
            'activeProjects' => $activeProjects
        ]);
    }

    /**
     * Generate project progress report
     */
    public function projectProgress($projectId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project details
        $project = $this->projectModel->getProjectDetails($projectId, $userId);

        // Verify project exists and belongs to user
        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/reports');
            return;
        }

        // Get project tasks
        $tasks = $this->taskModel->getProjectTasks($projectId);

        // Get task completion history
        $taskCompletionHistory = $this->taskModel->getTaskCompletionHistory($projectId);

        // Calculate task status distribution
        $taskStatusCounts = [
            'todo' => 0,
            'in_progress' => 0,
            'done' => 0
        ];

        foreach ($tasks as $task) {
            $taskStatusCounts[$task['status']]++;
        }

        // Calculate task priority distribution
        $taskPriorityCounts = [
            'low' => 0,
            'medium' => 0,
            'high' => 0,
            'urgent' => 0
        ];

        foreach ($tasks as $task) {
            $taskPriorityCounts[$task['priority']]++;
        }

        // Calculate task category distribution
        $taskCategoryCounts = [];
        $categories = $this->categoryModel->getUserCategories($userId);
        $categoryMap = [];

        foreach ($categories as $category) {
            $categoryMap[$category['id']] = $category['name'];
            $taskCategoryCounts[$category['name']] = 0;
        }
        $taskCategoryCounts['Uncategorized'] = 0;

        foreach ($tasks as $task) {
            if (!empty($task['category_id']) && isset($categoryMap[$task['category_id']])) {
                $categoryName = $categoryMap[$task['category_id']];
                $taskCategoryCounts[$categoryName]++;
            } else {
                $taskCategoryCounts['Uncategorized']++;
            }
        }

        // Calculate progress over time
        $progressOverTime = [];
        $startDate = !empty($project['start_date']) ? new DateTime($project['start_date']) : new DateTime('-30 days');
        $endDate = !empty($project['end_date']) ? new DateTime($project['end_date']) : new DateTime();
        $today = new DateTime();

        if ($endDate > $today) {
            $endDate = $today;
        }

        $interval = new DateInterval('P1D');
        $dateRange = new DatePeriod($startDate, $interval, $endDate);

        $completedTasksByDate = [];
        foreach ($taskCompletionHistory as $history) {
            $date = date('Y-m-d', strtotime($history['completed_at']));
            if (!isset($completedTasksByDate[$date])) {
                $completedTasksByDate[$date] = 0;
            }
            $completedTasksByDate[$date]++;
        }

        $cumulativeCompleted = 0;
        $totalTasks = count($tasks);

        foreach ($dateRange as $date) {
            $dateStr = $date->format('Y-m-d');
            if (isset($completedTasksByDate[$dateStr])) {
                $cumulativeCompleted += $completedTasksByDate[$dateStr];
            }
            $progressOverTime[$dateStr] = $totalTasks > 0 ? round(($cumulativeCompleted / $totalTasks) * 100) : 0;
        }

        $this->view('reports/project_progress', [
            'project' => $project,
            'tasks' => $tasks,
            'taskStatusCounts' => $taskStatusCounts,
            'taskPriorityCounts' => $taskPriorityCounts,
            'taskCategoryCounts' => $taskCategoryCounts,
            'progressOverTime' => $progressOverTime
        ]);
    }

    /**
     * Generate team member workload report
     */
    public function teamWorkload($projectId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project details
        $project = $this->projectModel->getProjectDetails($projectId, $userId);

        // Verify project exists and belongs to user
        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/reports');
            return;
        }

        // Get project members
        $projectMembers = $this->projectModel->getProjectMembers($projectId);

        // Get project tasks
        $tasks = $this->taskModel->getProjectTasks($projectId);

        // Calculate task distribution by member
        $memberTaskCounts = [];
        $memberTaskStatusCounts = [];

        foreach ($projectMembers as $member) {
            $memberId = $member['user_id'];
            $memberName = $member['name'];
            $memberTaskCounts[$memberId] = [
                'name' => $memberName,
                'total' => 0,
                'completed' => 0
            ];
            $memberTaskStatusCounts[$memberId] = [
                'name' => $memberName,
                'todo' => 0,
                'in_progress' => 0,
                'done' => 0
            ];
        }

        // Add project owner if not already in members
        if (!isset($memberTaskCounts[$userId])) {
            $memberTaskCounts[$userId] = [
                'name' => $user['name'],
                'total' => 0,
                'completed' => 0
            ];
            $memberTaskStatusCounts[$userId] = [
                'name' => $user['name'],
                'todo' => 0,
                'in_progress' => 0,
                'done' => 0
            ];
        }

        foreach ($tasks as $task) {
            $taskUserId = $task['user_id'];

            // Skip if user is not a project member
            if (!isset($memberTaskCounts[$taskUserId])) {
                continue;
            }

            $memberTaskCounts[$taskUserId]['total']++;

            if ($task['status'] === 'done') {
                $memberTaskCounts[$taskUserId]['completed']++;
            }

            $memberTaskStatusCounts[$taskUserId][$task['status']]++;
        }

        $this->view('reports/team_workload', [
            'project' => $project,
            'projectMembers' => $projectMembers,
            'tasks' => $tasks,
            'memberTaskCounts' => $memberTaskCounts,
            'memberTaskStatusCounts' => $memberTaskStatusCounts
        ]);
    }

    /**
     * Generate deadline compliance report
     */
    public function deadlineCompliance($projectId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project details
        $project = $this->projectModel->getProjectDetails($projectId, $userId);

        // Verify project exists and belongs to user
        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/reports');
            return;
        }

        // Get project tasks with due dates
        $tasks = $this->taskModel->getProjectTasksWithDueDates($projectId);

        // Calculate deadline compliance
        $onTime = 0;
        $late = 0;
        $pending = 0;
        $noDueDate = 0;
        $upcomingDeadlines = [];
        $overdueDeadlines = [];

        $today = new DateTime();

        foreach ($tasks as $task) {
            if (empty($task['due_date'])) {
                $noDueDate++;
                continue;
            }

            $dueDate = new DateTime($task['due_date']);
            $completedDate = !empty($task['completed_at']) ? new DateTime($task['completed_at']) : null;

            if ($task['status'] === 'done') {
                if ($completedDate <= $dueDate) {
                    $onTime++;
                } else {
                    $late++;
                }
            } else {
                $pending++;

                if ($dueDate < $today) {
                    $overdueDeadlines[] = $task;
                } else {
                    // Only include upcoming deadlines within the next 7 days
                    $daysDiff = $today->diff($dueDate)->days;
                    if ($daysDiff <= 7) {
                        $upcomingDeadlines[] = $task;
                    }
                }
            }
        }

        // Sort upcoming deadlines by due date (ascending)
        usort($upcomingDeadlines, function($a, $b) {
            return strtotime($a['due_date']) - strtotime($b['due_date']);
        });

        // Sort overdue deadlines by due date (ascending)
        usort($overdueDeadlines, function($a, $b) {
            return strtotime($a['due_date']) - strtotime($b['due_date']);
        });

        $this->view('reports/deadline_compliance', [
            'project' => $project,
            'tasks' => $tasks,
            'onTime' => $onTime,
            'late' => $late,
            'pending' => $pending,
            'noDueDate' => $noDueDate,
            'upcomingDeadlines' => $upcomingDeadlines,
            'overdueDeadlines' => $overdueDeadlines
        ]);
    }

    /**
     * Export project progress report as CSV
     */
    public function exportProjectProgressCSV($projectId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project details
        $project = $this->projectModel->getProjectDetails($projectId, $userId);

        // Verify project exists and belongs to user
        if (!$project) {
            $this->json(['success' => false, 'message' => 'Project not found'], 404);
            return;
        }

        // Get project tasks
        $tasks = $this->taskModel->getProjectTasks($projectId);

        // Prepare data for CSV
        $headers = ['Task ID', 'Title', 'Status', 'Priority', 'Due Date', 'Created At', 'Completed At'];
        $data = [];

        foreach ($tasks as $task) {
            $data[] = [
                $task['id'],
                $task['title'],
                $task['status'],
                $task['priority'],
                $task['due_date'] ?? 'N/A',
                $task['created_at'],
                $task['completed_at'] ?? 'N/A'
            ];
        }

        // Send CSV response
        require_once __DIR__ . '/../utils/ExportUtil.php';
        ExportUtil::sendCSV($project['name'] . ' - Progress Report', $headers, $data);
    }

    /**
     * Export project progress report as PDF
     */
    public function exportProjectProgressPDF($projectId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project details
        $project = $this->projectModel->getProjectDetails($projectId, $userId);

        // Verify project exists and belongs to user
        if (!$project) {
            $this->json(['success' => false, 'message' => 'Project not found'], 404);
            return;
        }

        // Get project tasks
        $tasks = $this->taskModel->getProjectTasks($projectId);

        // Calculate task status distribution
        $taskStatusCounts = [
            'todo' => 0,
            'in_progress' => 0,
            'done' => 0
        ];

        foreach ($tasks as $task) {
            $taskStatusCounts[$task['status']]++;
        }

        // Calculate task priority distribution
        $taskPriorityCounts = [
            'low' => 0,
            'medium' => 0,
            'high' => 0,
            'urgent' => 0
        ];

        foreach ($tasks as $task) {
            $taskPriorityCounts[$task['priority']]++;
        }

        // Calculate task category distribution
        $taskCategoryCounts = [];
        $categories = $this->categoryModel->getUserCategories($userId);
        $categoryMap = [];

        foreach ($categories as $category) {
            $categoryMap[$category['id']] = $category['name'];
            $taskCategoryCounts[$category['name']] = 0;
        }
        $taskCategoryCounts['Uncategorized'] = 0;

        foreach ($tasks as $task) {
            if (!empty($task['category_id']) && isset($categoryMap[$task['category_id']])) {
                $categoryName = $categoryMap[$task['category_id']];
                $taskCategoryCounts[$categoryName]++;
            } else {
                $taskCategoryCounts['Uncategorized']++;
            }
        }

        // Generate PDF
        require_once __DIR__ . '/../utils/PDFExporter.php';
        $html = PDFExporter::generateProjectProgressHTML($project, $tasks, $taskStatusCounts, $taskPriorityCounts, $taskCategoryCounts);
        PDFExporter::generatePDF($html, $project['name'] . ' - Progress Report');
    }

    /**
     * Export team workload report as CSV
     */
    public function exportTeamWorkloadCSV($projectId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project details
        $project = $this->projectModel->getProjectDetails($projectId, $userId);

        // Verify project exists and belongs to user
        if (!$project) {
            $this->json(['success' => false, 'message' => 'Project not found'], 404);
            return;
        }

        // Get project members
        $projectMembers = $this->projectModel->getProjectMembers($projectId);

        // Get project tasks
        $tasks = $this->taskModel->getProjectTasks($projectId);

        // Calculate task distribution by member
        $memberTaskCounts = [];

        foreach ($projectMembers as $member) {
            $memberId = $member['user_id'];
            $memberName = $member['name'];
            $memberTaskCounts[$memberId] = [
                'name' => $memberName,
                'total' => 0,
                'todo' => 0,
                'in_progress' => 0,
                'done' => 0
            ];
        }

        // Add project owner if not already in members
        if (!isset($memberTaskCounts[$userId])) {
            $memberTaskCounts[$userId] = [
                'name' => $user['name'],
                'total' => 0,
                'todo' => 0,
                'in_progress' => 0,
                'done' => 0
            ];
        }

        foreach ($tasks as $task) {
            $taskUserId = $task['user_id'];

            // Skip if user is not a project member
            if (!isset($memberTaskCounts[$taskUserId])) {
                continue;
            }

            $memberTaskCounts[$taskUserId]['total']++;
            $memberTaskCounts[$taskUserId][$task['status']]++;
        }

        // Prepare data for CSV
        $headers = ['Member Name', 'Total Tasks', 'To Do', 'In Progress', 'Done', 'Completion Rate'];
        $data = [];

        foreach ($memberTaskCounts as $memberId => $stats) {
            $completionRate = $stats['total'] > 0 ? round(($stats['done'] / $stats['total']) * 100) : 0;
            $data[] = [
                $stats['name'],
                $stats['total'],
                $stats['todo'],
                $stats['in_progress'],
                $stats['done'],
                $completionRate . '%'
            ];
        }

        // Send CSV response
        require_once __DIR__ . '/../utils/ExportUtil.php';
        ExportUtil::sendCSV($project['name'] . ' - Team Workload Report', $headers, $data);
    }

    /**
     * Export team workload report as PDF
     */
    public function exportTeamWorkloadPDF($projectId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project details
        $project = $this->projectModel->getProjectDetails($projectId, $userId);

        // Verify project exists and belongs to user
        if (!$project) {
            $this->json(['success' => false, 'message' => 'Project not found'], 404);
            return;
        }

        // Get project members
        $projectMembers = $this->projectModel->getProjectMembers($projectId);

        // Get project tasks
        $tasks = $this->taskModel->getProjectTasks($projectId);

        // Calculate task distribution by member
        $memberTaskCounts = [];
        $memberTaskStatusCounts = [];

        foreach ($projectMembers as $member) {
            $memberId = $member['user_id'];
            $memberName = $member['name'];
            $memberTaskCounts[$memberId] = [
                'name' => $memberName,
                'total' => 0,
                'completed' => 0
            ];
            $memberTaskStatusCounts[$memberId] = [
                'name' => $memberName,
                'todo' => 0,
                'in_progress' => 0,
                'done' => 0
            ];
        }

        // Add project owner if not already in members
        if (!isset($memberTaskCounts[$userId])) {
            $memberTaskCounts[$userId] = [
                'name' => $user['name'],
                'total' => 0,
                'completed' => 0
            ];
            $memberTaskStatusCounts[$userId] = [
                'name' => $user['name'],
                'todo' => 0,
                'in_progress' => 0,
                'done' => 0
            ];
        }

        foreach ($tasks as $task) {
            $taskUserId = $task['user_id'];

            // Skip if user is not a project member
            if (!isset($memberTaskCounts[$taskUserId])) {
                continue;
            }

            $memberTaskCounts[$taskUserId]['total']++;

            if ($task['status'] === 'done') {
                $memberTaskCounts[$taskUserId]['completed']++;
            }

            $memberTaskStatusCounts[$taskUserId][$task['status']]++;
        }

        // Generate HTML for PDF
        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Team Workload Report</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    color: #333;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                h1 {
                    font-size: 24px;
                    margin: 0 0 5px 0;
                }
                h2 {
                    font-size: 18px;
                    margin: 0 0 20px 0;
                    font-weight: normal;
                    color: #666;
                }
                .section {
                    margin-bottom: 30px;
                }
                .section-title {
                    font-size: 18px;
                    margin-bottom: 10px;
                    padding-bottom: 5px;
                    border-bottom: 1px solid #ddd;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                table, th, td {
                    border: 1px solid #ddd;
                }
                th, td {
                    padding: 8px;
                    text-align: left;
                }
                th {
                    background-color: #f2f2f2;
                }
                .progress-bar {
                    background-color: #f0f0f0;
                    height: 20px;
                    border-radius: 10px;
                    margin-bottom: 10px;
                    overflow: hidden;
                }
                .progress-bar-fill {
                    background-color: #4f46e5;
                    height: 100%;
                }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Team Workload Report</h1>
                <h2>' . htmlspecialchars($project['name']) . '</h2>
                <p>Generated on ' . date('F j, Y, g:i a') . '</p>
            </div>

            <div class="section">
                <div class="section-title">Team Overview</div>
                <table>
                    <tr>
                        <th>Team Member</th>
                        <th>Total Tasks</th>
                        <th>To Do</th>
                        <th>In Progress</th>
                        <th>Done</th>
                        <th>Completion Rate</th>
                    </tr>';

        foreach ($memberTaskCounts as $memberId => $stats) {
            $completionRate = $stats['total'] > 0 ? round(($stats['completed'] / $stats['total']) * 100) : 0;
            $statusCounts = $memberTaskStatusCounts[$memberId];

            $html .= '
                    <tr>
                        <td>' . htmlspecialchars($stats['name']) . '</td>
                        <td>' . $stats['total'] . '</td>
                        <td>' . $statusCounts['todo'] . '</td>
                        <td>' . $statusCounts['in_progress'] . '</td>
                        <td>' . $statusCounts['done'] . '</td>
                        <td>
                            <div class="progress-bar">
                                <div class="progress-bar-fill" style="width: ' . $completionRate . '%;"></div>
                            </div>
                            ' . $completionRate . '%
                        </td>
                    </tr>';
        }

        $html .= '
                </table>
            </div>

            <div class="footer">
                <p>Generated by Momentum Project Management System</p>
            </div>
        </body>
        </html>';

        // Generate PDF
        require_once __DIR__ . '/../utils/PDFExporter.php';
        PDFExporter::generatePDF($html, $project['name'] . ' - Team Workload Report');
    }

    /**
     * Export deadline compliance report as CSV
     */
    public function exportDeadlineComplianceCSV($projectId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project details
        $project = $this->projectModel->getProjectDetails($projectId, $userId);

        // Verify project exists and belongs to user
        if (!$project) {
            $this->json(['success' => false, 'message' => 'Project not found'], 404);
            return;
        }

        // Get project tasks with due dates
        $tasks = $this->taskModel->getProjectTasksWithDueDates($projectId);

        // Calculate deadline compliance
        $onTime = 0;
        $late = 0;
        $pending = 0;
        $noDueDate = 0;
        $upcomingDeadlines = [];
        $overdueDeadlines = [];

        $today = new DateTime();

        foreach ($tasks as $task) {
            if (empty($task['due_date'])) {
                $noDueDate++;
                continue;
            }

            $dueDate = new DateTime($task['due_date']);
            $completedDate = !empty($task['completed_at']) ? new DateTime($task['completed_at']) : null;

            if ($task['status'] === 'done') {
                if ($completedDate <= $dueDate) {
                    $onTime++;
                } else {
                    $late++;
                }
            } else {
                $pending++;

                if ($dueDate < $today) {
                    $overdueDeadlines[] = $task;
                } else {
                    // Only include upcoming deadlines within the next 7 days
                    $daysDiff = $today->diff($dueDate)->days;
                    if ($daysDiff <= 7) {
                        $upcomingDeadlines[] = $task;
                    }
                }
            }
        }

        // Prepare summary data
        $summaryHeaders = ['Metric', 'Value'];
        $summaryData = [
            ['On-Time Completions', $onTime],
            ['Late Completions', $late],
            ['Pending Tasks with Due Dates', $pending],
            ['Tasks without Due Dates', $noDueDate],
            ['Overdue Tasks', count($overdueDeadlines)],
            ['Upcoming Deadlines (Next 7 Days)', count($upcomingDeadlines)]
        ];

        // Prepare task data
        $taskHeaders = ['Task ID', 'Title', 'Status', 'Priority', 'Due Date', 'Days Overdue/Left', 'Completed At'];
        $taskData = [];

        // Add overdue tasks
        foreach ($overdueDeadlines as $task) {
            $dueDate = new DateTime($task['due_date']);
            $today = new DateTime();
            $daysOverdue = $today->diff($dueDate)->days;

            $taskData[] = [
                $task['id'],
                $task['title'],
                $task['status'],
                $task['priority'],
                $task['due_date'],
                $daysOverdue . ' days overdue',
                $task['completed_at'] ?? 'N/A'
            ];
        }

        // Add upcoming tasks
        foreach ($upcomingDeadlines as $task) {
            $dueDate = new DateTime($task['due_date']);
            $today = new DateTime();
            $daysLeft = $today->diff($dueDate)->days;

            $taskData[] = [
                $task['id'],
                $task['title'],
                $task['status'],
                $task['priority'],
                $task['due_date'],
                $daysLeft . ' days left',
                $task['completed_at'] ?? 'N/A'
            ];
        }

        // Combine data
        $headers = array_merge(['Summary'], array_fill(0, count($summaryHeaders) - 1, ''), [''], ['Tasks'], array_fill(0, count($taskHeaders) - 1, ''));
        $data = [array_merge($summaryHeaders, [''], $taskHeaders)];

        foreach ($summaryData as $i => $row) {
            if (isset($taskData[$i])) {
                $data[] = array_merge($row, [''], $taskData[$i]);
            } else {
                $data[] = array_merge($row, [''], array_fill(0, count($taskHeaders), ''));
            }
        }

        for ($i = count($summaryData); $i < count($taskData); $i++) {
            $data[] = array_merge(array_fill(0, count($summaryHeaders) + 1, ''), $taskData[$i]);
        }

        // Send CSV response
        require_once __DIR__ . '/../utils/ExportUtil.php';
        ExportUtil::sendCSV($project['name'] . ' - Deadline Compliance Report', $headers, $data);
    }

    /**
     * Export deadline compliance report as PDF
     */
    public function exportDeadlineCompliancePDF($projectId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project details
        $project = $this->projectModel->getProjectDetails($projectId, $userId);

        // Verify project exists and belongs to user
        if (!$project) {
            $this->json(['success' => false, 'message' => 'Project not found'], 404);
            return;
        }

        // Get project tasks with due dates
        $tasks = $this->taskModel->getProjectTasksWithDueDates($projectId);

        // Calculate deadline compliance
        $onTime = 0;
        $late = 0;
        $pending = 0;
        $noDueDate = 0;
        $upcomingDeadlines = [];
        $overdueDeadlines = [];

        $today = new DateTime();

        foreach ($tasks as $task) {
            if (empty($task['due_date'])) {
                $noDueDate++;
                continue;
            }

            $dueDate = new DateTime($task['due_date']);
            $completedDate = !empty($task['completed_at']) ? new DateTime($task['completed_at']) : null;

            if ($task['status'] === 'done') {
                if ($completedDate <= $dueDate) {
                    $onTime++;
                } else {
                    $late++;
                }
            } else {
                $pending++;

                if ($dueDate < $today) {
                    $overdueDeadlines[] = $task;
                } else {
                    // Only include upcoming deadlines within the next 7 days
                    $daysDiff = $today->diff($dueDate)->days;
                    if ($daysDiff <= 7) {
                        $upcomingDeadlines[] = $task;
                    }
                }
            }
        }

        // Sort upcoming deadlines by due date (ascending)
        usort($upcomingDeadlines, function($a, $b) {
            return strtotime($a['due_date']) - strtotime($b['due_date']);
        });

        // Sort overdue deadlines by due date (ascending)
        usort($overdueDeadlines, function($a, $b) {
            return strtotime($a['due_date']) - strtotime($b['due_date']);
        });

        // Generate HTML for PDF
        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Deadline Compliance Report</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    color: #333;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                h1 {
                    font-size: 24px;
                    margin: 0 0 5px 0;
                }
                h2 {
                    font-size: 18px;
                    margin: 0 0 20px 0;
                    font-weight: normal;
                    color: #666;
                }
                .section {
                    margin-bottom: 30px;
                }
                .section-title {
                    font-size: 18px;
                    margin-bottom: 10px;
                    padding-bottom: 5px;
                    border-bottom: 1px solid #ddd;
                }
                .stats-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr;
                    gap: 15px;
                    margin-bottom: 20px;
                }
                .stat-box {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    padding: 15px;
                    text-align: center;
                }
                .stat-title {
                    font-size: 14px;
                    color: #666;
                    margin-bottom: 5px;
                }
                .stat-value {
                    font-size: 24px;
                    font-weight: bold;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                table, th, td {
                    border: 1px solid #ddd;
                }
                th, td {
                    padding: 8px;
                    text-align: left;
                }
                th {
                    background-color: #f2f2f2;
                }
                .badge {
                    display: inline-block;
                    padding: 3px 6px;
                    border-radius: 3px;
                    font-size: 12px;
                    font-weight: bold;
                }
                .badge-red {
                    background-color: #fee2e2;
                    color: #b91c1c;
                }
                .badge-yellow {
                    background-color: #fef3c7;
                    color: #b45309;
                }
                .badge-green {
                    background-color: #d1fae5;
                    color: #047857;
                }
                .badge-gray {
                    background-color: #f3f4f6;
                    color: #4b5563;
                }
                .badge-blue {
                    background-color: #dbeafe;
                    color: #1e40af;
                }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Deadline Compliance Report</h1>
                <h2>' . htmlspecialchars($project['name']) . '</h2>
                <p>Generated on ' . date('F j, Y, g:i a') . '</p>
            </div>

            <div class="section">
                <div class="section-title">Deadline Compliance Overview</div>
                <div class="stats-grid">
                    <div class="stat-box">
                        <div class="stat-title">On-Time Completions</div>
                        <div class="stat-value">' . $onTime . '</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-title">Late Completions</div>
                        <div class="stat-value">' . $late . '</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-title">Pending Tasks</div>
                        <div class="stat-value">' . $pending . '</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-title">No Due Date</div>
                        <div class="stat-value">' . $noDueDate . '</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-title">Overdue Tasks</div>
                        <div class="stat-value">' . count($overdueDeadlines) . '</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-title">Upcoming Deadlines</div>
                        <div class="stat-value">' . count($upcomingDeadlines) . '</div>
                    </div>
                </div>
            </div>';

        if (!empty($overdueDeadlines)) {
            $html .= '
            <div class="section">
                <div class="section-title">Overdue Tasks</div>
                <table>
                    <tr>
                        <th>Task</th>
                        <th>Due Date</th>
                        <th>Status</th>
                        <th>Priority</th>
                        <th>Days Overdue</th>
                    </tr>';

            foreach ($overdueDeadlines as $task) {
                $dueDate = new DateTime($task['due_date']);
                $today = new DateTime();
                $daysOverdue = $today->diff($dueDate)->days;

                $statusClass = '';
                $statusLabel = '';

                switch ($task['status']) {
                    case 'todo':
                        $statusClass = 'badge-gray';
                        $statusLabel = 'To Do';
                        break;
                    case 'in_progress':
                        $statusClass = 'badge-blue';
                        $statusLabel = 'In Progress';
                        break;
                    case 'done':
                        $statusClass = 'badge-green';
                        $statusLabel = 'Done';
                        break;
                }

                $priorityClass = '';

                switch ($task['priority']) {
                    case 'low':
                        $priorityClass = 'badge-gray';
                        break;
                    case 'medium':
                        $priorityClass = 'badge-blue';
                        break;
                    case 'high':
                        $priorityClass = 'badge-yellow';
                        break;
                    case 'urgent':
                        $priorityClass = 'badge-red';
                        break;
                }

                $html .= '
                    <tr>
                        <td>' . htmlspecialchars($task['title']) . '</td>
                        <td>' . date('M j, Y', strtotime($task['due_date'])) . '</td>
                        <td><span class="badge ' . $statusClass . '">' . $statusLabel . '</span></td>
                        <td><span class="badge ' . $priorityClass . '">' . ucfirst($task['priority']) . '</span></td>
                        <td><span class="badge badge-red">' . $daysOverdue . ' days</span></td>
                    </tr>';
            }

            $html .= '
                </table>
            </div>';
        }

        if (!empty($upcomingDeadlines)) {
            $html .= '
            <div class="section">
                <div class="section-title">Upcoming Deadlines (Next 7 Days)</div>
                <table>
                    <tr>
                        <th>Task</th>
                        <th>Due Date</th>
                        <th>Status</th>
                        <th>Priority</th>
                        <th>Days Left</th>
                    </tr>';

            foreach ($upcomingDeadlines as $task) {
                $dueDate = new DateTime($task['due_date']);
                $today = new DateTime();
                $daysLeft = $today->diff($dueDate)->days;

                $statusClass = '';
                $statusLabel = '';

                switch ($task['status']) {
                    case 'todo':
                        $statusClass = 'badge-gray';
                        $statusLabel = 'To Do';
                        break;
                    case 'in_progress':
                        $statusClass = 'badge-blue';
                        $statusLabel = 'In Progress';
                        break;
                    case 'done':
                        $statusClass = 'badge-green';
                        $statusLabel = 'Done';
                        break;
                }

                $priorityClass = '';

                switch ($task['priority']) {
                    case 'low':
                        $priorityClass = 'badge-gray';
                        break;
                    case 'medium':
                        $priorityClass = 'badge-blue';
                        break;
                    case 'high':
                        $priorityClass = 'badge-yellow';
                        break;
                    case 'urgent':
                        $priorityClass = 'badge-red';
                        break;
                }

                $daysLeftClass = '';

                if ($daysLeft <= 1) {
                    $daysLeftClass = 'badge-red';
                } else if ($daysLeft <= 3) {
                    $daysLeftClass = 'badge-yellow';
                } else {
                    $daysLeftClass = 'badge-green';
                }

                $html .= '
                    <tr>
                        <td>' . htmlspecialchars($task['title']) . '</td>
                        <td>' . date('M j, Y', strtotime($task['due_date'])) . '</td>
                        <td><span class="badge ' . $statusClass . '">' . $statusLabel . '</span></td>
                        <td><span class="badge ' . $priorityClass . '">' . ucfirst($task['priority']) . '</span></td>
                        <td><span class="badge ' . $daysLeftClass . '">' . $daysLeft . ' day' . ($daysLeft !== 1 ? 's' : '') . '</span></td>
                    </tr>';
            }

            $html .= '
                </table>
            </div>';
        }

        $html .= '
            <div class="footer">
                <p>Generated by Momentum Project Management System</p>
            </div>
        </body>
        </html>';

        // Generate PDF
        require_once __DIR__ . '/../utils/PDFExporter.php';
        PDFExporter::generatePDF($html, $project['name'] . ' - Deadline Compliance Report');
    }
}
