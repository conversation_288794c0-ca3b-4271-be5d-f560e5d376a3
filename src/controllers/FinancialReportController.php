<?php
/**
 * Financial Report Controller
 *
 * Handles financial reporting functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Finance.php';
require_once __DIR__ . '/../models/Budget.php';
require_once __DIR__ . '/../models/IncomeSource.php';
require_once __DIR__ . '/../models/FinancialGoal.php';
require_once __DIR__ . '/../models/Debt.php';
require_once __DIR__ . '/../models/Subscription.php';
require_once __DIR__ . '/../models/FinancialInsight.php';
require_once __DIR__ . '/../utils/Session.php';
require_once __DIR__ . '/../utils/DateUtil.php';

class FinancialReportController extends BaseController {
    private $financeModel;
    private $budgetModel;
    private $incomeSourceModel;
    private $goalModel;
    private $debtModel;
    private $subscriptionModel;
    private $insightModel;

    public function __construct() {
        $this->financeModel = new Finance();
        $this->budgetModel = new Budget();
        $this->incomeSourceModel = new IncomeSource();
        $this->goalModel = new FinancialGoal();
        $this->debtModel = new Debt();
        $this->subscriptionModel = new Subscription();
        $this->insightModel = new FinancialInsight();
    }

    /**
     * Show financial reports dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get date range for summary
        $currentYear = date('Y');
        $currentMonth = date('m');

        // Get financial summary for current year
        $yearStartDate = "$currentYear-01-01";
        $yearEndDate = "$currentYear-12-31";
        $yearSummary = $this->financeModel->getSummary($userId, $yearStartDate, $yearEndDate);

        // Get financial summary for current month
        $monthStartDate = "$currentYear-$currentMonth-01";
        $monthEndDate = date('Y-m-t', strtotime($monthStartDate));
        $monthSummary = $this->financeModel->getSummary($userId, $monthStartDate, $monthEndDate);

        // Get monthly data for the current year
        $monthlyData = $this->financeModel->getMonthlyFinancialData($userId, $currentYear);

        // Get category spending for the current year
        $categorySpending = $this->financeModel->getSpendingByCategory($userId, $yearStartDate, $yearEndDate);

        // Get income sources summary
        $incomeSources = $this->incomeSourceModel->getSourcesWithTotalIncome($userId, $yearStartDate, $yearEndDate);

        // Get active financial goals
        $activeGoals = $this->goalModel->getActiveGoals($userId);

        // Get budget performance
        $budgetPerformance = $this->budgetModel->getBudgetPerformanceHistory($userId);

        $this->view('finances/reports/index', [
            'yearSummary' => $yearSummary,
            'monthSummary' => $monthSummary,
            'monthlyData' => $monthlyData,
            'categorySpending' => $categorySpending,
            'incomeSources' => $incomeSources,
            'activeGoals' => $activeGoals,
            'budgetPerformance' => $budgetPerformance,
            'currentYear' => $currentYear,
            'currentMonth' => $currentMonth
        ]);
    }

    /**
     * Show income vs expenses report
     */
    public function incomeVsExpenses() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Set default filters if not provided
        if (empty($filters['period'])) {
            $filters['period'] = 'monthly';
        }

        if (empty($filters['year'])) {
            $filters['year'] = date('Y');
        }

        if (empty($filters['start_date'])) {
            $filters['start_date'] = date('Y-01-01');
        }

        if (empty($filters['end_date'])) {
            $filters['end_date'] = date('Y-12-31');
        }

        // Get data based on period
        $reportData = [];
        $comparisonData = [];

        if ($filters['period'] === 'monthly') {
            // Get monthly data for selected year
            $reportData = $this->financeModel->getMonthlyFinancialData($userId, $filters['year']);

            // Get monthly data for previous year for comparison
            $prevYear = $filters['year'] - 1;
            $comparisonData = $this->financeModel->getMonthlyFinancialData($userId, $prevYear);
        } elseif ($filters['period'] === 'quarterly') {
            // Get quarterly data for selected year
            $reportData = $this->financeModel->getQuarterlyFinancialData($userId, $filters['year']);

            // Get quarterly data for previous year for comparison
            $prevYear = $filters['year'] - 1;
            $comparisonData = $this->financeModel->getQuarterlyFinancialData($userId, $prevYear);
        } elseif ($filters['period'] === 'yearly') {
            // Get yearly data for last 5 years
            $endYear = $filters['year'];
            $startYear = $endYear - 4;
            $reportData = $this->financeModel->getYearlyFinancialData($userId, $startYear, $endYear);
        } elseif ($filters['period'] === 'custom') {
            // Get custom period data
            $reportData = $this->financeModel->getCustomPeriodFinancialData(
                $userId,
                $filters['start_date'],
                $filters['end_date']
            );
        }

        $this->view('finances/reports/income_vs_expenses', [
            'reportData' => $reportData,
            'comparisonData' => $comparisonData,
            'filters' => $filters
        ]);
    }

    /**
     * Show category analysis report
     */
    public function categoryAnalysis() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Set default filters if not provided
        if (empty($filters['period'])) {
            $filters['period'] = 'monthly';
        }

        if (empty($filters['year'])) {
            $filters['year'] = date('Y');
        }

        if (empty($filters['month'])) {
            $filters['month'] = date('m');
        }

        if (empty($filters['start_date'])) {
            $filters['start_date'] = date('Y-01-01');
        }

        if (empty($filters['end_date'])) {
            $filters['end_date'] = date('Y-12-31');
        }

        // Get data based on period
        $categoryData = [];
        $trendData = [];

        if ($filters['period'] === 'monthly') {
            // Get category data for selected month
            $startDate = "{$filters['year']}-{$filters['month']}-01";
            $endDate = date('Y-m-t', strtotime($startDate));
            $categoryData = $this->financeModel->getSpendingByCategory($userId, $startDate, $endDate);

            // Get trend data for top 5 categories over the year
            $yearStartDate = "{$filters['year']}-01-01";
            $yearEndDate = "{$filters['year']}-12-31";
            $topCategories = array_slice($this->financeModel->getTopSpendingCategories($userId, $yearStartDate, $yearEndDate), 0, 5);
            $trendData = $this->financeModel->getCategorySpendingTrends($userId, $filters['year'], array_column($topCategories, 'category'));
        } elseif ($filters['period'] === 'yearly') {
            // Get category data for selected year
            $startDate = "{$filters['year']}-01-01";
            $endDate = "{$filters['year']}-12-31";
            $categoryData = $this->financeModel->getSpendingByCategory($userId, $startDate, $endDate);

            // Get yearly trend data for top 5 categories
            $topCategories = array_slice($categoryData, 0, 5);
            $trendData = $this->financeModel->getCategoryYearlyTrends($userId, $filters['year'] - 2, $filters['year'], array_column($topCategories, 'category'));
        } elseif ($filters['period'] === 'custom') {
            // Get category data for custom period
            $categoryData = $this->financeModel->getSpendingByCategory(
                $userId,
                $filters['start_date'],
                $filters['end_date']
            );
        }

        // Use the fixed view for category analysis
        $this->view('finances/reports/category_analysis_fixed', [
            'categoryData' => $categoryData,
            'trendData' => $trendData,
            'filters' => $filters
        ]);
    }

    /**
     * Show income source analysis report
     */
    public function incomeSourceAnalysis() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Set default filters if not provided
        if (empty($filters['period'])) {
            $filters['period'] = 'yearly';
        }

        if (empty($filters['year'])) {
            $filters['year'] = date('Y');
        }

        if (empty($filters['start_date'])) {
            $filters['start_date'] = date('Y-01-01');
        }

        if (empty($filters['end_date'])) {
            $filters['end_date'] = date('Y-12-31');
        }

        // Get income sources
        $incomeSources = $this->incomeSourceModel->getUserIncomeSources($userId);

        // Get data based on period
        $sourceData = [];
        $trendData = [];

        if ($filters['period'] === 'monthly') {
            // Get monthly data for each source for the year
            $trendData = $this->incomeSourceModel->getMonthlyIncomeBySource($userId, $filters['year']);

            // Get total for each source for the year
            $startDate = "{$filters['year']}-01-01";
            $endDate = "{$filters['year']}-12-31";
            $sourceData = $this->incomeSourceModel->getSourcesWithTotalIncome($userId, $startDate, $endDate);
        } elseif ($filters['period'] === 'yearly') {
            // Get yearly data for each source for last 3 years
            $endYear = $filters['year'];
            $startYear = $endYear - 2;
            $trendData = $this->incomeSourceModel->getYearlyIncomeBySource($userId, $startYear, $endYear);

            // Get total for each source for the year
            $startDate = "{$filters['year']}-01-01";
            $endDate = "{$filters['year']}-12-31";
            $sourceData = $this->incomeSourceModel->getSourcesWithTotalIncome($userId, $startDate, $endDate);
        } elseif ($filters['period'] === 'custom') {
            // Get data for custom period
            $sourceData = $this->incomeSourceModel->getSourcesWithTotalIncome(
                $userId,
                $filters['start_date'],
                $filters['end_date']
            );
        }

        $this->view('finances/reports/income_source_analysis', [
            'incomeSources' => $incomeSources,
            'sourceData' => $sourceData,
            'trendData' => $trendData,
            'filters' => $filters
        ]);
    }

    /**
     * Show financial goals progress report
     */
    public function goalsProgress() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all goals (including completed and abandoned)
        $allGoals = $this->goalModel->getUserGoals($userId);

        // Get active goals
        $activeGoals = $this->goalModel->getActiveGoals($userId);

        // Get completed goals
        $completedGoals = $this->goalModel->getUserGoals($userId, ['status' => 'completed']);

        // Get goals summary
        $goalsSummary = $this->goalModel->getGoalsSummary($userId);

        // Get contribution history for active goals
        $contributionHistory = [];
        foreach ($activeGoals as $goal) {
            $goalDetails = $this->goalModel->getGoalDetails($goal['id']);
            if (!empty($goalDetails['contributions'])) {
                $contributionHistory[$goal['id']] = $goalDetails['contributions'];
            }
        }

        $this->view('finances/reports/goals_progress', [
            'allGoals' => $allGoals,
            'activeGoals' => $activeGoals,
            'completedGoals' => $completedGoals,
            'goalsSummary' => $goalsSummary,
            'contributionHistory' => $contributionHistory
        ]);
    }

    /**
     * Show budget performance report
     */
    public function budgetPerformance() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Set default filters if not provided
        if (empty($filters['year'])) {
            $filters['year'] = date('Y');
        }

        // Get budget performance history
        $performanceHistory = $this->budgetModel->getBudgetPerformanceHistory($userId);

        // Get category performance for the year
        $categoryPerformance = $this->budgetModel->getCategoryPerformanceForYear($userId, $filters['year']);

        // Get monthly performance for the year
        $monthlyPerformance = $this->budgetModel->getMonthlyPerformanceForYear($userId, $filters['year']);

        // Get active budget
        $activeBudget = $this->budgetModel->getActiveBudget($userId);

        // Get budget progress if active budget exists
        $budgetProgress = null;
        if ($activeBudget) {
            $budgetProgress = $this->budgetModel->getBudgetProgress($activeBudget['id']);
        }

        $this->view('finances/reports/budget_performance', [
            'performanceHistory' => $performanceHistory,
            'categoryPerformance' => $categoryPerformance,
            'monthlyPerformance' => $monthlyPerformance,
            'activeBudget' => $activeBudget,
            'budgetProgress' => $budgetProgress,
            'filters' => $filters
        ]);
    }

    /**
     * Export report as CSV
     */
    public function exportCSV($reportType) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Prepare data based on report type
        $headers = [];
        $data = [];
        $filename = 'Financial_Report';

        switch ($reportType) {
            case 'income-vs-expenses':
                list($headers, $data, $filename) = $this->prepareIncomeVsExpensesExport($userId, $filters);
                break;
            case 'category-analysis':
                list($headers, $data, $filename) = $this->prepareCategoryAnalysisExport($userId, $filters);
                break;
            case 'income-source-analysis':
                list($headers, $data, $filename) = $this->prepareIncomeSourceAnalysisExport($userId, $filters);
                break;
            case 'goals-progress':
                list($headers, $data, $filename) = $this->prepareGoalsProgressExport($userId);
                break;
            case 'budget-performance':
                list($headers, $data, $filename) = $this->prepareBudgetPerformanceExport($userId, $filters);
                break;
        }

        // Send CSV response
        require_once __DIR__ . '/../utils/ExportUtil.php';
        ExportUtil::sendCSV($filename, $headers, $data);
    }

    /**
     * Show year-over-year comparison report
     */
    public function yearOverYear() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Set default filters if not provided
        if (empty($filters['current_year'])) {
            $filters['current_year'] = date('Y');
        }

        if (empty($filters['previous_year'])) {
            $filters['previous_year'] = date('Y') - 1;
        }

        if (empty($filters['view_type'])) {
            $filters['view_type'] = 'monthly';
        }

        // Get yearly summary comparison
        $currentYearStartDate = "{$filters['current_year']}-01-01";
        $currentYearEndDate = "{$filters['current_year']}-12-31";
        $previousYearStartDate = "{$filters['previous_year']}-01-01";
        $previousYearEndDate = "{$filters['previous_year']}-12-31";

        $currentYearSummary = $this->financeModel->getSummary($userId, $currentYearStartDate, $currentYearEndDate);
        $previousYearSummary = $this->financeModel->getSummary($userId, $previousYearStartDate, $previousYearEndDate);

        $yearlyComparison = [
            'current_year' => $currentYearSummary,
            'previous_year' => $previousYearSummary
        ];

        // Get detailed comparison data based on view type
        $comparisonData = [];

        if ($filters['view_type'] === 'monthly') {
            $currentYearMonthly = $this->financeModel->getMonthlyFinancialData($userId, $filters['current_year']);
            $previousYearMonthly = $this->financeModel->getMonthlyFinancialData($userId, $filters['previous_year']);

            // Organize data by month for comparison
            foreach ($currentYearMonthly as $data) {
                $month = $data['month_name'];
                if (!isset($comparisonData[$month])) {
                    $comparisonData[$month] = [
                        'current_year' => ['income' => 0, 'expense' => 0],
                        'previous_year' => ['income' => 0, 'expense' => 0]
                    ];
                }
                $comparisonData[$month]['current_year']['income'] = $data['total_income'];
                $comparisonData[$month]['current_year']['expense'] = $data['total_expense'];
            }

            foreach ($previousYearMonthly as $data) {
                $month = $data['month_name'];
                if (!isset($comparisonData[$month])) {
                    $comparisonData[$month] = [
                        'current_year' => ['income' => 0, 'expense' => 0],
                        'previous_year' => ['income' => 0, 'expense' => 0]
                    ];
                }
                $comparisonData[$month]['previous_year']['income'] = $data['total_income'];
                $comparisonData[$month]['previous_year']['expense'] = $data['total_expense'];
            }
        } elseif ($filters['view_type'] === 'quarterly') {
            $currentYearQuarterly = $this->financeModel->getQuarterlyFinancialData($userId, $filters['current_year']);
            $previousYearQuarterly = $this->financeModel->getQuarterlyFinancialData($userId, $filters['previous_year']);

            // Organize data by quarter for comparison
            foreach ($currentYearQuarterly as $data) {
                $quarter = $data['quarter_name'];
                if (!isset($comparisonData[$quarter])) {
                    $comparisonData[$quarter] = [
                        'current_year' => ['income' => 0, 'expense' => 0],
                        'previous_year' => ['income' => 0, 'expense' => 0]
                    ];
                }
                $comparisonData[$quarter]['current_year']['income'] = $data['total_income'];
                $comparisonData[$quarter]['current_year']['expense'] = $data['total_expense'];
            }

            foreach ($previousYearQuarterly as $data) {
                $quarter = $data['quarter_name'];
                if (!isset($comparisonData[$quarter])) {
                    $comparisonData[$quarter] = [
                        'current_year' => ['income' => 0, 'expense' => 0],
                        'previous_year' => ['income' => 0, 'expense' => 0]
                    ];
                }
                $comparisonData[$quarter]['previous_year']['income'] = $data['total_income'];
                $comparisonData[$quarter]['previous_year']['expense'] = $data['total_expense'];
            }
        } elseif ($filters['view_type'] === 'annual') {
            // Just compare the annual totals
            $comparisonData['Annual'] = [
                'current_year' => [
                    'income' => $currentYearSummary['total_income'] ?? 0,
                    'expense' => $currentYearSummary['total_expense'] ?? 0
                ],
                'previous_year' => [
                    'income' => $previousYearSummary['total_income'] ?? 0,
                    'expense' => $previousYearSummary['total_expense'] ?? 0
                ]
            ];
        }

        $this->view('finances/reports/year_over_year', [
            'yearlyComparison' => $yearlyComparison,
            'comparisonData' => $comparisonData,
            'filters' => $filters
        ]);
    }

    /**
     * Show month-over-month comparison report
     */
    public function monthOverMonth() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Set default filters if not provided
        if (empty($filters['months'])) {
            $filters['months'] = 12;
        }

        if (empty($filters['chart_type'])) {
            $filters['chart_type'] = 'line';
        }

        // Get month-over-month data
        $monthlyData = $this->financeModel->getMonthOverMonthComparison($userId, $filters['months']);

        // Calculate growth rates
        $growthRates = [];
        $prevIncome = null;
        $prevExpense = null;

        foreach ($monthlyData as $index => $data) {
            $income = $data['total_income'] ?? 0;
            $expense = $data['total_expense'] ?? 0;

            if ($prevIncome !== null && $prevIncome > 0) {
                $incomeGrowth = (($income - $prevIncome) / $prevIncome) * 100;
            } else {
                $incomeGrowth = null;
            }

            if ($prevExpense !== null && $prevExpense > 0) {
                $expenseGrowth = (($expense - $prevExpense) / $prevExpense) * 100;
            } else {
                $expenseGrowth = null;
            }

            if ($incomeGrowth !== null || $expenseGrowth !== null) {
                $growthRates[] = [
                    'month' => $data['month_name'],
                    'income_growth' => $incomeGrowth,
                    'expense_growth' => $expenseGrowth
                ];
            }

            $prevIncome = $income;
            $prevExpense = $expense;
        }

        $this->view('finances/reports/month_over_month', [
            'monthlyData' => $monthlyData,
            'growthRates' => $growthRates,
            'filters' => $filters
        ]);
    }

    /**
     * Show advanced financial dashboard
     */
    public function advancedDashboard() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Set default filters if not provided
        if (empty($filters['year'])) {
            $filters['year'] = date('Y');
        }

        // Get date range for the selected year
        $yearStartDate = "{$filters['year']}-01-01";
        $yearEndDate = "{$filters['year']}-12-31";

        // Get monthly data for the selected year
        $monthlyData = $this->financeModel->getMonthlyFinancialData($userId, $filters['year']);

        // Get top spending categories
        $topCategories = $this->financeModel->getTopSpendingCategories($userId, $yearStartDate, $yearEndDate, 5);

        // Get payment method distribution
        $paymentMethods = $this->financeModel->getPaymentMethodDistribution($userId, $yearStartDate, $yearEndDate);

        // Get income sources
        $incomeSources = $this->incomeSourceModel->getSourcesWithTotalIncome($userId, $yearStartDate, $yearEndDate);

        // Get budget performance
        $budgetPerformance = $this->budgetModel->getBudgetPerformanceHistory($userId);

        // Calculate financial health score
        $financialHealth = $this->calculateFinancialHealth($userId, $filters['year']);

        // Generate financial insights
        $insights = $this->generateFinancialInsights($userId, $filters['year'], $monthlyData, $topCategories, $incomeSources);

        $this->view('finances/reports/advanced_dashboard', [
            'monthlyData' => $monthlyData,
            'topCategories' => $topCategories,
            'paymentMethods' => $paymentMethods,
            'incomeSources' => $incomeSources,
            'budgetPerformance' => $budgetPerformance,
            'financialHealth' => $financialHealth,
            'insights' => $insights,
            'filters' => $filters
        ]);
    }

    /**
     * Show AI-powered financial insights
     */
    public function financialInsights() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Set default filters if not provided
        if (empty($filters['period'])) {
            $filters['period'] = 'monthly';
        }

        if (empty($filters['time_value'])) {
            $filters['time_value'] = 6; // Default to 6 months/quarters/years
        }

        // Generate comprehensive insights
        $comprehensiveInsights = $this->insightModel->generateComprehensiveInsights(
            $userId,
            $filters['period'],
            $filters['time_value']
        );

        // Get spending patterns
        $spendingPatterns = $comprehensiveInsights['spending_patterns'];

        // Get spending anomalies
        $anomalies = $comprehensiveInsights['anomalies'];

        // Get expense reduction recommendations
        $recommendations = $comprehensiveInsights['recommendations'];

        // Get financial summary
        $summary = $comprehensiveInsights['summary'];

        // Get top spending categories
        $topCategories = $comprehensiveInsights['top_categories'];

        // Get analysis period
        $analysisPeriod = $comprehensiveInsights['analysis_period'];

        $this->view('finances/reports/financial_insights', [
            'spendingPatterns' => $spendingPatterns,
            'anomalies' => $anomalies,
            'recommendations' => $recommendations,
            'summary' => $summary,
            'topCategories' => $topCategories,
            'analysisPeriod' => $analysisPeriod,
            'filters' => $filters
        ]);
    }

    /**
     * Calculate financial health score
     */
    private function calculateFinancialHealth($userId, $year) {
        $yearStartDate = "$year-01-01";
        $yearEndDate = "$year-12-31";

        // Get summary for the year
        $summary = $this->financeModel->getSummary($userId, $yearStartDate, $yearEndDate);
        $totalIncome = $summary['total_income'] ?? 0;
        $totalExpense = $summary['total_expense'] ?? 0;

        // Calculate income/expense ratio (1.5 or higher is ideal)
        $incomeExpenseRatio = $totalExpense > 0 ? $totalIncome / $totalExpense : 0;
        $ratioScore = min(100, ($incomeExpenseRatio / 1.5) * 100);

        // Calculate savings rate (20% or higher is ideal)
        $savingsRate = $totalIncome > 0 ? (($totalIncome - $totalExpense) / $totalIncome) * 100 : 0;
        $savingsScore = min(100, ($savingsRate / 20) * 100);

        // Get budget adherence score
        $budgetScore = 0;
        $budgetPerformance = $this->budgetModel->getBudgetPerformanceHistory($userId);

        if (!empty($budgetPerformance)) {
            $totalPercentage = 0;
            $count = 0;

            foreach ($budgetPerformance as $budget) {
                $percentage = $budget['percentage'];
                // Score is higher when closer to 100% (but not over)
                $budgetItemScore = $percentage <= 100 ? (100 - abs(100 - $percentage)) : (100 - ($percentage - 100));
                $totalPercentage += $budgetItemScore;
                $count++;
            }

            $budgetScore = $count > 0 ? $totalPercentage / $count : 0;
        }

        // Calculate overall score (weighted average)
        $overallScore = round(($ratioScore * 0.4) + ($savingsScore * 0.4) + ($budgetScore * 0.2));

        return [
            'overall_score' => $overallScore,
            'income_expense_ratio' => $incomeExpenseRatio,
            'savings_rate' => $savingsRate,
            'budget_score' => $budgetScore
        ];
    }

    /**
     * Generate financial insights based on data
     */
    private function generateFinancialInsights($userId, $year, $monthlyData, $topCategories, $incomeSources) {
        $insights = [];

        // Check if we have enough data
        if (empty($monthlyData) || count($monthlyData) < 2) {
            return $insights;
        }

        // Income trend insight
        $firstMonth = reset($monthlyData);
        $lastMonth = end($monthlyData);
        $incomeChange = $lastMonth['total_income'] - $firstMonth['total_income'];
        $incomeChangePercent = $firstMonth['total_income'] > 0 ? ($incomeChange / $firstMonth['total_income']) * 100 : 0;

        if (abs($incomeChangePercent) >= 10) {
            $insights[] = [
                'title' => $incomeChange > 0 ? 'Income Increasing' : 'Income Decreasing',
                'description' => $incomeChange > 0
                    ? "Your income has increased by " . number_format(abs($incomeChangePercent), 1) . "% over the year. Keep up the good work!"
                    : "Your income has decreased by " . number_format(abs($incomeChangePercent), 1) . "% over the year. Consider exploring new income sources."
            ];
        }

        // Expense trend insight
        $expenseChange = $lastMonth['total_expense'] - $firstMonth['total_expense'];
        $expenseChangePercent = $firstMonth['total_expense'] > 0 ? ($expenseChange / $firstMonth['total_expense']) * 100 : 0;

        if ($expenseChangePercent >= 15) {
            $insights[] = [
                'title' => 'Expenses Increasing',
                'description' => "Your expenses have increased by " . number_format($expenseChangePercent, 1) . "% over the year. Consider reviewing your budget and cutting unnecessary expenses."
            ];
        }

        // Top spending category insight
        if (!empty($topCategories)) {
            $topCategory = $topCategories[0];
            $insights[] = [
                'title' => 'Top Spending Category',
                'description' => "Your highest spending category is '{$topCategory['category']}' at " . View::formatCurrency($topCategory['total_amount']) . ". This represents a significant portion of your expenses."
            ];
        }

        // Income source diversity insight
        if (!empty($incomeSources)) {
            $sourceCount = count($incomeSources);
            $totalIncome = array_sum(array_column($incomeSources, 'total_income'));
            $topSource = $incomeSources[0];
            $topSourcePercentage = $totalIncome > 0 ? ($topSource['total_income'] / $totalIncome) * 100 : 0;

            if ($sourceCount == 1 || $topSourcePercentage > 80) {
                $insights[] = [
                    'title' => 'Income Source Diversity',
                    'description' => "You rely heavily on a single income source ({$topSource['name']}) for " . number_format($topSourcePercentage, 1) . "% of your income. Consider diversifying your income sources for financial stability."
                ];
            } elseif ($sourceCount >= 3) {
                $insights[] = [
                    'title' => 'Multiple Income Sources',
                    'description' => "You have {$sourceCount} different income sources, which is great for financial stability. Keep diversifying your income!"
                ];
            }
        }

        // Savings insight
        $yearStartDate = "$year-01-01";
        $yearEndDate = "$year-12-31";
        $summary = $this->financeModel->getSummary($userId, $yearStartDate, $yearEndDate);
        $totalIncome = $summary['total_income'] ?? 0;
        $totalExpense = $summary['total_expense'] ?? 0;
        $savings = $totalIncome - $totalExpense;
        $savingsRate = $totalIncome > 0 ? ($savings / $totalIncome) * 100 : 0;

        if ($savingsRate < 10 && $totalIncome > 0) {
            $insights[] = [
                'title' => 'Low Savings Rate',
                'description' => "Your savings rate is " . number_format($savingsRate, 1) . "%, which is below the recommended 20%. Consider increasing your savings to build financial security."
            ];
        } elseif ($savingsRate >= 20) {
            $insights[] = [
                'title' => 'Excellent Savings Rate',
                'description' => "Your savings rate is " . number_format($savingsRate, 1) . "%, which is excellent! You're on track for financial security."
            ];
        }

        return $insights;
    }



    /**
     * Prepare financial insights PDF data
     *
     * @param int $userId User ID
     * @param array $filters Filter parameters
     * @return array Array containing title, subtitle, and sections
     */
    private function prepareFinancialInsightsPDF($userId, $filters) {
        // Set default filters if not provided
        if (empty($filters['period'])) {
            $filters['period'] = 'monthly';
        }

        if (empty($filters['time_value'])) {
            $filters['time_value'] = 6; // Default to 6 months/quarters/years
        }

        // Generate comprehensive insights
        $comprehensiveInsights = $this->insightModel->generateComprehensiveInsights(
            $userId,
            $filters['period'],
            $filters['time_value']
        );

        // Get spending patterns
        $spendingPatterns = $comprehensiveInsights['spending_patterns'];

        // Get spending anomalies
        $anomalies = $comprehensiveInsights['anomalies'];

        // Get expense reduction recommendations
        $recommendations = $comprehensiveInsights['recommendations'];

        // Get financial summary
        $summary = $comprehensiveInsights['summary'];

        // Get top spending categories
        $topCategories = $comprehensiveInsights['top_categories'];

        // Get analysis period
        $analysisPeriod = $comprehensiveInsights['analysis_period'];

        // Prepare PDF sections
        $title = 'AI-Powered Financial Insights';
        $subtitle = 'Analysis Period: ' . date('M j, Y', strtotime($analysisPeriod['start_date'])) . ' - ' . date('M j, Y', strtotime($analysisPeriod['end_date']));

        $sections = [];

        // Financial Summary Section
        $sections[] = [
            'title' => 'Financial Summary',
            'content' => '<div class="summary-grid">
                <div class="summary-item">
                    <h3>Total Income</h3>
                    <p class="amount income">' . View::formatCurrency($summary['total_income'] ?? 0) . '</p>
                </div>
                <div class="summary-item">
                    <h3>Total Expenses</h3>
                    <p class="amount expense">' . View::formatCurrency($summary['total_expense'] ?? 0) . '</p>
                </div>
                <div class="summary-item">
                    <h3>Net Balance</h3>
                    <p class="amount ' . (($summary['total_income'] ?? 0) - ($summary['total_expense'] ?? 0) >= 0 ? 'positive' : 'negative') . '">' .
                    View::formatCurrency(($summary['total_income'] ?? 0) - ($summary['total_expense'] ?? 0)) . '</p>
                </div>
            </div>'
        ];

        // Spending Patterns Section
        $patternsContent = '';
        if (!empty($spendingPatterns)) {
            // Recurring Expenses
            if (!empty($spendingPatterns['recurring'])) {
                $patternsContent .= '<h3>Recurring Expenses</h3><table class="data-table">
                    <tr>
                        <th>Category</th>
                        <th>Description</th>
                        <th>Amount</th>
                        <th>Frequency</th>
                    </tr>';

                foreach ($spendingPatterns['recurring'] as $expense) {
                    $patternsContent .= '<tr>
                        <td>' . View::escape($expense['category']) . '</td>
                        <td>' . View::escape($expense['description']) . '</td>
                        <td>' . View::formatCurrency($expense['amount']) . '</td>
                        <td>' . View::escape($expense['frequency']) . '</td>
                    </tr>';
                }

                $patternsContent .= '</table>';
            }

            // Category Trends
            if (!empty($spendingPatterns['category_trends'])) {
                $patternsContent .= '<h3>Category Spending Trends</h3><div class="grid-container">';

                foreach ($spendingPatterns['category_trends'] as $category => $trend) {
                    $patternsContent .= '<div class="grid-item">
                        <h4>' . View::escape($category) . '</h4>
                        <p class="' . ($trend['trend'] === 'increasing' ? 'negative' : 'positive') . '">
                            ' . ($trend['trend'] === 'increasing' ? 'Increasing' : 'Decreasing') . ' by ' .
                            number_format($trend['percent_change'], 1) . '% over the period
                        </p>
                    </div>';
                }

                $patternsContent .= '</div>';
            }
        } else {
            $patternsContent = '<p class="no-data">No spending patterns detected for the selected period</p>';
        }

        $sections[] = [
            'title' => 'Spending Patterns',
            'content' => $patternsContent
        ];

        // Spending Anomalies Section
        $anomaliesContent = '';
        if (!empty($anomalies['category_anomalies']) || !empty($anomalies['transaction_anomalies'])) {
            // Category Anomalies
            if (!empty($anomalies['category_anomalies'])) {
                $anomaliesContent .= '<h3>Category Spending Anomalies</h3><table class="data-table">
                    <tr>
                        <th>Category</th>
                        <th>Current Amount</th>
                        <th>Average Amount</th>
                        <th>Difference</th>
                    </tr>';

                foreach ($anomalies['category_anomalies'] as $anomaly) {
                    $anomaliesContent .= '<tr>
                        <td>' . View::escape($anomaly['category']) . '</td>
                        <td>' . View::formatCurrency($anomaly['current_amount']) . '</td>
                        <td>' . View::formatCurrency($anomaly['avg_amount']) . '</td>
                        <td class="negative">+' . number_format($anomaly['percent_above_avg'], 1) . '%</td>
                    </tr>';
                }

                $anomaliesContent .= '</table>';
            }

            // Transaction Anomalies
            if (!empty($anomalies['transaction_anomalies'])) {
                $anomaliesContent .= '<h3>Unusual Transactions</h3><table class="data-table">
                    <tr>
                        <th>Date</th>
                        <th>Category</th>
                        <th>Description</th>
                        <th>Amount</th>
                        <th>Avg. Amount</th>
                    </tr>';

                foreach ($anomalies['transaction_anomalies'] as $anomaly) {
                    $anomaliesContent .= '<tr>
                        <td>' . date('M j, Y', strtotime($anomaly['date'])) . '</td>
                        <td>' . View::escape($anomaly['category']) . '</td>
                        <td>' . View::escape($anomaly['description']) . '</td>
                        <td class="negative">' . View::formatCurrency($anomaly['amount']) . '</td>
                        <td>' . View::formatCurrency($anomaly['avg_amount']) . '</td>
                    </tr>';
                }

                $anomaliesContent .= '</table>';
            }
        } else {
            $anomaliesContent = '<p class="no-data">No spending anomalies detected for the selected period</p>';
        }

        $sections[] = [
            'title' => 'Spending Anomalies',
            'content' => $anomaliesContent
        ];

        // Expense Reduction Recommendations Section
        $recommendationsContent = '';
        if (!empty($recommendations)) {
            $recommendationsContent .= '<div class="recommendations">';

            foreach ($recommendations as $recommendation) {
                $recommendationsContent .= '<div class="recommendation-item">
                    <h3>' . View::escape($recommendation['title']) . '</h3>
                    <p>' . View::escape($recommendation['description']) . '</p>';

                if (!empty($recommendation['potential_savings'])) {
                    $recommendationsContent .= '<p class="potential-savings">
                        Potential savings: ' . View::formatCurrency($recommendation['potential_savings']) . '
                    </p>';
                }

                $recommendationsContent .= '</div>';
            }

            $recommendationsContent .= '</div>';
        } else {
            $recommendationsContent = '<p class="no-data">No recommendations available for the selected period</p>';
        }

        $sections[] = [
            'title' => 'Expense Reduction Recommendations',
            'content' => $recommendationsContent
        ];

        return [$title, $subtitle, $sections];
    }

    /**
     * Export report as PDF
     */
    public function exportPDF($reportType) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Prepare data based on report type
        $title = 'Financial Report';
        $subtitle = '';
        $sections = [];

        switch ($reportType) {
            case 'income-vs-expenses':
                // Placeholder for unimplemented method
                $title = 'Income vs Expenses Report';
                $subtitle = 'Analysis of income and expenses';
                $sections = [['title' => 'Report Data', 'content' => '<p>This report is not yet implemented.</p>']];
                break;
            case 'category-analysis':
                // Placeholder for unimplemented method
                $title = 'Category Analysis Report';
                $subtitle = 'Analysis of spending by category';
                $sections = [['title' => 'Report Data', 'content' => '<p>This report is not yet implemented.</p>']];
                break;
            case 'income-source-analysis':
                // Placeholder for unimplemented method
                $title = 'Income Source Analysis Report';
                $subtitle = 'Analysis of income sources';
                $sections = [['title' => 'Report Data', 'content' => '<p>This report is not yet implemented.</p>']];
                break;
            case 'goals-progress':
                // Placeholder for unimplemented method
                $title = 'Goals Progress Report';
                $subtitle = 'Progress towards financial goals';
                $sections = [['title' => 'Report Data', 'content' => '<p>This report is not yet implemented.</p>']];
                break;
            case 'budget-performance':
                // Placeholder for unimplemented method
                $title = 'Budget Performance Report';
                $subtitle = 'Analysis of budget performance';
                $sections = [['title' => 'Report Data', 'content' => '<p>This report is not yet implemented.</p>']];
                break;
            case 'year-over-year':
                // Placeholder for unimplemented method
                $title = 'Year-Over-Year Comparison Report';
                $subtitle = 'Comparison of financial data across years';
                $sections = [['title' => 'Report Data', 'content' => '<p>This report is not yet implemented.</p>']];
                break;
            case 'month-over-month':
                // Placeholder for unimplemented method
                $title = 'Month-Over-Month Comparison Report';
                $subtitle = 'Comparison of financial data across months';
                $sections = [['title' => 'Report Data', 'content' => '<p>This report is not yet implemented.</p>']];
                break;
            case 'advanced-dashboard':
                // Placeholder for unimplemented method
                $title = 'Advanced Dashboard Report';
                $subtitle = 'Comprehensive financial dashboard';
                $sections = [['title' => 'Report Data', 'content' => '<p>This report is not yet implemented.</p>']];
                break;
            case 'financial-insights':
                list($title, $subtitle, $sections) = $this->prepareFinancialInsightsPDF($userId, $filters);
                break;
        }

        // Generate PDF
        require_once __DIR__ . '/../utils/ExportUtil.php';
        $html = ExportUtil::generatePDFHTML($title, $subtitle, $sections);

        require_once __DIR__ . '/../utils/PDFExporter.php';
        PDFExporter::generatePDF($html, $title);
    }
}
