<?php
/**
 * Task Controller
 *
 * Handles task-related functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Task.php';
require_once __DIR__ . '/../models/Category.php';

class TaskController extends BaseController {
    private $taskModel;
    private $categoryModel;

    public function __construct() {
        $this->taskModel = new Task();
        $this->categoryModel = new Category();
    }

    /**
     * Show task list
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Process quick filter parameters
        if (isset($filters['due_date_range']) && $filters['due_date_range'] === 'week') {
            // This Week filter
            $startDate = date('Y-m-d', strtotime('monday this week'));
            $endDate = date('Y-m-d', strtotime('sunday this week'));
            $filters['due_date_start'] = $startDate;
            $filters['due_date_end'] = $endDate;
            unset($filters['due_date_range']);
        }

        if (isset($filters['overdue']) && $filters['overdue'] === '1') {
            // Overdue filter
            $filters['overdue'] = true;
            $filters['status'] = 'todo,in_progress'; // Only show incomplete tasks
        }

        // Clean empty filter values and remove special parameters
        foreach ($filters as $key => $value) {
            // Remove empty values and special parameters
            if ($value === '' || $key === 'filter_processed') {
                unset($filters[$key]);
            }
        }

        // Log the cleaned filters for debugging
        error_log("Cleaned task filters: " . print_r($filters, true));

        // Get tasks based on filters
        $tasks = $this->taskModel->getUserTasks($userId, $filters);

        // Get categories for filter dropdown
        $categories = $this->categoryModel->getUserCategories($userId);

        $this->view('tasks/index', [
            'tasks' => $tasks,
            'categories' => $categories,
            'filters' => $filters
        ]);
    }

    /**
     * AJAX endpoint for task list
     */
    public function ajax() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Process quick filter parameters
        if (isset($filters['due_date_range']) && $filters['due_date_range'] === 'week') {
            // This Week filter
            $startDate = date('Y-m-d', strtotime('monday this week'));
            $endDate = date('Y-m-d', strtotime('sunday this week'));
            $filters['due_date_start'] = $startDate;
            $filters['due_date_end'] = $endDate;
            unset($filters['due_date_range']);
        }

        if (isset($filters['overdue']) && $filters['overdue'] === '1') {
            // Overdue filter
            $filters['overdue'] = true;
            $filters['status'] = 'todo,in_progress'; // Only show incomplete tasks
        }

        // Clean empty filter values and remove special parameters
        foreach ($filters as $key => $value) {
            // Remove empty values and special parameters
            if ($value === '' || $key === 'filter_processed') {
                unset($filters[$key]);
            }
        }

        // Get tasks based on filters
        $tasks = $this->taskModel->getUserTasks($userId, $filters);

        // Return JSON response
        $this->json([
            'success' => true,
            'count' => count($tasks),
            'tasks' => $tasks
        ]);
    }

    /**
     * Show task calendar
     */
    public function calendar() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get view type (day, week, month)
        $viewType = $this->getQueryData()['view'] ?? 'month';

        // Get date
        $date = $this->getQueryData()['date'] ?? date('Y-m-d');

        // Calculate date range based on view type
        $startDate = null;
        $endDate = null;

        switch ($viewType) {
            case 'day':
                $startDate = $date;
                $endDate = $date;
                break;
            case 'week':
                $startDate = date('Y-m-d', strtotime('monday this week', strtotime($date)));
                $endDate = date('Y-m-d', strtotime('sunday this week', strtotime($date)));
                break;
            case 'month':
            default:
                $startDate = date('Y-m-01', strtotime($date));
                $endDate = date('Y-m-t', strtotime($date));
                break;
        }

        // Get tasks for the date range
        $tasks = $this->taskModel->getTasksByDateRange($userId, $startDate, $endDate);

        // Get categories
        $categories = $this->categoryModel->getUserCategories($userId);

        $this->view('tasks/calendar', [
            'tasks' => $tasks,
            'categories' => $categories,
            'viewType' => $viewType,
            'date' => $date,
            'startDate' => $startDate,
            'endDate' => $endDate
        ]);
    }

    /**
     * Show task creation form
     */
    public function create() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get categories for dropdown
        $categories = $this->categoryModel->getUserCategories($userId);

        // Get parent task ID if creating a subtask
        $parentId = $this->getQueryData()['parent_id'] ?? null;
        $parentTask = null;

        if ($parentId) {
            $parentTask = $this->taskModel->find($parentId);

            // Verify parent task belongs to user
            if (!$parentTask || $parentTask['user_id'] != $userId) {
                Session::setFlash('error', 'Invalid parent task');
                $this->redirect('/tasks');
            }
        }

        $this->view('tasks/create', [
            'categories' => $categories,
            'parentTask' => $parentTask
        ]);
    }

    /**
     * Process task creation
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['title']);

        if (!empty($errors)) {
            // Get categories for dropdown
            $categories = $this->categoryModel->getUserCategories($userId);

            $this->view('tasks/create', [
                'errors' => $errors,
                'data' => $data,
                'categories' => $categories
            ]);
            return;
        }

        // Prepare task data
        $taskData = [
            'user_id' => $userId,
            'title' => $data['title'],
            'description' => $data['description'] ?? null,
            'status' => $data['status'] ?? 'todo',
            'priority' => $data['priority'] ?? 'medium',
            'due_date' => !empty($data['due_date']) ? $data['due_date'] : null,
            'due_time' => !empty($data['due_time']) ? $data['due_time'] : null,
            'estimated_time' => !empty($data['estimated_time']) ? $data['estimated_time'] : null,
            'category_id' => !empty($data['category_id']) ? $data['category_id'] : null,
            'parent_id' => !empty($data['parent_id']) ? $data['parent_id'] : null,
            'is_recurring' => isset($data['is_recurring']) && $data['is_recurring'] ? 1 : 0,
            'recurrence_pattern' => !empty($data['recurrence_pattern']) ? $data['recurrence_pattern'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create task
        $taskId = $this->taskModel->create($taskData);

        if ($taskId) {
            Session::setFlash('success', 'Task created successfully');

            // Redirect to parent task if creating a subtask
            if (!empty($data['parent_id'])) {
                $this->redirect('/tasks/view/' . $data['parent_id']);
            } else {
                $this->redirect('/tasks');
            }
        } else {
            Session::setFlash('error', 'Failed to create task');

            // Get categories for dropdown
            $categories = $this->categoryModel->getUserCategories($userId);

            $this->view('tasks/create', [
                'data' => $data,
                'categories' => $categories
            ]);
        }
    }

    /**
     * Show task details
     */
    public function viewTask($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get task
        $task = $this->taskModel->find($id);

        // Verify task exists and belongs to user
        if (!$task || $task['user_id'] != $userId) {
            Session::setFlash('error', 'Task not found');
            $this->redirect('/tasks');
        }

        // Get category
        $category = null;
        if ($task['category_id']) {
            $category = $this->categoryModel->find($task['category_id']);
        }

        // Get subtasks
        $subtasks = $this->taskModel->getSubtasks($id);

        // Get parent task if this is a subtask
        $parentTask = null;
        if ($task['parent_id']) {
            $parentTask = $this->taskModel->find($task['parent_id']);
        }

        $this->view('tasks/view', [
            'task' => $task,
            'category' => $category,
            'subtasks' => $subtasks,
            'parentTask' => $parentTask
        ]);
    }

    /**
     * Show task edit form
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get task
        $task = $this->taskModel->find($id);

        // Verify task exists and belongs to user
        if (!$task || $task['user_id'] != $userId) {
            Session::setFlash('error', 'Task not found');
            $this->redirect('/tasks');
        }

        // Get categories for dropdown
        $categories = $this->categoryModel->getUserCategories($userId);

        $this->view('tasks/edit', [
            'task' => $task,
            'categories' => $categories
        ]);
    }

    /**
     * Process task update
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get task
        $task = $this->taskModel->find($id);

        // Verify task exists and belongs to user
        if (!$task || $task['user_id'] != $userId) {
            Session::setFlash('error', 'Task not found');
            $this->redirect('/tasks');
        }

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['title']);

        if (!empty($errors)) {
            // Get categories for dropdown
            $categories = $this->categoryModel->getUserCategories($userId);

            $this->view('tasks/edit', [
                'errors' => $errors,
                'task' => array_merge($task, $data),
                'categories' => $categories
            ]);
            return;
        }

        // Prepare task data
        $taskData = [
            'title' => $data['title'],
            'description' => $data['description'] ?? null,
            'status' => $data['status'] ?? 'todo',
            'priority' => $data['priority'] ?? 'medium',
            'due_date' => !empty($data['due_date']) ? $data['due_date'] : null,
            'due_time' => !empty($data['due_time']) ? $data['due_time'] : null,
            'estimated_time' => !empty($data['estimated_time']) ? $data['estimated_time'] : null,
            'category_id' => !empty($data['category_id']) ? $data['category_id'] : null,
            'is_recurring' => isset($data['is_recurring']) && $data['is_recurring'] ? 1 : 0,
            'recurrence_pattern' => !empty($data['recurrence_pattern']) ? $data['recurrence_pattern'] : null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update completed_at if status changed to done
        if ($data['status'] === 'done' && $task['status'] !== 'done') {
            $taskData['completed_at'] = date('Y-m-d H:i:s');
        } elseif ($data['status'] !== 'done' && $task['status'] === 'done') {
            $taskData['completed_at'] = null;
        }

        // Update task
        $result = $this->taskModel->update($id, $taskData);

        if ($result) {
            Session::setFlash('success', 'Task updated successfully');
            $this->redirect('/tasks/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update task');

            // Get categories for dropdown
            $categories = $this->categoryModel->getUserCategories($userId);

            $this->view('tasks/edit', [
                'task' => array_merge($task, $data),
                'categories' => $categories
            ]);
        }
    }

    /**
     * Delete task
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get task
        $task = $this->taskModel->find($id);

        // Verify task exists and belongs to user
        if (!$task || $task['user_id'] != $userId) {
            Session::setFlash('error', 'Task not found');
            $this->redirect('/tasks');
        }

        // Delete task
        $result = $this->taskModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Task deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete task');
        }

        // Redirect to parent task if this is a subtask
        if ($task['parent_id']) {
            $this->redirect('/tasks/view/' . $task['parent_id']);
        } else {
            $this->redirect('/tasks');
        }
    }

    /**
     * Mark task as complete
     */
    public function complete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get task
        $task = $this->taskModel->find($id);

        // Verify task exists and belongs to user
        if (!$task || $task['user_id'] != $userId) {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Task not found'], 404);
            } else {
                Session::setFlash('error', 'Task not found');
                $this->redirect('/tasks');
            }
            return;
        }

        // Mark task as complete
        $result = $this->taskModel->markAsComplete($id, $userId);

        if ($result) {
            if ($this->isAjax()) {
                $this->json(['success' => true]);
            } else {
                Session::setFlash('success', 'Task marked as complete');

                // Check if this was the current focus task
                $user = Session::getUser();
                if (!empty($user['current_focus_task_id']) && $user['current_focus_task_id'] == $id) {
                    // Clear the current focus task
                    require_once __DIR__ . '/../models/User.php';
                    $userModel = new User();

                    // Get database instance
                    $db = Database::getInstance();

                    // Use direct SQL update to bypass any constraint issues
                    $updateSql = "UPDATE users SET current_focus_task_id = NULL, updated_at = ? WHERE id = ?";
                    $db->query($updateSql, [date('Y-m-d H:i:s'), $userId]);

                    // Update user in session
                    $updatedUser = $userModel->find($userId);
                    Session::setUser($updatedUser);

                    // Redirect to dashboard
                    $this->redirect('/dashboard');
                    return;
                }

                // Check if we came from the dashboard
                $referer = $_SERVER['HTTP_REFERER'] ?? '';
                if (strpos($referer, '/dashboard') !== false) {
                    $this->redirect('/dashboard');
                } else {
                    $this->redirect('/tasks');
                }
            }
        } else {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Failed to mark task as complete'], 500);
            } else {
                Session::setFlash('error', 'Failed to mark task as complete');
                $this->redirect('/tasks');
            }
        }
    }

    /**
     * Set task as current focus
     */
    public function setFocus($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get task
        $task = $this->taskModel->find($id);

        // Verify task exists and belongs to user
        if (!$task || $task['user_id'] != $userId) {
            Session::setFlash('error', 'Task not found');
            $this->redirect('/dashboard');
            return;
        }

        // Get database instance
        $db = Database::getInstance();

        // Use direct SQL update to bypass any constraint issues
        $updateSql = "UPDATE users SET current_focus_task_id = ?, updated_at = ? WHERE id = ?";
        $updateResult = $db->query($updateSql, [$id, date('Y-m-d H:i:s'), $userId]);

        if ($updateResult) {
            // Update user in session
            require_once __DIR__ . '/../models/User.php';
            $userModel = new User();
            $updatedUser = $userModel->find($userId);
            Session::setUser($updatedUser);

            Session::setFlash('success', 'Task set as current focus');
        } else {
            Session::setFlash('error', 'Failed to set task as current focus');
        }

        // Redirect back to dashboard
        $this->redirect('/dashboard');
    }
}
