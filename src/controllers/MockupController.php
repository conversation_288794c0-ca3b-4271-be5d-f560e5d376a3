<?php
/**
 * Mockup Controller
 *
 * This controller handles the display of mockups for the new dashboard design.
 */

namespace App\Controllers;

use App\Models\MockupTask;

class MockupController extends BaseController
{
    /**
     * Display the dashboard mockup
     */
    public function dashboard()
    {
        // Check if user is logged in
        if (!isset($_SESSION['user'])) {
            header('Location: /momentum/login');
            exit;
        }

        // Set the title
        $title = 'Dashboard Mockup';

        // Set dark mode based on user preference
        $darkMode = isset($_SESSION['dark_mode']) && $_SESSION['dark_mode'];

        // Get mockup data
        $userId = $_SESSION['user']['id'] ?? 1;
        $currentFocusTask = MockupTask::getCurrentFocusTask($userId);
        $adhdTasks = MockupTask::getADHDTasks();
        $productivityTasks = MockupTask::getProductivityTasks();

        // Pass data to the view
        $viewData = [
            'currentFocusTask' => $currentFocusTask,
            'adhdTasks' => $adhdTasks,
            'productivityTasks' => $productivityTasks
        ];

        // Include the layout with the mockup content
        $content = $this->view('dashboard/mockup', $viewData);

        // Use the simplified navigation
        $useSimplifiedNav = true;

        include dirname(__DIR__) . '/views/layouts/default.php';
    }

    /**
     * Display the HTML mockup directly
     */
    public function htmlMockup()
    {
        // Redirect to the HTML mockup
        header('Location: /momentum/public/dashboard-mockup.html');
        exit;
    }
}
