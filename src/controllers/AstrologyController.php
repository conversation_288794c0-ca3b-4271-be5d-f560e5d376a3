<?php

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/AstrologyModel.php';

class AstrologyController extends BaseController {
    private $astrologyModel;

    public function __construct() {
        $this->astrologyModel = new AstrologyModel();
    }

    /**
     * Show main astrology dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get current day's Rahu Kalaya timing
        $currentDay = date('l'); // Full day name (Monday, Tuesday, etc.)
        $rahuKalayaToday = $this->astrologyModel->getRahuKalayaForDay($currentDay);
        $nightRahuKalayaToday = $this->astrologyModel->getNightRahuKalayaForDay($currentDay);

        // Get all weekly Rahu Kalaya timings
        $weeklyRahuKalaya = $this->astrologyModel->getAllRahuKalayaTimings();
        $weeklyNightRahuKalaya = $this->astrologyModel->getAllNightRahuKalayaTimings();
        $weeklyBothRahuKalaya = $this->astrologyModel->getAllRahuKalayaTimingsBoth();

        // Check if current time is in Rahu Kalaya period
        $currentRahuKalayaStatus = $this->astrologyModel->isCurrentTimeRahuKalaya();
        $isCurrentlyRahuKalaya = $currentRahuKalayaStatus['active'];

        // Get user's astrology preferences if any
        $userPreferences = $this->astrologyModel->getUserPreferences($userId);

        $this->view('astrology/index', [
            'rahuKalayaToday' => $rahuKalayaToday,
            'nightRahuKalayaToday' => $nightRahuKalayaToday,
            'weeklyRahuKalaya' => $weeklyRahuKalaya,
            'weeklyNightRahuKalaya' => $weeklyNightRahuKalaya,
            'weeklyBothRahuKalaya' => $weeklyBothRahuKalaya,
            'isCurrentlyRahuKalaya' => $isCurrentlyRahuKalaya,
            'currentRahuKalayaStatus' => $currentRahuKalayaStatus,
            'userPreferences' => $userPreferences,
            'currentDay' => $currentDay
        ]);
    }

    /**
     * Show Rahu Kalaya schedule page
     */
    public function rahuKalaya() {
        $this->requireLogin();

        // Get all weekly Rahu Kalaya timings
        $weeklyRahuKalaya = $this->astrologyModel->getAllRahuKalayaTimings();
        $weeklyNightRahuKalaya = $this->astrologyModel->getAllNightRahuKalayaTimings();
        $weeklyBothRahuKalaya = $this->astrologyModel->getAllRahuKalayaTimingsBoth();

        // Get current day's timing
        $currentDay = date('l');
        $rahuKalayaToday = $this->astrologyModel->getRahuKalayaForDay($currentDay);
        $nightRahuKalayaToday = $this->astrologyModel->getNightRahuKalayaForDay($currentDay);

        // Check if current time is in Rahu Kalaya period
        $currentRahuKalayaStatus = $this->astrologyModel->isCurrentTimeRahuKalaya();
        $isCurrentlyRahuKalaya = $currentRahuKalayaStatus['active'];

        $this->view('astrology/rahu-kalaya', [
            'weeklyRahuKalaya' => $weeklyRahuKalaya,
            'weeklyNightRahuKalaya' => $weeklyNightRahuKalaya,
            'weeklyBothRahuKalaya' => $weeklyBothRahuKalaya,
            'rahuKalayaToday' => $rahuKalayaToday,
            'nightRahuKalayaToday' => $nightRahuKalayaToday,
            'isCurrentlyRahuKalaya' => $isCurrentlyRahuKalaya,
            'currentRahuKalayaStatus' => $currentRahuKalayaStatus,
            'currentDay' => $currentDay
        ]);
    }

    /**
     * Show astrology information and education page
     */
    public function info() {
        $this->requireLogin();

        $this->view('astrology/info');
    }

    /**
     * API endpoint to get current Rahu Kalaya status
     */
    public function getCurrentStatus() {
        header('Content-Type: application/json');

        $currentDay = date('l');
        $rahuKalayaToday = $this->astrologyModel->getRahuKalayaForDay($currentDay);
        $isCurrentlyRahuKalaya = $this->astrologyModel->isCurrentTimeRahuKalaya();

        echo json_encode([
            'success' => true,
            'currentDay' => $currentDay,
            'rahuKalayaToday' => $rahuKalayaToday,
            'isCurrentlyRahuKalaya' => $isCurrentlyRahuKalaya,
            'currentTime' => date('H:i:s')
        ]);
    }

    /**
     * Save user astrology preferences
     */
    public function savePreferences() {
        $this->requireLogin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/astrology');
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $notifications = isset($_POST['notifications']) ? 1 : 0;
        $location = $_POST['location'] ?? 'Sri Lanka';
        $timezone = $_POST['timezone'] ?? 'Asia/Colombo';

        $success = $this->astrologyModel->saveUserPreferences($userId, [
            'notifications' => $notifications,
            'location' => $location,
            'timezone' => $timezone
        ]);

        if ($success) {
            Session::setFlash('success', 'Astrology preferences saved successfully!');
        } else {
            Session::setFlash('error', 'Failed to save preferences. Please try again.');
        }

        $this->redirect('/astrology');
    }
}
