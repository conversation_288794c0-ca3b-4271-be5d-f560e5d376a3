<?php
/**
 * Income Evaluator Controller
 *
 * Handles income opportunity evaluation and comparison functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/IncomeEvaluator.php';
require_once __DIR__ . '/../models/IncomeOpportunity.php';
require_once __DIR__ . '/../utils/Session.php';

class IncomeEvaluatorController extends BaseController {
    private $evaluatorModel;
    private $opportunityModel;

    public function __construct() {
        $this->evaluatorModel = new IncomeEvaluator();
        $this->opportunityModel = new IncomeOpportunity();
    }

    /**
     * Show evaluator dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get user's comparisons
        $comparisons = $this->evaluatorModel->getUserComparisons($userId);

        // Get user's opportunities for quick comparison
        $opportunities = $this->opportunityModel->getUserOpportunities($userId);

        $this->view('evaluator/index', [
            'comparisons' => $comparisons,
            'opportunities' => $opportunities
        ]);
    }

    /**
     * Show comparison creation form
     */
    public function create() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get user's opportunities
        $opportunities = $this->opportunityModel->getUserOpportunities($userId);

        // Get evaluation criteria
        $criteria = $this->evaluatorModel->getCriteria($userId);

        // Group criteria by type
        $criteriaByType = [];
        foreach ($criteria as $criterion) {
            $criteriaByType[$criterion['criteria_type']][] = $criterion;
        }

        $this->view('evaluator/create', [
            'opportunities' => $opportunities,
            'criteriaByType' => $criteriaByType
        ]);
    }

    /**
     * Store a new comparison
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        $requiredFields = ['name', 'opportunity_ids'];
        $errors = $this->validateRequired($data, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');

            // Get user's opportunities
            $opportunities = $this->opportunityModel->getUserOpportunities($userId);

            // Get evaluation criteria
            $criteria = $this->evaluatorModel->getCriteria($userId);

            // Group criteria by type
            $criteriaByType = [];
            foreach ($criteria as $criterion) {
                $criteriaByType[$criterion['criteria_type']][] = $criterion;
            }

            $this->view('evaluator/create', [
                'data' => $data,
                'errors' => $errors,
                'opportunities' => $opportunities,
                'criteriaByType' => $criteriaByType
            ]);
            return;
        }

        // Create comparison
        $comparisonData = [
            'user_id' => $userId,
            'name' => $data['name'],
            'description' => !empty($data['description']) ? $data['description'] : null,
            'date_created' => date('Y-m-d H:i:s'),
            'last_updated' => date('Y-m-d H:i:s')
        ];

        $comparisonId = $this->evaluatorModel->createComparison($comparisonData);

        if (!$comparisonId) {
            Session::setFlash('error', 'Failed to create comparison');
            $this->redirect('/income-evaluator/create');
            return;
        }

        // Add opportunities to comparison
        $opportunityIds = is_array($data['opportunity_ids']) ? $data['opportunity_ids'] : [$data['opportunity_ids']];
        
        foreach ($opportunityIds as $opportunityId) {
            $this->evaluatorModel->addOpportunityToComparison($comparisonId, $opportunityId);
        }

        Session::setFlash('success', 'Comparison created successfully');
        $this->redirect('/income-evaluator/compare/' . $comparisonId);
    }

    /**
     * Show comparison interface
     */
    public function compare($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get comparison
        $comparison = $this->evaluatorModel->getComparison($id, $userId);

        if (!$comparison) {
            Session::setFlash('error', 'Comparison not found');
            $this->redirect('/income-evaluator');
            return;
        }

        // Get evaluation criteria
        $criteria = $this->evaluatorModel->getCriteria($userId);

        // Group criteria by type
        $criteriaByType = [];
        foreach ($criteria as $criterion) {
            $criteriaByType[$criterion['criteria_type']][] = $criterion;
        }

        // Get scores if they exist
        $scores = $this->evaluatorModel->getComparisonScores($id);

        $this->view('evaluator/compare', [
            'comparison' => $comparison,
            'criteriaByType' => $criteriaByType,
            'scores' => $scores
        ]);
    }

    /**
     * Save comparison scores
     */
    public function saveScores($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get form data
        $data = $this->getPostData();

        // Validate comparison ownership
        $comparison = $this->evaluatorModel->getComparison($id, $userId);
        if (!$comparison) {
            Session::setFlash('error', 'Comparison not found');
            $this->redirect('/income-evaluator');
            return;
        }

        // Process scores for each opportunity
        foreach ($comparison['opportunities'] as $opportunity) {
            $opportunityId = $opportunity['opportunity_id'];
            $scores = [];
            $notes = [];

            // Extract scores and notes for this opportunity
            foreach ($data as $key => $value) {
                if (strpos($key, "score_{$opportunityId}_") === 0) {
                    $criteriaId = substr($key, strlen("score_{$opportunityId}_"));
                    $scores[$criteriaId] = $value;
                }

                if (strpos($key, "note_{$opportunityId}_") === 0) {
                    $criteriaId = substr($key, strlen("note_{$opportunityId}_"));
                    $notes[$criteriaId] = $value;
                }
            }

            // Save scores
            if (!empty($scores)) {
                $this->evaluatorModel->saveCriteriaScores($id, $opportunityId, $scores, $notes);
            }
        }

        // Update rankings
        $this->evaluatorModel->updateRankings($id);

        // Update last_updated timestamp
        $this->evaluatorModel->updateComparison($id, [
            'last_updated' => date('Y-m-d H:i:s')
        ]);

        Session::setFlash('success', 'Scores saved successfully');
        $this->redirect('/income-evaluator/results/' . $id);
    }

    /**
     * Show comparison results
     */
    public function results($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get comparison
        $comparison = $this->evaluatorModel->getComparison($id, $userId);

        if (!$comparison) {
            Session::setFlash('error', 'Comparison not found');
            $this->redirect('/income-evaluator');
            return;
        }

        // Get scores
        $scores = $this->evaluatorModel->getComparisonScores($id);

        // Get evaluation criteria
        $criteria = $this->evaluatorModel->getCriteria($userId);

        // Group criteria by type
        $criteriaByType = [];
        foreach ($criteria as $criterion) {
            $criteriaByType[$criterion['criteria_type']][] = $criterion;
        }

        $this->view('evaluator/results', [
            'comparison' => $comparison,
            'scores' => $scores,
            'criteriaByType' => $criteriaByType
        ]);
    }

    /**
     * Delete a comparison
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Delete comparison
        $result = $this->evaluatorModel->deleteComparison($id, $userId);

        if ($result) {
            Session::setFlash('success', 'Comparison deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete comparison');
        }

        $this->redirect('/income-evaluator');
    }
}
