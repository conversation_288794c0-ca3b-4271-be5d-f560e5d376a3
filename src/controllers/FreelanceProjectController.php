<?php
/**
 * Freelance Project Controller
 *
 * Handles freelance project management functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/FreelanceProject.php';
require_once __DIR__ . '/../models/FreelanceClient.php';
require_once __DIR__ . '/../utils/Session.php';

class FreelanceProjectController extends BaseController {
    private $projectModel;
    private $clientModel;

    public function __construct() {
        $this->projectModel = new FreelanceProject();
        $this->clientModel = new FreelanceClient();
    }

    /**
     * Show projects list
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get projects based on filters
        $projects = $this->projectModel->getUserProjects($userId, $filters);

        // Get project summary
        $projectSummary = $this->projectModel->getProjectSummary($userId);

        // Get clients for filter dropdown
        $clients = $this->clientModel->getUserClients($userId);

        $this->view('freelance/projects/index', [
            'projects' => $projects,
            'projectSummary' => $projectSummary,
            'clients' => $clients,
            'filters' => $filters
        ]);
    }

    /**
     * Show project creation form
     */
    public function create() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all clients for the user
        $clients = $this->clientModel->getUserClients($userId);

        $this->view('freelance/projects/create', [
            'clients' => $clients
        ]);
    }

    /**
     * Process project creation
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        if (empty($data['name']) || empty($data['client_id'])) {
            Session::setFlash('error', 'Project name and client are required');
            $this->view('freelance/projects/create', [
                'data' => $data,
                'clients' => $this->clientModel->getUserClients($userId)
            ]);
            return;
        }

        // Prepare project data
        $projectData = [
            'user_id' => $userId,
            'client_id' => $data['client_id'],
            'name' => $data['name'],
            'description' => !empty($data['description']) ? $data['description'] : null,
            'start_date' => !empty($data['start_date']) ? $data['start_date'] : null,
            'deadline' => !empty($data['deadline']) ? $data['deadline'] : null,
            'status' => !empty($data['status']) ? $data['status'] : 'in_progress',
            'payment_type' => !empty($data['billing_type']) ? $data['billing_type'] : 'fixed',
            'notes' => !empty($data['notes']) ? $data['notes'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add the appropriate price field based on payment type
        if (!empty($data['value'])) {
            if ($projectData['payment_type'] === 'hourly') {
                $projectData['hourly_rate'] = $data['value'];
            } else {
                $projectData['fixed_price'] = $data['value'];
            }
        }

        // Create project
        $projectId = $this->projectModel->create($projectData);

        if ($projectId) {
            Session::setFlash('success', 'Project created successfully');
            $this->redirect('/freelance/projects/view/' . $projectId);
        } else {
            Session::setFlash('error', 'Failed to create project');
            $this->view('freelance/projects/create', [
                'data' => $data,
                'clients' => $this->clientModel->getUserClients($userId)
            ]);
        }
    }

    /**
     * Show project details
     */
    public function viewProject($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project details
        $project = $this->projectModel->getProjectDetails($id, $userId);

        // Verify project exists and belongs to user
        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/freelance/projects');
            return;
        }

        $this->view('freelance/projects/view', [
            'project' => $project
        ]);
    }

    /**
     * Show project edit form
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project details
        $project = $this->projectModel->getProjectDetails($id, $userId);

        // Verify project exists and belongs to user
        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/freelance/projects');
            return;
        }

        // Get clients for dropdown
        $clients = $this->clientModel->getUserClients($userId);

        $this->view('freelance/projects/edit', [
            'project' => $project,
            'clients' => $clients
        ]);
    }

    /**
     * Process project update
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project details
        $project = $this->projectModel->getProjectDetails($id, $userId);

        // Verify project exists and belongs to user
        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/freelance/projects');
            return;
        }

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        if (empty($data['name']) || empty($data['client_id'])) {
            Session::setFlash('error', 'Project name and client are required');

            // Get clients for dropdown
            $clients = $this->clientModel->getUserClients($userId);

            $this->view('freelance/projects/edit', [
                'project' => array_merge($project, $data),
                'clients' => $clients
            ]);
            return;
        }

        // Prepare project data
        $projectData = [
            'client_id' => $data['client_id'],
            'name' => $data['name'],
            'description' => !empty($data['description']) ? $data['description'] : null,
            'start_date' => !empty($data['start_date']) ? $data['start_date'] : null,
            'deadline' => !empty($data['deadline']) ? $data['deadline'] : null,
            'status' => !empty($data['status']) ? $data['status'] : 'in_progress',
            'payment_type' => !empty($data['billing_type']) ? $data['billing_type'] : 'fixed',
            'notes' => !empty($data['notes']) ? $data['notes'] : null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add the appropriate price field based on payment type
        if (!empty($data['value'])) {
            if ($projectData['payment_type'] === 'hourly') {
                $projectData['hourly_rate'] = $data['value'];
            } else {
                $projectData['fixed_price'] = $data['value'];
            }
        }

        // Update project
        $result = $this->projectModel->update($id, $projectData);

        if ($result) {
            Session::setFlash('success', 'Project updated successfully');
            $this->redirect('/freelance/projects/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update project');

            // Get clients for dropdown
            $clients = $this->clientModel->getUserClients($userId);

            $this->view('freelance/projects/edit', [
                'project' => array_merge($project, $data),
                'clients' => $clients
            ]);
        }
    }

    /**
     * Process project deletion
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project details
        $project = $this->projectModel->getProjectDetails($id, $userId);

        // Verify project exists and belongs to user
        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/freelance/projects');
            return;
        }

        // Delete project
        $result = $this->projectModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Project deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete project');
        }

        $this->redirect('/freelance/projects');
    }

    /**
     * Show milestone creation form
     */
    public function createMilestone($id) {
        $this->requireLogin();

        // This is a stub method - implementation will be added later
        $this->view('freelance/milestones/create', []);
    }

    /**
     * Process milestone creation
     */
    public function storeMilestone($id) {
        $this->requireLogin();

        // This is a stub method - implementation will be added later
        $this->redirect('/freelance/projects/view/' . $id);
    }

    /**
     * Show milestone edit form
     */
    public function editMilestone($id) {
        $this->requireLogin();

        // This is a stub method - implementation will be added later
        $this->view('freelance/milestones/edit', []);
    }

    /**
     * Process milestone update
     */
    public function updateMilestone($id) {
        $this->requireLogin();

        // This is a stub method - implementation will be added later
        $this->redirect('/freelance/projects/view/' . $id);
    }

    /**
     * Process milestone deletion
     */
    public function deleteMilestone($id) {
        $this->requireLogin();

        // This is a stub method - implementation will be added later
        $this->redirect('/freelance/projects/view/' . $id);
    }
}
