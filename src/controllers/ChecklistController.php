<?php
/**
 * Checklist Controller
 *
 * Handles the management of checklists and checklist templates
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Checklist.php';
require_once __DIR__ . '/../models/ChecklistItem.php';
require_once __DIR__ . '/../models/ChecklistTemplate.php';
require_once __DIR__ . '/../models/Project.php';
require_once __DIR__ . '/../utils/Session.php';

class ChecklistController extends BaseController {
    protected $checklistModel;
    protected $checklistItemModel;
    protected $templateModel;
    protected $projectModel;

    public function __construct() {
        parent::__construct();
        $this->checklistModel = new Checklist();
        $this->checklistItemModel = new ChecklistItem();
        $this->templateModel = new ChecklistTemplate();
        $this->projectModel = new Project();
    }

    /**
     * Display all checklists for the current user
     */
    public function index() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        $checklists = $this->checklistModel->getUserChecklists($userId);

        // Get completion stats for each checklist
        foreach ($checklists as &$checklist) {
            $checklist['stats'] = $this->checklistModel->getCompletionStats($checklist['id']);
        }

        $this->view('checklists/index', [
            'checklists' => $checklists,
            'title' => 'My Checklists'
        ]);
    }

    /**
     * Display a specific checklist
     *
     * @param int $id Checklist ID
     */
    public function viewChecklist($id) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        $checklist = $this->checklistModel->getChecklist($id, $userId);

        if (!$checklist) {
            Session::setFlash('error', 'Checklist not found');
            $this->redirect('/checklists');
            return;
        }

        // Get completion stats
        $stats = $this->checklistModel->getCompletionStats($id);

        // Get project details if associated with a project
        $project = null;
        if (!empty($checklist['project_id'])) {
            $project = $this->projectModel->getProjectDetails($checklist['project_id'], $userId);
        }

        $this->view('checklists/view', [
            'checklist' => $checklist,
            'stats' => $stats,
            'project' => $project,
            'title' => $checklist['name']
        ]);
    }

    /**
     * Display the form to create a new checklist
     */
    public function create() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        // Get all templates
        $systemTemplates = $this->templateModel->getSystemTemplates();
        $userTemplates = $this->templateModel->getUserTemplates($userId);

        // Get all projects
        $projects = $this->projectModel->getUserProjects($userId);

        $this->view('checklists/create', [
            'systemTemplates' => $systemTemplates,
            'userTemplates' => $userTemplates,
            'projects' => $projects,
            'title' => 'Create Checklist'
        ]);
    }

    /**
     * Process the creation of a new checklist
     */
    public function store() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/checklists/create');
            return;
        }

        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $projectId = !empty($_POST['project_id']) ? (int)$_POST['project_id'] : null;
        $templateId = !empty($_POST['template_id']) ? (int)$_POST['template_id'] : null;

        if (empty($name)) {
            Session::setFlash('error', 'Checklist name is required');
            $this->redirect('/checklists/create');
            return;
        }

        // Create the checklist
        $checklistData = [
            'user_id' => $userId,
            'project_id' => $projectId,
            'template_id' => $templateId,
            'name' => $name,
            'description' => $description,
            'checklist_type' => 'standard',
            'status' => 'active'
        ];

        $checklistId = $this->checklistModel->create($checklistData);

        if (!$checklistId) {
            Session::setFlash('error', 'Failed to create checklist');
            $this->redirect('/checklists/create');
            return;
        }

        Session::setFlash('success', 'Checklist created successfully');
        $this->redirect('/checklists/view/' . $checklistId);
    }

    /**
     * Display the form to edit a checklist
     *
     * @param int $id Checklist ID
     */
    public function edit($id) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        $checklist = $this->checklistModel->getChecklist($id, $userId);

        if (!$checklist) {
            Session::setFlash('error', 'Checklist not found');
            $this->redirect('/checklists');
            return;
        }

        // Get all projects
        $projects = $this->projectModel->getUserProjects($userId);

        $this->view('checklists/edit', [
            'checklist' => $checklist,
            'projects' => $projects,
            'title' => 'Edit Checklist'
        ]);
    }

    /**
     * Process the update of a checklist
     *
     * @param int $id Checklist ID
     */
    public function update($id) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/checklists/edit/' . $id);
            return;
        }

        $checklist = $this->checklistModel->getChecklist($id, $userId);

        if (!$checklist) {
            Session::setFlash('error', 'Checklist not found');
            $this->redirect('/checklists');
            return;
        }

        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $projectId = !empty($_POST['project_id']) ? (int)$_POST['project_id'] : null;
        $status = $_POST['status'] ?? 'active';

        if (empty($name)) {
            Session::setFlash('error', 'Checklist name is required');
            $this->redirect('/checklists/edit/' . $id);
            return;
        }

        // Update the checklist
        $checklistData = [
            'name' => $name,
            'description' => $description,
            'project_id' => $projectId,
            'status' => $status
        ];

        $result = $this->checklistModel->update($id, $checklistData);

        if (!$result) {
            Session::setFlash('error', 'Failed to update checklist');
            $this->redirect('/checklists/edit/' . $id);
            return;
        }

        Session::setFlash('success', 'Checklist updated successfully');
        $this->redirect('/checklists/view/' . $id);
    }

    /**
     * Delete a checklist
     *
     * @param int $id Checklist ID
     */
    public function delete($id) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        $checklist = $this->checklistModel->getChecklist($id, $userId);

        if (!$checklist) {
            Session::setFlash('error', 'Checklist not found');
            $this->redirect('/checklists');
            return;
        }

        $result = $this->checklistModel->deleteIfOwner($id, $userId);

        if (!$result) {
            Session::setFlash('error', 'Failed to delete checklist');
            $this->redirect('/checklists/view/' . $id);
            return;
        }

        Session::setFlash('success', 'Checklist deleted successfully');
        $this->redirect('/checklists');
    }

    /**
     * Update the status of a checklist item
     *
     * @param int $id Item ID
     */
    public function updateItemStatus() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            exit;
        }

        $data = json_decode(file_get_contents('php://input'), true);

        $itemId = $data['item_id'] ?? 0;
        $status = $data['status'] ?? '';

        if (empty($itemId) || empty($status)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Item ID and status are required']);
            exit;
        }

        // Get the item to verify it belongs to the user
        $item = $this->checklistItemModel->getItem($itemId);

        if (!$item) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Item not found']);
            exit;
        }

        // Get the checklist to verify ownership
        $checklist = $this->checklistModel->getChecklist($item['checklist_id'], $userId);

        if (!$checklist) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'You do not have permission to update this item']);
            exit;
        }

        // Update the item status
        $result = $this->checklistItemModel->updateStatus($itemId, $status);

        if (!$result) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to update item status']);
            exit;
        }

        // Get updated stats
        $stats = $this->checklistModel->getCompletionStats($item['checklist_id']);

        echo json_encode([
            'success' => true,
            'message' => 'Item status updated successfully',
            'stats' => $stats
        ]);
        exit;
    }

    /**
     * Add a new item to a checklist
     *
     * @param int $id Checklist ID
     */
    public function addItem($id) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/checklists/view/' . $id);
            return;
        }

        $checklist = $this->checklistModel->getChecklist($id, $userId);

        if (!$checklist) {
            Session::setFlash('error', 'Checklist not found');
            $this->redirect('/checklists');
            return;
        }

        $text = $_POST['text'] ?? '';
        $description = $_POST['description'] ?? '';
        $parentId = !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;
        $resourceLink = $_POST['resource_link'] ?? null;

        if (empty($text)) {
            Session::setFlash('error', 'Item text is required');
            $this->redirect('/checklists/view/' . $id);
            return;
        }

        // Create the item
        $itemData = [
            'checklist_id' => $id,
            'parent_id' => $parentId,
            'text' => $text,
            'description' => $description,
            'status' => 'pending',
            'resource_link' => $resourceLink
        ];

        $itemId = $this->checklistItemModel->create($itemData);

        if (!$itemId) {
            Session::setFlash('error', 'Failed to add item');
            $this->redirect('/checklists/view/' . $id);
            return;
        }

        Session::setFlash('success', 'Item added successfully');
        $this->redirect('/checklists/view/' . $id);
    }

    /**
     * Delete a checklist item
     *
     * @param int $id Item ID
     */
    public function deleteItem($id) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        // Get the item to verify it belongs to the user
        $item = $this->checklistItemModel->getItem($id);

        if (!$item) {
            Session::setFlash('error', 'Item not found');
            $this->redirect('/checklists');
            return;
        }

        // Get the checklist to verify ownership
        $checklist = $this->checklistModel->getChecklist($item['checklist_id'], $userId);

        if (!$checklist) {
            Session::setFlash('error', 'You do not have permission to delete this item');
            $this->redirect('/checklists');
            return;
        }

        $result = $this->checklistItemModel->delete($id);

        if (!$result) {
            Session::setFlash('error', 'Failed to delete item');
            $this->redirect('/checklists/view/' . $item['checklist_id']);
            return;
        }

        Session::setFlash('success', 'Item deleted successfully');
        $this->redirect('/checklists/view/' . $item['checklist_id']);
    }

    /**
     * Display all templates
     */
    public function templates() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        $systemTemplates = $this->templateModel->getSystemTemplates();
        $userTemplates = $this->templateModel->getUserTemplates($userId);

        $this->view('checklists/templates', [
            'systemTemplates' => $systemTemplates,
            'userTemplates' => $userTemplates,
            'title' => 'Checklist Templates'
        ]);
    }

    /**
     * Display a specific template
     *
     * @param int $id Template ID
     */
    public function viewTemplate($id) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        $template = $this->templateModel->getTemplateWithItems($id);

        if (!$template) {
            Session::setFlash('error', 'Template not found');
            $this->redirect('/checklists/templates');
            return;
        }

        $this->view('checklists/view_template', [
            'template' => $template,
            'title' => $template['name']
        ]);
    }

    /**
     * Display the form to create a new template
     */
    public function createTemplate() {
        $this->requireLogin();

        $this->view('checklists/create_template', [
            'title' => 'Create Template'
        ]);
    }

    /**
     * Process the creation of a new template
     */
    public function storeTemplate() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/checklists/create-template');
            return;
        }

        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $category = $_POST['category'] ?? 'general';

        if (empty($name)) {
            Session::setFlash('error', 'Template name is required');
            $this->redirect('/checklists/create-template');
            return;
        }

        // Create the template
        $templateData = [
            'name' => $name,
            'description' => $description,
            'template_type' => 'standard',
            'category' => $category,
            'is_system_template' => 0,
            'created_by' => $userId
        ];

        $templateId = $this->templateModel->create($templateData);

        if (!$templateId) {
            Session::setFlash('error', 'Failed to create template');
            $this->redirect('/checklists/create-template');
            return;
        }

        Session::setFlash('success', 'Template created successfully');
        $this->redirect('/checklists/view-template/' . $templateId);
    }

    /**
     * Create a template from an existing checklist
     *
     * @param int $id Checklist ID
     */
    public function createTemplateFromChecklist($id) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        $checklist = $this->checklistModel->getChecklist($id, $userId);

        if (!$checklist) {
            Session::setFlash('error', 'Checklist not found');
            $this->redirect('/checklists');
            return;
        }

        $this->view('checklists/create_template_from_checklist', [
            'checklist' => $checklist,
            'title' => 'Create Template from Checklist'
        ]);
    }

    /**
     * Process the creation of a template from a checklist
     *
     * @param int $id Checklist ID
     */
    public function storeTemplateFromChecklist($id) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/checklists/create-template-from-checklist/' . $id);
            return;
        }

        $checklist = $this->checklistModel->getChecklist($id, $userId);

        if (!$checklist) {
            Session::setFlash('error', 'Checklist not found');
            $this->redirect('/checklists');
            return;
        }

        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $category = $_POST['category'] ?? 'general';

        if (empty($name)) {
            Session::setFlash('error', 'Template name is required');
            $this->redirect('/checklists/create-template-from-checklist/' . $id);
            return;
        }

        // Create the template from the checklist
        $templateData = [
            'name' => $name,
            'description' => $description,
            'category' => $category
        ];

        $templateId = $this->templateModel->createFromChecklist($id, $templateData, $userId);

        if (!$templateId) {
            Session::setFlash('error', 'Failed to create template from checklist');
            $this->redirect('/checklists/create-template-from-checklist/' . $id);
            return;
        }

        Session::setFlash('success', 'Template created successfully from checklist');
        $this->redirect('/checklists/view-template/' . $templateId);
    }

    /**
     * Delete a template
     *
     * @param int $id Template ID
     */
    public function deleteTemplate($id) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        $result = $this->templateModel->deleteIfOwner($id, $userId);

        if (!$result) {
            Session::setFlash('error', 'Failed to delete template. You can only delete your own templates.');
            $this->redirect('/checklists/view-template/' . $id);
            return;
        }

        Session::setFlash('success', 'Template deleted successfully');
        $this->redirect('/checklists/templates');
    }

    /**
     * Display project checklists
     *
     * @param int $projectId Project ID
     */
    public function projectChecklists($projectId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        $project = $this->projectModel->getProjectDetails($projectId, $userId);

        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/projects');
            return;
        }

        $checklists = $this->checklistModel->getProjectChecklists($projectId, $userId);

        // Get completion stats for each checklist
        foreach ($checklists as &$checklist) {
            $checklist['stats'] = $this->checklistModel->getCompletionStats($checklist['id']);
        }

        // Check if this is an AJAX request
        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
            // Return just the checklist content for AJAX requests
            $this->view('checklists/project_checklists_ajax', [
                'project' => $project,
                'checklists' => $checklists
            ]);
            return;
        }

        // Return the full page for normal requests
        $this->view('checklists/project_checklists', [
            'project' => $project,
            'checklists' => $checklists,
            'title' => 'Project Checklists: ' . $project['name']
        ]);
    }

    /**
     * Create a checklist for a project
     *
     * @param int $projectId Project ID
     */
    public function createProjectChecklist($projectId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        $project = $this->projectModel->getProjectDetails($projectId, $userId);

        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/projects');
            return;
        }

        // Get all templates
        $systemTemplates = $this->templateModel->getSystemTemplates();
        $userTemplates = $this->templateModel->getUserTemplates($userId);

        $this->view('checklists/create_project_checklist', [
            'project' => $project,
            'systemTemplates' => $systemTemplates,
            'userTemplates' => $userTemplates,
            'title' => 'Create Checklist for Project: ' . $project['name']
        ]);
    }

    /**
     * Process the creation of a checklist for a project
     *
     * @param int $projectId Project ID
     */
    public function storeProjectChecklist($projectId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/checklists/create-project-checklist/' . $projectId);
            return;
        }

        $project = $this->projectModel->getProjectDetails($projectId, $userId);

        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/projects');
            return;
        }

        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $templateId = !empty($_POST['template_id']) ? (int)$_POST['template_id'] : null;

        if (empty($name)) {
            Session::setFlash('error', 'Checklist name is required');
            $this->redirect('/checklists/create-project-checklist/' . $projectId);
            return;
        }

        // Create the checklist
        $checklistData = [
            'user_id' => $userId,
            'project_id' => $projectId,
            'template_id' => $templateId,
            'name' => $name,
            'description' => $description,
            'checklist_type' => 'standard',
            'status' => 'active'
        ];

        $checklistId = $this->checklistModel->create($checklistData);

        if (!$checklistId) {
            Session::setFlash('error', 'Failed to create checklist');
            $this->redirect('/checklists/create-project-checklist/' . $projectId);
            return;
        }

        Session::setFlash('success', 'Checklist created successfully');
        $this->redirect('/checklists/view/' . $checklistId);
    }
}
