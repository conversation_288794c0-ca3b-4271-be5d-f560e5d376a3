<?php
/**
 * Freelance Payment Controller
 *
 * Handles freelance payment management functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/FreelancePayment.php';
require_once __DIR__ . '/../models/FreelanceClient.php';
require_once __DIR__ . '/../models/FreelanceInvoice.php';
require_once __DIR__ . '/../models/FreelanceProject.php';
require_once __DIR__ . '/../utils/Session.php';

class FreelancePaymentController extends BaseController {
    private $paymentModel;
    private $clientModel;
    private $invoiceModel;
    private $projectModel;

    public function __construct() {
        $this->paymentModel = new FreelancePayment();
        $this->clientModel = new FreelanceClient();
        $this->invoiceModel = new FreelanceInvoice();
        $this->projectModel = new FreelanceProject();
    }

    /**
     * Show payments list
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get payments based on filters
        $payments = $this->paymentModel->getUserPayments($userId, $filters);

        // Get payment summary
        $paymentSummary = $this->paymentModel->getPaymentSummary($userId);

        // Get clients for filter dropdown
        $clients = $this->clientModel->getUserClients($userId);

        $this->view('freelance/payments/index', [
            'payments' => $payments,
            'paymentSummary' => $paymentSummary,
            'clients' => $clients,
            'filters' => $filters
        ]);
    }

    /**
     * Show payment creation form
     */
    public function create() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all clients for the user
        $clients = $this->clientModel->getUserClients($userId);

        // Get all projects for the user
        $projects = $this->projectModel->getUserProjects($userId);

        // Get unpaid invoices for the user
        $invoices = $this->invoiceModel->getUserInvoices($userId, ['status' => 'sent']);

        // Add overdue invoices
        $overdueInvoices = $this->invoiceModel->getUserInvoices($userId, ['status' => 'overdue']);
        if ($overdueInvoices) {
            $invoices = array_merge($invoices, $overdueInvoices);
        }

        // Check if there's an invoice_id in the query string
        $queryData = $this->getQueryData();
        $data = [];

        if (!empty($queryData['invoice_id'])) {
            $data['invoice_id'] = $queryData['invoice_id'];

            // Get invoice details to pre-fill client and amount
            $invoice = $this->invoiceModel->getInvoiceDetails($queryData['invoice_id'], $userId);
            if ($invoice) {
                $data['client_id'] = $invoice['client_id'];
                $data['amount'] = $invoice['amount_due'];
                $data['project_id'] = $invoice['project_id'];
            }
        }

        if (!empty($queryData['client_id'])) {
            $data['client_id'] = $queryData['client_id'];
        }

        if (!empty($queryData['project_id'])) {
            $data['project_id'] = $queryData['project_id'];

            // Get project details to pre-fill client
            $project = $this->projectModel->getProjectDetails($queryData['project_id'], $userId);
            if ($project) {
                $data['client_id'] = $project['client_id'];
            }
        }

        $this->view('freelance/payments/create', [
            'clients' => $clients,
            'projects' => $projects,
            'invoices' => $invoices,
            'data' => $data
        ]);
    }

    /**
     * Process payment creation
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        if (empty($data['client_id']) || empty($data['amount']) ||
            empty($data['payment_date']) || empty($data['payment_method'])) {
            Session::setFlash('error', 'Client, amount, payment date, and payment method are required');

            // Get clients, projects, and invoices for dropdowns
            $clients = $this->clientModel->getUserClients($userId);
            $projects = $this->projectModel->getUserProjects($userId);
            $invoices = $this->invoiceModel->getUserInvoices($userId, ['status' => 'sent']);
            $overdueInvoices = $this->invoiceModel->getUserInvoices($userId, ['status' => 'overdue']);
            if ($overdueInvoices) {
                $invoices = array_merge($invoices, $overdueInvoices);
            }

            $this->view('freelance/payments/create', [
                'data' => $data,
                'clients' => $clients,
                'projects' => $projects,
                'invoices' => $invoices
            ]);
            return;
        }

        // Prepare payment data
        $paymentData = [
            'user_id' => $userId,
            'client_id' => $data['client_id'],
            'project_id' => !empty($data['project_id']) ? $data['project_id'] : 0, // Use 0 instead of null for NOT NULL column
            'invoice_id' => !empty($data['invoice_id']) ? $data['invoice_id'] : null,
            'amount' => $data['amount'],
            'payment_date' => $data['payment_date'],
            'payment_method' => $data['payment_method'],
            'transaction_id' => !empty($data['transaction_id']) ? $data['transaction_id'] : null,
            'notes' => !empty($data['notes']) ? $data['notes'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create payment
        $paymentId = $this->paymentModel->create($paymentData);

        if ($paymentId) {
            // If payment is for an invoice, update invoice status if needed
            if (!empty($data['invoice_id'])) {
                $invoice = $this->invoiceModel->getInvoiceDetails($data['invoice_id'], $userId);
                if ($invoice) {
                    // Calculate total payments for this invoice
                    $totalPaid = $invoice['amount_paid'] + $data['amount'];

                    // If total paid equals or exceeds total amount, mark invoice as paid
                    if ($totalPaid >= $invoice['total_amount']) {
                        $this->invoiceModel->update($data['invoice_id'], [
                            'status' => 'paid',
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                    }
                }
            }

            Session::setFlash('success', 'Payment recorded successfully');
            $this->redirect('/freelance/payments/view/' . $paymentId);
        } else {
            Session::setFlash('error', 'Failed to record payment');

            // Get clients, projects, and invoices for dropdowns
            $clients = $this->clientModel->getUserClients($userId);
            $projects = $this->projectModel->getUserProjects($userId);
            $invoices = $this->invoiceModel->getUserInvoices($userId, ['status' => 'sent']);
            $overdueInvoices = $this->invoiceModel->getUserInvoices($userId, ['status' => 'overdue']);
            if ($overdueInvoices) {
                $invoices = array_merge($invoices, $overdueInvoices);
            }

            $this->view('freelance/payments/create', [
                'data' => $data,
                'clients' => $clients,
                'projects' => $projects,
                'invoices' => $invoices
            ]);
        }
    }

    /**
     * Show payment details
     */
    public function viewPayment($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get payment details
        $payment = $this->paymentModel->getPaymentDetails($id, $userId);

        // Verify payment exists and belongs to user
        if (!$payment) {
            Session::setFlash('error', 'Payment not found');
            $this->redirect('/freelance/payments');
            return;
        }

        $this->view('freelance/payments/view', [
            'payment' => $payment
        ]);
    }

    /**
     * Show payment edit form
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get payment details
        $payment = $this->paymentModel->getPaymentDetails($id, $userId);

        // Verify payment exists and belongs to user
        if (!$payment) {
            Session::setFlash('error', 'Payment not found');
            $this->redirect('/freelance/payments');
            return;
        }

        // Get clients, projects, and invoices for dropdowns
        $clients = $this->clientModel->getUserClients($userId);
        $projects = $this->projectModel->getUserProjects($userId);
        $invoices = $this->invoiceModel->getUserInvoices($userId);

        $this->view('freelance/payments/edit', [
            'payment' => $payment,
            'clients' => $clients,
            'projects' => $projects,
            'invoices' => $invoices
        ]);
    }

    /**
     * Process payment update
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get payment details
        $payment = $this->paymentModel->getPaymentDetails($id, $userId);

        // Verify payment exists and belongs to user
        if (!$payment) {
            Session::setFlash('error', 'Payment not found');
            $this->redirect('/freelance/payments');
            return;
        }

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        if (empty($data['client_id']) || empty($data['amount']) ||
            empty($data['payment_date']) || empty($data['payment_method'])) {
            Session::setFlash('error', 'Client, amount, payment date, and payment method are required');

            // Get clients, projects, and invoices for dropdowns
            $clients = $this->clientModel->getUserClients($userId);
            $projects = $this->projectModel->getUserProjects($userId);
            $invoices = $this->invoiceModel->getUserInvoices($userId);

            $this->view('freelance/payments/edit', [
                'payment' => array_merge($payment, $data),
                'clients' => $clients,
                'projects' => $projects,
                'invoices' => $invoices
            ]);
            return;
        }

        // Prepare payment data
        $paymentData = [
            'client_id' => $data['client_id'],
            'project_id' => !empty($data['project_id']) ? $data['project_id'] : 0, // Use 0 instead of null for NOT NULL column
            'invoice_id' => !empty($data['invoice_id']) ? $data['invoice_id'] : null,
            'amount' => $data['amount'],
            'payment_date' => $data['payment_date'],
            'payment_method' => $data['payment_method'],
            'transaction_id' => !empty($data['transaction_id']) ? $data['transaction_id'] : null,
            'notes' => !empty($data['notes']) ? $data['notes'] : null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update payment
        $result = $this->paymentModel->update($id, $paymentData);

        if ($result) {
            // If payment is for an invoice, update invoice status if needed
            if (!empty($data['invoice_id'])) {
                $invoice = $this->invoiceModel->getInvoiceDetails($data['invoice_id'], $userId);
                if ($invoice) {
                    // Get all payments for this invoice
                    $invoicePayments = $this->paymentModel->getUserPayments($userId, ['invoice_id' => $data['invoice_id']]);
                    $totalPaid = 0;

                    foreach ($invoicePayments as $p) {
                        // Only count this payment if it's not the one we're currently updating
                        if ($p['id'] != $id) {
                            $totalPaid += $p['amount'];
                        }
                    }

                    // Add the current payment amount
                    $totalPaid += $data['amount'];

                    // If total paid equals or exceeds total amount, mark invoice as paid
                    if ($totalPaid >= $invoice['total_amount']) {
                        $this->invoiceModel->update($data['invoice_id'], [
                            'status' => 'paid',
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                    } else {
                        // If invoice is marked as paid but shouldn't be, revert to sent
                        if ($invoice['status'] === 'paid') {
                            $this->invoiceModel->update($data['invoice_id'], [
                                'status' => 'sent',
                                'updated_at' => date('Y-m-d H:i:s')
                            ]);
                        }
                    }
                }
            }

            Session::setFlash('success', 'Payment updated successfully');
            $this->redirect('/freelance/payments/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update payment');

            // Get clients, projects, and invoices for dropdowns
            $clients = $this->clientModel->getUserClients($userId);
            $projects = $this->projectModel->getUserProjects($userId);
            $invoices = $this->invoiceModel->getUserInvoices($userId);

            $this->view('freelance/payments/edit', [
                'payment' => array_merge($payment, $data),
                'clients' => $clients,
                'projects' => $projects,
                'invoices' => $invoices
            ]);
        }
    }

    /**
     * Process payment deletion
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get payment details
        $payment = $this->paymentModel->getPaymentDetails($id, $userId);

        // Verify payment exists and belongs to user
        if (!$payment) {
            Session::setFlash('error', 'Payment not found');
            $this->redirect('/freelance/payments');
            return;
        }

        // Delete payment
        $result = $this->paymentModel->delete($id);

        if ($result) {
            // If payment was for an invoice, update invoice status if needed
            if (!empty($payment['invoice_id'])) {
                $invoice = $this->invoiceModel->getInvoiceDetails($payment['invoice_id'], $userId);
                if ($invoice && $invoice['status'] === 'paid') {
                    // Check if invoice should still be marked as paid
                    $invoicePayments = $this->paymentModel->getUserPayments($userId, ['invoice_id' => $payment['invoice_id']]);
                    $totalPaid = 0;

                    foreach ($invoicePayments as $p) {
                        $totalPaid += $p['amount'];
                    }

                    // If total paid is less than total amount, revert to sent
                    if ($totalPaid < $invoice['total_amount']) {
                        $this->invoiceModel->update($payment['invoice_id'], [
                            'status' => 'sent',
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                    }
                }
            }

            Session::setFlash('success', 'Payment deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete payment');
        }

        $this->redirect('/freelance/payments');
    }
}
