<?php
/**
 * Income Source Controller
 *
 * Handles income source-related functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/IncomeSource.php';
require_once __DIR__ . '/../models/Finance.php';

class IncomeSourceController extends BaseController {
    private $incomeSourceModel;

    public function __construct() {
        $this->incomeSourceModel = new IncomeSource();
    }

    /**
     * Show income sources list
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();

        // Fallback to user ID 1 if not logged in (for testing purposes)
        $userId = $user ? $user['id'] : 1;

        // Debug output
        error_log('User from session: ' . print_r($user, true));
        error_log('Using user ID: ' . $userId);

        // Get filter parameters
        $filters = $this->getQueryData();

        // Set default date range to current month if not provided
        if (empty($filters['start_date'])) {
            $filters['start_date'] = date('Y-m-01'); // First day of current month
        }

        if (empty($filters['end_date'])) {
            $filters['end_date'] = date('Y-m-t'); // Last day of current month
        }

        // Source type filter
        if (!isset($filters['source_type'])) {
            $filters['source_type'] = '';
        }

        // Get income sources with total income
        $incomeSources = $this->incomeSourceModel->getSourcesWithTotalIncome(
            $userId,
            $filters['start_date'],
            $filters['end_date'],
            $filters['source_type']
        );

        // Debug output
        error_log('Income sources query result: ' . print_r($incomeSources, true));
        error_log('User ID: ' . $userId);
        error_log('Start date: ' . $filters['start_date']);
        error_log('End date: ' . $filters['end_date']);

        // Get total income for the period
        $totalIncome = 0;
        foreach ($incomeSources as $source) {
            $totalIncome += (float)($source['total_income'] ?? 0);
        }

        $this->view('finances/income_sources/index', [
            'incomeSources' => $incomeSources,
            'totalIncome' => $totalIncome,
            'filters' => $filters
        ]);
    }

    /**
     * Show income source creation form
     */
    public function create() {
        $this->requireLogin();

        $this->view('finances/income_sources/create');
    }

    /**
     * Process income source creation
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['name']);

        if (!empty($errors)) {
            $this->view('finances/income_sources/create', [
                'errors' => $errors,
                'data' => $data
            ]);
            return;
        }

        // Prepare income source data
        $sourceData = [
            'user_id' => $userId,
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'is_active' => isset($data['is_active']) ? 1 : 0,
            'is_recurring' => isset($data['is_recurring']) ? 1 : 0,
            'recurrence_pattern' => $data['recurrence_pattern'] ?? null,
            'expected_amount' => !empty($data['expected_amount']) ? $data['expected_amount'] : null,
            'category' => $data['category'] ?? null,
            'source_type' => $data['source_type'] ?? 'other',
            'payment_method' => $data['payment_method'] ?? null,
            'location' => $data['location'] ?? null,
            'contact_person' => $data['contact_person'] ?? null,
            'contact_info' => $data['contact_info'] ?? null,
            'notes' => $data['notes'] ?? null,
            'tags' => $data['tags'] ?? null,
            'last_payment_date' => !empty($data['last_payment_date']) ? $data['last_payment_date'] : null,
            'next_expected_date' => !empty($data['next_expected_date']) ? $data['next_expected_date'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create income source
        $sourceId = $this->incomeSourceModel->createIncomeSource($sourceData);

        if ($sourceId) {
            Session::setFlash('success', 'Income source created successfully');
            $this->redirect('/finances/income-sources');
        } else {
            Session::setFlash('error', 'Failed to create income source');

            $this->view('finances/income_sources/create', [
                'data' => $data
            ]);
        }
    }

    /**
     * Show income source details
     */
    public function viewSource($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get income source
        $incomeSource = $this->incomeSourceModel->getIncomeSource($id, $userId);

        // Verify income source exists and belongs to user
        if (!$incomeSource) {
            Session::setFlash('error', 'Income source not found');
            $this->redirect('/finances/income-sources');
        }

        // Get transactions for this income source
        $transactions = $this->incomeSourceModel->getSourceTransactions($id, $userId);

        // Get monthly income for this source
        $monthlyIncome = $this->incomeSourceModel->getMonthlyIncome($id, $userId);

        // Get source summary
        $summary = $this->incomeSourceModel->getSourceSummary($id, $userId);

        $this->view('finances/income_sources/view', [
            'incomeSource' => $incomeSource,
            'transactions' => $transactions,
            'monthlyIncome' => $monthlyIncome,
            'summary' => $summary
        ]);
    }

    /**
     * Show income source edit form
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get income source
        $incomeSource = $this->incomeSourceModel->getIncomeSource($id, $userId);

        // Verify income source exists and belongs to user
        if (!$incomeSource) {
            Session::setFlash('error', 'Income source not found');
            $this->redirect('/finances/income-sources');
        }

        $this->view('finances/income_sources/edit', [
            'incomeSource' => $incomeSource
        ]);
    }

    /**
     * Process income source update
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get income source
        $incomeSource = $this->incomeSourceModel->getIncomeSource($id, $userId);

        // Verify income source exists and belongs to user
        if (!$incomeSource) {
            Session::setFlash('error', 'Income source not found');
            $this->redirect('/finances/income-sources');
        }

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['name']);

        if (!empty($errors)) {
            $this->view('finances/income_sources/edit', [
                'errors' => $errors,
                'incomeSource' => array_merge($incomeSource, $data)
            ]);
            return;
        }

        // Prepare income source data
        $sourceData = [
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'is_active' => isset($data['is_active']) ? 1 : 0,
            'is_recurring' => isset($data['is_recurring']) ? 1 : 0,
            'recurrence_pattern' => $data['recurrence_pattern'] ?? null,
            'expected_amount' => !empty($data['expected_amount']) ? $data['expected_amount'] : null,
            'category' => $data['category'] ?? null,
            'source_type' => $data['source_type'] ?? 'other',
            'payment_method' => $data['payment_method'] ?? null,
            'location' => $data['location'] ?? null,
            'contact_person' => $data['contact_person'] ?? null,
            'contact_info' => $data['contact_info'] ?? null,
            'notes' => $data['notes'] ?? null,
            'tags' => $data['tags'] ?? null,
            'last_payment_date' => !empty($data['last_payment_date']) ? $data['last_payment_date'] : null,
            'next_expected_date' => !empty($data['next_expected_date']) ? $data['next_expected_date'] : null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update income source
        $result = $this->incomeSourceModel->updateIncomeSource($id, $sourceData);

        if ($result) {
            Session::setFlash('success', 'Income source updated successfully');
            $this->redirect('/finances/income-sources/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update income source');

            $this->view('finances/income_sources/edit', [
                'incomeSource' => array_merge($incomeSource, $data)
            ]);
        }
    }

    /**
     * Delete income source
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if this is a POST request for security
        $requestMethod = $_SERVER['REQUEST_METHOD'];
        if ($requestMethod !== 'POST') {
            // If not a POST request, redirect to the income sources page
            Session::setFlash('error', 'Invalid request method for delete operation');
            $this->redirect('/finances/income-sources');
            return;
        }

        // Get income source
        $incomeSource = $this->incomeSourceModel->getIncomeSource($id, $userId);

        // Verify income source exists and belongs to user
        if (!$incomeSource) {
            Session::setFlash('error', 'Income source not found');
            $this->redirect('/finances/income-sources');
            return;
        }

        // Debug log
        error_log("Deleting income source ID {$id} for user {$userId}: " . print_r($incomeSource, true));

        // First, check if there are any transactions linked to this income source
        $transactions = $this->incomeSourceModel->getSourceTransactions($id, $userId);

        // If there are transactions, unlink them first
        if (!empty($transactions)) {
            error_log("Found " . count($transactions) . " transactions linked to income source {$id}");
            foreach ($transactions as $transaction) {
                $this->incomeSourceModel->unlinkTransaction($id, $transaction['id']);
            }
        }

        // Delete income source
        $result = $this->incomeSourceModel->deleteIncomeSource($id, $userId);

        if ($result) {
            Session::setFlash('success', 'Income source deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete income source');
            error_log("Failed to delete income source ID {$id} for user {$userId}");
        }

        $this->redirect('/finances/income-sources');
    }

    /**
     * Link a transaction to an income source
     */
    public function linkTransaction() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->redirect('/finances/income-sources');
            return;
        }

        $data = $this->getPostData();

        if (empty($data['income_source_id']) || empty($data['transaction_id'])) {
            $this->json(['success' => false, 'message' => 'Missing required data']);
            return;
        }

        $result = $this->incomeSourceModel->linkTransaction($data['income_source_id'], $data['transaction_id']);

        if ($result) {
            $this->json(['success' => true]);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to link transaction']);
        }
    }

    /**
     * Unlink a transaction from an income source
     */
    public function unlinkTransaction() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->redirect('/finances/income-sources');
            return;
        }

        $data = $this->getPostData();

        if (empty($data['income_source_id']) || empty($data['transaction_id'])) {
            $this->json(['success' => false, 'message' => 'Missing required data']);
            return;
        }

        $result = $this->incomeSourceModel->unlinkTransaction($data['income_source_id'], $data['transaction_id']);

        if ($result) {
            $this->json(['success' => true]);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to unlink transaction']);
        }
    }

    /**
     * Handle GET requests to the delete endpoint
     * This is a fallback for when JavaScript submits the form with GET instead of POST
     */
    public function handleGetDelete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get income source
        $incomeSource = $this->incomeSourceModel->getIncomeSource($id, $userId);

        // Verify income source exists and belongs to user
        if (!$incomeSource) {
            Session::setFlash('error', 'Income source not found');
            $this->redirect('/finances/income-sources');
            return;
        }

        // Set error message
        Session::setFlash('error', 'Please use the delete button on the form to delete this income source');

        // Redirect to the view page
        $this->redirect('/finances/income-sources/view/' . $id);
    }

    /**
     * Show income forecast
     */
    public function forecast() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get forecast months parameter
        $months = isset($_GET['months']) ? (int)$_GET['months'] : 6;

        // Validate months (between 1 and 24)
        $months = max(1, min(24, $months));

        // Get income forecast
        $forecast = $this->incomeSourceModel->getIncomeForecast($userId, $months);

        // Get all active income sources for reference
        $incomeSources = $this->incomeSourceModel->getUserIncomeSources($userId, false);

        $this->view('finances/income_sources/forecast', [
            'forecast' => $forecast,
            'incomeSources' => $incomeSources,
            'months' => $months
        ]);
    }
}
