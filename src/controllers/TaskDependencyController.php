<?php
/**
 * TaskDependency Controller
 *
 * Handles task dependency-related functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Task.php';
require_once __DIR__ . '/../models/TaskDependency.php';
require_once __DIR__ . '/../models/Project.php';

class TaskDependencyController extends BaseController {
    private $taskModel;
    private $dependencyModel;
    private $projectModel;

    public function __construct() {
        $this->taskModel = new Task();
        $this->dependencyModel = new TaskDependency();
        $this->projectModel = new Project();
    }

    /**
     * Add dependency between tasks
     */
    public function addDependency() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data || empty($data['task_id']) || empty($data['depends_on_task_id']) || empty($data['dependency_type'])) {
            $this->json(['success' => false, 'message' => 'Missing required fields'], 400);
            return;
        }

        $taskId = $data['task_id'];
        $dependsOnTaskId = $data['depends_on_task_id'];
        $dependencyType = $data['dependency_type'];
        $lagTime = isset($data['lag_time']) ? intval($data['lag_time']) : 0;

        // Verify tasks exist and belong to user
        $task = $this->taskModel->find($taskId);
        $dependsOnTask = $this->taskModel->find($dependsOnTaskId);

        if (!$task || !$dependsOnTask || $task['user_id'] != $userId || $dependsOnTask['user_id'] != $userId) {
            $this->json(['success' => false, 'message' => 'Task not found or access denied'], 404);
            return;
        }

        // Verify tasks belong to the same project
        if ($task['project_id'] != $dependsOnTask['project_id'] || !$task['project_id']) {
            $this->json(['success' => false, 'message' => 'Tasks must belong to the same project'], 400);
            return;
        }

        // Check for circular dependencies
        if ($this->dependencyModel->wouldCreateCircularDependency($taskId, $dependsOnTaskId)) {
            $this->json(['success' => false, 'message' => 'This would create a circular dependency'], 400);
            return;
        }

        // Prepare dependency data
        $dependencyData = [
            'task_id' => $taskId,
            'depends_on_task_id' => $dependsOnTaskId,
            'dependency_type' => $dependencyType,
            'lag_time' => $lagTime,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create dependency
        $dependencyId = $this->dependencyModel->create($dependencyData);

        if ($dependencyId) {
            // Update dependent task dates
            $this->dependencyModel->updateDependentTaskDates($dependsOnTaskId);

            $this->json(['success' => true, 'dependency_id' => $dependencyId]);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to create dependency'], 500);
        }
    }

    /**
     * Delete dependency
     */
    public function deleteDependency() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data || empty($data['dependency_id'])) {
            $this->json(['success' => false, 'message' => 'Missing required fields'], 400);
            return;
        }

        $dependencyId = $data['dependency_id'];

        // Get dependency
        $dependency = $this->dependencyModel->find($dependencyId);

        if (!$dependency) {
            $this->json(['success' => false, 'message' => 'Dependency not found'], 404);
            return;
        }

        // Verify tasks belong to user
        $task = $this->taskModel->find($dependency['task_id']);
        $dependsOnTask = $this->taskModel->find($dependency['depends_on_task_id']);

        if (!$task || !$dependsOnTask || $task['user_id'] != $userId || $dependsOnTask['user_id'] != $userId) {
            $this->json(['success' => false, 'message' => 'Access denied'], 403);
            return;
        }

        // Delete dependency
        $result = $this->dependencyModel->delete($dependencyId);

        if ($result) {
            $this->json(['success' => true, 'message' => 'Dependency deleted successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to delete dependency'], 500);
        }
    }

    /**
     * Get task dependencies
     */
    public function getTaskDependencies($taskId) {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Verify task exists and belongs to user
        $task = $this->taskModel->find($taskId);

        if (!$task || $task['user_id'] != $userId) {
            $this->json(['success' => false, 'message' => 'Task not found or access denied'], 404);
            return;
        }

        // Get task with dependencies
        $taskWithDependencies = $this->taskModel->getTaskWithDependencies($taskId);

        $this->json([
            'success' => true,
            'task' => $taskWithDependencies
        ]);
    }

    /**
     * Get project dependencies for Gantt chart
     */
    public function getProjectDependencies($projectId) {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Verify project exists and belongs to user
        $project = $this->projectModel->find($projectId);

        if (!$project || $project['user_id'] != $userId) {
            $this->json(['success' => false, 'message' => 'Project not found or access denied'], 404);
            return;
        }

        // Get project tasks
        $tasks = $this->taskModel->getProjectTasks($projectId);

        // Get project dependencies
        $dependencies = $this->dependencyModel->getProjectDependencies($projectId);

        // Format data for Gantt chart
        $ganttData = [
            'tasks' => [],
            'links' => []
        ];

        foreach ($tasks as $task) {
            $ganttData['tasks'][] = [
                'id' => $task['id'],
                'text' => $task['title'],
                'start_date' => $task['due_date'] ? date('Y-m-d', strtotime($task['due_date'])) : date('Y-m-d'),
                'duration' => $task['estimated_time'] ? ceil($task['estimated_time'] / (60 * 8)) : 1, // Convert minutes to days (8 hours per day)
                'progress' => $task['status'] === 'done' ? 1 : ($task['status'] === 'in_progress' ? 0.5 : 0),
                'parent' => $task['parent_id'] ?: 0
            ];
        }

        foreach ($dependencies as $dependency) {
            $type = 0; // Default: Finish-to-Start

            switch ($dependency['dependency_type']) {
                case 'start_to_start':
                    $type = 1;
                    break;
                case 'finish_to_finish':
                    $type = 2;
                    break;
                case 'start_to_finish':
                    $type = 3;
                    break;
            }

            $ganttData['links'][] = [
                'id' => $dependency['id'],
                'source' => $dependency['depends_on_task_id'],
                'target' => $dependency['task_id'],
                'type' => $type
            ];
        }

        $this->json([
            'success' => true,
            'data' => $ganttData
        ]);
    }
}
