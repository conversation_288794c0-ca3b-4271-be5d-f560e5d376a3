<?php
/**
 * Dashboard Controller
 *
 * Handles dashboard-related functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Task.php';
require_once __DIR__ . '/../models/Category.php';
require_once __DIR__ . '/../models/Idea.php';
require_once __DIR__ . '/../models/Finance.php';
require_once __DIR__ . '/../models/Subscription.php';
require_once __DIR__ . '/../models/Project.php';
require_once __DIR__ . '/../models/IncomeOpportunity.php';
require_once __DIR__ . '/../models/PassiveIncomeStream.php';
require_once __DIR__ . '/../models/AIAgent.php';
require_once __DIR__ . '/../models/AIAgentCategory.php';
require_once __DIR__ . '/../models/AIAgentTask.php';
require_once __DIR__ . '/../models/AIAgentInteraction.php';

class DashboardController extends BaseController {
    private $taskModel;
    private $categoryModel;
    private $ideaModel;
    private $financeModel;
    private $subscriptionModel;
    private $projectModel;
    private $incomeOpportunityModel;
    private $passiveIncomeModel;
    private $aiAgentModel;
    private $aiAgentCategoryModel;
    private $aiAgentTaskModel;
    private $aiAgentInteractionModel;

    public function __construct() {
        $this->taskModel = new Task();
        $this->categoryModel = new Category();
        $this->ideaModel = new Idea();
        $this->financeModel = new Finance();
        $this->subscriptionModel = new Subscription();
        $this->projectModel = new Project();
        $this->incomeOpportunityModel = new IncomeOpportunity();
        $this->passiveIncomeModel = new PassiveIncomeStream();
        $this->aiAgentModel = new AIAgent();
        $this->aiAgentCategoryModel = new AIAgentCategory();
        $this->aiAgentTaskModel = new AIAgentTask();
        $this->aiAgentInteractionModel = new AIAgentInteraction();
    }

    /**
     * Show dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get current focus task if set
        $currentFocusTask = null;

        if (!empty($user['current_focus_task_id'])) {
            $currentFocusTask = $this->taskModel->find($user['current_focus_task_id']);
            // Verify task exists and belongs to user
            if (!$currentFocusTask || $currentFocusTask['user_id'] != $userId) {
                $currentFocusTask = null;
                // Clear invalid focus task
                require_once __DIR__ . '/../models/User.php';
                $userModel = new User();

                // Get database instance
                $db = Database::getInstance();

                // Use direct SQL update to bypass any constraint issues
                $updateSql = "UPDATE users SET current_focus_task_id = NULL, updated_at = ? WHERE id = ?";
                $updateResult = $db->query($updateSql, [date('Y-m-d H:i:s'), $userId]);

                $updatedUser = $userModel->find($userId);
                Session::setUser($updatedUser);
            }
        }

        // Get today's tasks
        $todayTasks = $this->taskModel->getTodayTasks($userId) ?: [];

        // Get upcoming tasks
        $upcomingTasks = $this->taskModel->getUpcomingTasks($userId) ?: [];

        // Get overdue tasks
        $overdueTasks = $this->taskModel->getOverdueTasks($userId) ?: [];

        // Get recent completed tasks
        $completedTasks = $this->taskModel->getCompletedTasks($userId, 5) ?: [];

        // Get categories with task count
        $categories = $this->categoryModel->getCategoriesWithTaskCount($userId) ?: [];

        // Get recent ideas
        $recentIdeas = $this->ideaModel->getRecentIdeas($userId) ?: [];

        // Get financial summary for current month
        $startOfMonth = date('Y-m-01');
        $endOfMonth = date('Y-m-t');
        $financialSummary = $this->financeModel->getSummary($userId, $startOfMonth, $endOfMonth) ?: [];

        // Get recent transactions
        $recentTransactions = $this->financeModel->getRecentTransactions($userId) ?: [];

        // Get upcoming subscriptions
        $upcomingSubscriptions = $this->subscriptionModel->getUpcomingSubscriptions($userId) ?: [];

        // Get total monthly subscription cost
        $monthlyCost = $this->subscriptionModel->getTotalMonthlyCost($userId) ?: 0;

        // Get active projects
        $activeProjects = $this->projectModel->getUserProjects($userId, ['status' => ['planning', 'in_progress']]) ?: [];

        // Get project statistics
        $allProjects = $this->projectModel->getUserProjects($userId) ?: [];
        $inProgressProjects = $this->projectModel->getUserProjects($userId, ['status' => 'in_progress']) ?: [];
        $completedProjects = $this->projectModel->getUserProjects($userId, ['status' => 'completed']) ?: [];

        $projectStats = [
            'total' => count($allProjects),
            'in_progress' => count($inProgressProjects),
            'completed' => count($completedProjects)
        ];

        // Get ADHD data for dashboard
        require_once __DIR__ . '/../controllers/ADHDController.php';
        $adhdController = new ADHDController();
        $adhdData = $adhdController->getDashboardData();

        // Get income opportunities data
        $activeOpportunities = $this->incomeOpportunityModel->getActiveOpportunities($userId, 5) ?: [];
        $opportunitySummary = $this->incomeOpportunityModel->getOpportunitySummary($userId) ?: [];

        // Get passive income data
        $activeStreams = $this->passiveIncomeModel->getActiveStreams($userId, 5) ?: [];
        $streamsSummary = $this->passiveIncomeModel->getStreamsSummary($userId) ?: [];

        // Get AI Agents Army data
        require_once __DIR__ . '/../controllers/AIAgentController.php';
        $aiAgentController = new AIAgentController();
        $aiAgentData = $aiAgentController->getDashboardData();

        // Get AI Assistant data (prompts and captures)
        $aiAssistantData = $this->getAIAssistantData($userId);

        $this->view('dashboard/index', [
            'currentFocusTask' => $currentFocusTask,
            'todayTasks' => $todayTasks,
            'upcomingTasks' => $upcomingTasks,
            'overdueTasks' => $overdueTasks,
            'completedTasks' => $completedTasks,
            'categories' => $categories,
            'recentIdeas' => $recentIdeas,
            'financialSummary' => $financialSummary,
            'recentTransactions' => $recentTransactions,
            'upcomingSubscriptions' => $upcomingSubscriptions,
            'monthlyCost' => $monthlyCost,
            'activeProjects' => $activeProjects,
            'projectStats' => $projectStats,
            'adhdData' => $adhdData,
            'activeOpportunities' => $activeOpportunities,
            'opportunitySummary' => $opportunitySummary,
            'activeStreams' => $activeStreams,
            'streamsSummary' => $streamsSummary,
            'aiAgentData' => $aiAgentData,
            'aiAssistantData' => $aiAssistantData,
            'stylesheets' => [
                '/momentum/css/ai-agents.css',
                '/momentum/css/ai-prompts.css',
                '/momentum/css/quick-capture.css'
            ],
            'scripts' => [
                '/momentum/js/ai-agents-dashboard.js',
                '/momentum/js/ai-assistant-widget.js'
            ]
        ]);
    }

    /**
     * Redirect to main dashboard (for backward compatibility)
     */
    public function old() {
        $this->redirect('/dashboard');
    }

    /**
     * Update dashboard layout
     */
    public function updateLayout() {
        $this->requireLogin();

        if ($this->isAjax()) {
            $data = json_decode(file_get_contents('php://input'), true);

            if (!$data || !isset($data['layout'])) {
                $this->json(['success' => false, 'message' => 'Invalid data'], 400);
                return;
            }

            $user = Session::getUser();
            $userId = $user['id'];

            require_once __DIR__ . '/../models/User.php';
            $userModel = new User();

            $result = $userModel->update($userId, [
                'dashboard_layout' => json_encode($data['layout']),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            if ($result) {
                // Update user in session
                $updatedUser = $userModel->find($userId);
                Session::setUser($updatedUser);

                $this->json(['success' => true]);
            } else {
                $this->json(['success' => false, 'message' => 'Failed to update layout'], 500);
            }
        } else {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
        }
    }

    /**
     * Toggle theme
     */
    public function toggleTheme() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];
        $currentTheme = $user['theme'] ?? 'light';
        $newTheme = $currentTheme === 'light' ? 'dark' : 'light';

        require_once __DIR__ . '/../models/User.php';
        $userModel = new User();

        $result = $userModel->update($userId, [
            'theme' => $newTheme,
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            // Update user in session
            $updatedUser = $userModel->find($userId);
            Session::setUser($updatedUser);

            if ($this->isAjax()) {
                $this->json(['success' => true, 'theme' => $newTheme]);
            } else {
                $this->redirect('/dashboard');
            }
        } else {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Failed to update theme'], 500);
            } else {
                Session::setFlash('error', 'Failed to update theme');
                $this->redirect('/dashboard');
            }
        }
    }

    /**
     * Set current focus task
     */
    public function setFocusTask() {
        $this->requireLogin();

        // Log the request method and content type
        error_log("setFocusTask called. Request method: " . $_SERVER['REQUEST_METHOD']);
        error_log("Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'Not set'));

        if (!$this->isAjax()) {
            error_log("Request is not AJAX");
            $this->json(['success' => false, 'message' => 'Invalid request - not AJAX'], 400);
            return;
        }

        // Get the raw input and log it
        $rawInput = file_get_contents('php://input');
        error_log("Raw input: " . $rawInput);

        $data = json_decode($rawInput, true);
        error_log("Decoded data: " . print_r($data, true));

        if (!$data || empty($data['task_id'])) {
            error_log("Missing task ID in request data");
            $this->json(['success' => false, 'message' => 'Missing task ID'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];
        $taskId = $data['task_id'];

        error_log("User ID: $userId, Task ID: $taskId");

        // Verify task exists and belongs to user
        $task = $this->taskModel->find($taskId);
        if (!$task) {
            error_log("Task not found with ID: $taskId");
            $this->json(['success' => false, 'message' => 'Task not found'], 404);
            return;
        }

        if ($task['user_id'] != $userId) {
            error_log("Task does not belong to user. Task user_id: {$task['user_id']}, Current user: $userId");
            $this->json(['success' => false, 'message' => 'Task does not belong to user'], 403);
            return;
        }

        require_once __DIR__ . '/../models/User.php';
        $userModel = new User();

        // Get database instance
        $db = Database::getInstance();

        // Use direct SQL update to bypass any constraint issues
        $updateSql = "UPDATE users SET current_focus_task_id = ?, updated_at = ? WHERE id = ?";
        $updateResult = $db->query($updateSql, [$taskId, date('Y-m-d H:i:s'), $userId]);

        if ($updateResult) {
            error_log("Direct update successful");
            $result = true;
        } else {
            error_log("Direct update failed");
            $result = false;
        }

        if ($result) {
            // Update user in session
            $updatedUser = $userModel->find($userId);
            Session::setUser($updatedUser);

            // Get category information
            if (!empty($task['category_id'])) {
                $category = $this->categoryModel->find($task['category_id']);
                if ($category) {
                    $task['category_name'] = $category['name'];
                    $task['category_color'] = $category['color'];
                }
            }

            $this->json([
                'success' => true,
                'message' => 'Focus task set successfully',
                'task' => $task
            ]);
        } else {
            error_log("Failed to set focus task. Database update failed.");

            // Try to get more specific error information
            $errorInfo = $db->getConnection()->errorInfo();
            $errorMessage = "Failed to set focus task - database error";

            if ($errorInfo && isset($errorInfo[2])) {
                $errorMessage .= ": " . $errorInfo[2];
            }

            $this->json(['success' => false, 'message' => $errorMessage], 500);
        }
    }

    /**
     * Clear current focus task
     */
    public function clearFocusTask() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        require_once __DIR__ . '/../models/User.php';
        $userModel = new User();

        // Get database instance
        $db = Database::getInstance();

        // Use direct SQL update to bypass any constraint issues
        $updateSql = "UPDATE users SET current_focus_task_id = NULL, updated_at = ? WHERE id = ?";
        $updateResult = $db->query($updateSql, [date('Y-m-d H:i:s'), $userId]);

        if ($updateResult) {
            error_log("Direct update successful (clearFocusTask)");
            $result = true;
        } else {
            error_log("Direct update failed (clearFocusTask)");
            $result = false;
        }

        if ($result) {
            // Update user in session
            $updatedUser = $userModel->find($userId);
            Session::setUser($updatedUser);

            $this->json([
                'success' => true,
                'message' => 'Focus task cleared successfully'
            ]);
        } else {
            // Try to get more specific error information
            $errorInfo = $db->getConnection()->errorInfo();
            $errorMessage = "Failed to clear focus task";

            if ($errorInfo && isset($errorInfo[2])) {
                $errorMessage .= ": " . $errorInfo[2];
            }

            $this->json(['success' => false, 'message' => $errorMessage], 500);
        }
    }

    /**
     * Get AI Assistant data for dashboard widget
     */
    private function getAIAssistantData($userId) {
        $data = [
            'promptStats' => ['total_prompts' => 0],
            'captureStats' => ['total_captures' => 0],
            'weeklyUsage' => 0,
            'favoriteCount' => 0,
            'recentActivity' => [],
            'favoritePrompts' => []
        ];

        try {
            // Load model classes
            require_once __DIR__ . '/../models/AIPrompt.php';
            require_once __DIR__ . '/../models/QuickCapture.php';

            // Get prompt statistics
            $promptModel = new AIPrompt();
            $promptStats = $promptModel->getPromptStats($userId);
            $data['promptStats'] = $promptStats['overview'] ?? ['total_prompts' => 0];
            $data['favoriteCount'] = $promptStats['overview']['favorite_prompts'] ?? 0;

            // Get recent favorite prompts
            $favoritePrompts = $promptModel->getFavoritePrompts($userId, 3);
            $data['favoritePrompts'] = is_array($favoritePrompts) ? $favoritePrompts : [];

            // Get capture statistics
            $captureModel = new QuickCapture();
            $captureStats = $captureModel->getCaptureStats($userId);
            $data['captureStats'] = $captureStats['overview'] ?? ['total_captures' => 0];

            // Calculate weekly usage (simplified)
            $weekStart = date('Y-m-d', strtotime('-7 days'));
            $recentPrompts = $promptModel->getUserPrompts($userId, ['date_from' => $weekStart]);
            $recentCaptures = $captureModel->getUserCaptures($userId, ['date_from' => $weekStart]);

            // Ensure we have arrays before counting
            $recentPrompts = is_array($recentPrompts) ? $recentPrompts : [];
            $recentCaptures = is_array($recentCaptures) ? $recentCaptures : [];
            $data['weeklyUsage'] = count($recentPrompts) + count($recentCaptures);

            // Get recent activity (mix of prompts and captures)
            $recentActivity = [];

            // Add recent prompts
            $recentPrompts = $promptModel->getRecentPrompts($userId, 5);
            $recentPrompts = is_array($recentPrompts) ? $recentPrompts : [];
            foreach ($recentPrompts as $prompt) {
                $recentActivity[] = [
                    'type' => 'prompt',
                    'title' => $prompt['title'],
                    'action' => 'Created prompt',
                    'created_at' => $prompt['created_at'],
                    'url' => '/momentum/ai-prompts/view/' . $prompt['id']
                ];
            }

            // Add recent captures
            $recentCaptures = $captureModel->getRecentCaptures($userId, 5);
            $recentCaptures = is_array($recentCaptures) ? $recentCaptures : [];
            foreach ($recentCaptures as $capture) {
                $recentActivity[] = [
                    'type' => 'capture',
                    'title' => $capture['title'] ?: ucfirst($capture['type']) . ' capture',
                    'action' => 'Created ' . $capture['type'],
                    'created_at' => $capture['created_at'],
                    'url' => '/momentum/quick-capture/view/' . $capture['id']
                ];
            }

            // Sort by date and limit
            usort($recentActivity, function($a, $b) {
                return strtotime($b['created_at']) - strtotime($a['created_at']);
            });
            $data['recentActivity'] = array_slice($recentActivity, 0, 5);

        } catch (Exception $e) {
            // Log error but don't break dashboard
            error_log('Error getting AI Assistant data: ' . $e->getMessage());
        }

        return $data;
    }
}
// Remove the database column check from the old method
// Remove the database column check from the setFocusTask method
// Remove the database column check from the clearFocusTask method

// Remove the database column check from the index method
// Remove the database column check from the old method

// Remove the database column check from setFocusTask
// Remove the database column check from clearFocusTask

// Remove the database column check from the index method
// Remove the database column check from the old method
// Remove the database column check from setFocusTask
// Remove the database column check from clearFocusTask
