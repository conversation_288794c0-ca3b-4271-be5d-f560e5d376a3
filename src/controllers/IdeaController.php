<?php
/**
 * Idea Controller
 *
 * Handles idea-related functionality for the brain dump feature.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Idea.php';
require_once __DIR__ . '/../models/Category.php';

class IdeaController extends BaseController {
    private $ideaModel;
    private $categoryModel;

    public function __construct() {
        $this->ideaModel = new Idea();
        $this->categoryModel = new Category();
    }

    /**
     * Show idea list
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get ideas based on filters
        $ideas = $this->ideaModel->getUserIdeas($userId, $filters);

        $this->view('ideas/index', [
            'ideas' => $ideas,
            'filters' => $filters
        ]);
    }

    /**
     * Show idea creation form
     */
    public function create() {
        $this->requireLogin();

        $this->view('ideas/create');
    }

    /**
     * Process idea creation
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['title']);

        if (!empty($errors)) {
            $this->view('ideas/create', [
                'errors' => $errors,
                'data' => $data
            ]);
            return;
        }

        // Prepare idea data
        $ideaData = [
            'user_id' => $userId,
            'title' => $data['title'],
            'content' => $data['content'] ?? null,
            'tags' => $data['tags'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create idea
        $ideaId = $this->ideaModel->create($ideaData);

        if ($ideaId) {
            Session::setFlash('success', 'Idea saved successfully');

            // Check if user wants to convert to task
            if (isset($data['convert_to_task']) && $data['convert_to_task']) {
                $this->redirect('/ideas/convert/' . $ideaId);
            } else {
                $this->redirect('/ideas');
            }
        } else {
            Session::setFlash('error', 'Failed to save idea');

            $this->view('ideas/create', [
                'data' => $data
            ]);
        }
    }

    /**
     * Show idea details
     */
    public function viewIdea($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get idea
        $idea = $this->ideaModel->find($id);

        // Verify idea exists and belongs to user
        if (!$idea || $idea['user_id'] != $userId) {
            Session::setFlash('error', 'Idea not found');
            $this->redirect('/ideas');
        }

        $this->view('ideas/view', [
            'idea' => $idea
        ]);
    }

    /**
     * Show idea edit form
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get idea
        $idea = $this->ideaModel->find($id);

        // Verify idea exists and belongs to user
        if (!$idea || $idea['user_id'] != $userId) {
            Session::setFlash('error', 'Idea not found');
            $this->redirect('/ideas');
        }

        $this->view('ideas/edit', [
            'idea' => $idea
        ]);
    }

    /**
     * Process idea update
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get idea
        $idea = $this->ideaModel->find($id);

        // Verify idea exists and belongs to user
        if (!$idea || $idea['user_id'] != $userId) {
            Session::setFlash('error', 'Idea not found');
            $this->redirect('/ideas');
        }

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['title']);

        if (!empty($errors)) {
            $this->view('ideas/edit', [
                'errors' => $errors,
                'idea' => array_merge($idea, $data)
            ]);
            return;
        }

        // Prepare idea data
        $ideaData = [
            'title' => $data['title'],
            'content' => $data['content'] ?? null,
            'tags' => $data['tags'] ?? null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update idea
        $result = $this->ideaModel->update($id, $ideaData);

        if ($result) {
            Session::setFlash('success', 'Idea updated successfully');
            $this->redirect('/ideas/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update idea');

            $this->view('ideas/edit', [
                'idea' => array_merge($idea, $data)
            ]);
        }
    }

    /**
     * Delete idea
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get idea
        $idea = $this->ideaModel->find($id);

        // Verify idea exists and belongs to user
        if (!$idea || $idea['user_id'] != $userId) {
            Session::setFlash('error', 'Idea not found');
            $this->redirect('/ideas');
        }

        // Delete idea
        $result = $this->ideaModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Idea deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete idea');
        }

        $this->redirect('/ideas');
    }

    /**
     * Show convert to task form
     */
    public function showConvert($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get idea
        $idea = $this->ideaModel->find($id);

        // Verify idea exists and belongs to user
        if (!$idea || $idea['user_id'] != $userId) {
            Session::setFlash('error', 'Idea not found');
            $this->redirect('/ideas');
        }

        // Get categories for dropdown
        $categories = $this->categoryModel->getUserCategories($userId);

        $this->view('ideas/convert', [
            'idea' => $idea,
            'categories' => $categories
        ]);
    }

    /**
     * Process convert to task
     */
    public function convert($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get idea
        $idea = $this->ideaModel->find($id);

        // Verify idea exists and belongs to user
        if (!$idea || $idea['user_id'] != $userId) {
            Session::setFlash('error', 'Idea not found');
            $this->redirect('/ideas');
        }

        $data = $this->getPostData();

        // Prepare task data
        $taskData = [
            'title' => $data['title'] ?? $idea['title'],
            'description' => $data['description'] ?? $idea['content'],
            'status' => $data['status'] ?? 'todo',
            'priority' => $data['priority'] ?? 'medium',
            'due_date' => !empty($data['due_date']) ? $data['due_date'] : null,
            'category_id' => !empty($data['category_id']) ? $data['category_id'] : null
        ];

        // Convert idea to task
        $taskId = $this->ideaModel->convertToTask($id, $userId, $taskData);

        if ($taskId) {
            Session::setFlash('success', 'Idea converted to task successfully');
            $this->redirect('/tasks/view/' . $taskId);
        } else {
            Session::setFlash('error', 'Failed to convert idea to task');

            // Get categories for dropdown
            $categories = $this->categoryModel->getUserCategories($userId);

            $this->view('ideas/convert', [
                'idea' => $idea,
                'data' => $data,
                'categories' => $categories
            ]);
        }
    }

    /**
     * Quick save idea (AJAX)
     */
    public function quickSave() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data || empty($data['title'])) {
            $this->json(['success' => false, 'message' => 'Title is required'], 400);
            return;
        }

        // Prepare idea data
        $ideaData = [
            'user_id' => $userId,
            'title' => $data['title'],
            'content' => $data['content'] ?? null,
            'tags' => $data['tags'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create idea
        $ideaId = $this->ideaModel->create($ideaData);

        if ($ideaId) {
            $this->json([
                'success' => true,
                'message' => 'Idea saved successfully',
                'idea_id' => $ideaId
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to save idea'], 500);
        }
    }
}
