<?php
/**
 * AI Agent Controller
 *
 * Handles AI agent functionality
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/AIAgent.php';
require_once __DIR__ . '/../models/AIAgentCategory.php';
require_once __DIR__ . '/../models/AIAgentTask.php';
require_once __DIR__ . '/../models/AIAgentInteraction.php';

class AIAgentController extends BaseController {
    private $agentModel;
    private $categoryModel;
    private $taskModel;
    private $interactionModel;

    public function __construct() {
        $this->agentModel = new AIAgent();
        $this->categoryModel = new AIAgentCategory();
        $this->taskModel = new AIAgentTask();
        $this->interactionModel = new AIAgentInteraction();
    }

    /**
     * Show AI Agents dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get categories with agents
        $categories = $this->categoryModel->getCategoriesWithAgents($userId);

        // Get agent statistics
        $agentStats = $this->agentModel->getAgentStats($userId);

        // Get task statistics
        $taskStats = $this->taskModel->getTaskStats($userId);

        // Get interaction statistics
        $interactionStats = $this->interactionModel->getInteractionStats($userId);

        // Get recent interactions
        $recentInteractions = $this->interactionModel->getRecentInteractions($userId, 10);

        // Get active agents
        $activeAgents = $this->agentModel->getActiveAgents($userId, 5);

        // Render the view
        $this->view('ai_agents/dashboard', [
            'categories' => $categories,
            'agentStats' => $agentStats,
            'taskStats' => $taskStats,
            'interactionStats' => $interactionStats,
            'recentInteractions' => $recentInteractions,
            'activeAgents' => $activeAgents,
            'stylesheets' => [
                '/momentum/css/ai-agents.css'
            ],
            'scripts' => [
                '/momentum/js/ai-agents-dashboard.js',
                '/momentum/js/ai-agent-management.js'
            ]
        ]);
    }

    /**
     * Show agent details
     */
    public function viewAgent($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get agent
        $agent = $this->agentModel->getAgent($id, $userId);

        if (!$agent) {
            Session::setFlash('error', 'Agent not found');
            $this->redirect('/ai-agents');
            return;
        }

        // Get agent skills
        $skills = $this->agentModel->getAgentSkills($id);

        // Get agent tasks
        $tasks = $this->taskModel->getAgentTasks($id);

        // Get agent interactions
        $interactions = $this->interactionModel->getAgentInteractions($id, 20);

        // Render the view
        $this->view('ai_agents/view', [
            'agent' => $agent,
            'skills' => $skills,
            'tasks' => $tasks,
            'interactions' => $interactions,
            'stylesheets' => [
                '/momentum/css/ai-agents.css'
            ],
            'scripts' => [
                '/momentum/js/ai-agent-management.js'
            ]
        ]);
    }

    /**
     * Show create agent form
     */
    public function createAgent() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get categories
        $categories = $this->categoryModel->getUserCategories($userId);

        // Get available skills
        $db = Database::getInstance();
        $skills = $db->fetchAll("SELECT * FROM ai_agent_skills ORDER BY name ASC");

        // Render the view
        $this->view('ai_agents/create', [
            'categories' => $categories,
            'skills' => $skills,
            'stylesheets' => [
                '/momentum/css/ai-agents.css'
            ],
            'scripts' => [
                '/momentum/js/ai-agent-management.js'
            ]
        ]);
    }

    /**
     * Store new agent
     */
    public function storeAgent() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Validate input
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $categoryId = intval($_POST['category_id'] ?? 0);
        $capabilities = trim($_POST['capabilities'] ?? '');
        $personalityTraits = trim($_POST['personality_traits'] ?? '');
        $intelligenceLevel = intval($_POST['intelligence_level'] ?? 5);
        $efficiencyRating = floatval($_POST['efficiency_rating'] ?? 5.0);
        $reliabilityScore = floatval($_POST['reliability_score'] ?? 5.0);
        $status = $_POST['status'] ?? 'active';

        if (empty($name)) {
            Session::setFlash('error', 'Agent name is required');
            $this->redirect('/ai-agents/create');
            return;
        }

        // Create agent
        $agentId = $this->agentModel->createAgent([
            'user_id' => $userId,
            'category_id' => $categoryId ?: null,
            'name' => $name,
            'description' => $description,
            'capabilities' => $capabilities,
            'personality_traits' => $personalityTraits,
            'intelligence_level' => $intelligenceLevel,
            'efficiency_rating' => $efficiencyRating,
            'reliability_score' => $reliabilityScore,
            'status' => $status,
            'last_active' => date('Y-m-d H:i:s')
        ]);

        if (!$agentId) {
            Session::setFlash('error', 'Failed to create agent');
            $this->redirect('/ai-agents/create');
            return;
        }

        // Add skills if provided
        if (isset($_POST['skills']) && is_array($_POST['skills'])) {
            foreach ($_POST['skills'] as $skillId => $proficiency) {
                $this->agentModel->addSkill($agentId, $skillId, $proficiency);
            }
        }

        // Log interaction
        $this->interactionModel->createInteraction([
            'agent_id' => $agentId,
            'user_id' => $userId,
            'interaction_type' => 'system',
            'content' => 'Agent created',
            'success' => true
        ]);

        Session::setFlash('success', 'Agent created successfully');
        $this->redirect('/ai-agents/view/' . $agentId);
    }

    /**
     * Show edit agent form
     */
    public function editAgent($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get agent
        $agent = $this->agentModel->getAgent($id, $userId);

        if (!$agent) {
            Session::setFlash('error', 'Agent not found');
            $this->redirect('/ai-agents');
            return;
        }

        // Get categories
        $categories = $this->categoryModel->getUserCategories($userId);

        // Get agent skills
        $agentSkills = $this->agentModel->getAgentSkills($id);

        // Get all skills
        $db = Database::getInstance();
        $allSkills = $db->fetchAll("SELECT * FROM ai_agent_skills ORDER BY name ASC");

        // Organize agent skills by ID for easy lookup
        $agentSkillsById = [];
        foreach ($agentSkills as $skill) {
            $agentSkillsById[$skill['id']] = $skill;
        }

        // Render the view
        $this->view('ai_agents/edit', [
            'agent' => $agent,
            'categories' => $categories,
            'agentSkills' => $agentSkillsById,
            'allSkills' => $allSkills,
            'stylesheets' => [
                '/momentum/css/ai-agents.css'
            ],
            'scripts' => [
                '/momentum/js/ai-agent-management.js'
            ]
        ]);
    }

    /**
     * Update agent
     */
    public function updateAgent($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get agent
        $agent = $this->agentModel->getAgent($id, $userId);

        if (!$agent) {
            Session::setFlash('error', 'Agent not found');
            $this->redirect('/ai-agents');
            return;
        }

        // Validate input
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $categoryId = intval($_POST['category_id'] ?? 0);
        $capabilities = trim($_POST['capabilities'] ?? '');
        $personalityTraits = trim($_POST['personality_traits'] ?? '');
        $intelligenceLevel = intval($_POST['intelligence_level'] ?? 5);
        $efficiencyRating = floatval($_POST['efficiency_rating'] ?? 5.0);
        $reliabilityScore = floatval($_POST['reliability_score'] ?? 5.0);
        $status = $_POST['status'] ?? 'active';

        if (empty($name)) {
            Session::setFlash('error', 'Agent name is required');
            $this->redirect('/ai-agents/edit/' . $id);
            return;
        }

        // Update agent
        $result = $this->agentModel->updateAgent($id, [
            'category_id' => $categoryId ?: null,
            'name' => $name,
            'description' => $description,
            'capabilities' => $capabilities,
            'personality_traits' => $personalityTraits,
            'intelligence_level' => $intelligenceLevel,
            'efficiency_rating' => $efficiencyRating,
            'reliability_score' => $reliabilityScore,
            'status' => $status
        ]);

        if (!$result) {
            Session::setFlash('error', 'Failed to update agent');
            $this->redirect('/ai-agents/edit/' . $id);
            return;
        }

        // Update skills
        // First, get current skills
        $currentSkills = $this->agentModel->getAgentSkills($id);
        $currentSkillIds = array_column($currentSkills, 'id');

        // Get submitted skills
        $submittedSkills = isset($_POST['skills']) && is_array($_POST['skills']) ? $_POST['skills'] : [];
        $submittedSkillIds = array_keys($submittedSkills);

        // Skills to remove
        $skillsToRemove = array_diff($currentSkillIds, $submittedSkillIds);
        foreach ($skillsToRemove as $skillId) {
            $this->agentModel->removeSkill($id, $skillId);
        }

        // Skills to add or update
        foreach ($submittedSkills as $skillId => $proficiency) {
            $this->agentModel->addSkill($id, $skillId, $proficiency);
        }

        // Log interaction
        $this->interactionModel->createInteraction([
            'agent_id' => $id,
            'user_id' => $userId,
            'interaction_type' => 'system',
            'content' => 'Agent updated',
            'success' => true
        ]);

        Session::setFlash('success', 'Agent updated successfully');
        $this->redirect('/ai-agents/view/' . $id);
    }

    /**
     * Delete agent
     */
    public function deleteAgent($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get agent
        $agent = $this->agentModel->getAgent($id, $userId);

        if (!$agent) {
            Session::setFlash('error', 'Agent not found');
            $this->redirect('/ai-agents');
            return;
        }

        // Delete agent
        $result = $this->agentModel->deleteAgent($id, $userId);

        if (!$result) {
            Session::setFlash('error', 'Failed to delete agent');
            $this->redirect('/ai-agents/view/' . $id);
            return;
        }

        Session::setFlash('success', 'Agent deleted successfully');
        $this->redirect('/ai-agents');
    }

    /**
     * Get dashboard data for the main dashboard
     */
    public function getDashboardData() {
        $user = Session::getUser();
        $userId = $user['id'];

        // Get agent statistics
        $agentStats = $this->agentModel->getAgentStats($userId);

        // Add debug logging
        error_log("AI Agent Stats: " . print_r($agentStats, true));

        // If agentStats is null or empty, manually count agents
        if (empty($agentStats) || $agentStats['total_agents'] == 0) {
            // Get database instance
            $db = Database::getInstance();

            // Count agents directly
            $countSql = "SELECT
                COUNT(*) as total_agents,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_agents
                FROM ai_agents
                WHERE user_id = ?";

            $manualStats = $db->fetchOne($countSql, [$userId]);
            error_log("Manual AI Agent Stats: " . print_r($manualStats, true));

            if ($manualStats && $manualStats['total_agents'] > 0) {
                $agentStats = $manualStats;
            }
        }

        // Get active agents
        $activeAgents = $this->agentModel->getActiveAgents($userId, 5) ?: [];
        error_log("Active Agents: " . count($activeAgents));

        // Get recent interactions
        $recentInteractions = $this->interactionModel->getRecentInteractions($userId, 5) ?: [];

        return [
            'agentStats' => $agentStats,
            'activeAgents' => $activeAgents,
            'recentInteractions' => $recentInteractions
        ];
    }

    /**
     * Show AI Agent Categories
     */
    public function categories() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get categories with agents
        $categories = $this->categoryModel->getCategoriesWithAgents($userId);

        // Get category statistics
        $categoryStats = [
            'total' => count($categories),
            'empty' => 0,
            'most_agents' => ['name' => '', 'count' => 0]
        ];

        // Calculate additional statistics
        foreach ($categories as $category) {
            $agentCount = count($category['agents']);

            if ($agentCount === 0) {
                $categoryStats['empty']++;
            }

            if ($agentCount > $categoryStats['most_agents']['count']) {
                $categoryStats['most_agents'] = [
                    'name' => $category['name'],
                    'count' => $agentCount
                ];
            }
        }

        // Render the view
        $this->view('ai_agents/categories', [
            'categories' => $categories,
            'categoryStats' => $categoryStats,
            'stylesheets' => [
                '/momentum/css/ai-agents.css'
            ],
            'scripts' => [
                '/momentum/js/ai-agent-management.js'
            ]
        ]);
    }

    /**
     * Show create category form
     */
    public function createCategory() {
        $this->requireLogin();

        // Get available icons
        $icons = $this->getAvailableIcons();

        // Get available colors
        $colors = $this->getAvailableColors();

        // Render the view
        $this->view('ai_agents/create_category', [
            'icons' => $icons,
            'colors' => $colors,
            'stylesheets' => [
                '/momentum/css/ai-agents.css'
            ],
            'scripts' => [
                '/momentum/js/ai-agent-management.js'
            ]
        ]);
    }

    /**
     * Store new category
     */
    public function storeCategory() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Validate input
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $color = $_POST['color'] ?? '#6366F1';
        $icon = $_POST['icon'] ?? 'fa-robot';
        $displayOrder = intval($_POST['display_order'] ?? 0);

        if (empty($name)) {
            Session::setFlash('error', 'Category name is required');
            $this->redirect('/ai-agents/categories/create');
            return;
        }

        // Create category
        $categoryId = $this->categoryModel->createCategory([
            'user_id' => $userId,
            'name' => $name,
            'description' => $description,
            'color' => $color,
            'icon' => $icon,
            'display_order' => $displayOrder,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        if (!$categoryId) {
            Session::setFlash('error', 'Failed to create category');
            $this->redirect('/ai-agents/categories/create');
            return;
        }

        Session::setFlash('success', 'Category created successfully');
        $this->redirect('/ai-agents/categories');
    }

    /**
     * Show edit category form
     */
    public function editCategory($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get category
        $category = $this->categoryModel->getCategory($id, $userId);

        if (!$category) {
            Session::setFlash('error', 'Category not found');
            $this->redirect('/ai-agents/categories');
            return;
        }

        // Get available icons
        $icons = $this->getAvailableIcons();

        // Get available colors
        $colors = $this->getAvailableColors();

        // Render the view
        $this->view('ai_agents/edit_category', [
            'category' => $category,
            'icons' => $icons,
            'colors' => $colors,
            'stylesheets' => [
                '/momentum/css/ai-agents.css'
            ],
            'scripts' => [
                '/momentum/js/ai-agent-management.js'
            ]
        ]);
    }

    /**
     * Update category
     */
    public function updateCategory($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get category
        $category = $this->categoryModel->getCategory($id, $userId);

        if (!$category) {
            Session::setFlash('error', 'Category not found');
            $this->redirect('/ai-agents/categories');
            return;
        }

        // Validate input
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $color = $_POST['color'] ?? '#6366F1';
        $icon = $_POST['icon'] ?? 'fa-robot';
        $displayOrder = intval($_POST['display_order'] ?? 0);

        if (empty($name)) {
            Session::setFlash('error', 'Category name is required');
            $this->redirect('/ai-agents/categories/edit/' . $id);
            return;
        }

        // Update category
        $result = $this->categoryModel->updateCategory($id, [
            'name' => $name,
            'description' => $description,
            'color' => $color,
            'icon' => $icon,
            'display_order' => $displayOrder,
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        if (!$result) {
            Session::setFlash('error', 'Failed to update category');
            $this->redirect('/ai-agents/categories/edit/' . $id);
            return;
        }

        Session::setFlash('success', 'Category updated successfully');
        $this->redirect('/ai-agents/categories');
    }

    /**
     * Delete category
     */
    public function deleteCategory($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get category
        $category = $this->categoryModel->getCategory($id, $userId);

        if (!$category) {
            Session::setFlash('error', 'Category not found');
            $this->redirect('/ai-agents/categories');
            return;
        }

        // Check if category has agents
        $agents = $this->agentModel->getCategoryAgents($id);

        if (count($agents) > 0) {
            Session::setFlash('error', 'Cannot delete category with agents. Please reassign or delete the agents first.');
            $this->redirect('/ai-agents/categories');
            return;
        }

        // Delete category
        $result = $this->categoryModel->deleteCategory($id, $userId);

        if (!$result) {
            Session::setFlash('error', 'Failed to delete category');
            $this->redirect('/ai-agents/categories');
            return;
        }

        Session::setFlash('success', 'Category deleted successfully');
        $this->redirect('/ai-agents/categories');
    }

    /**
     * Show create task form
     */
    public function createTask() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get agent ID from query string
        $agentId = isset($_GET['agent_id']) ? intval($_GET['agent_id']) : 0;

        if (!$agentId) {
            Session::setFlash('error', 'Agent ID is required');
            $this->redirect('/ai-agents');
            return;
        }

        // Get agent
        $agent = $this->agentModel->getAgent($agentId, $userId);

        if (!$agent) {
            Session::setFlash('error', 'Agent not found');
            $this->redirect('/ai-agents');
            return;
        }

        // Render the view
        $this->view('ai_agents/tasks/create', [
            'agent' => $agent,
            'stylesheets' => [
                '/momentum/css/ai-agents.css'
            ],
            'scripts' => [
                '/momentum/js/ai-agent-management.js'
            ]
        ]);
    }

    /**
     * Store new task
     */
    public function storeTask() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Validate input
        $agentId = isset($_POST['agent_id']) ? intval($_POST['agent_id']) : 0;
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $priority = $_POST['priority'] ?? 'medium';
        $status = $_POST['status'] ?? 'pending';
        $dueDate = $_POST['due_date'] ?? date('Y-m-d', strtotime('+1 day'));
        $dueTime = $_POST['due_time'] ?? '';

        if (!$agentId) {
            Session::setFlash('error', 'Agent ID is required');
            $this->redirect('/ai-agents');
            return;
        }

        // Get agent
        $agent = $this->agentModel->getAgent($agentId, $userId);

        if (!$agent) {
            Session::setFlash('error', 'Agent not found');
            $this->redirect('/ai-agents');
            return;
        }

        if (empty($title)) {
            Session::setFlash('error', 'Task title is required');
            $this->redirect('/ai-agents/tasks/create?agent_id=' . $agentId);
            return;
        }

        // Format due date with time if provided
        if (!empty($dueTime)) {
            $dueDate = date('Y-m-d H:i:s', strtotime($dueDate . ' ' . $dueTime));
        } else {
            $dueDate = date('Y-m-d H:i:s', strtotime($dueDate . ' 23:59:59'));
        }

        // Create task
        $taskId = $this->taskModel->createTask([
            'agent_id' => $agentId,
            'user_id' => $userId,
            'title' => $title,
            'description' => $description,
            'priority' => $priority,
            'status' => $status,
            'due_date' => $dueDate,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        if (!$taskId) {
            Session::setFlash('error', 'Failed to create task');
            $this->redirect('/ai-agents/tasks/create?agent_id=' . $agentId);
            return;
        }

        // Log interaction
        $this->interactionModel->createInteraction([
            'agent_id' => $agentId,
            'user_id' => $userId,
            'interaction_type' => 'system',
            'content' => 'Task assigned: ' . $title,
            'success' => true
        ]);

        Session::setFlash('success', 'Task created successfully');
        $this->redirect('/ai-agents/tasks/view/' . $taskId);
    }

    /**
     * View task
     */
    public function viewTask($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get task
        $task = $this->taskModel->getTask($id, $userId);

        if (!$task) {
            Session::setFlash('error', 'Task not found');
            $this->redirect('/ai-agents');
            return;
        }

        // Get agent
        $agent = $this->agentModel->getAgent($task['agent_id'], $userId);

        if (!$agent) {
            Session::setFlash('error', 'Agent not found');
            $this->redirect('/ai-agents');
            return;
        }

        // Get agent skills
        $agentSkills = $this->agentModel->getAgentSkills($task['agent_id']);

        // Get task interactions
        $interactions = $this->interactionModel->getTaskInteractions($id);

        // Render the view
        $this->view('ai_agents/tasks/view', [
            'task' => $task,
            'agent' => $agent,
            'agentSkills' => $agentSkills,
            'interactions' => $interactions,
            'stylesheets' => [
                '/momentum/css/ai-agents.css'
            ],
            'scripts' => [
                '/momentum/js/ai-agent-management.js'
            ]
        ]);
    }

    /**
     * Update task status
     */
    public function updateTaskStatus($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get task
        $task = $this->taskModel->getTask($id, $userId);

        if (!$task) {
            Session::setFlash('error', 'Task not found');
            $this->redirect('/ai-agents');
            return;
        }

        // Validate input
        $status = $_POST['status'] ?? '';

        if (empty($status)) {
            Session::setFlash('error', 'Status is required');
            $this->redirect('/ai-agents/tasks/view/' . $id);
            return;
        }

        // Update task
        $result = $this->taskModel->updateTask($id, [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        if (!$result) {
            Session::setFlash('error', 'Failed to update task status');
            $this->redirect('/ai-agents/tasks/view/' . $id);
            return;
        }

        // Log interaction
        $this->interactionModel->createInteraction([
            'agent_id' => $task['agent_id'],
            'user_id' => $userId,
            'interaction_type' => 'system',
            'content' => 'Task status updated to: ' . ucfirst(str_replace('_', ' ', $status)),
            'success' => true
        ]);

        Session::setFlash('success', 'Task status updated successfully');
        $this->redirect('/ai-agents/tasks/view/' . $id);
    }

    /**
     * Start task
     */
    public function startTask($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get task
        $task = $this->taskModel->getTask($id, $userId);

        if (!$task) {
            Session::setFlash('error', 'Task not found');
            $this->redirect('/ai-agents');
            return;
        }

        // Update task
        $result = $this->taskModel->updateTask($id, [
            'status' => 'in_progress',
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        if (!$result) {
            Session::setFlash('error', 'Failed to start task');
            $this->redirect('/ai-agents/tasks/view/' . $id);
            return;
        }

        // Log interaction
        $this->interactionModel->createInteraction([
            'agent_id' => $task['agent_id'],
            'user_id' => $userId,
            'interaction_type' => 'system',
            'content' => 'Task started',
            'success' => true
        ]);

        Session::setFlash('success', 'Task started successfully');
        $this->redirect('/ai-agents/tasks/view/' . $id);
    }

    /**
     * Complete task
     */
    public function completeTask($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get task
        $task = $this->taskModel->getTask($id, $userId);

        if (!$task) {
            Session::setFlash('error', 'Task not found');
            $this->redirect('/ai-agents');
            return;
        }

        // Update task
        $result = $this->taskModel->updateTask($id, [
            'status' => 'completed',
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        if (!$result) {
            Session::setFlash('error', 'Failed to complete task');
            $this->redirect('/ai-agents/tasks/view/' . $id);
            return;
        }

        // Log interaction
        $this->interactionModel->createInteraction([
            'agent_id' => $task['agent_id'],
            'user_id' => $userId,
            'interaction_type' => 'system',
            'content' => 'Task completed',
            'success' => true
        ]);

        Session::setFlash('success', 'Task completed successfully');
        $this->redirect('/ai-agents/tasks/view/' . $id);
    }

    /**
     * Edit task form
     */
    public function editTask($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get task
        $task = $this->taskModel->getTask($id, $userId);

        if (!$task) {
            Session::setFlash('error', 'Task not found');
            $this->redirect('/ai-agents');
            return;
        }

        // Get agent
        $agent = $this->agentModel->getAgent($task['agent_id'], $userId);

        if (!$agent) {
            Session::setFlash('error', 'Agent not found');
            $this->redirect('/ai-agents');
            return;
        }

        // Render the view
        $this->view('ai_agents/tasks/edit', [
            'task' => $task,
            'agent' => $agent,
            'stylesheets' => [
                '/momentum/css/ai-agents.css'
            ],
            'scripts' => [
                '/momentum/js/ai-agent-management.js'
            ]
        ]);
    }

    /**
     * Update task
     */
    public function updateTask($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get task
        $task = $this->taskModel->getTask($id, $userId);

        if (!$task) {
            Session::setFlash('error', 'Task not found');
            $this->redirect('/ai-agents');
            return;
        }

        // Validate input
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $priority = $_POST['priority'] ?? 'medium';
        $status = $_POST['status'] ?? 'pending';
        $dueDate = $_POST['due_date'] ?? date('Y-m-d', strtotime('+1 day'));
        $dueTime = $_POST['due_time'] ?? '';
        $completionNotes = trim($_POST['completion_notes'] ?? '');
        $successRating = intval($_POST['success_rating'] ?? 0);

        if (empty($title)) {
            Session::setFlash('error', 'Task title is required');
            $this->redirect('/ai-agents/tasks/edit/' . $id);
            return;
        }

        // Format due date with time if provided
        if (!empty($dueTime)) {
            $dueDate = date('Y-m-d H:i:s', strtotime($dueDate . ' ' . $dueTime));
        } else {
            $dueDate = date('Y-m-d H:i:s', strtotime($dueDate . ' 23:59:59'));
        }

        // Prepare update data
        $updateData = [
            'title' => $title,
            'description' => $description,
            'priority' => $priority,
            'status' => $status,
            'due_date' => $dueDate,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add completion details if task is completed or failed
        if ($status === 'completed' || $status === 'failed') {
            $updateData['completion_date'] = date('Y-m-d H:i:s');
            $updateData['completion_notes'] = $completionNotes;

            if ($successRating > 0) {
                $updateData['success_rating'] = $successRating;
            }
        }

        // Update task
        $result = $this->taskModel->updateTask($id, $updateData);

        if (!$result) {
            Session::setFlash('error', 'Failed to update task');
            $this->redirect('/ai-agents/tasks/edit/' . $id);
            return;
        }

        // Log interaction
        $this->interactionModel->createInteraction([
            'agent_id' => $task['agent_id'],
            'user_id' => $userId,
            'interaction_type' => 'system',
            'content' => 'Task updated: ' . $title,
            'success' => true
        ]);

        Session::setFlash('success', 'Task updated successfully');
        $this->redirect('/ai-agents/tasks/view/' . $id);
    }

    /**
     * Get available icons for categories
     */
    private function getAvailableIcons() {
        return [
            'fa-robot' => 'Robot',
            'fa-brain' => 'Brain',
            'fa-cogs' => 'Cogs',
            'fa-microchip' => 'Microchip',
            'fa-server' => 'Server',
            'fa-code' => 'Code',
            'fa-database' => 'Database',
            'fa-network-wired' => 'Network',
            'fa-laptop-code' => 'Laptop Code',
            'fa-desktop' => 'Desktop',
            'fa-mobile-alt' => 'Mobile',
            'fa-tablet-alt' => 'Tablet',
            'fa-search' => 'Search',
            'fa-chart-bar' => 'Chart Bar',
            'fa-chart-line' => 'Chart Line',
            'fa-chart-pie' => 'Chart Pie',
            'fa-file-alt' => 'Document',
            'fa-file-code' => 'Code File',
            'fa-file-excel' => 'Excel File',
            'fa-file-pdf' => 'PDF File',
            'fa-file-word' => 'Word File',
            'fa-file-image' => 'Image File',
            'fa-file-video' => 'Video File',
            'fa-file-audio' => 'Audio File',
            'fa-folder' => 'Folder',
            'fa-folder-open' => 'Folder Open',
            'fa-tasks' => 'Tasks',
            'fa-clipboard-list' => 'Clipboard List',
            'fa-calendar' => 'Calendar',
            'fa-calendar-alt' => 'Calendar Alt',
            'fa-clock' => 'Clock',
            'fa-stopwatch' => 'Stopwatch',
            'fa-bell' => 'Bell',
            'fa-envelope' => 'Envelope',
            'fa-comment' => 'Comment',
            'fa-comments' => 'Comments',
            'fa-user' => 'User',
            'fa-users' => 'Users',
            'fa-user-tie' => 'Business User',
            'fa-user-graduate' => 'Graduate',
            'fa-user-md' => 'Doctor',
            'fa-user-cog' => 'Admin',
            'fa-briefcase' => 'Briefcase',
            'fa-building' => 'Building',
            'fa-home' => 'Home',
            'fa-store' => 'Store',
            'fa-shopping-cart' => 'Shopping Cart',
            'fa-money-bill-alt' => 'Money',
            'fa-credit-card' => 'Credit Card',
            'fa-dollar-sign' => 'Dollar',
            'fa-euro-sign' => 'Euro',
            'fa-pound-sign' => 'Pound',
            'fa-yen-sign' => 'Yen',
            'fa-bitcoin' => 'Bitcoin',
            'fa-ethereum' => 'Ethereum',
            'fa-book' => 'Book',
            'fa-graduation-cap' => 'Graduation Cap',
            'fa-university' => 'University',
            'fa-microscope' => 'Microscope',
            'fa-flask' => 'Flask',
            'fa-vial' => 'Vial',
            'fa-dna' => 'DNA',
            'fa-heartbeat' => 'Heartbeat',
            'fa-medkit' => 'Medkit',
            'fa-stethoscope' => 'Stethoscope',
            'fa-hospital' => 'Hospital',
            'fa-ambulance' => 'Ambulance',
            'fa-pills' => 'Pills',
            'fa-syringe' => 'Syringe',
            'fa-thermometer' => 'Thermometer',
            'fa-procedures' => 'Medical Procedures',
            'fa-diagnoses' => 'Diagnoses',
            'fa-notes-medical' => 'Medical Notes',
            'fa-wheelchair' => 'Wheelchair',
            'fa-tooth' => 'Tooth',
            'fa-eye' => 'Eye',
            'fa-ear' => 'Ear',
            'fa-hand' => 'Hand',
            'fa-bone' => 'Bone',
            'fa-lungs' => 'Lungs',
            'fa-brain' => 'Brain',
            'fa-heart' => 'Heart',
            'fa-apple-alt' => 'Apple',
            'fa-carrot' => 'Carrot',
            'fa-utensils' => 'Utensils',
            'fa-hamburger' => 'Hamburger',
            'fa-pizza-slice' => 'Pizza',
            'fa-ice-cream' => 'Ice Cream',
            'fa-cookie' => 'Cookie',
            'fa-candy-cane' => 'Candy',
            'fa-coffee' => 'Coffee',
            'fa-glass-martini' => 'Martini',
            'fa-wine-glass' => 'Wine',
            'fa-beer' => 'Beer',
            'fa-cocktail' => 'Cocktail',
            'fa-glass-whiskey' => 'Whiskey',
            'fa-mug-hot' => 'Hot Drink',
            'fa-running' => 'Running',
            'fa-walking' => 'Walking',
            'fa-swimming-pool' => 'Swimming',
            'fa-biking' => 'Biking',
            'fa-hiking' => 'Hiking',
            'fa-skiing' => 'Skiing',
            'fa-snowboarding' => 'Snowboarding',
            'fa-dumbbell' => 'Dumbbell',
            'fa-weight' => 'Weight',
            'fa-basketball-ball' => 'Basketball',
            'fa-football-ball' => 'Football',
            'fa-baseball-ball' => 'Baseball',
            'fa-volleyball-ball' => 'Volleyball',
            'fa-hockey-puck' => 'Hockey',
            'fa-table-tennis' => 'Table Tennis',
            'fa-bowling-ball' => 'Bowling',
            'fa-golf-ball' => 'Golf',
            'fa-chess' => 'Chess',
            'fa-dice' => 'Dice',
            'fa-gamepad' => 'Gamepad',
            'fa-headset' => 'Headset',
            'fa-vr-cardboard' => 'VR',
            'fa-guitar' => 'Guitar',
            'fa-drum' => 'Drum',
            'fa-music' => 'Music',
            'fa-headphones' => 'Headphones',
            'fa-microphone' => 'Microphone',
            'fa-podcast' => 'Podcast',
            'fa-film' => 'Film',
            'fa-video' => 'Video',
            'fa-camera' => 'Camera',
            'fa-photo-video' => 'Photo Video',
            'fa-palette' => 'Palette',
            'fa-paint-brush' => 'Paint Brush',
            'fa-pencil-alt' => 'Pencil',
            'fa-pen' => 'Pen',
            'fa-marker' => 'Marker',
            'fa-highlighter' => 'Highlighter',
            'fa-eraser' => 'Eraser',
            'fa-crop' => 'Crop',
            'fa-fill' => 'Fill',
            'fa-fill-drip' => 'Fill Drip',
            'fa-tint' => 'Tint',
            'fa-swatchbook' => 'Swatchbook',
            'fa-layer-group' => 'Layer Group',
            'fa-object-group' => 'Object Group',
            'fa-object-ungroup' => 'Object Ungroup',
            'fa-shapes' => 'Shapes',
            'fa-draw-polygon' => 'Draw Polygon',
            'fa-vector-square' => 'Vector Square',
            'fa-bezier-curve' => 'Bezier Curve',
            'fa-drafting-compass' => 'Drafting Compass',
            'fa-ruler-combined' => 'Ruler Combined',
            'fa-ruler-horizontal' => 'Ruler Horizontal',
            'fa-ruler-vertical' => 'Ruler Vertical',
            'fa-ruler' => 'Ruler',
            'fa-compass' => 'Compass',
            'fa-map' => 'Map',
            'fa-map-marked-alt' => 'Map Marked',
            'fa-location-arrow' => 'Location Arrow',
            'fa-globe' => 'Globe',
            'fa-globe-americas' => 'Globe Americas',
            'fa-globe-europe' => 'Globe Europe',
            'fa-globe-asia' => 'Globe Asia',
            'fa-globe-africa' => 'Globe Africa',
            'fa-mountain' => 'Mountain',
            'fa-tree' => 'Tree',
            'fa-leaf' => 'Leaf',
            'fa-seedling' => 'Seedling',
            'fa-spa' => 'Spa',
            'fa-umbrella-beach' => 'Beach',
            'fa-sun' => 'Sun',
            'fa-moon' => 'Moon',
            'fa-cloud' => 'Cloud',
            'fa-cloud-rain' => 'Rain',
            'fa-cloud-showers-heavy' => 'Heavy Rain',
            'fa-cloud-sun' => 'Cloud Sun',
            'fa-cloud-sun-rain' => 'Cloud Sun Rain',
            'fa-cloud-moon' => 'Cloud Moon',
            'fa-cloud-moon-rain' => 'Cloud Moon Rain',
            'fa-poo-storm' => 'Storm',
            'fa-rainbow' => 'Rainbow',
            'fa-snowflake' => 'Snowflake',
            'fa-temperature-high' => 'High Temperature',
            'fa-temperature-low' => 'Low Temperature',
            'fa-wind' => 'Wind',
            'fa-smog' => 'Smog',
            'fa-bolt' => 'Lightning',
            'fa-fire' => 'Fire',
            'fa-water' => 'Water',
            'fa-fan' => 'Fan',
            'fa-lightbulb' => 'Lightbulb',
            'fa-plug' => 'Plug',
            'fa-battery-full' => 'Battery Full',
            'fa-battery-half' => 'Battery Half',
            'fa-battery-quarter' => 'Battery Quarter',
            'fa-battery-empty' => 'Battery Empty',
            'fa-car' => 'Car',
            'fa-truck' => 'Truck',
            'fa-motorcycle' => 'Motorcycle',
            'fa-bicycle' => 'Bicycle',
            'fa-bus' => 'Bus',
            'fa-train' => 'Train',
            'fa-subway' => 'Subway',
            'fa-taxi' => 'Taxi',
            'fa-ship' => 'Ship',
            'fa-plane' => 'Plane',
            'fa-helicopter' => 'Helicopter',
            'fa-space-shuttle' => 'Space Shuttle',
            'fa-rocket' => 'Rocket',
            'fa-satellite' => 'Satellite',
            'fa-meteor' => 'Meteor',
            'fa-user-astronaut' => 'Astronaut',
            'fa-user-ninja' => 'Ninja',
            'fa-user-secret' => 'Secret Agent',
            'fa-user-shield' => 'Security',
            'fa-shield-alt' => 'Shield',
            'fa-lock' => 'Lock',
            'fa-unlock' => 'Unlock',
            'fa-key' => 'Key',
            'fa-fingerprint' => 'Fingerprint',
            'fa-id-card' => 'ID Card',
            'fa-passport' => 'Passport',
            'fa-door-open' => 'Door Open',
            'fa-door-closed' => 'Door Closed',
            'fa-wifi' => 'WiFi',
            'fa-signal' => 'Signal',
            'fa-broadcast-tower' => 'Broadcast Tower',
            'fa-satellite-dish' => 'Satellite Dish',
            'fa-rss' => 'RSS',
            'fa-podcast' => 'Podcast',
            'fa-microphone-alt' => 'Microphone Alt',
            'fa-bullhorn' => 'Bullhorn',
            'fa-megaphone' => 'Megaphone',
            'fa-phone' => 'Phone',
            'fa-phone-alt' => 'Phone Alt',
            'fa-mobile' => 'Mobile',
            'fa-fax' => 'Fax',
            'fa-envelope-open' => 'Envelope Open',
            'fa-paper-plane' => 'Paper Plane',
            'fa-comment-alt' => 'Comment Alt',
            'fa-comment-dots' => 'Comment Dots',
            'fa-comment-medical' => 'Medical Comment',
            'fa-comment-slash' => 'Comment Slash',
            'fa-sms' => 'SMS',
            'fa-inbox' => 'Inbox',
            'fa-archive' => 'Archive',
            'fa-trash' => 'Trash',
            'fa-trash-alt' => 'Trash Alt',
            'fa-recycle' => 'Recycle',
            'fa-box' => 'Box',
            'fa-boxes' => 'Boxes',
            'fa-dolly' => 'Dolly',
            'fa-pallet' => 'Pallet',
            'fa-shipping-fast' => 'Fast Shipping',
            'fa-truck-loading' => 'Truck Loading',
            'fa-truck-moving' => 'Truck Moving',
            'fa-people-carry' => 'People Carry',
            'fa-dolly-flatbed' => 'Dolly Flatbed',
            'fa-conveyor-belt' => 'Conveyor Belt',
            'fa-warehouse' => 'Warehouse',
            'fa-store-alt' => 'Store Alt',
            'fa-shopping-bag' => 'Shopping Bag',
            'fa-shopping-basket' => 'Shopping Basket',
            'fa-cash-register' => 'Cash Register',
            'fa-receipt' => 'Receipt',
            'fa-tag' => 'Tag',
            'fa-tags' => 'Tags',
            'fa-barcode' => 'Barcode',
            'fa-qrcode' => 'QR Code',
            'fa-percentage' => 'Percentage',
            'fa-hand-holding-usd' => 'Hand Holding Money',
            'fa-coins' => 'Coins',
            'fa-piggy-bank' => 'Piggy Bank',
            'fa-money-check' => 'Money Check',
            'fa-money-check-alt' => 'Money Check Alt',
            'fa-wallet' => 'Wallet',
            'fa-cash-register' => 'Cash Register',
            'fa-donate' => 'Donate',
            'fa-gift' => 'Gift',
            'fa-gem' => 'Gem',
            'fa-crown' => 'Crown',
            'fa-award' => 'Award',
            'fa-medal' => 'Medal',
            'fa-trophy' => 'Trophy',
            'fa-ribbon' => 'Ribbon',
            'fa-certificate' => 'Certificate',
            'fa-graduation-cap' => 'Graduation Cap',
            'fa-hat-wizard' => 'Wizard Hat',
            'fa-magic' => 'Magic',
            'fa-wand-magic' => 'Magic Wand',
            'fa-hat-cowboy' => 'Cowboy Hat',
            'fa-hat-cowboy-side' => 'Cowboy Hat Side',
            'fa-mask' => 'Mask',
            'fa-ghost' => 'Ghost',
            'fa-skull' => 'Skull',
            'fa-skull-crossbones' => 'Skull Crossbones',
            'fa-spider' => 'Spider',
            'fa-spider-web' => 'Spider Web',
            'fa-bat' => 'Bat',
            'fa-cat' => 'Cat',
            'fa-crow' => 'Crow',
            'fa-dog' => 'Dog',
            'fa-dove' => 'Dove',
            'fa-dragon' => 'Dragon',
            'fa-fish' => 'Fish',
            'fa-frog' => 'Frog',
            'fa-hippo' => 'Hippo',
            'fa-horse' => 'Horse',
            'fa-horse-head' => 'Horse Head',
            'fa-kiwi-bird' => 'Kiwi Bird',
            'fa-otter' => 'Otter',
            'fa-paw' => 'Paw',
            'fa-spider' => 'Spider'
        ];
    }

    /**
     * Get available colors for categories
     */
    private function getAvailableColors() {
        return [
            '#6366F1' => 'Indigo',
            '#8B5CF6' => 'Purple',
            '#EC4899' => 'Pink',
            '#EF4444' => 'Red',
            '#F97316' => 'Orange',
            '#F59E0B' => 'Amber',
            '#EAB308' => 'Yellow',
            '#84CC16' => 'Lime',
            '#22C55E' => 'Green',
            '#10B981' => 'Emerald',
            '#14B8A6' => 'Teal',
            '#06B6D4' => 'Cyan',
            '#0EA5E9' => 'Sky',
            '#3B82F6' => 'Blue',
            '#6366F1' => 'Indigo',
            '#8B5CF6' => 'Purple',
            '#A855F7' => 'Violet',
            '#D946EF' => 'Fuchsia',
            '#EC4899' => 'Pink',
            '#F43F5E' => 'Rose',
            '#64748B' => 'Slate',
            '#6B7280' => 'Gray',
            '#71717A' => 'Zinc',
            '#737373' => 'Neutral',
            '#78716C' => 'Stone',
            '#292524' => 'Black'
        ];
    }
}
