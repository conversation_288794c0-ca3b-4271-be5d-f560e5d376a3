<?php
/**
 * Freelance Client Controller
 *
 * Handles freelance client management functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/FreelanceClient.php';
require_once __DIR__ . '/../models/FreelanceProject.php';
require_once __DIR__ . '/../models/FreelanceInvoice.php';
require_once __DIR__ . '/../utils/Session.php';

class FreelanceClientController extends BaseController {
    private $clientModel;
    private $projectModel;
    private $invoiceModel;

    public function __construct() {
        $this->clientModel = new FreelanceClient();
        $this->projectModel = new FreelanceProject();
        $this->invoiceModel = new FreelanceInvoice();
    }

    /**
     * Show clients list
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get clients based on filters
        $clients = $this->clientModel->getUserClients($userId, $filters);

        // Get client summary
        $clientSummary = $this->clientModel->getClientSummary($userId);

        $this->view('freelance/clients/index', [
            'clients' => $clients,
            'clientSummary' => $clientSummary,
            'filters' => $filters
        ]);
    }

    /**
     * Show client creation form
     */
    public function create() {
        $this->requireLogin();

        $this->view('freelance/clients/create');
    }

    /**
     * Process client creation
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['name']);

        if (!empty($errors)) {
            $this->view('freelance/clients/create', [
                'errors' => $errors,
                'data' => $data
            ]);
            return;
        }

        // Prepare client data
        $clientData = [
            'user_id' => $userId,
            'name' => $data['name'],
            'company' => $data['company'] ?? null,
            'email' => $data['email'] ?? null,
            'phone' => $data['phone'] ?? null,
            'address' => $data['address'] ?? null,
            'website' => $data['website'] ?? null,
            'notes' => $data['notes'] ?? null,
            'status' => $data['status'] ?? 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create client
        $clientId = $this->clientModel->create($clientData);

        if ($clientId) {
            Session::setFlash('success', 'Client created successfully');
            $this->redirect('/freelance/clients/view/' . $clientId);
        } else {
            Session::setFlash('error', 'Failed to create client');

            $this->view('freelance/clients/create', [
                'data' => $data
            ]);
        }
    }

    /**
     * Show client details
     */
    public function viewClient($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get client
        $client = $this->clientModel->getClientDetails($id, $userId);

        // Verify client exists and belongs to user
        if (!$client) {
            Session::setFlash('error', 'Client not found');
            $this->redirect('/freelance/clients');
        }

        // Get client projects
        $projects = $this->projectModel->getUserProjects($userId, ['client_id' => $id]);

        // Get client invoices
        $invoices = $this->invoiceModel->getUserInvoices($userId, ['client_id' => $id]);

        $this->view('freelance/clients/view', [
            'client' => $client,
            'projects' => $projects,
            'invoices' => $invoices
        ]);
    }

    /**
     * Show client edit form
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get client
        $client = $this->clientModel->find($id);

        // Verify client exists and belongs to user
        if (!$client || $client['user_id'] != $userId) {
            Session::setFlash('error', 'Client not found');
            $this->redirect('/freelance/clients');
        }

        $this->view('freelance/clients/edit', [
            'client' => $client
        ]);
    }

    /**
     * Process client update
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get client
        $client = $this->clientModel->find($id);

        // Verify client exists and belongs to user
        if (!$client || $client['user_id'] != $userId) {
            Session::setFlash('error', 'Client not found');
            $this->redirect('/freelance/clients');
        }

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['name']);

        if (!empty($errors)) {
            $this->view('freelance/clients/edit', [
                'errors' => $errors,
                'client' => array_merge($client, $data)
            ]);
            return;
        }

        // Prepare client data
        $clientData = [
            'name' => $data['name'],
            'company' => $data['company'] ?? null,
            'email' => $data['email'] ?? null,
            'phone' => $data['phone'] ?? null,
            'address' => $data['address'] ?? null,
            'website' => $data['website'] ?? null,
            'notes' => $data['notes'] ?? null,
            'status' => $data['status'] ?? 'active',
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update client
        $result = $this->clientModel->update($id, $clientData);

        if ($result) {
            Session::setFlash('success', 'Client updated successfully');
            $this->redirect('/freelance/clients/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update client');

            $this->view('freelance/clients/edit', [
                'client' => array_merge($client, $data)
            ]);
        }
    }

    /**
     * Process client deletion
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get client
        $client = $this->clientModel->find($id);

        // Verify client exists and belongs to user
        if (!$client || $client['user_id'] != $userId) {
            Session::setFlash('error', 'Client not found');
            $this->redirect('/freelance/clients');
        }

        // Check if client has projects
        $projects = $this->projectModel->getUserProjects($userId, ['client_id' => $id]);
        if (!empty($projects)) {
            Session::setFlash('error', 'Cannot delete client with active projects');
            $this->redirect('/freelance/clients/view/' . $id);
            return;
        }

        // Delete client
        $result = $this->clientModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Client deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete client');
        }

        $this->redirect('/freelance/clients');
    }
}
