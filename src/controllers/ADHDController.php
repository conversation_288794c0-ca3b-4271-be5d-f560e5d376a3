<?php
/**
 * ADHD Controller
 *
 * Handles ADHD symptom tracking and CBT features
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/ADHDSymptom.php';
require_once __DIR__ . '/../models/ThoughtRecord.php';
require_once __DIR__ . '/../models/ProductivityStrategy.php';
require_once __DIR__ . '/../models/ConsistencyTracker.php';
require_once __DIR__ . '/../models/MindfulnessExercise.php';
require_once __DIR__ . '/../models/MedicationTracker.php';
require_once __DIR__ . '/../models/TriggerIdentification.php';
require_once __DIR__ . '/../models/ExecutiveFunction.php';
require_once __DIR__ . '/../models/ADHDMedicationReference.php';

class ADHDController extends BaseController {
    private $symptomModel;
    private $thoughtRecordModel;
    private $strategyModel;
    private $consistencyModel;
    private $mindfulnessModel;
    private $medicationModel;
    private $triggerModel;
    private $executiveFunctionModel;
    private $medicationReferenceModel;

    public function __construct() {
        $this->symptomModel = new ADHDSymptom();
        $this->thoughtRecordModel = new ThoughtRecord();
        $this->strategyModel = new ProductivityStrategy();
        $this->consistencyModel = new ConsistencyTracker();
        $this->mindfulnessModel = new MindfulnessExercise();
        $this->medicationModel = new MedicationTracker();
        $this->triggerModel = new TriggerIdentification();
        $this->executiveFunctionModel = new ExecutiveFunction();
        $this->medicationReferenceModel = new ADHDMedicationReference();
    }

    /**
     * Show ADHD dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get recent symptom logs
        $recentLogs = $this->symptomModel->getRecentLogs($userId, 7);

        // Get symptom trends for visualization
        $symptomTrends = $this->symptomModel->getSymptomTrends($userId, 30);

        // Get current streak
        $currentStreak = $this->symptomModel->getCurrentStreak($userId);

        // Get today's consistency trackers
        $todayTrackers = $this->consistencyModel->getTodayTrackers($userId);

        // Get active strategies
        $activeStrategies = $this->strategyModel->getUserActiveStrategies($userId);

        // Get recommended strategies based on symptom scores
        $recommendedStrategies = $this->strategyModel->getRecommendedStrategies($userId);

        // Get recommended mindfulness exercises
        $recommendedExercises = $this->mindfulnessModel->getRecommendedExercises($userId, 2);

        // Get mindfulness practice stats
        $mindfulnessStats = $this->mindfulnessModel->getPracticeStats($userId);

        // Check if user has logged symptoms today
        $hasLoggedToday = $this->symptomModel->hasLoggedToday($userId);

        $this->view('adhd/index', [
            'recentLogs' => $recentLogs,
            'symptomTrends' => $symptomTrends,
            'currentStreak' => $currentStreak,
            'todayTrackers' => $todayTrackers,
            'activeStrategies' => $activeStrategies,
            'recommendedStrategies' => $recommendedStrategies,
            'recommendedExercises' => $recommendedExercises,
            'mindfulnessStats' => $mindfulnessStats,
            'hasLoggedToday' => $hasLoggedToday
        ]);
    }

    /**
     * Symptom Tracking Section
     */

    public function symptomTracker() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get recent logs for the last 30 days
        $recentLogs = $this->symptomModel->getRecentLogs($userId, 30);

        // Get symptom trends for visualization
        $symptomTrends = $this->symptomModel->getSymptomTrends($userId, 30);

        // Get recent events
        $recentEvents = $this->symptomModel->getRecentEvents($userId, 30);

        $this->view('adhd/symptom_tracking/tracker', [
            'recentLogs' => $recentLogs,
            'symptomTrends' => $symptomTrends,
            'recentEvents' => $recentEvents
        ]);
    }

    public function logSymptoms() {
        $this->requireLogin();

        // Check if user has already logged today
        $user = Session::getUser();
        $userId = $user['id'];

        $hasLoggedToday = $this->symptomModel->hasLoggedToday($userId);
        $todayLog = null;

        if ($hasLoggedToday) {
            $todayLog = $this->symptomModel->getLogByDate($userId, date('Y-m-d'));
        }

        $this->view('adhd/symptom_tracking/log_form', [
            'hasLoggedToday' => $hasLoggedToday,
            'todayLog' => $todayLog
        ]);
    }

    public function saveSymptomLog() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = [
            'focus_score',
            'productivity_score',
            'consistency_score',
            'organization_score',
            'impulsivity_score',
            'emotional_regulation_score'
        ];

        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/adhd/symptom-tracker/log');
        }

        // Check if user has already logged today
        $hasLoggedToday = $this->symptomModel->hasLoggedToday($userId);

        if ($hasLoggedToday) {
            // Update existing log
            $todayLog = $this->symptomModel->getLogByDate($userId, date('Y-m-d'));

            $updateData = [
                'focus_score' => $postData['focus_score'],
                'productivity_score' => $postData['productivity_score'],
                'consistency_score' => $postData['consistency_score'],
                'organization_score' => $postData['organization_score'],
                'impulsivity_score' => $postData['impulsivity_score'],
                'emotional_regulation_score' => $postData['emotional_regulation_score'],
                'notes' => $postData['notes'] ?? null,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $result = $this->symptomModel->update($todayLog['id'], $updateData);

            if ($result) {
                Session::setFlash('success', 'Symptom log updated successfully');
                $this->redirect('/adhd/symptom-tracker');
            } else {
                Session::setFlash('error', 'Failed to update symptom log');
                $this->redirect('/adhd/symptom-tracker/log');
            }
        } else {
            // Create new log
            $logData = [
                'user_id' => $userId,
                'log_date' => date('Y-m-d'),
                'focus_score' => $postData['focus_score'],
                'productivity_score' => $postData['productivity_score'],
                'consistency_score' => $postData['consistency_score'],
                'organization_score' => $postData['organization_score'],
                'impulsivity_score' => $postData['impulsivity_score'],
                'emotional_regulation_score' => $postData['emotional_regulation_score'],
                'notes' => $postData['notes'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $result = $this->symptomModel->createLog($logData);

            if ($result) {
                Session::setFlash('success', 'Symptom log saved successfully');
                $this->redirect('/adhd/symptom-tracker');
            } else {
                Session::setFlash('error', 'Failed to save symptom log');
                $this->redirect('/adhd/symptom-tracker/log');
            }
        }
    }

    public function logEvent() {
        $this->requireLogin();

        $this->view('adhd/symptom_tracking/event_form');
    }

    public function saveEvent() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['symptom_type', 'situation', 'intensity'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/adhd/symptom-tracker/event');
        }

        // Add user ID and timestamps
        $eventData = [
            'user_id' => $userId,
            'symptom_type' => $postData['symptom_type'],
            'situation' => $postData['situation'],
            'intensity' => $postData['intensity'],
            'impact' => $postData['impact'] ?? null,
            'coping_strategy' => $postData['coping_strategy'] ?? null,
            'timestamp' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->symptomModel->createEvent($eventData);

        if ($result) {
            Session::setFlash('success', 'Event logged successfully');
            $this->redirect('/adhd/symptom-tracker');
        } else {
            Session::setFlash('error', 'Failed to log event');
            $this->redirect('/adhd/symptom-tracker/event');
        }
    }

    public function viewReports() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Default to last 30 days if no date range specified
        if (!isset($filters['start_date'])) {
            $filters['start_date'] = date('Y-m-d', strtotime('-30 days'));
        }

        if (!isset($filters['end_date'])) {
            $filters['end_date'] = date('Y-m-d');
        }

        // Get symptom data for the date range
        $symptomData = $this->symptomModel->getSymptomData($userId, $filters['start_date'], $filters['end_date']);

        // Get events for the date range
        $events = $this->symptomModel->getSymptomData($userId, $filters['start_date'], $filters['end_date']);

        $this->view('adhd/symptom_tracking/reports', [
            'symptomData' => $symptomData,
            'events' => $events,
            'filters' => $filters
        ]);
    }

    /**
     * CBT Tools Section
     */

    public function thoughtRecords() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get thought records
        $thoughtRecords = $this->thoughtRecordModel->getUserRecords($userId);

        $this->view('adhd/cbt/thought_records', [
            'thoughtRecords' => $thoughtRecords
        ]);
    }

    public function newThoughtRecord() {
        $this->requireLogin();

        // Get cognitive distortions for dropdown
        $cognitiveDistortions = $this->thoughtRecordModel->getCognitiveDistortions();

        $this->view('adhd/cbt/thought_record_form', [
            'cognitiveDistortions' => $cognitiveDistortions
        ]);
    }

    public function saveThoughtRecord() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['situation', 'emotions', 'emotion_intensity', 'automatic_thoughts'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/adhd/cbt/thought-records/new');
        }

        // Add user ID and timestamps
        $recordData = [
            'user_id' => $userId,
            'situation' => $postData['situation'],
            'emotions' => $postData['emotions'],
            'emotion_intensity' => $postData['emotion_intensity'],
            'automatic_thoughts' => $postData['automatic_thoughts'],
            'cognitive_distortions' => $postData['cognitive_distortions'] ?? null,
            'evidence_for' => $postData['evidence_for'] ?? null,
            'evidence_against' => $postData['evidence_against'] ?? null,
            'balanced_thought' => $postData['balanced_thought'] ?? null,
            'outcome_emotion' => $postData['outcome_emotion'] ?? null,
            'outcome_intensity' => $postData['outcome_intensity'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->thoughtRecordModel->createRecord($recordData);

        if ($result) {
            Session::setFlash('success', 'Thought record saved successfully');
            $this->redirect('/adhd/cbt/thought-records');
        } else {
            Session::setFlash('error', 'Failed to save thought record');
            $this->redirect('/adhd/cbt/thought-records/new');
        }
    }

    public function viewThoughtRecord($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get thought record
        $thoughtRecord = $this->thoughtRecordModel->getRecord($id);

        // Verify thought record exists and belongs to user
        if (!$thoughtRecord || $thoughtRecord['user_id'] != $userId) {
            Session::setFlash('error', 'Thought record not found');
            $this->redirect('/adhd/cbt/thought-records');
        }

        // Get cognitive distortions
        $cognitiveDistortions = $this->thoughtRecordModel->getCognitiveDistortions();

        $this->view('adhd/cbt/thought_record_view', [
            'thoughtRecord' => $thoughtRecord,
            'cognitiveDistortions' => $cognitiveDistortions
        ]);
    }

    /**
     * Productivity Strategies Section
     */

    public function strategies() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all strategies
        $allStrategies = $this->strategyModel->getAllStrategies();

        // Get user's active strategies
        $activeStrategies = $this->strategyModel->getUserActiveStrategies($userId);

        // Get user's strategy history
        $strategyHistory = $this->strategyModel->getUserStrategyHistory($userId);

        $this->view('adhd/productivity/strategies', [
            'allStrategies' => $allStrategies,
            'activeStrategies' => $activeStrategies,
            'strategyHistory' => $strategyHistory
        ]);
    }

    public function viewStrategy($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get strategy
        $strategy = $this->strategyModel->getStrategy($id);

        if (!$strategy) {
            Session::setFlash('error', 'Strategy not found');
            $this->redirect('/adhd/productivity/strategies');
        }

        // Check if this strategy is active for the current user
        $userStrategy = $this->strategyModel->getUserStrategyByStrategyId($userId, $id);
        $isActive = !empty($userStrategy) && $userStrategy['is_active'] == 1;

        $this->view('adhd/productivity/strategy_view', [
            'strategy' => $strategy,
            'isActive' => $isActive,
            'userStrategy' => $userStrategy ?: null
        ]);
    }

    public function addStrategy() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['strategy_id'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please select a strategy');
            $this->redirect('/adhd/productivity/strategies');
        }

        // Add user ID and timestamps
        $strategyData = [
            'user_id' => $userId,
            'strategy_id' => $postData['strategy_id'],
            'start_date' => date('Y-m-d'),
            'is_active' => 1,
            'notes' => $postData['notes'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->strategyModel->addUserStrategy($strategyData);

        if ($result) {
            Session::setFlash('success', 'Strategy added successfully');
            $this->redirect('/adhd/productivity/strategies');
        } else {
            Session::setFlash('error', 'Failed to add strategy');
            $this->redirect('/adhd/productivity/strategies');
        }
    }

    public function updateStrategyEffectiveness() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['user_strategy_id', 'effectiveness_rating'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            $this->json(['success' => false, 'message' => 'Please provide all required fields']);
            return;
        }

        $updateData = [
            'effectiveness_rating' => $postData['effectiveness_rating'],
            'notes' => $postData['notes'] ?? null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->strategyModel->updateUserStrategy($postData['user_strategy_id'], $updateData);

        if ($result) {
            $this->json(['success' => true]);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to update strategy effectiveness']);
        }
    }

    public function deactivateStrategy($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $result = $this->strategyModel->deactivateUserStrategy($id, $userId);

        if ($result) {
            Session::setFlash('success', 'Strategy deactivated successfully');
        } else {
            Session::setFlash('error', 'Failed to deactivate strategy');
        }

        $this->redirect('/adhd/productivity/strategies');
    }

    /**
     * Mindfulness & Emotional Regulation Section
     */

    public function mindfulness() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all exercises
        $exercises = $this->mindfulnessModel->getAllExercises();

        // Group exercises by category
        $exercisesByCategory = [];
        foreach ($exercises as $exercise) {
            $category = $exercise['category'];
            if (!isset($exercisesByCategory[$category])) {
                $exercisesByCategory[$category] = [];
            }
            $exercisesByCategory[$category][] = $exercise;
        }

        // Get user's practice logs
        $recentLogs = $this->mindfulnessModel->getRecentLogs($userId, 30);

        // Get practice statistics
        $practiceStats = $this->mindfulnessModel->getPracticeStats($userId);

        // Get recommended exercises based on emotional regulation score
        $recommendedExercises = $this->mindfulnessModel->getRecommendedExercises($userId, 3);

        $this->view('adhd/mindfulness/index', [
            'exercisesByCategory' => $exercisesByCategory,
            'recentLogs' => $recentLogs,
            'practiceStats' => $practiceStats,
            'recommendedExercises' => $recommendedExercises
        ]);
    }

    public function viewExercise($id) {
        $this->requireLogin();

        // Get exercise details
        $exercise = $this->mindfulnessModel->getExercise($id);

        if (!$exercise) {
            Session::setFlash('error', 'Exercise not found');
            $this->redirect('/adhd/mindfulness');
        }

        $this->view('adhd/mindfulness/exercise_details', [
            'exercise' => $exercise
        ]);
    }

    public function practiceExercise($id) {
        $this->requireLogin();

        // Get exercise details
        $exercise = $this->mindfulnessModel->getExercise($id);

        if (!$exercise) {
            Session::setFlash('error', 'Exercise not found');
            $this->redirect('/adhd/mindfulness');
        }

        $this->view('adhd/mindfulness/practice_session', [
            'exercise' => $exercise
        ]);
    }

    public function logPractice() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['exercise_id', 'duration_minutes', 'mood_before', 'mood_after'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/adhd/mindfulness/practice/' . $postData['exercise_id']);
        }

        // Add user ID and timestamps
        $logData = [
            'user_id' => $userId,
            'exercise_id' => $postData['exercise_id'],
            'practice_date' => date('Y-m-d H:i:s'),
            'duration_minutes' => $postData['duration_minutes'],
            'mood_before' => $postData['mood_before'],
            'mood_after' => $postData['mood_after'],
            'focus_improvement' => $postData['focus_improvement'] ?? null,
            'notes' => $postData['notes'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->mindfulnessModel->logPractice($logData);

        if ($result) {
            Session::setFlash('success', 'Practice session logged successfully');
            $this->redirect('/adhd/mindfulness');
        } else {
            Session::setFlash('error', 'Failed to log practice session');
            $this->redirect('/adhd/mindfulness/practice/' . $postData['exercise_id']);
        }
    }

    public function quickExercises() {
        $this->requireLogin();

        // Get quick exercises (5 minutes or less)
        $quickExercises = $this->mindfulnessModel->getExercisesByMaxDuration(5);

        $this->view('adhd/mindfulness/quick_exercises', [
            'exercises' => $quickExercises
        ]);
    }

    /**
     * Consistency Tracking Section
     */

    public function consistencyTrackers() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all trackers
        $trackers = $this->consistencyModel->getUserTrackers($userId);

        // Get today's trackers
        $todayTrackers = $this->consistencyModel->getTodayTrackers($userId);

        // Get overall consistency score
        $overallConsistency = $this->consistencyModel->getOverallConsistency($userId);

        $this->view('adhd/consistency/trackers', [
            'trackers' => $trackers,
            'todayTrackers' => $todayTrackers,
            'overallConsistency' => $overallConsistency
        ]);
    }

    public function newTracker() {
        $this->requireLogin();

        $this->view('adhd/consistency/tracker_form');
    }

    public function saveTracker() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['habit_name', 'frequency'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/adhd/consistency/trackers/new');
        }

        // Add user ID and timestamps
        $trackerData = [
            'user_id' => $userId,
            'habit_name' => $postData['habit_name'],
            'description' => $postData['description'] ?? null,
            'frequency' => $postData['frequency'],
            'custom_days' => $postData['custom_days'] ?? null,
            'time_of_day' => $postData['time_of_day'] ?? null,
            'streak_count' => 0,
            'longest_streak' => 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->consistencyModel->createTracker($trackerData);

        if ($result) {
            Session::setFlash('success', 'Habit tracker created successfully');
            $this->redirect('/adhd/consistency/trackers');
        } else {
            Session::setFlash('error', 'Failed to create habit tracker');
            $this->redirect('/adhd/consistency/trackers/new');
        }
    }

    public function viewTracker($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get tracker
        $tracker = $this->consistencyModel->getTracker($id);

        // Verify tracker exists and belongs to user
        if (!$tracker || $tracker['user_id'] != $userId) {
            Session::setFlash('error', 'Tracker not found');
            $this->redirect('/adhd/consistency/trackers');
        }

        // Get logs for the last 30 days
        $startDate = date('Y-m-d', strtotime('-30 days'));
        $endDate = date('Y-m-d');
        $logs = $this->consistencyModel->getTrackerLogs($id, $startDate, $endDate);

        // Get completion rate
        $completionRate = $this->consistencyModel->getCompletionRate($id);

        $this->view('adhd/consistency/tracker_view', [
            'tracker' => $tracker,
            'logs' => $logs,
            'completionRate' => $completionRate
        ]);
    }

    public function logConsistency() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['tracker_id', 'completed'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            $this->json(['success' => false, 'message' => 'Please provide all required fields']);
            return;
        }

        // Verify tracker belongs to user
        $tracker = $this->consistencyModel->getTracker($postData['tracker_id']);

        if (!$tracker || $tracker['user_id'] != $userId) {
            $this->json(['success' => false, 'message' => 'Tracker not found']);
            return;
        }

        $completed = $postData['completed'] == 1;
        $difficultyRating = $postData['difficulty_rating'] ?? null;
        $notes = $postData['notes'] ?? null;

        $result = $this->consistencyModel->logCompletion(
            $postData['tracker_id'],
            date('Y-m-d'),
            $completed,
            $difficultyRating,
            $notes
        );

        if ($result) {
            // Update streak
            $this->consistencyModel->updateStreak($postData['tracker_id']);

            $this->json(['success' => true]);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to log completion']);
        }
    }

    public function deleteTracker($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Verify tracker belongs to user
        $tracker = $this->consistencyModel->getTracker($id);

        if (!$tracker || $tracker['user_id'] != $userId) {
            Session::setFlash('error', 'Tracker not found');
            $this->redirect('/adhd/consistency/trackers');
        }

        $result = $this->consistencyModel->deleteTracker($id);

        if ($result) {
            Session::setFlash('success', 'Tracker deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete tracker');
        }

        $this->redirect('/adhd/consistency/trackers');
    }

    /**
     * Medication Tracker Section
     */

    public function medications() {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication');
    }

    public function newMedication() {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/new');
    }

    public function saveMedication() {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/new');
    }

    public function viewMedication($id) {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/view/' . $id);
    }

    public function editMedication($id) {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/edit/' . $id);
    }

    public function updateMedication($id) {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/edit/' . $id);
    }

    public function deleteMedication($id) {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/delete/' . $id);
    }

    public function logMedication() {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/log');
    }

    public function logMedicationById($id) {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/log/' . $id);
    }

    public function saveMedicationLog() {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/log');
    }

    public function editMedicationLog($id) {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/log/edit/' . $id);
    }

    public function updateMedicationLog($id) {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/log/edit/' . $id);
    }

    public function deleteMedicationLog($id) {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/log/delete/' . $id);
    }

    public function newMedicationReminder($id) {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/reminder/new/' . $id);
    }

    public function saveMedicationReminder() {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication');
    }

    public function editMedicationReminder($id) {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/reminder/edit/' . $id);
    }

    public function updateMedicationReminder($id) {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/reminder/edit/' . $id);
    }

    public function deleteMedicationReminder($id) {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/reminder/delete/' . $id);
    }

    /**
     * Medication Reference Section
     */

    public function medicationReference() {
        $this->requireLogin();

        // Get all medication classes
        $medicationClasses = $this->medicationReferenceModel->getMedicationClasses();

        // Get all medications grouped by class
        $medicationsByClass = [];
        foreach ($medicationClasses as $class) {
            $className = $class['medication_class'];
            $medicationsByClass[$className] = $this->medicationReferenceModel->getMedicationsByClass($className);
        }

        // Get all enhancement categories
        $enhancementCategories = $this->medicationReferenceModel->getEnhancementCategories();

        // Get all medications grouped by enhancement area
        $medicationsByEnhancement = [];
        foreach ($enhancementCategories as $category) {
            $categoryName = $category['name'];
            $medicationsByEnhancement[$categoryName] = $this->medicationReferenceModel->getMedicationsByEnhancementArea($categoryName);
        }

        $this->view('adhd/medication/reference', [
            'medicationClasses' => $medicationClasses,
            'medicationsByClass' => $medicationsByClass,
            'enhancementCategories' => $enhancementCategories,
            'medicationsByEnhancement' => $medicationsByEnhancement
        ]);
    }

    public function viewMedicationReference($id) {
        $this->requireLogin();

        // Get medication reference
        $medication = $this->medicationReferenceModel->getMedication($id);

        if (!$medication) {
            Session::setFlash('error', 'Medication reference not found');
            $this->redirect('/adhd/medication/reference');
        }

        // Check if this is an AJAX request
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

        if ($isAjax) {
            $this->json($medication);
            return;
        }

        $this->view('adhd/medication/reference_details', [
            'medication' => $medication
        ]);
    }

    public function searchMedicationReference() {
        $this->requireLogin();

        $searchTerm = $this->getQueryData('search');

        if (empty($searchTerm)) {
            $this->redirect('/adhd/medication/reference');
        }

        // Ensure searchTerm is a string
        if (is_array($searchTerm)) {
            $searchTerm = $searchTerm[0];
        }

        // Search medications
        $searchResults = $this->medicationReferenceModel->searchMedications($searchTerm);

        $this->view('adhd/medication/reference_search', [
            'searchTerm' => $searchTerm,
            'searchResults' => $searchResults
        ]);
    }

    public function getMedicationSuggestions() {
        $this->requireLogin();

        $searchTerm = $this->getQueryData('term');

        if (empty($searchTerm)) {
            $this->json([]);
            return;
        }

        // Ensure searchTerm is a string
        if (is_array($searchTerm)) {
            $searchTerm = $searchTerm[0];
        }

        // Get medication suggestions
        $suggestions = $this->medicationReferenceModel->getMedicationSuggestions($searchTerm);

        $this->json($suggestions);
    }

    public function medicationReports() {
        // Redirect to the new Medical Medication section
        $this->redirect('/medical/medication/reports');
    }

    /**
     * Trigger Identification Section
     */

    public function triggers() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all triggers
        $triggers = $this->triggerModel->getUserTriggers($userId);

        // Get high-impact triggers
        $highImpactTriggers = $this->triggerModel->getHighImpactTriggers($userId);

        // Get trigger categories with counts
        $triggerCategories = $this->triggerModel->getTriggerCategories($userId);

        // Get most effective coping strategies
        $effectiveStrategies = $this->triggerModel->getMostEffectiveStrategies($userId);

        $this->view('adhd/triggers/index', [
            'triggers' => $triggers,
            'highImpactTriggers' => $highImpactTriggers,
            'triggerCategories' => $triggerCategories,
            'effectiveStrategies' => $effectiveStrategies
        ]);
    }

    public function newTrigger() {
        $this->requireLogin();

        $this->view('adhd/triggers/trigger_form');
    }

    public function saveTrigger() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['name', 'category'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/adhd/triggers/new');
        }

        // Add user ID and timestamps
        $triggerData = [
            'user_id' => $userId,
            'name' => $postData['name'],
            'description' => $postData['description'] ?? null,
            'category' => $postData['category'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->triggerModel->createTrigger($triggerData);

        if ($result) {
            Session::setFlash('success', 'Trigger added successfully');
            $this->redirect('/adhd/triggers');
        } else {
            Session::setFlash('error', 'Failed to add trigger');
            $this->redirect('/adhd/triggers/new');
        }
    }

    public function viewTrigger($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get trigger
        $trigger = $this->triggerModel->getTrigger($id);

        // Verify trigger exists and belongs to user
        if (!$trigger || $trigger['user_id'] != $userId) {
            Session::setFlash('error', 'Trigger not found');
            $this->redirect('/adhd/triggers');
        }

        // Get trigger occurrences
        $occurrences = $this->triggerModel->getTriggerOccurrences($id);

        // Get coping strategies
        $strategies = $this->triggerModel->getTriggerStrategies($id);

        $this->view('adhd/triggers/trigger_view', [
            'trigger' => $trigger,
            'occurrences' => $occurrences,
            'strategies' => $strategies
        ]);
    }

    public function logTriggerOccurrence($triggerId = null) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all triggers for dropdown
        $triggers = $this->triggerModel->getUserTriggers($userId);

        // If trigger ID is provided, verify it belongs to user
        $selectedTrigger = null;
        if ($triggerId) {
            $selectedTrigger = $this->triggerModel->getTrigger($triggerId);
            if (!$selectedTrigger || $selectedTrigger['user_id'] != $userId) {
                Session::setFlash('error', 'Trigger not found');
                $this->redirect('/adhd/triggers');
            }
        }

        $this->view('adhd/triggers/occurrence_form', [
            'triggers' => $triggers,
            'selectedTrigger' => $selectedTrigger
        ]);
    }

    public function saveTriggerOccurrence() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['trigger_id', 'occurrence_date', 'impact_rating'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/adhd/triggers/occurrence');
        }

        // Verify trigger belongs to user
        $trigger = $this->triggerModel->getTrigger($postData['trigger_id']);

        if (!$trigger || $trigger['user_id'] != $userId) {
            Session::setFlash('error', 'Trigger not found');
            $this->redirect('/adhd/triggers/occurrence');
        }

        // Add occurrence data
        $occurrenceData = [
            'trigger_id' => $postData['trigger_id'],
            'occurrence_date' => $postData['occurrence_date'],
            'occurrence_time' => $postData['occurrence_time'] ?? null,
            'impact_rating' => $postData['impact_rating'],
            'symptoms_experienced' => $postData['symptoms_experienced'] ?? null,
            'context' => $postData['context'] ?? null,
            'coping_strategy_used' => $postData['coping_strategy_used'] ?? null,
            'coping_effectiveness' => $postData['coping_effectiveness'] ?? null,
            'notes' => $postData['notes'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->triggerModel->createOccurrence($occurrenceData);

        if ($result) {
            Session::setFlash('success', 'Trigger occurrence logged successfully');
            $this->redirect('/adhd/triggers/view/' . $postData['trigger_id']);
        } else {
            Session::setFlash('error', 'Failed to log trigger occurrence');
            $this->redirect('/adhd/triggers/occurrence');
        }
    }

    public function editTriggerOccurrence($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the occurrence
        $occurrence = $this->triggerModel->getOccurrence($id);

        // Verify occurrence exists
        if (!$occurrence) {
            Session::setFlash('error', 'Occurrence not found');
            $this->redirect('/adhd/triggers');
        }

        // Get the trigger to verify ownership
        $trigger = $this->triggerModel->getTrigger($occurrence['trigger_id']);
        if (!$trigger || $trigger['user_id'] != $userId) {
            Session::setFlash('error', 'Trigger not found');
            $this->redirect('/adhd/triggers');
        }

        // Get all triggers for dropdown
        $triggers = $this->triggerModel->getUserTriggers($userId);

        $this->view('adhd/triggers/occurrence_edit_form', [
            'occurrence' => $occurrence,
            'trigger' => $trigger,
            'triggers' => $triggers
        ]);
    }

    public function updateTriggerOccurrence($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the occurrence
        $occurrence = $this->triggerModel->getOccurrence($id);

        // Verify occurrence exists
        if (!$occurrence) {
            Session::setFlash('error', 'Occurrence not found');
            $this->redirect('/adhd/triggers');
        }

        // Get the trigger to verify ownership
        $trigger = $this->triggerModel->getTrigger($occurrence['trigger_id']);
        if (!$trigger || $trigger['user_id'] != $userId) {
            Session::setFlash('error', 'Trigger not found');
            $this->redirect('/adhd/triggers');
        }

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['trigger_id', 'occurrence_date', 'impact_rating'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/adhd/triggers/occurrence/edit/' . $id);
        }

        // Verify new trigger belongs to user if changed
        if ($postData['trigger_id'] != $occurrence['trigger_id']) {
            $newTrigger = $this->triggerModel->getTrigger($postData['trigger_id']);
            if (!$newTrigger || $newTrigger['user_id'] != $userId) {
                Session::setFlash('error', 'Trigger not found');
                $this->redirect('/adhd/triggers/occurrence/edit/' . $id);
            }
        }

        // Update occurrence data
        $occurrenceData = [
            'trigger_id' => $postData['trigger_id'],
            'occurrence_date' => $postData['occurrence_date'],
            'occurrence_time' => $postData['occurrence_time'] ?? null,
            'impact_rating' => $postData['impact_rating'],
            'symptoms_experienced' => $postData['symptoms_experienced'] ?? null,
            'context' => $postData['context'] ?? null,
            'coping_strategy_used' => $postData['coping_strategy_used'] ?? null,
            'coping_effectiveness' => $postData['coping_effectiveness'] ?? null,
            'notes' => $postData['notes'] ?? null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->triggerModel->updateOccurrence($id, $occurrenceData);

        if ($result) {
            Session::setFlash('success', 'Trigger occurrence updated successfully');
            $this->redirect('/adhd/triggers/view/' . $postData['trigger_id']);
        } else {
            Session::setFlash('error', 'Failed to update trigger occurrence');
            $this->redirect('/adhd/triggers/occurrence/edit/' . $id);
        }
    }

    public function addCopingStrategy($triggerId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Verify trigger belongs to user
        $trigger = $this->triggerModel->getTrigger($triggerId);

        if (!$trigger || $trigger['user_id'] != $userId) {
            Session::setFlash('error', 'Trigger not found');
            $this->redirect('/adhd/triggers');
        }

        $this->view('adhd/triggers/strategy_form', [
            'trigger' => $trigger
        ]);
    }

    public function saveCopingStrategy() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['trigger_id', 'strategy_name'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/adhd/triggers/strategy/' . $postData['trigger_id']);
        }

        // Verify trigger belongs to user
        $trigger = $this->triggerModel->getTrigger($postData['trigger_id']);

        if (!$trigger || $trigger['user_id'] != $userId) {
            Session::setFlash('error', 'Trigger not found');
            $this->redirect('/adhd/triggers');
        }

        // Add strategy data
        $strategyData = [
            'trigger_id' => $postData['trigger_id'],
            'strategy_name' => $postData['strategy_name'],
            'description' => $postData['description'] ?? null,
            'effectiveness_rating' => $postData['effectiveness_rating'] ?? null,
            'notes' => $postData['notes'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->triggerModel->createCopingStrategy($strategyData);

        if ($result) {
            Session::setFlash('success', 'Coping strategy added successfully');
            $this->redirect('/adhd/triggers/view/' . $postData['trigger_id']);
        } else {
            Session::setFlash('error', 'Failed to add coping strategy');
            $this->redirect('/adhd/triggers/strategy/' . $postData['trigger_id']);
        }
    }

    /**
     * Executive Function Exercises Section
     */

    public function executiveFunctionExercises() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all exercises
        $exercises = $this->executiveFunctionModel->getAllExercises();

        // Group exercises by category
        $exercisesByCategory = [];
        foreach ($exercises as $exercise) {
            $category = $exercise['category'];
            if (!isset($exercisesByCategory[$category])) {
                $exercisesByCategory[$category] = [];
            }
            $exercisesByCategory[$category][] = $exercise;
        }

        // Get user's recent results
        $recentResults = $this->executiveFunctionModel->getUserRecentResults($userId, 10);

        // Get progress by category
        $progressByCategory = $this->executiveFunctionModel->getUserProgressByCategory($userId);

        $this->view('adhd/executive_function/index', [
            'exercisesByCategory' => $exercisesByCategory,
            'recentResults' => $recentResults,
            'progressByCategory' => $progressByCategory
        ]);
    }

    public function viewExecutiveExercise($id) {
        $this->requireLogin();

        // Get exercise
        $exercise = $this->executiveFunctionModel->getExercise($id);

        if (!$exercise) {
            Session::setFlash('error', 'Exercise not found');
            $this->redirect('/adhd/executive-function');
        }

        $this->view('adhd/executive_function/exercise_view', [
            'exercise' => $exercise
        ]);
    }

    public function practiceExecutiveExercise($id) {
        $this->requireLogin();

        // Get exercise
        $exercise = $this->executiveFunctionModel->getExercise($id);

        if (!$exercise) {
            Session::setFlash('error', 'Exercise not found');
            $this->redirect('/adhd/executive-function');
        }

        $this->view('adhd/executive_function/practice', [
            'exercise' => $exercise
        ]);
    }

    public function saveExerciseResult() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $postData = $this->getPostData();

        // Validate required fields
        $requiredFields = ['exercise_id', 'score', 'time_taken'];
        $errors = $this->validateRequired($postData, $requiredFields);

        if (!empty($errors)) {
            $this->json(['success' => false, 'message' => 'Please provide all required fields']);
            return;
        }

        // Add result data
        $resultData = [
            'user_id' => $userId,
            'exercise_id' => $postData['exercise_id'],
            'completion_date' => date('Y-m-d'),
            'completion_time' => date('H:i:s'),
            'score' => $postData['score'],
            'time_taken' => $postData['time_taken'],
            'difficulty_rating' => $postData['difficulty_rating'] ?? null,
            'notes' => $postData['notes'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->executiveFunctionModel->saveResult($resultData);

        if ($result) {
            // Update progress
            $exercise = $this->executiveFunctionModel->getExercise($postData['exercise_id']);
            $this->executiveFunctionModel->updateUserProgress($userId, $exercise['category'], $postData['score']);

            $this->json(['success' => true]);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to save exercise result']);
        }
    }

    public function viewProgress() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get progress by category
        $progressByCategory = $this->executiveFunctionModel->getUserProgressByCategory($userId);

        // Get progress over time
        $progressOverTime = $this->executiveFunctionModel->getUserProgressOverTime($userId, 90);

        // Get exercise completion counts
        $exerciseCounts = $this->executiveFunctionModel->getUserExerciseCounts($userId);

        $this->view('adhd/executive_function/progress', [
            'progressByCategory' => $progressByCategory,
            'progressOverTime' => $progressOverTime,
            'exerciseCounts' => $exerciseCounts
        ]);
    }

    /**
     * Display the ADHD Guide
     */
    public function guide() {
        $this->requireLogin();

        // Include the standalone ADHD guide page
        include_once dirname(dirname(__DIR__)) . '/public/adhd-guide.php';
        exit;
    }

    /**
     * Get ADHD data for dashboard
     *
     * @return array ADHD data for dashboard
     */
    public function getDashboardData() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if user has logged symptoms today
        $hasLoggedToday = $this->symptomModel->hasLoggedToday($userId);

        // Get current streak
        $currentStreak = $this->symptomModel->getCurrentStreak($userId);

        // Get medication reminders
        $medicationReminders = $this->medicationModel->getTodayReminders($userId) ?: [];

        // Get high-impact triggers
        $highImpactTriggers = $this->triggerModel->getHighImpactTriggers($userId, 8) ?: [];

        return [
            'hasLoggedToday' => $hasLoggedToday,
            'currentStreak' => $currentStreak,
            'medicationReminders' => $medicationReminders,
            'highImpactTriggers' => $highImpactTriggers
        ];
    }
}
