<?php
/**
 * Auth Controller
 *
 * Handles user authentication.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/User.php';

class AuthController extends BaseController {
    private $userModel;

    public function __construct() {
        $this->userModel = new User();
    }

    /**
     * Show login form
     */
    public function showLogin() {
        // Redirect to dashboard if already logged in
        if (Session::isLoggedIn()) {
            $this->redirect('/dashboard');
        }

        $this->view('auth/login', ['hideNav' => true]);
    }

    /**
     * Process login form
     */
    public function login() {
        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['email', 'password']);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->view('auth/login', ['errors' => $errors, 'data' => $data, 'hideNav' => true]);
            return;
        }

        // Authenticate user
        $user = $this->userModel->authenticate($data['email'], $data['password']);

        if ($user) {
            // Set user in session
            Session::setUser($user);

            // Regenerate session ID for security
            Session::regenerate();

            Session::setFlash('success', 'Welcome back, ' . $user['name']);
            $this->redirect('/dashboard');
        } else {
            Session::setFlash('error', 'Invalid email or password');
            $this->view('auth/login', ['data' => $data, 'hideNav' => true]);
        }
    }

    /**
     * Show registration form
     */
    public function showRegister() {
        // Redirect to dashboard if already logged in
        if (Session::isLoggedIn()) {
            $this->redirect('/dashboard');
        }

        $this->view('auth/register', ['hideNav' => true]);
    }

    /**
     * Process registration form
     */
    public function register() {
        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['name', 'email', 'password', 'confirm_password']);

        // Validate email
        if (empty($errors['email']) && !$this->validateEmail($data['email'])) {
            $errors['email'] = 'Please enter a valid email address';
        }

        // Check if email already exists
        if (empty($errors['email']) && $this->userModel->findOneBy('email', $data['email'])) {
            $errors['email'] = 'Email address is already registered';
        }

        // Validate password
        if (empty($errors['password']) && strlen($data['password']) < 6) {
            $errors['password'] = 'Password must be at least 6 characters';
        }

        // Validate password confirmation
        if (empty($errors['confirm_password']) && $data['password'] !== $data['confirm_password']) {
            $errors['confirm_password'] = 'Passwords do not match';
        }

        if (!empty($errors)) {
            $this->view('auth/register', ['errors' => $errors, 'data' => $data, 'hideNav' => true]);
            return;
        }

        // Register user
        $userId = $this->userModel->register([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => $data['password']
        ]);

        if ($userId) {
            // Create default categories for the user
            require_once __DIR__ . '/../models/Category.php';
            $categoryModel = new Category();

            $defaultCategories = [
                ['name' => 'Work', 'color' => '#e74c3c'],
                ['name' => 'Personal', 'color' => '#2ecc71'],
                ['name' => 'Health', 'color' => '#9b59b6'],
                ['name' => 'Finance', 'color' => '#f39c12'],
                ['name' => 'Education', 'color' => '#3498db']
            ];

            foreach ($defaultCategories as $category) {
                $categoryModel->create([
                    'user_id' => $userId,
                    'name' => $category['name'],
                    'color' => $category['color'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            Session::setFlash('success', 'Registration successful! Please log in.');
            $this->redirect('/login');
        } else {
            Session::setFlash('error', 'Registration failed. Please try again.');
            $this->view('auth/register', ['data' => $data, 'hideNav' => true]);
        }
    }

    /**
     * Log out user
     */
    public function logout() {
        Session::logout();
        Session::setFlash('success', 'You have been logged out');
        $this->redirect('/login');
    }

    /**
     * Show forgot password form
     */
    public function showForgotPassword() {
        $this->view('auth/forgot_password', ['hideNav' => true]);
    }

    /**
     * Process forgot password form
     */
    public function forgotPassword() {
        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['email']);

        if (!empty($errors)) {
            $this->view('auth/forgot_password', ['errors' => $errors, 'data' => $data, 'hideNav' => true]);
            return;
        }

        // Check if email exists
        $user = $this->userModel->findOneBy('email', $data['email']);

        if ($user) {
            // In a real application, you would generate a token and send a password reset email
            // For this demo, we'll just show a success message
            Session::setFlash('success', 'If your email is registered, you will receive password reset instructions');
        } else {
            // Don't reveal that the email doesn't exist for security reasons
            Session::setFlash('success', 'If your email is registered, you will receive password reset instructions');
        }

        $this->redirect('/login');
    }
}
