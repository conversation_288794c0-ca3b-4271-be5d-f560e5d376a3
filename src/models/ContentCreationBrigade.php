<?php
/**
 * Content Creation Brigade Class
 *
 * This class manages the Content Creation Brigade, which is responsible for
 * generating high-quality, SEO-optimized content at scale.
 */

require_once __DIR__ . '/agents/BrigadeCoordinator.php';
require_once __DIR__ . '/agents/content_creation/ResearchAgent.php';
require_once __DIR__ . '/agents/content_creation/ContentPlanningAgent.php';
require_once __DIR__ . '/agents/content_creation/WritingAgent.php';
require_once __DIR__ . '/agents/content_creation/EditingAgent.php';
require_once __DIR__ . '/agents/content_creation/SEOOptimizationAgent.php';

class ContentCreationBrigade {
    protected $projectId;
    protected $userId;
    protected $coordinator;
    protected $projectModel;
    protected $taskModel;
    protected $assignmentModel;
    protected $agentModel;
    
    /**
     * Constructor
     *
     * @param int $projectId The ID of the project this brigade is associated with
     * @param int $userId The ID of the user who owns this brigade
     */
    public function __construct($projectId, $userId) {
        require_once __DIR__ . '/Project.php';
        require_once __DIR__ . '/Task.php';
        require_once __DIR__ . '/ProjectAgentAssignment.php';
        require_once __DIR__ . '/AIAgent.php';
        
        $this->projectId = $projectId;
        $this->userId = $userId;
        
        $this->projectModel = new Project();
        $this->taskModel = new Task();
        $this->assignmentModel = new ProjectAgentAssignment();
        $this->agentModel = new AIAgent();
        
        // Create the brigade coordinator
        $this->coordinator = new BrigadeCoordinator('content_creation', $projectId, $userId);
    }
    
    /**
     * Initialize the brigade by creating necessary agents if they don't exist
     *
     * @return bool True if initialization was successful, false otherwise
     */
    public function initialize() {
        // Check if the project exists and is a content creation brigade
        $project = $this->projectModel->getProjectDetails($this->projectId, $this->userId);
        
        if (!$project || $project['brigade_type'] !== 'content_creation') {
            return false;
        }
        
        // Create the required agents if they don't exist
        $this->ensureAgentsExist();
        
        // Assign agents to roles if not already assigned
        $this->ensureAgentsAssigned();
        
        return true;
    }
    
    /**
     * Ensure all required agents exist
     */
    protected function ensureAgentsExist() {
        $requiredAgents = [
            'Research Agent' => [
                'description' => 'Gathers information and identifies trending topics.',
                'capabilities' => 'Research, data collection, trend analysis, competitor analysis, audience research',
                'personality_traits' => 'Curious, thorough, analytical, detail-oriented',
                'intelligence_level' => 8,
                'efficiency_rating' => 7.5,
                'reliability_score' => 8.0
            ],
            'Content Planning Agent' => [
                'description' => 'Creates content outlines and strategies.',
                'capabilities' => 'Content strategy development, outline creation, content calendar planning, topic clustering',
                'personality_traits' => 'Strategic, organized, creative, forward-thinking',
                'intelligence_level' => 8,
                'efficiency_rating' => 7.0,
                'reliability_score' => 7.5
            ],
            'Writing Agent' => [
                'description' => 'Generates actual content based on outlines.',
                'capabilities' => 'Content writing, storytelling, brand voice adaptation, grammar and style',
                'personality_traits' => 'Creative, articulate, adaptable, expressive',
                'intelligence_level' => 7,
                'efficiency_rating' => 8.0,
                'reliability_score' => 7.0
            ],
            'Editing Agent' => [
                'description' => 'Refines and improves content quality.',
                'capabilities' => 'Editing, proofreading, style improvement, clarity enhancement',
                'personality_traits' => 'Meticulous, critical, precise, improvement-focused',
                'intelligence_level' => 7,
                'efficiency_rating' => 8.5,
                'reliability_score' => 8.5
            ],
            'SEO Optimization Agent' => [
                'description' => 'Ensures content ranks well in search engines.',
                'capabilities' => 'Keyword research, SEO optimization, meta description creation, performance tracking',
                'personality_traits' => 'Analytical, technical, data-driven, detail-oriented',
                'intelligence_level' => 8,
                'efficiency_rating' => 7.5,
                'reliability_score' => 8.0
            ]
        ];
        
        foreach ($requiredAgents as $agentName => $agentData) {
            // Check if agent already exists
            $existingAgent = $this->findAgentByName($agentName);
            
            if (!$existingAgent) {
                // Create the agent
                $this->createAgent($agentName, $agentData);
            }
        }
    }
    
    /**
     * Find an agent by name
     *
     * @param string $name The name of the agent
     * @return array|null The agent data or null if not found
     */
    protected function findAgentByName($name) {
        $sql = "SELECT * FROM ai_agents WHERE user_id = ? AND name = ?";
        return $this->agentModel->db->fetchOne($sql, [$this->userId, $name]);
    }
    
    /**
     * Create a new agent
     *
     * @param string $name The name of the agent
     * @param array $data Additional agent data
     * @return int|bool The ID of the new agent or false on failure
     */
    protected function createAgent($name, $data) {
        $agentData = [
            'user_id' => $this->userId,
            'name' => $name,
            'description' => $data['description'],
            'capabilities' => $data['capabilities'],
            'status' => 'active',
            'personality_traits' => $data['personality_traits'],
            'intelligence_level' => $data['intelligence_level'],
            'efficiency_rating' => $data['efficiency_rating'],
            'reliability_score' => $data['reliability_score'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->agentModel->createAgent($agentData);
    }
    
    /**
     * Ensure agents are assigned to roles
     */
    protected function ensureAgentsAssigned() {
        $roles = ['Research Agent', 'Content Planning Agent', 'Writing Agent', 'Editing Agent', 'SEO Optimization Agent'];
        
        foreach ($roles as $role) {
            // Check if role is already assigned
            $assignments = $this->assignmentModel->getAgentsByRole($this->projectId, $role);
            
            if (empty($assignments)) {
                // Find the agent with this name
                $agent = $this->findAgentByName($role);
                
                if ($agent) {
                    // Assign the agent to the role
                    $this->assignmentModel->assignAgentToProject($this->projectId, $agent['id'], $role);
                }
            }
        }
    }
    
    /**
     * Generate content based on a topic
     *
     * @param string $topic The topic to generate content for
     * @param array $parameters Additional parameters for content generation
     * @return array The generated content and related data
     */
    public function generateContent($topic, $parameters = []) {
        // Prepare initial input for the workflow
        $initialInput = [
            'topic' => $topic,
            'parameters' => $parameters
        ];
        
        // Execute the workflow
        $result = $this->coordinator->executeWorkflow($initialInput);
        
        // Return the final content
        return [
            'topic' => $topic,
            'research_data' => $result['research_data'] ?? null,
            'content_plan' => $result['content_plan'] ?? null,
            'draft_content' => $result['draft_content'] ?? null,
            'edited_content' => $result['edited_content'] ?? null,
            'final_content' => $result['final_content'] ?? null,
            'generated_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Get the status of the brigade
     *
     * @return array Status information
     */
    public function getStatus() {
        // Get project details
        $project = $this->projectModel->getProjectDetails($this->projectId, $this->userId);
        
        // Get assigned agents
        $agents = $this->assignmentModel->getProjectAgents($this->projectId);
        
        // Get tasks
        $tasks = $this->taskModel->getProjectTasks($this->projectId);
        
        // Calculate progress
        $totalTasks = count($tasks);
        $completedTasks = 0;
        
        foreach ($tasks as $task) {
            if ($task['status'] === 'completed') {
                $completedTasks++;
            }
        }
        
        $progress = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100) : 0;
        
        // Update project progress
        $this->projectModel->update($this->projectId, [
            'progress' => $progress,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        return [
            'project' => $project,
            'agents' => $agents,
            'tasks' => $tasks,
            'progress' => $progress,
            'status' => $project['status']
        ];
    }
}
