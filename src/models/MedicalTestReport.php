<?php
/**
 * Medical Test Report Model
 *
 * Handles medical test report data
 */

require_once __DIR__ . '/BaseModel.php';

class MedicalTestReport extends BaseModel {
    protected $table = 'medical_test_reports';
    protected $parametersTable = 'medical_test_parameters';
    protected $commonParametersTable = 'common_test_parameters';

    /**
     * Get all reports for a user
     */
    public function getUserReports($userId, $limit = null, $offset = null) {
        $limitClause = $limit ? " LIMIT {$limit}" : "";
        $offsetClause = $offset ? " OFFSET {$offset}" : "";

        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                ORDER BY report_date DESC, created_at DESC
                {$limitClause}{$offsetClause}";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get reports by type for a user
     */
    public function getUserReportsByType($userId, $reportType, $limit = null, $offset = null) {
        $limitClause = $limit ? " LIMIT {$limit}" : "";
        $offsetClause = $offset ? " OFFSET {$offset}" : "";

        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND report_type = ?
                ORDER BY report_date DESC, created_at DESC
                {$limitClause}{$offsetClause}";

        return $this->db->fetchAll($sql, [$userId, $reportType]);
    }

    /**
     * Get report by ID
     */
    public function getReport($id) {
        return $this->find($id);
    }

    /**
     * Create a new report
     */
    public function createReport($data) {
        return $this->create($data);
    }

    /**
     * Update a report
     */
    public function updateReport($id, $data) {
        return $this->update($id, $data);
    }

    /**
     * Delete a report
     */
    public function deleteReport($id) {
        return $this->delete($id);
    }

    /**
     * Get parameters for a report
     */
    public function getReportParameters($reportId) {
        $sql = "SELECT * FROM {$this->parametersTable}
                WHERE report_id = ?
                ORDER BY parameter_name ASC";

        return $this->db->fetchAll($sql, [$reportId]);
    }

    /**
     * Add a parameter to a report
     */
    public function addParameter($data) {
        return $this->db->insert($this->parametersTable, $data);
    }

    /**
     * Update a parameter
     */
    public function updateParameter($id, $data) {
        return $this->db->update(
            $this->parametersTable,
            $data,
            "id = ?",
            [$id]
        );
    }

    /**
     * Delete a parameter
     */
    public function deleteParameter($id) {
        return $this->db->delete(
            $this->parametersTable,
            "id = ?",
            [$id]
        );
    }

    /**
     * Get common parameters for a test type
     */
    public function getCommonParameters($testType) {
        $sql = "SELECT * FROM {$this->commonParametersTable}
                WHERE test_type = ?
                ORDER BY category ASC, display_name ASC";

        return $this->db->fetchAll($sql, [$testType]);
    }

    /**
     * Get common parameters grouped by category
     */
    public function getCommonParametersByCategory($testType) {
        $parameters = $this->getCommonParameters($testType);

        // If no parameters found, return empty array
        if (empty($parameters)) {
            return [];
        }

        $grouped = [];

        foreach ($parameters as $parameter) {
            $category = $parameter['category'];
            if (!isset($grouped[$category])) {
                $grouped[$category] = [];
            }
            $grouped[$category][] = $parameter;
        }

        return $grouped;
    }

    /**
     * Get parameter history for a user
     */
    public function getParameterHistory($userId, $parameterName, $limit = 10) {
        $sql = "SELECT p.*, r.report_date, r.report_type, r.report_title
                FROM {$this->parametersTable} p
                JOIN {$this->table} r ON p.report_id = r.id
                WHERE r.user_id = ? AND p.parameter_name = ?
                ORDER BY r.report_date DESC, r.created_at DESC
                LIMIT ?";

        return $this->db->fetchAll($sql, [$userId, $parameterName, $limit]);
    }

    /**
     * Get abnormal parameters for a user's recent reports
     */
    public function getRecentAbnormalParameters($userId, $days = 90) {
        $sql = "SELECT p.*, r.report_date, r.report_type, r.report_title
                FROM {$this->parametersTable} p
                JOIN {$this->table} r ON p.report_id = r.id
                WHERE r.user_id = ? AND p.is_abnormal = 1
                AND r.report_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY r.report_date DESC, r.created_at DESC";

        return $this->db->fetchAll($sql, [$userId, $days]);
    }

    /**
     * Count reports by type for a user
     */
    public function countReportsByType($userId) {
        $sql = "SELECT report_type, COUNT(*) as count
                FROM {$this->table}
                WHERE user_id = ?
                GROUP BY report_type";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get reports for a date range
     */
    public function getReportsForDateRange($userId, $startDate, $endDate) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                AND report_date BETWEEN ? AND ?
                ORDER BY report_date DESC, created_at DESC";

        return $this->db->fetchAll($sql, [$userId, $startDate, $endDate]);
    }

    /**
     * Get distinct scan types for a user
     */
    public function getDistinctScanTypes($userId) {
        $sql = "SELECT DISTINCT scan_type FROM {$this->table}
                WHERE user_id = ? AND report_type = 'scan'
                AND scan_type IS NOT NULL AND scan_type != ''
                ORDER BY scan_type ASC";

        $results = $this->db->fetchAll($sql, [$userId]);

        // If no results, return default scan types
        if (empty($results)) {
            return [
                ['scan_type' => 'xray'],
                ['scan_type' => 'ct'],
                ['scan_type' => 'mri'],
                ['scan_type' => 'ultrasound'],
                ['scan_type' => 'other']
            ];
        }

        return $results;
    }
}
