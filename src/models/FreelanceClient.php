<?php
/**
 * Freelance Client Model
 *
 * Handles freelance client-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class FreelanceClient extends BaseModel {
    protected $table = 'freelance_clients';

    /**
     * Get clients for a specific user
     *
     * @param int $userId User ID
     * @param array $filters Optional filters
     * @return array Clients
     */
    public function getUserClients($userId, $filters = []) {
        $sql = "SELECT c.*,
                COUNT(DISTINCT p.id) as total_projects,
                COUNT(DISTINCT CASE WHEN p.status = 'in_progress' THEN p.id END) as active_projects,
                SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END) as total_paid,
                SUM(CASE WHEN i.status IN ('sent', 'overdue') THEN i.total_amount ELSE 0 END) as total_outstanding
                FROM {$this->table} c
                LEFT JOIN freelance_projects p ON c.id = p.client_id
                LEFT JOIN freelance_invoices i ON c.id = i.client_id
                WHERE c.user_id = ? ";

        $params = [$userId];

        // Apply filters
        if (!empty($filters['status'])) {
            $sql .= " AND c.status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (c.name LIKE ? OR c.company LIKE ? OR c.email LIKE ?)";
            $searchTerm = "%{$filters['search']}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        $sql .= " GROUP BY c.id ORDER BY c.name ASC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get client details with project statistics
     *
     * @param int $clientId Client ID
     * @param int $userId User ID
     * @return array|bool Client details or false if not found
     */
    public function getClientDetails($clientId, $userId) {
        $sql = "SELECT c.*,
                COUNT(DISTINCT p.id) as total_projects,
                COUNT(DISTINCT CASE WHEN p.status = 'in_progress' THEN p.id END) as active_projects,
                COUNT(DISTINCT CASE WHEN p.status = 'completed' THEN p.id END) as completed_projects,
                SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END) as total_paid,
                SUM(CASE WHEN i.status IN ('sent', 'overdue') THEN i.total_amount ELSE 0 END) as total_outstanding
                FROM {$this->table} c
                LEFT JOIN freelance_projects p ON c.id = p.client_id
                LEFT JOIN freelance_invoices i ON c.id = i.client_id
                WHERE c.id = ? AND c.user_id = ?
                GROUP BY c.id";

        return $this->db->fetchOne($sql, [$clientId, $userId]);
    }

    /**
     * Get active clients for a user
     *
     * @param int $userId User ID
     * @param int $limit Optional limit
     * @return array Active clients
     */
    public function getActiveClients($userId, $limit = null) {
        $sql = "SELECT c.*,
                COUNT(DISTINCT p.id) as active_projects
                FROM {$this->table} c
                LEFT JOIN freelance_projects p ON c.id = p.client_id AND p.status = 'in_progress'
                WHERE c.user_id = ? AND c.status = 'active'
                GROUP BY c.id
                ORDER BY active_projects DESC, c.name ASC";
        
        if ($limit) {
            $sql .= " LIMIT " . (int)$limit;
        }
        
        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get client summary statistics
     *
     * @param int $userId User ID
     * @return array Client summary
     */
    public function getClientSummary($userId) {
        $sql = "SELECT 
                COUNT(DISTINCT id) as total_clients,
                COUNT(DISTINCT CASE WHEN status = 'active' THEN id END) as active_clients,
                COUNT(DISTINCT CASE WHEN status = 'potential' THEN id END) as potential_clients
                FROM {$this->table}
                WHERE user_id = ?";
        
        $result = $this->db->fetchOne($sql, [$userId]);
        return $result ?: [
            'total_clients' => 0,
            'active_clients' => 0,
            'potential_clients' => 0
        ];
    }
}
