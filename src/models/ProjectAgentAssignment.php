<?php
/**
 * Project Agent Assignment Model
 *
 * Handles the assignment of AI agents to projects
 */

require_once __DIR__ . '/BaseModel.php';

class ProjectAgentAssignment extends BaseModel {
    protected $table = 'project_agent_assignments';

    /**
     * Get all agent assignments for a project
     *
     * @param int $projectId Project ID
     * @return array Array of agent assignments
     */
    public function getProjectAgents($projectId) {
        $sql = "SELECT pa.*, a.name as agent_name, a.avatar as agent_avatar, 
                       a.intelligence_level, a.efficiency_rating, a.reliability_score
                FROM {$this->table} pa
                JOIN ai_agents a ON pa.agent_id = a.id
                WHERE pa.project_id = ?
                ORDER BY pa.role ASC, a.name ASC";

        return $this->db->fetchAll($sql, [$projectId]);
    }

    /**
     * Get all project assignments for an agent
     *
     * @param int $agentId Agent ID
     * @return array Array of project assignments
     */
    public function getAgentProjects($agentId) {
        $sql = "SELECT pa.*, p.name as project_name, p.description as project_description,
                       p.status as project_status, p.progress as project_progress,
                       p.brigade_type
                FROM {$this->table} pa
                JOIN projects p ON pa.project_id = p.id
                WHERE pa.agent_id = ?
                ORDER BY p.name ASC";

        return $this->db->fetchAll($sql, [$agentId]);
    }

    /**
     * Assign an agent to a project
     *
     * @param int $projectId Project ID
     * @param int $agentId Agent ID
     * @param string $role Role of the agent in the project
     * @return int|false Assignment ID or false on failure
     */
    public function assignAgentToProject($projectId, $agentId, $role = null) {
        // Check if assignment already exists
        $sql = "SELECT id FROM {$this->table} 
                WHERE project_id = ? AND agent_id = ? AND (role = ? OR (role IS NULL AND ? IS NULL))";
        
        $existing = $this->db->fetchOne($sql, [$projectId, $agentId, $role, $role]);
        
        if ($existing) {
            return $existing['id']; // Assignment already exists
        }
        
        // Create new assignment
        $data = [
            'project_id' => $projectId,
            'agent_id' => $agentId,
            'role' => $role,
            'assignment_date' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->create($data);
    }

    /**
     * Remove an agent from a project
     *
     * @param int $projectId Project ID
     * @param int $agentId Agent ID
     * @param string $role Optional role to remove specific role assignment
     * @return bool Success or failure
     */
    public function removeAgentFromProject($projectId, $agentId, $role = null) {
        $sql = "DELETE FROM {$this->table} WHERE project_id = ? AND agent_id = ?";
        $params = [$projectId, $agentId];
        
        if ($role !== null) {
            $sql .= " AND role = ?";
            $params[] = $role;
        }
        
        return $this->db->query($sql, $params);
    }

    /**
     * Get agents by role for a project
     *
     * @param int $projectId Project ID
     * @param string $role Role to filter by
     * @return array Array of agents with the specified role
     */
    public function getAgentsByRole($projectId, $role) {
        $sql = "SELECT pa.*, a.name as agent_name, a.avatar as agent_avatar, 
                       a.intelligence_level, a.efficiency_rating, a.reliability_score
                FROM {$this->table} pa
                JOIN ai_agents a ON pa.agent_id = a.id
                WHERE pa.project_id = ? AND pa.role = ?
                ORDER BY a.name ASC";

        return $this->db->fetchAll($sql, [$projectId, $role]);
    }

    /**
     * Get all roles assigned in a project
     *
     * @param int $projectId Project ID
     * @return array Array of unique roles in the project
     */
    public function getProjectRoles($projectId) {
        $sql = "SELECT DISTINCT role FROM {$this->table} 
                WHERE project_id = ? AND role IS NOT NULL
                ORDER BY role ASC";

        $results = $this->db->fetchAll($sql, [$projectId]);
        return array_column($results, 'role');
    }
}
