<?php
/**
 * Enhanced Note Model
 *
 * Handles note-related database operations with speed optimizations and ADHD-friendly features.
 */

require_once __DIR__ . '/BaseModel.php';

class Note extends BaseModel {
    protected $table = 'notes';

    // Cache for frequently accessed data
    private static $categoryCache = [];
    private static $tagCache = [];
    private static $statsCache = [];

    /**
     * Get notes for a specific user with optimized queries and ADHD-friendly sorting
     */
    public function getUserNotes($userId, $filters = [], $limit = null, $offset = 0) {
        // Build optimized query with indexes
        $sql = "SELECT id, title, content, category, tags, is_pinned, is_favorite,
                       priority_level, created_at, updated_at, last_accessed,
                       CASE
                           WHEN is_pinned = 1 THEN 1
                           WHEN is_favorite = 1 THEN 2
                           WHEN priority_level = 'high' THEN 3
                           WHEN priority_level = 'medium' THEN 4
                           ELSE 5
                       END as sort_priority
                FROM {$this->table} WHERE user_id = ?";
        $params = [$userId];

        // Apply filters with optimized conditions
        if (!empty($filters)) {
            // Filter by category (exact match for speed)
            if (isset($filters['category']) && !empty($filters['category'])) {
                $sql .= " AND category = ?";
                $params[] = $filters['category'];
            }

            // Filter by tag (optimized LIKE with index)
            if (isset($filters['tag']) && !empty($filters['tag'])) {
                $sql .= " AND tags LIKE ?";
                $params[] = "%{$filters['tag']}%";
            }

            // Filter by priority (ADHD-friendly)
            if (isset($filters['priority']) && !empty($filters['priority'])) {
                $sql .= " AND priority_level = ?";
                $params[] = $filters['priority'];
            }

            // Filter by date range
            if (isset($filters['date_from']) && !empty($filters['date_from'])) {
                $sql .= " AND DATE(updated_at) >= ?";
                $params[] = $filters['date_from'];
            }

            if (isset($filters['date_to']) && !empty($filters['date_to'])) {
                $sql .= " AND DATE(updated_at) <= ?";
                $params[] = $filters['date_to'];
            }

            // Filter favorites only
            if (isset($filters['favorites_only']) && $filters['favorites_only']) {
                $sql .= " AND is_favorite = 1";
            }
        }

        // ADHD-friendly sorting: pinned first, then favorites, then by priority and recency
        $sql .= " ORDER BY sort_priority ASC, updated_at DESC";

        // Add pagination for performance
        if ($limit) {
            $sql .= " LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
        }

        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get note categories for a specific user with caching
     */
    public function getUserNoteCategories($userId) {
        // Check cache first for speed
        $cacheKey = "categories_user_{$userId}";
        if (isset(self::$categoryCache[$cacheKey])) {
            return self::$categoryCache[$cacheKey];
        }

        $sql = "SELECT DISTINCT category, COUNT(*) as note_count
                FROM {$this->table}
                WHERE user_id = ? AND category IS NOT NULL AND category != ''
                GROUP BY category
                ORDER BY note_count DESC, category ASC";

        $result = $this->db->fetchAll($sql, [$userId]);

        // Cache the result
        self::$categoryCache[$cacheKey] = $result;

        return $result;
    }

    /**
     * Enhanced search with full-text capabilities and ADHD-friendly features
     */
    public function searchNotes($userId, $searchTerm, $filters = []) {
        $sql = "SELECT *,
                       CASE
                           WHEN title LIKE ? THEN 1
                           WHEN content LIKE ? THEN 2
                           WHEN tags LIKE ? THEN 3
                           ELSE 4
                       END as relevance_score,
                       CASE
                           WHEN is_pinned = 1 THEN 1
                           WHEN is_favorite = 1 THEN 2
                           WHEN priority_level = 'high' THEN 3
                           ELSE 4
                       END as priority_score
                FROM {$this->table}
                WHERE user_id = ? AND (
                    title LIKE ? OR
                    content LIKE ? OR
                    tags LIKE ? OR
                    category LIKE ?
                )";

        $searchParam = "%{$searchTerm}%";
        $params = [
            $searchParam, $searchParam, $searchParam, // For relevance scoring
            $userId, $searchParam, $searchParam, $searchParam, $searchParam
        ];

        // Apply additional filters
        if (!empty($filters['category'])) {
            $sql .= " AND category = ?";
            $params[] = $filters['category'];
        }

        if (!empty($filters['priority'])) {
            $sql .= " AND priority_level = ?";
            $params[] = $filters['priority'];
        }

        // ADHD-friendly sorting: relevance + priority
        $sql .= " ORDER BY priority_score ASC, relevance_score ASC, updated_at DESC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get popular tags with usage count (ADHD-friendly autocomplete)
     */
    public function getPopularTags($userId, $limit = 20) {
        $cacheKey = "tags_user_{$userId}";
        if (isset(self::$tagCache[$cacheKey])) {
            return self::$tagCache[$cacheKey];
        }

        $sql = "SELECT tags FROM {$this->table}
                WHERE user_id = ? AND tags IS NOT NULL AND tags != ''";

        $notes = $this->db->fetchAll($sql, [$userId]);
        $tagCounts = [];

        foreach ($notes as $note) {
            $tags = explode(',', $note['tags']);
            foreach ($tags as $tag) {
                $tag = trim($tag);
                if (!empty($tag)) {
                    $tagCounts[$tag] = ($tagCounts[$tag] ?? 0) + 1;
                }
            }
        }

        // Sort by usage count
        arsort($tagCounts);
        $result = array_slice($tagCounts, 0, $limit, true);

        // Cache the result
        self::$tagCache[$cacheKey] = $result;

        return $result;
    }

    /**
     * Auto-save functionality for ADHD users
     */
    public function autoSave($userId, $noteId, $data) {
        $autoSaveData = [
            'title' => $data['title'] ?? '',
            'content' => $data['content'] ?? '',
            'category' => $data['category'] ?? null,
            'tags' => $data['tags'] ?? null,
            'updated_at' => date('Y-m-d H:i:s'),
            'auto_saved' => 1
        ];

        if ($noteId && $noteId !== 'new') {
            // Update existing note
            $note = $this->find($noteId);
            if ($note && $note['user_id'] == $userId) {
                return $this->update($noteId, $autoSaveData);
            }
        } else {
            // Create new auto-saved note
            $autoSaveData['user_id'] = $userId;
            $autoSaveData['created_at'] = date('Y-m-d H:i:s');
            return $this->create($autoSaveData);
        }

        return false;
    }

    /**
     * Update last accessed time for analytics
     */
    public function updateLastAccessed($noteId, $userId) {
        $note = $this->find($noteId);
        if ($note && $note['user_id'] == $userId) {
            return $this->update($noteId, ['last_accessed' => date('Y-m-d H:i:s')]);
        }
        return false;
    }

    /**
     * Get note statistics for ADHD insights with caching
     */
    public function getNoteStats($userId) {
        // Check cache first for speed
        $cacheKey = "stats_user_{$userId}";
        if (isset(self::$statsCache[$cacheKey])) {
            return self::$statsCache[$cacheKey];
        }

        $sql = "SELECT
                    COUNT(*) as total_notes,
                    COUNT(CASE WHEN is_pinned = 1 THEN 1 END) as pinned_notes,
                    COUNT(CASE WHEN is_favorite = 1 THEN 1 END) as favorite_notes,
                    COUNT(CASE WHEN priority_level = 'high' THEN 1 END) as high_priority,
                    COUNT(CASE WHEN priority_level = 'medium' THEN 1 END) as medium_priority,
                    COUNT(CASE WHEN priority_level = 'low' THEN 1 END) as low_priority,
                    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_notes,
                    COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_notes
                FROM {$this->table}
                WHERE user_id = ?";

        $result = $this->db->fetchOne($sql, [$userId]);

        // Cache the result
        if ($result) {
            self::$statsCache[$cacheKey] = $result;
        }

        return $result;
    }

    /**
     * Clear cache when data changes
     */
    public function clearCache($userId) {
        unset(self::$categoryCache["categories_user_{$userId}"]);
        unset(self::$tagCache["tags_user_{$userId}"]);
        unset(self::$statsCache["stats_user_{$userId}"]);
    }

    /**
     * Update last accessed time for analytics
     */
    public function updateLastAccessed($noteId, $userId) {
        $sql = "UPDATE {$this->table}
                SET last_accessed = NOW()
                WHERE id = ? AND user_id = ?";

        return $this->db->query($sql, [$noteId, $userId]);
    }

    /**
     * Override create to clear cache
     */
    public function create($data) {
        $result = parent::create($data);
        if ($result && isset($data['user_id'])) {
            $this->clearCache($data['user_id']);
        }
        return $result;
    }

    /**
     * Override update to clear cache
     */
    public function update($id, $data) {
        $note = $this->find($id);
        $result = parent::update($id, $data);
        if ($result && $note) {
            $this->clearCache($note['user_id']);
        }
        return $result;
    }

    /**
     * Override delete to clear cache
     */
    public function delete($id) {
        $note = $this->find($id);
        $result = parent::delete($id);
        if ($result && $note) {
            $this->clearCache($note['user_id']);
        }
        return $result;
    }
}
