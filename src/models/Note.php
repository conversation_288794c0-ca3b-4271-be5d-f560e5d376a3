<?php
/**
 * Note Model
 * 
 * Handles note-related database operations for the information hub.
 */

require_once __DIR__ . '/BaseModel.php';

class Note extends BaseModel {
    protected $table = 'notes';
    
    /**
     * Get notes for a specific user
     */
    public function getUserNotes($userId, $filters = []) {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ?";
        $params = [$userId];
        
        // Apply filters
        if (!empty($filters)) {
            // Filter by category
            if (isset($filters['category'])) {
                $sql .= " AND category = ?";
                $params[] = $filters['category'];
            }
            
            // Filter by tag
            if (isset($filters['tag'])) {
                $sql .= " AND tags LIKE ?";
                $params[] = "%{$filters['tag']}%";
            }
        }
        
        // Order by last update, newest first
        $sql .= " ORDER BY updated_at DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get note categories for a specific user
     */
    public function getUserNoteCategories($userId) {
        $sql = "SELECT DISTINCT category FROM {$this->table} 
                WHERE user_id = ? AND category IS NOT NULL AND category != ''
                ORDER BY category ASC";
        
        return $this->db->fetchAll($sql, [$userId]);
    }
    
    /**
     * Search notes by content
     */
    public function searchNotes($userId, $searchTerm) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE user_id = ? AND (title LIKE ? OR content LIKE ?)
                ORDER BY updated_at DESC";
        
        $searchParam = "%{$searchTerm}%";
        return $this->db->fetchAll($sql, [$userId, $searchParam, $searchParam]);
    }
}
