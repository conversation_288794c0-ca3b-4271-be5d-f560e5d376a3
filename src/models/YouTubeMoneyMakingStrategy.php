<?php
/**
 * YouTube Money Making Strategy Model
 * 
 * Handles the creation, retrieval, and management of YouTube money making strategies
 */

require_once __DIR__ . '/BaseModel.php';

class YouTubeMoneyMakingStrategy extends BaseModel {
    /**
     * Create a new money making strategy
     * 
     * @param array $data Strategy data
     * @return int|false The ID of the created strategy or false on failure
     */
    public function create($data) {
        try {
            $sql = "INSERT INTO youtube_money_making_strategies (
                analysis_id, 
                strategy_name, 
                description, 
                potential_revenue, 
                time_investment, 
                required_skills, 
                required_resources, 
                implementation_difficulty, 
                suitable_brigade, 
                created_at, 
                updated_at
            ) VALUES (
                :analysis_id, 
                :strategy_name, 
                :description, 
                :potential_revenue, 
                :time_investment, 
                :required_skills, 
                :required_resources, 
                :implementation_difficulty, 
                :suitable_brigade, 
                :created_at, 
                :updated_at
            )";
            
            $params = [
                ':analysis_id' => $data['analysis_id'],
                ':strategy_name' => $data['strategy_name'],
                ':description' => $data['description'],
                ':potential_revenue' => $data['potential_revenue'] ?? null,
                ':time_investment' => $data['time_investment'] ?? null,
                ':required_skills' => $data['required_skills'] ?? null,
                ':required_resources' => $data['required_resources'] ?? null,
                ':implementation_difficulty' => $data['implementation_difficulty'] ?? null,
                ':suitable_brigade' => $data['suitable_brigade'] ?? null,
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ];
            
            return $this->db->insert($sql, $params);
        } catch (Exception $e) {
            error_log("Error creating money making strategy: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get a strategy by ID
     * 
     * @param int $strategyId Strategy ID
     * @return array|false Strategy data or false if not found
     */
    public function getStrategy($strategyId) {
        try {
            $sql = "SELECT * FROM youtube_money_making_strategies WHERE id = :id";
            return $this->db->fetchOne($sql, [':id' => $strategyId]);
        } catch (Exception $e) {
            error_log("Error getting money making strategy: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get strategies by analysis ID
     * 
     * @param int $analysisId Analysis ID
     * @return array Array of strategies
     */
    public function getStrategiesByAnalysis($analysisId) {
        try {
            $sql = "SELECT * FROM youtube_money_making_strategies WHERE analysis_id = :analysis_id ORDER BY id ASC";
            return $this->db->fetchAll($sql, [':analysis_id' => $analysisId]);
        } catch (Exception $e) {
            error_log("Error getting strategies by analysis: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update a strategy
     * 
     * @param int $strategyId Strategy ID
     * @param array $data Strategy data to update
     * @return bool Success or failure
     */
    public function update($strategyId, $data) {
        try {
            $updateFields = [];
            $params = [':id' => $strategyId];
            
            $allowedFields = [
                'strategy_name', 'description', 'potential_revenue', 'time_investment',
                'required_skills', 'required_resources', 'implementation_difficulty', 'suitable_brigade'
            ];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "{$field} = :{$field}";
                    $params[":{$field}"] = $data[$field];
                }
            }
            
            if (empty($updateFields)) {
                return false;
            }
            
            $updateFields[] = "updated_at = :updated_at";
            $params[':updated_at'] = date('Y-m-d H:i:s');
            
            $sql = "UPDATE youtube_money_making_strategies SET " . implode(', ', $updateFields) . " WHERE id = :id";
            
            return $this->db->execute($sql, $params);
        } catch (Exception $e) {
            error_log("Error updating money making strategy: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a strategy
     * 
     * @param int $id Strategy ID
     * @return bool Success or failure
     */
    public function delete($id) {
        try {
            $sql = "DELETE FROM youtube_money_making_strategies WHERE id = :id";
            return $this->db->execute($sql, [':id' => $id]);
        } catch (Exception $e) {
            error_log("Error deleting money making strategy: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete all strategies for an analysis
     * 
     * @param int $analysisId Analysis ID
     * @return bool Success or failure
     */
    public function deleteAllStrategies($analysisId) {
        try {
            $sql = "DELETE FROM youtube_money_making_strategies WHERE analysis_id = :analysis_id";
            return $this->db->execute($sql, [':analysis_id' => $analysisId]);
        } catch (Exception $e) {
            error_log("Error deleting all money making strategies: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get strategies by brigade type
     * 
     * @param string $brigadeType Brigade type
     * @param int $userId User ID
     * @param int $limit Optional limit
     * @param int $offset Optional offset
     * @return array Array of strategies
     */
    public function getStrategiesByBrigade($brigadeType, $userId, $limit = null, $offset = null) {
        try {
            $sql = "SELECT s.*, a.user_id, v.title as video_title, v.youtube_id, v.thumbnail_url 
                    FROM youtube_money_making_strategies s 
                    JOIN youtube_analysis a ON s.analysis_id = a.id 
                    JOIN youtube_videos v ON a.video_id = v.id 
                    WHERE s.suitable_brigade = :brigade_type AND a.user_id = :user_id 
                    ORDER BY s.created_at DESC";
            
            $params = [':brigade_type' => $brigadeType, ':user_id' => $userId];
            
            if ($limit !== null) {
                $sql .= " LIMIT :limit";
                $params[':limit'] = $limit;
                
                if ($offset !== null) {
                    $sql .= " OFFSET :offset";
                    $params[':offset'] = $offset;
                }
            }
            
            return $this->db->fetchAll($sql, $params);
        } catch (Exception $e) {
            error_log("Error getting strategies by brigade: " . $e->getMessage());
            return [];
        }
    }
}
