<?php
/**
 * Freelance Project Model
 *
 * Handles freelance project-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class FreelanceProject extends BaseModel {
    protected $table = 'freelance_projects';
    protected $milestonesTable = 'freelance_project_milestones';
    protected $timeEntriesTable = 'freelance_time_entries';
    protected $invoicesTable = 'freelance_invoices';
    protected $paymentsTable = 'freelance_payments';

    /**
     * Get projects for a specific user
     *
     * @param int $userId User ID
     * @param array $filters Optional filters
     * @return array Projects
     */
    public function getUserProjects($userId, $filters = []) {
        $sql = "SELECT p.*,
                c.name as client_name,
                c.company as client_company,
                COUNT(DISTINCT m.id) as total_milestones,
                COUNT(DISTINCT CASE WHEN m.status = 'completed' THEN m.id END) as completed_milestones,
                SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END) as total_paid,
                SUM(CASE WHEN i.status IN ('sent', 'overdue') THEN i.total_amount ELSE 0 END) as total_outstanding
                FROM {$this->table} p
                LEFT JOIN freelance_clients c ON p.client_id = c.id
                LEFT JOIN {$this->milestonesTable} m ON p.id = m.project_id
                LEFT JOIN {$this->invoicesTable} i ON p.id = i.project_id
                WHERE p.user_id = ? ";

        $params = [$userId];

        // Apply filters
        if (!empty($filters['status'])) {
            $sql .= " AND p.status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['client_id'])) {
            $sql .= " AND p.client_id = ?";
            $params[] = $filters['client_id'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (p.name LIKE ? OR c.name LIKE ? OR c.company LIKE ?)";
            $searchTerm = "%{$filters['search']}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        $sql .= " GROUP BY p.id ORDER BY 
                  CASE 
                    WHEN p.status = 'in_progress' THEN 1
                    WHEN p.status = 'proposal' THEN 2
                    WHEN p.status = 'negotiation' THEN 3
                    WHEN p.status = 'review' THEN 4
                    WHEN p.status = 'completed' THEN 5
                    ELSE 6
                  END,
                  p.deadline ASC,
                  p.created_at DESC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get project details with statistics
     *
     * @param int $projectId Project ID
     * @param int $userId User ID
     * @return array|bool Project details or false if not found
     */
    public function getProjectDetails($projectId, $userId) {
        $sql = "SELECT p.*,
                c.name as client_name,
                c.company as client_company,
                c.email as client_email,
                c.phone as client_phone,
                COUNT(DISTINCT m.id) as total_milestones,
                COUNT(DISTINCT CASE WHEN m.status = 'completed' THEN m.id END) as completed_milestones,
                SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END) as total_paid,
                SUM(CASE WHEN i.status IN ('sent', 'overdue') THEN i.total_amount ELSE 0 END) as total_outstanding,
                SUM(CASE WHEN te.billable = 1 THEN te.duration ELSE 0 END) / 60 as total_billable_hours
                FROM {$this->table} p
                LEFT JOIN freelance_clients c ON p.client_id = c.id
                LEFT JOIN {$this->milestonesTable} m ON p.id = m.project_id
                LEFT JOIN {$this->invoicesTable} i ON p.id = i.project_id
                LEFT JOIN {$this->timeEntriesTable} te ON p.id = te.project_id
                WHERE p.id = ? AND p.user_id = ?
                GROUP BY p.id";

        return $this->db->fetchOne($sql, [$projectId, $userId]);
    }

    /**
     * Get active projects for a user
     *
     * @param int $userId User ID
     * @param int $limit Optional limit
     * @return array Active projects
     */
    public function getActiveProjects($userId, $limit = null) {
        $sql = "SELECT p.*,
                c.name as client_name,
                c.company as client_company,
                DATEDIFF(p.deadline, CURDATE()) as days_remaining
                FROM {$this->table} p
                JOIN freelance_clients c ON p.client_id = c.id
                WHERE p.user_id = ? AND p.status = 'in_progress'
                ORDER BY 
                    CASE 
                        WHEN p.deadline < CURDATE() THEN 0
                        ELSE 1
                    END,
                    p.deadline ASC";
        
        if ($limit) {
            $sql .= " LIMIT " . (int)$limit;
        }
        
        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get project milestones
     *
     * @param int $projectId Project ID
     * @return array Milestones
     */
    public function getProjectMilestones($projectId) {
        $sql = "SELECT * FROM {$this->milestonesTable} 
                WHERE project_id = ? 
                ORDER BY due_date ASC, created_at ASC";
        
        return $this->db->fetchAll($sql, [$projectId]);
    }

    /**
     * Get project summary statistics
     *
     * @param int $userId User ID
     * @return array Project summary
     */
    public function getProjectSummary($userId) {
        $sql = "SELECT 
                COUNT(DISTINCT id) as total_projects,
                COUNT(DISTINCT CASE WHEN status = 'in_progress' THEN id END) as active_projects,
                COUNT(DISTINCT CASE WHEN status = 'proposal' OR status = 'negotiation' THEN id END) as pending_projects,
                COUNT(DISTINCT CASE WHEN status = 'completed' THEN id END) as completed_projects,
                COUNT(DISTINCT CASE WHEN deadline < CURDATE() AND status != 'completed' AND status != 'cancelled' THEN id END) as overdue_projects
                FROM {$this->table}
                WHERE user_id = ?";
        
        $result = $this->db->fetchOne($sql, [$userId]);
        return $result ?: [
            'total_projects' => 0,
            'active_projects' => 0,
            'pending_projects' => 0,
            'completed_projects' => 0,
            'overdue_projects' => 0
        ];
    }
}
