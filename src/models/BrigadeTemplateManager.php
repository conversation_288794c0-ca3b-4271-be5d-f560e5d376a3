<?php
/**
 * Brigade Template Manager
 *
 * Manages brigade templates for the AI Agent Army
 */

require_once __DIR__ . '/BaseModel.php';
require_once __DIR__ . '/Project.php';
require_once __DIR__ . '/Task.php';
require_once __DIR__ . '/AIAgent.php';
require_once __DIR__ . '/ProjectAgentAssignment.php';

class BrigadeTemplateManager extends BaseModel {
    protected $projectModel;
    protected $taskModel;
    protected $agentModel;
    protected $assignmentModel;
    protected $templateDir;

    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct();
        $this->projectModel = new Project();
        $this->taskModel = new Task();
        $this->agentModel = new AIAgent();
        $this->assignmentModel = new ProjectAgentAssignment();
        $this->templateDir = __DIR__ . '/../templates/brigades/';
    }

    /**
     * Get all available brigade templates
     *
     * @return array Array of brigade templates
     */
    public function getAllBrigadeTemplates() {
        $templates = [];
        $brigadeTypes = ['content_creation', 'lead_generation', 'customer_support', 'data_analysis'];

        foreach ($brigadeTypes as $type) {
            $template = $this->getBrigadeTemplate($type);
            if ($template) {
                $templates[$type] = $template;
            }
        }

        return $templates;
    }

    /**
     * Get a specific brigade template
     *
     * @param string $brigadeType Brigade type
     * @return array|null Template data or null if not found
     */
    public function getBrigadeTemplate($brigadeType) {
        $templateFile = $this->templateDir . $brigadeType . '_brigade_template.php';
        
        if (file_exists($templateFile)) {
            return require $templateFile;
        }
        
        return null;
    }

    /**
     * Create a project from a brigade template
     *
     * @param int $userId User ID
     * @param string $brigadeType Brigade type
     * @param string $projectName Project name
     * @param string $projectDescription Project description
     * @param string $deadline Project deadline (YYYY-MM-DD)
     * @return int|false Project ID or false on failure
     */
    public function createProjectFromTemplate($userId, $brigadeType, $projectName, $projectDescription, $deadline) {
        try {
            // Get the template
            $template = $this->getBrigadeTemplate($brigadeType);
            
            if (!$template) {
                error_log("Brigade template not found for type: {$brigadeType}");
                return false;
            }
            
            // Create the project
            $projectData = [
                'user_id' => $userId,
                'name' => $projectName,
                'description' => $projectDescription,
                'start_date' => date('Y-m-d'),
                'end_date' => $deadline,
                'status' => 'planning',
                'progress' => 0,
                'is_template' => false,
                'brigade_type' => $brigadeType,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $projectId = $this->projectModel->create($projectData);
            
            if (!$projectId) {
                error_log("Failed to create project");
                return false;
            }
            
            // Create tasks from template
            $this->createTasksFromTemplate($projectId, $template);
            
            return $projectId;
        } catch (Exception $e) {
            error_log("Exception in createProjectFromTemplate: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Create tasks from a brigade template
     *
     * @param int $projectId Project ID
     * @param array $template Template data
     * @return bool Success or failure
     */
    private function createTasksFromTemplate($projectId, $template) {
        try {
            $taskMap = []; // Map task names to IDs for dependencies
            
            // First pass: Create all tasks
            foreach ($template['tasks'] as $taskData) {
                $task = [
                    'project_id' => $projectId,
                    'name' => $taskData['name'],
                    'description' => $taskData['description'],
                    'status' => 'pending',
                    'priority' => $taskData['priority'],
                    'assigned_role' => $taskData['assigned_role'],
                    'estimated_time' => $taskData['duration'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                $taskId = $this->taskModel->create($task);
                
                if ($taskId) {
                    $taskMap[$taskData['name']] = $taskId;
                }
            }
            
            // Second pass: Set up dependencies
            foreach ($template['tasks'] as $taskData) {
                if (!empty($taskData['dependencies'])) {
                    $taskId = $taskMap[$taskData['name']] ?? null;
                    
                    if ($taskId) {
                        foreach ($taskData['dependencies'] as $dependencyName) {
                            $dependencyId = $taskMap[$dependencyName] ?? null;
                            
                            if ($dependencyId) {
                                $this->taskModel->addDependency($taskId, $dependencyId);
                            }
                        }
                    }
                }
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Exception in createTasksFromTemplate: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Assign agents to brigade roles
     *
     * @param int $projectId Project ID
     * @param string $brigadeType Brigade type
     * @param int $userId User ID
     * @param array $roleAssignments Optional role assignments [role => agent_id]
     * @return bool Success or failure
     */
    public function assignAgentsToBrigadeRoles($projectId, $brigadeType, $userId, $roleAssignments = []) {
        try {
            // Get the template
            $template = $this->getBrigadeTemplate($brigadeType);
            
            if (!$template) {
                error_log("Brigade template not found for type: {$brigadeType}");
                return false;
            }
            
            // Get user's agents
            $userAgents = $this->agentModel->getUserAgents($userId);
            $agentsBySkill = [];
            
            // Organize agents by skills
            foreach ($userAgents as $agent) {
                $agentSkills = $this->agentModel->getAgentSkills($agent['id']);
                
                foreach ($agentSkills as $skill) {
                    $skillName = $skill['name'];
                    if (!isset($agentsBySkill[$skillName])) {
                        $agentsBySkill[$skillName] = [];
                    }
                    $agentsBySkill[$skillName][] = $agent;
                }
            }
            
            // Assign agents to roles
            foreach ($template['roles'] as $roleKey => $roleData) {
                // Check if role is already assigned
                if (isset($roleAssignments[$roleKey])) {
                    $agentId = $roleAssignments[$roleKey];
                    $this->assignmentModel->assignAgentToProject($projectId, $agentId, $roleData['name']);
                    continue;
                }
                
                // If role has a default agent, try to find it
                if ($roleData['default_agent']) {
                    foreach ($userAgents as $agent) {
                        if ($agent['name'] === $roleData['default_agent']) {
                            $this->assignmentModel->assignAgentToProject($projectId, $agent['id'], $roleData['name']);
                            continue 2; // Continue with the next role
                        }
                    }
                }
                
                // Try to find an agent with the required skills
                $bestAgent = null;
                $bestMatchCount = 0;
                
                foreach ($userAgents as $agent) {
                    $matchCount = 0;
                    $agentSkills = $this->agentModel->getAgentSkills($agent['id']);
                    $agentSkillNames = array_column($agentSkills, 'name');
                    
                    foreach ($roleData['required_skills'] as $requiredSkill) {
                        if (in_array($requiredSkill, $agentSkillNames)) {
                            $matchCount++;
                        }
                    }
                    
                    if ($matchCount > $bestMatchCount) {
                        $bestMatchCount = $matchCount;
                        $bestAgent = $agent;
                    }
                }
                
                // Assign the best matching agent if found
                if ($bestAgent) {
                    $this->assignmentModel->assignAgentToProject($projectId, $bestAgent['id'], $roleData['name']);
                }
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Exception in assignAgentsToBrigadeRoles: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return false;
        }
    }
}
