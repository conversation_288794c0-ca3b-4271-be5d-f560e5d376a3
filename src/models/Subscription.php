<?php
/**
 * Subscription Model
 *
 * Handles subscription-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class Subscription extends BaseModel {
    protected $table = 'subscriptions';

    /**
     * Get subscriptions for a specific user
     */
    public function getUserSubscriptions($userId) {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ? ORDER BY next_billing_date ASC";
        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Parse features list from JSON to array
     *
     * @param string|null $featuresJson JSON string of features
     * @return array Parsed features array
     */
    public function parseFeaturesList($featuresJson) {
        if (empty($featuresJson)) {
            return [];
        }

        $features = json_decode($featuresJson, true);
        return is_array($features) ? $features : [];
    }

    /**
     * Get upcoming subscriptions due within the next N days
     */
    public function getUpcomingSubscriptions($userId, $days = 7) {
        $today = date('Y-m-d');
        $futureDate = date('Y-m-d', strtotime("+{$days} days"));

        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND next_billing_date BETWEEN ? AND ?
                ORDER BY next_billing_date ASC";

        return $this->db->fetchAll($sql, [$userId, $today, $futureDate]);
    }

    /**
     * Get total monthly subscription cost
     */
    public function getTotalMonthlyCost($userId) {
        $sql = "SELECT
                    COALESCE(SUM(CASE
                        WHEN billing_cycle = 'monthly' THEN amount
                        WHEN billing_cycle = 'yearly' THEN amount / 12
                        WHEN billing_cycle = 'quarterly' THEN amount / 3
                        ELSE amount
                    END), 0) as monthly_cost
                FROM {$this->table}
                WHERE user_id = ? AND status = 'active'";

        $result = $this->db->fetchOne($sql, [$userId]);
        return (float)($result['monthly_cost'] ?? 0);
    }

    /**
     * Update the next billing date based on the billing cycle
     */
    public function updateNextBillingDate($subscriptionId) {
        $subscription = $this->find($subscriptionId);

        if (!$subscription) {
            return false;
        }

        $currentDate = $subscription['next_billing_date'];
        $newDate = null;

        switch ($subscription['billing_cycle']) {
            case 'monthly':
                $newDate = date('Y-m-d', strtotime($currentDate . ' +1 month'));
                break;
            case 'quarterly':
                $newDate = date('Y-m-d', strtotime($currentDate . ' +3 months'));
                break;
            case 'yearly':
                $newDate = date('Y-m-d', strtotime($currentDate . ' +1 year'));
                break;
            default:
                return false;
        }

        return $this->update($subscriptionId, [
            'next_billing_date' => $newDate,
            'last_billing_date' => $currentDate,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
}


