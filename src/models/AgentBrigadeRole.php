<?php
/**
 * Agent Brigade Role Model
 *
 * Handles the specialized roles within AI agent brigades
 */

require_once __DIR__ . '/BaseModel.php';

class AgentBrigadeRole extends BaseModel {
    protected $table = 'agent_brigade_roles';

    /**
     * Get all roles for a specific brigade type
     *
     * @param string $brigadeType Brigade type identifier
     * @return array Array of roles for the brigade type
     */
    public function getBrigadeRoles($brigadeType) {
        $sql = "SELECT * FROM {$this->table}
                WHERE brigade_type = ?
                ORDER BY name ASC";

        return $this->db->fetchAll($sql, [$brigadeType]);
    }

    /**
     * Get all available brigade types
     *
     * @return array Array of unique brigade types
     */
    public function getBrigadeTypes() {
        $sql = "SELECT DISTINCT brigade_type FROM {$this->table}
                ORDER BY brigade_type ASC";

        $results = $this->db->fetchAll($sql);
        return array_column($results, 'brigade_type');
    }

    /**
     * Get a specific role by ID
     *
     * @param int $id Role ID
     * @return array|false Role data or false if not found
     */
    public function getRole($id) {
        return $this->find($id);
    }

    /**
     * Get a specific role by name and brigade type
     *
     * @param string $name Role name
     * @param string $brigadeType Brigade type
     * @return array|false Role data or false if not found
     */
    public function getRoleByName($name, $brigadeType) {
        $sql = "SELECT * FROM {$this->table}
                WHERE name = ? AND brigade_type = ?
                LIMIT 1";

        return $this->db->fetchOne($sql, [$name, $brigadeType]);
    }

    /**
     * Create a new brigade role
     *
     * @param array $data Role data
     * @return int|false New role ID or false on failure
     */
    public function createRole($data) {
        // Ensure created_at and updated_at are set
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->create($data);
    }

    /**
     * Update an existing brigade role
     *
     * @param int $id Role ID
     * @param array $data Updated role data
     * @return bool Success or failure
     */
    public function updateRole($id, $data) {
        // Ensure updated_at is set
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->update($id, $data);
    }

    /**
     * Get formatted brigade type name
     *
     * @param string $brigadeType Brigade type identifier
     * @return string Formatted brigade type name
     */
    public function formatBrigadeType($brigadeType) {
        $types = [
            'content_creation' => 'Content Creation Brigade',
            'lead_generation' => 'Lead Generation Brigade',
            'customer_support' => 'Customer Support Brigade',
            'data_analysis' => 'Data Analysis Brigade'
        ];

        return $types[$brigadeType] ?? ucwords(str_replace('_', ' ', $brigadeType)) . ' Brigade';
    }

    /**
     * Get brigade type description
     *
     * @param string $brigadeType Brigade type identifier
     * @return string Brigade type description
     */
    public function getBrigadeDescription($brigadeType) {
        $descriptions = [
            'content_creation' => 'Generate high-quality, SEO-optimized content at scale for businesses',
            'lead_generation' => 'Identify and engage potential clients through personalized outreach',
            'customer_support' => 'Provide 24/7 automated customer support across multiple channels',
            'data_analysis' => 'Transform raw data into actionable business insights'
        ];

        return $descriptions[$brigadeType] ?? '';
    }

    /**
     * Create default brigade roles for all brigade types
     *
     * @return bool Success or failure
     */
    public function createDefaultRoles() {
        $defaultRoles = [
            // Content Creation Brigade
            [
                'brigade_type' => 'content_creation',
                'name' => 'Research Agent',
                'description' => 'Gathers information and identifies trending topics',
                'required_skills' => 'research,data_analysis,trend_identification',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'content_creation',
                'name' => 'Content Planning Agent',
                'description' => 'Creates content outlines and strategies',
                'required_skills' => 'planning,organization,content_strategy',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'content_creation',
                'name' => 'Writing Agent',
                'description' => 'Generates actual content based on outlines',
                'required_skills' => 'writing,creativity,communication',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'content_creation',
                'name' => 'Editing Agent',
                'description' => 'Refines and improves content quality',
                'required_skills' => 'editing,quality_control,attention_to_detail',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'content_creation',
                'name' => 'SEO Optimization Agent',
                'description' => 'Ensures content ranks well in search engines',
                'required_skills' => 'seo,keyword_research,analytics',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],

            // Lead Generation Brigade
            [
                'brigade_type' => 'lead_generation',
                'name' => 'Prospect Identification Agent',
                'description' => 'Finds potential clients matching criteria',
                'required_skills' => 'research,data_mining,pattern_recognition',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'lead_generation',
                'name' => 'Research Agent',
                'description' => 'Gathers detailed information about prospects',
                'required_skills' => 'research,data_analysis,information_gathering',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'lead_generation',
                'name' => 'Personalization Agent',
                'description' => 'Creates customized outreach messages',
                'required_skills' => 'personalization,writing,empathy',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'lead_generation',
                'name' => 'Engagement Agent',
                'description' => 'Manages follow-up sequences',
                'required_skills' => 'communication,persistence,timing',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'lead_generation',
                'name' => 'Analytics Agent',
                'description' => 'Tracks campaign performance and optimizes strategies',
                'required_skills' => 'analytics,data_analysis,optimization',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],

            // Customer Support Brigade
            [
                'brigade_type' => 'customer_support',
                'name' => 'Triage Agent',
                'description' => 'Categorizes and prioritizes incoming queries',
                'required_skills' => 'categorization,prioritization,decision_making',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'customer_support',
                'name' => 'Knowledge Agent',
                'description' => 'Retrieves relevant information from knowledge bases',
                'required_skills' => 'information_retrieval,knowledge_management,research',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'customer_support',
                'name' => 'Response Agent',
                'description' => 'Generates personalized, helpful responses',
                'required_skills' => 'communication,writing,empathy',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'customer_support',
                'name' => 'Escalation Agent',
                'description' => 'Identifies when human intervention is needed',
                'required_skills' => 'judgment,problem_identification,decision_making',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'customer_support',
                'name' => 'Analytics Agent',
                'description' => 'Tracks performance and identifies improvement opportunities',
                'required_skills' => 'analytics,pattern_recognition,optimization',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],

            // Data Analysis Brigade
            [
                'brigade_type' => 'data_analysis',
                'name' => 'Data Collection Agent',
                'description' => 'Gathers and organizes data from various sources',
                'required_skills' => 'data_collection,organization,automation',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'data_analysis',
                'name' => 'Processing Agent',
                'description' => 'Cleans, normalizes, and prepares data for analysis',
                'required_skills' => 'data_cleaning,normalization,preparation',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'data_analysis',
                'name' => 'Analysis Agent',
                'description' => 'Identifies patterns, trends, and insights',
                'required_skills' => 'data_analysis,pattern_recognition,statistical_analysis',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'data_analysis',
                'name' => 'Visualization Agent',
                'description' => 'Creates clear, compelling data visualizations',
                'required_skills' => 'data_visualization,design,communication',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'brigade_type' => 'data_analysis',
                'name' => 'Recommendation Agent',
                'description' => 'Generates actionable recommendations',
                'required_skills' => 'insight_generation,strategic_thinking,communication',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        $success = true;

        foreach ($defaultRoles as $role) {
            // Check if role already exists
            $sql = "SELECT id FROM {$this->table} WHERE brigade_type = ? AND name = ?";
            $existing = $this->db->fetchOne($sql, [$role['brigade_type'], $role['name']]);

            if (!$existing) {
                $result = $this->create($role);
                if (!$result) {
                    $success = false;
                }
            }
        }

        return $success;
    }
}
