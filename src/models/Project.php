<?php
/**
 * Project Model
 *
 * Handles project-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class Project extends BaseModel {
    protected $table = 'projects';

    /**
     * Get projects for a specific user
     *
     * @param int $userId User ID
     * @param array $filters Optional filters
     * @return array Array of projects
     */
    public function getUserProjects($userId, $filters = []) {
        $sql = "SELECT p.*,
                COUNT(DISTINCT t.id) as total_tasks,
                COUNT(DISTINCT CASE WHEN t.status = 'done' THEN t.id END) as completed_tasks,
                (SELECT COUNT(*) FROM project_agent_assignments paa WHERE paa.project_id = p.id) as assigned_agents
                FROM {$this->table} p
                LEFT JOIN tasks t ON p.id = t.project_id
                WHERE p.user_id = ? ";

        $params = [$userId];

        // Apply filters
        if (!empty($filters)) {
            // Filter by status
            if (isset($filters['status']) && !empty($filters['status'])) {
                if (is_array($filters['status'])) {
                    $placeholders = implode(',', array_fill(0, count($filters['status']), '?'));
                    $sql .= " AND p.status IN ($placeholders) ";
                    foreach ($filters['status'] as $status) {
                        $params[] = $status;
                    }
                } else {
                    $sql .= " AND p.status = ? ";
                    $params[] = $filters['status'];
                }
            }

            // Filter by name (search)
            if (isset($filters['search']) && !empty($filters['search'])) {
                $sql .= " AND (p.name LIKE ? OR p.description LIKE ?) ";
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            // Filter by brigade type
            if (isset($filters['brigade_type']) && !empty($filters['brigade_type'])) {
                if (is_array($filters['brigade_type'])) {
                    $placeholders = implode(',', array_fill(0, count($filters['brigade_type']), '?'));
                    $sql .= " AND p.brigade_type IN ($placeholders) ";
                    foreach ($filters['brigade_type'] as $brigadeType) {
                        $params[] = $brigadeType;
                    }
                } else {
                    $sql .= " AND p.brigade_type = ? ";
                    $params[] = $filters['brigade_type'];
                }
            }

            // Filter by brigade template status
            if (isset($filters['is_brigade_template'])) {
                $sql .= " AND p.is_brigade_template = ? ";
                $params[] = $filters['is_brigade_template'] ? 1 : 0;
            }

            // Filter by date range
            if (isset($filters['start_date']) && !empty($filters['start_date'])) {
                $sql .= " AND p.start_date >= ? ";
                $params[] = $filters['start_date'];
            }

            if (isset($filters['end_date']) && !empty($filters['end_date'])) {
                $sql .= " AND p.end_date <= ? ";
                $params[] = $filters['end_date'];
            }

            // Filter by template status
            if (isset($filters['is_template'])) {
                $sql .= " AND p.is_template = ? ";
                $params[] = $filters['is_template'] ? 1 : 0;
            }
        }

        // Group by project and order by creation date (newest first)
        $sql .= " GROUP BY p.id ORDER BY p.created_at DESC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get project details with task statistics
     */
    public function getProjectDetails($projectId, $userId) {
        $sql = "SELECT p.*,
                COUNT(DISTINCT t.id) as total_tasks,
                COUNT(DISTINCT CASE WHEN t.status = 'done' THEN t.id END) as completed_tasks,
                COUNT(DISTINCT CASE WHEN t.status = 'in_progress' THEN t.id END) as in_progress_tasks,
                COUNT(DISTINCT CASE WHEN t.status = 'todo' THEN t.id END) as todo_tasks
                FROM {$this->table} p
                LEFT JOIN tasks t ON p.id = t.project_id
                WHERE p.id = ? AND p.user_id = ?
                GROUP BY p.id";

        return $this->db->fetchOne($sql, [$projectId, $userId]);
    }

    /**
     * Get project templates
     *
     * @param int $userId User ID
     * @return array Array of project templates
     */
    public function getProjectTemplates($userId) {
        return $this->getUserProjects($userId, ['is_template' => true]);
    }

    /**
     * Get brigade templates
     *
     * @param int $userId User ID
     * @param string $brigadeType Optional brigade type to filter by
     * @return array Array of brigade templates
     */
    public function getBrigadeTemplates($userId, $brigadeType = null) {
        $filters = ['is_brigade_template' => true];

        if ($brigadeType) {
            $filters['brigade_type'] = $brigadeType;
        }

        return $this->getUserProjects($userId, $filters);
    }

    /**
     * Create project from template
     */
    public function createFromTemplate($templateId, $userId, $projectData) {
        // Get template
        $template = $this->find($templateId);

        // Verify template exists and belongs to user
        if (!$template || $template['user_id'] != $userId || !$template['is_template']) {
            return false;
        }

        // Start transaction
        $this->db->beginTransaction();

        try {
            // Create new project based on template
            $newProjectData = [
                'user_id' => $userId,
                'name' => $projectData['name'] ?? $template['name'] . ' (Copy)',
                'description' => $projectData['description'] ?? $template['description'],
                'start_date' => $projectData['start_date'] ?? date('Y-m-d'),
                'end_date' => $projectData['end_date'] ?? null,
                'status' => 'planning',
                'progress' => 0,
                'is_template' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $newProjectId = $this->create($newProjectData);

            if (!$newProjectId) {
                throw new Exception("Failed to create project");
            }

            // Get tasks from template
            require_once __DIR__ . '/Task.php';
            $taskModel = new Task();
            $templateTasks = $taskModel->getProjectTasks($templateId);

            // Create tasks for new project
            $taskMap = []; // Map old task IDs to new task IDs

            foreach ($templateTasks as $task) {
                $newTaskData = [
                    'user_id' => $userId,
                    'project_id' => $newProjectId,
                    'title' => $task['title'],
                    'description' => $task['description'],
                    'status' => 'todo',
                    'priority' => $task['priority'],
                    'category_id' => $task['category_id'],
                    'estimated_time' => $task['estimated_time'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                // Store old task ID and new task ID mapping
                $oldTaskId = $task['id'];
                $newTaskId = $taskModel->create($newTaskData);

                if (!$newTaskId) {
                    throw new Exception("Failed to create task");
                }

                $taskMap[$oldTaskId] = $newTaskId;
            }

            // Create task dependencies
            require_once __DIR__ . '/TaskDependency.php';
            $dependencyModel = new TaskDependency();
            $templateDependencies = $dependencyModel->getProjectDependencies($templateId);

            foreach ($templateDependencies as $dependency) {
                if (isset($taskMap[$dependency['task_id']]) && isset($taskMap[$dependency['depends_on_task_id']])) {
                    $newDependencyData = [
                        'task_id' => $taskMap[$dependency['task_id']],
                        'depends_on_task_id' => $taskMap[$dependency['depends_on_task_id']],
                        'dependency_type' => $dependency['dependency_type'],
                        'lag_time' => $dependency['lag_time'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    $dependencyModel->create($newDependencyData);
                }
            }

            // Commit transaction
            $this->db->commit();

            return $newProjectId;
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->rollback();
            error_log("Error creating project from template: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create project from brigade template
     *
     * @param int $templateId Brigade template ID
     * @param int $userId User ID
     * @param array $projectData Custom project data
     * @return int|false New project ID or false on failure
     */
    public function createFromBrigadeTemplate($templateId, $userId, $projectData = []) {
        // Get the template
        $template = $this->getProjectDetails($templateId, $userId);

        if (!$template || !$template['is_brigade_template']) {
            return false;
        }

        $this->db->beginTransaction();

        try {
            // Create new project based on brigade template
            $newProjectData = [
                'user_id' => $userId,
                'name' => $projectData['name'] ?? $template['name'] . ' (Implementation)',
                'description' => $projectData['description'] ?? $template['description'],
                'start_date' => $projectData['start_date'] ?? date('Y-m-d'),
                'end_date' => $projectData['end_date'] ?? null,
                'status' => 'planning',
                'progress' => 0,
                'is_template' => false,
                'is_brigade_template' => false,
                'brigade_type' => $template['brigade_type'],
                'parent_brigade_id' => $templateId,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $newProjectId = $this->create($newProjectData);

            if (!$newProjectId) {
                throw new Exception("Failed to create project");
            }

            // Copy tasks from template
            require_once __DIR__ . '/Task.php';
            $taskModel = new Task();
            $templateTasks = $taskModel->getProjectTasks($templateId);

            $taskMap = []; // Map of old task ID to new task ID

            foreach ($templateTasks as $task) {
                $newTaskData = [
                    'project_id' => $newProjectId,
                    'user_id' => $userId,
                    'title' => $task['title'],
                    'description' => $task['description'],
                    'status' => 'todo',
                    'priority' => $task['priority'],
                    'due_date' => $task['due_date'] ? date('Y-m-d', strtotime($task['due_date'] . ' +' . (strtotime($projectData['start_date'] ?? date('Y-m-d')) - strtotime($template['start_date'])) / 86400 . ' days')) : null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $newTaskId = $taskModel->createTask($newTaskData);
                $taskMap[$task['id']] = $newTaskId;
            }

            // Update task dependencies
            foreach ($templateTasks as $task) {
                $dependencies = $taskModel->getTaskDependencies($task['id']);

                foreach ($dependencies as $dependency) {
                    if (isset($taskMap[$dependency['dependency_id']])) {
                        $taskModel->addDependency($taskMap[$task['id']], $taskMap[$dependency['dependency_id']]);
                    }
                }
            }

            // Copy agent assignments if requested
            if (!empty($projectData['copy_agents']) && $projectData['copy_agents']) {
                require_once __DIR__ . '/ProjectAgentAssignment.php';
                $assignmentModel = new ProjectAgentAssignment();
                $templateAssignments = $assignmentModel->getProjectAgents($templateId);

                foreach ($templateAssignments as $assignment) {
                    $assignmentModel->assignAgentToProject($newProjectId, $assignment['agent_id'], $assignment['role']);
                }
            }

            $this->db->commit();
            return $newProjectId;

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Error creating project from brigade template: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update project progress based on task completion
     */
    public function updateProgress($projectId) {
        // Get project tasks
        require_once __DIR__ . '/Task.php';
        $taskModel = new Task();
        $tasks = $taskModel->getProjectTasks($projectId);

        $totalTasks = count($tasks);
        $completedTasks = 0;

        foreach ($tasks as $task) {
            if ($task['status'] === 'done') {
                $completedTasks++;
            }
        }

        // Calculate progress percentage
        $progress = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100) : 0;

        // Update project progress
        return $this->update($projectId, [
            'progress' => $progress,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Get project members
     */
    public function getProjectMembers($projectId) {
        $sql = "SELECT pm.id, pm.project_id, pm.user_id, pm.role, pm.created_at as joined_at,
                u.name, u.email
                FROM project_members pm
                JOIN users u ON pm.user_id = u.id
                WHERE pm.project_id = ?
                ORDER BY
                    CASE
                        WHEN pm.role = 'owner' THEN 1
                        WHEN pm.role = 'admin' THEN 2
                        ELSE 3
                    END,
                    pm.created_at ASC";

        return $this->db->fetchAll($sql, [$projectId]);
    }

    /**
     * Get project comments
     */
    public function getProjectComments($projectId) {
        $sql = "SELECT pc.id, pc.project_id, pc.user_id, pc.content, pc.created_at, pc.updated_at,
                u.name as user_name, u.email as user_email
                FROM project_comments pc
                JOIN users u ON pc.user_id = u.id
                WHERE pc.project_id = ?
                ORDER BY pc.created_at DESC";

        return $this->db->fetchAll($sql, [$projectId]);
    }
}
