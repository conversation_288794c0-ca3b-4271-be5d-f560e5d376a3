<?php
/**
 * Freelance Invoice Model
 *
 * Handles freelance invoice-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class FreelanceInvoice extends BaseModel {
    protected $table = 'freelance_invoices';
    protected $itemsTable = 'freelance_invoice_items';
    protected $paymentsTable = 'freelance_payments';

    /**
     * Get invoices for a specific user
     *
     * @param int $userId User ID
     * @param array $filters Optional filters
     * @return array Invoices
     */
    public function getUserInvoices($userId, $filters = []) {
        $sql = "SELECT i.*,
                c.name as client_name,
                c.company as client_company,
                p.name as project_name,
                SUM(CASE WHEN pay.id IS NOT NULL THEN pay.amount ELSE 0 END) as amount_paid,
                (i.total_amount - SUM(CASE WHEN pay.id IS NOT NULL THEN pay.amount ELSE 0 END)) as amount_due
                FROM {$this->table} i
                LEFT JOIN freelance_clients c ON i.client_id = c.id
                LEFT JOIN freelance_projects p ON i.project_id = p.id
                LEFT JOIN {$this->paymentsTable} pay ON i.id = pay.invoice_id
                WHERE i.user_id = ? ";

        $params = [$userId];

        // Apply filters
        if (!empty($filters['status'])) {
            $sql .= " AND i.status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['client_id'])) {
            $sql .= " AND i.client_id = ?";
            $params[] = $filters['client_id'];
        }

        if (!empty($filters['project_id'])) {
            $sql .= " AND i.project_id = ?";
            $params[] = $filters['project_id'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (i.invoice_number LIKE ? OR c.name LIKE ? OR p.name LIKE ?)";
            $searchTerm = "%{$filters['search']}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        $sql .= " GROUP BY i.id ORDER BY
                  CASE
                    WHEN i.status = 'overdue' THEN 1
                    WHEN i.status = 'sent' THEN 2
                    WHEN i.status = 'draft' THEN 3
                    WHEN i.status = 'paid' THEN 4
                    ELSE 5
                  END,
                  i.due_date ASC,
                  i.created_at DESC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get invoice details with items and payments
     *
     * @param int $invoiceId Invoice ID
     * @param int $userId User ID
     * @return array|bool Invoice details or false if not found
     */
    public function getInvoiceDetails($invoiceId, $userId) {
        // Get invoice
        $sql = "SELECT i.*,
                c.name as client_name,
                c.company as client_company,
                c.email as client_email,
                c.address as client_address,
                p.name as project_name
                FROM {$this->table} i
                LEFT JOIN freelance_clients c ON i.client_id = c.id
                LEFT JOIN freelance_projects p ON i.project_id = p.id
                WHERE i.id = ? AND i.user_id = ?";

        $invoice = $this->db->fetchOne($sql, [$invoiceId, $userId]);

        if (!$invoice) {
            return false;
        }

        // Get invoice items
        $sql = "SELECT * FROM {$this->itemsTable} WHERE invoice_id = ? ORDER BY id ASC";
        $invoice['items'] = $this->db->fetchAll($sql, [$invoiceId]);

        // Get payments
        $sql = "SELECT * FROM {$this->paymentsTable} WHERE invoice_id = ? ORDER BY payment_date DESC";
        $invoice['payments'] = $this->db->fetchAll($sql, [$invoiceId]);

        // Calculate amount paid and due
        $amountPaid = 0;
        foreach ($invoice['payments'] as $payment) {
            $amountPaid += $payment['amount'];
        }
        $invoice['amount_paid'] = $amountPaid;
        $invoice['amount_due'] = $invoice['total_amount'] - $amountPaid;

        return $invoice;
    }

    /**
     * Create invoice with items
     *
     * @param array $invoiceData Invoice data
     * @param array $items Invoice items
     * @return int|bool The ID of the created invoice or false on failure
     */
    public function createWithItems($invoiceData, $items) {
        // Start transaction
        $this->db->beginTransaction();

        try {
            // Create invoice
            $invoiceId = $this->create($invoiceData);

            if (!$invoiceId) {
                throw new Exception("Failed to create invoice");
            }

            // Create invoice items
            foreach ($items as $item) {
                $item['invoice_id'] = $invoiceId;
                $item['created_at'] = date('Y-m-d H:i:s');
                $item['updated_at'] = date('Y-m-d H:i:s');

                $result = $this->db->insert($this->itemsTable, $item);
                if (!$result) {
                    throw new Exception("Failed to create invoice item");
                }
            }

            // Commit transaction
            $this->db->commit();
            return $invoiceId;
        } catch (Exception $e) {
            // Rollback transaction
            $this->db->rollback();
            error_log("Error creating invoice: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get invoice summary statistics
     *
     * @param int $userId User ID
     * @return array Invoice summary
     */
    public function getInvoiceSummary($userId) {
        $sql = "SELECT
                COUNT(DISTINCT id) as total_invoices,
                COUNT(DISTINCT CASE WHEN status = 'sent' THEN id END) as sent_invoices,
                COUNT(DISTINCT CASE WHEN status = 'overdue' THEN id END) as overdue_invoices,
                COUNT(DISTINCT CASE WHEN status = 'paid' THEN id END) as paid_invoices,
                SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as total_paid,
                SUM(CASE WHEN status IN ('sent', 'overdue') THEN total_amount ELSE 0 END) as total_outstanding
                FROM {$this->table}
                WHERE user_id = ?";

        $result = $this->db->fetchOne($sql, [$userId]);
        return $result ?: [
            'total_invoices' => 0,
            'sent_invoices' => 0,
            'overdue_invoices' => 0,
            'paid_invoices' => 0,
            'total_paid' => 0,
            'total_outstanding' => 0
        ];
    }

    /**
     * Generate next invoice number
     *
     * @param int $userId User ID
     * @return string Next invoice number
     */
    public function generateNextInvoiceNumber($userId) {
        // Get the highest invoice number for this user
        $sql = "SELECT invoice_number FROM {$this->table} WHERE user_id = ? ORDER BY id DESC LIMIT 1";
        $result = $this->db->fetchOne($sql, [$userId]);

        $prefix = 'INV-';
        $year = date('Y');
        $month = date('m');

        if (!$result) {
            // No invoices yet, start with 001
            return $prefix . $year . $month . '-001';
        }

        // Extract the numeric part of the invoice number
        $lastInvoiceNumber = $result['invoice_number'];

        // Check if the invoice number follows our format
        if (preg_match('/^' . $prefix . '\d{6}-(\d{3})$/', $lastInvoiceNumber, $matches)) {
            $number = (int)$matches[1] + 1;
            return $prefix . $year . $month . '-' . str_pad($number, 3, '0', STR_PAD_LEFT);
        }

        // If the format doesn't match, start with 001
        return $prefix . $year . $month . '-001';
    }
}
