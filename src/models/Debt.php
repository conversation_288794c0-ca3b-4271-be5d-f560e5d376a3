<?php
/**
 * Debt Model
 * 
 * Handles debt-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class Debt extends BaseModel {
    protected $table = 'debts';
    
    /**
     * Get debts for a specific user
     */
    public function getUserDebts($userId, $filters = []) {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ?";
        $params = [$userId];
        
        // Apply filters
        if (!empty($filters)) {
            // Filter by type (given/received)
            if (isset($filters['type']) && in_array($filters['type'], ['given', 'received'])) {
                $sql .= " AND type = ?";
                $params[] = $filters['type'];
            }
            
            // Filter by status
            if (isset($filters['status']) && in_array($filters['status'], ['unpaid', 'partially_paid', 'paid'])) {
                $sql .= " AND status = ?";
                $params[] = $filters['status'];
            }
            
            // Filter by person/entity
            if (isset($filters['person_entity']) && !empty($filters['person_entity'])) {
                $sql .= " AND person_entity LIKE ?";
                $params[] = '%' . $filters['person_entity'] . '%';
            }
            
            // Filter by due date range
            if (isset($filters['start_date']) && isset($filters['end_date'])) {
                $sql .= " AND due_date BETWEEN ? AND ?";
                $params[] = $filters['start_date'];
                $params[] = $filters['end_date'];
            }
        }
        
        // Order by due date, soonest first
        $sql .= " ORDER BY CASE WHEN status = 'paid' THEN 1 ELSE 0 END, due_date ASC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get debt summary for a user
     */
    public function getDebtSummary($userId) {
        $sql = "SELECT 
                    SUM(CASE WHEN type = 'given' AND (status = 'unpaid' OR status = 'partially_paid') THEN amount - amount_paid ELSE 0 END) as total_receivable,
                    SUM(CASE WHEN type = 'received' AND (status = 'unpaid' OR status = 'partially_paid') THEN amount - amount_paid ELSE 0 END) as total_payable,
                    COUNT(CASE WHEN type = 'given' AND (status = 'unpaid' OR status = 'partially_paid') THEN 1 END) as receivable_count,
                    COUNT(CASE WHEN type = 'received' AND (status = 'unpaid' OR status = 'partially_paid') THEN 1 END) as payable_count
                FROM {$this->table} 
                WHERE user_id = ?";
        
        $result = $this->db->fetchOne($sql, [$userId]);
        
        // Ensure we have numeric values
        return [
            'total_receivable' => (float)($result['total_receivable'] ?? 0),
            'total_payable' => (float)($result['total_payable'] ?? 0),
            'receivable_count' => (int)($result['receivable_count'] ?? 0),
            'payable_count' => (int)($result['payable_count'] ?? 0)
        ];
    }
    
    /**
     * Record a payment for a debt
     */
    public function recordPayment($debtId, $paymentAmount) {
        // Get the current debt
        $debt = $this->find($debtId);
        
        if (!$debt) {
            return false;
        }
        
        // Calculate new amount paid
        $newAmountPaid = $debt['amount_paid'] + $paymentAmount;
        
        // Determine new status
        $newStatus = 'partially_paid';
        if ($newAmountPaid >= $debt['amount']) {
            $newStatus = 'paid';
            $newAmountPaid = $debt['amount']; // Cap at total amount
        }
        
        // Update the debt
        return $this->update($debtId, [
            'amount_paid' => $newAmountPaid,
            'status' => $newStatus,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Get upcoming debts due within the next N days
     */
    public function getUpcomingDebts($userId, $days = 7) {
        $today = date('Y-m-d');
        $futureDate = date('Y-m-d', strtotime("+{$days} days"));
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE user_id = ? AND status != 'paid' AND due_date BETWEEN ? AND ? 
                ORDER BY due_date ASC";
        
        return $this->db->fetchAll($sql, [$userId, $today, $futureDate]);
    }
    
    /**
     * Get overdue debts
     */
    public function getOverdueDebts($userId) {
        $today = date('Y-m-d');
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE user_id = ? AND status != 'paid' AND due_date < ? 
                ORDER BY due_date ASC";
        
        return $this->db->fetchAll($sql, [$userId, $today]);
    }
}
