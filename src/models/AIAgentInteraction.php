<?php
/**
 * AI Agent Interaction Model
 *
 * Handles AI agent interaction data and operations
 */

require_once __DIR__ . '/BaseModel.php';

class AIAgentInteraction extends BaseModel {
    protected $table = 'ai_agent_interactions';

    /**
     * Get interactions for an agent
     */
    public function getAgentInteractions($agentId, $limit = null) {
        $sql = "SELECT i.*
                FROM {$this->table} i
                WHERE i.agent_id = ?
                ORDER BY i.created_at DESC";

        if ($limit) {
            $sql .= " LIMIT " . intval($limit);
        }

        return $this->db->fetchAll($sql, [$agentId]);
    }

    /**
     * Get recent interactions for a user
     */
    public function getRecentInteractions($userId, $limit = 10) {
        $sql = "SELECT i.*, a.name as agent_name, a.avatar as agent_avatar
                FROM {$this->table} i
                JOIN ai_agents a ON i.agent_id = a.id
                WHERE i.user_id = ?
                ORDER BY i.created_at DESC
                LIMIT ?";

        return $this->db->fetchAll($sql, [$userId, $limit]);
    }

    /**
     * Create a new interaction
     */
    public function createInteraction($data) {
        // Ensure created_at is set
        $data['created_at'] = date('Y-m-d H:i:s');

        return $this->create($data);
    }

    /**
     * Update an existing interaction
     *
     * @param int $id Interaction ID
     * @param array $data Updated interaction data
     * @return bool Success or failure
     */
    public function updateInteraction($id, $data) {
        // Ensure updated_at is set
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->update($id, $data);
    }

    /**
     * Get interaction statistics
     */
    public function getInteractionStats($userId) {
        $sql = "SELECT
                    COUNT(*) as total_interactions,
                    SUM(CASE WHEN interaction_type = 'command' THEN 1 ELSE 0 END) as command_count,
                    SUM(CASE WHEN interaction_type = 'query' THEN 1 ELSE 0 END) as query_count,
                    SUM(CASE WHEN interaction_type = 'feedback' THEN 1 ELSE 0 END) as feedback_count,
                    SUM(CASE WHEN interaction_type = 'training' THEN 1 ELSE 0 END) as training_count,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_interactions,
                    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_interactions
                FROM {$this->table} i
                JOIN ai_agents a ON i.agent_id = a.id
                WHERE i.user_id = ?";

        return $this->db->fetchOne($sql, [$userId]);
    }

    /**
     * Get interaction count by day
     */
    public function getInteractionCountByDay($userId, $days = 30) {
        $sql = "SELECT
                    DATE(i.created_at) as date,
                    COUNT(*) as count
                FROM {$this->table} i
                JOIN ai_agents a ON i.agent_id = a.id
                WHERE i.user_id = ?
                AND i.created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                GROUP BY DATE(i.created_at)
                ORDER BY DATE(i.created_at) ASC";

        return $this->db->fetchAll($sql, [$userId, $days]);
    }

    /**
     * Get interaction count by agent
     */
    public function getInteractionCountByAgent($userId) {
        $sql = "SELECT
                    a.id as agent_id,
                    a.name as agent_name,
                    COUNT(*) as count
                FROM {$this->table} i
                JOIN ai_agents a ON i.agent_id = a.id
                WHERE i.user_id = ?
                GROUP BY a.id, a.name
                ORDER BY COUNT(*) DESC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get interaction count by type
     */
    public function getInteractionCountByType($userId) {
        $sql = "SELECT
                    i.interaction_type,
                    COUNT(*) as count
                FROM {$this->table} i
                JOIN ai_agents a ON i.agent_id = a.id
                WHERE i.user_id = ?
                GROUP BY i.interaction_type
                ORDER BY COUNT(*) DESC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get interactions for a specific task
     */
    public function getTaskInteractions($taskId) {
        $sql = "SELECT i.*
                FROM {$this->table} i
                JOIN ai_agent_tasks t ON i.agent_id = t.agent_id
                WHERE t.id = ?
                AND (
                    i.content LIKE CONCAT('%Task assigned: ', t.title, '%') OR
                    i.content LIKE CONCAT('%Task status updated%') OR
                    i.content LIKE '%Task started%' OR
                    i.content LIKE '%Task completed%' OR
                    i.content LIKE CONCAT('%', t.title, '%')
                )
                ORDER BY i.created_at DESC";

        return $this->db->fetchAll($sql, [$taskId]);
    }
}
