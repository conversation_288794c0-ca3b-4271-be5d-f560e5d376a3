<?php
/**
 * Mockup Task Model
 * 
 * This model provides mockup data for tasks.
 */

namespace App\Models;

class MockupTask
{
    /**
     * Get a mockup current focus task for a user
     * 
     * @param int $userId The user ID
     * @return array The mockup task data
     */
    public static function getCurrentFocusTask($userId)
    {
        return [
            'id' => 1,
            'title' => 'Complete Financial Report',
            'description' => 'Finish the quarterly financial report for Q2 2023',
            'due_date' => date('Y-m-d', strtotime('+2 days')),
            'status' => 'in_progress',
            'priority' => 'high',
            'user_id' => $userId
        ];
    }
    
    /**
     * Get mockup ADHD tasks
     * 
     * @return array Array of mockup ADHD tasks
     */
    public static function getADHDTasks()
    {
        return [
            [
                'id' => 2,
                'title' => 'Complete Symptom Tracking',
                'description' => 'Log today\'s ADHD symptoms',
                'due_date' => date('Y-m-d'),
                'status' => 'pending',
                'priority' => 'medium'
            ],
            [
                'id' => 3,
                'title' => 'Mindfulness Session',
                'description' => '15-minute mindfulness practice',
                'due_date' => date('Y-m-d'),
                'status' => 'pending',
                'priority' => 'medium'
            ],
            [
                'id' => 4,
                'title' => 'Update Consistency Tracker',
                'description' => 'Log today\'s habits and routines',
                'due_date' => date('Y-m-d'),
                'status' => 'pending',
                'priority' => 'medium'
            ]
        ];
    }
    
    /**
     * Get mockup productivity tasks
     * 
     * @return array Array of mockup productivity tasks
     */
    public static function getProductivityTasks()
    {
        return [
            [
                'id' => 5,
                'title' => 'Pomodoro Session',
                'description' => '4 pomodoro sessions for project work',
                'due_date' => date('Y-m-d'),
                'status' => 'pending',
                'priority' => 'high'
            ],
            [
                'id' => 6,
                'title' => 'Focus Mode Session',
                'description' => '1 hour of deep work in focus mode',
                'due_date' => date('Y-m-d'),
                'status' => 'pending',
                'priority' => 'high'
            ]
        ];
    }
}
