<?php
/**
 * TaskDependency Model
 *
 * Handles task dependency-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class TaskDependency extends BaseModel {
    protected $table = 'task_dependencies';
    
    /**
     * Get dependencies for a specific task
     */
    public function getTaskDependencies($taskId) {
        $sql = "SELECT td.*, 
                t.title as task_title, t.status as task_status, t.due_date as task_due_date,
                dt.title as depends_on_title, dt.status as depends_on_status, dt.due_date as depends_on_due_date
                FROM {$this->table} td
                JOIN tasks t ON td.task_id = t.id
                JOIN tasks dt ON td.depends_on_task_id = dt.id
                WHERE td.task_id = ?";
        
        return $this->db->fetchAll($sql, [$taskId]);
    }
    
    /**
     * Get tasks that depend on a specific task
     */
    public function getDependentTasks($taskId) {
        $sql = "SELECT td.*, 
                t.title as task_title, t.status as task_status, t.due_date as task_due_date,
                dt.title as depends_on_title, dt.status as depends_on_status, dt.due_date as depends_on_due_date
                FROM {$this->table} td
                JOIN tasks t ON td.task_id = t.id
                JOIN tasks dt ON td.depends_on_task_id = dt.id
                WHERE td.depends_on_task_id = ?";
        
        return $this->db->fetchAll($sql, [$taskId]);
    }
    
    /**
     * Get all dependencies for a project
     */
    public function getProjectDependencies($projectId) {
        $sql = "SELECT td.*, 
                t.title as task_title, t.status as task_status, t.due_date as task_due_date,
                dt.title as depends_on_title, dt.status as depends_on_status, dt.due_date as depends_on_due_date
                FROM {$this->table} td
                JOIN tasks t ON td.task_id = t.id
                JOIN tasks dt ON td.depends_on_task_id = dt.id
                WHERE t.project_id = ?";
        
        return $this->db->fetchAll($sql, [$projectId]);
    }
    
    /**
     * Check if adding a dependency would create a circular reference
     */
    public function wouldCreateCircularDependency($taskId, $dependsOnTaskId) {
        // Direct circular dependency check
        if ($taskId == $dependsOnTaskId) {
            return true;
        }
        
        // Check if the dependency task already depends on the task (directly or indirectly)
        return $this->isTaskDependentOn($dependsOnTaskId, $taskId, []);
    }
    
    /**
     * Recursive function to check if a task depends on another task
     */
    private function isTaskDependentOn($taskId, $dependsOnTaskId, $visitedTasks) {
        // Prevent infinite recursion
        if (in_array($taskId, $visitedTasks)) {
            return false;
        }
        
        // Add current task to visited tasks
        $visitedTasks[] = $taskId;
        
        // Get all tasks that the current task depends on
        $sql = "SELECT depends_on_task_id FROM {$this->table} WHERE task_id = ?";
        $dependencies = $this->db->fetchAll($sql, [$taskId]);
        
        foreach ($dependencies as $dependency) {
            $dependsOn = $dependency['depends_on_task_id'];
            
            // If the task directly depends on the target task, we found a circular dependency
            if ($dependsOn == $dependsOnTaskId) {
                return true;
            }
            
            // Check if any of the dependencies depend on the target task
            if ($this->isTaskDependentOn($dependsOn, $dependsOnTaskId, $visitedTasks)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Update task dates based on dependencies
     */
    public function updateDependentTaskDates($taskId) {
        // Get tasks that depend on this task
        $dependentTasks = $this->getDependentTasks($taskId);
        
        if (empty($dependentTasks)) {
            return true;
        }
        
        require_once __DIR__ . '/Task.php';
        $taskModel = new Task();
        
        // Get the current task
        $currentTask = $taskModel->find($taskId);
        
        if (!$currentTask) {
            return false;
        }
        
        foreach ($dependentTasks as $dependency) {
            $dependentTaskId = $dependency['task_id'];
            $dependentTask = $taskModel->find($dependentTaskId);
            $dependencyType = $dependency['dependency_type'];
            $lagTime = $dependency['lag_time']; // in minutes
            
            if (!$dependentTask) {
                continue;
            }
            
            // Calculate new dates based on dependency type
            $newStartDate = null;
            $newDueDate = null;
            
            switch ($dependencyType) {
                case 'finish_to_start':
                    // Dependent task can't start until this task is finished
                    if ($currentTask['due_date']) {
                        $newStartDate = date('Y-m-d', strtotime($currentTask['due_date'] . " +{$lagTime} minutes"));
                    }
                    break;
                    
                case 'start_to_start':
                    // Dependent task can't start until this task starts
                    if ($currentTask['start_date']) {
                        $newStartDate = date('Y-m-d', strtotime($currentTask['start_date'] . " +{$lagTime} minutes"));
                    }
                    break;
                    
                case 'finish_to_finish':
                    // Dependent task can't finish until this task finishes
                    if ($currentTask['due_date']) {
                        $newDueDate = date('Y-m-d', strtotime($currentTask['due_date'] . " +{$lagTime} minutes"));
                    }
                    break;
                    
                case 'start_to_finish':
                    // Dependent task can't finish until this task starts
                    if ($currentTask['start_date']) {
                        $newDueDate = date('Y-m-d', strtotime($currentTask['start_date'] . " +{$lagTime} minutes"));
                    }
                    break;
            }
            
            // Update dependent task dates
            $updateData = [
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            if ($newStartDate) {
                $updateData['start_date'] = $newStartDate;
            }
            
            if ($newDueDate) {
                $updateData['due_date'] = $newDueDate;
            }
            
            $taskModel->update($dependentTaskId, $updateData);
            
            // Recursively update tasks that depend on this dependent task
            $this->updateDependentTaskDates($dependentTaskId);
        }
        
        return true;
    }
}
