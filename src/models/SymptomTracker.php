<?php
/**
 * Symptom Tracker Model
 * 
 * Handles ADHD symptom tracking data
 */

require_once __DIR__ . '/BaseModel.php';

class SymptomTracker extends BaseModel {
    protected $table = 'adhd_symptom_logs';
    
    /**
     * Get all symptom logs for a user
     */
    public function getUserLogs($userId, $days = 30) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY log_date DESC";
        
        return $this->db->fetchAll($sql, [$userId, $days]);
    }
    
    /**
     * Get symptom log by date
     */
    public function getLogByDate($userId, $date) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND log_date = ?
                LIMIT 1";
        
        return $this->db->fetchOne($sql, [$userId, $date]);
    }
    
    /**
     * Check if user has logged symptoms today
     */
    public function hasLoggedToday($userId) {
        $today = date('Y-m-d');
        $log = $this->getLogByDate($userId, $today);
        
        return !empty($log);
    }
    
    /**
     * Log symptoms
     */
    public function logSymptoms($data) {
        // Check if log already exists for this date
        $existingLog = $this->getLogByDate($data['user_id'], $data['log_date']);
        
        if ($existingLog) {
            // Update existing log
            return $this->db->update($this->table, $data, "id = ?", [$existingLog['id']]);
        } else {
            // Create new log
            return $this->db->insert($this->table, $data);
        }
    }
    
    /**
     * Get symptom trends
     */
    public function getSymptomTrends($userId, $days = 90) {
        $sql = "SELECT 
                log_date,
                inattention_rating,
                hyperactivity_rating,
                impulsivity_rating,
                emotional_regulation_rating,
                executive_function_rating,
                overall_rating
                FROM {$this->table}
                WHERE user_id = ?
                AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY log_date";
        
        return $this->db->fetchAll($sql, [$userId, $days]);
    }
    
    /**
     * Get current streak
     */
    public function getCurrentStreak($userId) {
        $today = date('Y-m-d');
        
        $sql = "SELECT COUNT(*) as streak
                FROM (
                    SELECT log_date
                    FROM {$this->table}
                    WHERE user_id = ?
                    AND log_date <= ?
                    ORDER BY log_date DESC
                    LIMIT 90
                ) as logs
                WHERE DATEDIFF(?, log_date) = ROW_NUMBER() OVER () - 1";
        
        $result = $this->db->fetchOne($sql, [$userId, $today, $today]);
        
        return $result ? $result['streak'] : 0;
    }
    
    /**
     * Get longest streak
     */
    public function getLongestStreak($userId) {
        $sql = "SELECT MAX(streak_length) as longest_streak
                FROM (
                    SELECT 
                    COUNT(*) as streak_length,
                    DATEDIFF(MAX(log_date), MIN(log_date)) + 1 as days_span
                    FROM (
                        SELECT 
                        log_date,
                        @rn:=@rn+1 as row_num,
                        DATE_SUB(log_date, INTERVAL @rn DAY) as grp
                        FROM {$this->table}, (SELECT @rn:=0) as r
                        WHERE user_id = ?
                        ORDER BY log_date
                    ) as t
                    GROUP BY grp
                    HAVING COUNT(*) = days_span
                ) as streaks";
        
        $result = $this->db->fetchOne($sql, [$userId]);
        
        return $result ? $result['longest_streak'] : 0;
    }
    
    /**
     * Get symptom averages
     */
    public function getSymptomAverages($userId, $days = 30) {
        $sql = "SELECT 
                AVG(inattention_rating) as avg_inattention,
                AVG(hyperactivity_rating) as avg_hyperactivity,
                AVG(impulsivity_rating) as avg_impulsivity,
                AVG(emotional_regulation_rating) as avg_emotional_regulation,
                AVG(executive_function_rating) as avg_executive_function,
                AVG(overall_rating) as avg_overall
                FROM {$this->table}
                WHERE user_id = ?
                AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)";
        
        return $this->db->fetchOne($sql, [$userId, $days]);
    }
    
    /**
     * Get symptom correlations with factors
     */
    public function getFactorCorrelations($userId, $days = 90) {
        $sql = "SELECT 
                AVG(CASE WHEN medication_taken = 1 THEN overall_rating ELSE NULL END) as avg_with_medication,
                AVG(CASE WHEN medication_taken = 0 THEN overall_rating ELSE NULL END) as avg_without_medication,
                AVG(CASE WHEN sleep_quality >= 4 THEN overall_rating ELSE NULL END) as avg_with_good_sleep,
                AVG(CASE WHEN sleep_quality < 4 THEN overall_rating ELSE NULL END) as avg_with_poor_sleep,
                AVG(CASE WHEN stress_level <= 2 THEN overall_rating ELSE NULL END) as avg_with_low_stress,
                AVG(CASE WHEN stress_level > 2 THEN overall_rating ELSE NULL END) as avg_with_high_stress,
                AVG(CASE WHEN exercise_done = 1 THEN overall_rating ELSE NULL END) as avg_with_exercise,
                AVG(CASE WHEN exercise_done = 0 THEN overall_rating ELSE NULL END) as avg_without_exercise
                FROM {$this->table}
                WHERE user_id = ?
                AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)";
        
        return $this->db->fetchOne($sql, [$userId, $days]);
    }
}
