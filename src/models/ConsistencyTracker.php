<?php
/**
 * Consistency Tracker Model
 *
 * Handles habit tracking for consistency
 */

require_once __DIR__ . '/BaseModel.php';

class ConsistencyTracker extends BaseModel {
    protected $table = 'consistency_trackers';
    protected $logsTable = 'consistency_logs';

    /**
     * Get all trackers for a user
     */
    public function getUserTrackers($userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                ORDER BY created_at DESC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get tracker by ID
     */
    public function getTracker($id) {
        return $this->find($id);
    }

    /**
     * Create a new tracker
     */
    public function createTracker($data) {
        return $this->create($data);
    }

    /**
     * Update a tracker
     */
    public function updateTracker($id, $data) {
        return $this->update($id, $data);
    }

    /**
     * Delete a tracker
     */
    public function deleteTracker($id) {
        return $this->delete($id);
    }

    /**
     * Get logs for a tracker
     */
    public function getTrackerLogs($trackerId, $startDate = null, $endDate = null) {
        $params = [$trackerId];
        $sql = "SELECT * FROM {$this->logsTable}
                WHERE tracker_id = ?";

        if ($startDate) {
            $sql .= " AND log_date >= ?";
            $params[] = $startDate;
        }

        if ($endDate) {
            $sql .= " AND log_date <= ?";
            $params[] = $endDate;
        }

        $sql .= " ORDER BY log_date DESC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get log for a specific date
     */
    public function getLogByDate($trackerId, $date) {
        $sql = "SELECT * FROM {$this->logsTable}
                WHERE tracker_id = ? AND log_date = ?
                LIMIT 1";

        return $this->db->fetchOne($sql, [$trackerId, $date]);
    }

    /**
     * Create or update a log
     */
    public function logCompletion($trackerId, $date, $completed, $difficultyRating = null, $notes = null) {
        // Check if log exists
        $existingLog = $this->getLogByDate($trackerId, $date);

        $data = [
            'completed' => $completed ? 1 : 0,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($difficultyRating !== null) {
            $data['difficulty_rating'] = $difficultyRating;
        }

        if ($notes !== null) {
            $data['notes'] = $notes;
        }

        if ($existingLog) {
            // Update existing log
            return $this->db->update(
                $this->logsTable,
                $data,
                "id = ?",
                [$existingLog['id']]
            );
        } else {
            // Create new log
            $data['tracker_id'] = $trackerId;
            $data['log_date'] = $date;
            $data['created_at'] = date('Y-m-d H:i:s');

            return $this->db->insert($this->logsTable, $data);
        }
    }

    /**
     * Update streak count for a tracker
     */
    public function updateStreak($trackerId) {
        // Get the tracker
        $tracker = $this->getTracker($trackerId);

        if (!$tracker) {
            return false;
        }

        // Calculate current streak
        $sql = "WITH date_series AS (
                    SELECT CURDATE() - INTERVAL (a.a + (10 * b.a) + (100 * c.a)) DAY AS date
                    FROM (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS a
                    CROSS JOIN (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS b
                    CROSS JOIN (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3) AS c
                    WHERE CURDATE() - INTERVAL (a.a + (10 * b.a) + (100 * c.a)) DAY >= DATE_SUB(CURDATE(), INTERVAL 365 DAY)
                ),
                logs AS (
                    SELECT log_date, completed
                    FROM {$this->logsTable}
                    WHERE tracker_id = ?
                    AND log_date >= DATE_SUB(CURDATE(), INTERVAL 365 DAY)
                    AND log_date <= CURDATE()
                ),
                tracker_dates AS (
                    SELECT
                        date_series.date,
                        CASE
                            WHEN logs.completed = 1 THEN 1
                            WHEN logs.log_date IS NULL AND (
                                CASE
                                    WHEN ? = 'daily' THEN TRUE
                                    WHEN ? = 'weekdays' AND DAYOFWEEK(date_series.date) BETWEEN 2 AND 6 THEN TRUE
                                    WHEN ? = 'weekends' AND DAYOFWEEK(date_series.date) IN (1, 7) THEN TRUE
                                    WHEN ? = 'weekly' AND DAYOFWEEK(date_series.date) = 2 THEN TRUE
                                    WHEN ? = 'custom' AND JSON_CONTAINS(?, CAST(DAYOFWEEK(date_series.date) AS CHAR)) THEN TRUE
                                    ELSE FALSE
                                END
                            ) THEN 0
                            ELSE NULL
                        END AS status
                    FROM date_series
                    LEFT JOIN logs ON date_series.date = logs.log_date
                    ORDER BY date_series.date DESC
                ),
                streak_data AS (
                    SELECT
                        date,
                        status,
                        CASE WHEN status = 0 OR status IS NULL THEN 1 ELSE 0 END AS streak_breaker
                    FROM tracker_dates
                    WHERE status IS NOT NULL
                    ORDER BY date DESC
                ),
                streak_groups AS (
                    SELECT
                        date,
                        status,
                        SUM(streak_breaker) OVER (ORDER BY date DESC) AS streak_group
                    FROM streak_data
                )
                SELECT
                    COUNT(*) AS streak_length
                FROM streak_groups
                WHERE status = 1 AND streak_group = 0";

        $params = [
            $trackerId,
            $tracker['frequency'],
            $tracker['frequency'],
            $tracker['frequency'],
            $tracker['frequency'],
            $tracker['frequency'],
            $tracker['custom_days'] ?? '[]'
        ];

        $result = $this->db->fetchOne($sql, $params);
        $currentStreak = $result ? $result['streak_length'] : 0;

        // Update the tracker
        $data = [
            'streak_count' => $currentStreak,
            'longest_streak' => max($currentStreak, $tracker['longest_streak']),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->update($trackerId, $data);
    }

    /**
     * Get trackers due today for a user
     */
    public function getTodayTrackers($userId) {
        $dayOfWeek = date('N'); // 1 (Monday) to 7 (Sunday)

        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                AND (
                    frequency = 'daily'
                    OR (frequency = 'weekdays' AND ? BETWEEN 1 AND 5)
                    OR (frequency = 'weekends' AND ? IN (6, 7))
                    OR (frequency = 'weekly' AND ? = 1)
                    OR (frequency = 'custom' AND JSON_CONTAINS(custom_days, ?))
                )
                ORDER BY time_of_day";

        return $this->db->fetchAll($sql, [$userId, $dayOfWeek, $dayOfWeek, $dayOfWeek, $dayOfWeek]);
    }

    /**
     * Get completion rate for a tracker
     */
    public function getCompletionRate($trackerId, $days = 30) {
        $sql = "SELECT
                    COUNT(*) as total_days,
                    SUM(completed) as completed_days
                FROM {$this->logsTable}
                WHERE tracker_id = ?
                AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)";

        $result = $this->db->fetchOne($sql, [$trackerId, $days]);

        if ($result && $result['total_days'] > 0) {
            return ($result['completed_days'] / $result['total_days']) * 100;
        }

        return 0;
    }

    /**
     * Get overall consistency score for a user
     */
    public function getOverallConsistency($userId, $days = 30) {
        $sql = "SELECT
                    t.id,
                    t.habit_name,
                    COUNT(l.id) as total_days,
                    SUM(l.completed) as completed_days
                FROM {$this->table} t
                LEFT JOIN {$this->logsTable} l ON t.id = l.tracker_id AND l.log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                WHERE t.user_id = ?
                GROUP BY t.id, t.habit_name";

        $results = $this->db->fetchAll($sql, [$days, $userId]);

        $totalCompletedDays = 0;
        $totalDays = 0;

        if ($results) {
            foreach ($results as $result) {
                $totalCompletedDays += $result['completed_days'] ? $result['completed_days'] : 0;
                $totalDays += $result['total_days'] ? $result['total_days'] : 0;
            }
        }

        if ($totalDays > 0) {
            return ($totalCompletedDays / $totalDays) * 100;
        }

        return 0;
    }
}
