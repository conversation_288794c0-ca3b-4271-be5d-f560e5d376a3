<?php
/**
 * AI Prompt Model
 *
 * Handles AI prompt management and operations
 */

require_once __DIR__ . '/BaseModel.php';

class AIPrompt extends BaseModel {
    protected $table = 'ai_prompts';

    /**
     * Get prompts for a user with optional filters
     */
    public function getUserPrompts($userId, $filters = []) {
        $sql = "SELECT p.*, c.name as category_name, c.color as category_color, c.icon as category_icon
                FROM {$this->table} p
                LEFT JOIN ai_prompt_categories c ON p.category_id = c.id
                WHERE p.user_id = ?";

        $params = [$userId];

        // Apply filters
        if (!empty($filters['category_id'])) {
            $sql .= " AND p.category_id = ?";
            $params[] = $filters['category_id'];
        }

        if (!empty($filters['is_template'])) {
            $sql .= " AND p.is_template = ?";
            $params[] = $filters['is_template'];
        }

        if (!empty($filters['is_favorite'])) {
            $sql .= " AND p.is_favorite = ?";
            $params[] = $filters['is_favorite'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (p.title LIKE ? OR p.description LIKE ? OR p.tags LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        // Order by usage and favorites
        $sql .= " ORDER BY p.is_favorite DESC, p.usage_count DESC, p.updated_at DESC";

        if (!empty($filters['limit'])) {
            $sql .= " LIMIT ?";
            $params[] = (int)$filters['limit'];
        }

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get recent prompts for a user
     */
    public function getRecentPrompts($userId, $limit = 10) {
        $sql = "SELECT p.*, c.name as category_name, c.color as category_color
                FROM {$this->table} p
                LEFT JOIN ai_prompt_categories c ON p.category_id = c.id
                WHERE p.user_id = ?
                ORDER BY p.updated_at DESC
                LIMIT ?";

        return $this->db->fetchAll($sql, [$userId, $limit]);
    }

    /**
     * Get popular prompts (high usage/rating)
     */
    public function getPopularPrompts($userId, $limit = 10) {
        $sql = "SELECT p.*, c.name as category_name, c.color as category_color
                FROM {$this->table} p
                LEFT JOIN ai_prompt_categories c ON p.category_id = c.id
                WHERE p.user_id = ? AND p.usage_count > 0
                ORDER BY (p.usage_count * 0.7 + p.effectiveness_rating * 0.3) DESC
                LIMIT ?";

        return $this->db->fetchAll($sql, [$userId, $limit]);
    }

    /**
     * Get favorite prompts
     */
    public function getFavoritePrompts($userId, $limit = 10) {
        $sql = "SELECT p.*, c.name as category_name, c.color as category_color
                FROM {$this->table} p
                LEFT JOIN ai_prompt_categories c ON p.category_id = c.id
                WHERE p.user_id = ? AND p.is_favorite = 1
                ORDER BY p.updated_at DESC
                LIMIT ?";

        return $this->db->fetchAll($sql, [$userId, $limit]);
    }

    /**
     * Create a new prompt
     */
    public function createPrompt($data) {
        // Validate required fields
        if (empty($data['title']) || empty($data['prompt_text'])) {
            return false;
        }

        // Prepare data
        $promptData = [
            'user_id' => $data['user_id'],
            'category_id' => $data['category_id'] ?? null,
            'title' => $data['title'],
            'description' => $data['description'] ?? null,
            'prompt_text' => $data['prompt_text'],
            'variables' => !empty($data['variables']) ? json_encode($data['variables']) : null,
            'tags' => $data['tags'] ?? null,
            'is_template' => $data['is_template'] ?? 0,
            'is_favorite' => $data['is_favorite'] ?? 0,
            'is_public' => $data['is_public'] ?? 0,
            'parent_prompt_id' => $data['parent_prompt_id'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->create($promptData);
    }

    /**
     * Update prompt
     */
    public function updatePrompt($id, $data) {
        $updateData = [
            'title' => $data['title'],
            'description' => $data['description'] ?? null,
            'prompt_text' => $data['prompt_text'],
            'variables' => !empty($data['variables']) ? json_encode($data['variables']) : null,
            'tags' => $data['tags'] ?? null,
            'category_id' => $data['category_id'] ?? null,
            'is_template' => $data['is_template'] ?? 0,
            'is_favorite' => $data['is_favorite'] ?? 0,
            'is_public' => $data['is_public'] ?? 0,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->update($id, $updateData);
    }

    /**
     * Increment usage count
     */
    public function incrementUsage($promptId) {
        $sql = "UPDATE {$this->table} SET usage_count = usage_count + 1, updated_at = ? WHERE id = ?";
        return $this->execute($sql, [date('Y-m-d H:i:s'), $promptId]);
    }

    /**
     * Update effectiveness rating
     */
    public function updateEffectivenessRating($promptId, $rating) {
        // Get current rating data
        $current = $this->find($promptId);
        if (!$current) return false;

        // Calculate new average (simple approach - could be enhanced with weighted averages)
        $currentRating = $current['effectiveness_rating'] ?? 0;
        $usageCount = $current['usage_count'] ?? 1;

        $newRating = (($currentRating * ($usageCount - 1)) + $rating) / $usageCount;

        $sql = "UPDATE {$this->table} SET effectiveness_rating = ?, updated_at = ? WHERE id = ?";
        return $this->execute($sql, [$newRating, date('Y-m-d H:i:s'), $promptId]);
    }

    /**
     * Toggle favorite status
     */
    public function toggleFavorite($promptId, $userId) {
        $prompt = $this->find($promptId);
        if (!$prompt || $prompt['user_id'] != $userId) {
            return false;
        }

        $newStatus = $prompt['is_favorite'] ? 0 : 1;
        $sql = "UPDATE {$this->table} SET is_favorite = ?, updated_at = ? WHERE id = ?";
        return $this->execute($sql, [$newStatus, date('Y-m-d H:i:s'), $promptId]);
    }

    /**
     * Duplicate/fork a prompt
     */
    public function forkPrompt($promptId, $userId, $newTitle = null) {
        $original = $this->find($promptId);
        if (!$original) return false;

        $forkData = [
            'user_id' => $userId,
            'category_id' => $original['category_id'],
            'title' => $newTitle ?? ('Copy of ' . $original['title']),
            'description' => $original['description'],
            'prompt_text' => $original['prompt_text'],
            'variables' => $original['variables'],
            'tags' => $original['tags'],
            'is_template' => $original['is_template'],
            'parent_prompt_id' => $promptId,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->create($forkData);
    }

    /**
     * Get prompt statistics for dashboard
     */
    public function getPromptStats($userId) {
        $sql = "SELECT
                    COUNT(*) as total_prompts,
                    COUNT(CASE WHEN is_favorite = 1 THEN 1 END) as favorite_prompts,
                    COUNT(CASE WHEN is_template = 1 THEN 1 END) as template_prompts,
                    SUM(usage_count) as total_usage,
                    AVG(effectiveness_rating) as avg_rating
                FROM {$this->table}
                WHERE user_id = ?";

        $result = $this->db->fetchOne($sql, [$userId]);

        // Get category distribution
        $categorySql = "SELECT c.name, c.color, COUNT(p.id) as count
                       FROM ai_prompt_categories c
                       LEFT JOIN {$this->table} p ON c.id = p.category_id AND p.user_id = ?
                       WHERE c.user_id = ? OR c.is_system = 1
                       GROUP BY c.id, c.name, c.color
                       ORDER BY count DESC";

        $categories = $this->db->fetchAll($categorySql, [$userId, $userId]);

        return [
            'overview' => $result,
            'categories' => $categories
        ];
    }

    /**
     * Search prompts with advanced filters
     */
    public function searchPrompts($userId, $query, $filters = []) {
        $sql = "SELECT p.*, c.name as category_name, c.color as category_color
                FROM {$this->table} p
                LEFT JOIN ai_prompt_categories c ON p.category_id = c.id
                WHERE p.user_id = ? AND (
                    p.title LIKE ? OR
                    p.description LIKE ? OR
                    p.prompt_text LIKE ? OR
                    p.tags LIKE ?
                )";

        $searchTerm = '%' . $query . '%';
        $params = [$userId, $searchTerm, $searchTerm, $searchTerm, $searchTerm];

        // Apply additional filters
        if (!empty($filters['category_id'])) {
            $sql .= " AND p.category_id = ?";
            $params[] = $filters['category_id'];
        }

        if (isset($filters['min_rating']) && $filters['min_rating'] > 0) {
            $sql .= " AND p.effectiveness_rating >= ?";
            $params[] = $filters['min_rating'];
        }

        $sql .= " ORDER BY p.effectiveness_rating DESC, p.usage_count DESC";

        return $this->db->fetchAll($sql, $params);
    }
}
