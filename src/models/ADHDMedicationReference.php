<?php
/**
 * ADHD Medication Reference Model
 *
 * Handles ADHD medication reference data
 */

require_once __DIR__ . '/BaseModel.php';

class ADHDMedicationReference extends BaseModel {
    protected $table = 'adhd_medications_reference';

    /**
     * Get all medication references
     */
    public function getAllMedications() {
        $sql = "SELECT * FROM {$this->table} ORDER BY generic_name ASC";
        return $this->db->fetchAll($sql);
    }

    /**
     * Get medication references by class
     */
    public function getMedicationsByClass($class) {
        $sql = "SELECT * FROM {$this->table} WHERE medication_class = ? ORDER BY generic_name ASC";
        return $this->db->fetchAll($sql, [$class]);
    }

    /**
     * Get medication reference by ID
     */
    public function getMedication($id) {
        return $this->find($id);
    }

    /**
     * Get medication reference by generic name
     */
    public function getMedicationByGenericName($genericName) {
        $sql = "SELECT * FROM {$this->table} WHERE generic_name = ? LIMIT 1";
        return $this->db->fetchOne($sql, [$genericName]);
    }

    /**
     * Search medications by name (generic or brand)
     */
    public function searchMedications($searchTerm) {
        // Make sure searchTerm is a string
        $searchTerm = is_array($searchTerm) ? $searchTerm[0] : $searchTerm;
        $searchTerm = "%{$searchTerm}%";

        $sql = "SELECT * FROM {$this->table}
                WHERE generic_name LIKE ?
                OR brand_names LIKE ?
                ORDER BY generic_name ASC";
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm]);
    }

    /**
     * Get all medication classes
     */
    public function getMedicationClasses() {
        $sql = "SELECT DISTINCT medication_class FROM {$this->table} ORDER BY medication_class ASC";
        return $this->db->fetchAll($sql);
    }

    /**
     * Get medication suggestions for autocomplete
     */
    public function getMedicationSuggestions($searchTerm) {
        // Make sure searchTerm is a string
        $searchTerm = is_array($searchTerm) ? $searchTerm[0] : $searchTerm;
        $searchTerm = "%{$searchTerm}%";

        $sql = "SELECT id, generic_name, brand_names, medication_class, typical_dosages
                FROM {$this->table}
                WHERE generic_name LIKE ?
                OR brand_names LIKE ?
                ORDER BY generic_name ASC
                LIMIT 10";
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm]);
    }

    /**
     * Get all enhancement categories
     */
    public function getEnhancementCategories() {
        $sql = "SELECT * FROM enhancement_categories ORDER BY name ASC";
        return $this->db->fetchAll($sql);
    }

    /**
     * Get medications by enhancement area
     */
    public function getMedicationsByEnhancementArea($enhancementArea) {
        // Make sure enhancementArea is a string
        $enhancementArea = is_array($enhancementArea) ? $enhancementArea[0] : $enhancementArea;
        $searchTerm = "%{$enhancementArea}%";

        $sql = "SELECT * FROM {$this->table}
                WHERE enhancement_areas LIKE ?
                ORDER BY generic_name ASC";
        return $this->db->fetchAll($sql, [$searchTerm]);
    }

    /**
     * Get enhancement areas for a medication
     */
    public function getEnhancementAreasForMedication($medicationId) {
        $sql = "SELECT enhancement_areas FROM {$this->table} WHERE id = ?";
        $result = $this->db->fetchOne($sql, [$medicationId]);

        if (!$result || empty($result['enhancement_areas'])) {
            return [];
        }

        return array_map('trim', explode(',', $result['enhancement_areas']));
    }
}
