<?php
/**
 * ADHD Symptom Model
 * 
 * Handles ADHD symptom tracking data
 */

require_once __DIR__ . '/BaseModel.php';

class ADHDSymptom extends BaseModel {
    protected $table = 'adhd_symptom_logs';
    protected $eventsTable = 'adhd_symptom_events';
    
    /**
     * Create a new symptom log
     */
    public function createLog($data) {
        return $this->create($data);
    }
    
    /**
     * Create a new symptom event
     */
    public function createEvent($data) {
        return $this->db->insert($this->eventsTable, $data);
    }
    
    /**
     * Get recent symptom logs for a user
     */
    public function getRecentLogs($userId, $days = 7) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE user_id = ? 
                AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY) 
                ORDER BY log_date DESC";
        
        return $this->db->fetchAll($sql, [$userId, $days]);
    }
    
    /**
     * Get recent symptom events for a user
     */
    public function getRecentEvents($userId, $days = 7) {
        $sql = "SELECT * FROM {$this->eventsTable} 
                WHERE user_id = ? 
                AND timestamp >= DATE_SUB(NOW(), INTERVAL ? DAY) 
                ORDER BY timestamp DESC";
        
        return $this->db->fetchAll($sql, [$userId, $days]);
    }
    
    /**
     * Get symptom data for a date range
     */
    public function getSymptomData($userId, $startDate, $endDate) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE user_id = ? 
                AND log_date BETWEEN ? AND ? 
                ORDER BY log_date ASC";
        
        return $this->db->fetchAll($sql, [$userId, $startDate, $endDate]);
    }
    
    /**
     * Get symptom trends for a user
     */
    public function getSymptomTrends($userId, $days = 30) {
        $sql = "SELECT 
                    log_date,
                    AVG(focus_score) as avg_focus,
                    AVG(productivity_score) as avg_productivity,
                    AVG(consistency_score) as avg_consistency,
                    AVG(organization_score) as avg_organization,
                    AVG(impulsivity_score) as avg_impulsivity,
                    AVG(emotional_regulation_score) as avg_emotional
                FROM {$this->table} 
                WHERE user_id = ? 
                AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY) 
                GROUP BY log_date
                ORDER BY log_date ASC";
        
        return $this->db->fetchAll($sql, [$userId, $days]);
    }
    
    /**
     * Get symptom averages for a user
     */
    public function getSymptomAverages($userId, $days = 30) {
        $sql = "SELECT 
                    AVG(focus_score) as avg_focus,
                    AVG(productivity_score) as avg_productivity,
                    AVG(consistency_score) as avg_consistency,
                    AVG(organization_score) as avg_organization,
                    AVG(impulsivity_score) as avg_impulsivity,
                    AVG(emotional_regulation_score) as avg_emotional
                FROM {$this->table} 
                WHERE user_id = ? 
                AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)";
        
        return $this->db->fetchOne($sql, [$userId, $days]);
    }
    
    /**
     * Get log for a specific date
     */
    public function getLogByDate($userId, $date) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE user_id = ? AND log_date = ? 
                LIMIT 1";
        
        return $this->db->fetchOne($sql, [$userId, $date]);
    }
    
    /**
     * Check if user has logged symptoms today
     */
    public function hasLoggedToday($userId) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE user_id = ? AND log_date = CURDATE()";
        
        $result = $this->db->fetchOne($sql, [$userId]);
        return $result && $result['count'] > 0;
    }
    
    /**
     * Get user's current streak (consecutive days of logging)
     */
    public function getCurrentStreak($userId) {
        $sql = "WITH date_series AS (
                    SELECT CURDATE() - INTERVAL (a.a + (10 * b.a) + (100 * c.a)) DAY AS date
                    FROM (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS a
                    CROSS JOIN (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS b
                    CROSS JOIN (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3) AS c
                    WHERE CURDATE() - INTERVAL (a.a + (10 * b.a) + (100 * c.a)) DAY >= DATE_SUB(CURDATE(), INTERVAL 365 DAY)
                ),
                logs AS (
                    SELECT log_date
                    FROM {$this->table}
                    WHERE user_id = ?
                    AND log_date >= DATE_SUB(CURDATE(), INTERVAL 365 DAY)
                    AND log_date <= CURDATE()
                ),
                streak_data AS (
                    SELECT 
                        date_series.date,
                        CASE WHEN logs.log_date IS NOT NULL THEN 1 ELSE 0 END AS has_log,
                        CASE WHEN logs.log_date IS NULL THEN 1 ELSE 0 END AS streak_breaker
                    FROM date_series
                    LEFT JOIN logs ON date_series.date = logs.log_date
                    ORDER BY date_series.date DESC
                ),
                streak_groups AS (
                    SELECT
                        date,
                        has_log,
                        SUM(streak_breaker) OVER (ORDER BY date DESC) AS streak_group
                    FROM streak_data
                )
                SELECT
                    COUNT(*) AS streak_length
                FROM streak_groups
                WHERE has_log = 1 AND streak_group = 0";
        
        $result = $this->db->fetchOne($sql, [$userId]);
        return $result ? $result['streak_length'] : 0;
    }
}
