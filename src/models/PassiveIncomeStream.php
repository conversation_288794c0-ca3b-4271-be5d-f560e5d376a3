<?php
/**
 * Passive Income Stream Model
 * 
 * Handles passive income stream-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class PassiveIncomeStream extends BaseModel {
    protected $table = 'passive_income_streams';
    protected $earningsTable = 'passive_income_earnings';
    protected $maintenanceTable = 'passive_income_maintenance';
    protected $opportunitiesTable = 'income_opportunities';
    protected $financesTable = 'finances';

    /**
     * Create a new passive income stream
     *
     * @param array $data Stream data
     * @return int|bool The ID of the created stream or false on failure
     */
    public function create($data) {
        return $this->db->insert($this->table, $data);
    }

    /**
     * Update a passive income stream
     *
     * @param int $id Stream ID
     * @param array $data Updated stream data
     * @return bool Success status
     */
    public function update($id, $data) {
        return $this->db->update($this->table, $data, ['id' => $id]);
    }

    /**
     * Delete a passive income stream
     *
     * @param int $id Stream ID
     * @return bool Success status
     */
    public function delete($id) {
        return $this->db->delete($this->table, ['id' => $id]);
    }

    /**
     * Get a passive income stream by ID
     *
     * @param int $id Stream ID
     * @param int $userId User ID (for security)
     * @return array|null Stream data or null if not found
     */
    public function getStream($id, $userId) {
        $sql = "SELECT s.*, o.name as opportunity_name 
                FROM {$this->table} s
                LEFT JOIN {$this->opportunitiesTable} o ON s.opportunity_id = o.id
                WHERE s.id = ? AND s.user_id = ?";
        
        return $this->db->fetchOne($sql, [$id, $userId]);
    }

    /**
     * Get all passive income streams for a user
     *
     * @param int $userId User ID
     * @param array $filters Optional filters
     * @return array Streams
     */
    public function getUserStreams($userId, $filters = []) {
        $params = [$userId];
        $whereClause = "WHERE s.user_id = ?";
        
        // Apply status filter
        if (!empty($filters['status'])) {
            $whereClause .= " AND s.status = ?";
            $params[] = $filters['status'];
        }
        
        // Apply category filter
        if (!empty($filters['category'])) {
            $whereClause .= " AND s.category = ?";
            $params[] = $filters['category'];
        }
        
        // Apply search filter
        if (!empty($filters['search'])) {
            $whereClause .= " AND (s.name LIKE ? OR s.description LIKE ? OR s.platform LIKE ?)";
            $searchTerm = "%{$filters['search']}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        $sql = "SELECT s.*, 
                    o.name as opportunity_name,
                    COALESCE(SUM(e.amount), 0) as total_earnings,
                    COUNT(DISTINCT e.id) as earnings_count,
                    MAX(e.earning_date) as last_earning_date
                FROM {$this->table} s
                LEFT JOIN {$this->opportunitiesTable} o ON s.opportunity_id = o.id
                LEFT JOIN {$this->earningsTable} e ON s.id = e.stream_id
                {$whereClause}
                GROUP BY s.id
                ORDER BY s.status ASC, s.created_at DESC";
        
        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get active passive income streams for a user
     *
     * @param int $userId User ID
     * @param int $limit Optional limit
     * @return array Active streams
     */
    public function getActiveStreams($userId, $limit = null) {
        $sql = "SELECT s.*, 
                    o.name as opportunity_name,
                    COALESCE(SUM(e.amount), 0) as total_earnings
                FROM {$this->table} s
                LEFT JOIN {$this->opportunitiesTable} o ON s.opportunity_id = o.id
                LEFT JOIN {$this->earningsTable} e ON s.id = e.stream_id
                WHERE s.user_id = ? AND s.status IN ('growing', 'stable')
                GROUP BY s.id
                ORDER BY total_earnings DESC";
        
        if ($limit) {
            $sql .= " LIMIT " . (int)$limit;
        }
        
        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get categories for a user's passive income streams
     *
     * @param int $userId User ID
     * @return array Categories
     */
    public function getCategories($userId) {
        $sql = "SELECT DISTINCT category FROM {$this->table} WHERE user_id = ? ORDER BY category";
        $results = $this->db->fetchAll($sql, [$userId]);
        
        return array_column($results, 'category');
    }

    /**
     * Get summary statistics for a user's passive income streams
     *
     * @param int $userId User ID
     * @return array Summary statistics
     */
    public function getStreamsSummary($userId) {
        $sql = "SELECT 
                    COUNT(*) as total_streams,
                    SUM(CASE WHEN status = 'setup' THEN 1 ELSE 0 END) as setup_streams,
                    SUM(CASE WHEN status = 'growing' THEN 1 ELSE 0 END) as growing_streams,
                    SUM(CASE WHEN status = 'stable' THEN 1 ELSE 0 END) as stable_streams,
                    SUM(CASE WHEN status = 'declining' THEN 1 ELSE 0 END) as declining_streams,
                    SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_streams,
                    SUM(initial_investment) as total_investment,
                    SUM(maintenance_hours_per_month) as total_monthly_maintenance,
                    COUNT(DISTINCT category) as total_categories
                FROM {$this->table} 
                WHERE user_id = ?";
        
        $summary = $this->db->fetchOne($sql, [$userId]);
        
        // Get total earnings
        $sql = "SELECT COALESCE(SUM(e.amount), 0) as total_earnings
                FROM {$this->earningsTable} e
                JOIN {$this->table} s ON e.stream_id = s.id
                WHERE s.user_id = ?";
        
        $earnings = $this->db->fetchOne($sql, [$userId]);
        
        if ($summary && $earnings) {
            $summary['total_earnings'] = $earnings['total_earnings'];
            
            // Calculate ROI if there's investment
            if ($summary['total_investment'] > 0) {
                $summary['roi'] = ($earnings['total_earnings'] / $summary['total_investment']) * 100;
            } else {
                $summary['roi'] = 0;
            }
        }
        
        return $summary;
    }

    /**
     * Add an earning record for a passive income stream
     *
     * @param array $data Earning data
     * @return int|bool The ID of the created earning or false on failure
     */
    public function addEarning($data) {
        return $this->db->insert($this->earningsTable, $data);
    }

    /**
     * Get earnings for a passive income stream
     *
     * @param int $streamId Stream ID
     * @return array Earnings
     */
    public function getStreamEarnings($streamId) {
        $sql = "SELECT * FROM {$this->earningsTable} WHERE stream_id = ? ORDER BY earning_date DESC";
        return $this->db->fetchAll($sql, [$streamId]);
    }

    /**
     * Add a maintenance record for a passive income stream
     *
     * @param array $data Maintenance data
     * @return int|bool The ID of the created maintenance record or false on failure
     */
    public function addMaintenance($data) {
        return $this->db->insert($this->maintenanceTable, $data);
    }

    /**
     * Get maintenance records for a passive income stream
     *
     * @param int $streamId Stream ID
     * @return array Maintenance records
     */
    public function getStreamMaintenance($streamId) {
        $sql = "SELECT * FROM {$this->maintenanceTable} WHERE stream_id = ? ORDER BY maintenance_date DESC";
        return $this->db->fetchAll($sql, [$streamId]);
    }

    /**
     * Get monthly earnings for a passive income stream
     *
     * @param int $streamId Stream ID
     * @param int $months Number of months to include
     * @return array Monthly earnings
     */
    public function getMonthlyEarnings($streamId, $months = 12) {
        $sql = "SELECT 
                    DATE_FORMAT(earning_date, '%Y-%m') as month,
                    DATE_FORMAT(earning_date, '%b %Y') as month_name,
                    SUM(amount) as total
                FROM {$this->earningsTable}
                WHERE stream_id = ?
                AND earning_date >= DATE_SUB(CURRENT_DATE(), INTERVAL ? MONTH)
                GROUP BY DATE_FORMAT(earning_date, '%Y-%m')
                ORDER BY month";
        
        return $this->db->fetchAll($sql, [$streamId, $months]);
    }

    /**
     * Get upcoming maintenance for a user
     *
     * @param int $userId User ID
     * @param int $days Number of days to look ahead
     * @return array Upcoming maintenance
     */
    public function getUpcomingMaintenance($userId, $days = 30) {
        $sql = "SELECT m.*, s.name as stream_name, s.category, s.platform
                FROM {$this->maintenanceTable} m
                JOIN {$this->table} s ON m.stream_id = s.id
                WHERE s.user_id = ?
                AND m.next_maintenance_date BETWEEN CURRENT_DATE() AND DATE_ADD(CURRENT_DATE(), INTERVAL ? DAY)
                ORDER BY m.next_maintenance_date";
        
        return $this->db->fetchAll($sql, [$userId, $days]);
    }
}
