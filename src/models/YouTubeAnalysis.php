<?php
/**
 * YouTube Analysis Model
 * 
 * Handles the creation, retrieval, and management of YouTube video analyses
 */

require_once __DIR__ . '/BaseModel.php';
require_once __DIR__ . '/YouTubeVideo.php';
require_once __DIR__ . '/YouTubeMoneyMakingStrategy.php';
require_once __DIR__ . '/../utils/YouTubeAPI.php';
require_once __DIR__ . '/../utils/AIAnalyzer.php';

class YouTubeAnalysis extends BaseModel {
    /**
     * Create a new YouTube analysis
     * 
     * @param array $data Analysis data
     * @return int|false The ID of the created analysis or false on failure
     */
    public function create($data) {
        try {
            $sql = "INSERT INTO youtube_analysis (
                video_id, 
                user_id, 
                analysis_type, 
                status, 
                created_at, 
                updated_at
            ) VALUES (
                :video_id, 
                :user_id, 
                :analysis_type, 
                :status, 
                :created_at, 
                :updated_at
            )";
            
            $params = [
                ':video_id' => $data['video_id'],
                ':user_id' => $data['user_id'],
                ':analysis_type' => $data['analysis_type'] ?? 'money_making',
                ':status' => $data['status'] ?? 'pending',
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ];
            
            return $this->db->insert($sql, $params);
        } catch (Exception $e) {
            error_log("Error creating YouTube analysis: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get an analysis by ID
     * 
     * @param int $analysisId Analysis ID
     * @param int $userId User ID
     * @return array|false Analysis data or false if not found
     */
    public function getAnalysis($analysisId, $userId) {
        try {
            $sql = "SELECT a.*, v.title as video_title, v.youtube_id, v.thumbnail_url, v.channel_title 
                    FROM youtube_analysis a 
                    JOIN youtube_videos v ON a.video_id = v.id 
                    WHERE a.id = :id AND a.user_id = :user_id";
            
            $analysis = $this->db->fetchOne($sql, [':id' => $analysisId, ':user_id' => $userId]);
            
            if ($analysis) {
                // Get money making strategies
                $strategyModel = new YouTubeMoneyMakingStrategy();
                $analysis['strategies'] = $strategyModel->getStrategiesByAnalysis($analysisId);
            }
            
            return $analysis;
        } catch (Exception $e) {
            error_log("Error getting YouTube analysis: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all analyses for a user
     * 
     * @param int $userId User ID
     * @param int $limit Optional limit
     * @param int $offset Optional offset
     * @return array Array of analyses
     */
    public function getUserAnalyses($userId, $limit = null, $offset = null) {
        try {
            $sql = "SELECT a.*, v.title as video_title, v.youtube_id, v.thumbnail_url, v.channel_title 
                    FROM youtube_analysis a 
                    JOIN youtube_videos v ON a.video_id = v.id 
                    WHERE a.user_id = :user_id 
                    ORDER BY a.created_at DESC";
            
            $params = [':user_id' => $userId];
            
            if ($limit !== null) {
                $sql .= " LIMIT :limit";
                $params[':limit'] = $limit;
                
                if ($offset !== null) {
                    $sql .= " OFFSET :offset";
                    $params[':offset'] = $offset;
                }
            }
            
            return $this->db->fetchAll($sql, $params);
        } catch (Exception $e) {
            error_log("Error getting user YouTube analyses: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get analyses for a video
     * 
     * @param int $videoId Video ID
     * @param int $userId User ID
     * @return array Array of analyses
     */
    public function getVideoAnalyses($videoId, $userId) {
        try {
            $sql = "SELECT a.*, v.title as video_title, v.youtube_id, v.thumbnail_url, v.channel_title 
                    FROM youtube_analysis a 
                    JOIN youtube_videos v ON a.video_id = v.id 
                    WHERE a.video_id = :video_id AND a.user_id = :user_id 
                    ORDER BY a.created_at DESC";
            
            return $this->db->fetchAll($sql, [':video_id' => $videoId, ':user_id' => $userId]);
        } catch (Exception $e) {
            error_log("Error getting video analyses: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update an analysis
     * 
     * @param int $analysisId Analysis ID
     * @param array $data Analysis data to update
     * @return bool Success or failure
     */
    public function update($analysisId, $data) {
        try {
            $updateFields = [];
            $params = [':id' => $analysisId];
            
            $allowedFields = [
                'status', 'summary', 'transcript', 'key_points', 'opportunities',
                'implementation_steps', 'estimated_roi', 'difficulty_level'
            ];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "{$field} = :{$field}";
                    $params[":{$field}"] = $data[$field];
                }
            }
            
            if (empty($updateFields)) {
                return false;
            }
            
            $updateFields[] = "updated_at = :updated_at";
            $params[':updated_at'] = date('Y-m-d H:i:s');
            
            $sql = "UPDATE youtube_analysis SET " . implode(', ', $updateFields) . " WHERE id = :id";
            
            return $this->db->execute($sql, $params);
        } catch (Exception $e) {
            error_log("Error updating YouTube analysis: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete an analysis
     * 
     * @param int $id Analysis ID
     * @return bool Success or failure
     */
    public function delete($id) {
        try {
            // First, delete all strategies
            $strategyModel = new YouTubeMoneyMakingStrategy();
            $strategyModel->deleteAllStrategies($id);
            
            // Then delete the analysis
            $sql = "DELETE FROM youtube_analysis WHERE id = :id";
            return $this->db->execute($sql, [':id' => $id]);
        } catch (Exception $e) {
            error_log("Error deleting YouTube analysis: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete an analysis if it belongs to the user
     * 
     * @param int $analysisId Analysis ID
     * @param int $userId User ID
     * @return bool Success or failure
     */
    public function deleteIfOwner($analysisId, $userId) {
        try {
            // First, delete all strategies
            $strategyModel = new YouTubeMoneyMakingStrategy();
            $strategyModel->deleteAllStrategies($analysisId);
            
            // Then delete the analysis
            $sql = "DELETE FROM youtube_analysis WHERE id = :id AND user_id = :user_id";
            return $this->db->execute($sql, [':id' => $analysisId, ':user_id' => $userId]);
        } catch (Exception $e) {
            error_log("Error deleting YouTube analysis: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Execute a YouTube video analysis
     * 
     * @param int $analysisId Analysis ID
     * @param int $userId User ID
     * @param string $apiKey YouTube API key
     * @return bool Success or failure
     */
    public function executeAnalysis($analysisId, $userId, $apiKey) {
        try {
            // Get the analysis
            $analysis = $this->getAnalysis($analysisId, $userId);
            
            if (!$analysis) {
                return false;
            }
            
            // Update analysis status to in_progress
            $this->update($analysisId, ['status' => 'in_progress']);
            
            // Get the video
            $videoModel = new YouTubeVideo();
            $video = $videoModel->getVideo($analysis['video_id']);
            
            if (!$video) {
                $this->update($analysisId, ['status' => 'failed']);
                return false;
            }
            
            // Initialize YouTube API
            $youtubeAPI = new YouTubeAPI($apiKey);
            
            // Get video transcript
            $transcript = $youtubeAPI->getVideoTranscript($video['youtube_id']);
            
            // Update analysis with transcript
            $this->update($analysisId, ['transcript' => $transcript]);
            
            // Initialize AI Analyzer
            $aiAnalyzer = new AIAnalyzer();
            
            // Analyze video based on analysis type
            $analysisResults = [];
            switch ($analysis['analysis_type']) {
                case 'money_making':
                    $analysisResults = $aiAnalyzer->analyzeMoneyMakingOpportunities($video, $transcript);
                    break;
                case 'content_ideas':
                    $analysisResults = $aiAnalyzer->analyzeContentIdeas($video, $transcript);
                    break;
                default:
                    $analysisResults = $aiAnalyzer->analyzeMoneyMakingOpportunities($video, $transcript);
                    break;
            }
            
            if (empty($analysisResults)) {
                $this->update($analysisId, ['status' => 'failed']);
                return false;
            }
            
            // Update analysis with results
            $updateData = [
                'status' => 'completed',
                'summary' => $analysisResults['summary'],
                'key_points' => $analysisResults['key_points'],
                'opportunities' => $analysisResults['opportunities'],
                'implementation_steps' => $analysisResults['implementation_steps'],
                'estimated_roi' => $analysisResults['estimated_roi'],
                'difficulty_level' => $analysisResults['difficulty_level']
            ];
            
            $this->update($analysisId, $updateData);
            
            // Save money making strategies
            if (!empty($analysisResults['strategies'])) {
                $strategyModel = new YouTubeMoneyMakingStrategy();
                
                foreach ($analysisResults['strategies'] as $strategy) {
                    $strategyData = [
                        'analysis_id' => $analysisId,
                        'strategy_name' => $strategy['name'],
                        'description' => $strategy['description'],
                        'potential_revenue' => $strategy['potential_revenue'],
                        'time_investment' => $strategy['time_investment'],
                        'required_skills' => $strategy['required_skills'],
                        'required_resources' => $strategy['required_resources'],
                        'implementation_difficulty' => $strategy['implementation_difficulty'],
                        'suitable_brigade' => $strategy['suitable_brigade']
                    ];
                    
                    $strategyModel->create($strategyData);
                }
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Error executing YouTube analysis: " . $e->getMessage());
            $this->update($analysisId, ['status' => 'failed']);
            return false;
        }
    }
}
