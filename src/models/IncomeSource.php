<?php
/**
 * Income Source Model
 *
 * Handles income source-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class IncomeSource extends BaseModel {
    protected $table = 'income_sources';
    protected $transactionsTable = 'income_source_transactions';
    protected $financesTable = 'finances';

    /**
     * Get income sources for a specific user
     */
    public function getUserIncomeSources($userId, $includeInactive = false, $filters = []) {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ?";
        $params = [$userId];

        if (!$includeInactive) {
            $sql .= " AND is_active = 1";
        }

        // Apply filters if provided
        if (!empty($filters)) {
            // Filter by source type
            if (!empty($filters['source_type'])) {
                $sql .= " AND source_type = ?";
                $params[] = $filters['source_type'];
            }

            // Filter by category
            if (!empty($filters['category'])) {
                $sql .= " AND category = ?";
                $params[] = $filters['category'];
            }

            // Filter by tags (partial match)
            if (!empty($filters['tags'])) {
                $sql .= " AND tags LIKE ?";
                $params[] = "%{$filters['tags']}%";
            }

            // Filter by location
            if (!empty($filters['location'])) {
                $sql .= " AND location LIKE ?";
                $params[] = "%{$filters['location']}%";
            }

            // Filter by recurring status
            if (isset($filters['is_recurring'])) {
                $sql .= " AND is_recurring = ?";
                $params[] = $filters['is_recurring'] ? 1 : 0;
            }
        }

        $sql .= " ORDER BY name ASC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get income sources by type
     */
    public function getSourcesByType($userId, $sourceType, $includeInactive = false) {
        $filters = ['source_type' => $sourceType];
        return $this->getUserIncomeSources($userId, $includeInactive, $filters);
    }

    /**
     * Get a specific income source
     */
    public function getIncomeSource($id, $userId) {
        $sql = "SELECT * FROM {$this->table} WHERE id = ? AND user_id = ?";
        return $this->db->fetchOne($sql, [$id, $userId]);
    }

    /**
     * Create a new income source
     */
    public function createIncomeSource($data) {
        // Debug log
        error_log("Creating income source: " . print_r($data, true));

        // Format date fields
        if (isset($data['last_payment_date']) && $data['last_payment_date'] === '') {
            $data['last_payment_date'] = null;
        }

        if (isset($data['next_expected_date']) && $data['next_expected_date'] === '') {
            $data['next_expected_date'] = null;
        }

        // Format expected amount
        if (isset($data['expected_amount']) && $data['expected_amount'] === '') {
            $data['expected_amount'] = null;
        }

        // Make sure boolean fields are properly set
        $data['is_active'] = isset($data['is_active']) ? 1 : 0;
        $data['is_recurring'] = isset($data['is_recurring']) ? 1 : 0;

        // If not recurring, clear the recurrence pattern
        if (!$data['is_recurring']) {
            $data['recurrence_pattern'] = null;
        }

        return $this->create($data);
    }

    /**
     * Update an income source
     */
    public function updateIncomeSource($id, $data) {
        // Debug log
        error_log("Updating income source ID {$id}: " . print_r($data, true));

        // Format date fields
        if (isset($data['last_payment_date']) && $data['last_payment_date'] === '') {
            $data['last_payment_date'] = null;
        }

        if (isset($data['next_expected_date']) && $data['next_expected_date'] === '') {
            $data['next_expected_date'] = null;
        }

        // Format expected amount
        if (isset($data['expected_amount']) && $data['expected_amount'] === '') {
            $data['expected_amount'] = null;
        }

        // Make sure boolean fields are properly set
        $data['is_active'] = isset($data['is_active']) ? 1 : 0;
        $data['is_recurring'] = isset($data['is_recurring']) ? 1 : 0;

        // If not recurring, clear the recurrence pattern
        if (!$data['is_recurring']) {
            $data['recurrence_pattern'] = null;
        }

        $result = $this->update($id, $data);
        error_log("Update result: " . ($result ? "Success" : "Failed"));
        return $result;
    }

    /**
     * Delete an income source
     */
    public function deleteIncomeSource($id, $userId) {
        // Debug log
        error_log("Deleting income source ID {$id} for user {$userId}");

        try {
            // First, check if the income source exists
            $incomeSource = $this->getIncomeSource($id, $userId);
            if (!$incomeSource) {
                error_log("Income source ID {$id} not found for user {$userId}");
                return false;
            }

            // Delete any links to transactions first
            $sql = "DELETE FROM {$this->transactionsTable} WHERE income_source_id = ?";
            $this->db->query($sql, [$id]);

            // Now delete the income source
            $sql = "DELETE FROM {$this->table} WHERE id = ? AND user_id = ?";
            $stmt = $this->db->query($sql, [$id, $userId]);

            $result = $stmt && $stmt->rowCount() > 0;
            error_log("Delete result: " . ($result ? "Success" : "Failed"));
            return $result;
        } catch (Exception $e) {
            error_log("Error deleting income source: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Link a transaction to an income source
     */
    public function linkTransaction($incomeSourceId, $transactionId) {
        $sql = "INSERT INTO {$this->transactionsTable} (income_source_id, transaction_id, created_at)
                VALUES (?, ?, ?)";

        $stmt = $this->db->query($sql, [
            $incomeSourceId,
            $transactionId,
            date('Y-m-d H:i:s')
        ]);

        return $stmt && $stmt->rowCount() > 0;
    }

    /**
     * Unlink a transaction from an income source
     */
    public function unlinkTransaction($incomeSourceId, $transactionId) {
        $sql = "DELETE FROM {$this->transactionsTable}
                WHERE income_source_id = ? AND transaction_id = ?";

        $stmt = $this->db->query($sql, [$incomeSourceId, $transactionId]);
        return $stmt && $stmt->rowCount() > 0;
    }

    /**
     * Get transactions for a specific income source
     */
    public function getSourceTransactions($incomeSourceId, $userId, $limit = null) {
        $sql = "SELECT f.* FROM {$this->financesTable} f
                JOIN {$this->transactionsTable} ist ON f.id = ist.transaction_id
                WHERE ist.income_source_id = ? AND f.user_id = ? AND f.type = 'income'
                ORDER BY f.date DESC, f.created_at DESC";

        if ($limit) {
            $sql .= " LIMIT ?";
            return $this->db->fetchAll($sql, [$incomeSourceId, $userId, $limit]);
        }

        return $this->db->fetchAll($sql, [$incomeSourceId, $userId]);
    }

    /**
     * Get income source summary (total income, transaction count, etc.)
     */
    public function getSourceSummary($incomeSourceId, $userId, $startDate = null, $endDate = null) {
        $params = [$incomeSourceId, $userId];

        $dateFilter = "";
        if ($startDate && $endDate) {
            $dateFilter = " AND f.date BETWEEN ? AND ?";
            $params[] = $startDate;
            $params[] = $endDate;
        }

        $sql = "SELECT
                    COUNT(f.id) as transaction_count,
                    SUM(CASE WHEN f.transaction_type = 'monetary' THEN f.amount
                             WHEN f.transaction_type = 'non_monetary' THEN COALESCE(f.fair_market_value, 0)
                             ELSE 0 END) as total_income,
                    MIN(f.date) as first_transaction_date,
                    MAX(f.date) as last_transaction_date
                FROM {$this->financesTable} f
                JOIN {$this->transactionsTable} ist ON f.id = ist.transaction_id
                WHERE ist.income_source_id = ? AND f.user_id = ? AND f.type = 'income'
                {$dateFilter}";

        return $this->db->fetchOne($sql, $params);
    }

    /**
     * Get yearly income by source for a range of years
     *
     * @param int $userId User ID
     * @param int $startYear Start year
     * @param int $endYear End year
     * @return array Yearly income data by source
     */
    public function getYearlyIncomeBySource($userId, $startYear, $endYear) {
        $sql = "SELECT
                    s.id as source_id,
                    s.name as source_name,
                    s.source_type,
                    YEAR(f.date) as year,
                    SUM(CASE WHEN f.transaction_type = 'monetary' THEN f.amount
                             WHEN f.transaction_type = 'non_monetary' THEN COALESCE(f.fair_market_value, 0)
                             ELSE 0 END) as total_income
                FROM {$this->table} s
                JOIN {$this->transactionsTable} ist ON s.id = ist.income_source_id
                JOIN {$this->financesTable} f ON ist.transaction_id = f.id
                WHERE s.user_id = ? AND f.type = 'income'
                AND YEAR(f.date) BETWEEN ? AND ?
                GROUP BY s.id, s.name, s.source_type, YEAR(f.date)
                ORDER BY s.name, year";

        return $this->db->fetchAll($sql, [$userId, $startYear, $endYear]);
    }

    /**
     * Get monthly income by source for a specific year
     *
     * @param int $userId User ID
     * @param int $year Year to analyze
     * @return array Monthly income data by source
     */
    public function getMonthlyIncomeBySource($userId, $year) {
        $sql = "SELECT
                    s.id as source_id,
                    s.name as source_name,
                    s.source_type,
                    DATE_FORMAT(f.date, '%Y-%m') as month,
                    DATE_FORMAT(f.date, '%b') as month_name,
                    SUM(CASE WHEN f.transaction_type = 'monetary' THEN f.amount
                             WHEN f.transaction_type = 'non_monetary' THEN COALESCE(f.fair_market_value, 0)
                             ELSE 0 END) as total_income
                FROM {$this->table} s
                JOIN {$this->transactionsTable} ist ON s.id = ist.income_source_id
                JOIN {$this->financesTable} f ON ist.transaction_id = f.id
                WHERE s.user_id = ? AND f.type = 'income'
                AND YEAR(f.date) = ?
                GROUP BY s.id, s.name, s.source_type, DATE_FORMAT(f.date, '%Y-%m'), DATE_FORMAT(f.date, '%b')
                ORDER BY s.name, month";

        return $this->db->fetchAll($sql, [$userId, $year]);
    }

    /**
     * Get monthly income for a specific source
     */
    public function getMonthlyIncome($incomeSourceId, $userId, $months = 12) {
        $endDate = date('Y-m-d');
        $startDate = date('Y-m-d', strtotime("-{$months} months"));

        $sql = "SELECT
                    DATE_FORMAT(f.date, '%Y-%m') as month,
                    SUM(CASE WHEN f.transaction_type = 'monetary' THEN f.amount
                             WHEN f.transaction_type = 'non_monetary' THEN COALESCE(f.fair_market_value, 0)
                             ELSE 0 END) as total_income
                FROM {$this->financesTable} f
                JOIN {$this->transactionsTable} ist ON f.id = ist.transaction_id
                WHERE ist.income_source_id = ? AND f.user_id = ? AND f.type = 'income'
                AND f.date BETWEEN ? AND ?
                GROUP BY DATE_FORMAT(f.date, '%Y-%m')
                ORDER BY month";

        return $this->db->fetchAll($sql, [$incomeSourceId, $userId, $startDate, $endDate]);
    }

    /**
     * Get income sources with their total income
     */
    public function getSourcesWithTotalIncome($userId, $startDate = null, $endDate = null, $sourceType = null) {
        // Modified version that doesn't rely on transactions for basic source info
        $sql = "SELECT
                    s.id,
                    s.name,
                    s.description,
                    s.is_active,
                    s.is_recurring,
                    s.recurrence_pattern,
                    s.expected_amount,
                    s.category,
                    s.source_type,
                    s.payment_method,
                    s.location,
                    s.contact_person,
                    s.contact_info,
                    s.tags,
                    s.last_payment_date,
                    s.next_expected_date,
                    COALESCE(transaction_count, 0) as transaction_count,
                    COALESCE(total_income, 0) as total_income
                FROM {$this->table} s
                LEFT JOIN (
                    SELECT
                        ist.income_source_id,
                        COUNT(DISTINCT f.id) as transaction_count,
                        SUM(CASE WHEN f.transaction_type = 'monetary' THEN f.amount
                                 WHEN f.transaction_type = 'non_monetary' THEN COALESCE(f.fair_market_value, 0)
                                 ELSE 0 END) as total_income
                    FROM {$this->transactionsTable} ist
                    JOIN {$this->financesTable} f ON ist.transaction_id = f.id
                    WHERE f.type = 'income' " .
                    ($startDate && $endDate ? " AND f.date BETWEEN ? AND ?" : "") . "
                    GROUP BY ist.income_source_id
                ) AS income_data ON s.id = income_data.income_source_id
                WHERE s.user_id = ?";

        // Add source type filter if provided
        if (!empty($sourceType)) {
            $sql .= " AND s.source_type = ?";
        }

        $sql .= " ORDER BY s.name ASC";

        $params = [];
        if ($startDate && $endDate) {
            $params[] = $startDate;
            $params[] = $endDate;
        }
        $params[] = $userId;

        // Add source type parameter if provided
        if (!empty($sourceType)) {
            $params[] = $sourceType;
        }

        // Debug output
        error_log('SQL Query: ' . $sql);
        error_log('Parameters: ' . print_r($params, true));

        $results = $this->db->fetchAll($sql, $params);
        error_log('Query results: ' . print_r($results, true));

        return $results;
    }

    /**
     * Get income source transactions by transaction ID
     */
    public function getSourceTransactionsByTransactionId($transactionId) {
        $sql = "SELECT * FROM {$this->transactionsTable} WHERE transaction_id = ?";
        return $this->db->fetchAll($sql, [$transactionId]);
    }

    /**
     * Get income forecast for the next few months
     */
    public function getIncomeForecast($userId, $months = 6) {
        // Get all active recurring income sources
        $sql = "SELECT
                    id,
                    name,
                    expected_amount,
                    recurrence_pattern,
                    source_type,
                    category,
                    next_expected_date,
                    last_payment_date
                FROM {$this->table}
                WHERE user_id = ? AND is_active = 1 AND is_recurring = 1 AND expected_amount IS NOT NULL";

        $sources = $this->db->fetchAll($sql, [$userId]);

        // Initialize forecast array
        $forecast = [];
        $today = new DateTime();

        // For each month in the forecast period
        for ($i = 0; $i < $months; $i++) {
            $month = clone $today;
            $month->modify("+{$i} month");
            $monthKey = $month->format('Y-m');
            $monthStart = $month->format('Y-m-01');
            $monthEnd = $month->format('Y-m-t');

            $forecast[$monthKey] = [
                'month' => $monthKey,
                'month_name' => $month->format('F Y'),
                'start_date' => $monthStart,
                'end_date' => $monthEnd,
                'total_expected' => 0,
                'sources' => []
            ];

            // For each income source, determine if it will generate income in this month
            foreach ($sources as $source) {
                $expectedAmount = (float)$source['expected_amount'];
                $occurrences = $this->calculateOccurrencesInMonth($source, $month);

                if ($occurrences > 0) {
                    $sourceTotal = $expectedAmount * $occurrences;
                    $forecast[$monthKey]['total_expected'] += $sourceTotal;

                    $forecast[$monthKey]['sources'][] = [
                        'id' => $source['id'],
                        'name' => $source['name'],
                        'source_type' => $source['source_type'],
                        'category' => $source['category'],
                        'recurrence_pattern' => $source['recurrence_pattern'],
                        'expected_amount' => $expectedAmount,
                        'occurrences' => $occurrences,
                        'total' => $sourceTotal
                    ];
                }
            }
        }

        return array_values($forecast);
    }

    /**
     * Calculate how many times an income source will occur in a given month
     */
    private function calculateOccurrencesInMonth($source, DateTime $month) {
        $pattern = $source['recurrence_pattern'];
        $nextExpectedDate = !empty($source['next_expected_date']) ? new DateTime($source['next_expected_date']) : null;

        // If we have a next expected date and it's after the end of this month, no occurrences
        if ($nextExpectedDate) {
            $monthEnd = clone $month;
            $monthEnd->modify('last day of this month');

            if ($nextExpectedDate > $monthEnd) {
                return 0;
            }
        }

        // Calculate occurrences based on recurrence pattern
        switch ($pattern) {
            case 'daily':
                // Count days in month
                return (int)$month->format('t');

            case 'weekly':
                // Count weeks in month (approximately)
                return 4;

            case 'biweekly':
                // Approximately 2 occurrences per month
                return 2;

            case 'monthly':
                // Once per month
                return 1;

            case 'quarterly':
                // Once every 3 months
                $monthNum = (int)$month->format('n');
                return ($monthNum % 3 === 1) ? 1 : 0;

            case 'yearly':
                // Once per year
                $monthNum = (int)$month->format('n');
                return ($monthNum === 1) ? 1 : 0;

            case 'irregular':
                // For irregular patterns, if next_expected_date is in this month, count as 1
                if ($nextExpectedDate) {
                    $monthStart = clone $month;
                    $monthStart->modify('first day of this month');
                    $monthEnd = clone $month;
                    $monthEnd->modify('last day of this month');

                    if ($nextExpectedDate >= $monthStart && $nextExpectedDate <= $monthEnd) {
                        return 1;
                    }
                }
                return 0;

            default:
                return 0;
        }
    }
}
