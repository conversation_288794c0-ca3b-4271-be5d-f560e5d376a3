<?php
/**
 * Finance Model
 *
 * Handles finance-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class Finance extends BaseModel {
    protected $table = 'finances';

    /**
     * Get financial transactions for a specific user
     */
    public function getUserTransactions($userId, $filters = []) {
        // Add a debug log to see what's happening
        error_log("Getting transactions for user: " . $userId);
        error_log("Filters: " . print_r($filters, true));

        $sql = "SELECT * FROM {$this->table} WHERE user_id = ?";
        $params = [$userId];

        // Apply filters
        if (!empty($filters)) {
            // Filter by type (income/expense)
            if (isset($filters['type']) && !empty($filters['type'])) {
                $sql .= " AND type = ?";
                $params[] = $filters['type'];
            }

            // Filter by category
            if (isset($filters['category']) && !empty($filters['category'])) {
                $sql .= " AND category = ?";
                $params[] = $filters['category'];
            }

            // Filter by date range
            if (isset($filters['start_date']) && isset($filters['end_date'])) {
                $sql .= " AND date BETWEEN ? AND ?";
                $params[] = $filters['start_date'];
                $params[] = $filters['end_date'];
            }

            // Filter by transaction type (monetary/non_monetary)
            if (isset($filters['transaction_type']) && !empty($filters['transaction_type'])) {
                $sql .= " AND transaction_type = ?";
                $params[] = $filters['transaction_type'];
            }

            // Filter by payment method
            if (isset($filters['payment_method']) && !empty($filters['payment_method'])) {
                $sql .= " AND payment_method = ?";
                $params[] = $filters['payment_method'];
            }
        }

        // Order by date, newest first
        $sql .= " ORDER BY date DESC, created_at DESC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get summary of income and expenses for a specific period
     */
    public function getSummary($userId, $startDate, $endDate) {
        $sql = "SELECT
                    SUM(CASE WHEN type = 'income' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'income' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_income,
                    SUM(CASE WHEN type = 'expense' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'expense' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_expense
                FROM {$this->table}
                WHERE user_id = ? AND date BETWEEN ? AND ?";

        return $this->db->fetchOne($sql, [$userId, $startDate, $endDate]);
    }

    /**
     * Get comprehensive summary including non-monetary transactions
     */
    public function getComprehensiveSummary($userId, $startDate, $endDate) {
        $sql = "SELECT
                    SUM(CASE WHEN type = 'income' AND transaction_type = 'monetary' THEN amount ELSE 0 END) as monetary_income,
                    SUM(CASE WHEN type = 'expense' AND transaction_type = 'monetary' THEN amount ELSE 0 END) as monetary_expense,
                    SUM(CASE WHEN type = 'income' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, amount) ELSE 0 END) as non_monetary_income,
                    SUM(CASE WHEN type = 'expense' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, amount) ELSE 0 END) as non_monetary_expense,
                    SUM(CASE WHEN payment_method = 'credit_card' AND type = 'expense' THEN amount ELSE 0 END) as credit_card_expense
                FROM {$this->table}
                WHERE user_id = ? AND date BETWEEN ? AND ?";

        $result = $this->db->fetchOne($sql, [$userId, $startDate, $endDate]);

        // Ensure we have numeric values
        return [
            'monetary_income' => (float)($result['monetary_income'] ?? 0),
            'monetary_expense' => (float)($result['monetary_expense'] ?? 0),
            'non_monetary_income' => (float)($result['non_monetary_income'] ?? 0),
            'non_monetary_expense' => (float)($result['non_monetary_expense'] ?? 0),
            'credit_card_expense' => (float)($result['credit_card_expense'] ?? 0)
        ];
    }

    /**
     * Get spending by category for a specific period
     */
    public function getSpendingByCategory($userId, $startDate, $endDate) {
        $sql = "SELECT
                    category,
                    SUM(CASE WHEN transaction_type = 'monetary' THEN amount
                             WHEN transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_amount
                FROM {$this->table}
                WHERE user_id = ? AND type = 'expense' AND date BETWEEN ? AND ?
                GROUP BY category
                ORDER BY total_amount DESC";

        return $this->db->fetchAll($sql, [$userId, $startDate, $endDate]);
    }

    /**
     * Get recent transactions
     */
    public function getRecentTransactions($userId, $limit = 5) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                ORDER BY date DESC, created_at DESC
                LIMIT ?";

        return $this->db->fetchAll($sql, [$userId, $limit]);
    }

    /**
     * Get unique categories for a user
     */
    public function getUniqueCategories($userId) {
        $sql = "SELECT DISTINCT category
                FROM {$this->table}
                WHERE user_id = ?
                ORDER BY category ASC";

        $results = $this->db->fetchAll($sql, [$userId]);
        return array_column($results, 'category');
    }

    /**
     * Get monthly financial data for a specific year
     */
    public function getMonthlyFinancialData($userId, $year) {
        $sql = "SELECT
                    DATE_FORMAT(date, '%Y-%m') as month,
                    DATE_FORMAT(date, '%b') as month_name,
                    SUM(CASE WHEN type = 'income' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'income' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_income,
                    SUM(CASE WHEN type = 'expense' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'expense' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_expense
                FROM {$this->table}
                WHERE user_id = ? AND YEAR(date) = ?
                GROUP BY DATE_FORMAT(date, '%Y-%m'), DATE_FORMAT(date, '%b')
                ORDER BY month";

        return $this->db->fetchAll($sql, [$userId, $year]);
    }

    /**
     * Get quarterly financial data for a specific year
     */
    public function getQuarterlyFinancialData($userId, $year) {
        $sql = "SELECT
                    CONCAT(YEAR(date), '-Q', QUARTER(date)) as quarter,
                    CONCAT('Q', QUARTER(date)) as quarter_name,
                    SUM(CASE WHEN type = 'income' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'income' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_income,
                    SUM(CASE WHEN type = 'expense' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'expense' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_expense
                FROM {$this->table}
                WHERE user_id = ? AND YEAR(date) = ?
                GROUP BY YEAR(date), QUARTER(date)
                ORDER BY quarter";

        return $this->db->fetchAll($sql, [$userId, $year]);
    }

    /**
     * Get yearly financial data for a range of years
     */
    public function getYearlyFinancialData($userId, $startYear, $endYear) {
        $sql = "SELECT
                    YEAR(date) as year,
                    SUM(CASE WHEN type = 'income' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'income' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_income,
                    SUM(CASE WHEN type = 'expense' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'expense' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_expense
                FROM {$this->table}
                WHERE user_id = ? AND YEAR(date) BETWEEN ? AND ?
                GROUP BY YEAR(date)
                ORDER BY year";

        return $this->db->fetchAll($sql, [$userId, $startYear, $endYear]);
    }

    /**
     * Get custom period financial data
     */
    public function getCustomPeriodFinancialData($userId, $startDate, $endDate) {
        $sql = "SELECT
                    SUM(CASE WHEN type = 'income' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'income' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_income,
                    SUM(CASE WHEN type = 'expense' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'expense' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_expense
                FROM {$this->table}
                WHERE user_id = ? AND date BETWEEN ? AND ?";

        return $this->db->fetchOne($sql, [$userId, $startDate, $endDate]);
    }

    /**
     * Get year-over-year comparison data for monthly periods
     */
    public function getYearOverYearMonthlyComparison($userId, $currentYear, $previousYear, $month = null) {
        $monthFilter = '';
        $params = [$userId, $currentYear, $previousYear];

        if ($month) {
            $monthFilter = ' AND MONTH(date) = ?';
            $params[] = $month;
        }

        $sql = "SELECT
                    YEAR(date) as year,
                    MONTH(date) as month,
                    DATE_FORMAT(date, '%b') as month_name,
                    SUM(CASE WHEN type = 'income' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'income' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_income,
                    SUM(CASE WHEN type = 'expense' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'expense' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_expense
                FROM {$this->table}
                WHERE user_id = ? AND YEAR(date) IN (?, ?) $monthFilter
                GROUP BY YEAR(date), MONTH(date), DATE_FORMAT(date, '%b')
                ORDER BY YEAR(date), MONTH(date)";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get month-over-month comparison data
     */
    public function getMonthOverMonthComparison($userId, $numberOfMonths = 12) {
        // Calculate the start date (current month - numberOfMonths)
        $endDate = date('Y-m-t'); // Last day of current month
        $startDate = date('Y-m-01', strtotime("-" . ($numberOfMonths - 1) . " months"));

        $sql = "SELECT
                    DATE_FORMAT(date, '%Y-%m') as month,
                    DATE_FORMAT(date, '%b %Y') as month_name,
                    SUM(CASE WHEN type = 'income' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'income' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_income,
                    SUM(CASE WHEN type = 'expense' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'expense' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_expense
                FROM {$this->table}
                WHERE user_id = ? AND date BETWEEN ? AND ?
                GROUP BY DATE_FORMAT(date, '%Y-%m'), DATE_FORMAT(date, '%b %Y')
                ORDER BY month";

        return $this->db->fetchAll($sql, [$userId, $startDate, $endDate]);
    }

    /**
     * Get top spending categories
     */
    public function getTopSpendingCategories($userId, $startDate, $endDate, $limit = 5) {
        $sql = "SELECT
                    category,
                    SUM(CASE WHEN transaction_type = 'monetary' THEN amount
                             WHEN transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_amount
                FROM {$this->table}
                WHERE user_id = ? AND type = 'expense' AND date BETWEEN ? AND ?
                GROUP BY category
                ORDER BY total_amount DESC
                LIMIT ?";

        return $this->db->fetchAll($sql, [$userId, $startDate, $endDate, $limit]);
    }

    /**
     * Get category spending trends over time
     */
    public function getCategorySpendingTrends($userId, $year, $categories) {
        if (empty($categories)) {
            return [];
        }

        $placeholders = implode(',', array_fill(0, count($categories), '?'));
        $params = array_merge([$userId, $year], $categories);

        $sql = "SELECT
                    category,
                    DATE_FORMAT(date, '%Y-%m') as month,
                    DATE_FORMAT(date, '%b') as month_name,
                    SUM(CASE WHEN transaction_type = 'monetary' THEN amount
                             WHEN transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_amount
                FROM {$this->table}
                WHERE user_id = ? AND YEAR(date) = ? AND type = 'expense' AND category IN ($placeholders)
                GROUP BY category, DATE_FORMAT(date, '%Y-%m'), DATE_FORMAT(date, '%b')
                ORDER BY category, month";

        $results = $this->db->fetchAll($sql, $params);

        // Reorganize data by category
        $trendsByCategory = [];
        foreach ($results as $row) {
            $category = $row['category'];
            if (!isset($trendsByCategory[$category])) {
                $trendsByCategory[$category] = [];
            }
            $trendsByCategory[$category][] = $row;
        }

        return $trendsByCategory;
    }

    /**
     * Get income vs expense ratio over time
     */
    public function getIncomeExpenseRatio($userId, $period = 'monthly', $year = null) {
        if (!$year) {
            $year = date('Y');
        }

        $groupBy = '';
        $dateFormat = '';
        $orderBy = '';

        if ($period === 'monthly') {
            $groupBy = "DATE_FORMAT(date, '%Y-%m'), DATE_FORMAT(date, '%b')";
            $dateFormat = "DATE_FORMAT(date, '%Y-%m') as period, DATE_FORMAT(date, '%b') as period_name";
            $orderBy = "DATE_FORMAT(date, '%Y-%m')";
        } elseif ($period === 'quarterly') {
            $groupBy = "YEAR(date), QUARTER(date)";
            $dateFormat = "CONCAT(YEAR(date), '-Q', QUARTER(date)) as period, CONCAT('Q', QUARTER(date)) as period_name";
            $orderBy = "YEAR(date), QUARTER(date)";
        } elseif ($period === 'yearly') {
            $groupBy = "YEAR(date)";
            $dateFormat = "YEAR(date) as period, YEAR(date) as period_name";
            $orderBy = "YEAR(date)";
        }

        $sql = "SELECT
                    $dateFormat,
                    SUM(CASE WHEN type = 'income' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'income' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_income,
                    SUM(CASE WHEN type = 'expense' AND transaction_type = 'monetary' THEN amount
                             WHEN type = 'expense' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_expense,
                    CASE
                        WHEN SUM(CASE WHEN type = 'expense' THEN 1 ELSE 0 END) = 0 THEN NULL
                        ELSE SUM(CASE WHEN type = 'income' AND transaction_type = 'monetary' THEN amount
                                     WHEN type = 'income' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                                     ELSE 0 END) /
                             SUM(CASE WHEN type = 'expense' AND transaction_type = 'monetary' THEN amount
                                     WHEN type = 'expense' AND transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                                     ELSE 0 END)
                    END as income_expense_ratio
                FROM {$this->table}
                WHERE user_id = ? AND YEAR(date) = ?
                GROUP BY $groupBy
                ORDER BY $orderBy";

        return $this->db->fetchAll($sql, [$userId, $year]);
    }

    /**
     * Get payment method distribution
     */
    public function getPaymentMethodDistribution($userId, $startDate, $endDate) {
        $sql = "SELECT
                    payment_method,
                    COUNT(*) as transaction_count,
                    SUM(CASE WHEN transaction_type = 'monetary' THEN amount
                             WHEN transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_amount
                FROM {$this->table}
                WHERE user_id = ? AND type = 'expense' AND date BETWEEN ? AND ?
                GROUP BY payment_method
                ORDER BY total_amount DESC";

        return $this->db->fetchAll($sql, [$userId, $startDate, $endDate]);
    }
}

