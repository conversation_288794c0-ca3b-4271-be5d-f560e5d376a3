<?php
/**
 * Checklist Item Model
 * 
 * Handles the creation, retrieval, and management of checklist items
 */

require_once __DIR__ . '/BaseModel.php';

class ChecklistItem extends BaseModel {
    /**
     * Create a new checklist item
     * 
     * @param array $data Checklist item data
     * @return int|false The ID of the created item or false on failure
     */
    public function create($data) {
        try {
            $sql = "INSERT INTO checklist_items (
                checklist_id, 
                parent_id, 
                text, 
                description, 
                status, 
                sort_order, 
                due_date, 
                assigned_to, 
                resource_link,
                created_at, 
                updated_at
            ) VALUES (
                :checklist_id, 
                :parent_id, 
                :text, 
                :description, 
                :status, 
                :sort_order, 
                :due_date, 
                :assigned_to, 
                :resource_link,
                :created_at, 
                :updated_at
            )";
            
            $params = [
                ':checklist_id' => $data['checklist_id'],
                ':parent_id' => $data['parent_id'] ?? null,
                ':text' => $data['text'],
                ':description' => $data['description'] ?? '',
                ':status' => $data['status'] ?? 'pending',
                ':sort_order' => $data['sort_order'] ?? 0,
                ':due_date' => $data['due_date'] ?? null,
                ':assigned_to' => $data['assigned_to'] ?? null,
                ':resource_link' => $data['resource_link'] ?? null,
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ];
            
            return $this->db->insert($sql, $params);
        } catch (Exception $e) {
            error_log("Error creating checklist item: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get a checklist item by ID
     * 
     * @param int $itemId Item ID
     * @return array|false Item data or false if not found
     */
    public function getItem($itemId) {
        try {
            $sql = "SELECT * FROM checklist_items WHERE id = :id";
            return $this->db->fetchOne($sql, [':id' => $itemId]);
        } catch (Exception $e) {
            error_log("Error getting checklist item: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all items for a checklist
     * 
     * @param int $checklistId Checklist ID
     * @return array Array of checklist items organized in a hierarchy
     */
    public function getItemsByChecklist($checklistId) {
        try {
            $sql = "SELECT * FROM checklist_items WHERE checklist_id = :checklist_id ORDER BY sort_order ASC";
            $items = $this->db->fetchAll($sql, [':checklist_id' => $checklistId]);
            
            // Organize items into a hierarchy
            $itemMap = [];
            $rootItems = [];
            
            // First, map all items by their ID
            foreach ($items as $item) {
                $item['sub_items'] = [];
                $itemMap[$item['id']] = $item;
            }
            
            // Then, build the hierarchy
            foreach ($items as $item) {
                if ($item['parent_id'] === null) {
                    $rootItems[] = &$itemMap[$item['id']];
                } else {
                    if (isset($itemMap[$item['parent_id']])) {
                        $itemMap[$item['parent_id']]['sub_items'][] = &$itemMap[$item['id']];
                    }
                }
            }
            
            return $rootItems;
        } catch (Exception $e) {
            error_log("Error getting checklist items: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get all items for a checklist (flat structure)
     * 
     * @param int $checklistId Checklist ID
     * @return array Array of checklist items
     */
    public function getAllItemsFlat($checklistId) {
        try {
            $sql = "SELECT * FROM checklist_items WHERE checklist_id = :checklist_id ORDER BY sort_order ASC";
            return $this->db->fetchAll($sql, [':checklist_id' => $checklistId]);
        } catch (Exception $e) {
            error_log("Error getting checklist items: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update a checklist item
     * 
     * @param int $itemId Item ID
     * @param array $data Item data to update
     * @return bool Success or failure
     */
    public function update($itemId, $data) {
        try {
            $updateFields = [];
            $params = [':id' => $itemId];
            
            $allowedFields = ['text', 'description', 'status', 'sort_order', 'due_date', 'assigned_to', 'resource_link'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "{$field} = :{$field}";
                    $params[":{$field}"] = $data[$field];
                }
            }
            
            if (empty($updateFields)) {
                return false;
            }
            
            $updateFields[] = "updated_at = :updated_at";
            $params[':updated_at'] = date('Y-m-d H:i:s');
            
            $sql = "UPDATE checklist_items SET " . implode(', ', $updateFields) . " WHERE id = :id";
            
            return $this->db->execute($sql, $params);
        } catch (Exception $e) {
            error_log("Error updating checklist item: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update the status of a checklist item
     * 
     * @param int $itemId Item ID
     * @param string $status New status
     * @return bool Success or failure
     */
    public function updateStatus($itemId, $status) {
        try {
            $sql = "UPDATE checklist_items SET status = :status, updated_at = :updated_at WHERE id = :id";
            return $this->db->execute($sql, [
                ':id' => $itemId,
                ':status' => $status,
                ':updated_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log("Error updating checklist item status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a checklist item
     * 
     * @param int $itemId Item ID
     * @return bool Success or failure
     */
    public function delete($itemId) {
        try {
            // First, delete all child items
            $sql = "DELETE FROM checklist_items WHERE parent_id = :parent_id";
            $this->db->execute($sql, [':parent_id' => $itemId]);
            
            // Then delete the item itself
            $sql = "DELETE FROM checklist_items WHERE id = :id";
            return $this->db->execute($sql, [':id' => $itemId]);
        } catch (Exception $e) {
            error_log("Error deleting checklist item: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete all items for a checklist
     * 
     * @param int $checklistId Checklist ID
     * @return bool Success or failure
     */
    public function deleteAllItems($checklistId) {
        try {
            $sql = "DELETE FROM checklist_items WHERE checklist_id = :checklist_id";
            return $this->db->execute($sql, [':checklist_id' => $checklistId]);
        } catch (Exception $e) {
            error_log("Error deleting all checklist items: " . $e->getMessage());
            return false;
        }
    }
}
