<?php
/**
 * Budget Model
 *
 * Handles budget-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class Budget extends BaseModel {
    protected $table = 'budgets';
    protected $categoriesTable = 'budget_categories';
    protected $alertsTable = 'budget_alerts';

    public function __construct() {
        parent::__construct();
    }

    /**
     * Get active budget for a user
     */
    public function getActiveBudget($userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND is_active = 1
                AND CURDATE() BETWEEN start_date AND end_date
                ORDER BY created_at DESC
                LIMIT 1";

        return $this->db->fetchOne($sql, [$userId]);
    }

    /**
     * Get all budgets for a user
     */
    public function getUserBudgets($userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                ORDER BY start_date DESC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get budget categories for a specific budget
     */
    public function getBudgetCategories($budgetId) {
        $sql = "SELECT * FROM {$this->categoriesTable}
                WHERE budget_id = ?
                ORDER BY category ASC";

        return $this->db->fetchAll($sql, [$budgetId]);
    }

    /**
     * Get budget category by ID
     */
    public function getBudgetCategory($categoryId) {
        $sql = "SELECT * FROM {$this->categoriesTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$categoryId]);
    }

    /**
     * Create a new budget
     */
    public function createBudget($budgetData) {
        return $this->create($budgetData);
    }

    /**
     * Create a budget category
     */
    public function createBudgetCategory($categoryData) {
        $sql = "INSERT INTO {$this->categoriesTable}
                (budget_id, category, amount, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)";

        $params = [
            $categoryData['budget_id'],
            $categoryData['category'],
            $categoryData['amount'],
            $categoryData['created_at'],
            $categoryData['updated_at']
        ];

        $stmt = $this->db->query($sql, $params);

        if ($stmt) {
            // Return the last insert ID
            return $this->db->getConnection()->lastInsertId();
        }

        return false;
    }

    /**
     * Update a budget category
     */
    public function updateBudgetCategory($categoryId, $categoryData) {
        $sql = "UPDATE {$this->categoriesTable}
                SET category = ?, amount = ?, updated_at = ?
                WHERE id = ?";

        $params = [
            $categoryData['category'],
            $categoryData['amount'],
            $categoryData['updated_at'],
            $categoryId
        ];

        return $this->db->query($sql, $params);
    }

    /**
     * Delete a budget category
     */
    public function deleteBudgetCategory($categoryId) {
        $sql = "DELETE FROM {$this->categoriesTable} WHERE id = ?";
        return $this->db->query($sql, [$categoryId]);
    }

    /**
     * Create a budget alert
     */
    public function createBudgetAlert($alertData) {
        $sql = "INSERT INTO {$this->alertsTable}
                (budget_category_id, threshold_percentage, is_triggered, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)";

        $params = [
            $alertData['budget_category_id'],
            $alertData['threshold_percentage'],
            $alertData['is_triggered'] ?? false,
            $alertData['created_at'],
            $alertData['updated_at']
        ];

        return $this->db->query($sql, $params);
    }

    /**
     * Get budget alerts for a category
     */
    public function getCategoryAlerts($categoryId) {
        $sql = "SELECT * FROM {$this->alertsTable} WHERE budget_category_id = ?";
        return $this->db->fetchAll($sql, [$categoryId]);
    }

    /**
     * Get budget progress for a specific budget
     */
    public function getBudgetProgress($budgetId) {
        // Get budget details
        $budget = $this->find($budgetId);
        if (!$budget) {
            return null;
        }

        // Get budget categories
        $categories = $this->getBudgetCategories($budgetId);
        if (empty($categories)) {
            return [
                'budget' => $budget,
                'categories' => [],
                'total_budget' => 0,
                'total_spent' => 0,
                'percentage' => 0
            ];
        }

        // Calculate spending for each category
        $totalBudget = 0;
        $totalSpent = 0;
        $categoryProgress = [];

        foreach ($categories as $category) {
            $spent = $this->getCategorySpending($budget['user_id'], $category['category'], $budget['start_date'], $budget['end_date']);
            $percentage = $category['amount'] > 0 ? ($spent / $category['amount']) * 100 : 0;

            $categoryProgress[] = [
                'category' => $category,
                'spent' => $spent,
                'remaining' => $category['amount'] - $spent,
                'percentage' => $percentage
            ];

            $totalBudget += $category['amount'];
            $totalSpent += $spent;
        }

        $overallPercentage = $totalBudget > 0 ? ($totalSpent / $totalBudget) * 100 : 0;

        return [
            'budget' => $budget,
            'categories' => $categoryProgress,
            'total_budget' => $totalBudget,
            'total_spent' => $totalSpent,
            'percentage' => $overallPercentage
        ];
    }

    /**
     * Get spending for a specific category in a date range
     */
    private function getCategorySpending($userId, $category, $startDate, $endDate) {
        $sql = "SELECT
                    SUM(CASE WHEN transaction_type = 'monetary' THEN amount
                             WHEN transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_amount
                FROM finances
                WHERE user_id = ? AND type = 'expense' AND category = ? AND date BETWEEN ? AND ?";

        $result = $this->db->fetchOne($sql, [$userId, $category, $startDate, $endDate]);
        return (float)($result['total_amount'] ?? 0);
    }

    /**
     * Check for budget alerts that need to be triggered
     */
    public function checkBudgetAlerts($userId) {
        // Get active budget
        $activeBudget = $this->getActiveBudget($userId);
        if (!$activeBudget) {
            return [];
        }

        // Get budget progress
        $progress = $this->getBudgetProgress($activeBudget['id']);
        if (empty($progress['categories'])) {
            return [];
        }

        $triggeredAlerts = [];

        // Check each category for alerts
        foreach ($progress['categories'] as $categoryProgress) {
            $category = $categoryProgress['category'];
            $percentage = $categoryProgress['percentage'];

            // Get alerts for this category
            $alerts = $this->getCategoryAlerts($category['id']);

            foreach ($alerts as $alert) {
                // If percentage exceeds threshold and alert is not already triggered
                if ($percentage >= $alert['threshold_percentage'] && !$alert['is_triggered']) {
                    // Mark alert as triggered
                    $this->updateAlertStatus($alert['id'], true);

                    $triggeredAlerts[] = [
                        'category' => $category['category'],
                        'threshold' => $alert['threshold_percentage'],
                        'current_percentage' => $percentage,
                        'budget_amount' => $category['amount'],
                        'spent_amount' => $categoryProgress['spent']
                    ];
                }
            }
        }

        return $triggeredAlerts;
    }

    /**
     * Update alert triggered status
     */
    private function updateAlertStatus($alertId, $isTriggered) {
        $sql = "UPDATE {$this->alertsTable}
                SET is_triggered = ?, updated_at = ?
                WHERE id = ?";

        return $this->db->query($sql, [$isTriggered ? 1 : 0, date('Y-m-d H:i:s'), $alertId]);
    }

    /**
     * Get historical budget data for a user
     */
    public function getHistoricalBudgetData($userId, $limit = 6) {
        // Get completed budgets (end date in the past)
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND end_date < CURDATE()
                ORDER BY end_date DESC
                LIMIT ?";

        $budgets = $this->db->fetchAll($sql, [$userId, $limit]);

        if (empty($budgets)) {
            return [];
        }

        $historicalData = [];

        foreach ($budgets as $budget) {
            $budgetProgress = $this->getBudgetProgress($budget['id']);

            $historicalData[] = [
                'budget' => $budget,
                'progress' => $budgetProgress
            ];
        }

        return $historicalData;
    }

    /**
     * Get category spending trends
     */
    public function getCategorySpendingTrends($userId, $months = 6) {
        // Get all unique categories used in budgets
        $sql = "SELECT DISTINCT bc.category
                FROM {$this->categoriesTable} bc
                JOIN {$this->table} b ON bc.budget_id = b.id
                WHERE b.user_id = ?
                ORDER BY bc.category";

        $categories = $this->db->fetchAll($sql, [$userId]);

        if (empty($categories)) {
            return [];
        }

        $categories = array_column($categories, 'category');
        $trends = [];

        // Get spending for each month for each category
        $endDate = date('Y-m-d');
        $startDate = date('Y-m-d', strtotime("-{$months} months"));

        foreach ($categories as $category) {
            $sql = "SELECT
                        DATE_FORMAT(date, '%Y-%m') as month,
                        SUM(CASE WHEN transaction_type = 'monetary' THEN amount
                                 WHEN transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                                 ELSE 0 END) as total_amount
                    FROM finances
                    WHERE user_id = ? AND type = 'expense' AND category = ?
                    AND date BETWEEN ? AND ?
                    GROUP BY DATE_FORMAT(date, '%Y-%m')
                    ORDER BY month";

            $monthlySpending = $this->db->fetchAll($sql, [$userId, $category, $startDate, $endDate]);

            $trends[$category] = $monthlySpending;
        }

        return $trends;
    }

    /**
     * Get budget performance metrics
     */
    public function getBudgetPerformanceMetrics($userId) {
        // Get completed budgets
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND end_date < CURDATE()
                ORDER BY end_date DESC";

        $budgets = $this->db->fetchAll($sql, [$userId]);

        if (empty($budgets)) {
            return [
                'total_budgets' => 0,
                'under_budget_count' => 0,
                'over_budget_count' => 0,
                'under_budget_percentage' => 0,
                'average_budget_utilization' => 0,
                'categories_most_over_budget' => [],
                'categories_most_under_budget' => []
            ];
        }

        $totalBudgets = count($budgets);
        $underBudgetCount = 0;
        $totalUtilization = 0;
        $categoryPerformance = [];

        foreach ($budgets as $budget) {
            $progress = $this->getBudgetProgress($budget['id']);

            // Count under budget
            if ($progress['percentage'] <= 100) {
                $underBudgetCount++;
            }

            // Add to total utilization
            $totalUtilization += $progress['percentage'];

            // Track category performance
            foreach ($progress['categories'] as $category) {
                $categoryName = $category['category']['category'];

                if (!isset($categoryPerformance[$categoryName])) {
                    $categoryPerformance[$categoryName] = [
                        'count' => 0,
                        'total_percentage' => 0,
                        'over_budget_count' => 0
                    ];
                }

                $categoryPerformance[$categoryName]['count']++;
                $categoryPerformance[$categoryName]['total_percentage'] += $category['percentage'];

                if ($category['percentage'] > 100) {
                    $categoryPerformance[$categoryName]['over_budget_count']++;
                }
            }
        }

        // Calculate average utilization
        $averageUtilization = $totalBudgets > 0 ? $totalUtilization / $totalBudgets : 0;

        // Calculate category averages
        $categoryAverages = [];
        foreach ($categoryPerformance as $category => $data) {
            $categoryAverages[$category] = [
                'average_percentage' => $data['count'] > 0 ? $data['total_percentage'] / $data['count'] : 0,
                'over_budget_rate' => $data['count'] > 0 ? ($data['over_budget_count'] / $data['count']) * 100 : 0
            ];
        }

        // Sort categories by average percentage
        uasort($categoryAverages, function($a, $b) {
            return $b['average_percentage'] - $a['average_percentage'];
        });

        // Get top 5 most over and under budget
        $mostOverBudget = array_slice($categoryAverages, 0, 5);
        $mostUnderBudget = array_slice(array_reverse($categoryAverages, true), 0, 5);

        return [
            'total_budgets' => $totalBudgets,
            'under_budget_count' => $underBudgetCount,
            'over_budget_count' => $totalBudgets - $underBudgetCount,
            'under_budget_percentage' => $totalBudgets > 0 ? ($underBudgetCount / $totalBudgets) * 100 : 0,
            'average_budget_utilization' => $averageUtilization,
            'categories_most_over_budget' => $mostOverBudget,
            'categories_most_under_budget' => $mostUnderBudget
        ];
    }

    /**
     * Get budget performance history
     *
     * @param int $userId User ID
     * @param int $limit Maximum number of budgets to return
     * @return array Budget performance history
     */
    public function getBudgetPerformanceHistory($userId, $limit = 6) {
        // Get completed and active budgets
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                ORDER BY end_date DESC
                LIMIT ?";

        $budgets = $this->db->fetchAll($sql, [$userId, $limit]);

        if (empty($budgets)) {
            return [];
        }

        $history = [];

        foreach ($budgets as $budget) {
            $progress = $this->getBudgetProgress($budget['id']);

            // Skip if progress couldn't be calculated
            if (!$progress) {
                continue;
            }

            $history[] = [
                'id' => $budget['id'],
                'name' => $budget['name'],
                'start_date' => $budget['start_date'],
                'end_date' => $budget['end_date'],
                'is_active' => $budget['is_active'],
                'total_budget' => $progress['total_budget'],
                'total_spent' => $progress['total_spent'],
                'percentage' => $progress['percentage'],
                'status' => $this->getBudgetStatus($progress['percentage'], $budget['end_date'])
            ];
        }

        return $history;
    }

    /**
     * Get budget status based on percentage and end date
     *
     * @param float $percentage Budget utilization percentage
     * @param string $endDate Budget end date
     * @return string Budget status
     */
    private function getBudgetStatus($percentage, $endDate) {
        $today = date('Y-m-d');

        if ($endDate < $today) {
            // Budget is completed
            if ($percentage <= 100) {
                return 'completed_under';
            } else {
                return 'completed_over';
            }
        } else {
            // Budget is active
            if ($percentage <= 85) {
                return 'active_good';
            } elseif ($percentage <= 100) {
                return 'active_warning';
            } else {
                return 'active_over';
            }
        }
    }

    /**
     * Get category performance for a specific year
     *
     * @param int $userId User ID
     * @param int $year Year to analyze
     * @return array Category performance data
     */
    public function getCategoryPerformanceForYear($userId, $year) {
        // Get budgets for the year
        $startDate = "$year-01-01";
        $endDate = "$year-12-31";

        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND
                ((start_date BETWEEN ? AND ?) OR
                 (end_date BETWEEN ? AND ?) OR
                 (start_date <= ? AND end_date >= ?))
                ORDER BY start_date";

        $budgets = $this->db->fetchAll($sql, [
            $userId, $startDate, $endDate, $startDate, $endDate, $startDate, $endDate
        ]);

        if (empty($budgets)) {
            return [];
        }

        $categoryPerformance = [];

        foreach ($budgets as $budget) {
            $progress = $this->getBudgetProgress($budget['id']);

            if (empty($progress['categories'])) {
                continue;
            }

            foreach ($progress['categories'] as $category) {
                $categoryName = $category['category']['category'];

                if (!isset($categoryPerformance[$categoryName])) {
                    $categoryPerformance[$categoryName] = [
                        'category' => $categoryName,
                        'budgets' => 0,
                        'total_allocated' => 0,
                        'total_spent' => 0,
                        'average_percentage' => 0,
                        'over_budget_count' => 0
                    ];
                }

                $categoryPerformance[$categoryName]['budgets']++;
                $categoryPerformance[$categoryName]['total_allocated'] += $category['category']['amount'];
                $categoryPerformance[$categoryName]['total_spent'] += $category['spent'];

                if ($category['percentage'] > 100) {
                    $categoryPerformance[$categoryName]['over_budget_count']++;
                }
            }
        }

        // Calculate averages and percentages
        foreach ($categoryPerformance as $category => $data) {
            $categoryPerformance[$category]['average_percentage'] =
                $data['total_allocated'] > 0 ?
                ($data['total_spent'] / $data['total_allocated']) * 100 : 0;

            $categoryPerformance[$category]['over_budget_rate'] =
                $data['budgets'] > 0 ?
                ($data['over_budget_count'] / $data['budgets']) * 100 : 0;
        }

        // Sort by average percentage (highest first)
        uasort($categoryPerformance, function($a, $b) {
            return $b['average_percentage'] - $a['average_percentage'];
        });

        return array_values($categoryPerformance);
    }

    /**
     * Get monthly performance for a specific year
     *
     * @param int $userId User ID
     * @param int $year Year to analyze
     * @return array Monthly performance data
     */
    public function getMonthlyPerformanceForYear($userId, $year) {
        $monthlyPerformance = [];

        // Initialize monthly data
        for ($month = 1; $month <= 12; $month++) {
            $monthName = date('F', mktime(0, 0, 0, $month, 1, $year));
            $monthlyPerformance[$month] = [
                'month' => $month,
                'month_name' => $monthName,
                'total_allocated' => 0,
                'total_spent' => 0,
                'percentage' => 0,
                'status' => 'no_data'
            ];
        }

        // Get budgets for the year
        $startDate = "$year-01-01";
        $endDate = "$year-12-31";

        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND
                ((start_date BETWEEN ? AND ?) OR
                 (end_date BETWEEN ? AND ?) OR
                 (start_date <= ? AND end_date >= ?))
                ORDER BY start_date";

        $budgets = $this->db->fetchAll($sql, [
            $userId, $startDate, $endDate, $startDate, $endDate, $startDate, $endDate
        ]);

        if (empty($budgets)) {
            return array_values($monthlyPerformance);
        }

        // Process each budget
        foreach ($budgets as $budget) {
            $budgetStart = new DateTime($budget['start_date']);
            $budgetEnd = new DateTime($budget['end_date']);

            // Skip if budget is not in the requested year
            if ($budgetStart->format('Y') != $year && $budgetEnd->format('Y') != $year) {
                continue;
            }

            // Get budget progress
            $progress = $this->getBudgetProgress($budget['id']);

            if (empty($progress)) {
                continue;
            }

            // Calculate monthly allocation based on budget duration
            $budgetDuration = $budgetStart->diff($budgetEnd)->days + 1;
            $dailyAllocation = $progress['total_budget'] / $budgetDuration;

            // Distribute budget and spending across months
            $currentDate = clone $budgetStart;

            while ($currentDate <= $budgetEnd) {
                $currentMonth = (int)$currentDate->format('n');
                $currentYear = (int)$currentDate->format('Y');

                // Only process months in the requested year
                if ($currentYear == $year) {
                    $daysInMonth = (int)$currentDate->format('t');
                    $startDay = ($currentDate->format('Y-m') == $budgetStart->format('Y-m')) ?
                        (int)$budgetStart->format('j') : 1;
                    $endDay = ($currentDate->format('Y-m') == $budgetEnd->format('Y-m')) ?
                        (int)$budgetEnd->format('j') : $daysInMonth;

                    $daysInBudget = $endDay - $startDay + 1;
                    $monthAllocation = $dailyAllocation * $daysInBudget;

                    // Add to monthly data
                    $monthlyPerformance[$currentMonth]['total_allocated'] += $monthAllocation;

                    // Estimate spending based on overall budget percentage
                    $monthSpending = $monthAllocation * ($progress['percentage'] / 100);
                    $monthlyPerformance[$currentMonth]['total_spent'] += $monthSpending;
                }

                // Move to next month
                $currentDate->modify('+1 month');
                $currentDate->setDate($currentDate->format('Y'), $currentDate->format('m'), 1);
            }
        }

        // Calculate percentages and status
        foreach ($monthlyPerformance as $month => $data) {
            if ($data['total_allocated'] > 0) {
                $percentage = ($data['total_spent'] / $data['total_allocated']) * 100;
                $monthlyPerformance[$month]['percentage'] = $percentage;

                // Determine status
                if ($percentage <= 85) {
                    $monthlyPerformance[$month]['status'] = 'under_budget';
                } elseif ($percentage <= 100) {
                    $monthlyPerformance[$month]['status'] = 'near_budget';
                } else {
                    $monthlyPerformance[$month]['status'] = 'over_budget';
                }
            }
        }

        return array_values($monthlyPerformance);
    }
}
