<?php
/**
 * Financial Insight Model
 *
 * Handles AI-powered financial insights, pattern detection, and recommendations.
 */

require_once __DIR__ . '/BaseModel.php';
require_once __DIR__ . '/Finance.php';
require_once __DIR__ . '/Budget.php';

class FinancialInsight extends BaseModel {
    protected $table = 'finances'; // We'll use the same table but with specialized queries
    private $financeModel;
    private $budgetModel;

    public function __construct() {
        parent::__construct();
        $this->financeModel = new Finance();
        $this->budgetModel = new Budget();
    }

    /**
     * Detect spending patterns for a user
     *
     * @param int $userId User ID
     * @param string $startDate Start date for analysis
     * @param string $endDate End date for analysis
     * @param int $minTransactions Minimum transactions required for pattern detection
     * @return array Detected spending patterns
     */
    public function detectSpendingPatterns($userId, $startDate, $endDate, $minTransactions = 3) {
        $patterns = [];

        // Get all expense transactions for the period
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND type = 'expense' AND date BETWEEN ? AND ?
                ORDER BY date ASC";

        $transactions = $this->db->fetchAll($sql, [$userId, $startDate, $endDate]);

        if (count($transactions) < $minTransactions) {
            return $patterns;
        }

        // Pattern 1: Recurring expenses (same amount, same category, regular intervals)
        $patterns['recurring'] = $this->detectRecurringExpenses($transactions, $minTransactions);

        // Pattern 2: Category spending trends (increasing or decreasing)
        $patterns['category_trends'] = $this->detectCategoryTrends($userId, $startDate, $endDate);

        // Pattern 3: Day of week spending patterns
        $patterns['day_of_week'] = $this->detectDayOfWeekPatterns($transactions);

        // Pattern 4: Impulse spending (small, frequent purchases)
        $patterns['impulse'] = $this->detectImpulseSpending($transactions);

        return $patterns;
    }

    /**
     * Detect recurring expenses
     *
     * @param array $transactions List of transactions
     * @param int $minTransactions Minimum transactions required for pattern detection
     * @return array Recurring expense patterns
     */
    private function detectRecurringExpenses($transactions, $minTransactions) {
        $recurringExpenses = [];
        $potentialRecurring = [];

        // Group transactions by category and amount
        foreach ($transactions as $transaction) {
            $amount = $transaction['transaction_type'] === 'monetary' ?
                      $transaction['amount'] :
                      ($transaction['fair_market_value'] ?? $transaction['amount']);

            $key = $transaction['category'] . '_' . $amount;

            if (!isset($potentialRecurring[$key])) {
                $potentialRecurring[$key] = [
                    'category' => $transaction['category'],
                    'amount' => $amount,
                    'transactions' => []
                ];
            }

            $potentialRecurring[$key]['transactions'][] = [
                'id' => $transaction['id'],
                'date' => $transaction['date'],
                'description' => $transaction['description']
            ];
        }

        // Analyze each potential recurring expense
        foreach ($potentialRecurring as $key => $data) {
            if (count($data['transactions']) >= $minTransactions) {
                // Sort transactions by date
                usort($data['transactions'], function($a, $b) {
                    return strtotime($a['date']) - strtotime($b['date']);
                });

                // Calculate intervals between transactions
                $intervals = [];
                for ($i = 1; $i < count($data['transactions']); $i++) {
                    $prevDate = strtotime($data['transactions'][$i-1]['date']);
                    $currDate = strtotime($data['transactions'][$i]['date']);
                    $intervals[] = round(($currDate - $prevDate) / (60 * 60 * 24)); // Days
                }

                // Check if intervals are consistent (allow some variance)
                if (count($intervals) >= 2) {
                    $avgInterval = array_sum($intervals) / count($intervals);
                    $isConsistent = true;

                    foreach ($intervals as $interval) {
                        // Allow 20% variance from average interval
                        if (abs($interval - $avgInterval) > ($avgInterval * 0.2)) {
                            $isConsistent = false;
                            break;
                        }
                    }

                    if ($isConsistent) {
                        $recurringExpenses[] = [
                            'category' => $data['category'],
                            'amount' => $data['amount'],
                            'interval_days' => round($avgInterval),
                            'frequency' => $this->determineFrequency($avgInterval),
                            'transaction_count' => count($data['transactions']),
                            'last_date' => $data['transactions'][count($data['transactions'])-1]['date'],
                            'next_expected' => date('Y-m-d', strtotime($data['transactions'][count($data['transactions'])-1]['date'] . ' + ' . round($avgInterval) . ' days')),
                            'description' => $data['transactions'][0]['description']
                        ];
                    }
                }
            }
        }

        return $recurringExpenses;
    }

    /**
     * Determine frequency label based on interval days
     */
    private function determineFrequency($intervalDays) {
        if ($intervalDays <= 7) {
            return 'weekly';
        } elseif ($intervalDays <= 14) {
            return 'bi-weekly';
        } elseif ($intervalDays >= 25 && $intervalDays <= 35) {
            return 'monthly';
        } elseif ($intervalDays >= 85 && $intervalDays <= 95) {
            return 'quarterly';
        } elseif ($intervalDays >= 350 && $intervalDays <= 380) {
            return 'yearly';
        } else {
            return 'every ' . $intervalDays . ' days';
        }
    }

    /**
     * Detect category spending trends
     */
    private function detectCategoryTrends($userId, $startDate, $endDate) {
        $trends = [];

        // Get monthly spending by category
        $sql = "SELECT
                    category,
                    DATE_FORMAT(date, '%Y-%m') as month,
                    SUM(CASE WHEN transaction_type = 'monetary' THEN amount
                             WHEN transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_amount
                FROM {$this->table}
                WHERE user_id = ? AND type = 'expense' AND date BETWEEN ? AND ?
                GROUP BY category, DATE_FORMAT(date, '%Y-%m')
                ORDER BY category, month";

        $results = $this->db->fetchAll($sql, [$userId, $startDate, $endDate]);

        // Organize data by category
        $categoryData = [];
        foreach ($results as $row) {
            if (!isset($categoryData[$row['category']])) {
                $categoryData[$row['category']] = [];
            }
            $categoryData[$row['category']][$row['month']] = $row['total_amount'];
        }

        // Analyze trends for each category
        foreach ($categoryData as $category => $monthlyData) {
            if (count($monthlyData) >= 3) { // Need at least 3 months of data
                $months = array_keys($monthlyData);
                $amounts = array_values($monthlyData);

                // Calculate trend using linear regression
                $n = count($months);
                $xValues = range(1, $n);
                $sumX = array_sum($xValues);
                $sumY = array_sum($amounts);
                $sumXY = 0;
                $sumXX = 0;

                for ($i = 0; $i < $n; $i++) {
                    $sumXY += $xValues[$i] * $amounts[$i];
                    $sumXX += $xValues[$i] * $xValues[$i];
                }

                $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumXX - $sumX * $sumX);

                // Calculate average monthly spending
                $avgSpending = $sumY / $n;

                // Calculate percent change
                $percentChange = ($avgSpending > 0) ? ($slope / $avgSpending) * 100 : 0;

                // Only include significant trends
                if (abs($percentChange) >= 5) {
                    $trends[] = [
                        'category' => $category,
                        'direction' => $slope > 0 ? 'increasing' : 'decreasing',
                        'percent_change' => abs($percentChange),
                        'avg_monthly' => $avgSpending,
                        'months_analyzed' => $n,
                        'first_month' => $months[0],
                        'last_month' => $months[$n-1]
                    ];
                }
            }
        }

        // Sort by percent change (descending)
        usort($trends, function($a, $b) {
            return $b['percent_change'] - $a['percent_change'];
        });

        return $trends;
    }

    /**
     * Detect day of week spending patterns
     */
    private function detectDayOfWeekPatterns($transactions) {
        $daySpending = [
            'Monday' => 0,
            'Tuesday' => 0,
            'Wednesday' => 0,
            'Thursday' => 0,
            'Friday' => 0,
            'Saturday' => 0,
            'Sunday' => 0
        ];

        $dayCount = array_fill_keys(array_keys($daySpending), 0);

        foreach ($transactions as $transaction) {
            $dayOfWeek = date('l', strtotime($transaction['date']));
            $amount = $transaction['transaction_type'] === 'monetary' ?
                      $transaction['amount'] :
                      ($transaction['fair_market_value'] ?? $transaction['amount']);

            $daySpending[$dayOfWeek] += $amount;
            $dayCount[$dayOfWeek]++;
        }

        // Calculate average spending per day
        $avgDaySpending = [];
        foreach ($daySpending as $day => $total) {
            $avgDaySpending[$day] = $dayCount[$day] > 0 ? $total / $dayCount[$day] : 0;
        }

        // Calculate overall average
        $totalSpending = array_sum($daySpending);
        $totalDays = array_sum($dayCount);
        $overallAvg = $totalDays > 0 ? $totalSpending / $totalDays : 0;

        // Find days with significantly higher spending
        $highSpendingDays = [];
        foreach ($avgDaySpending as $day => $avg) {
            if ($avg > ($overallAvg * 1.25) && $dayCount[$day] >= 3) { // 25% higher than average
                $highSpendingDays[] = [
                    'day' => $day,
                    'avg_spending' => $avg,
                    'percent_above_average' => (($avg / $overallAvg) - 1) * 100,
                    'transaction_count' => $dayCount[$day]
                ];
            }
        }

        // Sort by percent above average (descending)
        usort($highSpendingDays, function($a, $b) {
            return $b['percent_above_average'] - $a['percent_above_average'];
        });

        return $highSpendingDays;
    }

    /**
     * Detect impulse spending (small, frequent purchases)
     */
    private function detectImpulseSpending($transactions) {
        $smallPurchases = [];
        $categoryCount = [];
        $categoryTotal = [];

        // Define small purchase threshold (adjust as needed)
        $smallThreshold = 20; // Amount considered "small"

        foreach ($transactions as $transaction) {
            $amount = $transaction['transaction_type'] === 'monetary' ?
                      $transaction['amount'] :
                      ($transaction['fair_market_value'] ?? $transaction['amount']);

            if ($amount <= $smallThreshold) {
                $category = $transaction['category'];

                if (!isset($categoryCount[$category])) {
                    $categoryCount[$category] = 0;
                    $categoryTotal[$category] = 0;
                }

                $categoryCount[$category]++;
                $categoryTotal[$category] += $amount;

                $smallPurchases[] = [
                    'id' => $transaction['id'],
                    'date' => $transaction['date'],
                    'amount' => $amount,
                    'category' => $category,
                    'description' => $transaction['description']
                ];
            }
        }

        // Find categories with frequent small purchases
        $impulseCategories = [];
        foreach ($categoryCount as $category => $count) {
            if ($count >= 5) { // At least 5 small purchases in this category
                $impulseCategories[] = [
                    'category' => $category,
                    'transaction_count' => $count,
                    'total_amount' => $categoryTotal[$category],
                    'avg_amount' => $categoryTotal[$category] / $count
                ];
            }
        }

        // Sort by transaction count (descending)
        usort($impulseCategories, function($a, $b) {
            return $b['transaction_count'] - $a['transaction_count'];
        });

        return [
            'categories' => $impulseCategories,
            'transactions' => $smallPurchases
        ];
    }

    /**
     * Detect anomalies in spending
     *
     * @param int $userId User ID
     * @param string $startDate Start date for analysis
     * @param string $endDate End date for analysis
     * @param float $thresholdPercent Percentage threshold for anomaly detection
     * @return array Detected anomalies
     */
    public function detectSpendingAnomalies($userId, $startDate, $endDate, $thresholdPercent = 50) {
        $anomalies = [];

        // Get monthly spending by category
        $sql = "SELECT
                    category,
                    DATE_FORMAT(date, '%Y-%m') as month,
                    SUM(CASE WHEN transaction_type = 'monetary' THEN amount
                             WHEN transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as total_amount
                FROM {$this->table}
                WHERE user_id = ? AND type = 'expense' AND date BETWEEN ? AND ?
                GROUP BY category, DATE_FORMAT(date, '%Y-%m')
                ORDER BY category, month";

        $results = $this->db->fetchAll($sql, [$userId, $startDate, $endDate]);

        // Organize data by category
        $categoryData = [];
        foreach ($results as $row) {
            if (!isset($categoryData[$row['category']])) {
                $categoryData[$row['category']] = [];
            }
            $categoryData[$row['category']][$row['month']] = $row['total_amount'];
        }

        // Analyze each category for anomalies
        foreach ($categoryData as $category => $monthlyData) {
            if (count($monthlyData) >= 3) { // Need at least 3 months of data
                $months = array_keys($monthlyData);
                $amounts = array_values($monthlyData);

                // Calculate average and standard deviation
                $avgSpending = array_sum($amounts) / count($amounts);
                $variance = 0;

                foreach ($amounts as $amount) {
                    $variance += pow($amount - $avgSpending, 2);
                }

                $stdDev = sqrt($variance / count($amounts));

                // Find months with anomalous spending
                $categoryAnomalies = [];
                foreach ($monthlyData as $month => $amount) {
                    $percentDiff = (($amount - $avgSpending) / $avgSpending) * 100;

                    // Check if spending is significantly higher than average
                    if ($percentDiff > $thresholdPercent) {
                        $categoryAnomalies[] = [
                            'month' => $month,
                            'amount' => $amount,
                            'avg_amount' => $avgSpending,
                            'percent_increase' => $percentDiff,
                            'std_deviations' => ($amount - $avgSpending) / ($stdDev > 0 ? $stdDev : 1)
                        ];
                    }
                }

                if (!empty($categoryAnomalies)) {
                    // Sort by percent increase (descending)
                    usort($categoryAnomalies, function($a, $b) {
                        return $b['percent_increase'] - $a['percent_increase'];
                    });

                    $anomalies[$category] = $categoryAnomalies;
                }
            }
        }

        // Get individual transaction anomalies
        $transactionAnomalies = $this->detectTransactionAnomalies($userId, $startDate, $endDate);

        return [
            'category_anomalies' => $anomalies,
            'transaction_anomalies' => $transactionAnomalies
        ];
    }

    /**
     * Detect anomalies in individual transactions
     */
    private function detectTransactionAnomalies($userId, $startDate, $endDate) {
        $anomalies = [];

        // Get average spending by category
        $sql = "SELECT
                    category,
                    AVG(CASE WHEN transaction_type = 'monetary' THEN amount
                             WHEN transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as avg_amount,
                    STDDEV(CASE WHEN transaction_type = 'monetary' THEN amount
                             WHEN transaction_type = 'non_monetary' THEN COALESCE(fair_market_value, 0)
                             ELSE 0 END) as std_dev
                FROM {$this->table}
                WHERE user_id = ? AND type = 'expense' AND date BETWEEN ? AND ?
                GROUP BY category";

        $categoryStats = $this->db->fetchAll($sql, [$userId, $startDate, $endDate]);

        // Convert to associative array
        $categoryAvg = [];
        $categoryStdDev = [];
        foreach ($categoryStats as $stat) {
            $categoryAvg[$stat['category']] = $stat['avg_amount'];
            $categoryStdDev[$stat['category']] = $stat['std_dev'] > 0 ? $stat['std_dev'] : 1; // Avoid division by zero
        }

        // Get all transactions
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND type = 'expense' AND date BETWEEN ? AND ?
                ORDER BY date DESC";

        $transactions = $this->db->fetchAll($sql, [$userId, $startDate, $endDate]);

        // Check each transaction for anomalies
        foreach ($transactions as $transaction) {
            $category = $transaction['category'];
            $amount = $transaction['transaction_type'] === 'monetary' ?
                      $transaction['amount'] :
                      ($transaction['fair_market_value'] ?? $transaction['amount']);

            if (isset($categoryAvg[$category])) {
                $avgAmount = $categoryAvg[$category];
                $stdDev = $categoryStdDev[$category];

                // Calculate z-score (number of standard deviations from mean)
                $zScore = ($amount - $avgAmount) / $stdDev;

                // Check if transaction is significantly higher than average (z-score > 2)
                if ($zScore > 2 && $amount > $avgAmount * 1.5) {
                    $anomalies[] = [
                        'id' => $transaction['id'],
                        'date' => $transaction['date'],
                        'category' => $category,
                        'amount' => $amount,
                        'avg_amount' => $avgAmount,
                        'percent_above_avg' => (($amount / $avgAmount) - 1) * 100,
                        'z_score' => $zScore,
                        'description' => $transaction['description']
                    ];
                }
            }
        }

        // Sort by z-score (descending)
        usort($anomalies, function($a, $b) {
            return $b['z_score'] - $a['z_score'];
        });

        return $anomalies;
    }

    /**
     * Generate expense reduction recommendations
     *
     * @param int $userId User ID
     * @param string $startDate Start date for analysis
     * @param string $endDate End date for analysis
     * @return array Expense reduction recommendations
     */
    public function generateExpenseReductionRecommendations($userId, $startDate, $endDate) {
        $recommendations = [];

        // Get spending patterns
        $patterns = $this->detectSpendingPatterns($userId, $startDate, $endDate);

        // Get anomalies
        $anomalies = $this->detectSpendingAnomalies($userId, $startDate, $endDate);

        // Get budget information
        $budgets = $this->budgetModel->getUserBudgets($userId);
        $activeBudget = null;
        foreach ($budgets as $budget) {
            if ($budget['is_active']) {
                $activeBudget = $budget;
                break;
            }
        }

        // 1. Recommendations based on recurring expenses
        if (!empty($patterns['recurring'])) {
            $recurringRecommendations = [];

            foreach ($patterns['recurring'] as $recurring) {
                // Check if this is a subscription or service that could be optimized
                $potentialSavingsCategories = ['Entertainment', 'Subscriptions', 'Utilities', 'Services'];

                if (in_array($recurring['category'], $potentialSavingsCategories)) {
                    $annualCost = $recurring['amount'];

                    // Convert to annual cost based on frequency
                    if ($recurring['frequency'] === 'weekly') {
                        $annualCost *= 52;
                    } elseif ($recurring['frequency'] === 'bi-weekly') {
                        $annualCost *= 26;
                    } elseif ($recurring['frequency'] === 'monthly') {
                        $annualCost *= 12;
                    } elseif ($recurring['frequency'] === 'quarterly') {
                        $annualCost *= 4;
                    }

                    // Only suggest for significant annual costs
                    if ($annualCost >= 100) {
                        $recurringRecommendations[] = [
                            'type' => 'recurring_expense',
                            'category' => $recurring['category'],
                            'description' => $recurring['description'],
                            'amount' => $recurring['amount'],
                            'frequency' => $recurring['frequency'],
                            'annual_cost' => $annualCost,
                            'recommendation' => "Consider reviewing your " . strtolower($recurring['frequency']) . " " .
                                               strtolower($recurring['category']) . " expense of " .
                                               number_format($recurring['amount'], 2) . " (" .
                                               number_format($annualCost, 2) . " annually). " .
                                               "Look for cheaper alternatives or negotiate a better rate."
                        ];
                    }
                }
            }

            if (!empty($recurringRecommendations)) {
                // Sort by annual cost (descending)
                usort($recurringRecommendations, function($a, $b) {
                    return $b['annual_cost'] - $a['annual_cost'];
                });

                $recommendations['recurring_expenses'] = $recurringRecommendations;
            }
        }

        // 2. Recommendations based on category trends
        if (!empty($patterns['category_trends'])) {
            $trendRecommendations = [];

            foreach ($patterns['category_trends'] as $trend) {
                if ($trend['direction'] === 'increasing' && $trend['percent_change'] >= 10) {
                    $trendRecommendations[] = [
                        'type' => 'increasing_category',
                        'category' => $trend['category'],
                        'percent_change' => $trend['percent_change'],
                        'avg_monthly' => $trend['avg_monthly'],
                        'recommendation' => "Your spending in " . $trend['category'] . " has increased by " .
                                           number_format($trend['percent_change'], 1) . "%. " .
                                           "Consider setting a budget for this category and finding ways to reduce expenses."
                    ];
                }
            }

            if (!empty($trendRecommendations)) {
                $recommendations['category_trends'] = $trendRecommendations;
            }
        }

        // 3. Recommendations based on impulse spending
        if (!empty($patterns['impulse']['categories'])) {
            $impulseRecommendations = [];

            foreach ($patterns['impulse']['categories'] as $impulse) {
                if ($impulse['transaction_count'] >= 8) { // Significant number of small purchases
                    $impulseRecommendations[] = [
                        'type' => 'impulse_spending',
                        'category' => $impulse['category'],
                        'transaction_count' => $impulse['transaction_count'],
                        'total_amount' => $impulse['total_amount'],
                        'recommendation' => "You made " . $impulse['transaction_count'] . " small purchases in " .
                                           $impulse['category'] . " totaling " . number_format($impulse['total_amount'], 2) . ". " .
                                           "These small expenses add up. Consider setting a weekly limit for " .
                                           $impulse['category'] . " purchases."
                    ];
                }
            }

            if (!empty($impulseRecommendations)) {
                $recommendations['impulse_spending'] = $impulseRecommendations;
            }
        }

        // 4. Recommendations based on day of week patterns
        if (!empty($patterns['day_of_week'])) {
            $dayRecommendations = [];

            foreach ($patterns['day_of_week'] as $day) {
                if ($day['percent_above_average'] >= 30) { // Significantly higher spending
                    $dayRecommendations[] = [
                        'type' => 'day_of_week',
                        'day' => $day['day'],
                        'avg_spending' => $day['avg_spending'],
                        'percent_above_average' => $day['percent_above_average'],
                        'recommendation' => "You tend to spend " . number_format($day['percent_above_average'], 1) . "% more on " .
                                           $day['day'] . "s compared to other days. " .
                                           "Be mindful of your spending habits on this day of the week."
                    ];
                }
            }

            if (!empty($dayRecommendations)) {
                $recommendations['day_of_week'] = $dayRecommendations;
            }
        }

        // 5. Recommendations based on anomalies
        if (!empty($anomalies['category_anomalies'])) {
            $anomalyRecommendations = [];

            foreach ($anomalies['category_anomalies'] as $category => $categoryAnomalies) {
                foreach ($categoryAnomalies as $anomaly) {
                    if ($anomaly['percent_increase'] >= 70) { // Very significant increase
                        $anomalyRecommendations[] = [
                            'type' => 'spending_anomaly',
                            'category' => $category,
                            'month' => $anomaly['month'],
                            'amount' => $anomaly['amount'],
                            'percent_increase' => $anomaly['percent_increase'],
                            'recommendation' => "Your " . $category . " spending in " . date('F Y', strtotime($anomaly['month'] . '-01')) .
                                               " was " . number_format($anomaly['percent_increase'], 1) . "% higher than your average. " .
                                               "Review what caused this spike and consider if it can be avoided in the future."
                        ];
                    }
                }
            }

            if (!empty($anomalyRecommendations)) {
                $recommendations['spending_anomalies'] = $anomalyRecommendations;
            }
        }

        // 6. Budget-based recommendations
        if ($activeBudget) {
            $budgetProgress = $this->budgetModel->getBudgetProgress($activeBudget['id']);
            $budgetRecommendations = [];

            if (!empty($budgetProgress) && !empty($budgetProgress['categories'])) {
                foreach ($budgetProgress['categories'] as $categoryProgress) {
                    if ($categoryProgress['percentage'] > 90) { // Near or over budget
                        $budgetRecommendations[] = [
                            'type' => 'budget_alert',
                            'category' => $categoryProgress['category']['category'],
                            'allocated' => $categoryProgress['category']['amount'],
                            'spent' => $categoryProgress['spent'],
                            'percentage' => $categoryProgress['percentage'],
                            'recommendation' => "You've spent " . number_format($categoryProgress['percentage'], 1) . "% of your " .
                                               $categoryProgress['category']['category'] . " budget. " .
                                               "Try to limit further spending in this category until the next budget period."
                        ];
                    }
                }

                if (!empty($budgetRecommendations)) {
                    $recommendations['budget_alerts'] = $budgetRecommendations;
                }
            }
        }

        return $recommendations;
    }

    /**
     * Get spending patterns
     *
     * @param int $userId User ID
     * @param string $startDate Start date for analysis
     * @param string $endDate End date for analysis
     * @return array Spending patterns
     */
    public function getSpendingPatterns($userId, $startDate, $endDate) {
        return $this->detectSpendingPatterns($userId, $startDate, $endDate);
    }

    /**
     * Get spending anomalies
     *
     * @param int $userId User ID
     * @param string $startDate Start date for analysis
     * @param string $endDate End date for analysis
     * @return array Spending anomalies
     */
    public function getSpendingAnomalies($userId, $startDate, $endDate) {
        return $this->detectSpendingAnomalies($userId, $startDate, $endDate);
    }

    /**
     * Get expense reduction recommendations
     *
     * @param int $userId User ID
     * @param string $startDate Start date for analysis
     * @param string $endDate End date for analysis
     * @return array Expense reduction recommendations
     */
    public function getExpenseReductionRecommendations($userId, $startDate, $endDate) {
        return $this->generateExpenseReductionRecommendations($userId, $startDate, $endDate);
    }

    /**
     * Generate comprehensive financial insights
     *
     * @param int $userId User ID
     * @param string $period Period for analysis ('monthly', 'quarterly', 'yearly')
     * @param int $timeValue Number of months/quarters/years to analyze
     * @return array Comprehensive financial insights
     */
    public function generateComprehensiveInsights($userId, $period = 'monthly', $timeValue = 6) {
        // Calculate date range based on period
        $endDate = date('Y-m-d');
        $startDate = '';

        if ($period === 'monthly') {
            $startDate = date('Y-m-d', strtotime("-$timeValue months"));
        } elseif ($period === 'quarterly') {
            $startDate = date('Y-m-d', strtotime("-" . ($timeValue * 3) . " months"));
        } elseif ($period === 'yearly') {
            $startDate = date('Y-m-d', strtotime("-$timeValue years"));
        } else {
            $startDate = date('Y-m-d', strtotime("-6 months")); // Default to 6 months
        }

        // Get spending patterns
        $patterns = $this->detectSpendingPatterns($userId, $startDate, $endDate);

        // Get anomalies
        $anomalies = $this->detectSpendingAnomalies($userId, $startDate, $endDate);

        // Get expense reduction recommendations
        $recommendations = $this->generateExpenseReductionRecommendations($userId, $startDate, $endDate);

        // Get financial summary
        $summary = $this->financeModel->getSummary($userId, $startDate, $endDate);

        // Get top spending categories
        $topCategories = $this->financeModel->getTopSpendingCategories($userId, $startDate, $endDate, 5);

        // Combine all insights
        return [
            'summary' => $summary,
            'top_categories' => $topCategories,
            'spending_patterns' => $patterns,
            'anomalies' => $anomalies,
            'recommendations' => $recommendations,
            'analysis_period' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'period_type' => $period,
                'time_value' => $timeValue
            ]
        ];
    }
}
