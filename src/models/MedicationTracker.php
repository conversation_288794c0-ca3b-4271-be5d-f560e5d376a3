<?php
/**
 * Medication Tracker Model
 *
 * Handles medication tracking data
 */

require_once __DIR__ . '/BaseModel.php';

class MedicationTracker extends BaseModel {
    protected $table = 'medications';
    protected $logsTable = 'medication_logs';
    protected $remindersTable = 'medication_reminders';

    /**
     * Get all medications for a user
     */
    public function getUserMedications($userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                ORDER BY name ASC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get medication by ID
     */
    public function getMedication($id) {
        return $this->find($id);
    }

    /**
     * Create a new medication
     */
    public function createMedication($data) {
        return $this->create($data);
    }

    /**
     * Update a medication
     */
    public function updateMedication($id, $data) {
        return $this->update($id, $data);
    }

    /**
     * Delete a medication
     */
    public function deleteMedication($id) {
        // First delete all logs and reminders
        $this->db->delete($this->logsTable, "medication_id = ?", [$id]);
        $this->db->delete($this->remindersTable, "medication_id = ?", [$id]);

        // Then delete the medication
        return $this->delete($id);
    }

    /**
     * Get medication logs for a user
     */
    public function getMedicationLogs($userId, $days = 30) {
        $sql = "SELECT l.*, m.name, m.dosage, m.dosage_unit
                FROM {$this->logsTable} l
                JOIN {$this->table} m ON l.medication_id = m.id
                WHERE m.user_id = ?
                AND l.log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY l.log_date DESC, l.log_time DESC";

        return $this->db->fetchAll($sql, [$userId, $days]);
    }

    /**
     * Get today's medication logs for a user
     */
    public function getTodayLogs($userId) {
        $today = date('Y-m-d');

        $sql = "SELECT l.*, m.name, m.dosage, m.dosage_unit
                FROM {$this->logsTable} l
                JOIN {$this->table} m ON l.medication_id = m.id
                WHERE m.user_id = ?
                AND l.log_date = ?
                ORDER BY l.log_time DESC";

        return $this->db->fetchAll($sql, [$userId, $today]);
    }

    /**
     * Get medication logs for a specific medication
     */
    public function getMedicationLogsByMedication($medicationId, $days = 30) {
        $sql = "SELECT *
                FROM {$this->logsTable}
                WHERE medication_id = ?
                AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY log_date DESC, log_time DESC";

        return $this->db->fetchAll($sql, [$medicationId, $days]);
    }

    /**
     * Get a specific medication log by ID
     */
    public function getMedicationLog($id) {
        $sql = "SELECT * FROM {$this->logsTable} WHERE id = ? LIMIT 1";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Get a specific reminder by ID
     */
    public function getReminder($id) {
        $sql = "SELECT * FROM {$this->remindersTable} WHERE id = ? LIMIT 1";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Log medication intake
     */
    public function logMedication($data) {
        return $this->db->insert($this->logsTable, $data);
    }

    /**
     * Update medication log
     */
    public function updateMedicationLog($id, $data) {
        return $this->db->update($this->logsTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete medication log
     */
    public function deleteMedicationLog($id) {
        return $this->db->delete($this->logsTable, "id = ?", [$id]);
    }

    /**
     * Get medication reminders for a user
     */
    public function getMedicationReminders($userId) {
        $sql = "SELECT r.*, m.name, m.dosage, m.dosage_unit
                FROM {$this->remindersTable} r
                JOIN {$this->table} m ON r.medication_id = m.id
                WHERE m.user_id = ?
                ORDER BY r.reminder_time ASC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get medication reminders for a specific medication
     */
    public function getMedicationRemindersByMedication($medicationId) {
        $sql = "SELECT *
                FROM {$this->remindersTable}
                WHERE medication_id = ?
                ORDER BY reminder_time ASC";

        return $this->db->fetchAll($sql, [$medicationId]);
    }

    /**
     * Create medication reminder
     */
    public function createReminder($data) {
        return $this->db->insert($this->remindersTable, $data);
    }

    /**
     * Update medication reminder
     */
    public function updateReminder($id, $data) {
        return $this->db->update($this->remindersTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete medication reminder
     */
    public function deleteReminder($id) {
        return $this->db->delete($this->remindersTable, "id = ?", [$id]);
    }

    /**
     * Get today's medication schedule for a user
     */
    public function getTodaySchedule($userId) {
        $today = date('Y-m-d');

        $sql = "SELECT r.*, m.name, m.dosage, m.dosage_unit,
                (SELECT COUNT(*) FROM {$this->logsTable} l
                 WHERE l.medication_id = m.id
                 AND l.log_date = ?
                 AND TIME(l.log_time) BETWEEN TIME(r.reminder_time) - INTERVAL 30 MINUTE
                 AND TIME(r.reminder_time) + INTERVAL 30 MINUTE) as taken
                FROM {$this->remindersTable} r
                JOIN {$this->table} m ON r.medication_id = m.id
                WHERE m.user_id = ?
                AND (r.days_of_week IS NULL OR r.days_of_week LIKE CONCAT('%', DAYOFWEEK(?), '%'))
                ORDER BY r.reminder_time ASC";

        return $this->db->fetchAll($sql, [$today, $userId, $today]);
    }

    /**
     * Get today's medication reminders for a user
     */
    public function getTodayReminders($userId) {
        $today = date('Y-m-d');
        $currentTime = date('H:i:s');

        $sql = "SELECT r.*, m.name, m.dosage, m.dosage_unit,
                (SELECT COUNT(*) FROM {$this->logsTable} l
                 WHERE l.medication_id = m.id
                 AND l.log_date = ?
                 AND TIME(l.log_time) BETWEEN TIME(r.reminder_time) - INTERVAL 30 MINUTE
                 AND TIME(r.reminder_time) + INTERVAL 30 MINUTE) as taken
                FROM {$this->remindersTable} r
                JOIN {$this->table} m ON r.medication_id = m.id
                WHERE m.user_id = ?
                AND (r.days_of_week IS NULL OR r.days_of_week LIKE CONCAT('%', DAYOFWEEK(?), '%'))
                AND TIME(r.reminder_time) > TIME(?)
                ORDER BY r.reminder_time ASC
                LIMIT 5";

        return $this->db->fetchAll($sql, [$today, $userId, $today, $currentTime]);
    }

    /**
     * Get medication effectiveness statistics
     */
    public function getEffectivenessStats($medicationId, $days = 90) {
        $sql = "SELECT
                AVG(effectiveness_rating) as avg_effectiveness,
                MIN(effectiveness_rating) as min_effectiveness,
                MAX(effectiveness_rating) as max_effectiveness,
                AVG(side_effects_severity) as avg_side_effects,
                GROUP_CONCAT(DISTINCT side_effects SEPARATOR ', ') as common_side_effects
                FROM {$this->logsTable}
                WHERE medication_id = ?
                AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                AND effectiveness_rating IS NOT NULL";

        return $this->db->fetchOne($sql, [$medicationId, $days]);
    }

    /**
     * Get medication effectiveness data for charts
     */
    public function getEffectivenessData($medicationId, $days = 30) {
        $sql = "SELECT
                log_date,
                effectiveness_rating,
                side_effects_severity
                FROM {$this->logsTable}
                WHERE medication_id = ?
                AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                AND effectiveness_rating IS NOT NULL
                ORDER BY log_date ASC";

        return $this->db->fetchAll($sql, [$medicationId, $days]);
    }

    /**
     * Get medication adherence rate
     */
    public function getAdherenceRate($medicationId, $days = 30) {
        // Get the medication to check frequency
        $medication = $this->find($medicationId);
        if (!$medication) {
            return 0;
        }

        // Get the number of days with logs
        $sql = "SELECT COUNT(DISTINCT log_date) as days_taken
                FROM {$this->logsTable}
                WHERE medication_id = ?
                AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)";

        $result = $this->db->fetchOne($sql, [$medicationId, $days]);
        $daysTaken = $result ? $result['days_taken'] : 0;

        // Calculate expected days based on frequency
        $expectedDays = 0;

        if ($medication['frequency'] === 'daily') {
            $expectedDays = min($days, (strtotime('now') - strtotime($medication['created_at'])) / 86400);
        } else if ($medication['frequency'] === 'as_needed') {
            return 100; // Not applicable for as-needed medications
        } else if ($medication['frequency'] === 'custom') {
            // Count expected days based on custom schedule
            $sql = "SELECT COUNT(*) as expected_days
                    FROM (
                        SELECT DISTINCT DATE_ADD(CURDATE(), INTERVAL -d DAY) as date
                        FROM (
                            SELECT 0 as d UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION
                            SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION
                            SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION
                            SELECT 15 UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
                            SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION
                            SELECT 25 UNION SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29
                        ) as days
                        WHERE d < ?
                        AND DATE_ADD(CURDATE(), INTERVAL -d DAY) >= DATE(?)
                        AND EXISTS (
                            SELECT 1 FROM {$this->remindersTable}
                            WHERE medication_id = ?
                            AND (days_of_week IS NULL OR days_of_week LIKE CONCAT('%', DAYOFWEEK(DATE_ADD(CURDATE(), INTERVAL -d DAY)), '%'))
                        )
                    ) as expected_dates";

            $result = $this->db->fetchOne($sql, [$days, $medication['created_at'], $medicationId]);
            $expectedDays = $result ? $result['expected_days'] : 0;
        }

        if ($expectedDays <= 0) {
            return 0;
        }

        return round(($daysTaken / $expectedDays) * 100);
    }

    /**
     * Get medication logs by date range
     */
    public function getMedicationLogsByDateRange($medicationId, $startDate, $endDate) {
        $sql = "SELECT *
                FROM {$this->logsTable}
                WHERE medication_id = ?
                AND log_date BETWEEN ? AND ?
                ORDER BY log_date DESC, log_time DESC";

        return $this->db->fetchAll($sql, [$medicationId, $startDate, $endDate]);
    }

    /**
     * Get adherence rate by date range
     */
    public function getAdherenceRateByDateRange($medicationId, $startDate, $endDate) {
        // Get the medication to check frequency
        $medication = $this->find($medicationId);
        if (!$medication) {
            return 0;
        }

        // Get the number of days with logs
        $sql = "SELECT COUNT(DISTINCT log_date) as days_taken
                FROM {$this->logsTable}
                WHERE medication_id = ?
                AND log_date BETWEEN ? AND ?";

        $result = $this->db->fetchOne($sql, [$medicationId, $startDate, $endDate]);
        $daysTaken = $result ? $result['days_taken'] : 0;

        // Calculate expected days based on frequency
        $expectedDays = 0;
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / 86400 + 1;

        if ($medication['frequency'] === 'daily') {
            $expectedDays = min($daysDiff, (strtotime($endDate) - strtotime(max($startDate, $medication['created_at']))) / 86400 + 1);
        } else if ($medication['frequency'] === 'as_needed') {
            return 100; // Not applicable for as-needed medications
        } else if ($medication['frequency'] === 'custom') {
            // Count expected days based on custom schedule
            $sql = "SELECT COUNT(*) as expected_days
                    FROM (
                        SELECT DISTINCT date
                        FROM (
                            SELECT DATE_ADD(?, INTERVAL d DAY) as date
                            FROM (
                                SELECT 0 as d UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION
                                SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION
                                SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION
                                SELECT 15 UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
                                SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION
                                SELECT 25 UNION SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION
                                SELECT 30 UNION SELECT 31 UNION SELECT 32 UNION SELECT 33 UNION SELECT 34 UNION
                                SELECT 35 UNION SELECT 36 UNION SELECT 37 UNION SELECT 38 UNION SELECT 39 UNION
                                SELECT 40 UNION SELECT 41 UNION SELECT 42 UNION SELECT 43 UNION SELECT 44 UNION
                                SELECT 45 UNION SELECT 46 UNION SELECT 47 UNION SELECT 48 UNION SELECT 49 UNION
                                SELECT 50 UNION SELECT 51 UNION SELECT 52 UNION SELECT 53 UNION SELECT 54 UNION
                                SELECT 55 UNION SELECT 56 UNION SELECT 57 UNION SELECT 58 UNION SELECT 59 UNION
                                SELECT 60 UNION SELECT 61 UNION SELECT 62 UNION SELECT 63 UNION SELECT 64 UNION
                                SELECT 65 UNION SELECT 66 UNION SELECT 67 UNION SELECT 68 UNION SELECT 69 UNION
                                SELECT 70 UNION SELECT 71 UNION SELECT 72 UNION SELECT 73 UNION SELECT 74 UNION
                                SELECT 75 UNION SELECT 76 UNION SELECT 77 UNION SELECT 78 UNION SELECT 79 UNION
                                SELECT 80 UNION SELECT 81 UNION SELECT 82 UNION SELECT 83 UNION SELECT 84 UNION
                                SELECT 85 UNION SELECT 86 UNION SELECT 87 UNION SELECT 88 UNION SELECT 89 UNION
                                SELECT 90
                            ) as days
                            WHERE DATE_ADD(?, INTERVAL d DAY) <= ?
                        ) as dates
                        WHERE date >= DATE(?)
                        AND EXISTS (
                            SELECT 1 FROM {$this->remindersTable}
                            WHERE medication_id = ?
                            AND (days_of_week IS NULL OR days_of_week LIKE CONCAT('%', DAYOFWEEK(date), '%'))
                        )
                    ) as expected_dates";

            $result = $this->db->fetchOne($sql, [$startDate, $startDate, $endDate, max($startDate, $medication['created_at']), $medicationId]);
            $expectedDays = $result ? $result['expected_days'] : 0;
        }

        if ($expectedDays <= 0) {
            return 0;
        }

        return round(($daysTaken / $expectedDays) * 100);
    }

    /**
     * Get average effectiveness by date range
     */
    public function getAverageEffectivenessByDateRange($medicationId, $startDate, $endDate) {
        $sql = "SELECT
                AVG(effectiveness_rating) as avg_effectiveness,
                AVG(side_effects_severity) as avg_side_effects
                FROM {$this->logsTable}
                WHERE medication_id = ?
                AND log_date BETWEEN ? AND ?
                AND effectiveness_rating IS NOT NULL";

        return $this->db->fetchOne($sql, [$medicationId, $startDate, $endDate]);
    }
}
