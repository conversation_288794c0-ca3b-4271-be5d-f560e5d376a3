<?php
/**
 * Category Model
 * 
 * Handles category-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class Category extends BaseModel {
    protected $table = 'categories';
    
    /**
     * Get categories for a specific user
     */
    public function getUserCategories($userId) {
        return $this->findBy('user_id', $userId);
    }
    
    /**
     * Get category with task count
     */
    public function getCategoriesWithTaskCount($userId) {
        $sql = "SELECT c.*, COUNT(t.id) as task_count 
                FROM {$this->table} c
                LEFT JOIN tasks t ON c.id = t.category_id AND t.status != 'done'
                WHERE c.user_id = ?
                GROUP BY c.id
                ORDER BY c.name ASC";
        
        return $this->db->fetchAll($sql, [$userId]);
    }
}
