<?php
/**
 * Freelance Payment Model
 *
 * Handles freelance payment-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class FreelancePayment extends BaseModel {
    protected $table = 'freelance_payments';

    /**
     * Get payments for a specific user
     *
     * @param int $userId User ID
     * @param array $filters Optional filters
     * @return array Payments
     */
    public function getUserPayments($userId, $filters = []) {
        $sql = "SELECT p.*,
                c.name as client_name,
                c.company as client_company,
                pr.name as project_name,
                i.invoice_number
                FROM {$this->table} p
                LEFT JOIN freelance_clients c ON p.client_id = c.id
                LEFT JOIN freelance_projects pr ON p.project_id = pr.id
                LEFT JOIN freelance_invoices i ON p.invoice_id = i.id
                WHERE p.user_id = ? ";

        $params = [$userId];

        // Apply filters
        if (!empty($filters['client_id'])) {
            $sql .= " AND p.client_id = ?";
            $params[] = $filters['client_id'];
        }

        if (!empty($filters['project_id'])) {
            $sql .= " AND p.project_id = ?";
            $params[] = $filters['project_id'];
        }

        if (!empty($filters['invoice_id'])) {
            $sql .= " AND p.invoice_id = ?";
            $params[] = $filters['invoice_id'];
        }

        if (!empty($filters['payment_method'])) {
            $sql .= " AND p.payment_method = ?";
            $params[] = $filters['payment_method'];
        }

        if (!empty($filters['date_from'])) {
            $sql .= " AND p.payment_date >= ?";
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $sql .= " AND p.payment_date <= ?";
            $params[] = $filters['date_to'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (c.name LIKE ? OR pr.name LIKE ? OR i.invoice_number LIKE ? OR p.transaction_id LIKE ?)";
            $searchTerm = "%{$filters['search']}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        $sql .= " ORDER BY p.payment_date DESC, p.created_at DESC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get payment details
     *
     * @param int $paymentId Payment ID
     * @param int $userId User ID
     * @return array|bool Payment details or false if not found
     */
    public function getPaymentDetails($paymentId, $userId) {
        $sql = "SELECT p.*,
                c.name as client_name,
                c.company as client_company,
                pr.name as project_name,
                i.invoice_number
                FROM {$this->table} p
                LEFT JOIN freelance_clients c ON p.client_id = c.id
                LEFT JOIN freelance_projects pr ON p.project_id = pr.id
                LEFT JOIN freelance_invoices i ON p.invoice_id = i.id
                WHERE p.id = ? AND p.user_id = ?";

        return $this->db->fetchOne($sql, [$paymentId, $userId]);
    }

    /**
     * Get recent payments
     *
     * @param int $userId User ID
     * @param int $limit Number of payments to return
     * @return array Recent payments
     */
    public function getRecentPayments($userId, $limit = 5) {
        $sql = "SELECT p.*,
                c.name as client_name,
                c.company as client_company,
                pr.name as project_name
                FROM {$this->table} p
                LEFT JOIN freelance_clients c ON p.client_id = c.id
                LEFT JOIN freelance_projects pr ON p.project_id = pr.id
                WHERE p.user_id = ?
                ORDER BY p.payment_date DESC, p.created_at DESC
                LIMIT ?";

        return $this->db->fetchAll($sql, [$userId, $limit]);
    }

    /**
     * Get payment summary statistics
     *
     * @param int $userId User ID
     * @return array Payment summary
     */
    public function getPaymentSummary($userId) {
        $sql = "SELECT 
                COUNT(DISTINCT id) as total_payments,
                SUM(amount) as total_amount,
                AVG(amount) as average_amount,
                MAX(amount) as largest_payment,
                COUNT(DISTINCT client_id) as unique_clients,
                COUNT(DISTINCT project_id) as unique_projects
                FROM {$this->table}
                WHERE user_id = ?";
        
        $result = $this->db->fetchOne($sql, [$userId]);
        return $result ?: [
            'total_payments' => 0,
            'total_amount' => 0,
            'average_amount' => 0,
            'largest_payment' => 0,
            'unique_clients' => 0,
            'unique_projects' => 0
        ];
    }

    /**
     * Get monthly payment totals for the last 12 months
     *
     * @param int $userId User ID
     * @return array Monthly payment totals
     */
    public function getMonthlyPaymentTotals($userId) {
        $sql = "SELECT 
                DATE_FORMAT(payment_date, '%Y-%m') as month,
                SUM(amount) as total
                FROM {$this->table}
                WHERE user_id = ? AND payment_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                GROUP BY DATE_FORMAT(payment_date, '%Y-%m')
                ORDER BY month ASC";
        
        return $this->db->fetchAll($sql, [$userId]);
    }
}
