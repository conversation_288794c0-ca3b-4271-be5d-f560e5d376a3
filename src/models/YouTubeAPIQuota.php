<?php
/**
 * YouTube API Quota Model
 * 
 * Handles the tracking and management of YouTube API quota usage
 */

require_once __DIR__ . '/BaseModel.php';

class YouTubeAPIQuota extends BaseModel {
    /**
     * Get quota usage for a user
     * 
     * @param int $userId User ID
     * @return array|false Quota data or false if not found
     */
    public function getQuotaUsage($userId) {
        try {
            $today = date('Y-m-d');
            
            $sql = "SELECT * FROM youtube_api_quota WHERE user_id = :user_id AND reset_date = :reset_date";
            $quota = $this->db->fetchOne($sql, [':user_id' => $userId, ':reset_date' => $today]);
            
            if (!$quota) {
                // Create new quota record for today
                $quotaId = $this->createQuotaRecord($userId);
                
                if (!$quotaId) {
                    return false;
                }
                
                $sql = "SELECT * FROM youtube_api_quota WHERE id = :id";
                $quota = $this->db->fetchOne($sql, [':id' => $quotaId]);
            }
            
            return $quota;
        } catch (Exception $e) {
            error_log("Error getting YouTube API quota usage: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new quota record for a user
     * 
     * @param int $userId User ID
     * @return int|false The ID of the created record or false on failure
     */
    private function createQuotaRecord($userId) {
        try {
            $today = date('Y-m-d');
            
            $sql = "INSERT INTO youtube_api_quota (
                user_id, 
                quota_used, 
                quota_limit, 
                reset_date, 
                created_at, 
                updated_at
            ) VALUES (
                :user_id, 
                :quota_used, 
                :quota_limit, 
                :reset_date, 
                :created_at, 
                :updated_at
            )";
            
            $params = [
                ':user_id' => $userId,
                ':quota_used' => 0,
                ':quota_limit' => 10000, // Default daily quota limit
                ':reset_date' => $today,
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ];
            
            return $this->db->insert($sql, $params);
        } catch (Exception $e) {
            error_log("Error creating YouTube API quota record: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update quota usage for a user
     * 
     * @param int $userId User ID
     * @param int $quotaUsed Quota points used
     * @return bool Success or failure
     */
    public function updateQuotaUsage($userId, $quotaUsed) {
        try {
            $quota = $this->getQuotaUsage($userId);
            
            if (!$quota) {
                return false;
            }
            
            $newQuotaUsed = $quota['quota_used'] + $quotaUsed;
            
            $sql = "UPDATE youtube_api_quota SET quota_used = :quota_used, updated_at = :updated_at WHERE id = :id";
            return $this->db->execute($sql, [
                ':id' => $quota['id'],
                ':quota_used' => $newQuotaUsed,
                ':updated_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log("Error updating YouTube API quota usage: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if quota is available for a user
     * 
     * @param int $userId User ID
     * @return bool True if quota is available, false otherwise
     */
    public function checkQuotaAvailable($userId) {
        try {
            $quota = $this->getQuotaUsage($userId);
            
            if (!$quota) {
                return false;
            }
            
            return $quota['quota_used'] < $quota['quota_limit'];
        } catch (Exception $e) {
            error_log("Error checking YouTube API quota availability: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get remaining quota for a user
     * 
     * @param int $userId User ID
     * @return int|false Remaining quota or false on failure
     */
    public function getRemainingQuota($userId) {
        try {
            $quota = $this->getQuotaUsage($userId);
            
            if (!$quota) {
                return false;
            }
            
            return $quota['quota_limit'] - $quota['quota_used'];
        } catch (Exception $e) {
            error_log("Error getting remaining YouTube API quota: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update quota limit for a user
     * 
     * @param int $userId User ID
     * @param int $quotaLimit New quota limit
     * @return bool Success or failure
     */
    public function updateQuotaLimit($userId, $quotaLimit) {
        try {
            $quota = $this->getQuotaUsage($userId);
            
            if (!$quota) {
                return false;
            }
            
            $sql = "UPDATE youtube_api_quota SET quota_limit = :quota_limit, updated_at = :updated_at WHERE id = :id";
            return $this->db->execute($sql, [
                ':id' => $quota['id'],
                ':quota_limit' => $quotaLimit,
                ':updated_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log("Error updating YouTube API quota limit: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Reset quota usage for a user
     * 
     * @param int $userId User ID
     * @return bool Success or failure
     */
    public function resetQuotaUsage($userId) {
        try {
            $quota = $this->getQuotaUsage($userId);
            
            if (!$quota) {
                return false;
            }
            
            $sql = "UPDATE youtube_api_quota SET quota_used = 0, updated_at = :updated_at WHERE id = :id";
            return $this->db->execute($sql, [
                ':id' => $quota['id'],
                ':updated_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log("Error resetting YouTube API quota usage: " . $e->getMessage());
            return false;
        }
    }
}
