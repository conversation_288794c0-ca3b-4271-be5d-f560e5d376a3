<?php
/**
 * YouTube Video Model
 * 
 * Handles the creation, retrieval, and management of YouTube videos
 */

require_once __DIR__ . '/BaseModel.php';

class YouTubeVideo extends BaseModel {
    /**
     * Create or update a YouTube video
     * 
     * @param array $data Video data
     * @return int|false The ID of the created/updated video or false on failure
     */
    public function createOrUpdate($data) {
        try {
            // Check if video already exists
            $existingVideo = $this->getVideoByYouTubeId($data['youtube_id']);
            
            if ($existingVideo) {
                // Update existing video
                $updateFields = [];
                $params = [':id' => $existingVideo['id']];
                
                $allowedFields = [
                    'search_id', 'title', 'description', 'channel_id', 'channel_title',
                    'published_at', 'view_count', 'like_count', 'comment_count',
                    'duration', 'thumbnail_url'
                ];
                
                foreach ($allowedFields as $field) {
                    if (isset($data[$field])) {
                        $updateFields[] = "{$field} = :{$field}";
                        $params[":{$field}"] = $data[$field];
                    }
                }
                
                if (empty($updateFields)) {
                    return $existingVideo['id'];
                }
                
                $updateFields[] = "updated_at = :updated_at";
                $params[':updated_at'] = date('Y-m-d H:i:s');
                
                $sql = "UPDATE youtube_videos SET " . implode(', ', $updateFields) . " WHERE id = :id";
                
                $this->db->execute($sql, $params);
                return $existingVideo['id'];
            } else {
                // Create new video
                $sql = "INSERT INTO youtube_videos (
                    youtube_id, 
                    search_id, 
                    title, 
                    description, 
                    channel_id, 
                    channel_title, 
                    published_at, 
                    view_count, 
                    like_count, 
                    comment_count, 
                    duration, 
                    thumbnail_url, 
                    created_at, 
                    updated_at
                ) VALUES (
                    :youtube_id, 
                    :search_id, 
                    :title, 
                    :description, 
                    :channel_id, 
                    :channel_title, 
                    :published_at, 
                    :view_count, 
                    :like_count, 
                    :comment_count, 
                    :duration, 
                    :thumbnail_url, 
                    :created_at, 
                    :updated_at
                )";
                
                $params = [
                    ':youtube_id' => $data['youtube_id'],
                    ':search_id' => $data['search_id'] ?? null,
                    ':title' => $data['title'],
                    ':description' => $data['description'] ?? null,
                    ':channel_id' => $data['channel_id'] ?? null,
                    ':channel_title' => $data['channel_title'] ?? null,
                    ':published_at' => $data['published_at'] ?? null,
                    ':view_count' => $data['view_count'] ?? null,
                    ':like_count' => $data['like_count'] ?? null,
                    ':comment_count' => $data['comment_count'] ?? null,
                    ':duration' => $data['duration'] ?? null,
                    ':thumbnail_url' => $data['thumbnail_url'] ?? null,
                    ':created_at' => date('Y-m-d H:i:s'),
                    ':updated_at' => date('Y-m-d H:i:s')
                ];
                
                return $this->db->insert($sql, $params);
            }
        } catch (Exception $e) {
            error_log("Error creating/updating YouTube video: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get a video by ID
     * 
     * @param int $videoId Video ID
     * @return array|false Video data or false if not found
     */
    public function getVideo($videoId) {
        try {
            $sql = "SELECT * FROM youtube_videos WHERE id = :id";
            $video = $this->db->fetchOne($sql, [':id' => $videoId]);
            
            if ($video) {
                $video['tags'] = $this->getVideoTags($videoId);
            }
            
            return $video;
        } catch (Exception $e) {
            error_log("Error getting YouTube video: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get a video by YouTube ID
     * 
     * @param string $youtubeId YouTube ID
     * @return array|false Video data or false if not found
     */
    public function getVideoByYouTubeId($youtubeId) {
        try {
            $sql = "SELECT * FROM youtube_videos WHERE youtube_id = :youtube_id";
            $video = $this->db->fetchOne($sql, [':youtube_id' => $youtubeId]);
            
            if ($video) {
                $video['tags'] = $this->getVideoTags($video['id']);
            }
            
            return $video;
        } catch (Exception $e) {
            error_log("Error getting YouTube video by YouTube ID: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get videos by search ID
     * 
     * @param int $searchId Search ID
     * @param int $limit Optional limit
     * @param int $offset Optional offset
     * @return array Array of videos
     */
    public function getVideosBySearch($searchId, $limit = null, $offset = null) {
        try {
            $sql = "SELECT * FROM youtube_videos WHERE search_id = :search_id ORDER BY view_count DESC";
            $params = [':search_id' => $searchId];
            
            if ($limit !== null) {
                $sql .= " LIMIT :limit";
                $params[':limit'] = $limit;
                
                if ($offset !== null) {
                    $sql .= " OFFSET :offset";
                    $params[':offset'] = $offset;
                }
            }
            
            $videos = $this->db->fetchAll($sql, $params);
            
            // Get tags for each video
            foreach ($videos as &$video) {
                $video['tags'] = $this->getVideoTags($video['id']);
            }
            
            return $videos;
        } catch (Exception $e) {
            error_log("Error getting videos by search: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Delete a video
     * 
     * @param int $id Video ID
     * @return bool Success or failure
     */
    public function delete($id) {
        try {
            // First, delete all tags
            $sql = "DELETE FROM youtube_tags WHERE video_id = :video_id";
            $this->db->execute($sql, [':video_id' => $id]);
            
            // Then delete the video
            $sql = "DELETE FROM youtube_videos WHERE id = :id";
            return $this->db->execute($sql, [':id' => $id]);
        } catch (Exception $e) {
            error_log("Error deleting YouTube video: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Save tags for a video
     * 
     * @param int $videoId Video ID
     * @param array $tags Array of tags
     * @return bool Success or failure
     */
    public function saveTags($videoId, $tags) {
        try {
            // First, delete existing tags
            $sql = "DELETE FROM youtube_tags WHERE video_id = :video_id";
            $this->db->execute($sql, [':video_id' => $videoId]);
            
            // Then insert new tags
            $sql = "INSERT INTO youtube_tags (video_id, tag, created_at) VALUES (:video_id, :tag, :created_at)";
            $now = date('Y-m-d H:i:s');
            
            foreach ($tags as $tag) {
                $this->db->execute($sql, [
                    ':video_id' => $videoId,
                    ':tag' => $tag,
                    ':created_at' => $now
                ]);
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Error saving YouTube video tags: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get tags for a video
     * 
     * @param int $videoId Video ID
     * @return array Array of tags
     */
    public function getVideoTags($videoId) {
        try {
            $sql = "SELECT tag FROM youtube_tags WHERE video_id = :video_id ORDER BY tag ASC";
            $tags = $this->db->fetchAll($sql, [':video_id' => $videoId]);
            
            return array_column($tags, 'tag');
        } catch (Exception $e) {
            error_log("Error getting YouTube video tags: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Search videos by keyword
     * 
     * @param string $keyword Keyword to search for
     * @param int $limit Optional limit
     * @param int $offset Optional offset
     * @return array Array of videos
     */
    public function searchVideos($keyword, $limit = null, $offset = null) {
        try {
            $sql = "SELECT v.* FROM youtube_videos v 
                    LEFT JOIN youtube_tags t ON v.id = t.video_id 
                    WHERE v.title LIKE :keyword 
                    OR v.description LIKE :keyword 
                    OR t.tag LIKE :keyword 
                    GROUP BY v.id 
                    ORDER BY v.view_count DESC";
            
            $params = [':keyword' => '%' . $keyword . '%'];
            
            if ($limit !== null) {
                $sql .= " LIMIT :limit";
                $params[':limit'] = $limit;
                
                if ($offset !== null) {
                    $sql .= " OFFSET :offset";
                    $params[':offset'] = $offset;
                }
            }
            
            $videos = $this->db->fetchAll($sql, $params);
            
            // Get tags for each video
            foreach ($videos as &$video) {
                $video['tags'] = $this->getVideoTags($video['id']);
            }
            
            return $videos;
        } catch (Exception $e) {
            error_log("Error searching YouTube videos: " . $e->getMessage());
            return [];
        }
    }
}
