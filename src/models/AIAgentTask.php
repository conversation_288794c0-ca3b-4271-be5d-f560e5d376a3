<?php
/**
 * AI Agent Task Model
 *
 * Handles AI agent task data and operations
 */

require_once __DIR__ . '/BaseModel.php';

class AIAgentTask extends BaseModel {
    protected $table = 'ai_agent_tasks';

    /**
     * Get all tasks for a user
     */
    public function getUserTasks($userId, $status = null) {
        $sql = "SELECT t.*, a.name as agent_name, a.avatar as agent_avatar,
                    c.name as category_name, c.color as category_color, c.icon as category_icon
                FROM {$this->table} t
                JOIN ai_agents a ON t.agent_id = a.id
                LEFT JOIN ai_agent_categories c ON a.category_id = c.id
                WHERE t.user_id = ?";

        $params = [$userId];

        if ($status) {
            $sql .= " AND t.status = ?";
            $params[] = $status;
        }

        $sql .= " ORDER BY
                    CASE
                        WHEN t.status = 'pending' THEN 1
                        WHEN t.status = 'in_progress' THEN 2
                        WHEN t.status = 'completed' THEN 3
                        WHEN t.status = 'failed' THEN 4
                        WHEN t.status = 'cancelled' THEN 5
                    END,
                    CASE
                        WHEN t.priority = 'urgent' THEN 1
                        WHEN t.priority = 'high' THEN 2
                        WHEN t.priority = 'medium' THEN 3
                        WHEN t.priority = 'low' THEN 4
                    END,
                    t.due_date ASC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get tasks for an agent
     */
    public function getAgentTasks($agentId, $status = null) {
        $sql = "SELECT t.*
                FROM {$this->table} t
                WHERE t.agent_id = ?";

        $params = [$agentId];

        if ($status) {
            $sql .= " AND t.status = ?";
            $params[] = $status;
        }

        $sql .= " ORDER BY
                    CASE
                        WHEN t.status = 'pending' THEN 1
                        WHEN t.status = 'in_progress' THEN 2
                        WHEN t.status = 'completed' THEN 3
                        WHEN t.status = 'failed' THEN 4
                        WHEN t.status = 'cancelled' THEN 5
                    END,
                    CASE
                        WHEN t.priority = 'urgent' THEN 1
                        WHEN t.priority = 'high' THEN 2
                        WHEN t.priority = 'medium' THEN 3
                        WHEN t.priority = 'low' THEN 4
                    END,
                    t.due_date ASC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get a single task by ID
     */
    public function getTask($id, $userId) {
        $sql = "SELECT t.*, a.name as agent_name, a.avatar as agent_avatar,
                    c.name as category_name, c.color as category_color, c.icon as category_icon
                FROM {$this->table} t
                JOIN ai_agents a ON t.agent_id = a.id
                LEFT JOIN ai_agent_categories c ON a.category_id = c.id
                WHERE t.id = ? AND t.user_id = ?";

        return $this->db->fetchOne($sql, [$id, $userId]);
    }

    /**
     * Create a new task
     */
    public function createTask($data) {
        // Ensure created_at and updated_at are set
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->create($data);
    }

    /**
     * Update a task
     */
    public function updateTask($id, $data) {
        // Ensure updated_at is set
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->update($id, $data);
    }

    /**
     * Delete a task
     */
    public function deleteTask($id, $userId) {
        $sql = "DELETE FROM {$this->table} WHERE id = ? AND user_id = ?";

        return $this->db->query($sql, [$id, $userId]);
    }

    /**
     * Update task status
     */
    public function updateTaskStatus($id, $status, $userId) {
        $data = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // If completing the task, set completion date
        if ($status === 'completed') {
            $data['completion_date'] = date('Y-m-d H:i:s');
        }

        $sql = "UPDATE {$this->table}
                SET status = ?, updated_at = ?";

        $params = [$status, $data['updated_at']];

        if (isset($data['completion_date'])) {
            $sql .= ", completion_date = ?";
            $params[] = $data['completion_date'];
        }

        $sql .= " WHERE id = ? AND user_id = ?";
        $params[] = $id;
        $params[] = $userId;

        return $this->db->query($sql, $params);
    }

    /**
     * Get task statistics
     */
    public function getTaskStats($userId) {
        $sql = "SELECT
                    COUNT(*) as total_tasks,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_tasks,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_tasks,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_tasks,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_tasks,
                    AVG(CASE WHEN status = 'completed' THEN success_rating ELSE NULL END) as avg_success_rating
                FROM {$this->table}
                WHERE user_id = ?";

        return $this->db->fetchOne($sql, [$userId]);
    }
}
