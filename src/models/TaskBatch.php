<?php
/**
 * Task Batch Model
 *
 * Handles task batching functionality for productivity management
 */

require_once __DIR__ . '/BaseModel.php';

class TaskBatch extends BaseModel {
    protected $table = 'task_batches';

    public function __construct() {
        parent::__construct();
    }

    /**
     * Get all task batches for a user
     */
    public function getUserBatches($userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                ORDER BY created_at DESC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get active task batches for a user
     */
    public function getActiveBatches($userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND status = 'active'
                ORDER BY energy_level DESC, name ASC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get task batches by energy level
     */
    public function getBatchesByEnergyLevel($userId, $energyLevel) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND energy_level = ? AND status = 'active'
                ORDER BY name ASC";

        return $this->db->fetchAll($sql, [$userId, $energyLevel]);
    }

    /**
     * Get task batches with tasks
     */
    public function getBatchesWithTasks($userId) {
        $batches = $this->getUserBatches($userId);

        if (empty($batches)) {
            return [];
        }

        // Get tasks for each batch
        foreach ($batches as &$batch) {
            $batch['tasks'] = $this->getBatchTasks($batch['id']);
            $batch['task_count'] = count($batch['tasks']);
            $batch['completed_count'] = array_reduce($batch['tasks'], function($carry, $task) {
                return $carry + ($task['status'] === 'done' ? 1 : 0);
            }, 0);
        }

        return $batches;
    }

    /**
     * Get tasks for a batch
     */
    public function getBatchTasks($batchId) {
        $sql = "SELECT t.* FROM tasks t
                JOIN task_batch_items tbi ON t.id = tbi.task_id
                WHERE tbi.batch_id = ?
                ORDER BY tbi.position ASC";

        return $this->db->fetchAll($sql, [$batchId]);
    }

    /**
     * Get a specific batch with its tasks
     */
    public function getBatchWithTasks($batchId) {
        $batch = $this->find($batchId);

        if (!$batch) {
            return null;
        }

        $batch['tasks'] = $this->getBatchTasks($batchId);
        $batch['task_count'] = count($batch['tasks']);
        $batch['completed_count'] = array_reduce($batch['tasks'], function($carry, $task) {
            return $carry + ($task['status'] === 'done' ? 1 : 0);
        }, 0);

        return $batch;
    }

    /**
     * Create a new task batch
     */
    public function create($data) {
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        $batchId = $this->db->insert($this->table, $data);

        return $batchId;
    }

    /**
     * Add tasks to a batch
     */
    public function addTasksToBatch($batchId, $taskIds) {
        $position = 0;
        $values = [];
        $placeholders = [];

        foreach ($taskIds as $taskId) {
            $values[] = $batchId;
            $values[] = $taskId;
            $values[] = $position;
            $placeholders[] = "(?, ?, ?)";
            $position++;
        }

        if (empty($values)) {
            return true;
        }

        $sql = "INSERT INTO task_batch_items (batch_id, task_id, position) VALUES " . implode(', ', $placeholders);

        return $this->db->execute($sql, $values);
    }

    /**
     * Remove a task from a batch
     */
    public function removeTaskFromBatch($batchId, $taskId) {
        $sql = "DELETE FROM task_batch_items
                WHERE batch_id = ? AND task_id = ?";

        return $this->db->execute($sql, [$batchId, $taskId]);
    }

    /**
     * Update a task batch
     */
    public function update($id, $data) {
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->db->update($this->table, $data, ['id' => $id]);
    }

    /**
     * Delete a task batch
     */
    public function delete($id) {
        // First delete all batch items
        $sql = "DELETE FROM task_batch_items WHERE batch_id = ?";
        $this->db->execute($sql, [$id]);

        // Then delete the batch
        return $this->db->delete($this->table, ['id' => $id]);
    }

    /**
     * Find a task batch by ID
     *
     * @param int $id Record ID
     * @param bool $useCache Whether to use cache
     * @return array|false Record data or false if not found
     */
    public function find($id, $useCache = null) {
        // Use class default if not specified
        $useCache = $useCache !== null ? $useCache : $this->useCache;

        // Generate cache key
        $cacheKey = "model_{$this->table}_find_{$id}";

        // Try to get from cache first
        if ($useCache && $this->cache->has($cacheKey)) {
            return $this->cache->get($cacheKey);
        }

        $sql = "SELECT * FROM {$this->table}
                WHERE id = ?
                LIMIT 1";

        $result = $this->db->fetchOne($sql, [$id]);

        // Store in cache if found
        if ($result && $useCache) {
            $this->cache->set($cacheKey, $result, $this->cacheTTL);
        }

        return $result;
    }

    /**
     * Check if a batch belongs to a user
     */
    public function belongsToUser($id, $userId) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}
                WHERE id = ? AND user_id = ?";

        $result = $this->db->fetchOne($sql, [$id, $userId]);

        return $result['count'] > 0;
    }

    /**
     * Reorder tasks in a batch
     */
    public function reorderBatchTasks($batchId, $taskIds) {
        // First, delete all existing batch items
        $sql = "DELETE FROM task_batch_items WHERE batch_id = ?";
        $this->db->execute($sql, [$batchId]);

        // Then add them back in the new order
        return $this->addTasksToBatch($batchId, $taskIds);
    }

    /**
     * Get recommended batches based on current energy level
     */
    public function getRecommendedBatches($userId, $currentEnergyLevel) {
        // Determine which energy level batches to recommend
        $recommendedEnergyLevel = 'medium';

        if ($currentEnergyLevel >= 8) {
            $recommendedEnergyLevel = 'high';
        } elseif ($currentEnergyLevel >= 5) {
            $recommendedEnergyLevel = 'medium';
        } else {
            $recommendedEnergyLevel = 'low';
        }

        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND energy_level = ? AND status = 'active'
                ORDER BY name ASC";

        $batches = $this->db->fetchAll($sql, [$userId, $recommendedEnergyLevel]);

        // Get tasks for each batch
        foreach ($batches as &$batch) {
            $batch['tasks'] = $this->getBatchTasks($batch['id']);
            $batch['task_count'] = count($batch['tasks']);
            $batch['completed_count'] = array_reduce($batch['tasks'], function($carry, $task) {
                return $carry + ($task['status'] === 'done' ? 1 : 0);
            }, 0);
        }

        return [
            'energy_level' => $recommendedEnergyLevel,
            'batches' => $batches
        ];
    }

    /**
     * Get batch statistics
     */
    public function getBatchStatistics($userId) {
        $sql = "SELECT
                    COUNT(*) as total_batches,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_batches,
                    SUM(CASE WHEN energy_level = 'high' THEN 1 ELSE 0 END) as high_energy_batches,
                    SUM(CASE WHEN energy_level = 'medium' THEN 1 ELSE 0 END) as medium_energy_batches,
                    SUM(CASE WHEN energy_level = 'low' THEN 1 ELSE 0 END) as low_energy_batches
                FROM {$this->table}
                WHERE user_id = ?";

        return $this->db->fetchOne($sql, [$userId]);
    }

    /**
     * Get tasks not in any batch
     */
    public function getTasksNotInBatch($userId) {
        $sql = "SELECT t.* FROM tasks t
                LEFT JOIN task_batch_items tbi ON t.id = tbi.task_id
                WHERE t.user_id = ? AND t.status != 'done' AND tbi.id IS NULL
                ORDER BY t.due_date ASC, t.priority DESC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Schedule a batch as a time block
     */
    public function scheduleBatch($batchId, $startTime, $duration) {
        $batch = $this->find($batchId);

        if (!$batch) {
            return false;
        }

        // Calculate end time
        $endTime = date('Y-m-d H:i:s', strtotime("$startTime + $duration minutes"));

        // Create time block data
        $timeBlockData = [
            'user_id' => $batch['user_id'],
            'title' => "Batch: " . $batch['name'],
            'description' => $batch['description'],
            'start_time' => $startTime,
            'end_time' => $endTime,
            'category' => 'Task Batch',
            'color' => $this->getColorForEnergyLevel($batch['energy_level']),
            'batch_id' => $batchId
        ];

        // Create time block
        require_once __DIR__ . '/TimeBlock.php';
        $timeBlockModel = new TimeBlock();

        return $timeBlockModel->create($timeBlockData);
    }

    /**
     * Get color for energy level
     */
    private function getColorForEnergyLevel($energyLevel) {
        switch ($energyLevel) {
            case 'high':
                return '#22C55E'; // Green
            case 'medium':
                return '#EAB308'; // Yellow
            case 'low':
                return '#EF4444'; // Red
            default:
                return '#6366F1'; // Indigo (primary)
        }
    }
}
