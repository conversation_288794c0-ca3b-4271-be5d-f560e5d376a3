<?php
/**
 * Brigade Coordinator Class
 *
 * This class coordinates the workflow between different agents in a brigade.
 * It manages the sequence of operations and data flow between agents.
 */

require_once __DIR__ . '/BaseAgent.php';

class BrigadeCoordinator {
    protected $brigadeType;
    protected $projectId;
    protected $userId;
    protected $agents = [];
    protected $workflow = [];
    protected $projectModel;
    protected $taskModel;
    protected $assignmentModel;
    
    /**
     * Constructor
     *
     * @param string $brigadeType The type of brigade (content_creation, lead_generation, etc.)
     * @param int $projectId The ID of the project this brigade is associated with
     * @param int $userId The ID of the user who owns this brigade
     */
    public function __construct($brigadeType, $projectId, $userId) {
        require_once __DIR__ . '/../Project.php';
        require_once __DIR__ . '/../Task.php';
        require_once __DIR__ . '/../ProjectAgentAssignment.php';
        
        $this->brigadeType = $brigadeType;
        $this->projectId = $projectId;
        $this->userId = $userId;
        
        $this->projectModel = new Project();
        $this->taskModel = new Task();
        $this->assignmentModel = new ProjectAgentAssignment();
        
        // Load the assigned agents for this project
        $this->loadAssignedAgents();
        
        // Set up the workflow based on brigade type
        $this->setupWorkflow();
    }
    
    /**
     * Load agents assigned to this project
     */
    protected function loadAssignedAgents() {
        $assignments = $this->assignmentModel->getProjectAgents($this->projectId);
        
        foreach ($assignments as $assignment) {
            $this->agents[$assignment['role']] = [
                'agent_id' => $assignment['agent_id'],
                'agent_name' => $assignment['agent_name'],
                'instance' => null // Will be instantiated when needed
            ];
        }
    }
    
    /**
     * Set up the workflow based on brigade type
     */
    protected function setupWorkflow() {
        switch ($this->brigadeType) {
            case 'content_creation':
                $this->workflow = [
                    'Research Agent' => [
                        'next' => 'Content Planning Agent',
                        'output' => 'research_data'
                    ],
                    'Content Planning Agent' => [
                        'next' => 'Writing Agent',
                        'output' => 'content_plan'
                    ],
                    'Writing Agent' => [
                        'next' => 'Editing Agent',
                        'output' => 'draft_content'
                    ],
                    'Editing Agent' => [
                        'next' => 'SEO Optimization Agent',
                        'output' => 'edited_content'
                    ],
                    'SEO Optimization Agent' => [
                        'next' => null,
                        'output' => 'final_content'
                    ]
                ];
                break;
                
            case 'lead_generation':
                // Define workflow for lead generation brigade
                break;
                
            case 'customer_support':
                // Define workflow for customer support brigade
                break;
                
            case 'data_analysis':
                // Define workflow for data analysis brigade
                break;
                
            default:
                // Default empty workflow
                break;
        }
    }
    
    /**
     * Get an agent instance by role
     *
     * @param string $role The role of the agent
     * @return BaseAgent|null The agent instance or null if not found
     */
    public function getAgentByRole($role) {
        if (!isset($this->agents[$role])) {
            return null;
        }
        
        // If the agent instance hasn't been created yet, create it
        if ($this->agents[$role]['instance'] === null) {
            $agentId = $this->agents[$role]['agent_id'];
            
            // Determine the agent class based on role and brigade type
            $className = $this->getAgentClassName($role);
            
            if (class_exists($className)) {
                $this->agents[$role]['instance'] = new $className($agentId, $this->userId);
            } else {
                // Fall back to base agent if specific class doesn't exist
                $this->agents[$role]['instance'] = new BaseAgent($agentId, $this->userId);
            }
        }
        
        return $this->agents[$role]['instance'];
    }
    
    /**
     * Get the class name for an agent based on role and brigade type
     *
     * @param string $role The role of the agent
     * @return string The class name
     */
    protected function getAgentClassName($role) {
        // Convert spaces to nothing and remove "Agent" suffix for the class name
        $rolePart = str_replace(' ', '', str_replace('Agent', '', $role));
        
        // Convert brigade type to camel case
        $brigadePart = str_replace('_', '', ucwords($this->brigadeType, '_'));
        
        // Combine to form the class name
        return $rolePart . 'Agent';
    }
    
    /**
     * Execute a workflow step
     *
     * @param string $role The role of the agent to execute
     * @param array $input The input data for the agent
     * @return array The output data from the agent
     */
    public function executeStep($role, $input = []) {
        $agent = $this->getAgentByRole($role);
        
        if (!$agent) {
            throw new Exception("Agent with role '{$role}' not found");
        }
        
        // Process the request through the agent
        $result = $agent->processRequest($input);
        
        // Update task status if applicable
        $this->updateTaskStatus($role);
        
        return $result;
    }
    
    /**
     * Execute the entire workflow
     *
     * @param array $initialInput The initial input data
     * @return array The final output data
     */
    public function executeWorkflow($initialInput = []) {
        $currentRole = $this->getFirstRole();
        $currentInput = $initialInput;
        $outputs = [];
        
        while ($currentRole !== null) {
            // Execute the current step
            $result = $this->executeStep($currentRole, $currentInput);
            
            // Store the output
            $outputKey = $this->workflow[$currentRole]['output'];
            $outputs[$outputKey] = $result;
            
            // Get the next role
            $nextRole = $this->workflow[$currentRole]['next'];
            
            // Prepare input for the next step
            if ($nextRole !== null) {
                $currentInput = $outputs;
            }
            
            // Move to the next role
            $currentRole = $nextRole;
        }
        
        return $outputs;
    }
    
    /**
     * Get the first role in the workflow
     *
     * @return string|null The first role or null if workflow is empty
     */
    protected function getFirstRole() {
        if (empty($this->workflow)) {
            return null;
        }
        
        return array_key_first($this->workflow);
    }
    
    /**
     * Update the status of a task associated with a role
     *
     * @param string $role The role of the agent
     * @return bool True if task was updated, false otherwise
     */
    protected function updateTaskStatus($role) {
        // Find tasks associated with this role
        $tasks = $this->taskModel->getProjectTasks($this->projectId);
        
        foreach ($tasks as $task) {
            // Check if the task is assigned to this role
            if (strpos($task['assigned_to'], $role) !== false) {
                // Update the task status to completed
                $this->taskModel->updateTask($task['id'], [
                    'status' => 'completed',
                    'completion_date' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                
                return true;
            }
        }
        
        return false;
    }
}
