<?php
/**
 * Writing Agent Class
 *
 * This agent is responsible for generating actual content based on outlines
 * for the Content Creation Brigade.
 */

require_once __DIR__ . '/../BaseAgent.php';

class WritingAgent extends BaseAgent {
    /**
     * Process a writing request
     *
     * @param array $request The request data including content plan
     * @return array The draft content
     */
    public function processRequest($request) {
        // Extract the topic, research data, and content plan
        $topic = $request['topic'] ?? '';
        $researchData = $request['research_data'] ?? null;
        $contentPlan = $request['content_plan'] ?? null;
        $parameters = $request['parameters'] ?? [];
        
        if (!$contentPlan) {
            $response = "Cannot generate content without a content plan.";
            $this->recordInteraction(json_encode($request), $response, 'command', false);
            return ['error' => $response];
        }
        
        // Log the request
        $requestLog = "Writing request for topic: {$topic}";
        
        // Generate the content
        $draftContent = $this->generateContent($topic, $researchData, $contentPlan, $parameters);
        
        // Generate a summary of the draft content
        $summary = $this->generateContentSummary($draftContent);
        
        // Record the interaction
        $this->recordInteraction(
            $requestLog,
            "Generated " . count($draftContent['content_pieces']) . " content pieces for {$topic}."
        );
        
        // Return the draft content
        return $draftContent;
    }
    
    /**
     * Generate content based on research data and content plan
     *
     * @param string $topic The main topic
     * @param array $researchData The research data
     * @param array $contentPlan The content plan
     * @param array $parameters Additional parameters
     * @return array The draft content
     */
    protected function generateContent($topic, $researchData, $contentPlan, $parameters = []) {
        // Extract content pieces from the content plan
        $plannedPieces = $contentPlan['content_pieces'] ?? [];
        
        // Determine which pieces to generate
        $pieceIndexToGenerate = $parameters['piece_index'] ?? null;
        
        if ($pieceIndexToGenerate !== null) {
            // Generate only the specified piece
            $piecesToGenerate = [];
            foreach ($plannedPieces as $piece) {
                if ($piece['id'] == $pieceIndexToGenerate) {
                    $piecesToGenerate[] = $piece;
                    break;
                }
            }
        } else {
            // Generate all pieces
            $piecesToGenerate = $plannedPieces;
        }
        
        // Generate content for each piece
        $generatedPieces = [];
        foreach ($piecesToGenerate as $piece) {
            $generatedPieces[] = $this->generateContentPiece(
                $piece,
                $topic,
                $researchData,
                $contentPlan['strategy'] ?? []
            );
        }
        
        // Return the draft content
        return [
            'topic' => $topic,
            'content_pieces' => $generatedPieces,
            'generated_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Generate content for a specific piece
     *
     * @param array $piece The content piece from the plan
     * @param string $topic The main topic
     * @param array $researchData The research data
     * @param array $strategy The content strategy
     * @return array The generated content piece
     */
    protected function generateContentPiece($piece, $topic, $researchData, $strategy) {
        // Extract piece details
        $id = $piece['id'];
        $title = $piece['title'];
        $format = $piece['format'];
        $subtopic = $piece['subtopic'];
        $outline = $piece['outline'];
        $targetWordCount = $piece['target_word_count'];
        
        // Generate content based on the outline
        $sections = [];
        foreach ($outline as $outlineItem) {
            $sectionTitle = $outlineItem['section'];
            $sectionContent = $this->generateSectionContent(
                $sectionTitle,
                $outlineItem['content'],
                $subtopic,
                $topic,
                $researchData,
                $format
            );
            
            $sections[] = [
                'title' => $sectionTitle,
                'content' => $sectionContent
            ];
        }
        
        // Calculate actual word count
        $actualWordCount = 0;
        foreach ($sections as $section) {
            $actualWordCount += str_word_count($section['content']);
        }
        
        // Return the generated piece
        return [
            'id' => $id,
            'title' => $title,
            'format' => $format,
            'subtopic' => $subtopic,
            'sections' => $sections,
            'target_word_count' => $targetWordCount,
            'actual_word_count' => $actualWordCount,
            'keywords_used' => $piece['target_keywords'],
            'generated_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Generate content for a specific section
     *
     * @param string $sectionTitle The title of the section
     * @param string $sectionGuideline The content guideline for the section
     * @param string $subtopic The subtopic
     * @param string $topic The main topic
     * @param array $researchData The research data
     * @param string $format The content format
     * @return string The generated section content
     */
    protected function generateSectionContent($sectionTitle, $sectionGuideline, $subtopic, $topic, $researchData, $format) {
        // In a real implementation, this would use an AI model or API to generate content.
        // For this example, we'll simulate content generation with templates.
        
        // Determine the appropriate template based on section title and format
        $template = $this->getSectionTemplate($sectionTitle, $format);
        
        // Replace placeholders in the template
        $content = str_replace(
            ['{topic}', '{subtopic}'],
            [$topic, $subtopic],
            $template
        );
        
        // Add research data if available
        if ($researchData && $sectionTitle == 'Introduction') {
            $statsCount = count($researchData['statistics'] ?? []);
            if ($statsCount > 0) {
                $randomStat = $researchData['statistics'][array_rand($researchData['statistics'])];
                $content .= "\n\nAccording to recent research, " . $randomStat['description'] . " " . $randomStat['value'] . ".";
            }
        }
        
        // Add key points if available
        if ($researchData && strpos($sectionTitle, 'Key') !== false) {
            $keyPoints = $researchData['key_points'] ?? [];
            if (!empty($keyPoints)) {
                $content .= "\n\nKey insights from research include:";
                for ($i = 0; $i < min(3, count($keyPoints)); $i++) {
                    $content .= "\n- " . $keyPoints[$i];
                }
            }
        }
        
        return $content;
    }
    
    /**
     * Get a template for a specific section
     *
     * @param string $sectionTitle The title of the section
     * @param string $format The content format
     * @return string The section template
     */
    protected function getSectionTemplate($sectionTitle, $format) {
        // Templates for different section types
        $templates = [
            'Introduction' => [
                'Blog Post' => "In today's rapidly evolving landscape, {subtopic} has become increasingly important for businesses and individuals alike. This comprehensive guide will explore the various aspects of {subtopic}, providing you with valuable insights and practical strategies to leverage this concept effectively.\n\nWhether you're new to {subtopic} or looking to deepen your understanding, this article will serve as your definitive resource on the subject.",
                'Video' => "Welcome to our video on {subtopic}! In the next few minutes, we'll break down everything you need to know about this fascinating topic. We'll cover the basics, dive into some advanced concepts, and share practical tips you can implement right away.\n\nBefore we dive in, make sure to subscribe to our channel for more content like this!",
                'Infographic' => "This infographic provides a visual overview of {subtopic}, highlighting key statistics, processes, and best practices. Use this as a quick reference guide or share it with colleagues who want to understand {subtopic} at a glance.",
                'default' => "Welcome to our comprehensive exploration of {subtopic}. This content will provide you with valuable insights and practical information about this important aspect of {topic}."
            ],
            'What is' => [
                'default' => "{subtopic} refers to a specialized approach within the broader field of {topic}. It encompasses a set of principles, methodologies, and practices designed to optimize outcomes and deliver measurable results.\n\nAt its core, {subtopic} is about leveraging strategic insights and tactical implementations to address specific challenges and capitalize on emerging opportunities."
            ],
            'Why' => [
                'default' => "The importance of {subtopic} cannot be overstated in today's competitive environment. Organizations that effectively implement {subtopic} strategies typically see significant improvements in performance metrics, customer satisfaction, and overall business outcomes.\n\nSome key benefits include:\n- Enhanced operational efficiency\n- Improved customer engagement\n- Better decision-making capabilities\n- Increased competitive advantage\n- Long-term sustainability"
            ],
            'How to' => [
                'default' => "Implementing {subtopic} effectively requires a systematic approach and careful planning. Here's a step-by-step guide to help you get started:\n\n1. **Assessment**: Evaluate your current situation and identify specific needs related to {subtopic}.\n\n2. **Strategy Development**: Create a comprehensive strategy that aligns with your overall objectives.\n\n3. **Resource Allocation**: Ensure you have the necessary resources, including personnel, technology, and budget.\n\n4. **Implementation**: Execute your strategy in phases, starting with pilot initiatives.\n\n5. **Monitoring**: Continuously track performance metrics to gauge effectiveness.\n\n6. **Optimization**: Refine your approach based on data and feedback."
            ],
            'Key Components' => [
                'default' => "Understanding the fundamental components of {subtopic} is essential for successful implementation. These elements work together to create a cohesive and effective framework.\n\n**Component 1: Strategic Planning**\nThis involves setting clear objectives and developing a roadmap for {subtopic} initiatives.\n\n**Component 2: Technological Infrastructure**\nThe right tools and systems are crucial for supporting {subtopic} efforts.\n\n**Component 3: Human Expertise**\nSkilled professionals with specialized knowledge in {subtopic} are invaluable assets.\n\n**Component 4: Process Optimization**\nStreamlined workflows and efficient processes enhance the effectiveness of {subtopic}.\n\n**Component 5: Performance Measurement**\nRobust metrics and analytics provide insights into the impact of {subtopic} initiatives."
            ],
            'Common Challenges' => [
                'default' => "While implementing {subtopic}, organizations often encounter several challenges. Understanding these obstacles and having strategies to overcome them is crucial for success.\n\n**Challenge 1: Resource Constraints**\nMany organizations struggle with limited budgets, personnel, or technological capabilities.\n*Solution:* Prioritize initiatives based on potential impact and implement in phases.\n\n**Challenge 2: Resistance to Change**\nStakeholders may resist new approaches related to {subtopic}.\n*Solution:* Focus on change management and clear communication of benefits.\n\n**Challenge 3: Complexity**\nThe multifaceted nature of {subtopic} can be overwhelming.\n*Solution:* Break down implementation into manageable components with clear milestones.\n\n**Challenge 4: Integration Issues**\nIncorporating {subtopic} into existing systems can be challenging.\n*Solution:* Develop a comprehensive integration plan with technical expertise."
            ],
            'Practical Demonstration' => [
                'default' => "Let's walk through a practical example of {subtopic} in action. This demonstration will illustrate how the concepts we've discussed can be applied in real-world scenarios.\n\n**Scenario:**\nA mid-sized company looking to improve their approach to {subtopic}.\n\n**Initial Situation:**\nThe company was experiencing challenges with efficiency and outcomes related to {subtopic}.\n\n**Implementation Steps:**\n1. They conducted a thorough assessment of their current practices.\n2. They identified specific areas for improvement.\n3. They developed a tailored strategy for {subtopic}.\n4. They implemented new tools and methodologies.\n5. They trained their team on best practices.\n\n**Results:**\nAfter six months, the company saw significant improvements in key metrics related to {subtopic}, including enhanced productivity, better outcomes, and increased stakeholder satisfaction."
            ],
            'Expert Tips' => [
                'default' => "Based on industry best practices and expert insights, here are some valuable tips for maximizing the effectiveness of your {subtopic} initiatives:\n\n1. **Start with Clear Objectives**: Define what success looks like for your {subtopic} efforts.\n\n2. **Invest in the Right Tools**: Choose technologies that specifically support your {subtopic} goals.\n\n3. **Prioritize Continuous Learning**: Stay updated on emerging trends and best practices in {subtopic}.\n\n4. **Foster Cross-Functional Collaboration**: Encourage teams from different departments to work together on {subtopic} initiatives.\n\n5. **Measure and Iterate**: Regularly assess the performance of your {subtopic} strategies and make data-driven adjustments.\n\n6. **Consider Cultural Factors**: Ensure that your approach to {subtopic} aligns with your organizational culture.\n\n7. **Look Beyond Short-Term Gains**: Develop a long-term vision for how {subtopic} will evolve within your organization."
            ],
            'Conclusion' => [
                'default' => "As we've explored throughout this content, {subtopic} represents a significant opportunity for organizations and individuals who want to excel in the realm of {topic}. By understanding the fundamental concepts, implementing best practices, and addressing common challenges, you can leverage {subtopic} to achieve meaningful results.\n\nRemember that successful implementation of {subtopic} is not a one-time effort but an ongoing journey of refinement and optimization. Stay curious, remain adaptable, and continue to build your expertise in this important area.\n\nWe hope this resource has provided you with valuable insights and practical guidance on {subtopic}. As you move forward with your initiatives, keep these principles in mind and don't hesitate to revisit this content for reference."
            ],
            'default' => [
                'default' => "This section explores important aspects of {subtopic} within the context of {topic}. Understanding these elements will help you develop a more comprehensive perspective and enable more effective implementation of related strategies and tactics."
            ]
        ];
        
        // Find the appropriate template
        foreach ($templates as $key => $formatTemplates) {
            if (strpos($sectionTitle, $key) !== false) {
                return $formatTemplates[$format] ?? $formatTemplates['default'];
            }
        }
        
        // Return default template if no match found
        return $templates['default']['default'];
    }
    
    /**
     * Generate a summary of the draft content
     *
     * @param array $draftContent The draft content
     * @return string Summary of the draft content
     */
    protected function generateContentSummary($draftContent) {
        $topic = $draftContent['topic'];
        $contentPieceCount = count($draftContent['content_pieces']);
        
        $totalWordCount = 0;
        $formats = [];
        
        foreach ($draftContent['content_pieces'] as $piece) {
            $totalWordCount += $piece['actual_word_count'];
            $formats[$piece['format']] = ($formats[$piece['format']] ?? 0) + 1;
        }
        
        $summary = "Content Generation Summary for {$topic}:\n\n";
        $summary .= "- Generated {$contentPieceCount} content pieces\n";
        $summary .= "- Total word count: {$totalWordCount} words\n";
        
        $summary .= "- Content formats:\n";
        foreach ($formats as $format => $count) {
            $summary .= "  - {$format}: {$count} pieces\n";
        }
        
        return $summary;
    }
}
