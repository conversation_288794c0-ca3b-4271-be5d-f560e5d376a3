<?php
/**
 * Content Planning Agent Class
 *
 * This agent is responsible for creating content outlines and strategies
 * for the Content Creation Brigade.
 */

require_once __DIR__ . '/../BaseAgent.php';

class ContentPlanningAgent extends BaseAgent {
    /**
     * Process a content planning request
     *
     * @param array $request The request data including research results
     * @return array The content plan
     */
    public function processRequest($request) {
        // Extract the topic and research data
        $topic = $request['topic'] ?? '';
        $researchData = $request['research_data'] ?? null;
        $parameters = $request['parameters'] ?? [];
        
        if (!$researchData) {
            $response = "Cannot create content plan without research data.";
            $this->recordInteraction(json_encode($request), $response, 'command', false);
            return ['error' => $response];
        }
        
        // Log the request
        $requestLog = "Content planning request for topic: {$topic}";
        
        // Create the content plan
        $contentPlan = $this->createContentPlan($topic, $researchData, $parameters);
        
        // Generate a summary of the content plan
        $summary = $this->generatePlanSummary($contentPlan);
        
        // Record the interaction
        $this->recordInteraction(
            $requestLog,
            "Created content plan for {$topic} with " . count($contentPlan['content_pieces']) . " content pieces."
        );
        
        // Return the content plan
        return $contentPlan;
    }
    
    /**
     * Create a content plan based on research data
     *
     * @param string $topic The main topic
     * @param array $researchData The research data
     * @param array $parameters Additional parameters
     * @return array The content plan
     */
    protected function createContentPlan($topic, $researchData, $parameters = []) {
        // Extract content format preferences from parameters or research data
        $contentFormats = $parameters['content_formats'] ?? 
                         ($researchData['audience_insights']['content_preferences']['preferred_formats'] ?? ['Blog Post']);
        
        // Extract content quantity from parameters
        $contentQuantity = $parameters['content_quantity'] ?? 3;
        
        // Create the content strategy
        $strategy = $this->createContentStrategy($topic, $researchData);
        
        // Create the content calendar
        $calendar = $this->createContentCalendar($topic, $contentQuantity);
        
        // Create content pieces
        $contentPieces = $this->createContentPieces($topic, $researchData, $contentFormats, $contentQuantity);
        
        // Return the complete content plan
        return [
            'topic' => $topic,
            'strategy' => $strategy,
            'calendar' => $calendar,
            'content_pieces' => $contentPieces,
            'created_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Create a content strategy
     *
     * @param string $topic The main topic
     * @param array $researchData The research data
     * @return array The content strategy
     */
    protected function createContentStrategy($topic, $researchData) {
        // Extract key insights from research data
        $keyPoints = $researchData['key_points'] ?? [];
        $trends = $researchData['trends'] ?? [];
        $audienceInsights = $researchData['audience_insights'] ?? [];
        
        // Create objectives based on research
        $objectives = [
            "Establish authority on {$topic}",
            "Address audience pain points related to {$topic}",
            "Capitalize on trending aspects of {$topic}"
        ];
        
        // Add trend-specific objectives
        foreach ($trends as $trend) {
            $objectives[] = "Leverage trend: " . $trend['name'];
        }
        
        // Create target audience description
        $targetAudience = $this->createTargetAudienceDescription($audienceInsights);
        
        // Create key messages
        $keyMessages = $this->extractKeyMessages($keyPoints, $trends);
        
        // Create content pillars
        $contentPillars = $this->createContentPillars($topic, $keyPoints);
        
        // Create distribution channels
        $distributionChannels = $this->recommendDistributionChannels($audienceInsights);
        
        // Create success metrics
        $successMetrics = [
            'Engagement' => ['Comments', 'Shares', 'Time on page'],
            'Conversion' => ['Email sign-ups', 'Product inquiries', 'Downloads'],
            'SEO' => ['Organic traffic', 'Keyword rankings', 'Backlinks'],
            'Brand' => ['Brand mentions', 'Sentiment analysis', 'Follower growth']
        ];
        
        return [
            'objectives' => $objectives,
            'target_audience' => $targetAudience,
            'key_messages' => $keyMessages,
            'content_pillars' => $contentPillars,
            'distribution_channels' => $distributionChannels,
            'success_metrics' => $successMetrics
        ];
    }
    
    /**
     * Create a target audience description
     *
     * @param array $audienceInsights Audience insights from research
     * @return array Target audience description
     */
    protected function createTargetAudienceDescription($audienceInsights) {
        if (empty($audienceInsights)) {
            return [
                'demographics' => 'General audience interested in the topic',
                'psychographics' => 'Individuals seeking information and solutions',
                'pain_points' => ['Lack of information', 'Confusion about best practices']
            ];
        }
        
        $demographics = $audienceInsights['demographics'] ?? [];
        $interests = $audienceInsights['interests'] ?? [];
        $painPoints = $audienceInsights['pain_points'] ?? [];
        
        // Format demographics
        $demographicsDescription = '';
        if (!empty($demographics['age_range'])) {
            $demographicsDescription .= "Age range: " . $demographics['age_range'] . ". ";
        }
        if (!empty($demographics['gender_split'])) {
            $demographicsDescription .= "Gender: " . $demographics['gender_split'] . ". ";
        }
        if (!empty($demographics['education_level'])) {
            $demographicsDescription .= "Education: " . $demographics['education_level'] . ". ";
        }
        if (!empty($demographics['income_level'])) {
            $demographicsDescription .= "Income: " . $demographics['income_level'] . ".";
        }
        
        // Create psychographics from interests
        $psychographics = "Individuals interested in " . implode(", ", $interests) . ".";
        
        return [
            'demographics' => $demographicsDescription,
            'psychographics' => $psychographics,
            'pain_points' => $painPoints
        ];
    }
    
    /**
     * Extract key messages from research data
     *
     * @param array $keyPoints Key points from research
     * @param array $trends Trends from research
     * @return array Key messages
     */
    protected function extractKeyMessages($keyPoints, $trends) {
        $messages = [];
        
        // Extract messages from key points
        if (!empty($keyPoints)) {
            // Take up to 3 key points
            $selectedPoints = array_slice($keyPoints, 0, min(3, count($keyPoints)));
            foreach ($selectedPoints as $point) {
                $messages[] = $point;
            }
        }
        
        // Extract messages from trends
        if (!empty($trends)) {
            foreach ($trends as $trend) {
                if (isset($trend['description'])) {
                    $messages[] = $trend['description'];
                }
            }
        }
        
        // If we still need more messages, generate some generic ones
        if (count($messages) < 3) {
            $genericMessages = [
                "Our approach provides unique solutions to common problems.",
                "We offer evidence-based strategies that deliver results.",
                "Our comprehensive methodology addresses all aspects of the issue."
            ];
            
            $messages = array_merge($messages, array_slice($genericMessages, 0, 3 - count($messages)));
        }
        
        return $messages;
    }
    
    /**
     * Create content pillars based on topic and key points
     *
     * @param string $topic The main topic
     * @param array $keyPoints Key points from research
     * @return array Content pillars
     */
    protected function createContentPillars($topic, $keyPoints) {
        $pillars = [];
        
        // If we have key points, use them to create pillars
        if (!empty($keyPoints)) {
            // Group key points into 3-5 pillars
            $pillarCount = min(5, max(3, ceil(count($keyPoints) / 3)));
            
            for ($i = 0; $i < $pillarCount; $i++) {
                $pillarName = "Pillar " . ($i + 1) . ": " . $this->generatePillarName($topic, $i);
                $relatedPoints = [];
                
                // Assign key points to this pillar
                for ($j = $i; $j < count($keyPoints); $j += $pillarCount) {
                    if (isset($keyPoints[$j])) {
                        $relatedPoints[] = $keyPoints[$j];
                    }
                }
                
                $pillars[] = [
                    'name' => $pillarName,
                    'description' => "Content focused on " . strtolower(substr($pillarName, strpos($pillarName, ':') + 2)),
                    'related_points' => $relatedPoints
                ];
            }
        } else {
            // Create default pillars
            $defaultPillars = [
                "Fundamentals of {$topic}",
                "Advanced strategies for {$topic}",
                "Case studies and examples of {$topic}",
                "Future trends in {$topic}"
            ];
            
            foreach ($defaultPillars as $index => $pillarName) {
                $pillars[] = [
                    'name' => "Pillar " . ($index + 1) . ": " . $pillarName,
                    'description' => "Content focused on " . strtolower($pillarName),
                    'related_points' => []
                ];
            }
        }
        
        return $pillars;
    }
    
    /**
     * Generate a pillar name based on topic and index
     *
     * @param string $topic The main topic
     * @param int $index The pillar index
     * @return string Pillar name
     */
    protected function generatePillarName($topic, $index) {
        $pillarTemplates = [
            "Fundamentals of {$topic}",
            "Advanced strategies for {$topic}",
            "Practical applications of {$topic}",
            "Case studies in {$topic}",
            "Future trends in {$topic}",
            "Challenges and solutions in {$topic}",
            "Best practices for {$topic}",
            "Innovation in {$topic}"
        ];
        
        $actualIndex = $index % count($pillarTemplates);
        return $pillarTemplates[$actualIndex];
    }
    
    /**
     * Recommend distribution channels based on audience insights
     *
     * @param array $audienceInsights Audience insights from research
     * @return array Recommended distribution channels
     */
    protected function recommendDistributionChannels($audienceInsights) {
        $channels = [
            'Website/Blog' => [
                'priority' => 'High',
                'frequency' => 'Weekly',
                'notes' => 'Primary channel for in-depth content'
            ],
            'Email Newsletter' => [
                'priority' => 'Medium',
                'frequency' => 'Bi-weekly',
                'notes' => 'For nurturing existing audience'
            ],
            'Social Media' => [
                'priority' => 'High',
                'frequency' => 'Daily',
                'platforms' => ['LinkedIn', 'Twitter', 'Facebook'],
                'notes' => 'For audience building and content promotion'
            ]
        ];
        
        // Adjust based on audience insights if available
        if (!empty($audienceInsights['content_preferences']['preferred_formats'])) {
            $preferredFormats = $audienceInsights['content_preferences']['preferred_formats'];
            
            // Adjust for video preference
            if (in_array('Videos', $preferredFormats)) {
                $channels['YouTube'] = [
                    'priority' => 'High',
                    'frequency' => 'Weekly',
                    'notes' => 'For video content'
                ];
            }
            
            // Adjust for podcast preference
            if (in_array('Podcasts', $preferredFormats)) {
                $channels['Podcast Platforms'] = [
                    'priority' => 'Medium',
                    'frequency' => 'Weekly',
                    'platforms' => ['Spotify', 'Apple Podcasts', 'Google Podcasts'],
                    'notes' => 'For audio content'
                ];
            }
        }
        
        return $channels;
    }
    
    /**
     * Create a content calendar
     *
     * @param string $topic The main topic
     * @param int $contentQuantity Number of content pieces
     * @return array Content calendar
     */
    protected function createContentCalendar($topic, $contentQuantity) {
        $calendar = [];
        $startDate = time();
        
        for ($i = 0; $i < $contentQuantity; $i++) {
            // Schedule content every 7 days
            $publishDate = date('Y-m-d', $startDate + ($i * 7 * 24 * 60 * 60));
            
            $calendar[] = [
                'content_id' => $i + 1,
                'publish_date' => $publishDate,
                'status' => 'planned',
                'notes' => "Content piece " . ($i + 1) . " about {$topic}"
            ];
        }
        
        return $calendar;
    }
    
    /**
     * Create content pieces
     *
     * @param string $topic The main topic
     * @param array $researchData The research data
     * @param array $contentFormats Preferred content formats
     * @param int $contentQuantity Number of content pieces
     * @return array Content pieces
     */
    protected function createContentPieces($topic, $researchData, $contentFormats, $contentQuantity) {
        $contentPieces = [];
        
        // Get key points and trends from research data
        $keyPoints = $researchData['key_points'] ?? [];
        $trends = $researchData['trends'] ?? [];
        
        // Create subtopics based on key points and trends
        $subtopics = $this->createSubtopics($topic, $keyPoints, $trends);
        
        // Ensure we have enough subtopics
        while (count($subtopics) < $contentQuantity) {
            $subtopics[] = "Additional aspect of {$topic} " . (count($subtopics) + 1);
        }
        
        // Create content pieces
        for ($i = 0; $i < $contentQuantity; $i++) {
            $format = $contentFormats[array_rand($contentFormats)];
            $subtopic = $subtopics[$i % count($subtopics)];
            
            $contentPieces[] = [
                'id' => $i + 1,
                'title' => $this->generateTitle($subtopic, $format),
                'format' => $format,
                'subtopic' => $subtopic,
                'target_keywords' => $this->generateKeywords($subtopic),
                'outline' => $this->createOutline($subtopic, $format),
                'target_word_count' => $this->getTargetWordCount($format),
                'notes' => "This piece addresses {$subtopic} within the broader topic of {$topic}."
            ];
        }
        
        return $contentPieces;
    }
    
    /**
     * Create subtopics based on key points and trends
     *
     * @param string $topic The main topic
     * @param array $keyPoints Key points from research
     * @param array $trends Trends from research
     * @return array Subtopics
     */
    protected function createSubtopics($topic, $keyPoints, $trends) {
        $subtopics = [];
        
        // Extract subtopics from key points
        if (!empty($keyPoints)) {
            foreach ($keyPoints as $point) {
                // Extract a subtopic from the key point
                $subtopic = preg_replace('/^Key point \d+ about [\w\s]+: /', '', $point);
                $subtopics[] = $subtopic;
            }
        }
        
        // Extract subtopics from trends
        if (!empty($trends)) {
            foreach ($trends as $trend) {
                if (isset($trend['name'])) {
                    $subtopics[] = $trend['name'];
                }
            }
        }
        
        // If we don't have enough subtopics, create some generic ones
        if (empty($subtopics)) {
            $subtopics = [
                "Getting started with {$topic}",
                "Advanced techniques for {$topic}",
                "Common challenges in {$topic}",
                "Future of {$topic}",
                "Case studies in {$topic}"
            ];
        }
        
        return $subtopics;
    }
    
    /**
     * Generate a title for a content piece
     *
     * @param string $subtopic The subtopic
     * @param string $format The content format
     * @return string Title
     */
    protected function generateTitle($subtopic, $format) {
        $titleTemplates = [
            'Blog Post' => [
                "The Ultimate Guide to {subtopic}",
                "10 Ways to Master {subtopic}",
                "How {subtopic} Can Transform Your Business",
                "Why {subtopic} Matters More Than Ever",
                "{subtopic}: A Comprehensive Analysis"
            ],
            'Video' => [
                "{subtopic} Explained in 5 Minutes",
                "The Complete Video Guide to {subtopic}",
                "Watch How {subtopic} Works in Real-Time",
                "Expert Insights: {subtopic} Revealed",
                "Visual Guide to Understanding {subtopic}"
            ],
            'Infographic' => [
                "{subtopic} at a Glance",
                "The Visual Guide to {subtopic}",
                "{subtopic} by the Numbers",
                "Understanding {subtopic}: A Visual Breakdown",
                "The {subtopic} Process Visualized"
            ],
            'Podcast' => [
                "Deep Dive: Exploring {subtopic}",
                "Expert Conversations on {subtopic}",
                "The {subtopic} Discussion",
                "Unpacking {subtopic} with Industry Leaders",
                "The Future of {subtopic}: A Podcast Series"
            ],
            'Ebook' => [
                "The Complete Guide to {subtopic}",
                "Mastering {subtopic}: A Comprehensive Ebook",
                "{subtopic}: Strategies for Success",
                "The {subtopic} Handbook",
                "Everything You Need to Know About {subtopic}"
            ]
        ];
        
        // Use the appropriate template for the format, or default to blog post
        $templates = $titleTemplates[$format] ?? $titleTemplates['Blog Post'];
        
        // Select a random template
        $template = $templates[array_rand($templates)];
        
        // Replace {subtopic} with the actual subtopic
        return str_replace('{subtopic}', $subtopic, $template);
    }
    
    /**
     * Generate keywords for a subtopic
     *
     * @param string $subtopic The subtopic
     * @return array Keywords
     */
    protected function generateKeywords($subtopic) {
        $keywords = [];
        
        // Add the subtopic itself as a keyword
        $keywords[] = $subtopic;
        
        // Add variations
        $keywords[] = "best " . $subtopic;
        $keywords[] = $subtopic . " guide";
        $keywords[] = "how to " . $subtopic;
        $keywords[] = $subtopic . " examples";
        
        // Add a long-tail keyword
        $keywords[] = "how to implement " . $subtopic . " effectively";
        
        return $keywords;
    }
    
    /**
     * Create an outline for a content piece
     *
     * @param string $subtopic The subtopic
     * @param string $format The content format
     * @return array Outline
     */
    protected function createOutline($subtopic, $format) {
        $outline = [];
        
        // Add introduction
        $outline[] = [
            'section' => 'Introduction',
            'content' => "Introduce {$subtopic} and its importance."
        ];
        
        // Add main sections (varies by format)
        switch ($format) {
            case 'Blog Post':
                $outline[] = [
                    'section' => 'What is ' . $subtopic . '?',
                    'content' => "Define {$subtopic} and provide context."
                ];
                $outline[] = [
                    'section' => 'Why ' . $subtopic . ' Matters',
                    'content' => "Explain the importance and benefits of {$subtopic}."
                ];
                $outline[] = [
                    'section' => 'Key Components of ' . $subtopic,
                    'content' => "Break down the main elements of {$subtopic}."
                ];
                $outline[] = [
                    'section' => 'How to Implement ' . $subtopic,
                    'content' => "Provide practical steps for implementing {$subtopic}."
                ];
                $outline[] = [
                    'section' => 'Common Challenges and Solutions',
                    'content' => "Address potential obstacles and how to overcome them."
                ];
                break;
                
            case 'Video':
                $outline[] = [
                    'section' => 'Quick Overview',
                    'content' => "30-second summary of {$subtopic}."
                ];
                $outline[] = [
                    'section' => 'Visual Explanation',
                    'content' => "Visual breakdown of {$subtopic} with graphics."
                ];
                $outline[] = [
                    'section' => 'Practical Demonstration',
                    'content' => "Show {$subtopic} in action."
                ];
                $outline[] = [
                    'section' => 'Expert Tips',
                    'content' => "Share insider knowledge about {$subtopic}."
                ];
                break;
                
            case 'Infographic':
                $outline[] = [
                    'section' => 'Key Statistics',
                    'content' => "Visual representation of important numbers related to {$subtopic}."
                ];
                $outline[] = [
                    'section' => 'Process Flow',
                    'content' => "Step-by-step visual guide to {$subtopic}."
                ];
                $outline[] = [
                    'section' => 'Comparison Chart',
                    'content' => "Visual comparison of different aspects of {$subtopic}."
                ];
                $outline[] = [
                    'section' => 'Quick Tips',
                    'content' => "Visual bullets with actionable advice."
                ];
                break;
                
            default:
                $outline[] = [
                    'section' => 'Overview of ' . $subtopic,
                    'content' => "Provide a comprehensive overview of {$subtopic}."
                ];
                $outline[] = [
                    'section' => 'Key Aspects',
                    'content' => "Discuss the most important aspects of {$subtopic}."
                ];
                $outline[] = [
                    'section' => 'Practical Applications',
                    'content' => "Explain how {$subtopic} can be applied in real-world scenarios."
                ];
                break;
        }
        
        // Add conclusion
        $outline[] = [
            'section' => 'Conclusion',
            'content' => "Summarize key points about {$subtopic} and provide next steps."
        ];
        
        return $outline;
    }
    
    /**
     * Get target word count based on content format
     *
     * @param string $format The content format
     * @return int Target word count
     */
    protected function getTargetWordCount($format) {
        $wordCounts = [
            'Blog Post' => rand(1200, 2000),
            'Video' => rand(800, 1200), // Script word count
            'Infographic' => rand(300, 500), // Text elements
            'Podcast' => rand(2000, 3000), // Script word count
            'Ebook' => rand(5000, 10000),
            'Social Media' => rand(50, 280)
        ];
        
        return $wordCounts[$format] ?? 1500;
    }
    
    /**
     * Generate a summary of the content plan
     *
     * @param array $contentPlan The content plan
     * @return string Summary of the content plan
     */
    protected function generatePlanSummary($contentPlan) {
        $topic = $contentPlan['topic'];
        $contentPieceCount = count($contentPlan['content_pieces']);
        $pillarCount = count($contentPlan['strategy']['content_pillars']);
        
        $summary = "Content Plan Summary for {$topic}:\n\n";
        $summary .= "- Developed a content strategy with {$pillarCount} content pillars\n";
        $summary .= "- Created a content calendar with {$contentPieceCount} planned pieces\n";
        $summary .= "- Defined target audience and key messages\n";
        $summary .= "- Established distribution channels and success metrics\n";
        
        return $summary;
    }
}
