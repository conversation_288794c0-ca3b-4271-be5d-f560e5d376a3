<?php
/**
 * Editing Agent Class
 *
 * This agent is responsible for refining and improving content quality
 * for the Content Creation Brigade.
 */

require_once __DIR__ . '/../BaseAgent.php';

class EditingAgent extends BaseAgent {
    /**
     * Process an editing request
     *
     * @param array $request The request data including draft content
     * @return array The edited content
     */
    public function processRequest($request) {
        // Extract the topic and draft content
        $topic = $request['topic'] ?? '';
        $draftContent = $request['draft_content'] ?? null;
        $parameters = $request['parameters'] ?? [];
        
        if (!$draftContent) {
            $response = "Cannot edit content without draft content.";
            $this->recordInteraction(json_encode($request), $response, 'command', false);
            return ['error' => $response];
        }
        
        // Log the request
        $requestLog = "Editing request for topic: {$topic}";
        
        // Edit the content
        $editedContent = $this->editContent($topic, $draftContent, $parameters);
        
        // Generate a summary of the editing changes
        $summary = $this->generateEditingSummary($draftContent, $editedContent);
        
        // Record the interaction
        $this->recordInteraction(
            $requestLog,
            "Edited " . count($editedContent['content_pieces']) . " content pieces for {$topic}."
        );
        
        // Return the edited content
        return $editedContent;
    }
    
    /**
     * Edit content based on draft content
     *
     * @param string $topic The main topic
     * @param array $draftContent The draft content
     * @param array $parameters Additional parameters
     * @return array The edited content
     */
    protected function editContent($topic, $draftContent, $parameters = []) {
        // Extract content pieces from the draft content
        $draftPieces = $draftContent['content_pieces'] ?? [];
        
        // Determine which pieces to edit
        $pieceIndexToEdit = $parameters['piece_index'] ?? null;
        
        if ($pieceIndexToEdit !== null) {
            // Edit only the specified piece
            $piecesToEdit = [];
            foreach ($draftPieces as $piece) {
                if ($piece['id'] == $pieceIndexToEdit) {
                    $piecesToEdit[] = $piece;
                    break;
                }
            }
        } else {
            // Edit all pieces
            $piecesToEdit = $draftPieces;
        }
        
        // Edit each piece
        $editedPieces = [];
        foreach ($piecesToEdit as $piece) {
            $editedPieces[] = $this->editContentPiece($piece, $parameters);
        }
        
        // Return the edited content
        return [
            'topic' => $topic,
            'content_pieces' => $editedPieces,
            'edited_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Edit a specific content piece
     *
     * @param array $piece The content piece to edit
     * @param array $parameters Additional parameters
     * @return array The edited content piece
     */
    protected function editContentPiece($piece, $parameters = []) {
        // Extract piece details
        $id = $piece['id'];
        $title = $piece['title'];
        $format = $piece['format'];
        $subtopic = $piece['subtopic'];
        $sections = $piece['sections'];
        
        // Determine editing level
        $editingLevel = $parameters['editing_level'] ?? 'standard';
        
        // Edit the title
        $editedTitle = $this->editTitle($title, $editingLevel);
        
        // Edit each section
        $editedSections = [];
        foreach ($sections as $section) {
            $editedSections[] = [
                'title' => $section['title'],
                'content' => $this->editSectionContent($section['content'], $editingLevel)
            ];
        }
        
        // Calculate word count
        $editedWordCount = 0;
        foreach ($editedSections as $section) {
            $editedWordCount += str_word_count($section['content']);
        }
        
        // Return the edited piece
        return [
            'id' => $id,
            'title' => $editedTitle,
            'format' => $format,
            'subtopic' => $subtopic,
            'sections' => $editedSections,
            'original_word_count' => $piece['actual_word_count'] ?? $piece['target_word_count'],
            'edited_word_count' => $editedWordCount,
            'keywords_used' => $piece['keywords_used'],
            'editing_level' => $editingLevel,
            'editing_notes' => $this->generateEditingNotes($editingLevel),
            'edited_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Edit a title
     *
     * @param string $title The original title
     * @param string $editingLevel The level of editing to apply
     * @return string The edited title
     */
    protected function editTitle($title, $editingLevel = 'standard') {
        // In a real implementation, this would use more sophisticated editing techniques.
        // For this example, we'll make simple improvements.
        
        // Make title more compelling based on editing level
        switch ($editingLevel) {
            case 'light':
                // Just fix capitalization and basic issues
                return $this->fixCapitalization($title);
                
            case 'deep':
                // Make significant improvements
                return $this->makeMoreCompelling($title);
                
            case 'standard':
            default:
                // Standard editing
                $improvedTitle = $this->fixCapitalization($title);
                
                // Add power words if not already present
                $powerWords = ['Ultimate', 'Essential', 'Proven', 'Powerful', 'Effective'];
                $hasPowerWord = false;
                
                foreach ($powerWords as $word) {
                    if (stripos($improvedTitle, $word) !== false) {
                        $hasPowerWord = true;
                        break;
                    }
                }
                
                if (!$hasPowerWord && !str_starts_with($improvedTitle, 'The ')) {
                    $improvedTitle = 'The ' . $improvedTitle;
                }
                
                return $improvedTitle;
        }
    }
    
    /**
     * Fix capitalization in a title
     *
     * @param string $title The title to fix
     * @return string The fixed title
     */
    protected function fixCapitalization($title) {
        // Words that should not be capitalized (unless first or last word)
        $smallWords = ['a', 'an', 'the', 'and', 'but', 'or', 'for', 'nor', 'on', 'at', 'to', 'from', 'by', 'with', 'in', 'of'];
        
        // Split the title into words
        $words = explode(' ', $title);
        $result = [];
        
        // Process each word
        foreach ($words as $index => $word) {
            // Always capitalize first and last word
            if ($index === 0 || $index === count($words) - 1) {
                $result[] = ucfirst(strtolower($word));
            } 
            // Don't capitalize small words
            elseif (in_array(strtolower($word), $smallWords)) {
                $result[] = strtolower($word);
            } 
            // Capitalize other words
            else {
                $result[] = ucfirst(strtolower($word));
            }
        }
        
        return implode(' ', $result);
    }
    
    /**
     * Make a title more compelling
     *
     * @param string $title The original title
     * @return string A more compelling title
     */
    protected function makeMoreCompelling($title) {
        // First fix capitalization
        $title = $this->fixCapitalization($title);
        
        // Power words to potentially add
        $powerWords = ['Ultimate', 'Essential', 'Proven', 'Powerful', 'Effective', 'Comprehensive', 'Complete'];
        $powerWord = $powerWords[array_rand($powerWords)];
        
        // Number patterns to potentially add
        $numberPatterns = ['5 Essential', '7 Proven', '10 Powerful', '3 Simple'];
        $numberPattern = $numberPatterns[array_rand($numberPatterns)];
        
        // Question patterns
        $questionPatterns = ['How to', 'Why You Should', 'What Makes'];
        $questionPattern = $questionPatterns[array_rand($questionPatterns)];
        
        // Randomly select an enhancement approach
        $approach = rand(1, 4);
        
        switch ($approach) {
            case 1:
                // Add a power word if not already present
                if (!str_starts_with($title, 'The ')) {
                    return "The {$powerWord} " . substr($title, 0, 1) . strtolower(substr($title, 1));
                }
                return "The {$powerWord} " . substr($title, 4);
                
            case 2:
                // Add a number pattern
                if (preg_match('/^(The|A|An) /', $title)) {
                    return $numberPattern . ' ' . substr($title, strpos($title, ' ') + 1);
                }
                return $numberPattern . ' ' . $title;
                
            case 3:
                // Convert to a question format
                if (strpos($title, ':') !== false) {
                    $parts = explode(':', $title, 2);
                    return $parts[0] . ': ' . $questionPattern . ' ' . $parts[1];
                }
                return $questionPattern . ' ' . $title;
                
            case 4:
            default:
                // Add a subtitle
                if (strpos($title, ':') === false) {
                    $subtitles = [
                        'A Complete Guide',
                        'Everything You Need to Know',
                        'Strategies for Success',
                        'Expert Insights and Tips'
                    ];
                    $subtitle = $subtitles[array_rand($subtitles)];
                    return $title . ': ' . $subtitle;
                }
                return $title;
        }
    }
    
    /**
     * Edit section content
     *
     * @param string $content The original content
     * @param string $editingLevel The level of editing to apply
     * @return string The edited content
     */
    protected function editSectionContent($content, $editingLevel = 'standard') {
        // In a real implementation, this would use more sophisticated editing techniques.
        // For this example, we'll make simple improvements.
        
        // Apply different editing levels
        switch ($editingLevel) {
            case 'light':
                // Just fix basic issues
                $editedContent = $this->fixBasicIssues($content);
                break;
                
            case 'deep':
                // Make significant improvements
                $editedContent = $this->fixBasicIssues($content);
                $editedContent = $this->improveClarity($editedContent);
                $editedContent = $this->enhanceEngagement($editedContent);
                $editedContent = $this->improveStructure($editedContent);
                break;
                
            case 'standard':
            default:
                // Standard editing
                $editedContent = $this->fixBasicIssues($content);
                $editedContent = $this->improveClarity($editedContent);
                break;
        }
        
        return $editedContent;
    }
    
    /**
     * Fix basic issues in content
     *
     * @param string $content The content to fix
     * @return string The fixed content
     */
    protected function fixBasicIssues($content) {
        // Fix common typos
        $typos = [
            'teh' => 'the',
            'adn' => 'and',
            'waht' => 'what',
            'taht' => 'that',
            'thier' => 'their',
            'recieve' => 'receive',
            'seperate' => 'separate',
            'definately' => 'definitely',
            'accomodate' => 'accommodate',
            'occured' => 'occurred',
            'neccessary' => 'necessary',
            'buisness' => 'business',
            'gaurd' => 'guard',
            'cemetary' => 'cemetery',
            'wierd' => 'weird',
            'concious' => 'conscious',
            'truely' => 'truly',
            'existance' => 'existence',
            'enviroment' => 'environment',
            'goverment' => 'government'
        ];
        
        foreach ($typos as $typo => $correction) {
            $content = str_ireplace($typo, $correction, $content);
        }
        
        // Fix double spaces
        $content = preg_replace('/\s+/', ' ', $content);
        
        // Fix spacing after punctuation
        $content = preg_replace('/([.!?])\s*(\w)/', '$1 $2', $content);
        
        // Ensure proper paragraph breaks
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        
        return $content;
    }
    
    /**
     * Improve clarity of content
     *
     * @param string $content The content to improve
     * @return string The improved content
     */
    protected function improveClarity($content) {
        // Replace weak phrases with stronger alternatives
        $weakPhrases = [
            'in order to' => 'to',
            'a number of' => 'several',
            'the majority of' => 'most',
            'in spite of the fact that' => 'although',
            'due to the fact that' => 'because',
            'in the event that' => 'if',
            'in the process of' => 'while',
            'it is important to note that' => 'notably',
            'for the purpose of' => 'for',
            'with regard to' => 'regarding',
            'in reference to' => 'about',
            'with the exception of' => 'except for',
            'in the near future' => 'soon',
            'at this point in time' => 'now',
            'for all intents and purposes' => 'essentially',
            'it is clear that' => '',
            'needless to say' => '',
            'as a matter of fact' => 'in fact'
        ];
        
        foreach ($weakPhrases as $weak => $strong) {
            $content = str_ireplace($weak, $strong, $content);
        }
        
        // Remove redundant words
        $redundantPhrases = [
            'absolutely essential',
            'actual fact',
            'advance planning',
            'basic fundamentals',
            'completely eliminate',
            'current status',
            'end result',
            'future plans',
            'past history',
            'personal opinion',
            'repeat again',
            'unexpected surprise',
            'unintentional mistake',
            'very unique'
        ];
        
        foreach ($redundantPhrases as $phrase) {
            $parts = explode(' ', $phrase);
            $replacement = end($parts);
            $content = str_ireplace($phrase, $replacement, $content);
        }
        
        return $content;
    }
    
    /**
     * Enhance engagement in content
     *
     * @param string $content The content to enhance
     * @return string The enhanced content
     */
    protected function enhanceEngagement($content) {
        // Add transition phrases between paragraphs
        $transitions = [
            'Furthermore, ',
            'Moreover, ',
            'In addition, ',
            'Similarly, ',
            'Consequently, ',
            'As a result, ',
            'Therefore, ',
            'However, ',
            'Nevertheless, ',
            'On the other hand, ',
            'Interestingly, ',
            'Notably, '
        ];
        
        // Split into paragraphs
        $paragraphs = explode("\n\n", $content);
        
        // Add transitions to some paragraphs (not the first one)
        for ($i = 1; $i < count($paragraphs); $i++) {
            // Only add transitions to some paragraphs (about 1/3)
            if (rand(1, 3) === 1 && !empty($paragraphs[$i])) {
                $transition = $transitions[array_rand($transitions)];
                
                // Check if paragraph already starts with a transition
                $firstFewWords = strtolower(substr($paragraphs[$i], 0, 15));
                $hasTransition = false;
                
                foreach ($transitions as $t) {
                    if (strpos($firstFewWords, strtolower(trim($t, ' ,'))) === 0) {
                        $hasTransition = true;
                        break;
                    }
                }
                
                if (!$hasTransition) {
                    $paragraphs[$i] = $transition . lcfirst(trim($paragraphs[$i]));
                }
            }
        }
        
        // Rejoin paragraphs
        $content = implode("\n\n", $paragraphs);
        
        // Add rhetorical questions in appropriate places
        if (strlen($content) > 500 && strpos($content, '?') === false) {
            $questions = [
                "But what does this mean for you?",
                "How can you apply this in your own context?",
                "Why is this so important?",
                "What makes this approach so effective?",
                "Have you considered how this impacts your strategy?"
            ];
            
            // Find a suitable position (roughly in the middle)
            $middlePosition = strlen($content) / 2;
            $nearestParagraphBreak = strrpos(substr($content, 0, $middlePosition), "\n\n");
            
            if ($nearestParagraphBreak !== false) {
                $question = $questions[array_rand($questions)] . "\n\n";
                $content = substr($content, 0, $nearestParagraphBreak) . "\n\n" . $question . substr($content, $nearestParagraphBreak + 2);
            }
        }
        
        return $content;
    }
    
    /**
     * Improve structure of content
     *
     * @param string $content The content to improve
     * @return string The improved content
     */
    protected function improveStructure($content) {
        // Split into paragraphs
        $paragraphs = explode("\n\n", $content);
        
        // Check if we need to break up long paragraphs
        for ($i = 0; $i < count($paragraphs); $i++) {
            $paragraph = $paragraphs[$i];
            
            // If paragraph is very long, try to break it up
            if (strlen($paragraph) > 800) {
                // Find a good breaking point (after a sentence, roughly in the middle)
                $middlePosition = strlen($paragraph) / 2;
                $sentenceBreak = strpos($paragraph, '. ', $middlePosition - 100);
                
                if ($sentenceBreak !== false && $sentenceBreak < $middlePosition + 100) {
                    $sentenceBreak += 2; // Move past the period and space
                    $paragraphs[$i] = substr($paragraph, 0, $sentenceBreak);
                    array_splice($paragraphs, $i + 1, 0, substr($paragraph, $sentenceBreak));
                }
            }
        }
        
        // Check if we need to add bullet points for clarity
        for ($i = 0; $i < count($paragraphs); $i++) {
            $paragraph = $paragraphs[$i];
            
            // Look for lists indicated by commas or semicolons
            if (substr_count($paragraph, ', ') >= 3 || substr_count($paragraph, '; ') >= 3) {
                // Check if the paragraph contains phrases like "include", "such as", "following"
                $listIndicators = ['include', 'such as', 'following', 'several', 'various', 'many', 'multiple'];
                $hasIndicator = false;
                
                foreach ($listIndicators as $indicator) {
                    if (stripos($paragraph, $indicator) !== false) {
                        $hasIndicator = true;
                        break;
                    }
                }
                
                if ($hasIndicator) {
                    // Try to convert to bullet points
                    $parts = preg_split('/(:|-)/', $paragraph, 2, PREG_SPLIT_DELIM_CAPTURE);
                    
                    if (count($parts) >= 3) {
                        $intro = $parts[0] . $parts[1];
                        $listContent = $parts[2];
                        
                        // Split the list content by commas or semicolons
                        $items = preg_split('/[,;]\s+/', $listContent);
                        
                        // Format as bullet points
                        $bulletList = $intro . "\n";
                        foreach ($items as $item) {
                            $item = trim($item);
                            if (!empty($item)) {
                                // Capitalize first letter if it's not already
                                $item = ucfirst($item);
                                $bulletList .= "- {$item}\n";
                            }
                        }
                        
                        $paragraphs[$i] = $bulletList;
                    }
                }
            }
        }
        
        // Rejoin paragraphs
        return implode("\n\n", $paragraphs);
    }
    
    /**
     * Generate editing notes
     *
     * @param string $editingLevel The level of editing applied
     * @return array Editing notes
     */
    protected function generateEditingNotes($editingLevel) {
        $notes = [
            'improvements' => [],
            'suggestions' => []
        ];
        
        // Add improvements based on editing level
        switch ($editingLevel) {
            case 'light':
                $notes['improvements'] = [
                    'Fixed basic spelling and grammar issues',
                    'Corrected punctuation and spacing',
                    'Ensured consistent formatting'
                ];
                $notes['suggestions'] = [
                    'Consider adding more specific examples',
                    'The content could benefit from more engaging language',
                    'Consider breaking up longer paragraphs for readability'
                ];
                break;
                
            case 'deep':
                $notes['improvements'] = [
                    'Comprehensive grammar and spelling corrections',
                    'Significantly improved clarity and conciseness',
                    'Enhanced overall structure and flow',
                    'Added engaging elements like questions and transitions',
                    'Improved formatting for better readability',
                    'Strengthened the title for better impact',
                    'Eliminated redundancies and weak phrasing'
                ];
                $notes['suggestions'] = [
                    'Consider adding more industry-specific examples',
                    'The conclusion could be strengthened with a stronger call to action',
                    'Consider adding data visualizations for complex concepts'
                ];
                break;
                
            case 'standard':
            default:
                $notes['improvements'] = [
                    'Corrected grammar and spelling issues',
                    'Improved clarity by eliminating weak phrases',
                    'Enhanced readability with better paragraph structure',
                    'Strengthened the title',
                    'Fixed formatting inconsistencies'
                ];
                $notes['suggestions'] = [
                    'Consider adding more specific examples',
                    'Some sections could benefit from additional supporting evidence',
                    'Consider incorporating more engaging elements like questions or stories'
                ];
                break;
        }
        
        return $notes;
    }
    
    /**
     * Generate a summary of the editing changes
     *
     * @param array $draftContent The original draft content
     * @param array $editedContent The edited content
     * @return string Summary of the editing changes
     */
    protected function generateEditingSummary($draftContent, $editedContent) {
        $topic = $draftContent['topic'];
        $contentPieceCount = count($editedContent['content_pieces']);
        
        $totalOriginalWordCount = 0;
        $totalEditedWordCount = 0;
        $improvementTypes = [];
        
        foreach ($editedContent['content_pieces'] as $piece) {
            $totalOriginalWordCount += $piece['original_word_count'];
            $totalEditedWordCount += $piece['edited_word_count'];
            
            // Collect improvement types
            if (isset($piece['editing_notes']['improvements'])) {
                foreach ($piece['editing_notes']['improvements'] as $improvement) {
                    $improvementTypes[$improvement] = ($improvementTypes[$improvement] ?? 0) + 1;
                }
            }
        }
        
        $wordCountChange = $totalEditedWordCount - $totalOriginalWordCount;
        $wordCountChangePercent = $totalOriginalWordCount > 0 ? round(($wordCountChange / $totalOriginalWordCount) * 100, 1) : 0;
        
        $summary = "Editing Summary for {$topic}:\n\n";
        $summary .= "- Edited {$contentPieceCount} content pieces\n";
        $summary .= "- Original word count: {$totalOriginalWordCount} words\n";
        $summary .= "- Edited word count: {$totalEditedWordCount} words\n";
        $summary .= "- Word count change: {$wordCountChange} words ({$wordCountChangePercent}%)\n";
        
        $summary .= "- Key improvements:\n";
        foreach ($improvementTypes as $improvement => $count) {
            if ($count >= $contentPieceCount / 2) {
                $summary .= "  - {$improvement}\n";
            }
        }
        
        return $summary;
    }
}
