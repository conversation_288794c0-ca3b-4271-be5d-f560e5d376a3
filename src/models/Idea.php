<?php
/**
 * Idea Model
 * 
 * Handles idea-related database operations for the brain dump feature.
 */

require_once __DIR__ . '/BaseModel.php';

class Idea extends BaseModel {
    protected $table = 'ideas';
    
    /**
     * Get ideas for a specific user
     */
    public function getUserIdeas($userId, $filters = []) {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ?";
        $params = [$userId];
        
        // Apply filters
        if (!empty($filters)) {
            // Filter by tag
            if (isset($filters['tag'])) {
                $sql .= " AND tags LIKE ?";
                $params[] = "%{$filters['tag']}%";
            }
            
            // Filter by date
            if (isset($filters['date'])) {
                $sql .= " AND DATE(created_at) = ?";
                $params[] = $filters['date'];
            }
        }
        
        // Order by creation date, newest first
        $sql .= " ORDER BY created_at DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Convert an idea to a task
     */
    public function convertToTask($ideaId, $userId, $taskData) {
        // Get the idea
        $idea = $this->find($ideaId);
        
        if (!$idea || $idea['user_id'] != $userId) {
            return false;
        }
        
        // Create a new task
        require_once __DIR__ . '/Task.php';
        $taskModel = new Task();
        
        // Set default task data from idea
        $taskData['title'] = $taskData['title'] ?? $idea['title'];
        $taskData['description'] = $taskData['description'] ?? $idea['content'];
        $taskData['user_id'] = $userId;
        $taskData['created_at'] = date('Y-m-d H:i:s');
        $taskData['updated_at'] = date('Y-m-d H:i:s');
        
        // Create the task
        $taskId = $taskModel->create($taskData);
        
        if ($taskId) {
            // Update the idea to mark it as converted
            $this->update($ideaId, [
                'converted_to_task' => 1,
                'task_id' => $taskId,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            return $taskId;
        }
        
        return false;
    }
    
    /**
     * Get recent ideas for a specific user
     */
    public function getRecentIdeas($userId, $limit = 5) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE user_id = ? 
                ORDER BY created_at DESC 
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$userId, $limit]);
    }
}
