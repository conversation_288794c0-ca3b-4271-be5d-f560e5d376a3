<?php
/**
 * AI Prompt Category Model
 *
 * Handles AI prompt category management
 */

require_once __DIR__ . '/BaseModel.php';

class AIPromptCategory extends BaseModel {
    protected $table = 'ai_prompt_categories';

    /**
     * Get categories for a user (including system categories)
     */
    public function getUserCategories($userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? OR is_system = 1
                ORDER BY is_system DESC, display_order ASC, name ASC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get user's custom categories only
     */
    public function getCustomCategories($userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND is_system = 0
                ORDER BY display_order ASC, name ASC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get system categories
     */
    public function getSystemCategories() {
        $sql = "SELECT * FROM {$this->table}
                WHERE is_system = 1
                ORDER BY display_order ASC, name ASC";

        return $this->db->fetchAll($sql);
    }

    /**
     * Create a new category
     */
    public function createCategory($data) {
        // Validate required fields
        if (empty($data['name'])) {
            return false;
        }

        // Get next display order
        $maxOrderSql = "SELECT MAX(display_order) as max_order FROM {$this->table} WHERE user_id = ?";
        $maxOrder = $this->db->fetchOne($maxOrderSql, [$data['user_id']]);
        $nextOrder = ($maxOrder['max_order'] ?? 0) + 1;

        $categoryData = [
            'user_id' => $data['user_id'],
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'color' => $data['color'] ?? '#6366F1',
            'icon' => $data['icon'] ?? 'fa-folder',
            'display_order' => $nextOrder,
            'is_system' => 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->create($categoryData);
    }

    /**
     * Update category
     */
    public function updateCategory($id, $data) {
        // Don't allow updating system categories
        $category = $this->find($id);
        if (!$category || $category['is_system']) {
            return false;
        }

        $updateData = [
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'color' => $data['color'] ?? '#6366F1',
            'icon' => $data['icon'] ?? 'fa-folder',
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->update($id, $updateData);
    }

    /**
     * Delete category (only custom categories)
     */
    public function deleteCategory($id, $userId) {
        // Check if category exists and belongs to user
        $category = $this->find($id);
        if (!$category || $category['user_id'] != $userId || $category['is_system']) {
            return false;
        }

        // Check if category has prompts
        $promptCount = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM ai_prompts WHERE category_id = ?",
            [$id]
        );

        if ($promptCount['count'] > 0) {
            // Move prompts to uncategorized (null category)
            $this->execute(
                "UPDATE ai_prompts SET category_id = NULL WHERE category_id = ?",
                [$id]
            );
        }

        return $this->delete($id);
    }

    /**
     * Reorder categories
     */
    public function reorderCategories($userId, $categoryOrders) {
        if (!is_array($categoryOrders)) {
            return false;
        }

        $success = true;
        foreach ($categoryOrders as $categoryId => $order) {
            $sql = "UPDATE {$this->table}
                    SET display_order = ?, updated_at = ?
                    WHERE id = ? AND user_id = ? AND is_system = 0";

            $result = $this->execute($sql, [
                $order,
                date('Y-m-d H:i:s'),
                $categoryId,
                $userId
            ]);

            if (!$result) {
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Get category with prompt count
     */
    public function getCategoriesWithCounts($userId) {
        $sql = "SELECT c.*,
                       COUNT(p.id) as prompt_count,
                       AVG(p.effectiveness_rating) as avg_rating
                FROM {$this->table} c
                LEFT JOIN ai_prompts p ON c.id = p.category_id AND p.user_id = ?
                WHERE c.user_id = ? OR c.is_system = 1
                GROUP BY c.id
                ORDER BY c.is_system DESC, c.display_order ASC, c.name ASC";

        return $this->db->fetchAll($sql, [$userId, $userId]);
    }

    /**
     * Get category statistics
     */
    public function getCategoryStats($userId) {
        $sql = "SELECT
                    COUNT(*) as total_categories,
                    COUNT(CASE WHEN is_system = 0 THEN 1 END) as custom_categories,
                    COUNT(CASE WHEN is_system = 1 THEN 1 END) as system_categories
                FROM {$this->table}
                WHERE user_id = ? OR is_system = 1";

        return $this->db->fetchOne($sql, [$userId]);
    }

    /**
     * Check if category name exists for user
     */
    public function categoryNameExists($userId, $name, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}
                WHERE (user_id = ? OR is_system = 1) AND name = ?";
        $params = [$userId, $name];

        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }

        $result = $this->db->fetchOne($sql, $params);
        return $result['count'] > 0;
    }

    /**
     * Get default colors for categories
     */
    public static function getDefaultColors() {
        return [
            '#EF4444', // Red
            '#F97316', // Orange
            '#F59E0B', // Amber
            '#EAB308', // Yellow
            '#84CC16', // Lime
            '#22C55E', // Green
            '#10B981', // Emerald
            '#14B8A6', // Teal
            '#06B6D4', // Cyan
            '#0EA5E9', // Sky
            '#3B82F6', // Blue
            '#6366F1', // Indigo
            '#8B5CF6', // Violet
            '#A855F7', // Purple
            '#D946EF', // Fuchsia
            '#EC4899', // Pink
            '#F43F5E', // Rose
        ];
    }

    /**
     * Get default icons for categories
     */
    public static function getDefaultIcons() {
        return [
            'fa-brain',
            'fa-lightbulb',
            'fa-pen-fancy',
            'fa-chart-line',
            'fa-code',
            'fa-graduation-cap',
            'fa-briefcase',
            'fa-cogs',
            'fa-rocket',
            'fa-star',
            'fa-heart',
            'fa-bookmark',
            'fa-folder',
            'fa-tag',
            'fa-puzzle-piece',
            'fa-magic',
            'fa-fire',
            'fa-gem'
        ];
    }
}
