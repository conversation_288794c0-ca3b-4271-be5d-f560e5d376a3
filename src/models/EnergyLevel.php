<?php
/**
 * Energy Level Model
 *
 * Handles energy level tracking functionality for productivity management
 */

require_once __DIR__ . '/BaseModel.php';

class EnergyLevel extends BaseModel {
    protected $table = 'energy_levels';

    public function __construct() {
        parent::__construct();
    }

    /**
     * Get all energy level records for a user
     */
    public function getUserEnergyLevels($userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                ORDER BY recorded_at DESC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get energy level records for a specific date range
     */
    public function getEnergyLevelsByDateRange($userId, $startDate, $endDate) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND recorded_at BETWEEN ? AND ?
                ORDER BY recorded_at ASC";

        return $this->db->fetchAll($sql, [$userId, $startDate, $endDate]);
    }

    /**
     * Get energy level records for today
     */
    public function getTodayEnergyLevels($userId) {
        $today = date('Y-m-d');
        $startDate = $today . ' 00:00:00';
        $endDate = $today . ' 23:59:59';

        return $this->getEnergyLevelsByDateRange($userId, $startDate, $endDate);
    }

    /**
     * Get energy level records for the past week
     */
    public function getWeekEnergyLevels($userId) {
        $endDate = date('Y-m-d H:i:s');
        $startDate = date('Y-m-d H:i:s', strtotime('-7 days'));

        return $this->getEnergyLevelsByDateRange($userId, $startDate, $endDate);
    }

    /**
     * Get energy level records for the past month
     */
    public function getMonthEnergyLevels($userId) {
        $endDate = date('Y-m-d H:i:s');
        $startDate = date('Y-m-d H:i:s', strtotime('-30 days'));

        return $this->getEnergyLevelsByDateRange($userId, $startDate, $endDate);
    }

    /**
     * Get the latest energy level record for a user
     */
    public function getLatestEnergyLevel($userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                ORDER BY recorded_at DESC
                LIMIT 1";

        return $this->db->fetchOne($sql, [$userId]);
    }

    /**
     * Get average energy level by hour of day
     * Returns an array with hour => avg_level
     */
    public function getAverageEnergyLevelByHour($userId, $days = 30) {
        $endDate = date('Y-m-d H:i:s');
        $startDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));

        $sql = "SELECT HOUR(recorded_at) as hour, AVG(level) as avg_level
                FROM {$this->table}
                WHERE user_id = ? AND recorded_at BETWEEN ? AND ?
                GROUP BY HOUR(recorded_at)
                ORDER BY hour ASC";

        $results = $this->db->fetchAll($sql, [$userId, $startDate, $endDate]);

        // Convert to hour => avg_level format
        $hourlyAverages = [];
        foreach ($results as $row) {
            $hourlyAverages[$row['hour']] = round($row['avg_level'], 1);
        }

        return $hourlyAverages;
    }

    /**
     * Get average energy level by day of week
     * Returns an array with day_of_week => avg_level
     */
    public function getAverageEnergyLevelByDayOfWeek($userId, $days = 90) {
        $endDate = date('Y-m-d H:i:s');
        $startDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));

        $sql = "SELECT DAYOFWEEK(recorded_at) as day_of_week, AVG(level) as avg_level
                FROM {$this->table}
                WHERE user_id = ? AND recorded_at BETWEEN ? AND ?
                GROUP BY DAYOFWEEK(recorded_at)
                ORDER BY day_of_week ASC";

        $results = $this->db->fetchAll($sql, [$userId, $startDate, $endDate]);

        // Convert to day_of_week => avg_level format (1 = Sunday, 7 = Saturday)
        $dayOfWeekAverages = [];
        foreach ($results as $row) {
            $dayOfWeekAverages[$row['day_of_week']] = round($row['avg_level'], 1);
        }

        return $dayOfWeekAverages;
    }

    /**
     * Create a new energy level record
     */
    public function create($data) {
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->db->insert($this->table, $data);
    }

    /**
     * Update an energy level record
     */
    public function update($id, $data) {
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->db->update($this->table, $data, ['id' => $id]);
    }

    /**
     * Delete an energy level record
     */
    public function delete($id) {
        return $this->db->delete($this->table, ['id' => $id]);
    }

    /**
     * Find an energy level record by ID
     *
     * @param int $id Record ID
     * @param bool $useCache Whether to use cache
     * @return array|false Record data or false if not found
     */
    public function find($id, $useCache = null) {
        // Use class default if not specified
        $useCache = $useCache !== null ? $useCache : $this->useCache;

        // Generate cache key
        $cacheKey = "model_{$this->table}_find_{$id}";

        // Try to get from cache first
        if ($useCache && $this->cache->has($cacheKey)) {
            return $this->cache->get($cacheKey);
        }

        $sql = "SELECT * FROM {$this->table}
                WHERE id = ?
                LIMIT 1";

        $result = $this->db->fetchOne($sql, [$id]);

        // Store in cache if found
        if ($result && $useCache) {
            $this->cache->set($cacheKey, $result, $this->cacheTTL);
        }

        return $result;
    }

    /**
     * Check if an energy level record belongs to a user
     */
    public function belongsToUser($id, $userId) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}
                WHERE id = ? AND user_id = ?";

        $result = $this->db->fetchOne($sql, [$id, $userId]);

        return $result['count'] > 0;
    }

    /**
     * Get energy level recommendations for optimal scheduling
     * Returns an array with hour => recommendation (high, medium, low)
     */
    public function getEnergyLevelRecommendations($userId) {
        $hourlyAverages = $this->getAverageEnergyLevelByHour($userId);

        $recommendations = [];
        foreach (range(0, 23) as $hour) {
            if (isset($hourlyAverages[$hour])) {
                $level = $hourlyAverages[$hour];
                if ($level >= 7) {
                    $recommendations[$hour] = 'high';
                } elseif ($level >= 4) {
                    $recommendations[$hour] = 'medium';
                } else {
                    $recommendations[$hour] = 'low';
                }
            } else {
                $recommendations[$hour] = 'unknown';
            }
        }

        return $recommendations;
    }
}
