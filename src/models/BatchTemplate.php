<?php
/**
 * Batch Template Model
 *
 * Handles batch template functionality for productivity management
 */

require_once __DIR__ . '/BaseModel.php';

class BatchTemplate extends BaseModel {
    protected $table = 'batch_templates';

    public function __construct() {
        parent::__construct();
    }

    /**
     * Get all batch templates for a user
     */
    public function getUserTemplates($userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                ORDER BY name ASC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get batch templates with items
     */
    public function getTemplatesWithItems($userId) {
        $templates = $this->getUserTemplates($userId);

        if (empty($templates)) {
            return [];
        }

        // Get items for each template
        foreach ($templates as &$template) {
            $template['items'] = $this->getTemplateItems($template['id']);
            $template['item_count'] = count($template['items']);
        }

        return $templates;
    }

    /**
     * Get items for a template
     */
    public function getTemplateItems($templateId) {
        $sql = "SELECT * FROM batch_template_items
                WHERE template_id = ?
                ORDER BY position ASC";

        return $this->db->fetchAll($sql, [$templateId]);
    }

    /**
     * Get a specific template with its items
     */
    public function getTemplateWithItems($templateId) {
        $template = $this->find($templateId);

        if (!$template) {
            return null;
        }

        $template['items'] = $this->getTemplateItems($templateId);
        $template['item_count'] = count($template['items']);

        return $template;
    }

    /**
     * Create a new batch template
     */
    public function create($data) {
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        $templateId = $this->db->insert($this->table, $data);

        return $templateId;
    }

    /**
     * Add items to a template
     */
    public function addItemsToTemplate($templateId, $items) {
        $position = 0;

        foreach ($items as $item) {
            $itemData = [
                'template_id' => $templateId,
                'task_type' => $item['task_type'],
                'description' => $item['description'] ?? null,
                'estimated_time' => $item['estimated_time'] ?? null,
                'position' => $position
            ];

            $this->db->insert('batch_template_items', $itemData);
            $position++;
        }

        return true;
    }

    /**
     * Remove an item from a template
     */
    public function removeItemFromTemplate($itemId) {
        $sql = "DELETE FROM batch_template_items
                WHERE id = ?";

        return $this->db->execute($sql, [$itemId]);
    }

    /**
     * Update a batch template
     */
    public function update($id, $data) {
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->db->update($this->table, $data, ['id' => $id]);
    }

    /**
     * Delete a batch template
     */
    public function delete($id) {
        // First delete all template items
        $sql = "DELETE FROM batch_template_items WHERE template_id = ?";
        $this->db->execute($sql, [$id]);

        // Then delete the template
        return $this->db->delete($this->table, ['id' => $id]);
    }

    /**
     * Find a batch template by ID
     *
     * @param int $id Record ID
     * @param bool $useCache Whether to use cache
     * @return array|false Record data or false if not found
     */
    public function find($id, $useCache = null) {
        // Use class default if not specified
        $useCache = $useCache !== null ? $useCache : $this->useCache;

        // Generate cache key
        $cacheKey = "model_{$this->table}_find_{$id}";

        // Try to get from cache first
        if ($useCache && $this->cache->has($cacheKey)) {
            return $this->cache->get($cacheKey);
        }

        $sql = "SELECT * FROM {$this->table}
                WHERE id = ?
                LIMIT 1";

        $result = $this->db->fetchOne($sql, [$id]);

        // Store in cache if found
        if ($result && $useCache) {
            $this->cache->set($cacheKey, $result, $this->cacheTTL);
        }

        return $result;
    }

    /**
     * Check if a template belongs to a user
     */
    public function belongsToUser($id, $userId) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}
                WHERE id = ? AND user_id = ?";

        $result = $this->db->fetchOne($sql, [$id, $userId]);

        return $result['count'] > 0;
    }

    /**
     * Reorder items in a template
     */
    public function reorderTemplateItems($templateId, $itemIds) {
        $position = 0;

        foreach ($itemIds as $itemId) {
            $this->db->update('batch_template_items', ['position' => $position], ['id' => $itemId]);
            $position++;
        }

        return true;
    }

    /**
     * Create a batch from a template
     */
    public function createBatchFromTemplate($templateId, $userId) {
        // Get template
        $template = $this->getTemplateWithItems($templateId);

        if (!$template) {
            return false;
        }

        // Create batch
        require_once __DIR__ . '/TaskBatch.php';
        $taskBatchModel = new TaskBatch();

        $batchData = [
            'user_id' => $userId,
            'template_id' => $templateId,
            'name' => $template['name'],
            'description' => $template['description'],
            'energy_level' => $template['energy_level'],
            'estimated_time' => $template['estimated_time'],
            'status' => 'active',
            'is_recurring' => $template['is_recurring'],
            'recurrence_pattern' => $template['recurrence_pattern']
        ];

        $batchId = $taskBatchModel->create($batchData);

        if (!$batchId) {
            return false;
        }

        return $batchId;
    }

    /**
     * Get recurring templates that need to be generated
     */
    public function getRecurringTemplatesForGeneration() {
        $today = date('Y-m-d');

        $sql = "SELECT bt.*, tb.id as batch_id, tb.last_generated
                FROM {$this->table} bt
                LEFT JOIN task_batches tb ON bt.id = tb.template_id AND tb.is_recurring = 1
                WHERE bt.is_recurring = 1
                AND (tb.last_generated IS NULL OR tb.last_generated < ?)
                ORDER BY bt.user_id";

        return $this->db->fetchAll($sql, [$today]);
    }

    /**
     * Generate batches from recurring templates
     */
    public function generateRecurringBatches() {
        $templates = $this->getRecurringTemplatesForGeneration();
        $generatedCount = 0;

        foreach ($templates as $template) {
            // Check if we should generate a batch today based on recurrence pattern
            if (!$this->shouldGenerateToday($template['recurrence_pattern'], $template['last_generated'])) {
                continue;
            }

            // Create batch from template
            $batchId = $this->createBatchFromTemplate($template['id'], $template['user_id']);

            if ($batchId) {
                // Update last_generated date
                require_once __DIR__ . '/TaskBatch.php';
                $taskBatchModel = new TaskBatch();
                $taskBatchModel->update($batchId, ['last_generated' => date('Y-m-d')]);

                $generatedCount++;
            }
        }

        return $generatedCount;
    }

    /**
     * Check if a batch should be generated today based on recurrence pattern
     */
    private function shouldGenerateToday($pattern, $lastGenerated) {
        $today = date('Y-m-d');
        $dayOfWeek = date('N'); // 1 (Monday) to 7 (Sunday)

        // If never generated, always generate
        if (!$lastGenerated) {
            return true;
        }

        switch ($pattern) {
            case 'daily':
                // Generate if last generation was not today
                return $lastGenerated < $today;

            case 'weekdays':
                // Generate on weekdays (Monday to Friday) if last generation was not today
                return $dayOfWeek <= 5 && $lastGenerated < $today;

            case 'weekly':
                // Generate if it's been at least 7 days since last generation
                $lastDate = new DateTime($lastGenerated);
                $todayDate = new DateTime($today);
                $diff = $lastDate->diff($todayDate);
                return $diff->days >= 7;

            case 'monthly':
                // Generate if it's the same day of month as when the template was created
                // and it's been at least 28 days since last generation
                $lastDate = new DateTime($lastGenerated);
                $todayDate = new DateTime($today);
                $diff = $lastDate->diff($todayDate);
                return $diff->days >= 28 && date('j') == date('j', strtotime($lastGenerated));

            default:
                return false;
        }
    }

    /**
     * Get template statistics
     */
    public function getTemplateStatistics($userId) {
        $sql = "SELECT
                    COUNT(*) as total_templates,
                    SUM(CASE WHEN is_recurring = 1 THEN 1 ELSE 0 END) as recurring_templates,
                    SUM(CASE WHEN energy_level = 'high' THEN 1 ELSE 0 END) as high_energy_templates,
                    SUM(CASE WHEN energy_level = 'medium' THEN 1 ELSE 0 END) as medium_energy_templates,
                    SUM(CASE WHEN energy_level = 'low' THEN 1 ELSE 0 END) as low_energy_templates
                FROM {$this->table}
                WHERE user_id = ?";

        return $this->db->fetchOne($sql, [$userId]);
    }
}
