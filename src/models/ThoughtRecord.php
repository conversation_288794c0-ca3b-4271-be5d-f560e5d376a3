<?php
/**
 * Thought Record Model
 * 
 * Handles CBT thought records
 */

require_once __DIR__ . '/BaseModel.php';

class ThoughtRecord extends BaseModel {
    protected $table = 'thought_records';
    protected $distortionsTable = 'cognitive_distortions';
    
    /**
     * Get thought records for a user
     */
    public function getUserRecords($userId) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE user_id = ? 
                ORDER BY created_at DESC";
        
        return $this->db->fetchAll($sql, [$userId]);
    }
    
    /**
     * Get recent thought records for a user
     */
    public function getRecentRecords($userId, $limit = 5) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE user_id = ? 
                ORDER BY created_at DESC 
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$userId, $limit]);
    }
    
    /**
     * Get cognitive distortions
     */
    public function getCognitiveDistortions() {
        $sql = "SELECT * FROM {$this->distortionsTable} 
                ORDER BY name ASC";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get thought record by ID
     */
    public function getRecord($id) {
        return $this->find($id);
    }
    
    /**
     * Create a new thought record
     */
    public function createRecord($data) {
        return $this->create($data);
    }
    
    /**
     * Update a thought record
     */
    public function updateRecord($id, $data) {
        return $this->update($id, $data);
    }
    
    /**
     * Get thought record stats for a user
     */
    public function getStats($userId) {
        $sql = "SELECT 
                    COUNT(*) as total_records,
                    AVG(emotion_intensity) as avg_initial_intensity,
                    AVG(outcome_intensity) as avg_outcome_intensity,
                    AVG(emotion_intensity - IFNULL(outcome_intensity, emotion_intensity)) as avg_intensity_reduction
                FROM {$this->table} 
                WHERE user_id = ?";
        
        return $this->db->fetchOne($sql, [$userId]);
    }
    
    /**
     * Get most common cognitive distortions for a user
     */
    public function getCommonDistortions($userId) {
        $sql = "SELECT 
                    cd.name,
                    COUNT(*) as count
                FROM {$this->table} tr
                JOIN {$this->distortionsTable} cd ON FIND_IN_SET(cd.id, tr.cognitive_distortions)
                WHERE tr.user_id = ?
                GROUP BY cd.name
                ORDER BY count DESC
                LIMIT 5";
        
        return $this->db->fetchAll($sql, [$userId]);
    }
}
