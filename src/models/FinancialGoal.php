<?php
/**
 * Financial Goal Model
 *
 * Handles financial goals-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class FinancialGoal extends BaseModel {
    protected $table = 'financial_goals';
    protected $contributionsTable = 'goal_contributions';
    protected $milestonesTable = 'goal_milestones';

    /**
     * Get all financial goals for a specific user
     */
    public function getUserGoals($userId, $filters = []) {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ?";
        $params = [$userId];

        // Apply filters
        if (!empty($filters)) {
            // Filter by status
            if (isset($filters['status']) && !empty($filters['status'])) {
                $sql .= " AND status = ?";
                $params[] = $filters['status'];
            }

            // Filter by category
            if (isset($filters['category']) && !empty($filters['category'])) {
                $sql .= " AND category = ?";
                $params[] = $filters['category'];
            }

            // Filter by priority
            if (isset($filters['priority']) && !empty($filters['priority'])) {
                $sql .= " AND priority = ?";
                $params[] = $filters['priority'];
            }
        }

        // Order by priority (high to low) and then by target date (soonest first)
        $sql .= " ORDER BY 
                  CASE 
                    WHEN priority = 'high' THEN 1 
                    WHEN priority = 'medium' THEN 2 
                    WHEN priority = 'low' THEN 3 
                    ELSE 4 
                  END, 
                  target_date ASC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get active financial goals for a specific user
     */
    public function getActiveGoals($userId) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE user_id = ? AND status = 'active'
                ORDER BY 
                CASE 
                  WHEN priority = 'high' THEN 1 
                  WHEN priority = 'medium' THEN 2 
                  WHEN priority = 'low' THEN 3 
                  ELSE 4 
                END, 
                target_date ASC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get a specific financial goal with detailed information
     */
    public function getGoalDetails($goalId) {
        $sql = "SELECT * FROM {$this->table} WHERE id = ?";
        $goal = $this->db->fetchOne($sql, [$goalId]);

        if (!$goal) {
            return null;
        }

        // Get contributions
        $sql = "SELECT * FROM {$this->contributionsTable} 
                WHERE goal_id = ? 
                ORDER BY contribution_date DESC";
        $goal['contributions'] = $this->db->fetchAll($sql, [$goalId]);

        // Get milestones
        $sql = "SELECT * FROM {$this->milestonesTable} 
                WHERE goal_id = ? 
                ORDER BY target_date ASC";
        $goal['milestones'] = $this->db->fetchAll($sql, [$goalId]);

        // Calculate progress percentage
        $goal['progress_percentage'] = $this->calculateProgressPercentage($goal);

        // Calculate time progress
        $goal['time_progress'] = $this->calculateTimeProgress($goal);

        // Calculate if on track
        $goal['on_track'] = $this->isOnTrack($goal);

        return $goal;
    }

    /**
     * Calculate progress percentage for a goal
     */
    public function calculateProgressPercentage($goal) {
        if ($goal['target_amount'] <= 0) {
            return 0;
        }

        return min(100, round(($goal['current_amount'] / $goal['target_amount']) * 100, 1));
    }

    /**
     * Calculate time progress percentage (how much time has elapsed)
     */
    public function calculateTimeProgress($goal) {
        $startDate = strtotime($goal['start_date']);
        $targetDate = strtotime($goal['target_date']);
        $currentDate = time();

        // If target date is in the past, return 100%
        if ($currentDate > $targetDate) {
            return 100;
        }

        // If start date is in the future, return 0%
        if ($currentDate < $startDate) {
            return 0;
        }

        $totalDuration = $targetDate - $startDate;
        if ($totalDuration <= 0) {
            return 100; // Avoid division by zero
        }

        $elapsedDuration = $currentDate - $startDate;
        return min(100, round(($elapsedDuration / $totalDuration) * 100, 1));
    }

    /**
     * Determine if a goal is on track (progress >= time progress)
     */
    public function isOnTrack($goal) {
        $progressPercentage = $this->calculateProgressPercentage($goal);
        $timeProgress = $this->calculateTimeProgress($goal);

        // If time progress is 0, consider it on track
        if ($timeProgress == 0) {
            return true;
        }

        // Goal is on track if progress percentage is at least 90% of time progress
        return ($progressPercentage / $timeProgress) >= 0.9;
    }

    /**
     * Add a contribution to a goal
     */
    public function addContribution($goalId, $contributionData) {
        // Insert contribution
        $contributionId = $this->db->insert($this->contributionsTable, $contributionData);

        if ($contributionId) {
            // Update goal's current amount
            $goal = $this->find($goalId);
            $newAmount = $goal['current_amount'] + $contributionData['amount'];
            
            $updateData = [
                'current_amount' => $newAmount,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // If goal is now complete, update status
            if ($newAmount >= $goal['target_amount']) {
                $updateData['status'] = 'completed';
            }
            
            $this->update($goalId, $updateData);
            
            // Update milestone status if applicable
            $this->updateMilestoneStatus($goalId, $newAmount);
            
            return $contributionId;
        }
        
        return false;
    }

    /**
     * Update milestone status based on current amount
     */
    private function updateMilestoneStatus($goalId, $currentAmount) {
        $sql = "SELECT * FROM {$this->milestonesTable} 
                WHERE goal_id = ? AND is_reached = 0 AND target_amount <= ?";
        $milestones = $this->db->fetchAll($sql, [$goalId, $currentAmount]);
        
        foreach ($milestones as $milestone) {
            $this->db->update(
                $this->milestonesTable,
                [
                    'is_reached' => 1,
                    'reached_date' => date('Y-m-d'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                "id = ?",
                [$milestone['id']]
            );
        }
    }

    /**
     * Add a milestone to a goal
     */
    public function addMilestone($milestoneData) {
        return $this->db->insert($this->milestonesTable, $milestoneData);
    }

    /**
     * Get goal summary statistics for a user
     */
    public function getGoalsSummary($userId) {
        $sql = "SELECT 
                    COUNT(*) as total_goals,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_goals,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_goals,
                    SUM(target_amount) as total_target_amount,
                    SUM(current_amount) as total_saved_amount
                FROM {$this->table}
                WHERE user_id = ?";
        
        return $this->db->fetchOne($sql, [$userId]);
    }

    /**
     * Get unique categories for a user's goals
     */
    public function getUniqueCategories($userId) {
        $sql = "SELECT DISTINCT category FROM {$this->table} 
                WHERE user_id = ? AND category IS NOT NULL AND category != ''
                ORDER BY category";
        
        $results = $this->db->fetchAll($sql, [$userId]);
        return array_column($results, 'category');
    }
}
