<?php
/**
 * AI Agent Category Model
 *
 * Handles AI agent category data and operations
 */

require_once __DIR__ . '/BaseModel.php';

class AIAgentCategory extends BaseModel {
    protected $table = 'ai_agent_categories';
    
    /**
     * Get all categories for a user
     */
    public function getUserCategories($userId) {
        $sql = "SELECT c.*, 
                    (SELECT COUNT(*) FROM ai_agents a WHERE a.category_id = c.id) as agent_count
                FROM {$this->table} c
                WHERE c.user_id = ?
                ORDER BY c.display_order ASC, c.name ASC";
        
        return $this->db->fetchAll($sql, [$userId]);
    }
    
    /**
     * Get a single category by ID
     */
    public function getCategory($id, $userId) {
        $sql = "SELECT c.*, 
                    (SELECT COUNT(*) FROM ai_agents a WHERE a.category_id = c.id) as agent_count
                FROM {$this->table} c
                WHERE c.id = ? AND c.user_id = ?";
        
        return $this->db->fetchOne($sql, [$id, $userId]);
    }
    
    /**
     * Create a new category
     */
    public function createCategory($data) {
        // Ensure created_at and updated_at are set
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->create($data);
    }
    
    /**
     * Update a category
     */
    public function updateCategory($id, $data) {
        // Ensure updated_at is set
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->update($id, $data);
    }
    
    /**
     * Delete a category
     */
    public function deleteCategory($id, $userId) {
        $sql = "DELETE FROM {$this->table} WHERE id = ? AND user_id = ?";
        
        return $this->db->execute($sql, [$id, $userId]);
    }
    
    /**
     * Update category display order
     */
    public function updateDisplayOrder($id, $order) {
        $sql = "UPDATE {$this->table} 
                SET display_order = ?, updated_at = NOW() 
                WHERE id = ?";
        
        return $this->db->execute($sql, [$order, $id]);
    }
    
    /**
     * Get category with agents
     */
    public function getCategoryWithAgents($id, $userId) {
        // Get the category
        $category = $this->getCategory($id, $userId);
        
        if (!$category) {
            return null;
        }
        
        // Get agents in this category
        $sql = "SELECT a.*, 
                    (SELECT COUNT(*) FROM ai_agent_tasks t WHERE t.agent_id = a.id AND t.status = 'pending') as pending_tasks,
                    (SELECT COUNT(*) FROM ai_agent_tasks t WHERE t.agent_id = a.id AND t.status = 'in_progress') as active_tasks
                FROM ai_agents a
                WHERE a.category_id = ? AND a.user_id = ?
                ORDER BY a.name ASC";
        
        $agents = $this->db->fetchAll($sql, [$id, $userId]);
        
        // Add agents to category
        $category['agents'] = $agents;
        
        return $category;
    }
    
    /**
     * Get categories with agents
     */
    public function getCategoriesWithAgents($userId) {
        // Get all categories
        $categories = $this->getUserCategories($userId);
        
        // Get all agents
        $sql = "SELECT a.*, 
                    (SELECT COUNT(*) FROM ai_agent_tasks t WHERE t.agent_id = a.id AND t.status = 'pending') as pending_tasks,
                    (SELECT COUNT(*) FROM ai_agent_tasks t WHERE t.agent_id = a.id AND t.status = 'in_progress') as active_tasks
                FROM ai_agents a
                WHERE a.user_id = ?
                ORDER BY a.name ASC";
        
        $allAgents = $this->db->fetchAll($sql, [$userId]);
        
        // Group agents by category
        $agentsByCategory = [];
        foreach ($allAgents as $agent) {
            $categoryId = $agent['category_id'] ?? 0;
            if (!isset($agentsByCategory[$categoryId])) {
                $agentsByCategory[$categoryId] = [];
            }
            $agentsByCategory[$categoryId][] = $agent;
        }
        
        // Add agents to categories
        foreach ($categories as &$category) {
            $category['agents'] = $agentsByCategory[$category['id']] ?? [];
        }
        
        // Add uncategorized agents
        $uncategorized = [
            'id' => 0,
            'name' => 'Uncategorized',
            'description' => 'Agents without a category',
            'color' => '#9CA3AF',
            'icon' => 'fa-question-circle',
            'display_order' => 999,
            'agents' => $agentsByCategory[0] ?? []
        ];
        
        // Only add uncategorized if it has agents
        if (!empty($uncategorized['agents'])) {
            $categories[] = $uncategorized;
        }
        
        return $categories;
    }
}
