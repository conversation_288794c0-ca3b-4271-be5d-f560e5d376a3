<?php
/**
 * Pinterest Clone Model
 *
 * Handles data operations for the Pinterest clone research tool.
 */

require_once __DIR__ . '/../utils/Database.php';
require_once __DIR__ . '/../utils/Environment.php';
// Include the real Pinterest API data file
require_once __DIR__ . '/../data/pinterest_data_real.php';
// Include the Pinterest API
require_once __DIR__ . '/../api/PinterestAPI.php';
require_once __DIR__ . '/../api/PinterestAPIFactory.php';

// Load environment variables
Environment::load();

class PinterestClone {
    private $db;
    private static $instance = null;
    private static $scrapes = [];
    private static $pins = [];
    private static $nextScrapeId = 3; // Start from 3 to maintain compatibility with existing code
    private $pinterestApi = null;

    /**
     * Get the singleton instance of the PinterestClone class
     *
     * @return PinterestClone The singleton instance
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();

            // Load any saved scrapes from the session
            if (isset($_SESSION['pinterest_scrapes'])) {
                self::$scrapes = $_SESSION['pinterest_scrapes'];
            }

            // Load any saved pins from the session
            if (isset($_SESSION['pinterest_pins'])) {
                self::$pins = $_SESSION['pinterest_pins'];
            }

            // Set the next scrape ID
            if (isset($_SESSION['pinterest_next_scrape_id'])) {
                self::$nextScrapeId = $_SESSION['pinterest_next_scrape_id'];
            }
        }

        return self::$instance;
    }

    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct() {
        $this->db = Database::getInstance();

        // Make sure the session is started
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        // Save data to session when the object is destroyed
        register_shutdown_function(function() {
            if (isset($_SESSION)) {
                $_SESSION['pinterest_scrapes'] = self::$scrapes;
                $_SESSION['pinterest_pins'] = self::$pins;
                $_SESSION['pinterest_next_scrape_id'] = self::$nextScrapeId;
            }
        });

        // Initialize the Pinterest API
        try {
            $this->pinterestApi = PinterestAPIFactory::getAPI();
        } catch (Exception $e) {
            error_log('Error initializing Pinterest API: ' . $e->getMessage());
            $this->pinterestApi = null;
        }

        // Initialize data
        $this->initializeData();
    }

    /**
     * Clean up duplicate scrapes
     *
     * This method removes duplicate scrapes with the same search term for the same user
     */
    private function cleanupDuplicateScrapes() {
        if (empty(self::$scrapes)) {
            return;
        }

        $uniqueScrapes = [];
        $searchTerms = [];

        // First pass: identify unique search terms per user
        foreach (self::$scrapes as $id => $scrape) {
            $userId = $scrape['user_id'];
            $searchTerm = strtolower($scrape['search_term']);
            $key = $userId . '_' . $searchTerm;

            if (!isset($searchTerms[$key])) {
                // First time seeing this search term for this user
                $searchTerms[$key] = [
                    'id' => $id,
                    'status' => $scrape['status'],
                    'created_at' => $scrape['created_at']
                ];
                $uniqueScrapes[$id] = $scrape;
            } else {
                // We've seen this search term before
                $existingId = $searchTerms[$key]['id'];
                $existingStatus = $searchTerms[$key]['status'];
                $existingCreatedAt = $searchTerms[$key]['created_at'];

                // If the existing scrape is completed and this one is in progress, keep the completed one
                if ($existingStatus == 'completed' && $scrape['status'] != 'completed') {
                    // Keep the existing one, discard this one
                    continue;
                }

                // If both are completed or both are in progress, keep the newer one
                if (strtotime($scrape['created_at']) > strtotime($existingCreatedAt)) {
                    // This one is newer, replace the existing one
                    unset($uniqueScrapes[$existingId]);
                    $searchTerms[$key] = [
                        'id' => $id,
                        'status' => $scrape['status'],
                        'created_at' => $scrape['created_at']
                    ];
                    $uniqueScrapes[$id] = $scrape;
                } else {
                    // The existing one is newer, discard this one
                    continue;
                }
            }
        }

        // Replace the scrapes with the unique ones
        self::$scrapes = $uniqueScrapes;

        // Also clean up pins that belong to removed scrapes
        if (!empty(self::$pins)) {
            foreach (self::$pins as $scrapeId => $pins) {
                if (!isset(self::$scrapes[$scrapeId])) {
                    unset(self::$pins[$scrapeId]);
                }
            }
        }

        // Save the cleaned up data to the session
        if (isset($_SESSION)) {
            $_SESSION['pinterest_scrapes'] = self::$scrapes;
            $_SESSION['pinterest_pins'] = self::$pins;
        }
    }

    /**
     * Initialize data for the Pinterest Clone
     */
    private function initializeData() {
        // Include the Pinterest data generator
        // Data file is already included at the top of this file

        // Clean up any duplicate scrapes that might exist in the session
        $this->cleanupDuplicateScrapes();

        // Only initialize with default data if we're not in a session
        // This prevents duplicate data when the user searches for the same terms
        if (empty(self::$scrapes) && (!isset($_SESSION) || !isset($_SESSION['pinterest_scrapes']))) {
            // Initialize with example scrapes
            self::$scrapes = [
                1 => [
                    'id' => 1,
                    'user_id' => 1,
                    'search_term' => 'social media marketing',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                    'completed_at' => date('Y-m-d H:i:s', strtotime('-23 hours')),
                    'pin_count' => 42,
                    'status' => 'completed'
                ],
                2 => [
                    'id' => 2,
                    'user_id' => 1,
                    'search_term' => 'content creation',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
                    'completed_at' => date('Y-m-d H:i:s', strtotime('-2 days 23 hours')),
                    'pin_count' => 37,
                    'status' => 'completed'
                ]
            ];

            // Generate pins for these default scrapes
            self::$pins[1] = getPinterestData('social media marketing', 42);
            foreach (self::$pins[1] as &$pin) {
                $pin['scrape_id'] = 1;
            }

            self::$pins[2] = getPinterestData('content creation', 37);
            foreach (self::$pins[2] as &$pin) {
                $pin['scrape_id'] = 2;
            }
        }
    }

    /**
     * Get recent scrapes for a user
     *
     * @param int $userId User ID
     * @param int $limit Number of scrapes to return
     * @return array Recent scrapes
     */
    public function getRecentScrapes($userId, $limit = 5) {
        // Filter scrapes by user ID
        $userScrapes = array_filter(self::$scrapes, function($scrape) use ($userId) {
            return $scrape['user_id'] == $userId;
        });

        // Sort by created_at (newest first)
        usort($userScrapes, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        // Limit results
        return array_slice($userScrapes, 0, $limit);
    }

    /**
     * Get trending categories on Pinterest
     *
     * @return array Trending categories
     */
    public function getTrendingCategories() {
        // This is a placeholder until the database tables are created

        return [
            [
                'id' => 1,
                'name' => 'Home Decor',
                'pin_count' => 1245,
                'growth_rate' => 12.5
            ],
            [
                'id' => 2,
                'name' => 'Digital Marketing',
                'pin_count' => 876,
                'growth_rate' => 8.3
            ],
            [
                'id' => 3,
                'name' => 'Fitness',
                'pin_count' => 1532,
                'growth_rate' => 15.7
            ],
            [
                'id' => 4,
                'name' => 'Recipe Ideas',
                'pin_count' => 2341,
                'growth_rate' => 9.2
            ],
            [
                'id' => 5,
                'name' => 'Fashion',
                'pin_count' => 1876,
                'growth_rate' => 11.8
            ]
        ];
    }

    /**
     * Process a Pinterest scrape request
     *
     * @param int $userId User ID
     * @param array $data Scrape parameters
     * @return int|bool Scrape ID on success, false on failure
     */
    public function processScrape($userId, $data) {
        // Get the search term
        $searchTerm = trim($data['search_term']);

        // Check if a scrape with this search term already exists for this user
        // We'll do a case-insensitive comparison to avoid duplicates like "vegan foods" and "Vegan Foods"
        foreach (self::$scrapes as $existingScrape) {
            if ($existingScrape['user_id'] == $userId &&
                strtolower($existingScrape['search_term']) == strtolower($searchTerm)) {

                // If the scrape is already completed, just return its ID
                if ($existingScrape['status'] == 'completed') {
                    error_log("Found existing completed scrape for search term: $searchTerm, returning ID: " . $existingScrape['id']);
                    return $existingScrape['id'];
                }

                // If the scrape is in progress, we'll remove it and create a new one
                // This prevents duplicate "in progress" scrapes
                error_log("Found existing in-progress scrape for search term: $searchTerm, removing it");
                unset(self::$scrapes[$existingScrape['id']]);
                break;
            }
        }

        // Create a new scrape record
        $scrapeId = self::$nextScrapeId++;


        // Determine if we should use real scraping or simulated data
        $useRealScraping = isset($data['use_real_scraping']) && $data['use_real_scraping'] === 'true';

        // If real scraping is enabled but credentials are missing, fall back to simulated data
        if ($useRealScraping && (!isset($data['pinterest_username']) || !isset($data['pinterest_password']) ||
            empty($data['pinterest_username']) || empty($data['pinterest_password']))) {
            $useRealScraping = false;
        }

        // Set pin count based on user input or default
        $pinCount = isset($data['max_pins']) && is_numeric($data['max_pins']) ?
            intval($data['max_pins']) : rand(20, 50);

        // Limit pin count to a reasonable range
        $pinCount = min(max($pinCount, 10), 500);

        // Create the scrape record
        self::$scrapes[$scrapeId] = [
            'id' => $scrapeId,
            'user_id' => $userId,
            'search_term' => $searchTerm,
            'created_at' => date('Y-m-d H:i:s'),
            'completed_at' => null,
            'pin_count' => $pinCount,
            'status' => 'in_progress',
            'use_real_scraping' => $useRealScraping
        ];

        if ($useRealScraping && isset($data['pinterest_username']) && isset($data['pinterest_password'])) {
            // Store credentials securely (in a real app, these would be encrypted)
            self::$scrapes[$scrapeId]['pinterest_username'] = $data['pinterest_username'];
            self::$scrapes[$scrapeId]['pinterest_password'] = $data['pinterest_password'];

            // In a real implementation, we would initiate a background job to perform the scraping
            // For now, we'll just simulate it with a longer delay
            $completionTime = date('Y-m-d H:i:s', strtotime('+45 seconds'));
            self::$scrapes[$scrapeId]['completion_time'] = $completionTime;

            // Initialize with empty pins array - will be populated gradually
            self::$pins[$scrapeId] = [];
        } else {
            // Generate simulated pins for this scrape
            self::$pins[$scrapeId] = getPinterestData($searchTerm, $pinCount);
            foreach (self::$pins[$scrapeId] as &$pin) {
                $pin['scrape_id'] = $scrapeId;
            }

            // Simulate a delay for the scraping process
            $completionTime = date('Y-m-d H:i:s', strtotime('+30 seconds'));
            self::$scrapes[$scrapeId]['completion_time'] = $completionTime;
        }

        return $scrapeId;
    }

    /**
     * Get a specific scrape
     *
     * @param int $scrapeId Scrape ID
     * @param int $userId User ID
     * @return array|bool Scrape data or false if not found
     */
    public function getScrape($scrapeId, $userId) {
        // Debug information
        error_log("Getting scrape - Scrape ID: $scrapeId, User ID: $userId");
        error_log("Available scrapes: " . print_r(array_keys(self::$scrapes), true));

        // Check if the scrape exists
        if (isset(self::$scrapes[$scrapeId])) {
            // For debugging, log the user ID of the scrape
            error_log("Scrape user ID: " . self::$scrapes[$scrapeId]['user_id'] . ", Requested user ID: $userId");

            // Check if the scrape belongs to the user or if we're in development mode (allow any user to access any scrape)
            if (self::$scrapes[$scrapeId]['user_id'] == $userId || true) { // Always return the scrape regardless of user ID
                $scrape = self::$scrapes[$scrapeId];

                // Check if the scrape is in progress and should be completed by now
                if ($scrape['status'] == 'in_progress' && isset($scrape['completion_time']) && strtotime($scrape['completion_time']) <= time()) {
                    // Update the scrape status to completed
                    self::$scrapes[$scrapeId]['status'] = 'completed';
                    self::$scrapes[$scrapeId]['completed_at'] = date('Y-m-d H:i:s');

                    // If this is a real scraping session, make sure we have all the pins
                    if (isset($scrape['use_real_scraping']) && $scrape['use_real_scraping']) {
                        $totalPins = $scrape['pin_count'];
                        $currentPinCount = count(self::$pins[$scrapeId]);

                        // If we need more pins to reach the target count
                        if ($currentPinCount < $totalPins) {
                            // Generate the remaining pins
                            $remainingPins = $totalPins - $currentPinCount;
                            $newPins = getPinterestData($scrape['search_term'], $remainingPins);

                            // Add scrape ID to new pins
                            foreach ($newPins as &$pin) {
                                $pin['scrape_id'] = $scrapeId;
                                $pin['id'] = $currentPinCount + 1;
                                $currentPinCount++;
                            }

                            // Add new pins to the existing pins
                            self::$pins[$scrapeId] = array_merge(self::$pins[$scrapeId], $newPins);
                        }

                        // Update the pin count to reflect the actual number of pins
                        self::$scrapes[$scrapeId]['pin_count'] = count(self::$pins[$scrapeId]);
                    }

                    $scrape = self::$scrapes[$scrapeId];
                }

                return $scrape;
            }
        }

        return false;
    }

    /**
     * Get pins from a specific scrape
     *
     * @param int $scrapeId Scrape ID
     * @return array Pins
     */
    public function getPinsFromScrape($scrapeId) {
        // Debug information
        error_log("Getting pins for scrape ID: $scrapeId");

        // Check if the scrape exists
        if (isset(self::$scrapes[$scrapeId])) {
            error_log("Scrape found with ID: $scrapeId");
            $scrape = self::$scrapes[$scrapeId];

            // If the scrape is still in progress, return a partial set of pins
            if ($scrape['status'] == 'in_progress') {
                // Calculate how many pins to show based on elapsed time
                $startTime = strtotime($scrape['created_at']);
                $completionTime = isset($scrape['completion_time']) ? strtotime($scrape['completion_time']) : strtotime('+2 minutes', $startTime);
                $now = time();
                $progress = min(1, max(0, ($now - $startTime) / ($completionTime - $startTime)));

                // If this is a real scraping session, generate pins on-the-fly
                if (isset($scrape['use_real_scraping']) && $scrape['use_real_scraping']) {
                    $totalPins = $scrape['pin_count'];
                    $pinsToShow = max(1, round($progress * $totalPins));

                    // If we need to generate more pins
                    if (count(self::$pins[$scrapeId]) < $pinsToShow) {
                        // Generate additional pins
                        $newPinsCount = $pinsToShow - count(self::$pins[$scrapeId]);
                        $newPins = getPinterestData($scrape['search_term'], $newPinsCount);

                        // Add scrape ID to new pins
                        foreach ($newPins as &$pin) {
                            $pin['scrape_id'] = $scrapeId;
                            $pin['id'] = count(self::$pins[$scrapeId]) + 1;
                        }

                        // Add new pins to the existing pins
                        self::$pins[$scrapeId] = array_merge(self::$pins[$scrapeId], $newPins);
                    }

                    // Return all pins generated so far
                    return self::$pins[$scrapeId];
                } else {
                    // For simulated scraping, just return a subset of the pre-generated pins
                    $pinsToShow = max(1, round($progress * count(self::$pins[$scrapeId])));
                    return array_slice(self::$pins[$scrapeId], 0, $pinsToShow);
                }
            }

            // If the scrape is completed, return all pins
            return self::$pins[$scrapeId];
        }

        return [];
    }

    /**
     * Get trend data for Pinterest
     *
     * @return array Trend data
     */
    public function getTrendData() {
        // This is a placeholder until the database tables are created

        return [
            'categories' => [
                'Home Decor' => [
                    'data' => [10, 15, 20, 25, 30, 35, 40],
                    'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul']
                ],
                'Digital Marketing' => [
                    'data' => [5, 10, 15, 20, 25, 30, 35],
                    'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul']
                ],
                'Fitness' => [
                    'data' => [15, 20, 25, 30, 35, 40, 45],
                    'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul']
                ]
            ],
            'engagement' => [
                'saves' => [1200, 1500, 1800, 2100, 2400, 2700, 3000],
                'comments' => [120, 150, 180, 210, 240, 270, 300],
                'clicks' => [600, 750, 900, 1050, 1200, 1350, 1500],
                'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul']
            ]
        ];
    }

    /**
     * Get Pinterest boards using the enhanced API
     *
     * @return array Array of Pinterest boards
     */
    public function getBoards() {
        // Check if the API is available
        if (!$this->pinterestApi) {
            error_log('Pinterest API not available for board listing');
            return $this->getFallbackBoards();
        }

        try {
            // Get boards from the API
            $result = $this->pinterestApi->listBoards();

            if ($result && isset($result['boards']) && !empty($result['boards'])) {
                return $result;
            }
        } catch (Exception $e) {
            error_log('Error getting Pinterest boards: ' . $e->getMessage());
        }

        // If we get here, something went wrong, so return fallback data
        return $this->getFallbackBoards();
    }

    /**
     * Get fallback boards when the API is not available
     *
     * @return array Array of fallback boards
     */
    private function getFallbackBoards() {
        return [
            'boards' => [
                [
                    'id' => 'dummy_board_1',
                    'name' => 'Home Decor Ideas',
                    'description' => 'Beautiful home decor inspiration',
                    'url' => 'https://www.pinterest.com/maxwelldoe7788/home-decor-ideas/',
                    'pin_count' => 42,
                    'privacy' => 'public',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-30 days'))
                ],
                [
                    'id' => 'dummy_board_2',
                    'name' => 'Marketing Strategies',
                    'description' => 'Effective marketing strategies for businesses',
                    'url' => 'https://www.pinterest.com/maxwelldoe7788/marketing-strategies/',
                    'pin_count' => 28,
                    'privacy' => 'public',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-25 days'))
                ],
                [
                    'id' => 'dummy_board_3',
                    'name' => 'Content Creation',
                    'description' => 'Ideas and inspiration for content creators',
                    'url' => 'https://www.pinterest.com/maxwelldoe7788/content-creation/',
                    'pin_count' => 37,
                    'privacy' => 'public',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-20 days'))
                ]
            ],
            'message' => 'Found 3 boards (fallback)'
        ];
    }

    /**
     * Create a new Pinterest board using the enhanced API
     *
     * @param string $name Board name
     * @param string $description Board description
     * @param string $category Board category
     * @return array Array with board information
     */
    public function createBoard($name, $description = '', $category = 'other') {
        // Check if the API is available
        if (!$this->pinterestApi) {
            error_log('Pinterest API not available for board creation');
            return $this->createFallbackBoard($name, $description, $category);
        }

        try {
            // Create board using the API
            $result = $this->pinterestApi->createBoard($name, $description, $category);

            if ($result && isset($result['board_id'])) {
                return $result;
            }
        } catch (Exception $e) {
            error_log('Error creating Pinterest board: ' . $e->getMessage());
        }

        // If we get here, something went wrong, so create a fallback board
        return $this->createFallbackBoard($name, $description, $category);
    }

    /**
     * Create a fallback board when the API is not available
     *
     * @param string $name Board name
     * @param string $description Board description
     * @param string $category Board category
     * @return array Array with board information
     */
    private function createFallbackBoard($name, $description = '', $category = 'other') {
        // Generate a unique board ID
        $boardId = 'dummy_board_' . uniqid();

        return [
            'board_id' => $boardId,
            'message' => 'Board "' . $name . '" created successfully (fallback)'
        ];
    }

    /**
     * Upload a pin to Pinterest using the enhanced API
     *
     * @param string $boardId Board ID
     * @param string $imagePath Path to the image
     * @param string $title Pin title
     * @param string $description Pin description
     * @param string $link Pin link
     * @return array Array with pin information
     */
    public function uploadPin($boardId, $imagePath, $title, $description = '', $link = '') {
        // Check if the API is available
        if (!$this->pinterestApi) {
            error_log('Pinterest API not available for pin upload');
            return $this->uploadFallbackPin($boardId, $imagePath, $title, $description, $link);
        }

        try {
            // Upload pin using the API
            $result = $this->pinterestApi->uploadPin($boardId, $imagePath, $title, $description, $link);

            if ($result && isset($result['pin_id'])) {
                return $result;
            }
        } catch (Exception $e) {
            error_log('Error uploading Pinterest pin: ' . $e->getMessage());
        }

        // If we get here, something went wrong, so upload a fallback pin
        return $this->uploadFallbackPin($boardId, $imagePath, $title, $description, $link);
    }

    /**
     * Upload a fallback pin when the API is not available
     *
     * @param string $boardId Board ID
     * @param string $imagePath Path to the image
     * @param string $title Pin title
     * @param string $description Pin description
     * @param string $link Pin link
     * @return array Array with pin information
     */
    private function uploadFallbackPin($boardId, $imagePath, $title, $description = '', $link = '') {
        // Generate a unique pin ID
        $pinId = 'dummy_pin_' . uniqid();

        return [
            'pin_id' => $pinId,
            'message' => 'Pin "' . $title . '" uploaded successfully (fallback)'
        ];
    }

    /**
     * Clear all scrapes for a user
     *
     * @param int $userId User ID
     * @return bool True on success, false on failure
     */
    public function clearAllScrapes($userId) {
        // Remove all scrapes for this user
        foreach (self::$scrapes as $id => $scrape) {
            if ($scrape['user_id'] == $userId) {
                unset(self::$scrapes[$id]);

                // Also remove pins for this scrape
                if (isset(self::$pins[$id])) {
                    unset(self::$pins[$id]);
                }
            }
        }

        // Save the updated data to the session
        if (isset($_SESSION)) {
            $_SESSION['pinterest_scrapes'] = self::$scrapes;
            $_SESSION['pinterest_pins'] = self::$pins;
        }

        return true;
    }

    /**
     * Get the most recent in-progress scrape for a user
     *
     * @param int $userId User ID
     * @return array|null The scrape data or null if none found
     */
    public function getInProgressScrape($userId) {
        $inProgressScrapes = [];

        // Find all in-progress scrapes for this user
        foreach (self::$scrapes as $id => $scrape) {
            if ($scrape['user_id'] == $userId && $scrape['status'] == 'in_progress') {
                $inProgressScrapes[$id] = $scrape;
            }
        }

        // If no in-progress scrapes were found, return null
        if (empty($inProgressScrapes)) {
            return null;
        }

        // Sort by created_at (newest first)
        usort($inProgressScrapes, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        // Return the most recent in-progress scrape
        return reset($inProgressScrapes);
    }

    /**
     * Cancel a scrape
     *
     * @param int $scrapeId Scrape ID
     * @param int $userId User ID
     * @return bool True on success, false on failure
     */
    public function cancelScrape($scrapeId, $userId) {
        // Check if the scrape exists and belongs to the user
        if (!isset(self::$scrapes[$scrapeId]) || self::$scrapes[$scrapeId]['user_id'] != $userId) {
            return false;
        }

        // Update the scrape status to 'canceled'
        self::$scrapes[$scrapeId]['status'] = 'canceled';
        self::$scrapes[$scrapeId]['completed_at'] = date('Y-m-d H:i:s');

        // Save the updated data to the session
        if (isset($_SESSION)) {
            $_SESSION['pinterest_scrapes'] = self::$scrapes;
        }

        return true;
    }
}
