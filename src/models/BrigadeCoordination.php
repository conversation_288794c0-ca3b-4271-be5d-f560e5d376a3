<?php
/**
 * Brigade Coordination Model
 *
 * Handles coordination between different brigade projects
 */

require_once __DIR__ . '/BaseModel.php';

class BrigadeCoordination extends BaseModel {
    protected $table = 'brigade_coordination';

    /**
     * Get all coordination relationships where a project is the source
     *
     * @param int $projectId Project ID
     * @return array Array of coordination relationships
     */
    public function getOutgoingCoordination($projectId) {
        $sql = "SELECT bc.*, p.name as target_project_name, p.brigade_type as target_brigade_type
                FROM {$this->table} bc
                JOIN projects p ON bc.target_project_id = p.id
                WHERE bc.source_project_id = ?
                ORDER BY p.name ASC";

        return $this->db->fetchAll($sql, [$projectId]);
    }

    /**
     * Get all coordination relationships where a project is the target
     *
     * @param int $projectId Project ID
     * @return array Array of coordination relationships
     */
    public function getIncomingCoordination($projectId) {
        $sql = "SELECT bc.*, p.name as source_project_name, p.brigade_type as source_brigade_type
                FROM {$this->table} bc
                JOIN projects p ON bc.source_project_id = p.id
                WHERE bc.target_project_id = ?
                ORDER BY p.name ASC";

        return $this->db->fetchAll($sql, [$projectId]);
    }

    /**
     * Create a new coordination relationship
     *
     * @param int $sourceProjectId Source project ID
     * @param int $targetProjectId Target project ID
     * @param string $coordinationType Type of coordination
     * @param string $description Optional description
     * @return int|false New coordination ID or false on failure
     */
    public function createCoordination($sourceProjectId, $targetProjectId, $coordinationType, $description = null) {
        // Check if coordination already exists
        $sql = "SELECT id FROM {$this->table} 
                WHERE source_project_id = ? AND target_project_id = ? AND coordination_type = ?";
        
        $existing = $this->db->fetchOne($sql, [$sourceProjectId, $targetProjectId, $coordinationType]);
        
        if ($existing) {
            return $existing['id']; // Coordination already exists
        }
        
        // Create new coordination
        $data = [
            'source_project_id' => $sourceProjectId,
            'target_project_id' => $targetProjectId,
            'coordination_type' => $coordinationType,
            'description' => $description,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->create($data);
    }

    /**
     * Remove a coordination relationship
     *
     * @param int $sourceProjectId Source project ID
     * @param int $targetProjectId Target project ID
     * @param string $coordinationType Optional coordination type
     * @return bool Success or failure
     */
    public function removeCoordination($sourceProjectId, $targetProjectId, $coordinationType = null) {
        $sql = "DELETE FROM {$this->table} WHERE source_project_id = ? AND target_project_id = ?";
        $params = [$sourceProjectId, $targetProjectId];
        
        if ($coordinationType !== null) {
            $sql .= " AND coordination_type = ?";
            $params[] = $coordinationType;
        }
        
        return $this->db->query($sql, $params);
    }

    /**
     * Get all coordination types
     *
     * @return array Array of coordination types
     */
    public function getCoordinationTypes() {
        return [
            'data_sharing' => 'Data Sharing',
            'workflow_dependency' => 'Workflow Dependency',
            'resource_sharing' => 'Resource Sharing',
            'output_input' => 'Output to Input',
            'collaborative' => 'Collaborative Effort'
        ];
    }

    /**
     * Get formatted coordination type name
     *
     * @param string $coordinationType Coordination type identifier
     * @return string Formatted coordination type name
     */
    public function formatCoordinationType($coordinationType) {
        $types = $this->getCoordinationTypes();
        return $types[$coordinationType] ?? ucwords(str_replace('_', ' ', $coordinationType));
    }
}
