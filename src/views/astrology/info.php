<?php
/**
 * Astrology Information & Education Page
 */
?>

<link rel="stylesheet" href="/momentum/public/css/astrology.css">

<div class="astrology-container">
    <!-- Header -->
    <div class="astrology-header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-book text-purple-600"></i>
                <h1 class="title-gradient">Understanding Rahu Kalaya</h1>
                <i class="fas fa-graduation-cap text-purple-600"></i>
            </div>
            <p class="header-subtitle">Learn about Vedic Astrology and Rahu Kalaya Significance</p>
        </div>
    </div>

    <!-- Navigation Breadcrumb -->
    <div class="breadcrumb-nav">
        <div class="breadcrumb-content">
            <a href="/momentum/astrology" class="breadcrumb-link">
                <i class="fas fa-star"></i> Astrology
            </a>
            <i class="fas fa-chevron-right"></i>
            <span class="breadcrumb-current">Understanding Rahu Kalaya</span>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Educational Content -->
        <div class="education-grid">
            <!-- Scientific/Astronomical Background -->
            <div class="education-card blue-gradient">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-atom"></i>
                    </div>
                    <h2 class="card-title">Scientific/Astronomical Background</h2>
                </div>
                <div class="card-content">
                    <p><strong>Rahu & Ketu are not physical planets</strong> but lunar nodes, where the Moon's orbit crosses the ecliptic plane. These mathematical points are responsible for solar and lunar eclipses.</p>

                    <p>The Rahu Kalaya is not based on astronomy directly, but on astrological tradition, where it's believed that Rahu's influence during these times can disturb concentration, luck, or clarity.</p>

                    <div class="highlight-box">
                        <i class="fas fa-clock"></i>
                        <p><strong>Fixed Pattern:</strong> It follows a fixed pattern for simplicity, dividing the 12-hour span (day or night) into 8 equal parts and assigning Rahu to one part each day.</p>
                    </div>

                    <div class="scientific-facts">
                        <h4><i class="fas fa-telescope"></i> Key Facts:</h4>
                        <ul>
                            <li>Lunar nodes complete a cycle in approximately 18.6 years (Saros cycle)</li>
                            <li>Eclipses occur when Sun and Moon align near these nodes</li>
                            <li>Rahu and Ketu are called "eclipse points" in Vedic astronomy</li>
                            <li>The timing system is traditional, not astronomically calculated</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Vedic Significance -->
            <div class="education-card orange-gradient">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h2 class="card-title">Vedic Significance</h2>
                </div>
                <div class="card-content">
                    <p>In Vedic astrology, Rahu is considered a shadow planet with malefic influences, associated with confusion, illusion, and unexpected events. Despite being a mathematical point rather than a physical celestial body, Rahu holds immense significance in Hindu astrology.</p>

                    <p>The daily Rahu Kalaya represents a time when Rahu's influence is considered strongest, making it inauspicious for beginning new ventures or important activities.</p>

                    <div class="mythology-box">
                        <h4><i class="fas fa-scroll"></i> Mythological Origin</h4>
                        <p>According to Hindu mythology, Rahu was a demon who disguised himself to drink the nectar of immortality. When discovered, Vishnu beheaded him, but since he had already consumed the nectar, his head became immortal as Rahu.</p>
                    </div>
                </div>
            </div>

            <!-- Calculation Method -->
            <div class="education-card green-gradient">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <h2 class="card-title">Calculation Method</h2>
                </div>
                <div class="card-content">
                    <p>Rahu Kalaya timings are calculated by dividing the time between sunrise and sunset (or night hours) into eight equal segments. Each day of the week is assigned a specific segment:</p>

                    <div class="calculation-steps">
                        <div class="step">
                            <span class="step-number">1</span>
                            <p>Calculate total daylight hours (sunset - sunrise) or night hours</p>
                        </div>
                        <div class="step">
                            <span class="step-number">2</span>
                            <p>Divide the 12-hour span into 8 equal segments (1.5 hours each)</p>
                        </div>
                        <div class="step">
                            <span class="step-number">3</span>
                            <p>Assign specific segment to each day based on traditional rules</p>
                        </div>
                    </div>

                    <div class="day-assignments">
                        <h4>Daytime Assignments:</h4>
                        <ul>
                            <li><strong>Monday:</strong> 2nd segment (7:30 AM - 9:00 AM)</li>
                            <li><strong>Tuesday:</strong> 7th segment (3:00 PM - 4:30 PM)</li>
                            <li><strong>Wednesday:</strong> 5th segment (12:00 PM - 1:30 PM)</li>
                            <li><strong>Thursday:</strong> 6th segment (1:30 PM - 3:00 PM)</li>
                            <li><strong>Friday:</strong> 4th segment (10:30 AM - 12:00 PM)</li>
                            <li><strong>Saturday:</strong> 3rd segment (9:00 AM - 10:30 AM)</li>
                            <li><strong>Sunday:</strong> 8th segment (4:30 PM - 6:00 PM)</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Nighttime Rahu Kalaya -->
            <div class="education-card purple-gradient night-info-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-moon"></i>
                    </div>
                    <h2 class="card-title">Nighttime Rahu Kalaya (Ratri Rahu Kaalaya)</h2>
                </div>
                <div class="card-content">
                    <p>In addition to daytime periods, Vedic astrology also recognizes nighttime Rahu Kalaya periods. These follow the same principle but apply to night hours.</p>

                    <div class="night-assignments">
                        <h4><i class="fas fa-moon"></i> Night Assignments:</h4>
                        <ul>
                            <li><strong>Monday Night:</strong> 10:30 PM – 12:00 AM</li>
                            <li><strong>Tuesday Night:</strong> 9:00 PM – 10:30 PM</li>
                            <li><strong>Wednesday Night:</strong> 7:30 PM – 9:00 PM</li>
                            <li><strong>Thursday Night:</strong> 6:00 PM – 7:30 PM</li>
                            <li><strong>Friday Night:</strong> 1:30 AM – 3:00 AM</li>
                            <li><strong>Saturday Night:</strong> 12:00 AM – 1:30 AM</li>
                            <li><strong>Sunday Night:</strong> 3:00 AM – 4:30 AM</li>
                        </ul>
                    </div>

                    <div class="night-note">
                        <i class="fas fa-info-circle"></i>
                        <p><strong>Note:</strong> Nighttime Rahu Kalaya is particularly significant for activities like meditation, spiritual practices, and important decisions made during evening hours.</p>
                    </div>
                </div>
            </div>

            <!-- Practical Applications -->
            <div class="education-card purple-gradient">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h2 class="card-title">Practical Applications</h2>
                </div>
                <div class="card-content">
                    <h4>Activities to Avoid During Rahu Kalaya:</h4>
                    <div class="avoid-grid">
                        <div class="avoid-item">
                            <i class="fas fa-briefcase"></i>
                            <span>Starting new business ventures</span>
                        </div>
                        <div class="avoid-item">
                            <i class="fas fa-file-signature"></i>
                            <span>Signing important contracts</span>
                        </div>
                        <div class="avoid-item">
                            <i class="fas fa-plane"></i>
                            <span>Beginning long journeys</span>
                        </div>
                        <div class="avoid-item">
                            <i class="fas fa-home"></i>
                            <span>House warming ceremonies</span>
                        </div>
                        <div class="avoid-item">
                            <i class="fas fa-ring"></i>
                            <span>Wedding ceremonies</span>
                        </div>
                        <div class="avoid-item">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Major purchases</span>
                        </div>
                    </div>

                    <h4 class="mt-4">Acceptable Activities:</h4>
                    <div class="acceptable-list">
                        <div class="acceptable-item">
                            <i class="fas fa-check-circle"></i>
                            <span>Emergency medical procedures</span>
                        </div>
                        <div class="acceptable-item">
                            <i class="fas fa-check-circle"></i>
                            <span>Continuing ongoing work</span>
                        </div>
                        <div class="acceptable-item">
                            <i class="fas fa-check-circle"></i>
                            <span>Routine daily activities</span>
                        </div>
                        <div class="acceptable-item">
                            <i class="fas fa-check-circle"></i>
                            <span>Spiritual practices and meditation</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Perspective -->
            <div class="education-card teal-gradient">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h2 class="card-title">Modern Perspective</h2>
                </div>
                <div class="card-content">
                    <p>While Rahu Kalaya is rooted in ancient Vedic traditions, many modern practitioners view it as a psychological and cultural framework for mindful timing rather than a strict astronomical influence.</p>

                    <div class="perspective-points">
                        <div class="point">
                            <h5><i class="fas fa-brain"></i> Psychological Benefits</h5>
                            <p>Taking pause before important decisions can lead to better outcomes regardless of astrological beliefs.</p>
                        </div>
                        <div class="point">
                            <h5><i class="fas fa-users"></i> Cultural Significance</h5>
                            <p>Observing Rahu Kalaya maintains connection with traditional wisdom and cultural heritage.</p>
                        </div>
                        <div class="point">
                            <h5><i class="fas fa-clock"></i> Time Management</h5>
                            <p>Structured timing systems can improve planning and decision-making processes.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Regional Variations -->
            <div class="education-card red-gradient">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h2 class="card-title">Regional Variations</h2>
                </div>
                <div class="card-content">
                    <p>Rahu Kalaya timings can vary based on geographical location and local sunrise/sunset times. Our calculations are specifically calibrated for Sri Lanka (UTC+5:30).</p>

                    <div class="location-info">
                        <h5>Factors Affecting Timing:</h5>
                        <ul>
                            <li>Latitude and longitude of location</li>
                            <li>Seasonal variations in daylight hours</li>
                            <li>Local time zone differences</li>
                            <li>Regional calculation methods</li>
                        </ul>
                    </div>

                    <div class="note-box">
                        <i class="fas fa-info-circle"></i>
                        <p><strong>Note:</strong> For locations outside Sri Lanka, consult local astronomical calculations or traditional sources for accurate Rahu Kalaya timings.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h3 class="actions-title">Quick Actions</h3>
            <div class="actions-grid">
                <a href="/momentum/astrology" class="action-card">
                    <i class="fas fa-arrow-left"></i>
                    <span>Back to Dashboard</span>
                </a>
                <a href="/momentum/astrology/rahu-kalaya" class="action-card">
                    <i class="fas fa-table"></i>
                    <span>View Schedule</span>
                </a>
                <button onclick="window.print()" class="action-card">
                    <i class="fas fa-print"></i>
                    <span>Print Guide</span>
                </button>
                <button onclick="shareContent()" class="action-card">
                    <i class="fas fa-share"></i>
                    <span>Share</span>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Additional styles for the info page */
.education-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.education-card {
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.98) !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .education-card {
        background: rgba(17, 24, 39, 0.98) !important;
        border: 1px solid rgba(75, 85, 99, 0.5);
    }
}

/* Override all gradient backgrounds for better readability */
.blue-gradient, .orange-gradient, .green-gradient,
.purple-gradient, .teal-gradient, .red-gradient {
    background: rgba(255, 255, 255, 0.98) !important;
    border: 1px solid rgba(156, 163, 175, 0.3) !important;
}

/* Dark mode overrides */
@media (prefers-color-scheme: dark) {
    .blue-gradient, .orange-gradient, .green-gradient,
    .purple-gradient, .teal-gradient, .red-gradient {
        background: rgba(17, 24, 39, 0.98) !important;
        border: 1px solid rgba(75, 85, 99, 0.5) !important;
    }
}

.card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.card-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #4c1d95;
}

/* Dark mode card icons */
@media (prefers-color-scheme: dark) {
    .card-icon {
        background: rgba(75, 85, 99, 0.5);
        color: #c7d2fe;
    }
}

.card-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #1f2937 !important;
    margin: 0;
}

/* Dark mode card titles */
@media (prefers-color-scheme: dark) {
    .card-title {
        color: #f9fafb !important;
    }
}

.card-content {
    color: #111827 !important;
    line-height: 1.6;
    font-weight: 500;
}

/* Dark mode card content */
@media (prefers-color-scheme: dark) {
    .card-content {
        color: #f3f4f6 !important;
    }
}

.highlight-box, .mythology-box, .note-box {
    background: rgba(249, 250, 251, 0.95) !important;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
    border-left: 4px solid #6366f1;
    color: #111827 !important;
    font-weight: 500;
}

/* Dark mode highlight boxes */
@media (prefers-color-scheme: dark) {
    .highlight-box, .mythology-box, .note-box {
        background: rgba(31, 41, 55, 0.95) !important;
        color: #f3f4f6 !important;
    }
}

.mythology-box {
    border-left-color: #f59e0b;
}

.note-box {
    border-left-color: #10b981;
}

.calculation-steps {
    margin: 1.5rem 0;
}

.step {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.step-number {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: #6366f1;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    flex-shrink: 0;
}

.day-assignments {
    background: rgba(249, 250, 251, 0.95) !important;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
    color: #111827 !important;
    font-weight: 500;
}

/* Dark mode day assignments */
@media (prefers-color-scheme: dark) {
    .day-assignments {
        background: rgba(31, 41, 55, 0.95) !important;
        color: #f3f4f6 !important;
    }
}

.day-assignments ul {
    list-style: none;
    padding: 0;
    margin: 0.5rem 0 0 0;
}

.day-assignments li {
    padding: 0.25rem 0;
}

.day-assignments h4 {
    color: #1f2937 !important;
    margin: 0 0 0.5rem 0;
    font-weight: 700;
}

/* Dark mode day assignments headings */
@media (prefers-color-scheme: dark) {
    .day-assignments h4 {
        color: #f9fafb !important;
    }
}

.avoid-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.avoid-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(254, 226, 226, 0.9) !important;
    border-radius: 0.5rem;
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #7f1d1d !important;
    font-weight: 600;
}

/* Dark mode avoid items */
@media (prefers-color-scheme: dark) {
    .avoid-item {
        background: rgba(127, 29, 29, 0.2) !important;
        color: #fca5a5 !important;
        border: 1px solid rgba(239, 68, 68, 0.4);
    }
}

.avoid-item i {
    color: #dc2626 !important;
}

/* Dark mode avoid item icons */
@media (prefers-color-scheme: dark) {
    .avoid-item i {
        color: #f87171 !important;
    }
}

.acceptable-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.acceptable-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: rgba(220, 252, 231, 0.9) !important;
    border-radius: 0.5rem;
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #14532d !important;
    font-weight: 600;
}

/* Dark mode acceptable items */
@media (prefers-color-scheme: dark) {
    .acceptable-item {
        background: rgba(20, 83, 45, 0.2) !important;
        color: #86efac !important;
        border: 1px solid rgba(34, 197, 94, 0.4);
    }
}

.acceptable-item i {
    color: #16a34a !important;
}

/* Dark mode acceptable item icons */
@media (prefers-color-scheme: dark) {
    .acceptable-item i {
        color: #4ade80 !important;
    }
}

.perspective-points {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.point {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 0.5rem;
    color: #374151;
}

/* Dark mode points */
@media (prefers-color-scheme: dark) {
    .point {
        background: rgba(55, 65, 81, 0.7);
        color: #e5e7eb;
    }
}

.point h5 {
    margin: 0 0 0.5rem 0;
    color: #4c1d95;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Dark mode point headings */
@media (prefers-color-scheme: dark) {
    .point h5 {
        color: #c7d2fe;
    }
}

.location-info {
    background: rgba(255, 255, 255, 0.6);
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
    color: #374151;
}

/* Dark mode location info */
@media (prefers-color-scheme: dark) {
    .location-info {
        background: rgba(55, 65, 81, 0.7);
        color: #e5e7eb;
    }
}

.location-info h5 {
    margin: 0 0 0.5rem 0;
    color: #4c1d95;
}

/* Dark mode location info headings */
@media (prefers-color-scheme: dark) {
    .location-info h5 {
        color: #c7d2fe;
    }
}

.location-info ul {
    margin: 0.5rem 0 0 0;
    padding-left: 1.5rem;
}

.night-info-card {
    background: linear-gradient(135deg, rgba(30, 27, 75, 0.1), rgba(76, 29, 149, 0.1));
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.night-assignments {
    background: rgba(139, 92, 246, 0.15);
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
    color: #374151;
}

/* Dark mode night assignments */
@media (prefers-color-scheme: dark) {
    .night-assignments {
        background: rgba(139, 92, 246, 0.2);
        color: #e5e7eb;
    }
}

.night-assignments h4 {
    margin: 0 0 0.5rem 0;
    color: #4c1d95;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Dark mode night assignments headings */
@media (prefers-color-scheme: dark) {
    .night-assignments h4 {
        color: #c7d2fe;
    }
}

.night-assignments ul {
    margin: 0.5rem 0 0 0;
    padding-left: 1.5rem;
}

.night-assignments li {
    padding: 0.25rem 0;
    color: #4c1d95;
}

/* Dark mode night assignments list items */
@media (prefers-color-scheme: dark) {
    .night-assignments li {
        color: #e5e7eb;
    }
}

.night-note {
    background: rgba(139, 92, 246, 0.1);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
    border-left: 4px solid #8b5cf6;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.night-note i {
    color: #8b5cf6;
    margin-top: 0.125rem;
}

.scientific-facts {
    background: rgba(59, 130, 246, 0.15);
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
    color: #374151;
}

/* Dark mode scientific facts */
@media (prefers-color-scheme: dark) {
    .scientific-facts {
        background: rgba(59, 130, 246, 0.2);
        color: #e5e7eb;
    }
}

.scientific-facts h4 {
    margin: 0 0 0.5rem 0;
    color: #1e40af;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Dark mode scientific facts headings */
@media (prefers-color-scheme: dark) {
    .scientific-facts h4 {
        color: #93c5fd;
    }
}

.scientific-facts ul {
    margin: 0.5rem 0 0 0;
    padding-left: 1.5rem;
}

@media (max-width: 768px) {
    .education-grid {
        grid-template-columns: 1fr;
    }

    .avoid-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
function shareContent() {
    if (navigator.share) {
        navigator.share({
            title: 'Understanding Rahu Kalaya - Astro Wisdom',
            text: 'Learn about Vedic astrology and Rahu Kalaya significance',
            url: window.location.href
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(() => {
            alert('Link copied to clipboard!');
        });
    }
}
</script>
