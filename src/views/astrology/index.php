<?php
/**
 * Astrology Dashboard - Main Index
 */
?>

<link rel="stylesheet" href="/momentum/public/css/astrology.css">

<div class="astrology-container">
    <!-- Header -->
    <div class="astrology-header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-star text-purple-600"></i>
                <h1 class="title-gradient">Astro Wisdom</h1>
                <i class="fas fa-sparkles text-purple-600"></i>
            </div>
            <p class="header-subtitle">Your Guide to Vedic Astrology & Rahu Kalaya</p>
        </div>
    </div>

    <!-- Current Status Alert -->
    <?php if ($isCurrentlyRahuKalaya): ?>
        <?php
            $activeType = $currentRahuKalayaStatus['type'];
            $activeTiming = $currentRahuKalayaStatus['timing'];
            $timeDisplay = $activeType === 'day' ? $rahuKalayaToday['formatted_time'] : $nightRahuKalayaToday['formatted_time'];
            $periodName = $activeType === 'day' ? 'Daytime Rahu Kalaya' : 'Nighttime Rahu Kalaya (Ratri Rahu Kaalaya)';
        ?>
        <div class="alert alert-warning rahu-kalaya-alert">
            <div class="alert-content">
                <i class="fas fa-exclamation-triangle"></i>
                <div>
                    <strong><?= $periodName ?> Active Now!</strong>
                    <p>Current time is within today's <?= strtolower($periodName) ?> period (<?= $timeDisplay ?>).
                    Avoid starting new ventures or important activities.</p>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="alert alert-success">
            <div class="alert-content">
                <i class="fas fa-check-circle"></i>
                <div>
                    <strong>Auspicious Time</strong>
                    <p>Current time is outside both day and night Rahu Kalaya periods. Good time for new activities.</p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Hero Section -->
        <div class="hero-section">
            <h2 class="hero-title">Rahu Kalaya Schedule</h2>
            <p class="hero-description">
                Discover the inauspicious times according to Vedic astrology.
                Plan your important activities wisely by avoiding these periods.
            </p>
        </div>

        <!-- Today's Rahu Kalaya Cards -->
        <div class="today-cards-grid">
            <!-- Daytime Rahu Kalaya -->
            <div class="today-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-sun"></i>
                        Today's Daytime Rahu Kalaya - <?= $currentDay ?>
                    </h3>
                </div>
                <div class="card-content">
                    <div class="time-display">
                        <div class="time-info">
                            <span class="time-label">Day Period:</span>
                            <span class="time-value"><?= $rahuKalayaToday['formatted_time'] ?></span>
                        </div>
                        <div class="status-indicator <?= ($isCurrentlyRahuKalaya && $currentRahuKalayaStatus['type'] === 'day') ? 'active' : 'inactive' ?>">
                            <i class="fas fa-circle"></i>
                            <span><?= ($isCurrentlyRahuKalaya && $currentRahuKalayaStatus['type'] === 'day') ? 'Active Now' : 'Not Active' ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Nighttime Rahu Kalaya -->
            <div class="today-card night-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-moon"></i>
                        Tonight's Ratri Rahu Kaalaya - <?= $currentDay ?>
                    </h3>
                </div>
                <div class="card-content">
                    <div class="time-display">
                        <div class="time-info">
                            <span class="time-label">Night Period:</span>
                            <span class="time-value"><?= $nightRahuKalayaToday['formatted_time'] ?></span>
                        </div>
                        <div class="status-indicator <?= ($isCurrentlyRahuKalaya && $currentRahuKalayaStatus['type'] === 'night') ? 'active' : 'inactive' ?>">
                            <i class="fas fa-circle"></i>
                            <span><?= ($isCurrentlyRahuKalaya && $currentRahuKalayaStatus['type'] === 'night') ? 'Active Now' : 'Not Active' ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Weekly Schedule -->
        <div class="weekly-schedule">
            <div class="schedule-header">
                <h3 class="schedule-title">
                    <i class="fas fa-clock"></i>
                    Weekly Rahu Kalaya Schedule
                </h3>
                <p class="schedule-subtitle">Sri Lanka Standard Time</p>
            </div>

            <div class="schedule-grid">
                <?php foreach ($weeklyRahuKalaya as $timing): ?>
                    <div class="schedule-item <?= $timing['day'] === $currentDay ? 'current-day' : '' ?>">
                        <div class="day-name"><?= $timing['day'] ?></div>
                        <div class="day-time"><?= $timing['formatted_time'] ?></div>
                        <?php if ($timing['day'] === $currentDay): ?>
                            <div class="current-day-badge">Today</div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Important Note -->
        <div class="important-note">
            <div class="note-header">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Important Note</span>
            </div>
            <p>Avoid starting new ventures, traveling, signing contracts, or making important decisions during Rahu Kalaya periods.</p>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h3 class="actions-title">Quick Actions</h3>
            <div class="actions-grid">
                <a href="/momentum/astrology/rahu-kalaya" class="action-card">
                    <i class="fas fa-table"></i>
                    <span>Full Schedule</span>
                </a>
                <a href="/momentum/astrology/info" class="action-card">
                    <i class="fas fa-info-circle"></i>
                    <span>Learn More</span>
                </a>
                <button onclick="refreshStatus()" class="action-card">
                    <i class="fas fa-sync-alt"></i>
                    <span>Refresh Status</span>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh status every minute
setInterval(refreshStatus, 60000);

function refreshStatus() {
    fetch('/momentum/astrology/api/current-status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update status indicator
                const statusIndicator = document.querySelector('.status-indicator');
                const statusText = statusIndicator.querySelector('span');

                if (data.isCurrentlyRahuKalaya) {
                    statusIndicator.className = 'status-indicator active';
                    statusText.textContent = 'Active Now';
                } else {
                    statusIndicator.className = 'status-indicator inactive';
                    statusText.textContent = 'Not Active';
                }

                // Update alert
                location.reload(); // Simple reload for now
            }
        })
        .catch(error => {
            console.error('Error refreshing status:', error);
        });
}

// Add current time display
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour12: true,
        hour: 'numeric',
        minute: '2-digit',
        second: '2-digit'
    });

    // Update time display if element exists
    const timeDisplay = document.querySelector('.current-time');
    if (timeDisplay) {
        timeDisplay.textContent = timeString;
    }
}

// Update time every second
setInterval(updateCurrentTime, 1000);
updateCurrentTime();
</script>
