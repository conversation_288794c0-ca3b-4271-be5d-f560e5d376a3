<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex items-center mb-8">
        <a href="/momentum/online-business/dashboard/<?= $venture['id'] ?>" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
            <i class="fas fa-arrow-left"></i>
            <span class="ml-1">Back to Dashboard</span>
        </a>
        <h1 class="text-3xl font-bold">Edit Business Venture</h1>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Business Information</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Update the details of your online business venture.</p>
        </div>
        
        <form action="/momentum/online-business/update/<?= $venture['id'] ?>" method="post" class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Business Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Business Name <span class="text-red-600">*</span></label>
                    <input type="text" name="name" id="name" value="<?= htmlspecialchars($venture['name']) ?>" required class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                    <?php if (isset($errors['name'])): ?>
                        <p class="mt-1 text-sm text-red-600"><?= $errors['name'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- Business Type -->
                <div>
                    <label for="business_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Business Type <span class="text-red-600">*</span></label>
                    <select name="business_type" id="business_type" required class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                        <option value="">Select a business type</option>
                        <option value="E-commerce" <?= $venture['business_type'] === 'E-commerce' ? 'selected' : '' ?>>E-commerce</option>
                        <option value="Digital Products" <?= $venture['business_type'] === 'Digital Products' ? 'selected' : '' ?>>Digital Products</option>
                        <option value="Services" <?= $venture['business_type'] === 'Services' ? 'selected' : '' ?>>Services</option>
                        <option value="Affiliate Marketing" <?= $venture['business_type'] === 'Affiliate Marketing' ? 'selected' : '' ?>>Affiliate Marketing</option>
                        <option value="Content Creation" <?= $venture['business_type'] === 'Content Creation' ? 'selected' : '' ?>>Content Creation</option>
                        <option value="Dropshipping" <?= $venture['business_type'] === 'Dropshipping' ? 'selected' : '' ?>>Dropshipping</option>
                        <option value="Print on Demand" <?= $venture['business_type'] === 'Print on Demand' ? 'selected' : '' ?>>Print on Demand</option>
                        <option value="SaaS" <?= $venture['business_type'] === 'SaaS' ? 'selected' : '' ?>>SaaS</option>
                        <option value="Membership Site" <?= $venture['business_type'] === 'Membership Site' ? 'selected' : '' ?>>Membership Site</option>
                        <option value="Online Courses" <?= $venture['business_type'] === 'Online Courses' ? 'selected' : '' ?>>Online Courses</option>
                        <option value="Other" <?= $venture['business_type'] === 'Other' ? 'selected' : '' ?>>Other</option>
                    </select>
                    <?php if (isset($errors['business_type'])): ?>
                        <p class="mt-1 text-sm text-red-600"><?= $errors['business_type'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- Website -->
                <div>
                    <label for="website" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Website URL</label>
                    <input type="url" name="website" id="website" value="<?= htmlspecialchars($venture['website'] ?? '') ?>" placeholder="https://example.com" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                </div>

                <!-- Start Date -->
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                    <input type="date" name="start_date" id="start_date" value="<?= $venture['start_date'] ?? '' ?>" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                    <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                        <option value="planning" <?= $venture['status'] === 'planning' ? 'selected' : '' ?>>Planning</option>
                        <option value="startup" <?= $venture['status'] === 'startup' ? 'selected' : '' ?>>Startup</option>
                        <option value="operational" <?= $venture['status'] === 'operational' ? 'selected' : '' ?>>Operational</option>
                        <option value="growing" <?= $venture['status'] === 'growing' ? 'selected' : '' ?>>Growing</option>
                        <option value="declining" <?= $venture['status'] === 'declining' ? 'selected' : '' ?>>Declining</option>
                        <option value="inactive" <?= $venture['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                    </select>
                </div>

                <!-- Description -->
                <div class="md:col-span-2">
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                    <textarea name="description" id="description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50"><?= htmlspecialchars($venture['description'] ?? '') ?></textarea>
                </div>

                <!-- Notes -->
                <div class="md:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                    <textarea name="notes" id="notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50"><?= htmlspecialchars($venture['notes'] ?? '') ?></textarea>
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <a href="/momentum/online-business/dashboard/<?= $venture['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 mr-3">
                    Cancel
                </a>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Update Business Venture
                </button>
            </div>
        </form>
    </div>
</div>
