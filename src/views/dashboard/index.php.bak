<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Dashboard</h1>
            <div class="flex space-x-2">
                <a href="/momentum/tasks/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i> New Task
                </a>
                <a href="/momentum/ideas/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                    <i class="fas fa-lightbulb mr-2"></i> New Idea
                </a>
            </div>
        </div>

        <!-- Dashboard widgets -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="dashboard-widgets">
            <!-- Current Focus Widget -->
            <div class="col-span-1 md:col-span-2 lg:col-span-3 bg-white dark:bg-gray-800 overflow-hidden shadow-lg rounded-lg border-l-4 border-primary-500" data-widget="current-focus" id="current-focus-widget">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-bullseye text-primary-500 mr-2"></i> Current Focus
                        </h3>
                        <div>
                            <button id="focus-mode-toggle" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 mr-2">
                                <i class="fas fa-expand mr-2"></i> Enter Focus Mode
                            </button>
                            <?php if ($currentFocusTask): ?>
                                <button id="clear-focus-task" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                    <i class="fas fa-times mr-2"></i> Clear Focus
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if ($currentFocusTask): ?>
                        <div id="current-focus-content" class="bg-gradient-to-r from-primary-50 to-white dark:from-gray-700 dark:to-gray-800 p-6 rounded-lg shadow-inner border border-primary-100 dark:border-gray-600">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                <div class="mb-4 md:mb-0">
                                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2"><?= View::escape($currentFocusTask['title']) ?></h2>
                                    <?php if (!empty($currentFocusTask['description'])): ?>
                                        <p class="text-gray-600 dark:text-gray-300"><?= nl2br(View::escape($currentFocusTask['description'])) ?></p>
                                    <?php endif; ?>

                                    <div class="flex flex-wrap items-center mt-3 space-x-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            <?php if ($currentFocusTask['status'] === 'todo'): ?>
                                                bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200
                                            <?php elseif ($currentFocusTask['status'] === 'in_progress'): ?>
                                                bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                            <?php elseif ($currentFocusTask['status'] === 'done'): ?>
                                                bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                            <?php endif; ?>
                                        ">
                                            <?php if ($currentFocusTask['status'] === 'todo'): ?>
                                                To Do
                                            <?php elseif ($currentFocusTask['status'] === 'in_progress'): ?>
                                                In Progress
                                            <?php elseif ($currentFocusTask['status'] === 'done'): ?>
                                                Done
                                            <?php endif; ?>
                                        </span>

                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            <?php if ($currentFocusTask['priority'] === 'low'): ?>
                                                bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200
                                            <?php elseif ($currentFocusTask['priority'] === 'medium'): ?>
                                                bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                            <?php elseif ($currentFocusTask['priority'] === 'high'): ?>
                                                bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                                            <?php elseif ($currentFocusTask['priority'] === 'urgent'): ?>
                                                bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                            <?php endif; ?>
                                        ">
                                            <?= ucfirst($currentFocusTask['priority']) ?> Priority
                                        </span>

                                        <?php if (!empty($currentFocusTask['category_id']) && !empty($currentFocusTask['category_color'])): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" style="background-color: <?= $currentFocusTask['category_color'] ?>25; color: <?= $currentFocusTask['category_color'] ?>;">
                                                <?= View::escape($currentFocusTask['category_name'] ?? 'Category') ?>
                                            </span>
                                        <?php endif; ?>

                                        <?php if ($currentFocusTask['due_date']): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                                <i class="far fa-calendar mr-1"></i> Due: <?= View::formatDate($currentFocusTask['due_date']) ?>
                                                <?php if ($currentFocusTask['due_time']): ?>
                                                    at <?= View::formatTime($currentFocusTask['due_time']) ?>
                                                <?php endif; ?>
                                            </span>
                                        <?php endif; ?>

                                        <?php if (!empty($currentFocusTask['estimated_time'])): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
                                                <i class="far fa-clock mr-1"></i> Est: <?= $currentFocusTask['estimated_time'] ?> min
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="flex flex-col space-y-3">
                                    <a href="/momentum/productivity/focus-timer?task_id=<?= $currentFocusTask['id'] ?>" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                        <i class="fas fa-clock mr-2"></i> Start Timer
                                    </a>

                                    <button id="mark-complete-btn" data-task-id="<?= $currentFocusTask['id'] ?>" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                                        <i class="fas fa-check mr-2"></i> Mark as Done
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div id="current-focus-empty" class="bg-gradient-to-r from-primary-50 to-white dark:from-gray-700 dark:to-gray-800 p-6 rounded-lg shadow-inner border border-primary-100 dark:border-gray-600 text-center">
                            <div class="flex flex-col items-center justify-center py-4">
                                <i class="fas fa-bullseye text-primary-400 dark:text-primary-300 text-4xl mb-4"></i>
                                <p class="text-gray-600 dark:text-gray-300 mb-4 text-lg">No task currently in focus</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto">Select a task from "Today's Tasks" below to focus on it. Focusing on one task at a time helps improve concentration and productivity.</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
