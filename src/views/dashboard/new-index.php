<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Dashboard</h1>
            <div class="flex space-x-2">
                <a href="/momentum/productivity/tools" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                    <i class="fas fa-th-large mr-2"></i> Productivity Tools
                </a>
                <a href="/momentum/tasks/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i> New Task
                </a>
                <a href="/momentum/ideas/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                    <i class="fas fa-lightbulb mr-2"></i> New Idea
                </a>
            </div>
        </div>

        <!-- Current Focus Widget -->
        <div class="mb-8 bg-white dark:bg-gray-800 overflow-hidden shadow-lg rounded-lg border-l-4 border-primary-500" id="current-focus-widget">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-bullseye text-primary-500 mr-2"></i> Current Focus
                    </h3>
                    <div>
                        <button id="focus-mode-toggle" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 mr-2">
                            <i class="fas fa-expand mr-2"></i> Enter Focus Mode
                        </button>
                        <?php if ($currentFocusTask): ?>
                            <button id="clear-focus-task" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <i class="fas fa-times mr-2"></i> Clear Focus
                            </button>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if ($currentFocusTask): ?>
                    <div id="current-focus-content" class="bg-gradient-to-r from-primary-50 to-white dark:from-gray-700 dark:to-gray-800 p-6 rounded-lg shadow-inner border border-primary-100 dark:border-gray-600">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                            <div class="mb-4 md:mb-0">
                                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2"><?= View::escape($currentFocusTask['title']) ?></h2>
                                <?php if (!empty($currentFocusTask['description'])): ?>
                                    <p class="text-gray-600 dark:text-gray-300"><?= nl2br(View::escape($currentFocusTask['description'])) ?></p>
                                <?php endif; ?>

                                <div class="flex flex-wrap items-center mt-3 space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        <?php if ($currentFocusTask['status'] === 'todo'): ?>
                                            bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200
                                        <?php elseif ($currentFocusTask['status'] === 'in_progress'): ?>
                                            bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                        <?php elseif ($currentFocusTask['status'] === 'done'): ?>
                                            bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                        <?php endif; ?>
                                    ">
                                        <?php if ($currentFocusTask['status'] === 'todo'): ?>
                                            To Do
                                        <?php elseif ($currentFocusTask['status'] === 'in_progress'): ?>
                                            In Progress
                                        <?php elseif ($currentFocusTask['status'] === 'done'): ?>
                                            Done
                                        <?php endif; ?>
                                    </span>

                                    <?php if (!empty($currentFocusTask['priority'])): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        <?php if ($currentFocusTask['priority'] === 'low'): ?>
                                            bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200
                                        <?php elseif ($currentFocusTask['priority'] === 'medium'): ?>
                                            bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                        <?php elseif ($currentFocusTask['priority'] === 'high'): ?>
                                            bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                                        <?php elseif ($currentFocusTask['priority'] === 'urgent'): ?>
                                            bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                        <?php endif; ?>
                                    ">
                                        <?= ucfirst($currentFocusTask['priority']) ?> Priority
                                    </span>
                                    <?php endif; ?>

                                    <?php if (!empty($currentFocusTask['category_id']) && !empty($currentFocusTask['category_color'])): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" style="background-color: <?= $currentFocusTask['category_color'] ?>25; color: <?= $currentFocusTask['category_color'] ?>;">
                                            <?= View::escape($currentFocusTask['category_name'] ?? 'Category') ?>
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($currentFocusTask['due_date']): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                            <i class="far fa-calendar mr-1"></i> Due: <?= View::formatDate($currentFocusTask['due_date']) ?>
                                            <?php if ($currentFocusTask['due_time']): ?>
                                                at <?= View::formatTime($currentFocusTask['due_time']) ?>
                                            <?php endif; ?>
                                        </span>
                                    <?php endif; ?>

                                    <?php if (!empty($currentFocusTask['estimated_time'])): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
                                            <i class="far fa-clock mr-1"></i> Est: <?= $currentFocusTask['estimated_time'] ?> min
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="flex flex-col space-y-3">
                                <a href="/momentum/productivity/focus-timer?task_id=<?= $currentFocusTask['id'] ?>" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                    <i class="fas fa-clock mr-2"></i> Start Timer
                                </a>

                                <button id="mark-complete-btn" data-task-id="<?= $currentFocusTask['id'] ?>" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                                    <i class="fas fa-check mr-2"></i> Mark as Done
                                </button>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div id="current-focus-empty" class="bg-gradient-to-r from-primary-50 to-white dark:from-gray-700 dark:to-gray-800 p-6 rounded-lg shadow-inner border border-primary-100 dark:border-gray-600 text-center">
                        <div class="flex flex-col items-center justify-center py-4">
                            <i class="fas fa-bullseye text-primary-400 dark:text-primary-300 text-4xl mb-4"></i>
                            <p class="text-gray-600 dark:text-gray-300 mb-4 text-lg">No task currently in focus</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto">Select a task from "Today's Tasks" below to focus on it. Focusing on one task at a time helps improve concentration and productivity.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Productivity Tools Banner -->
        <div class="mb-8 bg-gradient-to-r from-red-500 to-orange-500 overflow-hidden shadow-lg rounded-lg">
            <div class="px-6 py-4 flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="mb-4 md:mb-0">
                    <h3 class="text-xl font-semibold text-white flex items-center">
                        <i class="fas fa-th-large mr-2"></i> Productivity Tools
                    </h3>
                    <p class="text-white text-opacity-90 mt-1">
                        Access all productivity tools in one place - Focus Timer, Task Batching, Energy Tracking and more
                    </p>
                </div>
                <a href="/momentum/productivity/tools" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white">
                    <i class="fas fa-arrow-right mr-2"></i> Open Productivity Tools
                </a>
            </div>
        </div>

        <!-- Feature Categories - Hub and Spoke Model -->
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Features</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- ADHD Management -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border-t-4 border-purple-500 hover:shadow-lg transition-shadow duration-200">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 bg-purple-100 dark:bg-purple-900 p-3 rounded-full">
                            <i class="fas fa-brain text-purple-600 dark:text-purple-300 text-xl"></i>
                        </div>
                        <h3 class="ml-3 text-lg font-medium text-gray-900 dark:text-white">ADHD Management</h3>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Tools for symptom tracking, CBT, and executive function support.</p>
                    <div class="grid grid-cols-2 gap-2">
                        <a href="/momentum/adhd" class="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 flex items-center">
                            <i class="fas fa-tachometer-alt mr-1"></i> Dashboard
                        </a>
                        <a href="/momentum/adhd/symptom-tracker" class="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 flex items-center">
                            <i class="fas fa-chart-line mr-1"></i> Symptom Tracker
                        </a>
                        <a href="/momentum/adhd/cbt/thought-records" class="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 flex items-center">
                            <i class="fas fa-comments mr-1"></i> Thought Records
                        </a>
                        <a href="/momentum/adhd/mindfulness" class="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 flex items-center">
                            <i class="fas fa-spa mr-1"></i> Mindfulness
                        </a>
                    </div>
                    <div class="mt-4">
                        <a href="/momentum/adhd" class="inline-flex items-center px-3 py-1.5 border border-purple-300 dark:border-purple-700 rounded-md shadow-sm text-sm font-medium text-purple-700 dark:text-purple-300 bg-white dark:bg-gray-800 hover:bg-purple-50 dark:hover:bg-purple-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200">
                            <i class="fas fa-arrow-right mr-1"></i> Go to ADHD Hub
                        </a>
                    </div>
                </div>
            </div>

            <!-- Task Management -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border-t-4 border-blue-500 hover:shadow-lg transition-shadow duration-200">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 p-3 rounded-full">
                            <i class="fas fa-tasks text-blue-600 dark:text-blue-300 text-xl"></i>
                        </div>
                        <h3 class="ml-3 text-lg font-medium text-gray-900 dark:text-white">Task Management</h3>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Organize and track your tasks with flexible views and categories.</p>
                    <div class="grid grid-cols-2 gap-2">
                        <a href="/momentum/tasks" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center">
                            <i class="fas fa-list mr-1"></i> Task List
                        </a>
                        <a href="/momentum/tasks/calendar" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center">
                            <i class="fas fa-calendar-alt mr-1"></i> Calendar
                        </a>
                        <a href="/momentum/tasks/create" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center">
                            <i class="fas fa-plus mr-1"></i> New Task
                        </a>
                        <a href="/momentum/tasks?status=done" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center">
                            <i class="fas fa-check-circle mr-1"></i> Completed
                        </a>
                    </div>
                    <div class="mt-4">
                        <a href="/momentum/tasks" class="inline-flex items-center px-3 py-1.5 border border-blue-300 dark:border-blue-700 rounded-md shadow-sm text-sm font-medium text-blue-700 dark:text-blue-300 bg-white dark:bg-gray-800 hover:bg-blue-50 dark:hover:bg-blue-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            <i class="fas fa-arrow-right mr-1"></i> Go to Tasks
                        </a>
                    </div>
                </div>
            </div>

            <!-- Project Management -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border-t-4 border-green-500 hover:shadow-lg transition-shadow duration-200">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 p-3 rounded-full">
                            <i class="fas fa-project-diagram text-green-600 dark:text-green-300 text-xl"></i>
                        </div>
                        <h3 class="ml-3 text-lg font-medium text-gray-900 dark:text-white">Project Management</h3>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Manage complex projects with task dependencies and progress tracking.</p>
                    <div class="grid grid-cols-2 gap-2">
                        <a href="/momentum/projects" class="text-sm text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 flex items-center">
                            <i class="fas fa-list mr-1"></i> Project List
                        </a>
                        <a href="/momentum/projects/create" class="text-sm text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 flex items-center">
                            <i class="fas fa-plus mr-1"></i> New Project
                        </a>
                        <a href="/momentum/reports" class="text-sm text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 flex items-center">
                            <i class="fas fa-chart-bar mr-1"></i> Reports
                        </a>
                        <a href="/momentum/help/project-features" class="text-sm text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 flex items-center">
                            <i class="fas fa-question-circle mr-1"></i> Help
                        </a>
                    </div>
                    <div class="mt-4">
                        <a href="/momentum/projects" class="inline-flex items-center px-3 py-1.5 border border-green-300 dark:border-green-700 rounded-md shadow-sm text-sm font-medium text-green-700 dark:text-green-300 bg-white dark:bg-gray-800 hover:bg-green-50 dark:hover:bg-green-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                            <i class="fas fa-arrow-right mr-1"></i> Go to Projects
                        </a>
                    </div>
                </div>
            </div>

            <!-- Financial Management -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border-t-4 border-yellow-500 hover:shadow-lg transition-shadow duration-200">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 bg-yellow-100 dark:bg-yellow-900 p-3 rounded-full">
                            <i class="fas fa-money-bill-wave text-yellow-600 dark:text-yellow-300 text-xl"></i>
                        </div>
                        <h3 class="ml-3 text-lg font-medium text-gray-900 dark:text-white">Financial Management</h3>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Track income, expenses, subscriptions, and manage your budget.</p>
                    <div class="grid grid-cols-2 gap-2">
                        <a href="/momentum/finances" class="text-sm text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 flex items-center">
                            <i class="fas fa-tachometer-alt mr-1"></i> Dashboard
                        </a>
                        <a href="/momentum/finances/transactions/create" class="text-sm text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 flex items-center">
                            <i class="fas fa-exchange-alt mr-1"></i> New Transaction
                        </a>
                        <a href="/momentum/finances/subscriptions" class="text-sm text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 flex items-center">
                            <i class="fas fa-sync mr-1"></i> Subscriptions
                        </a>
                        <a href="/momentum/finances/debts" class="text-sm text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 flex items-center">
                            <i class="fas fa-hand-holding-usd mr-1"></i> Debt Tracking
                        </a>
                    </div>
                    <div class="mt-4">
                        <a href="/momentum/finances" class="inline-flex items-center px-3 py-1.5 border border-yellow-300 dark:border-yellow-700 rounded-md shadow-sm text-sm font-medium text-yellow-700 dark:text-yellow-300 bg-white dark:bg-gray-800 hover:bg-yellow-50 dark:hover:bg-yellow-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200">
                            <i class="fas fa-arrow-right mr-1"></i> Go to Finances
                        </a>
                    </div>
                </div>
            </div>

            <!-- Productivity Tools -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border-t-4 border-red-500 hover:shadow-lg transition-shadow duration-200">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 bg-red-100 dark:bg-red-900 p-3 rounded-full">
                            <i class="fas fa-stopwatch text-red-600 dark:text-red-300 text-xl"></i>
                        </div>
                        <h3 class="ml-3 text-lg font-medium text-gray-900 dark:text-white">Productivity Tools</h3>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Enhance focus and productivity with specialized tools.</p>
                    <div class="grid grid-cols-2 gap-2">
                        <a href="/momentum/productivity/tools" class="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 flex items-center font-medium">
                            <i class="fas fa-th-large mr-1"></i> All Productivity Tools
                        </a>
                        <a href="/momentum/productivity/focus-timer" class="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 flex items-center">
                            <i class="fas fa-clock mr-1"></i> Focus Timer
                        </a>
                        <a href="/momentum/productivity/focus-mode" class="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 flex items-center">
                            <i class="fas fa-expand mr-1"></i> Focus Mode
                        </a>
                        <a href="/momentum/productivity/time-blocking" class="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 flex items-center">
                            <i class="fas fa-calendar-alt mr-1"></i> Time Blocking
                        </a>
                        <a href="/momentum/productivity/task-batching" class="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 flex items-center">
                            <i class="fas fa-layer-group mr-1"></i> Task Batching
                        </a>
                        <a href="/momentum/productivity/energy-tracking" class="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 flex items-center">
                            <i class="fas fa-bolt mr-1"></i> Energy Tracking
                        </a>
                    </div>
                    <div class="mt-4">
                        <a href="/momentum/productivity/tools" class="inline-flex items-center px-3 py-1.5 border border-red-300 dark:border-red-700 rounded-md shadow-sm text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                            <i class="fas fa-arrow-right mr-1"></i> View All Tools
                        </a>
                    </div>
                </div>
            </div>

            <!-- Idea Management -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border-t-4 border-indigo-500 hover:shadow-lg transition-shadow duration-200">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 bg-indigo-100 dark:bg-indigo-900 p-3 rounded-full">
                            <i class="fas fa-lightbulb text-indigo-600 dark:text-indigo-300 text-xl"></i>
                        </div>
                        <h3 class="ml-3 text-lg font-medium text-gray-900 dark:text-white">Idea Management</h3>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Capture and organize your ideas and inspirations.</p>
                    <div class="grid grid-cols-2 gap-2">
                        <a href="/momentum/ideas" class="text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 flex items-center">
                            <i class="fas fa-list mr-1"></i> Idea List
                        </a>
                        <a href="/momentum/ideas/create" class="text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 flex items-center">
                            <i class="fas fa-plus mr-1"></i> New Idea
                        </a>
                        <a href="/momentum/ideas?filter=recent" class="text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 flex items-center">
                            <i class="fas fa-clock mr-1"></i> Recent Ideas
                        </a>
                        <a href="/momentum/ideas?filter=unconverted" class="text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 flex items-center">
                            <i class="fas fa-exchange-alt mr-1"></i> Unconverted
                        </a>
                    </div>
                    <div class="mt-4">
                        <a href="/momentum/ideas" class="inline-flex items-center px-3 py-1.5 border border-indigo-300 dark:border-indigo-700 rounded-md shadow-sm text-sm font-medium text-indigo-700 dark:text-indigo-300 bg-white dark:bg-gray-800 hover:bg-indigo-50 dark:hover:bg-indigo-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                            <i class="fas fa-arrow-right mr-1"></i> Go to Ideas
                        </a>
                    </div>
                </div>
            </div>

            <!-- Health & Medical -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border-t-4 border-pink-500 hover:shadow-lg transition-shadow duration-200">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 bg-pink-100 dark:bg-pink-900 p-3 rounded-full">
                            <i class="fas fa-heartbeat text-pink-600 dark:text-pink-300 text-xl"></i>
                        </div>
                        <h3 class="ml-3 text-lg font-medium text-gray-900 dark:text-white">Health & Medical</h3>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Track and manage your health records and medical tests.</p>
                    <div class="grid grid-cols-2 gap-2">
                        <a href="/momentum/medical" class="text-sm text-pink-600 dark:text-pink-400 hover:text-pink-800 dark:hover:text-pink-300 flex items-center">
                            <i class="fas fa-tachometer-alt mr-1"></i> Dashboard
                        </a>
                        <a href="/momentum/medical/reports" class="text-sm text-pink-600 dark:text-pink-400 hover:text-pink-800 dark:hover:text-pink-300 flex items-center">
                            <i class="fas fa-clipboard-list mr-1"></i> Test Reports
                        </a>
                        <a href="/momentum/medical/reports/new" class="text-sm text-pink-600 dark:text-pink-400 hover:text-pink-800 dark:hover:text-pink-300 flex items-center">
                            <i class="fas fa-plus mr-1"></i> New Report
                        </a>
                        <a href="/momentum/medical/reports/generate" class="text-sm text-pink-600 dark:text-pink-400 hover:text-pink-800 dark:hover:text-pink-300 flex items-center">
                            <i class="fas fa-chart-line mr-1"></i> Generate Reports
                        </a>
                    </div>
                    <div class="mt-4">
                        <a href="/momentum/medical" class="inline-flex items-center px-3 py-1.5 border border-pink-300 dark:border-pink-700 rounded-md shadow-sm text-sm font-medium text-pink-700 dark:text-pink-300 bg-white dark:bg-gray-800 hover:bg-pink-50 dark:hover:bg-pink-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-200">
                            <i class="fas fa-arrow-right mr-1"></i> Go to Medical Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <!-- Tools -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border-t-4 border-gray-500 hover:shadow-lg transition-shadow duration-200">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 bg-gray-100 dark:bg-gray-700 p-3 rounded-full">
                            <i class="fas fa-tools text-gray-600 dark:text-gray-300 text-xl"></i>
                        </div>
                        <h3 class="ml-3 text-lg font-medium text-gray-900 dark:text-white">Tools</h3>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Utility tools to help with various tasks.</p>
                    <div class="grid grid-cols-2 gap-2">
                        <a href="/momentum/tools/currency-converter" class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 flex items-center">
                            <i class="fas fa-exchange-alt mr-1"></i> Currency Converter
                        </a>
                        <a href="/momentum/dashboard/toggle-theme" class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 flex items-center">
                            <i class="fas fa-adjust mr-1"></i> Toggle Theme
                        </a>
                    </div>
                    <div class="mt-4">
                        <a href="/momentum/tools" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                            <i class="fas fa-arrow-right mr-1"></i> Go to Tools
                        </a>
                    </div>
                </div>
            </div>

            <!-- Astrology & Vedic Wisdom -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border-t-4 border-purple-500 hover:shadow-lg transition-shadow duration-200">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 bg-purple-100 dark:bg-purple-900 p-3 rounded-full">
                            <i class="fas fa-star text-purple-600 dark:text-purple-300 text-xl"></i>
                        </div>
                        <h3 class="ml-3 text-lg font-medium text-gray-900 dark:text-white">Astrology & Vedic Wisdom</h3>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Rahu Kalaya timings and Vedic astrology guidance for mindful planning.</p>
                    <div class="grid grid-cols-2 gap-2">
                        <a href="/momentum/astrology" class="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 flex items-center">
                            <i class="fas fa-tachometer-alt mr-1"></i> Dashboard
                        </a>
                        <a href="/momentum/astrology/rahu-kalaya" class="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 flex items-center">
                            <i class="fas fa-clock mr-1"></i> Rahu Kalaya
                        </a>
                        <a href="/momentum/astrology/info" class="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 flex items-center">
                            <i class="fas fa-book mr-1"></i> Learn More
                        </a>
                        <a href="/momentum/astrology/api/current-status" class="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 flex items-center">
                            <i class="fas fa-sync mr-1"></i> Current Status
                        </a>
                    </div>
                    <div class="mt-4">
                        <a href="/momentum/astrology" class="inline-flex items-center px-3 py-1.5 border border-purple-300 dark:border-purple-700 rounded-md shadow-sm text-sm font-medium text-purple-700 dark:text-purple-300 bg-white dark:bg-gray-800 hover:bg-purple-50 dark:hover:bg-purple-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200">
                            <i class="fas fa-arrow-right mr-1"></i> Go to Astrology
                        </a>
                    </div>
                </div>
            </div>

            <!-- Help & Resources -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border-t-4 border-teal-500 hover:shadow-lg transition-shadow duration-200">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 bg-teal-100 dark:bg-teal-900 p-3 rounded-full">
                            <i class="fas fa-question-circle text-teal-600 dark:text-teal-300 text-xl"></i>
                        </div>
                        <h3 class="ml-3 text-lg font-medium text-gray-900 dark:text-white">Help & Resources</h3>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Access guides, tutorials, and support resources.</p>
                    <div class="grid grid-cols-2 gap-2">
                        <a href="/momentum/help/user-guide" class="text-sm text-teal-600 dark:text-teal-400 hover:text-teal-800 dark:hover:text-teal-300 flex items-center">
                            <i class="fas fa-book mr-1"></i> User Guide
                        </a>
                        <a href="/momentum/help/feature-overview" class="text-sm text-teal-600 dark:text-teal-400 hover:text-teal-800 dark:hover:text-teal-300 flex items-center">
                            <i class="fas fa-list-alt mr-1"></i> Feature Overview
                        </a>
                        <a href="/momentum/help/adhd-project-planning-guide" class="text-sm text-teal-600 dark:text-teal-400 hover:text-teal-800 dark:hover:text-teal-300 flex items-center">
                            <i class="fas fa-project-diagram mr-1"></i> ADHD Planning
                        </a>
                        <a href="/momentum/help/dashboard-redesign-plan" class="text-sm text-teal-600 dark:text-teal-400 hover:text-teal-800 dark:hover:text-teal-300 flex items-center">
                            <i class="fas fa-columns mr-1"></i> Dashboard Plan
                        </a>
                    </div>
                    <div class="mt-4">
                        <a href="/momentum/help" class="inline-flex items-center px-3 py-1.5 border border-teal-300 dark:border-teal-700 rounded-md shadow-sm text-sm font-medium text-teal-700 dark:text-teal-300 bg-white dark:bg-gray-800 hover:bg-teal-50 dark:hover:bg-teal-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors duration-200">
                            <i class="fas fa-arrow-right mr-1"></i> Go to Help Center
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Today's Tasks Widget -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg mb-8" data-widget="today-tasks">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Today's Tasks</h3>
                    <a href="/momentum/tasks?due_date=<?= date('Y-m-d') ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        View all
                    </a>
                </div>
                <?php if (empty($todayTasks)): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No tasks for today</p>
                        <a href="/momentum/tasks/create" class="mt-2 inline-flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                            <i class="fas fa-plus mr-1"></i> Add a task
                        </a>
                    </div>
                <?php else: ?>
                    <ul class="divide-y divide-gray-200 dark:divide-gray-700" id="today-tasks-list">
                        <?php foreach ($todayTasks as $task): ?>
                            <li class="py-3 task-item <?= $currentFocusTask && $currentFocusTask['id'] == $task['id'] ? 'bg-primary-50 dark:bg-primary-900 border-l-4 border-primary-500 pl-2' : '' ?>" data-task-id="<?= $task['id'] ?>">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center flex-1">
                                        <input type="checkbox" data-task-id="<?= $task['id'] ?>" class="task-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 rounded" <?= $task['status'] === 'done' ? 'checked' : '' ?>>
                                        <div class="ml-3 flex-1">
                                            <a href="/momentum/tasks/view/<?= $task['id'] ?>" class="text-sm font-medium text-gray-900 dark:text-white <?= $task['status'] === 'done' ? 'line-through text-gray-500 dark:text-gray-400' : '' ?>">
                                                <?= View::escape($task['title']) ?>
                                            </a>
                                            <div class="flex flex-wrap items-center mt-1">
                                                <?php if ($task['due_time']): ?>
                                                    <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">
                                                        <i class="far fa-clock"></i> <?= View::formatTime($task['due_time']) ?>
                                                    </span>
                                                <?php endif; ?>
                                                <?php if (!empty($task['estimated_time'])): ?>
                                                    <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">
                                                        <i class="fas fa-hourglass-half"></i> <?= $task['estimated_time'] ?> min
                                                    </span>
                                                <?php endif; ?>
                                                <?php if (!empty($task['category_id']) && !empty($task['category_color'])): ?>
                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium mr-2" style="background-color: <?= $task['category_color'] ?>25; color: <?= $task['category_color'] ?>;">
                                                        <?= View::escape($task['category_name'] ?? 'Category') ?>
                                                    </span>
                                                <?php endif; ?>
                                                <?php if ($task['priority'] === 'high'): ?>
                                                    <span class="text-orange-500 dark:text-orange-400 text-xs" title="High Priority">
                                                        <i class="fas fa-arrow-up"></i> High
                                                    </span>
                                                <?php elseif ($task['priority'] === 'urgent'): ?>
                                                    <span class="text-red-500 dark:text-red-400 text-xs" title="Urgent">
                                                        <i class="fas fa-exclamation-circle"></i> Urgent
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ml-2 flex items-center">
                                        <?php if ($currentFocusTask && $currentFocusTask['id'] == $task['id']): ?>
                                            <span class="text-xs text-primary-600 dark:text-primary-400 font-medium mr-2">
                                                <i class="fas fa-bullseye"></i> In Focus
                                            </span>
                                        <?php else: ?>
                                            <button type="button" class="set-focus-btn inline-flex items-center px-2 py-1 border border-transparent rounded text-xs font-medium text-primary-700 dark:text-primary-300 bg-primary-100 dark:bg-primary-800 hover:bg-primary-200 dark:hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200" data-task-id="<?= $task['id'] ?>">
                                                <i class="fas fa-bullseye mr-1"></i> Focus
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
        </div>

        <!-- Today's Schedule Widget -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg mb-8" data-widget="today-schedule">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Today's Schedule</h3>
                    <a href="/momentum/productivity/time-blocking" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        View calendar
                    </a>
                </div>
                <div id="today-time-blocks-widget">
                    <div class="flex justify-center items-center h-20">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Energy Level Widget -->
        <div id="energy-level-widget" class="mb-8">
            <div class="flex justify-center items-center h-20">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
        </div>

        <!-- Task Batch Widget -->
        <div id="task-batch-widget" class="mb-8">
            <div class="flex justify-center items-center h-20">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
        </div>

        <!-- Keyboard Shortcuts Guide -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg mb-8" data-widget="keyboard-shortcuts">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Keyboard Shortcuts</h3>
                    <button id="toggle-shortcuts-details" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        Show Details
                    </button>
                </div>
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                        <p class="text-xs text-gray-700 dark:text-gray-300 font-medium">Navigation</p>
                        <p class="text-sm text-gray-900 dark:text-white">
                            <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded">Backspace</kbd> Go Back
                        </p>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                        <p class="text-xs text-gray-700 dark:text-gray-300 font-medium">Feature Access</p>
                        <p class="text-sm text-gray-900 dark:text-white">
                            <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded">Alt</kbd> + Letter
                        </p>
                    </div>
                </div>
                <div id="shortcuts-details" class="hidden bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Feature Shortcuts (Alt + Key)</h4>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded mr-2">A</kbd>
                            <span class="text-gray-700 dark:text-gray-300">ADHD Management</span>
                        </div>
                        <div class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded mr-2">T</kbd>
                            <span class="text-gray-700 dark:text-gray-300">Task Management</span>
                        </div>
                        <div class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded mr-2">P</kbd>
                            <span class="text-gray-700 dark:text-gray-300">Project Management</span>
                        </div>
                        <div class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded mr-2">F</kbd>
                            <span class="text-gray-700 dark:text-gray-300">Financial Management</span>
                        </div>
                        <div class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded mr-2">O</kbd>
                            <span class="text-gray-700 dark:text-gray-300">Focus Timer</span>
                        </div>
                        <div class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded mr-2">I</kbd>
                            <span class="text-gray-700 dark:text-gray-300">Idea Management</span>
                        </div>
                        <div class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded mr-2">C</kbd>
                            <span class="text-gray-700 dark:text-gray-300">Currency Converter</span>
                        </div>
                        <div class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded mr-2">S</kbd>
                            <span class="text-gray-700 dark:text-gray-300">Astrology</span>
                        </div>
                        <div class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded mr-2">H</kbd>
                            <span class="text-gray-700 dark:text-gray-300">Help Center</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Summary Widget -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg mb-8" data-widget="financial-summary">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Financial Summary</h3>
                    <a href="/momentum/finances" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        View details
                    </a>
                </div>
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-green-50 dark:bg-green-900 p-3 rounded-lg">
                            <p class="text-xs text-green-700 dark:text-green-300 font-medium">Income</p>
                            <p class="text-lg font-semibold text-green-800 dark:text-green-200">
                                <?= View::formatCurrency($financialSummary['total_income'] ?? 0) ?>
                            </p>
                        </div>
                        <div class="bg-red-50 dark:bg-red-900 p-3 rounded-lg">
                            <p class="text-xs text-red-700 dark:text-red-300 font-medium">Expenses</p>
                            <p class="text-lg font-semibold text-red-800 dark:text-red-200">
                                <?= View::formatCurrency($financialSummary['total_expense'] ?? 0) ?>
                            </p>
                        </div>
                    </div>
                    <div class="bg-blue-50 dark:bg-blue-900 p-3 rounded-lg">
                        <p class="text-xs text-blue-700 dark:text-blue-300 font-medium">Balance</p>
                        <p class="text-lg font-semibold <?= ($financialSummary['total_income'] ?? 0) - ($financialSummary['total_expense'] ?? 0) >= 0 ? 'text-blue-800 dark:text-blue-200' : 'text-red-800 dark:text-red-200' ?>">
                            <?= View::formatCurrency(($financialSummary['total_income'] ?? 0) - ($financialSummary['total_expense'] ?? 0)) ?>
                        </p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mb-2">Monthly Subscriptions</p>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">
                            <?= View::formatCurrency($monthlyCost) ?> / month
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle task checkbox clicks
        const taskCheckboxes = document.querySelectorAll('.task-checkbox');
        const markCompleteBtn = document.getElementById('mark-complete-btn');
        const focusModeToggle = document.getElementById('focus-mode-toggle');
        const clearFocusBtn = document.getElementById('clear-focus-task');
        const setFocusBtns = document.querySelectorAll('.set-focus-btn');
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        const currentFocusWidget = document.getElementById('current-focus-widget');

        // Function to show success message
        function showSuccessMessage(message) {
            const successMessage = document.createElement('div');
            successMessage.className = 'fixed bottom-4 right-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md animate-fade-in z-50';
            successMessage.innerHTML = `
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm">${message}</p>
                    </div>
                </div>
            `;
            document.body.appendChild(successMessage);

            // Remove message after 3 seconds
            setTimeout(() => {
                successMessage.classList.add('opacity-0', 'transition-opacity', 'duration-500');
                setTimeout(() => {
                    successMessage.remove();
                }, 500);
            }, 3000);
        }

        // Function to mark task as complete
        function markTaskAsComplete(taskId) {
            return fetch(`/momentum/tasks/complete/${taskId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json());
        }

        // Function to set focus task
        function setFocusTask(taskId) {
            console.log('Setting focus task with ID:', taskId);

            return fetch('/momentum/dashboard/set-focus-task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ task_id: taskId })
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`Server responded with status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('Error in setFocusTask:', error);
                throw error;
            });
        }

        // Function to clear focus task
        function clearFocusTask() {
            return fetch('/momentum/dashboard/clear-focus-task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json());
        }

        // Handle task checkbox clicks
        taskCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const taskId = this.getAttribute('data-task-id');

                if (this.checked) {
                    markTaskAsComplete(taskId)
                    .then(data => {
                        if (data.success) {
                            // Update UI
                            const taskItem = this.closest('.task-item');
                            const taskTitle = taskItem.querySelector('a');
                            taskTitle.classList.add('line-through', 'text-gray-500', 'dark:text-gray-400');

                            // If this was the focus task, clear it
                            if (taskItem.classList.contains('bg-primary-50') || taskItem.classList.contains('dark:bg-primary-900')) {
                                clearFocusTask().then(() => {
                                    // Reload the page to refresh the focus task widget
                                    window.location.reload();
                                });
                            }

                            showSuccessMessage('Task marked as complete!');
                        } else {
                            // Uncheck the checkbox if there was an error
                            this.checked = false;
                            alert('Failed to mark task as complete. Please try again.');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        this.checked = false;
                        alert('An error occurred. Please try again.');
                    });
                }
            });
        });

        // Handle Mark Complete button in Current Focus widget
        if (markCompleteBtn) {
            markCompleteBtn.addEventListener('click', function() {
                const taskId = this.getAttribute('data-task-id');

                markTaskAsComplete(taskId)
                .then(data => {
                    if (data.success) {
                        // Reload the page to refresh the widgets
                        window.location.reload();
                    } else {
                        alert('Failed to mark task as complete. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
            });
        }

        // Handle Focus Mode toggle
        if (focusModeToggle && currentFocusWidget) {
            focusModeToggle.addEventListener('click', function() {
                const taskId = markCompleteBtn ? markCompleteBtn.getAttribute('data-task-id') : null;

                if (taskId) {
                    window.location.href = `/momentum/productivity/focus-mode?task_id=${taskId}`;
                } else {
                    alert('Please set a focus task first.');
                }
            });
        }

        // Handle Clear Focus button
        if (clearFocusBtn) {
            clearFocusBtn.addEventListener('click', function() {
                clearFocusTask()
                .then(data => {
                    if (data.success) {
                        // Reload the page to refresh the widgets
                        window.location.reload();
                    } else {
                        alert('Failed to clear focus task. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
            });
        }

        // Handle Set Focus buttons
        setFocusBtns.forEach(button => {
            button.addEventListener('click', function() {
                const taskId = this.getAttribute('data-task-id');

                setFocusTask(taskId)
                .then(data => {
                    if (data.success) {
                        // Reload the page to refresh the widgets
                        window.location.reload();
                    } else {
                        alert('Failed to set focus task. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
            });
        });

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            // Handle Backspace key for navigation
            if (e.key === 'Backspace' && !e.target.matches('input, textarea, [contenteditable="true"]')) {
                // If we're on a sub-page, go back to the main dashboard
                const currentPath = window.location.pathname;
                if (currentPath !== '/momentum/dashboard' && currentPath !== '/momentum/' && currentPath !== '/momentum') {
                    e.preventDefault();
                    window.history.back();
                }
            }
        });

        // Add keyboard shortcuts for feature categories
        const featureShortcuts = {
            'a': '/momentum/adhd', // ADHD Management
            't': '/momentum/tasks', // Task Management
            'p': '/momentum/projects', // Project Management
            'f': '/momentum/finances', // Financial Management
            'o': '/momentum/productivity/focus-timer', // Productivity Tools (focus)
            'i': '/momentum/ideas', // Idea Management
            'c': '/momentum/tools/currency-converter', // Tools (currency)
            's': '/momentum/astrology', // Astrology
            'h': '/momentum/help' // Help & Resources
        };

        // Add keyboard shortcut listener
        document.addEventListener('keydown', function(e) {
            // Only trigger shortcuts when Alt key is pressed and not in an input field
            if (e.altKey && !e.ctrlKey && !e.metaKey && !e.target.matches('input, textarea, [contenteditable="true"]')) {
                const key = e.key.toLowerCase();
                if (featureShortcuts[key]) {
                    e.preventDefault();
                    window.location.href = featureShortcuts[key];
                }
            }
        });

        // Toggle keyboard shortcuts details
        const toggleShortcutsBtn = document.getElementById('toggle-shortcuts-details');
        const shortcutsDetails = document.getElementById('shortcuts-details');

        if (toggleShortcutsBtn && shortcutsDetails) {
            toggleShortcutsBtn.addEventListener('click', function() {
                const isHidden = shortcutsDetails.classList.contains('hidden');

                if (isHidden) {
                    shortcutsDetails.classList.remove('hidden');
                    toggleShortcutsBtn.textContent = 'Hide Details';
                } else {
                    shortcutsDetails.classList.add('hidden');
                    toggleShortcutsBtn.textContent = 'Show Details';
                }
            });
        }

        // Load today's time blocks widget
        const todayTimeBlocksWidget = document.getElementById('today-time-blocks-widget');
        if (todayTimeBlocksWidget) {
            fetch('/momentum/productivity/widgets/today-time-blocks')
                .then(response => response.text())
                .then(html => {
                    todayTimeBlocksWidget.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading time blocks widget:', error);
                    todayTimeBlocksWidget.innerHTML =
                        '<div class="text-center py-4">' +
                        '<p class="text-gray-500 dark:text-gray-400">Failed to load schedule</p>' +
                        '<a href="/momentum/productivity/time-blocking" class="mt-2 inline-flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">' +
                        '<i class="fas fa-calendar-alt mr-1"></i> Go to Time Blocking</a>' +
                        '</div>';
                });
        }

        // Load energy level widget
        const energyLevelWidget = document.getElementById('energy-level-widget');
        if (energyLevelWidget) {
            fetch('/momentum/productivity/widgets/energy-level')
                .then(response => response.text())
                .then(html => {
                    energyLevelWidget.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading energy level widget:', error);
                    energyLevelWidget.innerHTML =
                        '<div class="text-center py-4">' +
                        '<p class="text-gray-500 dark:text-gray-400">Failed to load energy level data</p>' +
                        '<a href="/momentum/productivity/energy-tracking" class="mt-2 inline-flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">' +
                        '<i class="fas fa-bolt mr-1"></i> Go to Energy Tracking</a>' +
                        '</div>';
                });
        }

        // Load task batch widget
        const taskBatchWidget = document.getElementById('task-batch-widget');
        if (taskBatchWidget) {
            fetch('/momentum/productivity/widgets/task-batch')
                .then(response => response.text())
                .then(html => {
                    taskBatchWidget.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading task batch widget:', error);
                    taskBatchWidget.innerHTML =
                        '<div class="text-center py-4">' +
                        '<p class="text-gray-500 dark:text-gray-400">Failed to load task batch data</p>' +
                        '<a href="/momentum/productivity/task-batching" class="mt-2 inline-flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">' +
                        '<i class="fas fa-layer-group mr-1"></i> Go to Task Batching</a>' +
                        '</div>';
                });
        }
    });
</script>