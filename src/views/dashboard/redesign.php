<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Dashboard Header with Simplified Controls -->
        <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    <?= date('l, F j, Y') ?> • <span id="view-mode-text" class="text-primary-600 dark:text-primary-400">ADHD Optimized View</span>
                </p>
            </div>

            <div class="flex flex-wrap items-center gap-3">
                <!-- Quick Action Buttons - Most Important Actions -->
                <a href="/momentum/tasks/create" class="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-1.5"></i> New Task
                </a>
                <a href="/momentum/ideas/create" class="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                    <i class="fas fa-lightbulb mr-1.5"></i> New Idea
                </a>

                <!-- Layout Selector (Fixed Version) -->
                <div class="relative inline-block text-left">
                    <button id="layout-selector-button" type="button" aria-expanded="false" aria-haspopup="true"
                        class="layout-button inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-columns mr-1.5"></i> Layout <i class="fas fa-chevron-down ml-1.5 text-xs"></i>
                    </button>
                </div>

                <!-- Layout Dropdown (Separate from button for better positioning) -->
                <div id="layout-dropdown" class="hidden fixed origin-top-right mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 divide-y divide-gray-100 dark:divide-gray-700 focus:outline-none z-50">
                    <div class="py-1">
                        <a href="#" data-view="adhd-optimized" class="view-option group flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 bg-primary-50 dark:bg-primary-900 border-l-4 border-primary-500">
                            <i class="fas fa-brain mr-3 text-primary-500"></i> ADHD Optimized
                        </a>
                        <a href="#" data-view="focus" class="view-option group flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-bullseye mr-3 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400"></i> Focus View
                        </a>
                        <a href="#" data-view="standard" class="view-option group flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-th-large mr-3 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400"></i> Standard View
                        </a>
                        <a href="#" data-view="custom" class="view-option group flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-sliders-h mr-3 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400"></i> Custom View
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Widget Controls (Only visible in Custom layout) -->
        <div id="widget-controls" class="hidden mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Widget Controls</h3>
            <div class="flex flex-wrap gap-2">
                <button class="px-2 py-1 text-xs bg-purple-500 text-white rounded">Save Layout</button>
                <button class="px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded">Reset Layout</button>
                <span class="text-xs text-gray-500 dark:text-gray-400 self-center ml-2">Drag widgets to rearrange</span>
            </div>
        </div>

        <!-- Main Dashboard Content -->
        <div id="dashboard-widgets" class="space-y-6" data-view-mode="adhd-optimized" data-arrangement="adhd-optimized">
            <!-- Section 1: Current Focus (Full Width) -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-lg rounded-lg border-l-4 border-primary-500" id="current-focus-widget" data-widget="current-focus-widget">
                <div class="px-5 py-5">
                    <div class="flex justify-between items-center mb-3">
                        <div class="flex items-center">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-bullseye text-primary-500 mr-2.5"></i> Current Focus
                            </h2>
                        </div>
                        <div class="flex space-x-2">
                            <button id="focus-mode-toggle" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-primary-500 transition-colors duration-200">
                                <i class="fas fa-expand mr-1.5"></i> Enter Focus Mode
                            </button>
                            <?php if ($currentFocusTask): ?>
                                <button id="clear-focus-task" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-primary-500 transition-colors duration-200">
                                    <i class="fas fa-times mr-1.5"></i> Clear Focus
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if ($currentFocusTask): ?>
                        <div id="current-focus-content" class="bg-primary-50 dark:bg-primary-900/30 rounded-lg border border-primary-100 dark:border-primary-800">
                            <div class="p-4">
                                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                    <div class="mb-4 md:mb-0 md:pr-4">
                                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2"><?= View::escape($currentFocusTask['title']) ?></h3>
                                        <?php if (!empty($currentFocusTask['description'])): ?>
                                            <p class="text-sm text-gray-600 dark:text-gray-300 mb-3"><?= nl2br(View::escape($currentFocusTask['description'])) ?></p>
                                        <?php endif; ?>

                                        <div class="flex flex-wrap items-center gap-2 mb-1">
                                            <!-- Task metadata badges -->
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                <?php if ($currentFocusTask['status'] === 'todo'): ?>
                                                    bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200
                                                <?php elseif ($currentFocusTask['status'] === 'in_progress'): ?>
                                                    bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                                <?php elseif ($currentFocusTask['status'] === 'done'): ?>
                                                    bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                                <?php endif; ?>
                                            ">
                                                <?php if ($currentFocusTask['status'] === 'todo'): ?>
                                                    <i class="fas fa-circle text-xs mr-1"></i> To Do
                                                <?php elseif ($currentFocusTask['status'] === 'in_progress'): ?>
                                                    <i class="fas fa-spinner text-xs mr-1"></i> In Progress
                                                <?php elseif ($currentFocusTask['status'] === 'done'): ?>
                                                    <i class="fas fa-check text-xs mr-1"></i> Done
                                                <?php endif; ?>
                                            </span>

                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                <?php if ($currentFocusTask['priority'] === 'low'): ?>
                                                    bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200
                                                <?php elseif ($currentFocusTask['priority'] === 'medium'): ?>
                                                    bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                                <?php elseif ($currentFocusTask['priority'] === 'high'): ?>
                                                    bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                                                <?php elseif ($currentFocusTask['priority'] === 'urgent'): ?>
                                                    bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                                <?php endif; ?>
                                            ">
                                                <?php if ($currentFocusTask['priority'] === 'urgent'): ?>
                                                    <i class="fas fa-exclamation-circle text-xs mr-1"></i>
                                                <?php elseif ($currentFocusTask['priority'] === 'high'): ?>
                                                    <i class="fas fa-arrow-up text-xs mr-1"></i>
                                                <?php endif; ?>
                                                <?= ucfirst($currentFocusTask['priority']) ?> Priority
                                            </span>

                                            <?php if (!empty($currentFocusTask['category_id']) && !empty($currentFocusTask['category_color'])): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" style="background-color: <?= $currentFocusTask['category_color'] ?>25; color: <?= $currentFocusTask['category_color'] ?>;">
                                                    <span class="w-1.5 h-1.5 rounded-full mr-1" style="background-color: <?= $currentFocusTask['category_color'] ?>;"></span>
                                                    <?= View::escape($currentFocusTask['category_name'] ?? 'Category') ?>
                                                </span>
                                            <?php endif; ?>

                                            <?php if ($currentFocusTask['due_date']): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                                    <i class="far fa-calendar text-xs mr-1"></i> Due: <?= View::formatDate($currentFocusTask['due_date']) ?>
                                                    <?php if ($currentFocusTask['due_time']): ?>
                                                        at <?= View::formatTime($currentFocusTask['due_time']) ?>
                                                    <?php endif; ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="flex space-x-3">
                                        <a href="/momentum/productivity/focus-timer?task_id=<?= $currentFocusTask['id'] ?>" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                            <i class="fas fa-clock mr-1.5"></i> Start Timer
                                        </a>

                                        <a href="/momentum/tasks/complete/<?= $currentFocusTask['id'] ?>" id="mark-complete-btn" data-task-id="<?= $currentFocusTask['id'] ?>" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                                            <i class="fas fa-check mr-1.5"></i> Complete
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div id="current-focus-empty" class="bg-gray-50 dark:bg-gray-700 p-5 rounded-lg text-center">
                            <div class="flex flex-col items-center justify-center py-4">
                                <i class="fas fa-bullseye text-primary-400 dark:text-primary-300 text-4xl mb-3"></i>
                                <p class="text-gray-600 dark:text-gray-300 mb-2 text-lg">No task currently in focus</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto mb-4">Select a task from "Today's Tasks" below to focus on it.</p>
                                <a href="/momentum/tasks" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-primary-500 transition-colors duration-200">
                                    <i class="fas fa-tasks mr-1.5"></i> View All Tasks
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Section 2: Task Management (2-column grid) -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Today's Tasks Widget -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="today-tasks" style="height: 400px; max-height: 400px; overflow: hidden; display: flex; flex-direction: column;">
                    <div class="px-5 py-5" style="display: flex; flex-direction: column; height: 100%; overflow: hidden;">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-calendar-day text-blue-500 mr-2"></i> Today's Tasks
                            </h2>
                            <a href="/momentum/tasks?due_date=<?= date('Y-m-d') ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                View all <i class="fas fa-chevron-right ml-1 text-xs"></i>
                            </a>
                        </div>

                        <!-- Today's tasks content -->
                        <div class="task-list-container" id="today-tasks-container" style="flex: 1; display: flex; flex-direction: column; overflow: hidden; position: relative;">
                            <?php if (empty($todayTasks)): ?>
                                <div class="task-list-empty">
                                    <p class="text-gray-500 dark:text-gray-400">No tasks for today</p>
                                    <a href="/momentum/tasks/create" class="mt-2 inline-flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                        <i class="fas fa-plus mr-1"></i> Add a task
                                    </a>
                                </div>
                            <?php else: ?>
                                <ul class="divide-y divide-gray-200 dark:divide-gray-700" id="today-tasks-list" style="max-height: 300px; overflow-y: auto; padding-right: 5px; margin-right: -5px;">
                                    <?php foreach ($todayTasks as $task): ?>
                                        <li class="py-3 task-item <?= $currentFocusTask && $currentFocusTask['id'] == $task['id'] ? 'bg-primary-50 dark:bg-primary-900 border-l-4 border-primary-500 pl-2' : '' ?>" data-task-id="<?= $task['id'] ?>">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center flex-1">
                                                    <input type="checkbox" data-task-id="<?= $task['id'] ?>" class="task-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 rounded" <?= $task['status'] === 'done' ? 'checked' : '' ?>>
                                                    <div class="ml-3 flex-1">
                                                        <a href="/momentum/tasks/view/<?= $task['id'] ?>" class="text-sm font-medium text-gray-900 dark:text-white <?= $task['status'] === 'done' ? 'line-through text-gray-500 dark:text-gray-400' : '' ?>">
                                                            <?= View::escape($task['title']) ?>
                                                        </a>
                                                        <div class="flex flex-wrap items-center mt-1">
                                                            <?php if ($task['due_time']): ?>
                                                                <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">
                                                                    <i class="far fa-clock"></i> <?= View::formatTime($task['due_time']) ?>
                                                                </span>
                                                            <?php endif; ?>
                                                            <?php if (!empty($task['estimated_time'])): ?>
                                                                <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">
                                                                    <i class="fas fa-hourglass-half"></i> <?= $task['estimated_time'] ?> min
                                                                </span>
                                                            <?php endif; ?>
                                                            <?php if (!empty($task['category_id']) && !empty($task['category_color'])): ?>
                                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium mr-2" style="background-color: <?= $task['category_color'] ?>25; color: <?= $task['category_color'] ?>;">
                                                                    <?= View::escape($task['category_name'] ?? 'Category') ?>
                                                                </span>
                                                            <?php endif; ?>
                                                            <?php if ($task['priority'] === 'high'): ?>
                                                                <span class="text-orange-500 dark:text-orange-400 text-xs" title="High Priority">
                                                                    <i class="fas fa-arrow-up"></i> High
                                                                </span>
                                                            <?php elseif ($task['priority'] === 'urgent'): ?>
                                                                <span class="text-red-500 dark:text-red-400 text-xs" title="Urgent">
                                                                    <i class="fas fa-exclamation-circle"></i> Urgent
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="ml-2 flex items-center">
                                                    <?php if ($currentFocusTask && $currentFocusTask['id'] == $task['id']): ?>
                                                        <span class="text-xs text-primary-600 dark:text-primary-400 font-medium mr-2">
                                                            <i class="fas fa-bullseye"></i> In Focus
                                                        </span>
                                                    <?php else: ?>
                                                        <a href="/momentum/tasks/set-focus/<?= $task['id'] ?>" class="inline-flex items-center px-2 py-1 border border-transparent rounded text-xs font-medium text-primary-700 dark:text-primary-300 bg-primary-100 dark:bg-primary-800 hover:bg-primary-200 dark:hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                                            <i class="fas fa-bullseye mr-1"></i> Focus
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>

                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Overdue Tasks Widget -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="overdue-tasks" style="height: 400px; max-height: 400px; overflow: hidden; display: flex; flex-direction: column;">
                    <div class="px-5 py-5" style="display: flex; flex-direction: column; height: 100%; overflow: hidden;">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-exclamation-circle text-red-500 mr-2"></i> Overdue Tasks
                            </h2>
                            <a href="/momentum/tasks?filter=overdue" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                View all <i class="fas fa-chevron-right ml-1 text-xs"></i>
                            </a>
                        </div>

                        <!-- Overdue tasks content -->
                        <div class="task-list-container" id="overdue-tasks-container" style="flex: 1; display: flex; flex-direction: column; overflow: hidden; position: relative;">
                            <?php if (empty($overdueTasks)): ?>
                                <div class="task-list-empty">
                                    <p class="text-gray-500 dark:text-gray-400">No overdue tasks</p>
                                </div>
                            <?php else: ?>
                                <ul class="divide-y divide-gray-200 dark:divide-gray-700" id="overdue-tasks-list" style="max-height: 300px; overflow-y: auto; padding-right: 5px; margin-right: -5px;">
                                    <?php foreach ($overdueTasks as $task): ?>
                                        <li class="py-3 task-item <?= $currentFocusTask && $currentFocusTask['id'] == $task['id'] ? 'bg-primary-50 dark:bg-primary-900 border-l-4 border-primary-500 pl-2' : '' ?>" data-task-id="<?= $task['id'] ?>">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center flex-1">
                                                    <input type="checkbox" data-task-id="<?= $task['id'] ?>" class="task-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 rounded" <?= $task['status'] === 'done' ? 'checked' : '' ?>>
                                                    <div class="ml-3 flex-1">
                                                        <a href="/momentum/tasks/view/<?= $task['id'] ?>" class="text-sm font-medium text-gray-900 dark:text-white <?= $task['status'] === 'done' ? 'line-through text-gray-500 dark:text-gray-400' : '' ?>">
                                                            <?= View::escape($task['title']) ?>
                                                        </a>
                                                        <div class="flex flex-wrap items-center mt-1">
                                                            <span class="text-xs text-red-500 dark:text-red-400 mr-2">
                                                                <i class="far fa-calendar"></i> Due: <?= View::formatDate($task['due_date']) ?>
                                                                <?php if ($task['due_time']): ?>
                                                                    at <?= View::formatTime($task['due_time']) ?>
                                                                <?php endif; ?>
                                                            </span>
                                                            <?php if (!empty($task['category_id']) && !empty($task['category_color'])): ?>
                                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium mr-2" style="background-color: <?= $task['category_color'] ?>25; color: <?= $task['category_color'] ?>;">
                                                                    <?= View::escape($task['category_name'] ?? 'Category') ?>
                                                                </span>
                                                            <?php endif; ?>
                                                            <?php if ($task['priority'] === 'high'): ?>
                                                                <span class="text-orange-500 dark:text-orange-400 text-xs" title="High Priority">
                                                                    <i class="fas fa-arrow-up"></i> High
                                                                </span>
                                                            <?php elseif ($task['priority'] === 'urgent'): ?>
                                                                <span class="text-red-500 dark:text-red-400 text-xs" title="Urgent">
                                                                    <i class="fas fa-exclamation-circle"></i> Urgent
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="ml-2 flex items-center">
                                                    <?php if ($currentFocusTask && $currentFocusTask['id'] == $task['id']): ?>
                                                        <span class="text-xs text-primary-600 dark:text-primary-400 font-medium mr-2">
                                                            <i class="fas fa-bullseye"></i> In Focus
                                                        </span>
                                                    <?php else: ?>
                                                        <a href="/momentum/tasks/set-focus/<?= $task['id'] ?>" class="inline-flex items-center px-2 py-1 border border-transparent rounded text-xs font-medium text-primary-700 dark:text-primary-300 bg-primary-100 dark:bg-primary-800 hover:bg-primary-200 dark:hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                                            <i class="fas fa-bullseye mr-1"></i> Focus
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>

                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 3: Support & Resources (3-column grid) -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Keyboard Shortcuts Guide -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="keyboard-shortcuts">
                    <div class="px-5 py-5">
                        <div class="flex justify-between items-center mb-3">
                            <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-keyboard text-purple-500 mr-2"></i> Keyboard Shortcuts
                            </h2>
                            <button id="toggle-shortcuts-details" class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                Show Details
                            </button>
                        </div>
                        <div class="grid grid-cols-2 gap-3 mb-3">
                            <div class="bg-gray-50 dark:bg-gray-700 p-2 rounded">
                                <p class="text-xs text-gray-700 dark:text-gray-300 font-medium">Navigation</p>
                                <p class="text-sm text-gray-900 dark:text-white">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">Backspace</kbd> Go Back
                                </p>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 p-2 rounded">
                                <p class="text-xs text-gray-700 dark:text-gray-300 font-medium">Feature Access</p>
                                <p class="text-sm text-gray-900 dark:text-white">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">Alt</kbd> + Letter
                                </p>
                            </div>
                        </div>
                        <div id="shortcuts-details" class="hidden bg-gray-50 dark:bg-gray-700 p-3 rounded">
                            <h4 class="text-xs font-medium text-gray-900 dark:text-white mb-2">Feature Shortcuts (Alt + Key)</h4>
                            <div class="grid grid-cols-2 gap-2 text-xs">
                                <div class="flex items-center">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded mr-1 text-xs">A</kbd>
                                    <span class="text-gray-700 dark:text-gray-300">ADHD Management</span>
                                </div>
                                <div class="flex items-center">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded mr-1 text-xs">T</kbd>
                                    <span class="text-gray-700 dark:text-gray-300">Task Management</span>
                                </div>
                                <div class="flex items-center">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded mr-1 text-xs">P</kbd>
                                    <span class="text-gray-700 dark:text-gray-300">Project Management</span>
                                </div>
                                <div class="flex items-center">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded mr-1 text-xs">F</kbd>
                                    <span class="text-gray-700 dark:text-gray-300">Financial Management</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ADHD Guide Widget -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="adhd-guide">
                    <div class="px-5 py-5">
                        <div class="flex justify-between items-center mb-3">
                            <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-brain text-green-500 mr-2"></i> ADHD Guide
                            </h2>
                            <a href="/momentum/adhd/guide" class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                View full guide
                            </a>
                        </div>

                        <div class="space-y-3">
                            <div class="bg-green-50 dark:bg-green-900/30 p-3 rounded-lg">
                                <h3 class="text-sm font-medium text-green-800 dark:text-green-300 mb-1">Today's Tip</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Break large tasks into smaller, manageable chunks to reduce overwhelm and make progress easier to track.</p>
                            </div>

                            <?php if ($adhdData && !empty($adhdData['hasLoggedToday'])): ?>
                                <div class="bg-primary-50 dark:bg-primary-900/30 p-3 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-primary-500 mr-2"></i>
                                        <p class="text-sm text-gray-600 dark:text-gray-300">You've logged your symptoms today!</p>
                                    </div>
                                </div>
                            <?php else: ?>
                                <a href="/momentum/adhd/log" class="block bg-gray-50 dark:bg-gray-700 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                    <div class="flex items-center">
                                        <i class="fas fa-clipboard-list text-gray-500 dark:text-gray-400 mr-2"></i>
                                        <p class="text-sm text-gray-600 dark:text-gray-300">Log your symptoms today</p>
                                    </div>
                                </a>
                            <?php endif; ?>

                            <?php if ($adhdData && !empty($adhdData['currentStreak'])): ?>
                                <div class="bg-orange-50 dark:bg-orange-900/30 p-3 rounded-lg">
                                    <h3 class="text-sm font-medium text-orange-800 dark:text-orange-300 mb-1">Current Streak</h3>
                                    <div class="flex items-center">
                                        <i class="fas fa-fire text-orange-500 mr-2"></i>
                                        <p class="text-sm text-gray-600 dark:text-gray-300"><?= $adhdData['currentStreak'] ?> days of consistent tracking</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Help Center Widget -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="help-center">
                    <div class="px-5 py-5">
                        <div class="flex justify-between items-center mb-3">
                            <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-question-circle text-amber-500 mr-2"></i> Help Center
                            </h2>
                            <a href="/momentum/help" class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                View all resources
                            </a>
                        </div>

                        <div class="grid grid-cols-1 gap-2">
                            <a href="/momentum/help/getting-started" class="block bg-gray-50 dark:bg-gray-700 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-1">Getting Started</h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Learn the basics of using Momentum</p>
                            </a>

                            <a href="/momentum/help/adhd-strategies" class="block bg-gray-50 dark:bg-gray-700 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-1">ADHD Strategies</h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Effective techniques for ADHD management</p>
                            </a>

                            <a href="/momentum/help/dashboard-redesign-plan" class="block bg-primary-50 dark:bg-primary-900/30 p-3 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-800/50 transition-colors">
                                <h3 class="text-sm font-medium text-primary-900 dark:text-primary-100 mb-1">Dashboard Redesign</h3>
                                <p class="text-xs text-primary-700 dark:text-primary-300">Learn about the new ADHD-friendly dashboard</p>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Task Management Scripts -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Task Management Scripts loaded');

        // Get elements
        const markCompleteBtn = document.getElementById('mark-complete-btn');
        const taskCheckboxes = document.querySelectorAll('.task-checkbox');
        const clearFocusBtn = document.getElementById('clear-focus-task');

        console.log('Mark complete button:', markCompleteBtn ? 'Found' : 'Not found');
        console.log('Task checkboxes:', taskCheckboxes.length);
        console.log('Clear focus button:', clearFocusBtn ? 'Found' : 'Not found');

        // Function to show success message
        function showSuccessMessage(message) {
            console.log('Showing success message:', message);

            // Create message element if it doesn't exist
            let messageElement = document.getElementById('success-message');

            if (!messageElement) {
                messageElement = document.createElement('div');
                messageElement.id = 'success-message';
                messageElement.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50 transform transition-all duration-300 opacity-0 translate-y-[-20px]';
                document.body.appendChild(messageElement);
            }

            // Set message text
            messageElement.textContent = message;

            // Show message with animation
            setTimeout(() => {
                messageElement.classList.remove('opacity-0', 'translate-y-[-20px]');
                messageElement.classList.add('opacity-100', 'translate-y-0');
            }, 10);

            // Hide message after delay
            setTimeout(() => {
                messageElement.classList.remove('opacity-100', 'translate-y-0');
                messageElement.classList.add('opacity-0', 'translate-y-[-20px]');
            }, 3000);
        }

        // Function to mark task as complete
        function markTaskAsComplete(taskId) {
            console.log('Marking task as complete with ID:', taskId);

            // Log the URL we're fetching
            const url = `/momentum/tasks/complete/${taskId}`;
            console.log('Fetching URL:', url);

            return fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Response received:', response);
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);

                if (!response.ok) {
                    throw new Error(`Server responded with status: ${response.status}`);
                }

                // Try to parse the response as JSON
                return response.text().then(text => {
                    console.log('Raw response text:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('Failed to parse response as JSON:', e);
                        // Return a default success response if the server didn't return valid JSON
                        return { success: true, message: 'Task marked as complete (non-JSON response)' };
                    }
                });
            })
            .catch(error => {
                console.error('Error in markTaskAsComplete:', error);
                throw error;
            });
        }

        // Function to clear focus task
        function clearFocusTask() {
            console.log('Clearing focus task');

            return fetch('/momentum/dashboard/clear-focus-task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json());
        }

        // Handle Mark Complete button in Current Focus widget
        if (markCompleteBtn) {
            console.log('Adding click handler to mark complete button');
            console.log('Button details:', {
                id: markCompleteBtn.id,
                taskId: markCompleteBtn.getAttribute('data-task-id'),
                text: markCompleteBtn.textContent.trim(),
                href: markCompleteBtn.getAttribute('href')
            });

            // Add visual feedback when clicked
            markCompleteBtn.addEventListener('click', function(e) {
                // Don't prevent default - let the link work naturally
                // This ensures the button works even if JavaScript fails

                // Just add visual feedback
                this.classList.add('opacity-75');
                this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1.5"></i> Processing...';

                console.log('Mark complete button clicked - allowing natural link navigation');
            });
        }

        // Handle task checkboxes
        taskCheckboxes.forEach((checkbox, index) => {
            console.log(`Adding change handler to task checkbox ${index + 1}`);

            checkbox.addEventListener('change', function() {
                const taskId = this.getAttribute('data-task-id');
                console.log('Task checkbox changed for task ID:', taskId);

                if (this.checked) {
                    markTaskAsComplete(taskId)
                    .then(data => {
                        if (data.success) {
                            // Update UI
                            const taskItem = this.closest('.task-item');
                            const taskTitle = taskItem.querySelector('a');
                            taskTitle.classList.add('line-through', 'text-gray-500', 'dark:text-gray-400');

                            // If this was the focus task, clear it
                            if (taskItem.classList.contains('bg-primary-50') || taskItem.classList.contains('dark:bg-primary-900')) {
                                clearFocusTask().then(() => {
                                    // Reload the page to refresh the focus task widget
                                    window.location.reload();
                                });
                            }

                            showSuccessMessage('Task marked as complete!');
                        } else {
                            // Uncheck the checkbox if there was an error
                            this.checked = false;
                            alert('Failed to mark task as complete. Please try again.');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        this.checked = false;
                        alert('An error occurred. Please try again.');
                    });
                }
            });
        });

        // Handle Clear Focus button
        if (clearFocusBtn) {
            console.log('Adding click handler to clear focus button');

            clearFocusBtn.addEventListener('click', function() {
                console.log('Clear focus button clicked');

                clearFocusTask()
                .then(data => {
                    if (data.success) {
                        showSuccessMessage('Focus task cleared successfully!');
                        // Reload the page to refresh the widgets
                        window.location.reload();
                    } else {
                        alert('Failed to clear focus task. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
            });
        }
    });
</script>

