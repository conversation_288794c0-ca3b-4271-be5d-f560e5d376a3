<?php
/**
 * AI Agents Army Widget
 *
 * This widget displays AI agents on the main dashboard
 */
?>
<div class="px-5 py-5">
        <div class="flex justify-between items-center mb-4">
            <div class="flex items-center">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-robot text-indigo-500 mr-2.5"></i> AI Agents Army
                </h2>
                <span class="ml-3 px-2 py-1 text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200 rounded-full">
                    Key Feature
                </span>
            </div>
            <div class="flex space-x-2">
                <a href="/momentum/ai-agents/create" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200 mr-2">
                    <i class="fas fa-plus mr-1.5"></i> New Agent
                </a>
                <a href="/momentum/ai-agents" class="text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 flex items-center mr-3">
                    <i class="fas fa-external-link-alt mr-1"></i> View All
                </a>
                <a href="/momentum/docs/index.php" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center">
                    <i class="fas fa-book mr-1"></i> Docs
                </a>
            </div>
        </div>

        <!-- Stats Overview -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <!-- Total Agents -->
            <div class="bg-gray-50 dark:bg-gray-750 rounded-lg p-3">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-indigo-100 dark:bg-indigo-900 rounded-md p-2">
                        <i class="fas fa-robot text-indigo-600 dark:text-indigo-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-xs font-medium text-gray-500 dark:text-gray-400">
                            Total Agents
                        </p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">
                            <?= $aiAgentData['agentStats']['total_agents'] ?? 0 ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Active Agents -->
            <div class="bg-gray-50 dark:bg-gray-750 rounded-lg p-3">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-2">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-xs font-medium text-gray-500 dark:text-gray-400">
                            Active
                        </p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">
                            <?= $aiAgentData['agentStats']['active_agents'] ?? 0 ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Intelligence -->
            <div class="bg-gray-50 dark:bg-gray-750 rounded-lg p-3">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-md p-2">
                        <i class="fas fa-brain text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-xs font-medium text-gray-500 dark:text-gray-400">
                            Avg Intelligence
                        </p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">
                            <?= number_format($aiAgentData['agentStats']['avg_intelligence'] ?? 0, 1) ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Efficiency -->
            <div class="bg-gray-50 dark:bg-gray-750 rounded-lg p-3">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-purple-100 dark:bg-purple-900 rounded-md p-2">
                        <i class="fas fa-tachometer-alt text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-xs font-medium text-gray-500 dark:text-gray-400">
                            Avg Efficiency
                        </p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">
                            <?= number_format($aiAgentData['agentStats']['avg_efficiency'] ?? 0, 1) ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Agents -->
        <div class="mb-4">
            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Active Agents
            </h3>
            <?php if (empty($aiAgentData['activeAgents'])): ?>
                <div class="text-center py-4 bg-gray-50 dark:bg-gray-750 rounded-lg">
                    <p class="text-sm text-gray-500 dark:text-gray-400">No active agents</p>
                    <a href="/momentum/ai-agents/create" class="mt-2 inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900 hover:bg-indigo-200 dark:hover:bg-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Create Agent
                    </a>
                </div>
            <?php else: ?>
                <div class="bg-gray-50 dark:bg-gray-750 rounded-lg overflow-hidden">
                    <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                        <?php foreach (array_slice($aiAgentData['activeAgents'], 0, 3) as $agent): ?>
                            <li class="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <a href="/momentum/ai-agents/view/<?= $agent['id'] ?>" class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center">
                                        <?php if ($agent['avatar']): ?>
                                            <img src="<?= htmlspecialchars($agent['avatar']) ?>" alt="<?= htmlspecialchars($agent['name']) ?>" class="w-8 h-8 rounded-full">
                                        <?php else: ?>
                                            <i class="fas fa-robot text-indigo-600 dark:text-indigo-400"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">
                                            <?= htmlspecialchars($agent['name']) ?>
                                        </p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">
                                            <?= $agent['category_name'] ? htmlspecialchars($agent['category_name']) : 'Uncategorized' ?>
                                        </p>
                                    </div>
                                    <div class="ml-auto">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                            Active
                                        </span>
                                    </div>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>

        <!-- Quick Actions -->
        <div>
            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Quick Actions
            </h3>
            <div class="grid grid-cols-3 gap-2">
                <a href="/momentum/ai-agents/create" class="flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-plus mr-2 text-indigo-500"></i> New Agent
                </a>
                <a href="/momentum/ai-agents/tasks/create" class="flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-tasks mr-2 text-indigo-500"></i> Assign Task
                </a>
                <a href="/momentum/youtube_agent_interface.php" class="flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fab fa-youtube mr-2 text-red-600"></i> YouTube
                </a>
            </div>
        </div>
    </div>
</div>
