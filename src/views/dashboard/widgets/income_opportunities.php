<div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <div>
            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Income Opportunities</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                Track your online income streams
            </p>
        </div>
        <a href="/momentum/income-opportunities" class="inline-flex items-center px-3 py-1 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
            <i class="fas fa-external-link-alt mr-1"></i> View All
        </a>
    </div>

    <?php if (empty($activeOpportunities)): ?>
        <div class="p-6 text-center">
            <p class="text-gray-500 dark:text-gray-400">No active income opportunities yet.</p>
            <a href="/momentum/income-opportunities/create" class="inline-flex items-center justify-center px-4 py-2 mt-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i> Add Opportunity
            </a>
        </div>
    <?php else: ?>
        <div class="p-4">
            <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="bg-primary-50 dark:bg-primary-900 p-3 rounded-lg">
                    <p class="text-xs text-primary-500 dark:text-primary-400 font-medium">Active Opportunities</p>
                    <p class="text-2xl font-bold text-primary-700 dark:text-primary-300"><?= $opportunitySummary['active_opportunities'] ?? 0 ?></p>
                </div>
                <div class="bg-green-50 dark:bg-green-900 p-3 rounded-lg">
                    <p class="text-xs text-green-500 dark:text-green-400 font-medium">Total Categories</p>
                    <p class="text-2xl font-bold text-green-700 dark:text-green-300"><?= $opportunitySummary['total_categories'] ?? 0 ?></p>
                </div>
            </div>

            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Active Opportunities</h4>
            <ul class="space-y-3">
                <?php foreach ($activeOpportunities as $opportunity): ?>
                    <li class="border-l-4
                        <?php if ($opportunity['income_type'] === 'active'): ?>
                            border-blue-500
                        <?php elseif ($opportunity['income_type'] === 'passive'): ?>
                            border-green-500
                        <?php else: ?>
                            border-purple-500
                        <?php endif; ?>
                        pl-3 py-2">
                        <a href="/momentum/income-opportunities/view/<?= $opportunity['id'] ?>" class="block hover:text-primary-600 dark:hover:text-primary-400">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($opportunity['name']) ?></h5>
                                    <p class="text-xs text-gray-500 dark:text-gray-400"><?= htmlspecialchars($opportunity['category']) ?></p>
                                </div>
                                <span class="px-2 py-1 text-xs rounded-full
                                    <?php if ($opportunity['income_type'] === 'active'): ?>
                                        bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200
                                    <?php elseif ($opportunity['income_type'] === 'passive'): ?>
                                        bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
                                    <?php else: ?>
                                        bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200
                                    <?php endif; ?>">
                                    <?= ucfirst(htmlspecialchars($opportunity['income_type'])) ?>
                                </span>
                            </div>

                            <?php
                            // Get income potential display
                            $incomeDisplay = 'Varies';
                            if (!empty($opportunity['estimated_income_min']) || !empty($opportunity['estimated_income_max'])) {
                                if (!empty($opportunity['estimated_income_min']) && !empty($opportunity['estimated_income_max'])) {
                                    $incomeDisplay = 'Rs ' . number_format($opportunity['estimated_income_min'], 0) . ' - ' . number_format($opportunity['estimated_income_max'], 0);
                                } elseif (!empty($opportunity['estimated_income_min'])) {
                                    $incomeDisplay = 'Rs ' . number_format($opportunity['estimated_income_min'], 0) . '+';
                                } else {
                                    $incomeDisplay = 'Up to Rs ' . number_format($opportunity['estimated_income_max'], 0);
                                }
                                $incomeDisplay .= '/' . $opportunity['income_frequency'];
                            }
                            ?>

                            <div class="flex justify-between mt-1 text-xs text-gray-500 dark:text-gray-400">
                                <span><?= $incomeDisplay ?></span>
                                <span class="
                                    <?php if ($opportunity['startup_cost'] === 'none'): ?>
                                        text-green-600 dark:text-green-400
                                    <?php elseif ($opportunity['startup_cost'] === 'low'): ?>
                                        text-blue-600 dark:text-blue-400
                                    <?php elseif ($opportunity['startup_cost'] === 'medium'): ?>
                                        text-yellow-600 dark:text-yellow-400
                                    <?php else: ?>
                                        text-red-600 dark:text-red-400
                                    <?php endif; ?>">
                                    <?= ucfirst(htmlspecialchars($opportunity['startup_cost'])) ?> cost
                                </span>
                            </div>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>

            <div class="mt-4 flex justify-center space-x-4">
                <a href="/momentum/income-opportunities/create" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                    <i class="fas fa-plus-circle mr-1"></i> Add New Opportunity
                </a>
                <a href="/momentum/income-evaluator" class="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300">
                    <i class="fas fa-balance-scale mr-1"></i> Compare & Evaluate
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>
