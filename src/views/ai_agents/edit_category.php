<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Category</h1>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Update the details of your AI agent category
                </p>
            </div>
            <div>
                <a href="/momentum/ai-agents/categories" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Categories
                </a>
            </div>
        </div>

        <!-- Edit Category Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                    Category Details
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                    Update the details for "<?= htmlspecialchars($category['name']) ?>"
                </p>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <form action="/momentum/ai-agents/categories/update/<?= $category['id'] ?>" method="POST" class="space-y-6">
                    <!-- Basic Information -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Category Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" required
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="e.g., Research Assistants" value="<?= htmlspecialchars($category['name']) ?>">
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Description
                        </label>
                        <textarea name="description" id="description" rows="3"
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="Describe the purpose of this category"><?= htmlspecialchars($category['description'] ?? '') ?></textarea>
                    </div>

                    <!-- Icon and Color -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Icon Selection -->
                        <div>
                            <label for="icon" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Icon
                            </label>
                            <div class="mt-1 relative">
                                <select name="icon" id="icon"
                                    class="block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    <?php foreach ($icons as $value => $label): ?>
                                        <option value="<?= $value ?>" <?= $value === $category['icon'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($label) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <i id="icon-preview" class="fas <?= htmlspecialchars($category['icon']) ?>"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Color Selection -->
                        <div>
                            <label for="color" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Color
                            </label>
                            <div class="mt-1 relative">
                                <select name="color" id="color"
                                    class="block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    <?php foreach ($colors as $value => $label): ?>
                                        <option value="<?= $value ?>" <?= $value === $category['color'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($label) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <div id="color-preview" class="w-4 h-4 rounded-full" style="background-color: <?= htmlspecialchars($category['color']) ?>;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Display Order -->
                    <div>
                        <label for="display_order" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Display Order
                        </label>
                        <input type="number" name="display_order" id="display_order" min="0" value="<?= intval($category['display_order'] ?? 0) ?>"
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="0">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Lower numbers will appear first in the list
                        </p>
                    </div>

                    <!-- Preview -->
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Preview</h4>
                        <div class="flex items-center">
                            <div id="preview-icon-container" class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full" style="background-color: <?= htmlspecialchars($category['color']) ?>;">
                                <i id="preview-icon" class="fas <?= htmlspecialchars($category['icon']) ?> text-white"></i>
                            </div>
                            <div class="ml-4">
                                <div id="preview-name" class="text-sm font-medium text-gray-900 dark:text-white">
                                    <?= htmlspecialchars($category['name']) ?>
                                </div>
                                <div id="preview-description" class="text-sm text-gray-500 dark:text-gray-400">
                                    <?= htmlspecialchars($category['description'] ?? 'No description') ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-between">
                        <a href="/momentum/ai-agents/categories/delete/<?= $category['id'] ?>" 
                           class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                           onclick="return confirm('Are you sure you want to delete this category? This action cannot be undone.');">
                            <i class="fas fa-trash-alt mr-2"></i> Delete Category
                        </a>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-save mr-2"></i> Update Category
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const nameInput = document.getElementById('name');
        const descriptionInput = document.getElementById('description');
        const iconSelect = document.getElementById('icon');
        const colorSelect = document.getElementById('color');
        const iconPreview = document.getElementById('icon-preview');
        const colorPreview = document.getElementById('color-preview');
        const previewName = document.getElementById('preview-name');
        const previewDescription = document.getElementById('preview-description');
        const previewIcon = document.getElementById('preview-icon');
        const previewIconContainer = document.getElementById('preview-icon-container');
        
        // Update preview when name changes
        nameInput.addEventListener('input', function() {
            previewName.textContent = this.value || 'Category Name';
        });
        
        // Update preview when description changes
        descriptionInput.addEventListener('input', function() {
            previewDescription.textContent = this.value || 'No description';
        });
        
        // Update preview when icon changes
        iconSelect.addEventListener('change', function() {
            const selectedIcon = this.value;
            iconPreview.className = 'fas ' + selectedIcon;
            previewIcon.className = 'fas ' + selectedIcon + ' text-white';
        });
        
        // Update preview when color changes
        colorSelect.addEventListener('change', function() {
            const selectedColor = this.value;
            colorPreview.style.backgroundColor = selectedColor;
            previewIconContainer.style.backgroundColor = selectedColor;
        });
    });
</script>
