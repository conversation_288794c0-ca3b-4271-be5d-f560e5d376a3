<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Agent Header -->
        <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
            <div>
                <div class="flex items-center">
                    <a href="/momentum/ai-agents" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300 mr-2">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><?= htmlspecialchars($agent['name']) ?></h1>
                    <span class="ml-3 px-2 py-1 text-xs font-medium <?= $agent['status'] === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' ?> rounded-full">
                        <?= ucfirst($agent['status']) ?>
                    </span>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    <?= $agent['category_name'] ? htmlspecialchars($agent['category_name']) : 'Uncategorized' ?>
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="/momentum/ai-agents/edit/<?= $agent['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-edit mr-2"></i> Edit Agent
                </a>
                <a href="/momentum/ai-agents/tasks/create?agent_id=<?= $agent['id'] ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-tasks mr-2"></i> Assign Task
                </a>
            </div>
        </div>

        <!-- Agent Details -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Column: Agent Details and Skills -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Agent Details Card -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Agent Details
                        </h3>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</h4>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                <?= nl2br(htmlspecialchars($agent['description'])) ?>
                            </p>
                        </div>

                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Capabilities</h4>
                            <ul class="mt-1 text-sm text-gray-900 dark:text-white list-disc list-inside">
                                <?php foreach (explode("\n", $agent['capabilities']) as $capability): ?>
                                    <?php if (trim($capability)): ?>
                                        <li><?= htmlspecialchars(trim($capability)) ?></li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ul>
                        </div>

                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Personality Traits</h4>
                            <ul class="mt-1 text-sm text-gray-900 dark:text-white list-disc list-inside">
                                <?php foreach (explode("\n", $agent['personality_traits']) as $trait): ?>
                                    <?php if (trim($trait)): ?>
                                        <li><?= htmlspecialchars(trim($trait)) ?></li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ul>
                        </div>

                        <div class="grid grid-cols-3 gap-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Intelligence</h4>
                                <div class="mt-1 flex items-center">
                                    <span class="text-lg font-semibold text-gray-900 dark:text-white"><?= $agent['intelligence_level'] ?></span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400 ml-1">/10</span>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Efficiency</h4>
                                <div class="mt-1 flex items-center">
                                    <span class="text-lg font-semibold text-gray-900 dark:text-white"><?= number_format($agent['efficiency_rating'], 1) ?></span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400 ml-1">/10</span>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Reliability</h4>
                                <div class="mt-1 flex items-center">
                                    <span class="text-lg font-semibold text-gray-900 dark:text-white"><?= number_format($agent['reliability_score'], 1) ?></span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400 ml-1">/10</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Agent Skills Card -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Skills
                        </h3>
                        <a href="/momentum/ai-agents/edit/<?= $agent['id'] ?>" class="text-sm text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                            <i class="fas fa-edit"></i> Edit Skills
                        </a>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <?php if (empty($skills)): ?>
                            <p class="text-sm text-gray-500 dark:text-gray-400">No skills assigned to this agent.</p>
                        <?php else: ?>
                            <ul class="space-y-3">
                                <?php foreach ($skills as $skill): ?>
                                    <li class="flex items-center justify-between">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($skill['name']) ?></h4>
                                            <p class="text-xs text-gray-500 dark:text-gray-400"><?= htmlspecialchars($skill['description']) ?></p>
                                        </div>
                                        <div class="flex items-center">
                                            <span class="text-sm font-medium text-gray-900 dark:text-white"><?= $skill['proficiency_level'] ?></span>
                                            <span class="text-xs text-gray-500 dark:text-gray-400 ml-1">/10</span>
                                        </div>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Right Column: Tasks and Interactions -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Agent Tasks Card -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Tasks
                        </h3>
                        <a href="/momentum/ai-agents/tasks/create?agent_id=<?= $agent['id'] ?>" class="text-sm text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                            <i class="fas fa-plus"></i> New Task
                        </a>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <?php if (empty($tasks)): ?>
                            <p class="text-sm text-gray-500 dark:text-gray-400">No tasks assigned to this agent.</p>
                        <?php else: ?>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead>
                                        <tr>
                                            <th class="px-3 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Title</th>
                                            <th class="px-3 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                            <th class="px-3 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Priority</th>
                                            <th class="px-3 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Due Date</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        <?php foreach ($tasks as $task): ?>
                                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                                <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                                    <a href="/momentum/ai-agents/tasks/view/<?= $task['id'] ?>" class="hover:text-indigo-600 dark:hover:text-indigo-400">
                                                        <?= htmlspecialchars($task['title']) ?>
                                                    </a>
                                                </td>
                                                <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                                        <?php
                                                            switch ($task['status']) {
                                                                case 'completed': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'; break;
                                                                case 'in_progress': echo 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'; break;
                                                                case 'failed': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'; break;
                                                                default: echo 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
                                                            }
                                                        ?>">
                                                        <?= ucfirst(str_replace('_', ' ', $task['status'])) ?>
                                                    </span>
                                                </td>
                                                <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                                        <?php
                                                            switch ($task['priority']) {
                                                                case 'urgent': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'; break;
                                                                case 'high': echo 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'; break;
                                                                case 'medium': echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'; break;
                                                                default: echo 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
                                                            }
                                                        ?>">
                                                        <?= ucfirst($task['priority']) ?>
                                                    </span>
                                                </td>
                                                <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                    <?= date('M j, Y', strtotime($task['due_date'])) ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Agent Interactions Card -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Recent Interactions
                        </h3>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <?php if (empty($interactions)): ?>
                            <p class="text-sm text-gray-500 dark:text-gray-400">No interactions recorded for this agent.</p>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($interactions as $interaction): ?>
                                    <div class="border-l-4
                                        <?php
                                            switch ($interaction['interaction_type']) {
                                                case 'command': echo 'border-blue-500'; break;
                                                case 'query': echo 'border-green-500'; break;
                                                case 'feedback': echo 'border-yellow-500'; break;
                                                case 'system': echo 'border-purple-500'; break;
                                                default: echo 'border-gray-500';
                                            }
                                        ?> pl-4 py-2">
                                        <div class="flex justify-between items-start">
                                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                                <?= ucfirst($interaction['interaction_type']) ?>
                                            </span>
                                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                                <?= date('M j, Y g:i A', strtotime($interaction['created_at'])) ?>
                                            </span>
                                        </div>
                                        <div class="mt-1 text-sm text-gray-900 dark:text-white whitespace-pre-wrap">
                                            <?= htmlspecialchars($interaction['content']) ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
