<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Task Header -->
        <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
            <div>
                <div class="flex items-center">
                    <a href="/momentum/ai-agents/view/<?= $task['agent_id'] ?>" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300 mr-2">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><?= htmlspecialchars($task['title']) ?></h1>
                    <span class="ml-3 px-2 py-1 text-xs font-medium 
                        <?php
                            switch ($task['status']) {
                                case 'completed': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'; break;
                                case 'in_progress': echo 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'; break;
                                case 'failed': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'; break;
                                default: echo 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
                            }
                        ?> rounded-full">
                        <?= ucfirst(str_replace('_', ' ', $task['status'])) ?>
                    </span>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Assigned to <a href="/momentum/ai-agents/view/<?= $agent['id'] ?>" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300"><?= htmlspecialchars($agent['name']) ?></a>
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="/momentum/ai-agents/tasks/edit/<?= $task['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-edit mr-2"></i> Edit Task
                </a>
                <?php if ($task['status'] === 'pending'): ?>
                    <form action="/momentum/ai-agents/tasks/start/<?= $task['id'] ?>" method="post" class="inline">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-play mr-2"></i> Start Task
                        </button>
                    </form>
                <?php elseif ($task['status'] === 'in_progress'): ?>
                    <form action="/momentum/ai-agents/tasks/complete/<?= $task['id'] ?>" method="post" class="inline">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <i class="fas fa-check mr-2"></i> Complete Task
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>

        <!-- Task Details -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Column: Task Details -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Task Details Card -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Task Details
                        </h3>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</h4>
                            <div class="mt-2 text-sm text-gray-900 dark:text-white whitespace-pre-wrap">
                                <?= nl2br(htmlspecialchars($task['description'])) ?>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Priority</h4>
                                <div class="mt-2">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        <?php
                                            switch ($task['priority']) {
                                                case 'urgent': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'; break;
                                                case 'high': echo 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'; break;
                                                case 'medium': echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'; break;
                                                default: echo 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
                                            }
                                        ?>">
                                        <?= ucfirst($task['priority']) ?>
                                    </span>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Due Date</h4>
                                <div class="mt-2 text-sm text-gray-900 dark:text-white">
                                    <?= date('M j, Y', strtotime($task['due_date'])) ?>
                                    <?= strtotime($task['due_date']) < time() && $task['status'] !== 'completed' ? 
                                        '<span class="text-red-600 dark:text-red-400 ml-2">(Overdue)</span>' : '' ?>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</h4>
                                <div class="mt-2 text-sm text-gray-900 dark:text-white">
                                    <?= date('M j, Y', strtotime($task['created_at'])) ?>
                                </div>
                            </div>
                        </div>

                        <?php if (!empty($task['completion_notes'])): ?>
                            <div class="mb-6">
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Completion Notes</h4>
                                <div class="mt-2 text-sm text-gray-900 dark:text-white whitespace-pre-wrap">
                                    <?= nl2br(htmlspecialchars($task['completion_notes'])) ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Task Interactions Card -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Task Interactions
                        </h3>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <?php if (empty($interactions)): ?>
                            <p class="text-sm text-gray-500 dark:text-gray-400">No interactions recorded for this task.</p>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($interactions as $interaction): ?>
                                    <div class="border-l-4
                                        <?php
                                            switch ($interaction['interaction_type']) {
                                                case 'command': echo 'border-blue-500'; break;
                                                case 'query': echo 'border-green-500'; break;
                                                case 'feedback': echo 'border-yellow-500'; break;
                                                case 'system': echo 'border-purple-500'; break;
                                                default: echo 'border-gray-500';
                                            }
                                        ?> pl-4 py-2">
                                        <div class="flex justify-between items-start">
                                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                                <?= ucfirst($interaction['interaction_type']) ?>
                                            </span>
                                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                                <?= date('M j, Y g:i A', strtotime($interaction['created_at'])) ?>
                                            </span>
                                        </div>
                                        <div class="mt-1 text-sm text-gray-900 dark:text-white whitespace-pre-wrap">
                                            <?= htmlspecialchars($interaction['content']) ?>
                                        </div>
                                        <?php if ($interaction['success'] !== null): ?>
                                            <div class="mt-1">
                                                <span class="text-xs <?= $interaction['success'] ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                                                    <?= $interaction['success'] ? 'Success' : 'Failed' ?>
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Right Column: Agent Details -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Agent Details Card -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Agent Details
                        </h3>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 rounded-full mr-2 <?= $agent['status'] === 'active' ? 'bg-green-500' : 'bg-gray-500' ?>"></div>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($agent['name']) ?></h4>
                        </div>
                        
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4"><?= htmlspecialchars($agent['description']) ?></p>
                        
                        <div class="grid grid-cols-3 gap-4 mb-4">
                            <div>
                                <h5 class="text-xs font-medium text-gray-500 dark:text-gray-400">Intelligence</h5>
                                <div class="mt-1 flex items-center">
                                    <span class="text-sm font-semibold text-gray-900 dark:text-white"><?= $agent['intelligence_level'] ?></span>
                                    <span class="text-xs text-gray-500 dark:text-gray-400 ml-1">/10</span>
                                </div>
                            </div>
                            <div>
                                <h5 class="text-xs font-medium text-gray-500 dark:text-gray-400">Efficiency</h5>
                                <div class="mt-1 flex items-center">
                                    <span class="text-sm font-semibold text-gray-900 dark:text-white"><?= number_format($agent['efficiency_rating'], 1) ?></span>
                                    <span class="text-xs text-gray-500 dark:text-gray-400 ml-1">/10</span>
                                </div>
                            </div>
                            <div>
                                <h5 class="text-xs font-medium text-gray-500 dark:text-gray-400">Reliability</h5>
                                <div class="mt-1 flex items-center">
                                    <span class="text-sm font-semibold text-gray-900 dark:text-white"><?= number_format($agent['reliability_score'], 1) ?></span>
                                    <span class="text-xs text-gray-500 dark:text-gray-400 ml-1">/10</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h5 class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">Top Skills</h5>
                            <?php if (empty($agentSkills)): ?>
                                <p class="text-xs text-gray-500 dark:text-gray-400">No skills assigned.</p>
                            <?php else: ?>
                                <ul class="space-y-1">
                                    <?php 
                                    // Sort skills by proficiency level
                                    usort($agentSkills, function($a, $b) {
                                        return $b['proficiency_level'] - $a['proficiency_level'];
                                    });
                                    
                                    // Display top 5 skills
                                    $topSkills = array_slice($agentSkills, 0, 5);
                                    foreach ($topSkills as $skill): 
                                    ?>
                                        <li class="flex items-center justify-between text-xs">
                                            <span class="text-gray-700 dark:text-gray-300"><?= htmlspecialchars($skill['name']) ?></span>
                                            <span class="text-gray-500 dark:text-gray-400"><?= $skill['proficiency_level'] ?>/10</span>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php endif; ?>
                        </div>
                        
                        <a href="/momentum/ai-agents/view/<?= $agent['id'] ?>" class="text-sm text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                            <i class="fas fa-external-link-alt mr-1"></i> View Agent Details
                        </a>
                    </div>
                </div>

                <!-- Task Status Card -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Task Status
                        </h3>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <div class="space-y-4">
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400">Progress</span>
                                    <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
                                        <?php
                                            switch ($task['status']) {
                                                case 'pending': echo '0%'; break;
                                                case 'in_progress': echo '50%'; break;
                                                case 'completed': echo '100%'; break;
                                                case 'failed': echo 'Failed'; break;
                                                default: echo 'Unknown';
                                            }
                                        ?>
                                    </span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="h-2 rounded-full 
                                        <?php
                                            switch ($task['status']) {
                                                case 'pending': echo 'w-0 bg-indigo-600'; break;
                                                case 'in_progress': echo 'w-1/2 bg-indigo-600'; break;
                                                case 'completed': echo 'w-full bg-green-600'; break;
                                                case 'failed': echo 'w-full bg-red-600'; break;
                                                default: echo 'w-0';
                                            }
                                        ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <h5 class="text-xs font-medium text-gray-500 dark:text-gray-400">Status</h5>
                                    <div class="mt-1 text-sm font-medium text-gray-900 dark:text-white">
                                        <?= ucfirst(str_replace('_', ' ', $task['status'])) ?>
                                    </div>
                                </div>
                                <div>
                                    <h5 class="text-xs font-medium text-gray-500 dark:text-gray-400">Last Updated</h5>
                                    <div class="mt-1 text-sm font-medium text-gray-900 dark:text-white">
                                        <?= date('M j, Y', strtotime($task['updated_at'])) ?>
                                    </div>
                                </div>
                            </div>

                            <?php if ($task['status'] === 'pending' || $task['status'] === 'in_progress'): ?>
                                <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
                                    <form action="/momentum/ai-agents/tasks/update-status/<?= $task['id'] ?>" method="post">
                                        <div class="mb-3">
                                            <label for="new_status" class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Update Status</label>
                                            <select name="status" id="new_status" required
                                                class="block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-1.5 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-xs dark:bg-gray-700 dark:text-white">
                                                <option value="pending" <?= $task['status'] === 'pending' ? 'selected' : '' ?>>Pending</option>
                                                <option value="in_progress" <?= $task['status'] === 'in_progress' ? 'selected' : '' ?>>In Progress</option>
                                                <option value="completed">Completed</option>
                                                <option value="failed">Failed</option>
                                            </select>
                                        </div>
                                        <button type="submit" class="w-full inline-flex justify-center items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                            Update Status
                                        </button>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
