<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex items-center mb-6">
            <a href="/momentum/ai-agents/view/<?= $agent['id'] ?>" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Assign Task to <?= htmlspecialchars($agent['name']) ?></h1>
        </div>

        <!-- Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <?php if (isset($error)): ?>
                    <div class="mb-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 dark:bg-red-900 dark:text-red-200" role="alert">
                        <p><?= $error ?></p>
                    </div>
                <?php endif; ?>

                <?php if (isset($success)): ?>
                    <div class="mb-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 dark:bg-green-900 dark:text-green-200" role="alert">
                        <p><?= $success ?></p>
                    </div>
                <?php endif; ?>

                <form action="/momentum/ai-agents/tasks/create" method="post">
                    <input type="hidden" name="agent_id" value="<?= $agent['id'] ?>">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Left Column -->
                        <div class="space-y-6">
                            <!-- Basic Information -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Task Information</h3>
                                
                                <div class="mb-4">
                                    <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Title</label>
                                    <input type="text" name="title" id="title" required
                                        class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                </div>

                                <div class="mb-4">
                                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                                    <textarea name="description" id="description" rows="4" required
                                        class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-white"></textarea>
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Provide detailed instructions for the agent to complete this task.</p>
                                </div>

                                <div class="mb-4">
                                    <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Priority</label>
                                    <select name="priority" id="priority" required
                                        class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                        <option value="low">Low</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="high">High</option>
                                        <option value="urgent">Urgent</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="space-y-6">
                            <!-- Schedule -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Task Schedule</h3>
                                
                                <div class="mb-4">
                                    <label for="due_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Due Date</label>
                                    <input type="date" name="due_date" id="due_date" required
                                        value="<?= date('Y-m-d', strtotime('+1 day')) ?>"
                                        class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                </div>

                                <div class="mb-4">
                                    <label for="due_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Due Time (optional)</label>
                                    <input type="time" name="due_time" id="due_time"
                                        class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                </div>

                                <div class="mb-4">
                                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                                    <select name="status" id="status" required
                                        class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                        <option value="pending" selected>Pending</option>
                                        <option value="in_progress">In Progress</option>
                                        <option value="completed">Completed</option>
                                        <option value="failed">Failed</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Agent Information -->
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Agent Information</h4>
                                <div class="flex items-center mb-2">
                                    <div class="w-3 h-3 rounded-full mr-2 <?= $agent['status'] === 'active' ? 'bg-green-500' : 'bg-gray-500' ?>"></div>
                                    <p class="text-sm text-gray-700 dark:text-gray-300"><?= htmlspecialchars($agent['name']) ?></p>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mb-2"><?= htmlspecialchars($agent['description']) ?></p>
                                
                                <div class="grid grid-cols-3 gap-2 text-xs">
                                    <div>
                                        <span class="text-gray-500 dark:text-gray-400">Intelligence:</span>
                                        <span class="text-gray-700 dark:text-gray-300 font-medium"><?= $agent['intelligence_level'] ?>/10</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500 dark:text-gray-400">Efficiency:</span>
                                        <span class="text-gray-700 dark:text-gray-300 font-medium"><?= number_format($agent['efficiency_rating'], 1) ?>/10</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500 dark:text-gray-400">Reliability:</span>
                                        <span class="text-gray-700 dark:text-gray-300 font-medium"><?= number_format($agent['reliability_score'], 1) ?>/10</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end space-x-3">
                        <a href="/momentum/ai-agents/view/<?= $agent['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Cancel
                        </a>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Assign Task
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
