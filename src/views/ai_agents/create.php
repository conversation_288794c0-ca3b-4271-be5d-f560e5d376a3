<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- <PERSON> Header -->
        <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Create New AI Agent</h1>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Add a new intelligent agent to your AI Agents Army
                </p>
            </div>
            <div>
                <a href="/momentum/ai-agents" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Agents
                </a>
            </div>
        </div>

        <!-- Create Agent Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                    Agent Details
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                    Configure your new AI agent's capabilities and personality
                </p>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <form action="/momentum/ai-agents/store" method="POST" class="space-y-6">
                    <!-- Basic Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Agent Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="name" id="name" required
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                placeholder="e.g., Research Assistant">
                        </div>
                        <div>
                            <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Category
                            </label>
                            <select name="category_id" id="category_id"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                <option value="">-- Select Category --</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Description
                        </label>
                        <textarea name="description" id="description" rows="3"
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="Describe what this agent does and its purpose"></textarea>
                    </div>

                    <!-- Capabilities -->
                    <div>
                        <label for="capabilities" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Capabilities
                        </label>
                        <textarea name="capabilities" id="capabilities" rows="3"
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="List the agent's capabilities (e.g., Web research, Data analysis, Report generation)"></textarea>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Separate different capabilities with commas or line breaks
                        </p>
                    </div>

                    <!-- Personality Traits -->
                    <div>
                        <label for="personality_traits" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Personality Traits
                        </label>
                        <textarea name="personality_traits" id="personality_traits" rows="2"
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="Describe the agent's personality (e.g., Analytical, Detail-oriented, Friendly)"></textarea>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Separate different traits with commas or line breaks
                        </p>
                    </div>

                    <!-- Agent Ratings -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="intelligence_level" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Intelligence Level (1-10)
                            </label>
                            <div class="mt-1 flex items-center">
                                <input type="range" name="intelligence_level" id="intelligence_level" min="1" max="10" value="5"
                                    class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                <span id="intelligence_level_display" class="ml-2 text-sm text-gray-700 dark:text-gray-300 w-6">5</span>
                            </div>
                        </div>
                        <div>
                            <label for="efficiency_rating" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Efficiency Rating (1-10)
                            </label>
                            <div class="mt-1 flex items-center">
                                <input type="range" name="efficiency_rating" id="efficiency_rating" min="1" max="10" step="0.1" value="5.0"
                                    class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                <span id="efficiency_rating_display" class="ml-2 text-sm text-gray-700 dark:text-gray-300 w-6">5.0</span>
                            </div>
                        </div>
                        <div>
                            <label for="reliability_score" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Reliability Score (1-10)
                            </label>
                            <div class="mt-1 flex items-center">
                                <input type="range" name="reliability_score" id="reliability_score" min="1" max="10" step="0.1" value="5.0"
                                    class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                <span id="reliability_score_display" class="ml-2 text-sm text-gray-700 dark:text-gray-300 w-6">5.0</span>
                            </div>
                        </div>
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Status
                        </label>
                        <select name="status" id="status"
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="training">Training</option>
                        </select>
                    </div>

                    <!-- Skills Section -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Agent Skills</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <?php foreach ($skills as $skill): ?>
                                <div class="border border-gray-200 dark:border-gray-700 rounded-md p-4">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                                                <?= htmlspecialchars($skill['name']) ?>
                                            </h4>
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                <?= htmlspecialchars($skill['description']) ?>
                                            </p>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200 mt-2">
                                                <?= ucfirst(htmlspecialchars($skill['skill_type'])) ?>
                                            </span>
                                        </div>
                                        <div class="ml-4">
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input type="checkbox" class="sr-only peer skill-toggle" data-skill-id="<?= $skill['id'] ?>">
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3 skill-proficiency-container hidden">
                                        <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Proficiency (1-10)
                                        </label>
                                        <div class="flex items-center">
                                            <input type="range" name="skills[<?= $skill['id'] ?>]" min="1" max="10" value="5" 
                                                class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer skill-proficiency" disabled>
                                            <span class="ml-2 text-xs text-gray-700 dark:text-gray-300 w-4 proficiency-display">5</span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-robot mr-2"></i> Create Agent
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Intelligence level slider
        const intelligenceSlider = document.getElementById('intelligence_level');
        const intelligenceDisplay = document.getElementById('intelligence_level_display');
        
        intelligenceSlider.addEventListener('input', function() {
            intelligenceDisplay.textContent = this.value;
        });
        
        // Efficiency rating slider
        const efficiencySlider = document.getElementById('efficiency_rating');
        const efficiencyDisplay = document.getElementById('efficiency_rating_display');
        
        efficiencySlider.addEventListener('input', function() {
            efficiencyDisplay.textContent = parseFloat(this.value).toFixed(1);
        });
        
        // Reliability score slider
        const reliabilitySlider = document.getElementById('reliability_score');
        const reliabilityDisplay = document.getElementById('reliability_score_display');
        
        reliabilitySlider.addEventListener('input', function() {
            reliabilityDisplay.textContent = parseFloat(this.value).toFixed(1);
        });
        
        // Skill toggles
        const skillToggles = document.querySelectorAll('.skill-toggle');
        
        skillToggles.forEach(toggle => {
            toggle.addEventListener('change', function() {
                const container = this.closest('.border').querySelector('.skill-proficiency-container');
                const proficiencySlider = container.querySelector('.skill-proficiency');
                
                if (this.checked) {
                    container.classList.remove('hidden');
                    proficiencySlider.disabled = false;
                } else {
                    container.classList.add('hidden');
                    proficiencySlider.disabled = true;
                }
            });
        });
        
        // Proficiency sliders
        const proficiencySliders = document.querySelectorAll('.skill-proficiency');
        
        proficiencySliders.forEach(slider => {
            slider.addEventListener('input', function() {
                const display = this.parentNode.querySelector('.proficiency-display');
                display.textContent = this.value;
            });
        });
    });
</script>
