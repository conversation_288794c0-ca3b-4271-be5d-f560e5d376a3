<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Dashboard Header -->
        <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">AI Agents Army</h1>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Manage your intelligent agents and automate tasks
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="/momentum/ai-agents/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-plus mr-2"></i> New Agent
                </a>
                <a href="/momentum/ai-agents/categories" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-folder mr-2"></i> Categories
                </a>
                <a href="/momentum/youtube_agent_interface.php" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fab fa-youtube mr-2 text-red-600"></i> YouTube Browser
                </a>
                <a href="/momentum/aegis-director-interface.php" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-shield-alt mr-2 text-indigo-600"></i> Aegis Director
                </a>
                <a href="/momentum/docs/index.php" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-book mr-2 text-blue-600"></i> Documentation
                </a>
            </div>
        </div>

        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <!-- Total Agents -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-indigo-500 rounded-md p-3">
                            <i class="fas fa-robot text-white text-xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    Total Agents
                                </dt>
                                <dd>
                                    <div class="text-lg font-medium text-gray-900 dark:text-white">
                                        <?php
                                        // Debug output
                                        error_log("Dashboard view - total_agents: " . ($agentStats['total_agents'] ?? 'null'));

                                        // Direct database query to count agents
                                        $db = Database::getInstance();
                                        $userId = Session::getUser()['id'];
                                        $countSql = "SELECT COUNT(*) as count FROM ai_agents WHERE user_id = ?";
                                        $countResult = $db->fetchOne($countSql, [$userId]);
                                        $directCount = $countResult ? $countResult['count'] : 0;

                                        error_log("Direct agent count: " . $directCount);

                                        // Display the direct count instead of the stats value
                                        echo $directCount;
                                        ?>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Agents -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
                            <i class="fas fa-check-circle text-white text-xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    Active Agents
                                </dt>
                                <dd>
                                    <div class="text-lg font-medium text-gray-900 dark:text-white">
                                        <?php
                                        // Direct database query to count active agents
                                        $db = Database::getInstance();
                                        $userId = Session::getUser()['id'];
                                        $countSql = "SELECT COUNT(*) as count FROM ai_agents WHERE user_id = ? AND status = 'active'";
                                        $countResult = $db->fetchOne($countSql, [$userId]);
                                        $directCount = $countResult ? $countResult['count'] : 0;

                                        error_log("Direct active agent count: " . $directCount);

                                        // Display the direct count instead of the stats value
                                        echo $directCount;
                                        ?>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Tasks -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-yellow-500 rounded-md p-3">
                            <i class="fas fa-tasks text-white text-xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    Pending Tasks
                                </dt>
                                <dd>
                                    <div class="text-lg font-medium text-gray-900 dark:text-white">
                                        <?php
                                        // Direct database query to count pending tasks
                                        $db = Database::getInstance();
                                        $userId = Session::getUser()['id'];
                                        $countSql = "SELECT COUNT(*) as count FROM ai_agent_tasks WHERE user_id = ? AND status = 'pending'";
                                        $countResult = $db->fetchOne($countSql, [$userId]);
                                        $directCount = $countResult ? $countResult['count'] : 0;

                                        error_log("Direct pending tasks count: " . $directCount);

                                        // Display the direct count instead of the stats value
                                        echo $directCount;
                                        ?>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Completed Tasks -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
                            <i class="fas fa-clipboard-check text-white text-xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    Completed Tasks
                                </dt>
                                <dd>
                                    <div class="text-lg font-medium text-gray-900 dark:text-white">
                                        <?php
                                        // Direct database query to count completed tasks
                                        $db = Database::getInstance();
                                        $userId = Session::getUser()['id'];
                                        $countSql = "SELECT COUNT(*) as count FROM ai_agent_tasks WHERE user_id = ? AND status = 'completed'";
                                        $countResult = $db->fetchOne($countSql, [$userId]);
                                        $directCount = $countResult ? $countResult['count'] : 0;

                                        error_log("Direct completed tasks count: " . $directCount);

                                        // Display the direct count instead of the stats value
                                        echo $directCount;
                                        ?>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Column: Agent Categories -->
            <div class="lg:col-span-2 space-y-6">
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Agent Categories
                        </h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                            Your AI agents organized by category
                        </p>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <?php if (empty($categories)): ?>
                            <div class="text-center py-8">
                                <i class="fas fa-robot text-gray-400 text-5xl mb-4"></i>
                                <p class="text-gray-500 dark:text-gray-400">No agent categories found</p>
                                <a href="/momentum/ai-agents/categories/create" class="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    Create Category
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="space-y-6">
                                <?php foreach ($categories as $category): ?>
                                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                                        <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                                            <div class="flex items-center">
                                                <i class="fas <?= htmlspecialchars($category['icon']) ?>" style="color: <?= htmlspecialchars($category['color']) ?>;"></i>
                                                <h4 class="ml-2 text-md font-medium text-gray-900 dark:text-white">
                                                    <?= htmlspecialchars($category['name']) ?>
                                                    <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">(<?= count($category['agents']) ?> agents)</span>
                                                </h4>
                                            </div>
                                            <?php if ($category['id'] > 0): ?>
                                                <a href="/momentum/ai-agents/categories/edit/<?= $category['id'] ?>" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                        <?php if (empty($category['agents'])): ?>
                                            <div class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
                                                No agents in this category
                                            </div>
                                        <?php else: ?>
                                            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                                <?php foreach ($category['agents'] as $agent): ?>
                                                    <li class="px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-750">
                                                        <a href="/momentum/ai-agents/view/<?= $agent['id'] ?>" class="flex justify-between items-center">
                                                            <div class="flex items-center">
                                                                <div class="w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center">
                                                                    <?php if ($agent['avatar']): ?>
                                                                        <img src="<?= htmlspecialchars($agent['avatar']) ?>" alt="<?= htmlspecialchars($agent['name']) ?>" class="w-8 h-8 rounded-full">
                                                                    <?php else: ?>
                                                                        <i class="fas fa-robot text-indigo-600 dark:text-indigo-400"></i>
                                                                    <?php endif; ?>
                                                                </div>
                                                                <div class="ml-3">
                                                                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                                                                        <?= htmlspecialchars($agent['name']) ?>
                                                                    </p>
                                                                    <div class="flex items-center">
                                                                        <span class="text-xs text-gray-500 dark:text-gray-400">
                                                                            <?= $agent['pending_tasks'] ?> pending tasks
                                                                        </span>
                                                                        <span class="mx-2 text-gray-300 dark:text-gray-600">•</span>
                                                                        <span class="text-xs <?= $agent['status'] === 'active' ? 'text-green-500' : ($agent['status'] === 'error' ? 'text-red-500' : 'text-yellow-500') ?>">
                                                                            <?= ucfirst($agent['status']) ?>
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="flex items-center">
                                                                <div class="flex items-center mr-4">
                                                                    <span class="text-xs text-gray-500 dark:text-gray-400 mr-1">IQ:</span>
                                                                    <span class="text-xs font-medium text-gray-900 dark:text-white"><?= $agent['intelligence_level'] ?></span>
                                                                </div>
                                                                <i class="fas fa-chevron-right text-gray-400"></i>
                                                            </div>
                                                        </a>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Right Column: Active Agents and Recent Interactions -->
            <div class="space-y-6">
                <!-- Active Agents -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Active Agents
                        </h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                            Your most recently active AI agents
                        </p>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <?php if (empty($activeAgents)): ?>
                            <div class="text-center py-4">
                                <p class="text-gray-500 dark:text-gray-400">No active agents</p>
                            </div>
                        <?php else: ?>
                            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($activeAgents as $agent): ?>
                                    <li class="py-3">
                                        <a href="/momentum/ai-agents/view/<?= $agent['id'] ?>" class="flex items-center hover:bg-gray-50 dark:hover:bg-gray-750 px-2 py-1 rounded-md">
                                            <div class="w-10 h-10 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center">
                                                <?php if ($agent['avatar']): ?>
                                                    <img src="<?= htmlspecialchars($agent['avatar']) ?>" alt="<?= htmlspecialchars($agent['name']) ?>" class="w-10 h-10 rounded-full">
                                                <?php else: ?>
                                                    <i class="fas fa-robot text-indigo-600 dark:text-indigo-400 text-lg"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">
                                                    <?= htmlspecialchars($agent['name']) ?>
                                                </p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                                    Last active: <?= date('M j, g:i a', strtotime($agent['last_active'])) ?>
                                                </p>
                                            </div>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Interactions -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Recent Interactions
                        </h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                            Your latest communications with AI agents
                        </p>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <?php if (empty($recentInteractions)): ?>
                            <div class="text-center py-4">
                                <p class="text-gray-500 dark:text-gray-400">No recent interactions</p>
                            </div>
                        <?php else: ?>
                            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($recentInteractions as $interaction): ?>
                                    <li class="py-3">
                                        <div class="flex items-start">
                                            <div class="w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center">
                                                <?php if ($interaction['agent_avatar']): ?>
                                                    <img src="<?= htmlspecialchars($interaction['agent_avatar']) ?>" alt="<?= htmlspecialchars($interaction['agent_name']) ?>" class="w-8 h-8 rounded-full">
                                                <?php else: ?>
                                                    <i class="fas fa-robot text-indigo-600 dark:text-indigo-400"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div class="ml-3 flex-1">
                                                <div class="flex justify-between items-center">
                                                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                                                        <?= htmlspecialchars($interaction['agent_name']) ?>
                                                    </p>
                                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                                        <?= date('M j, g:i a', strtotime($interaction['created_at'])) ?>
                                                    </p>
                                                </div>
                                                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium <?= getInteractionTypeClass($interaction['interaction_type']) ?>">
                                                        <?= ucfirst($interaction['interaction_type']) ?>
                                                    </span>
                                                    <?= htmlspecialchars(truncateText($interaction['content'], 50)) ?>
                                                </p>
                                            </div>
                                        </div>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
/**
 * Helper function to get the appropriate CSS class for interaction type
 */
function getInteractionTypeClass($type) {
    switch ($type) {
        case 'command':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
        case 'query':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
        case 'feedback':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
        case 'training':
            return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
        case 'system':
            return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
}

/**
 * Helper function to truncate text
 */
function truncateText($text, $length = 50) {
    if (strlen($text) <= $length) {
        return $text;
    }

    return substr($text, 0, $length) . '...';
}
?>
