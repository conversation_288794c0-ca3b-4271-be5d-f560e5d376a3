<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h1>My Checklists</h1>
            <p class="lead">Track your progress with interactive checklists</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="/momentum/checklists/create" class="btn btn-primary">
                    <i class="fas fa-plus"></i> New Checklist
                </a>
                <a href="/momentum/checklists/templates" class="btn btn-outline-primary">
                    <i class="fas fa-list-alt"></i> Templates
                </a>
            </div>
        </div>
    </div>
    
    <?php if (empty($checklists)): ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <h3 class="mb-3">No Checklists Yet</h3>
                <p class="mb-4">Create your first checklist to start tracking your progress</p>
                <a href="/momentum/checklists/create" class="btn btn-lg btn-primary">
                    <i class="fas fa-plus"></i> Create Your First Checklist
                </a>
            </div>
        </div>
    <?php else: ?>
        <div class="row">
            <?php foreach ($checklists as $checklist): ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0"><?= htmlspecialchars($checklist['name']) ?></h5>
                            <span class="badge <?= getStatusBadgeClass($checklist['status']) ?>">
                                <?= ucfirst($checklist['status']) ?>
                            </span>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($checklist['description'])): ?>
                                <p class="card-text"><?= htmlspecialchars($checklist['description']) ?></p>
                            <?php else: ?>
                                <p class="card-text text-muted">No description provided</p>
                            <?php endif; ?>
                            
                            <div class="progress mb-3" style="height: 10px;">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: <?= $checklist['stats']['completion_percentage'] ?>%;" 
                                     aria-valuenow="<?= $checklist['stats']['completion_percentage'] ?>" 
                                     aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between small text-muted mb-3">
                                <span>Progress: <?= $checklist['stats']['completion_percentage'] ?>%</span>
                                <span><?= $checklist['stats']['completed'] ?> / <?= $checklist['stats']['total'] ?> completed</span>
                            </div>
                            
                            <?php if (!empty($checklist['project_id'])): ?>
                                <p class="mb-0">
                                    <span class="badge bg-info">Project Checklist</span>
                                </p>
                            <?php endif; ?>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">Created: <?= date('M j, Y', strtotime($checklist['created_at'])) ?></small>
                                <a href="/momentum/checklists/view/<?= $checklist['id'] ?>" class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<?php
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'active':
            return 'bg-success';
        case 'completed':
            return 'bg-primary';
        case 'archived':
            return 'bg-secondary';
        default:
            return 'bg-secondary';
    }
}
?>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
