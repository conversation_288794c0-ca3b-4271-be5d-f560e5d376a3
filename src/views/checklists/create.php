<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h1>Create New Checklist</h1>
            <p class="lead">Create a new checklist from scratch or from a template</p>
        </div>
        <div class="col-auto">
            <a href="/momentum/checklists" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Checklists
            </a>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">New Checklist</h5>
                </div>
                <div class="card-body">
                    <form action="/momentum/checklists/store" method="POST">
                        <div class="mb-3">
                            <label for="name" class="form-label">Checklist Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description (Optional)</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="project_id" class="form-label">Associated Project (Optional)</label>
                            <select class="form-select" id="project_id" name="project_id">
                                <option value="">None</option>
                                <?php foreach ($projects as $project): ?>
                                    <option value="<?= $project['id'] ?>"><?= htmlspecialchars($project['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="template_id" class="form-label">Template (Optional)</label>
                            <select class="form-select" id="template_id" name="template_id">
                                <option value="">None (Create Empty Checklist)</option>
                                
                                <?php if (!empty($systemTemplates)): ?>
                                    <optgroup label="System Templates">
                                        <?php foreach ($systemTemplates as $template): ?>
                                            <option value="<?= $template['id'] ?>"><?= htmlspecialchars($template['name']) ?></option>
                                        <?php endforeach; ?>
                                    </optgroup>
                                <?php endif; ?>
                                
                                <?php if (!empty($userTemplates)): ?>
                                    <optgroup label="Your Templates">
                                        <?php foreach ($userTemplates as $template): ?>
                                            <option value="<?= $template['id'] ?>"><?= htmlspecialchars($template['name']) ?></option>
                                        <?php endforeach; ?>
                                    </optgroup>
                                <?php endif; ?>
                            </select>
                            <div class="form-text">Select a template to pre-populate your checklist with items</div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">Create Checklist</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">AI Agent Army Templates</h5>
                </div>
                <div class="card-body">
                    <p>Use our specialized AI Agent Army templates to implement your brigades effectively:</p>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <i class="fas fa-check-square text-success me-2"></i> Implementation Master Checklist
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check-square text-success me-2"></i> Brigade Setup Checklists
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check-square text-success me-2"></i> Agent Configuration Checklist
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check-square text-success me-2"></i> Daily Operation Checklist
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">Tips</h5>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li>Use templates to save time on repetitive checklists</li>
                        <li>Associate checklists with projects to keep organized</li>
                        <li>Break down large tasks into smaller checklist items</li>
                        <li>Create your own templates from successful checklists</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
