<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div class="flex items-center">
                <a href="/momentum/finances/subscriptions" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                    Add New Subscription
                </h1>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <form action="/momentum/finances/create-subscription" method="POST" class="p-6">
                <!-- Display validation errors if any -->
                <?php if (isset($errors) && !empty($errors)): ?>
                    <div class="mb-4 bg-red-50 dark:bg-red-900 p-4 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-400 dark:text-red-300"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                    Please fix the following errors:
                                </h3>
                                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                    <ul class="list-disc pl-5 space-y-1">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?= $error ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Name -->
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Subscription Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="name" id="name" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($data['name']) ? View::escape($data['name']) : '' ?>" required>
                </div>

                <!-- Amount -->
                <div class="mb-4">
                    <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Amount <span class="text-red-500">*</span>
                    </label>
                    <div class="flex">
                        <div class="flex-none w-10 flex items-center justify-center bg-gray-100 dark:bg-gray-600 border border-r-0 border-gray-300 dark:border-gray-700 rounded-l-md">
                            <span class="text-gray-500 dark:text-gray-400 font-medium">Rs</span>
                        </div>
                        <input type="number" name="amount" id="amount" step="0.01" min="0.01" class="flex-grow rounded-none rounded-r-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($data['amount']) ? View::escape($data['amount']) : '' ?>" required>
                    </div>
                </div>

                <!-- Billing Cycle -->
                <div class="mb-4">
                    <label for="billing_cycle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Billing Cycle <span class="text-red-500">*</span>
                    </label>
                    <select name="billing_cycle" id="billing_cycle" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" required>
                        <option value="monthly" <?= (isset($data['billing_cycle']) && $data['billing_cycle'] === 'monthly') ? 'selected' : 'selected' ?>>Monthly</option>
                        <option value="yearly" <?= (isset($data['billing_cycle']) && $data['billing_cycle'] === 'yearly') ? 'selected' : '' ?>>Yearly</option>
                        <option value="quarterly" <?= (isset($data['billing_cycle']) && $data['billing_cycle'] === 'quarterly') ? 'selected' : '' ?>>Quarterly</option>
                        <option value="weekly" <?= (isset($data['billing_cycle']) && $data['billing_cycle'] === 'weekly') ? 'selected' : '' ?>>Weekly</option>
                    </select>
                </div>

                <!-- Description -->
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Description
                    </label>
                    <textarea name="description" id="description" rows="3" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"><?= isset($data['description']) ? View::escape($data['description']) : '' ?></textarea>
                </div>

                <!-- Features List -->
                <div class="mb-6 border border-gray-200 dark:border-gray-700 rounded-md p-4">
                    <h3 class="text-base font-medium text-gray-900 dark:text-white mb-4">Features & Benefits</h3>

                    <!-- Main Features Section -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Main Features Section Title
                        </label>
                        <input type="text" name="main_title" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white mb-2"
                            value="<?= isset($data['main_title']) ? View::escape($data['main_title']) : '' ?>"
                            placeholder="e.g., Main Features">

                        <div id="main-features-container">
                            <div class="flex items-center mb-2 feature-item">
                                <input type="text" name="main_features[]" class="flex-grow rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                                    placeholder="Enter a feature">
                                <button type="button" class="ml-2 text-red-500 hover:text-red-700 remove-feature">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <button type="button" id="add-main-feature" class="mt-2 inline-flex items-center px-3 py-1 border border-transparent rounded-md text-sm font-medium text-primary-700 dark:text-primary-300 bg-primary-100 dark:bg-primary-900 hover:bg-primary-200 dark:hover:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-1"></i> Add Feature
                        </button>
                    </div>

                    <!-- Additional Features Section -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Additional Benefits Section Title
                        </label>
                        <input type="text" name="additional_title" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white mb-2"
                            value="<?= isset($data['additional_title']) ? View::escape($data['additional_title']) : '' ?>"
                            placeholder="e.g., Also Included">

                        <div id="additional-features-container">
                            <div class="flex items-center mb-2 feature-item">
                                <input type="text" name="additional_features[]" class="flex-grow rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                                    placeholder="Enter a benefit">
                                <button type="button" class="ml-2 text-red-500 hover:text-red-700 remove-feature">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <button type="button" id="add-additional-feature" class="mt-2 inline-flex items-center px-3 py-1 border border-transparent rounded-md text-sm font-medium text-primary-700 dark:text-primary-300 bg-primary-100 dark:bg-primary-900 hover:bg-primary-200 dark:hover:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-1"></i> Add Benefit
                        </button>
                    </div>
                </div>

                <!-- Next Billing Date -->
                <div class="mb-6">
                    <label for="next_billing_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Next Billing Date <span class="text-red-500">*</span>
                    </label>
                    <input type="date" name="next_billing_date" id="next_billing_date" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($data['next_billing_date']) ? View::escape($data['next_billing_date']) : date('Y-m-d') ?>" required>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3">
                    <a href="/momentum/finances/subscriptions" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        Add Subscription
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add main feature
        document.getElementById('add-main-feature').addEventListener('click', function() {
            addFeature('main-features-container', 'main_features[]');
        });

        // Add additional feature
        document.getElementById('add-additional-feature').addEventListener('click', function() {
            addFeature('additional-features-container', 'additional_features[]');
        });

        // Remove feature (delegated event)
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-feature') || e.target.closest('.remove-feature')) {
                const button = e.target.classList.contains('remove-feature') ? e.target : e.target.closest('.remove-feature');
                const featureItem = button.closest('.feature-item');

                // Don't remove if it's the last one in the container
                const container = featureItem.parentElement;
                if (container.querySelectorAll('.feature-item').length > 1) {
                    featureItem.remove();
                } else {
                    // Clear the input instead of removing
                    featureItem.querySelector('input').value = '';
                }
            }
        });

        // Function to add a new feature input
        function addFeature(containerId, inputName) {
            const container = document.getElementById(containerId);
            const newFeature = document.createElement('div');
            newFeature.className = 'flex items-center mb-2 feature-item';
            newFeature.innerHTML = `
                <input type="text" name="${inputName}" class="flex-grow rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="Enter a feature">
                <button type="button" class="ml-2 text-red-500 hover:text-red-700 remove-feature">
                    <i class="fas fa-times"></i>
                </button>
            `;
            container.appendChild(newFeature);

            // Focus the new input
            newFeature.querySelector('input').focus();
        }
    });
</script>
