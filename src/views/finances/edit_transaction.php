<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div class="flex items-center">
                <a href="/momentum/finances" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                    Edit Transaction
                </h1>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <form action="/momentum/finances/edit-transaction/<?= $transaction['id'] ?>" method="POST" class="p-6" enctype="multipart/form-data">
                <!-- Display validation errors if any -->
                <?php if (isset($errors) && !empty($errors)): ?>
                    <div class="mb-4 bg-red-50 dark:bg-red-900 p-4 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-400 dark:text-red-300"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                    Please fix the following errors:
                                </h3>
                                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                    <ul class="list-disc pl-5 space-y-1">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?= $error ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Transaction Type (Income/Expense) -->
                <div class="mb-4">
                    <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Transaction Type <span class="text-red-500">*</span>
                    </label>
                    <div class="flex space-x-4">
                        <div class="flex items-center">
                            <input type="radio" id="type_expense" name="type" value="expense" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 dark:bg-gray-700" <?= $transaction['type'] === 'expense' ? 'checked' : '' ?>>
                            <label for="type_expense" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                Expense
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="type_income" name="type" value="income" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 dark:bg-gray-700" <?= $transaction['type'] === 'income' ? 'checked' : '' ?>>
                            <label for="type_income" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                Income
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Transaction Form (Monetary/Non-Monetary) -->
                <div class="mb-4">
                    <label for="transaction_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Transaction Form <span class="text-red-500">*</span>
                    </label>
                    <div class="flex space-x-4">
                        <div class="flex items-center">
                            <input type="radio" id="transaction_type_monetary" name="transaction_type" value="monetary" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 dark:bg-gray-700"
                                <?= (!isset($transaction['transaction_type']) || $transaction['transaction_type'] === 'monetary') ? 'checked' : '' ?>
                                onchange="toggleTransactionFields()">
                            <label for="transaction_type_monetary" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                Money
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="transaction_type_non_monetary" name="transaction_type" value="non_monetary" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 dark:bg-gray-700"
                                <?= (isset($transaction['transaction_type']) && $transaction['transaction_type'] === 'non_monetary') ? 'checked' : '' ?>
                                onchange="toggleTransactionFields()">
                            <label for="transaction_type_non_monetary" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                Goods/Services
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Monetary Transaction Fields -->
                <div id="monetary-fields" class="<?= (isset($transaction['transaction_type']) && $transaction['transaction_type'] === 'non_monetary') ? 'hidden' : '' ?>">
                    <!-- Amount -->
                    <div class="mb-4">
                        <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Amount <span class="text-red-500">*</span>
                        </label>
                        <div class="flex">
                            <div class="flex-none w-10 flex items-center justify-center bg-gray-100 dark:bg-gray-600 border border-r-0 border-gray-300 dark:border-gray-700 rounded-l-md">
                                <span class="text-gray-500 dark:text-gray-400 font-medium">Rs</span>
                            </div>
                            <input type="number" name="amount" id="amount" step="0.01" min="0.01" class="flex-grow rounded-none rounded-r-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= View::escape($transaction['amount']) ?>">
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div class="mb-4">
                        <label for="payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Payment Method <span class="text-red-500">*</span>
                        </label>
                        <select name="payment_method" id="payment_method" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="cash" <?= (isset($transaction['payment_method']) && $transaction['payment_method'] === 'cash') ? 'selected' : '' ?>>Cash</option>
                            <option value="credit_card" <?= (isset($transaction['payment_method']) && $transaction['payment_method'] === 'credit_card') ? 'selected' : '' ?>>Credit Card</option>
                            <option value="debit_card" <?= (isset($transaction['payment_method']) && $transaction['payment_method'] === 'debit_card') ? 'selected' : '' ?>>Debit Card</option>
                            <option value="bank_transfer" <?= (isset($transaction['payment_method']) && $transaction['payment_method'] === 'bank_transfer') ? 'selected' : '' ?>>Bank Transfer</option>
                            <option value="other" <?= (isset($transaction['payment_method']) && $transaction['payment_method'] === 'other') ? 'selected' : '' ?>>Other</option>
                        </select>
                    </div>
                </div>

                <!-- Non-Monetary Transaction Fields -->
                <div id="non-monetary-fields" class="<?= (isset($transaction['transaction_type']) && $transaction['transaction_type'] === 'non_monetary') ? '' : 'hidden' ?>">
                    <!-- Fair Market Value -->
                    <div class="mb-4">
                        <label for="fair_market_value" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Estimated Value <span class="text-red-500">*</span>
                        </label>
                        <div class="flex">
                            <div class="flex-none w-10 flex items-center justify-center bg-gray-100 dark:bg-gray-600 border border-r-0 border-gray-300 dark:border-gray-700 rounded-l-md">
                                <span class="text-gray-500 dark:text-gray-400 font-medium">Rs</span>
                            </div>
                            <input type="number" name="fair_market_value" id="fair_market_value" step="0.01" min="0.01" class="flex-grow rounded-none rounded-r-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($transaction['fair_market_value']) ? View::escape($transaction['fair_market_value']) : '' ?>">
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Approximate market value of goods or services</p>
                    </div>

                    <!-- Goods/Services Description -->
                    <div class="mb-4">
                        <label for="goods_services_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Goods/Services Description <span class="text-red-500">*</span>
                        </label>
                        <textarea name="goods_services_description" id="goods_services_description" rows="3" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"><?= isset($transaction['goods_services_description']) ? View::escape($transaction['goods_services_description']) : '' ?></textarea>
                    </div>
                </div>

                <!-- Category -->
                <div class="mb-4">
                    <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Category <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="category" id="category" list="category-list" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= View::escape($transaction['category']) ?>" required>
                    <datalist id="category-list">
                        <!-- Basic Categories -->
                        <option value="Food & Dining">
                        <option value="Shopping">
                        <option value="Housing">
                        <option value="Transportation">
                        <option value="Utilities">
                        <option value="Healthcare">
                        <option value="Entertainment">
                        <option value="Personal Care">
                        <option value="Education">
                        <option value="Gifts & Donations">
                        <option value="Investments">
                        <option value="Salary">
                        <option value="Business">

                        <!-- Debt Categories -->
                        <option value="Debt Payment">
                        <option value="Loan Payment">
                        <option value="Credit Card Payment">

                        <!-- Relationship-based Categories -->
                        <option value="Spouse/Partner Payment">
                        <option value="Family Support">
                        <option value="Child Support">
                        <option value="Relative Assistance">
                        <option value="Friend Loan">
                        <option value="Shared Expenses">
                        <option value="Allowance">
                        <option value="Gift to Person">

                        <option value="Other">
                    </datalist>
                </div>

                <!-- Description -->
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Description
                    </label>
                    <input type="text" name="description" id="description" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= View::escape($transaction['description']) ?>">
                </div>

                <!-- Receipt Upload -->
                <div class="mb-4">
                    <label for="receipt" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Receipt (Optional)
                    </label>
                    <div class="mt-1 flex items-center">
                        <input type="file" name="receipt" id="receipt" accept="image/jpeg,image/png,image/gif,application/pdf" class="w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 dark:file:bg-primary-900 dark:file:text-primary-300 hover:file:bg-primary-100 dark:hover:file:bg-primary-800">
                    </div>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Accepted formats: JPEG, PNG, GIF, PDF. Maximum size: 5MB.
                    </p>

                    <?php
                    // Get receipts for this transaction
                    $receiptModel = new Receipt();
                    $receipts = $receiptModel->getTransactionReceipts($transaction['id']);
                    if (!empty($receipts)):
                    ?>
                    <div class="mt-3">
                        <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Existing Receipts:</p>
                        <div class="flex flex-wrap gap-2">
                            <?php foreach ($receipts as $receipt): ?>
                                <div class="flex items-center bg-gray-50 dark:bg-gray-700 rounded-md p-2">
                                    <span class="text-xs text-gray-600 dark:text-gray-300 truncate max-w-[150px]" title="<?= View::escape($receipt['file_name']) ?>">
                                        <?= View::escape($receipt['file_name']) ?>
                                    </span>
                                    <a href="/momentum/finances/transactions/<?= $transaction['id'] ?>/receipts" class="ml-2 text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <a href="/momentum/finances/transactions/<?= $transaction['id'] ?>/receipts" class="inline-flex items-center mt-2 text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                            <i class="fas fa-receipt mr-1"></i> Manage Receipts
                        </a>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Income Source Selection (only shown for income transactions) -->
                <div id="income-source-field" class="mb-4 <?= $transaction['type'] === 'income' ? '' : 'hidden' ?>">
                    <label for="income_source_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Income Source (Optional)
                    </label>
                    <select name="income_source_id" id="income_source_id" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                        <option value="">-- Select Income Source --</option>
                        <?php
                        // Get income sources from the controller
                        $incomeSourceModel = new IncomeSource();
                        $user = Session::getUser();
                        $incomeSources = $incomeSourceModel->getUserIncomeSources($user['id']);

                        // Get current income source for this transaction
                        $currentSourceId = null;
                        $sourceTransactions = $incomeSourceModel->getSourceTransactionsByTransactionId($transaction['id']);
                        if (!empty($sourceTransactions)) {
                            $currentSourceId = $sourceTransactions[0]['income_source_id'];
                        }

                        foreach ($incomeSources as $source):
                        ?>
                            <option value="<?= $source['id'] ?>" <?= $currentSourceId == $source['id'] ? 'selected' : '' ?>>
                                <?= View::escape($source['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Link this transaction to an income source for better tracking</p>
                </div>

                <!-- Date -->
                <div class="mb-6">
                    <label for="date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Date <span class="text-red-500">*</span>
                    </label>
                    <input type="date" name="date" id="date" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= date('Y-m-d', strtotime($transaction['date'])) ?>" required>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3">
                    <a href="/momentum/finances" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        Update Transaction
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function toggleTransactionFields() {
        const monetaryFields = document.getElementById('monetary-fields');
        const nonMonetaryFields = document.getElementById('non-monetary-fields');
        const isMonetary = document.getElementById('transaction_type_monetary').checked;

        if (isMonetary) {
            monetaryFields.classList.remove('hidden');
            nonMonetaryFields.classList.add('hidden');
        } else {
            monetaryFields.classList.add('hidden');
            nonMonetaryFields.classList.remove('hidden');
        }
    }

    function toggleIncomeSourceField() {
        const incomeSourceField = document.getElementById('income-source-field');
        const isIncome = document.getElementById('type_income').checked;

        if (isIncome) {
            incomeSourceField.classList.remove('hidden');
        } else {
            incomeSourceField.classList.add('hidden');
        }
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        toggleTransactionFields();
        toggleIncomeSourceField();

        // Add event listeners for transaction type changes
        document.getElementById('type_income').addEventListener('change', toggleIncomeSourceField);
        document.getElementById('type_expense').addEventListener('change', toggleIncomeSourceField);
    });
</script>
