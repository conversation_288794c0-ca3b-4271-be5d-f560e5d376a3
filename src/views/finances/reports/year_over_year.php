<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Year-over-Year Analysis</h1>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/finances/reports" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Reports
                </a>
                <button id="export-pdf" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-file-pdf mr-2"></i> Export PDF
                </button>
                <button id="export-csv" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-file-csv mr-2"></i> Export CSV
                </button>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <form action="/momentum/finances/reports/year-over-year" method="GET" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                    <div class="flex-1">
                        <label for="current_year" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Current Year</label>
                        <select id="current_year" name="current_year" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                            <?php for ($year = date('Y'); $year >= date('Y') - 10; $year--): ?>
                                <option value="<?= $year ?>" <?= $filters['current_year'] == $year ? 'selected' : '' ?>><?= $year ?></option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <div class="flex-1">
                        <label for="previous_year" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Previous Year</label>
                        <select id="previous_year" name="previous_year" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                            <?php for ($year = date('Y') - 1; $year >= date('Y') - 11; $year--): ?>
                                <option value="<?= $year ?>" <?= $filters['previous_year'] == $year ? 'selected' : '' ?>><?= $year ?></option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <div class="flex-1">
                        <label for="view_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">View Type</label>
                        <select id="view_type" name="view_type" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                            <option value="monthly" <?= $filters['view_type'] == 'monthly' ? 'selected' : '' ?>>Monthly</option>
                            <option value="quarterly" <?= $filters['view_type'] == 'quarterly' ? 'selected' : '' ?>>Quarterly</option>
                            <option value="annual" <?= $filters['view_type'] == 'annual' ? 'selected' : '' ?>>Annual</option>
                        </select>
                    </div>
                    <div>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Year-over-Year Summary -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Year-over-Year Summary</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($yearlyComparison)): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No data available for the selected years</p>
                    </div>
                <?php else: ?>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Income Comparison -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-base font-medium text-gray-900 dark:text-white mb-2">Income</h3>
                            <div class="flex items-baseline">
                                <span class="text-2xl font-semibold text-gray-900 dark:text-white"><?= View::formatCurrency($yearlyComparison['current_year']['total_income']) ?></span>
                                <?php 
                                $incomeDiff = $yearlyComparison['current_year']['total_income'] - $yearlyComparison['previous_year']['total_income'];
                                $incomePercent = $yearlyComparison['previous_year']['total_income'] > 0 
                                    ? ($incomeDiff / $yearlyComparison['previous_year']['total_income']) * 100 
                                    : 0;
                                ?>
                                <span class="ml-2 text-sm <?= $incomeDiff >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                                    <?= $incomeDiff >= 0 ? '+' : '' ?><?= View::formatCurrency($incomeDiff) ?> (<?= number_format($incomePercent, 1) ?>%)
                                </span>
                            </div>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">vs <?= View::formatCurrency($yearlyComparison['previous_year']['total_income']) ?> in <?= $filters['previous_year'] ?></p>
                        </div>

                        <!-- Expense Comparison -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-base font-medium text-gray-900 dark:text-white mb-2">Expenses</h3>
                            <div class="flex items-baseline">
                                <span class="text-2xl font-semibold text-gray-900 dark:text-white"><?= View::formatCurrency($yearlyComparison['current_year']['total_expense']) ?></span>
                                <?php 
                                $expenseDiff = $yearlyComparison['current_year']['total_expense'] - $yearlyComparison['previous_year']['total_expense'];
                                $expensePercent = $yearlyComparison['previous_year']['total_expense'] > 0 
                                    ? ($expenseDiff / $yearlyComparison['previous_year']['total_expense']) * 100 
                                    : 0;
                                ?>
                                <span class="ml-2 text-sm <?= $expenseDiff <= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                                    <?= $expenseDiff >= 0 ? '+' : '' ?><?= View::formatCurrency($expenseDiff) ?> (<?= number_format($expensePercent, 1) ?>%)
                                </span>
                            </div>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">vs <?= View::formatCurrency($yearlyComparison['previous_year']['total_expense']) ?> in <?= $filters['previous_year'] ?></p>
                        </div>

                        <!-- Net Comparison -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-base font-medium text-gray-900 dark:text-white mb-2">Net Income</h3>
                            <?php 
                            $currentNet = $yearlyComparison['current_year']['total_income'] - $yearlyComparison['current_year']['total_expense'];
                            $previousNet = $yearlyComparison['previous_year']['total_income'] - $yearlyComparison['previous_year']['total_expense'];
                            $netDiff = $currentNet - $previousNet;
                            $netPercent = $previousNet != 0 ? ($netDiff / abs($previousNet)) * 100 : 0;
                            ?>
                            <div class="flex items-baseline">
                                <span class="text-2xl font-semibold <?= $currentNet >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>"><?= View::formatCurrency($currentNet) ?></span>
                                <span class="ml-2 text-sm <?= $netDiff >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                                    <?= $netDiff >= 0 ? '+' : '' ?><?= View::formatCurrency($netDiff) ?> (<?= number_format($netPercent, 1) ?>%)
                                </span>
                            </div>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">vs <?= View::formatCurrency($previousNet) ?> in <?= $filters['previous_year'] ?></p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Year-over-Year Chart -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Year-over-Year Comparison</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($comparisonData)): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No data available for the selected years</p>
                    </div>
                <?php else: ?>
                    <div class="h-80">
                        <canvas id="year-over-year-chart"></canvas>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Detailed Comparison Table -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Detailed Comparison</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($comparisonData)): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No data available for the selected years</p>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Period</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"><?= $filters['current_year'] ?> Income</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"><?= $filters['previous_year'] ?> Income</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">% Change</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"><?= $filters['current_year'] ?> Expense</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"><?= $filters['previous_year'] ?> Expense</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">% Change</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($comparisonData as $period => $data): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white"><?= View::escape($period) ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white"><?= View::formatCurrency($data['current_year']['income'] ?? 0) ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white"><?= View::formatCurrency($data['previous_year']['income'] ?? 0) ?></td>
                                        <?php 
                                        $incomeChange = isset($data['previous_year']['income']) && $data['previous_year']['income'] > 0 
                                            ? (($data['current_year']['income'] - $data['previous_year']['income']) / $data['previous_year']['income']) * 100 
                                            : 0;
                                        ?>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right <?= $incomeChange >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                                            <?= number_format($incomeChange, 1) ?>%
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white"><?= View::formatCurrency($data['current_year']['expense'] ?? 0) ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white"><?= View::formatCurrency($data['previous_year']['expense'] ?? 0) ?></td>
                                        <?php 
                                        $expenseChange = isset($data['previous_year']['expense']) && $data['previous_year']['expense'] > 0 
                                            ? (($data['current_year']['expense'] - $data['previous_year']['expense']) / $data['previous_year']['expense']) * 100 
                                            : 0;
                                        ?>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right <?= $expenseChange <= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                                            <?= number_format($expenseChange, 1) ?>%
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($comparisonData)): ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('year-over-year-chart').getContext('2d');
        
        // Extract data from PHP
        const comparisonData = <?= json_encode($comparisonData) ?>;
        const periods = Object.keys(comparisonData);
        const currentYearIncome = periods.map(period => comparisonData[period].current_year.income || 0);
        const previousYearIncome = periods.map(period => comparisonData[period].previous_year.income || 0);
        const currentYearExpense = periods.map(period => comparisonData[period].current_year.expense || 0);
        const previousYearExpense = periods.map(period => comparisonData[period].previous_year.expense || 0);
        
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: periods,
                datasets: [
                    {
                        label: '<?= $filters['current_year'] ?> Income',
                        data: currentYearIncome,
                        backgroundColor: 'rgba(34, 197, 94, 0.7)',
                        borderColor: 'rgba(34, 197, 94, 1)',
                        borderWidth: 1
                    },
                    {
                        label: '<?= $filters['previous_year'] ?> Income',
                        data: previousYearIncome,
                        backgroundColor: 'rgba(34, 197, 94, 0.3)',
                        borderColor: 'rgba(34, 197, 94, 0.7)',
                        borderWidth: 1
                    },
                    {
                        label: '<?= $filters['current_year'] ?> Expense',
                        data: currentYearExpense,
                        backgroundColor: 'rgba(239, 68, 68, 0.7)',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        borderWidth: 1
                    },
                    {
                        label: '<?= $filters['previous_year'] ?> Expense',
                        data: previousYearExpense,
                        backgroundColor: 'rgba(239, 68, 68, 0.3)',
                        borderColor: 'rgba(239, 68, 68, 0.7)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rs ' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += 'Rs ' + context.raw.toLocaleString();
                                return label;
                            }
                        }
                    }
                }
            }
        });
    });

    // Export buttons
    document.getElementById('export-pdf').addEventListener('click', function() {
        window.location.href = '/momentum/finances/reports/export/year-over-year/pdf?current_year=<?= $filters['current_year'] ?>&previous_year=<?= $filters['previous_year'] ?>&view_type=<?= $filters['view_type'] ?>';
    });

    document.getElementById('export-csv').addEventListener('click', function() {
        window.location.href = '/momentum/finances/reports/export/year-over-year/csv?current_year=<?= $filters['current_year'] ?>&previous_year=<?= $filters['previous_year'] ?>&view_type=<?= $filters['view_type'] ?>';
    });
</script>
<?php endif; ?>
