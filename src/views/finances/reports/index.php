<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Financial Reports</h1>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/finances" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Finances
                </a>
            </div>
        </div>

        <!-- Financial Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- Year Summary -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Year Summary (<?= $currentYear ?>)</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Income</p>
                            <p class="text-xl font-semibold text-green-600 dark:text-green-400">
                                <?= View::formatCurrency($yearSummary['total_income'] ?? 0) ?>
                            </p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Expenses</p>
                            <p class="text-xl font-semibold text-red-600 dark:text-red-400">
                                <?= View::formatCurrency($yearSummary['total_expense'] ?? 0) ?>
                            </p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <p class="text-sm text-gray-500 dark:text-gray-400">Net</p>
                        <p class="text-xl font-semibold <?= ($yearSummary['total_income'] ?? 0) - ($yearSummary['total_expense'] ?? 0) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                            <?= View::formatCurrency(($yearSummary['total_income'] ?? 0) - ($yearSummary['total_expense'] ?? 0)) ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Month Summary -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Month Summary (<?= date('F Y') ?>)</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Income</p>
                            <p class="text-xl font-semibold text-green-600 dark:text-green-400">
                                <?= View::formatCurrency($monthSummary['total_income'] ?? 0) ?>
                            </p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Expenses</p>
                            <p class="text-xl font-semibold text-red-600 dark:text-red-400">
                                <?= View::formatCurrency($monthSummary['total_expense'] ?? 0) ?>
                            </p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <p class="text-sm text-gray-500 dark:text-gray-400">Net</p>
                        <p class="text-xl font-semibold <?= ($monthSummary['total_income'] ?? 0) - ($monthSummary['total_expense'] ?? 0) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                            <?= View::formatCurrency(($monthSummary['total_income'] ?? 0) - ($monthSummary['total_expense'] ?? 0)) ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Budget Performance -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Budget Performance</h3>
                    <?php if (empty($budgetPerformance)): ?>
                        <div class="text-center py-4">
                            <p class="text-gray-500 dark:text-gray-400">No budget data available</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($budgetPerformance as $budget): ?>
                                <div>
                                    <div class="flex justify-between mb-1">
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?= View::escape($budget['name']) ?></span>
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            <?= number_format($budget['percentage'], 0) ?>%
                                        </span>
                                    </div>
                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                        <div class="h-2.5 rounded-full <?= $budget['percentage'] > 100 ? 'bg-red-600' : 'bg-primary-600' ?>" style="width: <?= min(100, $budget['percentage']) ?>%"></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Report Types -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            <!-- Income vs Expenses -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Income vs Expenses</h3>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <div class="text-center mb-4">
                        <i class="fas fa-chart-line text-4xl text-primary-500 dark:text-primary-400"></i>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 mb-4 text-center">
                        Compare your income and expenses over time to track your financial health
                    </p>
                    <div class="flex justify-center">
                        <a href="/momentum/finances/reports/income-vs-expenses" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            View Report
                        </a>
                    </div>
                </div>
            </div>

            <!-- Year-over-Year Comparison -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Year-over-Year Analysis</h3>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <div class="text-center mb-4">
                        <i class="fas fa-calendar-alt text-4xl text-indigo-500 dark:text-indigo-400"></i>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 mb-4 text-center">
                        Compare financial performance between years to identify trends and progress
                    </p>
                    <div class="flex justify-center">
                        <a href="/momentum/finances/reports/year-over-year" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                            View Report
                        </a>
                    </div>
                </div>
            </div>

            <!-- Month-over-Month Comparison -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Month-over-Month Analysis</h3>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <div class="text-center mb-4">
                        <i class="fas fa-chart-bar text-4xl text-green-500 dark:text-green-400"></i>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 mb-4 text-center">
                        Track monthly changes in your finances to identify short-term trends
                    </p>
                    <div class="flex justify-center">
                        <a href="/momentum/finances/reports/month-over-month" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                            View Report
                        </a>
                    </div>
                </div>
            </div>

            <!-- Income Source Analysis -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Income Source Analysis</h3>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <div class="text-center mb-4">
                        <i class="fas fa-money-bill-wave text-4xl text-yellow-500 dark:text-yellow-400"></i>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 mb-4 text-center">
                        Analyze your income sources to understand your revenue streams
                    </p>
                    <div class="flex justify-center">
                        <a href="/momentum/finances/reports/income-source-analysis" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200">
                            View Report
                        </a>
                    </div>
                </div>
            </div>

            <!-- Category Analysis -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Category Analysis</h3>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <div class="text-center mb-4">
                        <i class="fas fa-tags text-4xl text-purple-500 dark:text-purple-400"></i>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 mb-4 text-center">
                        Analyze spending by category to identify where your money is going
                    </p>
                    <div class="flex justify-center">
                        <a href="/momentum/finances/reports/category-analysis" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200">
                            View Report
                        </a>
                    </div>
                </div>
            </div>

            <!-- Advanced Dashboard -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Advanced Dashboard</h3>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <div class="text-center mb-4">
                        <i class="fas fa-tachometer-alt text-4xl text-blue-500 dark:text-blue-400"></i>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 mb-4 text-center">
                        Comprehensive financial dashboard with advanced visualizations
                    </p>
                    <div class="flex justify-center">
                        <a href="/momentum/finances/reports/advanced-dashboard" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            View Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <!-- AI-Powered Financial Insights -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">AI-Powered Financial Insights</h3>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <div class="text-center mb-4">
                        <i class="fas fa-brain text-4xl text-purple-500 dark:text-purple-400"></i>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 mb-4 text-center">
                        Get intelligent insights about your spending patterns, anomalies, and recommendations
                    </p>
                    <div class="flex justify-center">
                        <a href="/momentum/finances/reports/financial-insights" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200">
                            View Insights
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Income/Expense Chart -->
<?php if (!empty($monthlyData)): ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('monthly-chart').getContext('2d');

        // Extract data from PHP
        const monthlyData = <?= json_encode($monthlyData) ?>;
        const labels = monthlyData.map(item => item.month_name);
        const incomeData = monthlyData.map(item => parseFloat(item.total_income));
        const expenseData = monthlyData.map(item => parseFloat(item.total_expense));

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Income',
                        data: incomeData,
                        backgroundColor: 'rgba(34, 197, 94, 0.2)',
                        borderColor: 'rgba(34, 197, 94, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Expenses',
                        data: expenseData,
                        backgroundColor: 'rgba(239, 68, 68, 0.2)',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rs ' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += 'Rs ' + context.raw.toLocaleString();
                                return label;
                            }
                        }
                    }
                }
            }
        });
    });
</script>
<?php endif; ?>
