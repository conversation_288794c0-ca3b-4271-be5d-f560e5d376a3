<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Income Source Analysis</h1>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/finances/reports" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Reports
                </a>
                <button id="export-pdf" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-file-pdf mr-2"></i> Export PDF
                </button>
                <button id="export-csv" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-file-csv mr-2"></i> Export CSV
                </button>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <form action="/momentum/finances/reports/income-source-analysis" method="GET" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                    <div class="flex-1">
                        <label for="period" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Period</label>
                        <select id="period" name="period" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                            <option value="monthly" <?= $filters['period'] == 'monthly' ? 'selected' : '' ?>>Monthly</option>
                            <option value="quarterly" <?= $filters['period'] == 'quarterly' ? 'selected' : '' ?>>Quarterly</option>
                            <option value="yearly" <?= $filters['period'] == 'yearly' ? 'selected' : '' ?>>Yearly</option>
                            <option value="custom" <?= $filters['period'] == 'custom' ? 'selected' : '' ?>>Custom</option>
                        </select>
                    </div>
                    <div class="flex-1">
                        <label for="year" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Year</label>
                        <select id="year" name="year" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                            <?php for ($year = date('Y'); $year >= date('Y') - 5; $year--): ?>
                                <option value="<?= $year ?>" <?= $filters['year'] == $year ? 'selected' : '' ?>><?= $year ?></option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <div class="flex-1 custom-date-range <?= $filters['period'] != 'custom' ? 'hidden' : '' ?>">
                        <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                        <input type="date" id="start_date" name="start_date" value="<?= $filters['start_date'] ?? '' ?>" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                    </div>
                    <div class="flex-1 custom-date-range <?= $filters['period'] != 'custom' ? 'hidden' : '' ?>">
                        <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">End Date</label>
                        <input type="date" id="end_date" name="end_date" value="<?= $filters['end_date'] ?? '' ?>" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                    </div>
                    <div>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Income Source Distribution -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Income Source Distribution</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($sourceData)): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No income source data available for the selected period</p>
                    </div>
                <?php else: ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="h-80">
                            <canvas id="income-source-pie-chart"></canvas>
                        </div>
                        <div class="h-80">
                            <canvas id="income-source-bar-chart"></canvas>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Income Source Trends -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Income Source Trends</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($trendData)): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No trend data available for the selected period</p>
                    </div>
                <?php else: ?>
                    <div class="h-80">
                        <canvas id="income-source-trend-chart"></canvas>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Income Source Details -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Income Source Details</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($incomeSources)): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No income sources available</p>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Source Name</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total Income</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">% of Total</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Transactions</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Avg. per Transaction</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php 
                                $totalIncome = array_sum(array_column($sourceData, 'total_income'));
                                foreach ($sourceData as $source): 
                                    $percentage = $totalIncome > 0 ? ($source['total_income'] / $totalIncome) * 100 : 0;
                                    $avgPerTransaction = $source['transaction_count'] > 0 ? $source['total_income'] / $source['transaction_count'] : 0;
                                ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white"><?= View::escape($source['name']) ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"><?= View::escape(ucfirst($source['source_type'])) ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white"><?= View::formatCurrency($source['total_income']) ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white"><?= number_format($percentage, 1) ?>%</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white"><?= $source['transaction_count'] ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white"><?= View::formatCurrency($avgPerTransaction) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                                <tr class="bg-gray-50 dark:bg-gray-700">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">Total</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-bold text-gray-900 dark:text-white"><?= View::formatCurrency($totalIncome) ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-bold text-gray-900 dark:text-white">100%</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-bold text-gray-900 dark:text-white"><?= array_sum(array_column($sourceData, 'transaction_count')) ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($sourceData) || !empty($trendData)): ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle custom date range fields based on period selection
        document.getElementById('period').addEventListener('change', function() {
            const customDateFields = document.querySelectorAll('.custom-date-range');
            if (this.value === 'custom') {
                customDateFields.forEach(field => field.classList.remove('hidden'));
            } else {
                customDateFields.forEach(field => field.classList.add('hidden'));
            }
        });

        <?php if (!empty($sourceData)): ?>
        // Income Source Pie Chart
        const pieCtx = document.getElementById('income-source-pie-chart').getContext('2d');
        const sourceData = <?= json_encode($sourceData) ?>;
        const sourceNames = sourceData.map(item => item.name);
        const sourceIncomes = sourceData.map(item => parseFloat(item.total_income || 0));
        
        // Generate colors based on the number of sources
        const generateColors = (count) => {
            const colors = [];
            for (let i = 0; i < count; i++) {
                const hue = (i * 137) % 360; // Use golden angle approximation for nice distribution
                colors.push(`hsl(${hue}, 70%, 60%)`);
            }
            return colors;
        };
        
        const backgroundColors = generateColors(sourceNames.length);
        
        new Chart(pieCtx, {
            type: 'pie',
            data: {
                labels: sourceNames,
                datasets: [{
                    data: sourceIncomes,
                    backgroundColor: backgroundColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: Rs ${value.toLocaleString()} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
        
        // Income Source Bar Chart
        const barCtx = document.getElementById('income-source-bar-chart').getContext('2d');
        
        new Chart(barCtx, {
            type: 'bar',
            data: {
                labels: sourceNames,
                datasets: [{
                    label: 'Income',
                    data: sourceIncomes,
                    backgroundColor: backgroundColors,
                    borderColor: backgroundColors.map(color => color.replace('0.2', '1')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rs ' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += 'Rs ' + context.raw.toLocaleString();
                                return label;
                            }
                        }
                    }
                }
            }
        });
        <?php endif; ?>
        
        <?php if (!empty($trendData)): ?>
        // Income Source Trend Chart
        const trendCtx = document.getElementById('income-source-trend-chart').getContext('2d');
        const trendData = <?= json_encode($trendData) ?>;
        
        // Extract unique periods and sources
        const periods = [...new Set(trendData.map(item => item.period_name))];
        const sources = [...new Set(trendData.map(item => item.source_name))];
        
        // Prepare datasets
        const datasets = sources.map((source, index) => {
            const sourceData = trendData.filter(item => item.source_name === source);
            const data = periods.map(period => {
                const match = sourceData.find(item => item.period_name === period);
                return match ? parseFloat(match.total_income) : 0;
            });
            
            const hue = (index * 137) % 360;
            const color = `hsl(${hue}, 70%, 60%)`;
            
            return {
                label: source,
                data: data,
                backgroundColor: `hsla(${hue}, 70%, 60%, 0.2)`,
                borderColor: color,
                borderWidth: 2,
                tension: 0.4
            };
        });
        
        new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: periods,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rs ' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += 'Rs ' + context.raw.toLocaleString();
                                return label;
                            }
                        }
                    }
                }
            }
        });
        <?php endif; ?>
    });

    // Export buttons
    document.getElementById('export-pdf').addEventListener('click', function() {
        window.location.href = '/momentum/finances/reports/export/income-source-analysis/pdf?period=<?= $filters['period'] ?>&year=<?= $filters['year'] ?><?= isset($filters['start_date']) ? '&start_date=' . $filters['start_date'] : '' ?><?= isset($filters['end_date']) ? '&end_date=' . $filters['end_date'] : '' ?>';
    });

    document.getElementById('export-csv').addEventListener('click', function() {
        window.location.href = '/momentum/finances/reports/export/income-source-analysis/csv?period=<?= $filters['period'] ?>&year=<?= $filters['year'] ?><?= isset($filters['start_date']) ? '&start_date=' . $filters['start_date'] : '' ?><?= isset($filters['end_date']) ? '&end_date=' . $filters['end_date'] : '' ?>';
    });
</script>
<?php endif; ?>
