<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Income vs Expenses Report</h1>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/finances/reports" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Reports
                </a>
                <a href="/momentum/finances/reports/export/income-vs-expenses/pdf?period=<?= $filters['period'] ?>&year=<?= $filters['year'] ?>" id="export-pdf" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-file-pdf mr-2"></i> Export PDF
                </a>
                <a href="/momentum/finances/reports/export/income-vs-expenses/csv?period=<?= $filters['period'] ?>&year=<?= $filters['year'] ?>" id="export-csv" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-file-csv mr-2"></i> Export CSV
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <form action="/momentum/finances/reports/income-vs-expenses" method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="period" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Period</label>
                    <select id="period" name="period" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                        <option value="monthly" <?= $filters['period'] === 'monthly' ? 'selected' : '' ?>>Monthly</option>
                        <option value="quarterly" <?= $filters['period'] === 'quarterly' ? 'selected' : '' ?>>Quarterly</option>
                        <option value="yearly" <?= $filters['period'] === 'yearly' ? 'selected' : '' ?>>Yearly</option>
                        <option value="custom" <?= $filters['period'] === 'custom' ? 'selected' : '' ?>>Custom Range</option>
                    </select>
                </div>
                <div id="year-filter" class="<?= $filters['period'] === 'custom' ? 'hidden' : '' ?>">
                    <label for="year" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Year</label>
                    <select id="year" name="year" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                        <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                            <option value="<?= $y ?>" <?= $filters['year'] == $y ? 'selected' : '' ?>><?= $y ?></option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div id="date-range-filter" class="<?= $filters['period'] !== 'custom' ? 'hidden' : '' ?> md:col-span-2">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                            <input type="date" id="start_date" name="start_date" value="<?= $filters['start_date'] ?? '' ?>" class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                        </div>
                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">End Date</label>
                            <input type="date" id="end_date" name="end_date" value="<?= $filters['end_date'] ?? '' ?>" class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                        </div>
                    </div>
                </div>
                <div class="flex items-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-filter mr-2"></i> Apply Filters
                    </button>
                </div>
            </form>
        </div>

        <!-- Income vs Expenses Chart -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Income vs Expenses</h2>
            <div class="h-80">
                <canvas id="incomeExpensesChart"></canvas>
            </div>
        </div>

        <!-- Summary -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Summary</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Income</h3>
                    <p class="mt-1 text-2xl font-semibold text-green-600 dark:text-green-400">
                        <?php
                        $totalIncome = 0;
                        if ($filters['period'] === 'custom') {
                            $totalIncome = $reportData['total_income'] ?? 0;
                        } else {
                            foreach ($reportData as $data) {
                                $totalIncome += $data['total_income'] ?? 0;
                            }
                        }
                        echo View::formatCurrency($totalIncome);
                        ?>
                    </p>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Expenses</h3>
                    <p class="mt-1 text-2xl font-semibold text-red-600 dark:text-red-400">
                        <?php
                        $totalExpenses = 0;
                        if ($filters['period'] === 'custom') {
                            $totalExpenses = $reportData['total_expense'] ?? 0;
                        } else {
                            foreach ($reportData as $data) {
                                $totalExpenses += $data['total_expense'] ?? 0;
                            }
                        }
                        echo View::formatCurrency($totalExpenses);
                        ?>
                    </p>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Net Balance</h3>
                    <p class="mt-1 text-2xl font-semibold <?= $totalIncome - $totalExpenses >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                        <?= View::formatCurrency($totalIncome - $totalExpenses) ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Data Table -->
        <?php if ($filters['period'] !== 'custom'): ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Detailed Data</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    <?= $filters['period'] === 'monthly' ? 'Month' : ($filters['period'] === 'quarterly' ? 'Quarter' : 'Year') ?>
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Income</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Expenses</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Net</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Income/Expense Ratio</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($reportData as $data): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                        <?= $filters['period'] === 'monthly' ? $data['month_name'] : ($filters['period'] === 'quarterly' ? $data['quarter_name'] : $data['year']) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-green-600 dark:text-green-400">
                                        <?= View::formatCurrency($data['total_income'] ?? 0) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-red-600 dark:text-red-400">
                                        <?= View::formatCurrency($data['total_expense'] ?? 0) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right <?= ($data['total_income'] ?? 0) - ($data['total_expense'] ?? 0) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                                        <?= View::formatCurrency(($data['total_income'] ?? 0) - ($data['total_expense'] ?? 0)) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
                                        <?php
                                        $ratio = ($data['total_expense'] ?? 0) > 0 ? ($data['total_income'] ?? 0) / ($data['total_expense'] ?? 0) : 0;
                                        echo number_format($ratio, 2);
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle filters based on period selection
        const periodSelect = document.getElementById('period');
        const yearFilter = document.getElementById('year-filter');
        const dateRangeFilter = document.getElementById('date-range-filter');

        periodSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                yearFilter.classList.add('hidden');
                dateRangeFilter.classList.remove('hidden');
            } else {
                yearFilter.classList.remove('hidden');
                dateRangeFilter.classList.add('hidden');
            }
        });

        // Chart setup
        const ctx = document.getElementById('incomeExpensesChart').getContext('2d');
        
        <?php if ($filters['period'] !== 'custom'): ?>
            // Prepare data for chart
            const labels = [];
            const incomeData = [];
            const expenseData = [];
            const netData = [];

            <?php foreach ($reportData as $data): ?>
                labels.push('<?= $filters['period'] === 'monthly' ? $data['month_name'] : ($filters['period'] === 'quarterly' ? $data['quarter_name'] : $data['year']) ?>');
                incomeData.push(<?= $data['total_income'] ?? 0 ?>);
                expenseData.push(<?= $data['total_expense'] ?? 0 ?>);
                netData.push(<?= ($data['total_income'] ?? 0) - ($data['total_expense'] ?? 0) ?>);
            <?php endforeach; ?>

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Income',
                            data: incomeData,
                            backgroundColor: 'rgba(34, 197, 94, 0.5)',
                            borderColor: 'rgb(34, 197, 94)',
                            borderWidth: 1
                        },
                        {
                            label: 'Expenses',
                            data: expenseData,
                            backgroundColor: 'rgba(239, 68, 68, 0.5)',
                            borderColor: 'rgb(239, 68, 68)',
                            borderWidth: 1
                        },
                        {
                            label: 'Net',
                            data: netData,
                            type: 'line',
                            fill: false,
                            borderColor: 'rgb(59, 130, 246)',
                            tension: 0.1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: document.querySelector('html').classList.contains('dark') ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                color: document.querySelector('html').classList.contains('dark') ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                                callback: function(value) {
                                    return 'Rs ' + value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            grid: {
                                color: document.querySelector('html').classList.contains('dark') ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                color: document.querySelector('html').classList.contains('dark') ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: {
                                color: document.querySelector('html').classList.contains('dark') ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)'
                            }
                        }
                    }
                }
            });
        <?php else: ?>
            // For custom period, show a simple summary chart
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Summary'],
                    datasets: [
                        {
                            label: 'Income',
                            data: [<?= $reportData['total_income'] ?? 0 ?>],
                            backgroundColor: 'rgba(34, 197, 94, 0.5)',
                            borderColor: 'rgb(34, 197, 94)',
                            borderWidth: 1
                        },
                        {
                            label: 'Expenses',
                            data: [<?= $reportData['total_expense'] ?? 0 ?>],
                            backgroundColor: 'rgba(239, 68, 68, 0.5)',
                            borderColor: 'rgb(239, 68, 68)',
                            borderWidth: 1
                        },
                        {
                            label: 'Net',
                            data: [<?= ($reportData['total_income'] ?? 0) - ($reportData['total_expense'] ?? 0) ?>],
                            backgroundColor: 'rgba(59, 130, 246, 0.5)',
                            borderColor: 'rgb(59, 130, 246)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: document.querySelector('html').classList.contains('dark') ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                color: document.querySelector('html').classList.contains('dark') ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                                callback: function(value) {
                                    return 'Rs ' + value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            grid: {
                                color: document.querySelector('html').classList.contains('dark') ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                color: document.querySelector('html').classList.contains('dark') ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: {
                                color: document.querySelector('html').classList.contains('dark') ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)'
                            }
                        }
                    }
                }
            });
        <?php endif; ?>
    });
</script>
