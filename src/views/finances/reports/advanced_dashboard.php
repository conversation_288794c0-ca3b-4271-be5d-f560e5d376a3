<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Advanced Financial Dashboard</h1>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/finances/reports" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Reports
                </a>
                <button id="export-pdf" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-file-pdf mr-2"></i> Export PDF
                </button>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <form action="/momentum/finances/reports/advanced-dashboard" method="GET" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                    <div class="flex-1">
                        <label for="year" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Year</label>
                        <select id="year" name="year" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                            <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                                <option value="<?= $y ?>" <?= $filters['year'] == $y ? 'selected' : '' ?>><?= $y ?></option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <div>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Financial Health Score -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Financial Health Score</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Overall Score -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                        <h3 class="text-base font-medium text-gray-900 dark:text-white mb-2">Overall Score</h3>
                        <div class="inline-flex items-center justify-center w-24 h-24 rounded-full bg-white dark:bg-gray-800 shadow-md mb-2">
                            <span class="text-3xl font-bold <?= $financialHealth['overall_score'] >= 70 ? 'text-green-600 dark:text-green-400' : ($financialHealth['overall_score'] >= 40 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') ?>">
                                <?= $financialHealth['overall_score'] ?>
                            </span>
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            <?= $financialHealth['overall_score'] >= 70 ? 'Excellent' : ($financialHealth['overall_score'] >= 40 ? 'Good' : 'Needs Improvement') ?>
                        </p>
                    </div>

                    <!-- Income vs Expense Ratio -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-base font-medium text-gray-900 dark:text-white mb-2">Income/Expense Ratio</h3>
                        <div class="flex items-baseline">
                            <span class="text-2xl font-semibold <?= $financialHealth['income_expense_ratio'] >= 1.5 ? 'text-green-600 dark:text-green-400' : ($financialHealth['income_expense_ratio'] >= 1 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') ?>">
                                <?= number_format($financialHealth['income_expense_ratio'], 2) ?>
                            </span>
                            <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">
                                (<?= $financialHealth['income_expense_ratio'] >= 1.5 ? 'Excellent' : ($financialHealth['income_expense_ratio'] >= 1 ? 'Good' : 'Needs Improvement') ?>)
                            </span>
                        </div>
                        <div class="mt-2 w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                            <div class="h-2.5 rounded-full <?= $financialHealth['income_expense_ratio'] >= 1.5 ? 'bg-green-600' : ($financialHealth['income_expense_ratio'] >= 1 ? 'bg-yellow-600' : 'bg-red-600') ?>" style="width: <?= min(100, $financialHealth['income_expense_ratio'] * 50) ?>%"></div>
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Target: 1.5 or higher</p>
                    </div>

                    <!-- Savings Rate -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-base font-medium text-gray-900 dark:text-white mb-2">Savings Rate</h3>
                        <div class="flex items-baseline">
                            <span class="text-2xl font-semibold <?= $financialHealth['savings_rate'] >= 20 ? 'text-green-600 dark:text-green-400' : ($financialHealth['savings_rate'] >= 10 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') ?>">
                                <?= number_format($financialHealth['savings_rate'], 1) ?>%
                            </span>
                            <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">
                                (<?= $financialHealth['savings_rate'] >= 20 ? 'Excellent' : ($financialHealth['savings_rate'] >= 10 ? 'Good' : 'Needs Improvement') ?>)
                            </span>
                        </div>
                        <div class="mt-2 w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                            <div class="h-2.5 rounded-full <?= $financialHealth['savings_rate'] >= 20 ? 'bg-green-600' : ($financialHealth['savings_rate'] >= 10 ? 'bg-yellow-600' : 'bg-red-600') ?>" style="width: <?= min(100, $financialHealth['savings_rate'] * 2.5) ?>%"></div>
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Target: 20% or higher</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Income and Expense Trends -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- Monthly Income Trend -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Monthly Income Trend</h2>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <?php if (empty($monthlyData)): ?>
                        <div class="text-center py-4">
                            <p class="text-gray-500 dark:text-gray-400">No data available for the selected year</p>
                        </div>
                    <?php else: ?>
                        <div class="h-80">
                            <canvas id="monthly-income-chart"></canvas>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Monthly Expense Trend -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Monthly Expense Trend</h2>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <?php if (empty($monthlyData)): ?>
                        <div class="text-center py-4">
                            <p class="text-gray-500 dark:text-gray-400">No data available for the selected year</p>
                        </div>
                    <?php else: ?>
                        <div class="h-80">
                            <canvas id="monthly-expense-chart"></canvas>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Category and Payment Method Analysis -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- Top Spending Categories -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Top Spending Categories</h2>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <?php if (empty($topCategories)): ?>
                        <div class="text-center py-4">
                            <p class="text-gray-500 dark:text-gray-400">No category data available for the selected year</p>
                        </div>
                    <?php else: ?>
                        <div class="h-80">
                            <canvas id="category-chart"></canvas>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Payment Method Distribution -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Payment Method Distribution</h2>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <?php if (empty($paymentMethods)): ?>
                        <div class="text-center py-4">
                            <p class="text-gray-500 dark:text-gray-400">No payment method data available for the selected year</p>
                        </div>
                    <?php else: ?>
                        <div class="h-80">
                            <canvas id="payment-method-chart"></canvas>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Income Source and Budget Performance -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- Income Source Distribution -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Income Source Distribution</h2>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <?php if (empty($incomeSources)): ?>
                        <div class="text-center py-4">
                            <p class="text-gray-500 dark:text-gray-400">No income source data available for the selected year</p>
                        </div>
                    <?php else: ?>
                        <div class="h-80">
                            <canvas id="income-source-chart"></canvas>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Budget Performance -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Budget Performance</h2>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <?php if (empty($budgetPerformance)): ?>
                        <div class="text-center py-4">
                            <p class="text-gray-500 dark:text-gray-400">No budget data available for the selected year</p>
                        </div>
                    <?php else: ?>
                        <div class="h-80">
                            <canvas id="budget-performance-chart"></canvas>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Financial Insights -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Financial Insights</h2>
                <a href="/momentum/finances/reports/financial-insights" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                    <span>View AI-Powered Insights</span>
                    <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($insights)): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No insights available for the selected year</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($insights as $insight): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-lightbulb text-yellow-500 text-xl"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-base font-medium text-gray-900 dark:text-white"><?= View::escape($insight['title']) ?></h3>
                                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400"><?= View::escape($insight['description']) ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($monthlyData) || !empty($topCategories) || !empty($paymentMethods) || !empty($incomeSources) || !empty($budgetPerformance)): ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Generate colors based on the number of items
        const generateColors = (count) => {
            const colors = [];
            for (let i = 0; i < count; i++) {
                const hue = (i * 137) % 360; // Use golden angle approximation for nice distribution
                colors.push(`hsl(${hue}, 70%, 60%)`);
            }
            return colors;
        };

        <?php if (!empty($monthlyData)): ?>
        // Monthly Income Chart
        const incomeCtx = document.getElementById('monthly-income-chart').getContext('2d');
        const monthlyData = <?= json_encode($monthlyData) ?>;
        const months = monthlyData.map(item => item.month_name);
        const incomeData = monthlyData.map(item => parseFloat(item.total_income || 0));

        new Chart(incomeCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [{
                    label: 'Income',
                    data: incomeData,
                    backgroundColor: 'rgba(34, 197, 94, 0.2)',
                    borderColor: 'rgba(34, 197, 94, 1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rs ' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += 'Rs ' + context.raw.toLocaleString();
                                return label;
                            }
                        }
                    }
                }
            }
        });

        // Monthly Expense Chart
        const expenseCtx = document.getElementById('monthly-expense-chart').getContext('2d');
        const expenseData = monthlyData.map(item => parseFloat(item.total_expense || 0));

        new Chart(expenseCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [{
                    label: 'Expenses',
                    data: expenseData,
                    backgroundColor: 'rgba(239, 68, 68, 0.2)',
                    borderColor: 'rgba(239, 68, 68, 1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rs ' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += 'Rs ' + context.raw.toLocaleString();
                                return label;
                            }
                        }
                    }
                }
            }
        });
        <?php endif; ?>

        <?php if (!empty($topCategories)): ?>
        // Category Chart
        const categoryCtx = document.getElementById('category-chart').getContext('2d');
        const categoryData = <?= json_encode($topCategories) ?>;
        const categories = categoryData.map(item => item.category);
        const categoryAmounts = categoryData.map(item => parseFloat(item.total_amount || 0));
        const categoryColors = generateColors(categories.length);

        new Chart(categoryCtx, {
            type: 'doughnut',
            data: {
                labels: categories,
                datasets: [{
                    data: categoryAmounts,
                    backgroundColor: categoryColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: Rs ${value.toLocaleString()} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
        <?php endif; ?>

        <?php if (!empty($paymentMethods)): ?>
        // Payment Method Chart
        const paymentCtx = document.getElementById('payment-method-chart').getContext('2d');
        const paymentData = <?= json_encode($paymentMethods) ?>;
        const methods = paymentData.map(item => item.payment_method);
        const methodAmounts = paymentData.map(item => parseFloat(item.total_amount || 0));
        const methodColors = generateColors(methods.length);

        new Chart(paymentCtx, {
            type: 'pie',
            data: {
                labels: methods,
                datasets: [{
                    data: methodAmounts,
                    backgroundColor: methodColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: Rs ${value.toLocaleString()} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
        <?php endif; ?>

        <?php if (!empty($incomeSources)): ?>
        // Income Source Chart
        const sourceCtx = document.getElementById('income-source-chart').getContext('2d');
        const sourceData = <?= json_encode($incomeSources) ?>;
        const sources = sourceData.map(item => item.name);
        const sourceAmounts = sourceData.map(item => parseFloat(item.total_income || 0));
        const sourceColors = generateColors(sources.length);

        new Chart(sourceCtx, {
            type: 'pie',
            data: {
                labels: sources,
                datasets: [{
                    data: sourceAmounts,
                    backgroundColor: sourceColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: Rs ${value.toLocaleString()} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
        <?php endif; ?>

        <?php if (!empty($budgetPerformance)): ?>
        // Budget Performance Chart
        const budgetCtx = document.getElementById('budget-performance-chart').getContext('2d');
        const budgetData = <?= json_encode($budgetPerformance) ?>;
        const budgetNames = budgetData.map(item => item.name);
        const budgetPercentages = budgetData.map(item => parseFloat(item.percentage || 0));
        const budgetColors = budgetPercentages.map(percentage =>
            percentage <= 85 ? 'rgba(34, 197, 94, 0.7)' :
            percentage <= 100 ? 'rgba(234, 179, 8, 0.7)' :
            'rgba(239, 68, 68, 0.7)'
        );

        new Chart(budgetCtx, {
            type: 'bar',
            data: {
                labels: budgetNames,
                datasets: [{
                    label: 'Budget Utilization',
                    data: budgetPercentages,
                    backgroundColor: budgetColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: Math.max(100, ...budgetPercentages) + 10,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += context.raw.toFixed(1) + '%';
                                return label;
                            }
                        }
                    }
                }
            }
        });
        <?php endif; ?>
    });

    // Export button
    document.getElementById('export-pdf').addEventListener('click', function() {
        window.location.href = '/momentum/finances/reports/export/advanced-dashboard/pdf?year=<?= $filters['year'] ?>';
    });
</script>
<?php endif; ?>
