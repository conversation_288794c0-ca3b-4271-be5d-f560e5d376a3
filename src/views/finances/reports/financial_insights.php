<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">AI-Powered Financial Insights</h1>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/finances/reports" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Reports
                </a>
                <a href="/momentum/finances/reports/export/financial-insights/pdf?period=<?= $filters['period'] ?>&time_value=<?= $filters['time_value'] ?>" id="export-pdf" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-file-pdf mr-2"></i> Export PDF
                </a>
            </div>
        </div>

        <!-- Analysis Period and Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Analysis Period</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div class="mb-4 md:mb-0">
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            <?php
                            $periodText = '';
                            switch ($analysisPeriod['period_type']) {
                                case 'monthly':
                                    $periodText = 'Last ' . $analysisPeriod['time_value'] . ' months';
                                    break;
                                case 'quarterly':
                                    $periodText = 'Last ' . $analysisPeriod['time_value'] . ' quarters';
                                    break;
                                case 'yearly':
                                    $periodText = 'Last ' . $analysisPeriod['time_value'] . ' years';
                                    break;
                                default:
                                    $periodText = 'Custom period';
                            }
                            ?>
                            <span class="font-medium text-gray-700 dark:text-gray-300"><?= $periodText ?></span>
                            (<?= date('M j, Y', strtotime($analysisPeriod['start_date'])) ?> - <?= date('M j, Y', strtotime($analysisPeriod['end_date'])) ?>)
                        </p>
                    </div>
                    <form action="/momentum/finances/reports/financial-insights" method="GET" class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                        <div class="flex items-center">
                            <label for="period" class="mr-2 text-sm text-gray-500 dark:text-gray-400">Period:</label>
                            <select id="period" name="period" class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50 text-sm">
                                <option value="monthly" <?= $filters['period'] === 'monthly' ? 'selected' : '' ?>>Monthly</option>
                                <option value="quarterly" <?= $filters['period'] === 'quarterly' ? 'selected' : '' ?>>Quarterly</option>
                                <option value="yearly" <?= $filters['period'] === 'yearly' ? 'selected' : '' ?>>Yearly</option>
                            </select>
                        </div>
                        <div class="flex items-center">
                            <label for="time_value" class="mr-2 text-sm text-gray-500 dark:text-gray-400">Time Range:</label>
                            <select id="time_value" name="time_value" class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50 text-sm">
                                <?php for ($i = 3; $i <= 24; $i += 3): ?>
                                    <option value="<?= $i ?>" <?= $filters['time_value'] == $i ? 'selected' : '' ?>><?= $i ?></option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        <button type="submit" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-filter mr-2"></i> Apply
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Financial Summary -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Financial Summary</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                        <h3 class="text-base font-medium text-green-800 dark:text-green-200 mb-2">Total Income</h3>
                        <p class="text-2xl font-semibold text-green-600 dark:text-green-400"><?= View::formatCurrency($summary['total_income'] ?? 0) ?></p>
                    </div>
                    <div class="bg-red-50 dark:bg-red-900 p-4 rounded-lg">
                        <h3 class="text-base font-medium text-red-800 dark:text-red-200 mb-2">Total Expenses</h3>
                        <p class="text-2xl font-semibold text-red-600 dark:text-red-400"><?= View::formatCurrency($summary['total_expense'] ?? 0) ?></p>
                    </div>
                    <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                        <h3 class="text-base font-medium text-blue-800 dark:text-blue-200 mb-2">Net Balance</h3>
                        <p class="text-2xl font-semibold <?= ($summary['total_income'] ?? 0) - ($summary['total_expense'] ?? 0) >= 0 ? 'text-blue-600 dark:text-blue-400' : 'text-red-600 dark:text-red-400' ?>">
                            <?= View::formatCurrency(($summary['total_income'] ?? 0) - ($summary['total_expense'] ?? 0)) ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Spending Patterns -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Spending Patterns</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($spendingPatterns)): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No spending patterns detected for the selected period</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-6">
                        <!-- Recurring Expenses -->
                        <?php if (!empty($spendingPatterns['recurring'])): ?>
                            <div>
                                <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3">Recurring Expenses</h3>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                                        <thead class="bg-gray-100 dark:bg-gray-800">
                                            <tr>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Category</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Amount</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Frequency</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                                            <?php foreach ($spendingPatterns['recurring'] as $expense): ?>
                                                <tr>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"><?= View::escape($expense['category']) ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"><?= View::escape($expense['description']) ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"><?= View::formatCurrency($expense['amount']) ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"><?= View::escape($expense['frequency']) ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Category Trends -->
                        <?php if (!empty($spendingPatterns['category_trends'])): ?>
                            <div>
                                <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3">Category Spending Trends</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <?php foreach ($spendingPatterns['category_trends'] as $category => $trend): ?>
                                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                            <div class="flex items-center justify-between mb-2">
                                                <h4 class="text-sm font-medium text-gray-900 dark:text-white"><?= View::escape($category) ?></h4>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $trend['trend'] === 'increasing' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' ?>">
                                                    <?= $trend['trend'] === 'increasing' ? '<i class="fas fa-arrow-up mr-1"></i> Increasing' : '<i class="fas fa-arrow-down mr-1"></i> Decreasing' ?>
                                                </span>
                                            </div>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                                <?= $trend['trend'] === 'increasing' ? 'Up' : 'Down' ?> by <?= number_format($trend['percent_change'], 1) ?>% over the period
                                            </p>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Day of Week Patterns -->
                        <?php if (!empty($spendingPatterns['day_of_week'])): ?>
                            <div>
                                <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3">Day of Week Spending Patterns</h3>
                                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">
                                        Your highest spending day is <span class="font-medium text-gray-700 dark:text-gray-300"><?= $spendingPatterns['day_of_week']['highest_day'] ?></span>
                                        (avg. <?= View::formatCurrency($spendingPatterns['day_of_week']['highest_amount']) ?>)
                                    </p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        Your lowest spending day is <span class="font-medium text-gray-700 dark:text-gray-300"><?= $spendingPatterns['day_of_week']['lowest_day'] ?></span>
                                        (avg. <?= View::formatCurrency($spendingPatterns['day_of_week']['lowest_amount']) ?>)
                                    </p>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Impulse Spending -->
                        <?php if (!empty($spendingPatterns['impulse'])): ?>
                            <div>
                                <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3">Potential Impulse Spending</h3>
                                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">
                                        You've made <?= count($spendingPatterns['impulse']) ?> small, frequent purchases that may be impulse spending.
                                    </p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        Total amount: <span class="font-medium text-gray-700 dark:text-gray-300">
                                            <?= View::formatCurrency(array_sum(array_column($spendingPatterns['impulse'], 'amount'))) ?>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Spending Anomalies -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Spending Anomalies</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($anomalies['category_anomalies']) && empty($anomalies['transaction_anomalies'])): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No spending anomalies detected for the selected period</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-6">
                        <!-- Category Anomalies -->
                        <?php if (!empty($anomalies['category_anomalies'])): ?>
                            <div>
                                <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3">Category Spending Anomalies</h3>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                                        <thead class="bg-gray-100 dark:bg-gray-800">
                                            <tr>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Category</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Current Amount</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Average Amount</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Difference</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                                            <?php foreach ($anomalies['category_anomalies'] as $anomaly): ?>
                                                <tr>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"><?= View::escape($anomaly['category']) ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"><?= View::formatCurrency($anomaly['current_amount']) ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"><?= View::formatCurrency($anomaly['avg_amount']) ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600 dark:text-red-400">
                                                        +<?= number_format($anomaly['percent_above_avg'], 1) ?>%
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Transaction Anomalies -->
                        <?php if (!empty($anomalies['transaction_anomalies'])): ?>
                            <div>
                                <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3">Unusual Transactions</h3>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                                        <thead class="bg-gray-100 dark:bg-gray-800">
                                            <tr>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Category</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Amount</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Avg. Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                                            <?php foreach ($anomalies['transaction_anomalies'] as $anomaly): ?>
                                                <tr>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"><?= date('M j, Y', strtotime($anomaly['date'])) ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"><?= View::escape($anomaly['category']) ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"><?= View::escape($anomaly['description']) ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600 dark:text-red-400"><?= View::formatCurrency($anomaly['amount']) ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"><?= View::formatCurrency($anomaly['avg_amount']) ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Expense Reduction Recommendations -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Expense Reduction Recommendations</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($recommendations)): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No recommendations available for the selected period</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($recommendations as $recommendation): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-lightbulb text-yellow-500 text-xl"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-white"><?= View::escape($recommendation['title']) ?></h3>
                                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400"><?= View::escape($recommendation['description']) ?></p>
                                        <?php if (!empty($recommendation['potential_savings'])): ?>
                                            <p class="mt-1 text-sm font-medium text-green-600 dark:text-green-400">
                                                Potential savings: <?= View::formatCurrency($recommendation['potential_savings']) ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($spendingPatterns) || !empty($anomalies)): ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add any charts or interactive elements here
    });
</script>
<?php endif; ?>
