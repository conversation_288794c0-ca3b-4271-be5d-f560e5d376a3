<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8"> <!-- Narrower container for focus -->
        <div class="flex items-center mb-6">
            <a href="/momentum/finances/goals/view/<?= $goal['id'] ?>" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Add Contribution to <?= View::escape($goal['name']) ?>
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <!-- Goal Summary -->
            <div class="px-4 py-5 sm:p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400">
                        <i class="fas <?= $goal['icon'] ?? 'fa-piggy-bank' ?> text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white"><?= View::escape($goal['name']) ?></h2>
                        <div class="mt-1 flex items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400">
                                <?= View::formatCurrency($goal['current_amount']) ?> of <?= View::formatCurrency($goal['target_amount']) ?>
                                (<?= round(($goal['current_amount'] / $goal['target_amount']) * 100, 1) ?>% complete)
                            </span>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                        <div class="bg-primary-600 h-2.5 rounded-full" style="width: <?= min(100, round(($goal['current_amount'] / $goal['target_amount']) * 100, 1)) ?>%"></div>
                    </div>
                </div>
            </div>

            <form action="/momentum/finances/goals/contribution/<?= $goal['id'] ?>" method="POST" class="p-6">
                <!-- Display validation errors if any -->
                <?php if (isset($errors) && !empty($errors)): ?>
                    <div class="mb-4 bg-red-50 dark:bg-red-900 p-4 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-400 dark:text-red-300"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                    Please fix the following errors:
                                </h3>
                                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                    <ul class="list-disc pl-5 space-y-1">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?= $error ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="space-y-6">
                    <!-- Contribution Amount -->
                    <div>
                        <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Contribution Amount (Rs)</label>
                        <div class="mt-1 currency-input-wrapper">
                            <div class="currency-symbol">
                                <span>Rs</span>
                            </div>
                            <input type="number" name="amount" id="amount" class="currency-input block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="0.00" min="0" step="0.01" value="<?= isset($data['amount']) ? View::escape($data['amount']) : '' ?>" required>
                        </div>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Remaining to reach goal: <?= View::formatCurrency(max(0, $goal['target_amount'] - $goal['current_amount'])) ?>
                        </p>
                    </div>

                    <!-- Contribution Date -->
                    <div>
                        <label for="contribution_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Contribution Date</label>
                        <input type="date" name="contribution_date" id="contribution_date" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" value="<?= isset($data['contribution_date']) ? View::escape($data['contribution_date']) : date('Y-m-d') ?>" required>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes (Optional)</label>
                        <textarea name="notes" id="notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="Add any notes about this contribution..."><?= isset($data['notes']) ? View::escape($data['notes']) : '' ?></textarea>
                    </div>

                    <!-- Milestone Notification -->
                    <?php
                        $nextMilestone = null;
                        $nextMilestoneAmount = PHP_FLOAT_MAX;

                        if (!empty($goal['milestones'])) {
                            foreach ($goal['milestones'] as $milestone) {
                                if (!$milestone['is_reached'] && $milestone['target_amount'] > $goal['current_amount'] && $milestone['target_amount'] < $nextMilestoneAmount) {
                                    $nextMilestone = $milestone;
                                    $nextMilestoneAmount = $milestone['target_amount'];
                                }
                            }
                        }

                        if ($nextMilestone) {
                            $amountToMilestone = $nextMilestone['target_amount'] - $goal['current_amount'];
                    ?>
                        <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-md">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-flag text-blue-400 dark:text-blue-300"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                                        Next Milestone: <?= View::escape($nextMilestone['name']) ?>
                                    </h3>
                                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                                        <p>You need <?= View::formatCurrency($amountToMilestone) ?> more to reach this milestone.</p>
                                        <button type="button" id="use-milestone-amount" class="mt-2 inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 dark:text-blue-200 bg-blue-100 dark:bg-blue-800 hover:bg-blue-200 dark:hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            Use this amount
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php } ?>

                    <!-- Goal Completion Notification -->
                    <?php
                        $amountToComplete = max(0, $goal['target_amount'] - $goal['current_amount']);
                        if ($amountToComplete > 0) {
                    ?>
                        <div class="bg-green-50 dark:bg-green-900 p-4 rounded-md">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-green-400 dark:text-green-300"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                                        Complete Your Goal
                                    </h3>
                                    <div class="mt-2 text-sm text-green-700 dark:text-green-300">
                                        <p>You need <?= View::formatCurrency($amountToComplete) ?> more to complete this goal.</p>
                                        <button type="button" id="use-completion-amount" class="mt-2 inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-green-700 dark:text-green-200 bg-green-100 dark:bg-green-800 hover:bg-green-200 dark:hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                            Use this amount
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php } ?>

                    <!-- Submit Button -->
                    <div class="flex justify-end pt-5 border-t border-gray-200 dark:border-gray-700">
                        <a href="/momentum/finances/goals/view/<?= $goal['id'] ?>" class="bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Cancel
                        </a>
                        <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Add Contribution
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const amountInput = document.getElementById('amount');

    // Use milestone amount button
    const useMilestoneAmountBtn = document.getElementById('use-milestone-amount');
    if (useMilestoneAmountBtn) {
        useMilestoneAmountBtn.addEventListener('click', function() {
            <?php if (isset($amountToMilestone)): ?>
            amountInput.value = <?= $amountToMilestone ?>;
            <?php endif; ?>
        });
    }

    // Use completion amount button
    const useCompletionAmountBtn = document.getElementById('use-completion-amount');
    if (useCompletionAmountBtn) {
        useCompletionAmountBtn.addEventListener('click', function() {
            <?php if (isset($amountToComplete)): ?>
            amountInput.value = <?= $amountToComplete ?>;
            <?php endif; ?>
        });
    }
});
</script>
