<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Simple header with clear action buttons -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div class="flex items-center mb-3 md:mb-0">
                <a href="/momentum/finances" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Financial Goals</h1>
            </div>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/finances/goals/create" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i> New Goal
                </a>
                <a href="/momentum/finances" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-chart-line mr-2"></i> Financial Overview
                </a>
            </div>
        </div>

        <!-- Goals Summary -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:p-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Goals Summary</h2>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <p class="text-sm text-gray-500 dark:text-gray-400">Total Goals</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?= $summary['total_goals'] ?? 0 ?></p>
                    </div>
                    <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                        <p class="text-sm text-blue-500 dark:text-blue-300">Active Goals</p>
                        <p class="text-2xl font-semibold text-blue-700 dark:text-blue-200"><?= $summary['active_goals'] ?? 0 ?></p>
                    </div>
                    <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                        <p class="text-sm text-green-500 dark:text-green-300">Completed Goals</p>
                        <p class="text-2xl font-semibold text-green-700 dark:text-green-200"><?= $summary['completed_goals'] ?? 0 ?></p>
                    </div>
                    <div class="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg">
                        <p class="text-sm text-purple-500 dark:text-purple-300">Total Saved</p>
                        <p class="text-2xl font-semibold text-purple-700 dark:text-purple-200"><?= View::formatCurrency($summary['total_saved_amount'] ?? 0) ?></p>
                        <p class="text-xs text-purple-500 dark:text-purple-300">
                            of <?= View::formatCurrency($summary['total_target_amount'] ?? 0) ?>
                            (<?= $summary['total_target_amount'] > 0 ? round(($summary['total_saved_amount'] / $summary['total_target_amount']) * 100, 1) : 0 ?>%)
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Filter Goals</h3>
                <form action="/momentum/finances/goals" method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                        <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Statuses</option>
                            <option value="active" <?= isset($filters['status']) && $filters['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="completed" <?= isset($filters['status']) && $filters['status'] === 'completed' ? 'selected' : '' ?>>Completed</option>
                            <option value="abandoned" <?= isset($filters['status']) && $filters['status'] === 'abandoned' ? 'selected' : '' ?>>Abandoned</option>
                        </select>
                    </div>
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
                        <select name="category" id="category" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Categories</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= View::escape($category) ?>" <?= isset($filters['category']) && $filters['category'] === $category ? 'selected' : '' ?>><?= View::escape($category) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Priority</label>
                        <select name="priority" id="priority" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Priorities</option>
                            <option value="high" <?= isset($filters['priority']) && $filters['priority'] === 'high' ? 'selected' : '' ?>>High</option>
                            <option value="medium" <?= isset($filters['priority']) && $filters['priority'] === 'medium' ? 'selected' : '' ?>>Medium</option>
                            <option value="low" <?= isset($filters['priority']) && $filters['priority'] === 'low' ? 'selected' : '' ?>>Low</option>
                        </select>
                    </div>
                    <div class="md:col-span-3 flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-filter mr-2"></i> Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Goals List -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Your Financial Goals</h3>
            </div>
            
            <?php if (empty($goals)): ?>
                <div class="p-8 text-center">
                    <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
                        <i class="fas fa-piggy-bank text-2xl text-gray-500 dark:text-gray-400"></i>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">No financial goals found</p>
                    <a href="/momentum/finances/goals/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">Create Your First Goal</a>
                </div>
            <?php else: ?>
                <div class="divide-y divide-gray-200 dark:divide-gray-700">
                    <?php foreach ($goals as $goal): ?>
                        <?php 
                            // Calculate progress percentage
                            $progressPercentage = $goal['target_amount'] > 0 ? min(100, round(($goal['current_amount'] / $goal['target_amount']) * 100, 1)) : 0;
                            
                            // Determine status color
                            $statusColor = 'gray';
                            if ($goal['status'] === 'active') $statusColor = 'blue';
                            if ($goal['status'] === 'completed') $statusColor = 'green';
                            if ($goal['status'] === 'abandoned') $statusColor = 'red';
                            
                            // Determine priority icon
                            $priorityIcon = 'fa-flag';
                            $priorityColor = 'gray';
                            if ($goal['priority'] === 'high') $priorityColor = 'red';
                            if ($goal['priority'] === 'medium') $priorityColor = 'yellow';
                            if ($goal['priority'] === 'low') $priorityColor = 'blue';
                        ?>
                        <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                <div class="flex items-start mb-2 md:mb-0">
                                    <div class="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-full bg-primary-100 dark:bg-primary-900 mr-4">
                                        <i class="fas <?= $goal['icon'] ?? 'fa-piggy-bank' ?> text-primary-600 dark:text-primary-400"></i>
                                    </div>
                                    <div>
                                        <div class="flex items-center">
                                            <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                                                <?= View::escape($goal['name']) ?>
                                            </h4>
                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<?= $statusColor ?>-100 dark:bg-<?= $statusColor ?>-900 text-<?= $statusColor ?>-800 dark:text-<?= $statusColor ?>-200">
                                                <?= ucfirst($goal['status']) ?>
                                            </span>
                                            <span class="ml-2 text-<?= $priorityColor ?>-500 dark:text-<?= $priorityColor ?>-400" title="<?= ucfirst($goal['priority']) ?> Priority">
                                                <i class="fas <?= $priorityIcon ?>"></i>
                                            </span>
                                        </div>
                                        <?php if (!empty($goal['category'])): ?>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                                Category: <?= View::escape($goal['category']) ?>
                                            </p>
                                        <?php endif; ?>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            Target: <?= View::formatCurrency($goal['target_amount']) ?> by <?= date('M j, Y', strtotime($goal['target_date'])) ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="flex flex-col md:items-end">
                                    <div class="flex items-center mb-1">
                                        <span class="text-lg font-semibold text-gray-900 dark:text-white mr-2">
                                            <?= View::formatCurrency($goal['current_amount']) ?>
                                        </span>
                                        <span class="text-sm text-gray-500 dark:text-gray-400">
                                            of <?= View::formatCurrency($goal['target_amount']) ?>
                                        </span>
                                    </div>
                                    <div class="w-full md:w-48 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-2">
                                        <div class="bg-primary-600 h-2.5 rounded-full" style="width: <?= $progressPercentage ?>%"></div>
                                    </div>
                                    <div class="flex space-x-2">
                                        <a href="/momentum/finances/goals/view/<?= $goal['id'] ?>" class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-xs font-medium rounded text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                            <i class="fas fa-eye mr-1"></i> View
                                        </a>
                                        <a href="/momentum/finances/goals/contribution/<?= $goal['id'] ?>" class="inline-flex items-center px-2.5 py-1.5 border border-transparent shadow-sm text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                                            <i class="fas fa-plus mr-1"></i> Add
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
