<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8"> <!-- Narrower container for focus -->
        <div class="flex items-center mb-6">
            <a href="/momentum/finances/goals/view/<?= $goal['id'] ?>" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Edit Goal: <?= View::escape($goal['name']) ?>
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <form action="/momentum/finances/goals/edit/<?= $goal['id'] ?>" method="POST" id="goal-form" class="p-6">
                <!-- Display validation errors if any -->
                <?php if (isset($errors) && !empty($errors)): ?>
                    <div class="mb-4 bg-red-50 dark:bg-red-900 p-4 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-400 dark:text-red-300"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                    Please fix the following errors:
                                </h3>
                                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                    <ul class="list-disc pl-5 space-y-1">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?= $error ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Goal Information -->
                <div class="space-y-6">
                    <!-- Goal Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Goal Name</label>
                        <input type="text" name="name" id="name" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="e.g., New Car, Emergency Fund, Vacation" value="<?= View::escape($goal['name']) ?>" required>
                    </div>

                    <!-- Goal Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description (Optional)</label>
                        <textarea name="description" id="description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="Describe your financial goal..."><?= View::escape($goal['description'] ?? '') ?></textarea>
                    </div>

                    <!-- Target Amount -->
                    <div>
                        <label for="target_amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target Amount (Rs)</label>
                        <div class="mt-1 currency-input-wrapper">
                            <div class="currency-symbol">
                                <span>Rs</span>
                            </div>
                            <input type="number" name="target_amount" id="target_amount" class="currency-input block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="0.00" min="0" step="0.01" value="<?= View::escape($goal['target_amount']) ?>" required>
                        </div>
                    </div>

                    <!-- Date Range -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                            <input type="date" name="start_date" id="start_date" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" value="<?= View::escape($goal['start_date']) ?>" required>
                        </div>
                        <div>
                            <label for="target_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target Date</label>
                            <input type="date" name="target_date" id="target_date" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" value="<?= View::escape($goal['target_date']) ?>" required>
                        </div>
                    </div>

                    <!-- Category and Priority -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Category (Optional)</label>
                            <select name="category" id="category" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select a category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= View::escape($category) ?>" <?= $goal['category'] === $category ? 'selected' : '' ?>><?= View::escape($category) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div>
                            <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Priority</label>
                            <select name="priority" id="priority" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="low" <?= $goal['priority'] === 'low' ? 'selected' : '' ?>>Low</option>
                                <option value="medium" <?= $goal['priority'] === 'medium' ? 'selected' : '' ?>>Medium</option>
                                <option value="high" <?= $goal['priority'] === 'high' ? 'selected' : '' ?>>High</option>
                            </select>
                        </div>
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                        <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="active" <?= $goal['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="completed" <?= $goal['status'] === 'completed' ? 'selected' : '' ?>>Completed</option>
                            <option value="abandoned" <?= $goal['status'] === 'abandoned' ? 'selected' : '' ?>>Abandoned</option>
                        </select>
                    </div>

                    <!-- Icon Selection -->
                    <div>
                        <label for="icon" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Icon</label>
                        <select name="icon" id="icon" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="fa-piggy-bank" <?= $goal['icon'] === 'fa-piggy-bank' ? 'selected' : '' ?>>💰 Piggy Bank</option>
                            <option value="fa-car" <?= $goal['icon'] === 'fa-car' ? 'selected' : '' ?>>🚗 Car</option>
                            <option value="fa-home" <?= $goal['icon'] === 'fa-home' ? 'selected' : '' ?>>🏠 Home</option>
                            <option value="fa-plane" <?= $goal['icon'] === 'fa-plane' ? 'selected' : '' ?>>✈️ Travel</option>
                            <option value="fa-graduation-cap" <?= $goal['icon'] === 'fa-graduation-cap' ? 'selected' : '' ?>>🎓 Education</option>
                            <option value="fa-heartbeat" <?= $goal['icon'] === 'fa-heartbeat' ? 'selected' : '' ?>>❤️ Health</option>
                            <option value="fa-gift" <?= $goal['icon'] === 'fa-gift' ? 'selected' : '' ?>>🎁 Gift</option>
                            <option value="fa-laptop" <?= $goal['icon'] === 'fa-laptop' ? 'selected' : '' ?>>💻 Electronics</option>
                            <option value="fa-ring" <?= $goal['icon'] === 'fa-ring' ? 'selected' : '' ?>>💍 Wedding</option>
                            <option value="fa-baby" <?= $goal['icon'] === 'fa-baby' ? 'selected' : '' ?>>👶 Baby</option>
                            <option value="fa-umbrella-beach" <?= $goal['icon'] === 'fa-umbrella-beach' ? 'selected' : '' ?>>🏖️ Vacation</option>
                            <option value="fa-shield-alt" <?= $goal['icon'] === 'fa-shield-alt' ? 'selected' : '' ?>>🛡️ Emergency Fund</option>
                        </select>
                    </div>

                    <!-- Milestones Section -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Milestones</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Add key milestones to track your progress toward this goal</p>

                        <div id="milestones-container">
                            <!-- Existing Milestones -->
                            <?php if (!empty($goal['milestones'])): ?>
                                <?php foreach ($goal['milestones'] as $index => $milestone): ?>
                                    <div class="milestone-item border border-gray-200 dark:border-gray-700 rounded-md p-4 mb-4">
                                        <div class="flex justify-between mb-2">
                                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Milestone <?= $milestone['is_reached'] ? '(Reached)' : '' ?>
                                            </h4>
                                            <?php if (!$milestone['is_reached']): ?>
                                                <button type="button" class="remove-milestone text-red-500 hover:text-red-700">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                        <div class="space-y-3">
                                            <input type="hidden" name="milestone_ids[]" value="<?= $milestone['id'] ?>">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                                                <input type="text" name="milestone_names[]" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="e.g., 25% Complete" value="<?= View::escape($milestone['name']) ?>" <?= $milestone['is_reached'] ? 'readonly' : '' ?>>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target Amount (Rs)</label>
                                                <div class="mt-1 currency-input-wrapper">
                                                    <div class="currency-symbol">
                                                        <span>Rs</span>
                                                    </div>
                                                    <input type="number" name="milestone_amounts[]" class="currency-input block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="0.00" min="0" step="0.01" value="<?= View::escape($milestone['target_amount']) ?>" <?= $milestone['is_reached'] ? 'readonly' : '' ?>>
                                                </div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target Date (Optional)</label>
                                                <input type="date" name="milestone_dates[]" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" value="<?= $milestone['target_date'] ? View::escape($milestone['target_date']) : '' ?>" <?= $milestone['is_reached'] ? 'readonly' : '' ?>>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes (Optional)</label>
                                                <textarea name="milestone_notes[]" rows="2" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="Additional details about this milestone..." <?= $milestone['is_reached'] ? 'readonly' : '' ?>><?= View::escape($milestone['notes'] ?? '') ?></textarea>
                                            </div>
                                            <?php if ($milestone['is_reached']): ?>
                                                <div class="mt-2 text-sm text-green-600 dark:text-green-400">
                                                    <i class="fas fa-check-circle mr-1"></i> Reached on <?= date('M j, Y', strtotime($milestone['reached_date'])) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>

                            <!-- Milestone template - will be cloned by JavaScript -->
                            <div class="milestone-template hidden">
                                <div class="milestone-item border border-gray-200 dark:border-gray-700 rounded-md p-4 mb-4">
                                    <div class="flex justify-between mb-2">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">New Milestone</h4>
                                        <button type="button" class="remove-milestone text-red-500 hover:text-red-700">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <div class="space-y-3">
                                        <input type="hidden" name="milestone_ids[]" value="">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                                            <input type="text" name="milestone_names[]" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="e.g., 25% Complete">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target Amount (Rs)</label>
                                            <div class="mt-1 currency-input-wrapper">
                                                <div class="currency-symbol">
                                                    <span>Rs</span>
                                                </div>
                                                <input type="number" name="milestone_amounts[]" class="currency-input block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="0.00" min="0" step="0.01">
                                            </div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target Date (Optional)</label>
                                            <input type="date" name="milestone_dates[]" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes (Optional)</label>
                                            <textarea name="milestone_notes[]" rows="2" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="Additional details about this milestone..."></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="button" id="add-milestone" class="mt-2 inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-plus mr-2"></i> Add Milestone
                        </button>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-between pt-5 border-t border-gray-200 dark:border-gray-700">
                        <div>
                            <button type="button" id="delete-goal" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                Delete Goal
                            </button>
                        </div>
                        <div class="flex">
                            <a href="/momentum/finances/goals/view/<?= $goal['id'] ?>" class="bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                Cancel
                            </a>
                            <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                Update Goal
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            Delete Goal
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                Are you sure you want to delete this goal? All data associated with this goal, including contributions and milestones, will be permanently removed. This action cannot be undone.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <a href="/momentum/finances/goals/delete/<?= $goal['id'] ?>" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                    Delete
                </a>
                <button type="button" id="cancel-delete" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const milestonesContainer = document.getElementById('milestones-container');
    const addMilestoneButton = document.getElementById('add-milestone');
    const milestoneTemplate = document.querySelector('.milestone-template');

    // Add milestone
    addMilestoneButton.addEventListener('click', function() {
        const newMilestone = milestoneTemplate.cloneNode(true);
        newMilestone.classList.remove('hidden', 'milestone-template');
        milestonesContainer.appendChild(newMilestone);

        // Add event listener to remove button
        const removeButton = newMilestone.querySelector('.remove-milestone');
        removeButton.addEventListener('click', function() {
            milestonesContainer.removeChild(newMilestone);
        });
    });

    // Add event listeners to existing remove buttons
    document.querySelectorAll('.remove-milestone').forEach(button => {
        button.addEventListener('click', function() {
            const milestoneItem = this.closest('.milestone-item');
            milestoneItem.remove();
        });
    });

    // Delete goal modal
    const deleteButton = document.getElementById('delete-goal');
    const deleteModal = document.getElementById('delete-modal');
    const cancelDelete = document.getElementById('cancel-delete');

    deleteButton.addEventListener('click', function() {
        deleteModal.classList.remove('hidden');
    });

    cancelDelete.addEventListener('click', function() {
        deleteModal.classList.add('hidden');
    });
});
</script>
