<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/finances/subscriptions" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Subscription Details
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <!-- Subscription Header -->
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                            <?= View::escape($subscription['name']) ?>
                        </h2>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            <?= ucfirst($subscription['billing_cycle']) ?> subscription
                        </p>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            <?= $subscription['status'] === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' ?>">
                            <?= ucfirst($subscription['status']) ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Subscription Details -->
            <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Amount -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Amount</h3>
                        <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                            <?= View::formatCurrency($subscription['amount']) ?>
                        </p>
                    </div>

                    <!-- Billing Cycle -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Billing Cycle</h3>
                        <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                            <?= ucfirst($subscription['billing_cycle']) ?>
                        </p>
                    </div>

                    <!-- Next Billing Date -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Next Billing Date</h3>
                        <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                            <?= View::formatDate($subscription['next_billing_date']) ?>
                            <?php
                            $today = new DateTime();
                            $nextBilling = new DateTime($subscription['next_billing_date']);
                            $diff = $today->diff($nextBilling);
                            $daysUntilBilling = $diff->days;
                            $isPast = $nextBilling < $today;
                            ?>
                            <span class="ml-2 text-sm font-medium
                                <?php if ($isPast): ?>
                                    text-red-600 dark:text-red-400
                                <?php elseif ($daysUntilBilling <= 7): ?>
                                    text-yellow-600 dark:text-yellow-400
                                <?php else: ?>
                                    text-green-600 dark:text-green-400
                                <?php endif; ?>">
                                <?php if ($isPast): ?>
                                    (<?= $daysUntilBilling ?> days overdue)
                                <?php else: ?>
                                    (in <?= $daysUntilBilling ?> days)
                                <?php endif; ?>
                            </span>
                        </p>
                    </div>

                    <!-- Last Billing Date -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Billing Date</h3>
                        <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                            <?= !empty($subscription['last_billing_date']) ? View::formatDate($subscription['last_billing_date']) : 'N/A' ?>
                        </p>
                    </div>

                    <!-- Category -->
                    <?php if (!empty($subscription['category'])): ?>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Category</h3>
                        <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                            <?= View::escape($subscription['category']) ?>
                        </p>
                    </div>
                    <?php endif; ?>

                    <!-- Created At -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</h3>
                        <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                            <?= View::formatDateTime($subscription['created_at']) ?>
                        </p>
                    </div>
                </div>

                <!-- Description -->
                <?php if (!empty($subscription['description'])): ?>
                <div class="mt-6">
                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</h3>
                    <div class="mt-2 p-4 bg-gray-50 dark:bg-gray-700 rounded-md">
                        <p class="text-gray-900 dark:text-white whitespace-pre-line">
                            <?= View::escape($subscription['description']) ?>
                        </p>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Features List -->
                <?php
                $featuresList = [];
                if (!empty($subscription['features_list'])) {
                    $featuresList = json_decode($subscription['features_list'], true);
                }

                if (!empty($featuresList)):
                ?>
                <div class="mt-6">
                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Features & Benefits</h3>
                    <div class="mt-2">
                        <?php if (!empty($featuresList['main_features'])): ?>
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                <?= !empty($featuresList['main_title']) ? View::escape($featuresList['main_title']) : 'Main Features' ?>
                            </h4>
                            <ul class="space-y-2">
                                <?php foreach ($featuresList['main_features'] as $feature): ?>
                                <li class="flex items-start">
                                    <span class="flex-shrink-0 h-5 w-5 text-green-500 dark:text-green-400">
                                        <i class="fas fa-check"></i>
                                    </span>
                                    <span class="ml-2 text-gray-700 dark:text-gray-300">
                                        <?= View::escape($feature) ?>
                                    </span>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($featuresList['additional_features'])): ?>
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                <?= !empty($featuresList['additional_title']) ? View::escape($featuresList['additional_title']) : 'Additional Benefits' ?>
                            </h4>
                            <ul class="space-y-2">
                                <?php foreach ($featuresList['additional_features'] as $feature): ?>
                                <li class="flex items-start">
                                    <span class="flex-shrink-0 h-5 w-5 text-green-500 dark:text-green-400">
                                        <i class="fas fa-check"></i>
                                    </span>
                                    <span class="ml-2 text-gray-700 dark:text-gray-300">
                                        <?= View::escape($feature) ?>
                                    </span>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Cost Analysis -->
                <div class="mt-8">
                    <h3 class="text-base font-medium text-gray-900 dark:text-white mb-4">Cost Analysis</h3>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-md p-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Monthly Cost</h4>
                                <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                                    <?php
                                    $monthlyCost = 0;
                                    switch ($subscription['billing_cycle']) {
                                        case 'monthly':
                                            $monthlyCost = $subscription['amount'];
                                            break;
                                        case 'quarterly':
                                            $monthlyCost = $subscription['amount'] / 3;
                                            break;
                                        case 'yearly':
                                            $monthlyCost = $subscription['amount'] / 12;
                                            break;
                                    }
                                    ?>
                                    <?= View::formatCurrency($monthlyCost) ?>
                                </p>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Yearly Cost</h4>
                                <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                                    <?php
                                    $yearlyCost = 0;
                                    switch ($subscription['billing_cycle']) {
                                        case 'monthly':
                                            $yearlyCost = $subscription['amount'] * 12;
                                            break;
                                        case 'quarterly':
                                            $yearlyCost = $subscription['amount'] * 4;
                                            break;
                                        case 'yearly':
                                            $yearlyCost = $subscription['amount'];
                                            break;
                                    }
                                    ?>
                                    <?= View::formatCurrency($yearlyCost) ?>
                                </p>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Daily Cost</h4>
                                <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                                    <?php
                                    $dailyCost = $yearlyCost / 365;
                                    ?>
                                    <?= View::formatCurrency($dailyCost) ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="px-4 py-4 sm:px-6 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
                <div class="flex justify-between">
                    <a href="/momentum/finances/edit-subscription/<?= $subscription['id'] ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-edit mr-2"></i> Edit Subscription
                    </a>
                    <a href="/momentum/finances/delete-subscription/<?= $subscription['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200" onclick="return confirm('Are you sure you want to delete this subscription?')">
                        <i class="fas fa-trash mr-2"></i> Delete
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
