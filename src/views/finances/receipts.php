<?php
require_once __DIR__ . '/../../utils/Session.php';
require_once __DIR__ . '/../../utils/View.php';
require_once __DIR__ . '/../../utils/Database.php';
require_once __DIR__ . '/../../models/Finance.php';
require_once __DIR__ . '/../../models/Receipt.php';
?>
<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div class="flex items-center">
                <a href="/momentum/finances#receipt-management" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                    Receipt Management
                </h1>
            </div>
            <div class="mt-4 md:mt-0 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/finances/create-transaction" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i> Add Transaction with Receipt
                </a>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Filter Receipts</h3>
                <form action="/momentum/finances/receipts" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="transaction_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Transaction Type
                        </label>
                        <select name="transaction_type" id="transaction_type" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="">All Types</option>
                            <option value="income" <?= isset($filters['transaction_type']) && $filters['transaction_type'] === 'income' ? 'selected' : '' ?>>Income</option>
                            <option value="expense" <?= isset($filters['transaction_type']) && $filters['transaction_type'] === 'expense' ? 'selected' : '' ?>>Expense</option>
                        </select>
                    </div>
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Category
                        </label>
                        <input type="text" name="category" id="category" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($filters['category']) ? View::escape($filters['category']) : '' ?>">
                    </div>
                    <div>
                        <label for="date_from" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Date From
                        </label>
                        <input type="date" name="date_from" id="date_from" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($filters['date_from']) ? View::escape($filters['date_from']) : '' ?>">
                    </div>
                    <div>
                        <label for="date_to" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Date To
                        </label>
                        <input type="date" name="date_to" id="date_to" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($filters['date_to']) ? View::escape($filters['date_to']) : '' ?>">
                    </div>
                    <div class="md:col-span-4 flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-filter mr-2"></i> Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Receipts List -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">All Receipts</h3>
            </div>

            <?php if (empty($receipts)): ?>
                <div class="px-4 py-5 sm:p-6 text-center">
                    <div class="text-gray-500 dark:text-gray-400 mb-4">
                        <i class="fas fa-receipt text-4xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No receipts found</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">
                        <?php if (!empty($filters)): ?>
                            Try adjusting your filters or
                        <?php endif; ?>
                        add a new transaction with a receipt to get started.
                    </p>
                    <a href="/momentum/finances/create-transaction" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i> Add Transaction with Receipt
                    </a>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Transaction</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Receipt</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Upload Date</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($receipts as $receipt): ?>
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        <?= date('M j, Y', strtotime($receipt['transaction_date'])) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 dark:text-white"><?= View::escape($receipt['transaction_category']) ?></div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            <?= $receipt['transaction_type'] === 'income' ? 'Income' : 'Expense' ?> -
                                            <?= View::formatCurrency($receipt['transaction_amount']) ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        <span class="truncate max-w-[150px] inline-block" title="<?= View::escape($receipt['file_name']) ?>">
                                            <?= View::escape($receipt['file_name']) ?>
                                        </span>
                                        <span class="text-xs text-gray-500 dark:text-gray-400">
                                            (<?= round($receipt['file_size'] / 1024, 1) ?> KB)
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        <?= date('M j, Y', strtotime($receipt['upload_date'])) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex justify-end space-x-2">
                                            <a href="/momentum/<?= $receipt['file_path'] ?>" target="_blank" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="/momentum/finances/transactions/<?= $receipt['transaction_id'] ?>/receipts" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" title="Manage">
                                                <i class="fas fa-cog"></i>
                                            </a>
                                            <a href="/momentum/finances/receipts/<?= $receipt['id'] ?>/delete" onclick="return confirm('Are you sure you want to delete this receipt?')" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
