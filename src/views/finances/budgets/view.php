<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/finances/budgets" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <?= View::escape($budget['name']) ?>
            </h1>
        </div>

        <!-- Budget Overview -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Budget Overview</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- Budget Period -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-base font-medium text-gray-900 dark:text-white mb-1">Budget Period</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                <?= View::formatDate($budget['start_date']) ?> to <?= View::formatDate($budget['end_date']) ?>
                            </p>
                            <?php
                            $today = date('Y-m-d');
                            $startDate = $budget['start_date'];
                            $endDate = $budget['end_date'];
                            $totalDays = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24);
                            $daysElapsed = min($totalDays, max(0, (strtotime($today) - strtotime($startDate)) / (60 * 60 * 24)));
                            $percentElapsed = ($totalDays > 0) ? ($daysElapsed / $totalDays) * 100 : 0;
                            ?>
                            <div class="mt-2">
                                <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                                    <span>Progress</span>
                                    <span><?= number_format($percentElapsed, 0) ?>% complete</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                                    <div class="h-1.5 rounded-full bg-blue-500" style="width: <?= $percentElapsed ?>%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Total Budget -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-base font-medium text-gray-900 dark:text-white mb-1">Total Budget</h3>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">
                                <?= View::formatCurrency($budgetProgress['total_budget']) ?>
                            </p>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                Across <?= count($budgetProgress['categories']) ?> categories
                            </p>
                        </div>
                    </div>

                    <!-- Spending Progress -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-base font-medium text-gray-900 dark:text-white mb-1">Spending Progress</h3>
                            <div class="flex items-center">
                                <p class="text-2xl font-bold text-gray-900 dark:text-white">
                                    <?= View::formatCurrency($budgetProgress['total_spent']) ?>
                                </p>
                                <p class="ml-2 text-sm text-gray-500 dark:text-gray-400">
                                    of <?= View::formatCurrency($budgetProgress['total_budget']) ?>
                                </p>
                            </div>
                            <div class="mt-2">
                                <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                                    <span>Spent</span>
                                    <span><?= number_format($budgetProgress['percentage'], 1) ?>%</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                                    <div class="h-1.5 rounded-full <?= $budgetProgress['percentage'] > 100 ? 'bg-red-500' : ($budgetProgress['percentage'] > 90 ? 'bg-yellow-500' : 'bg-green-500') ?>" style="width: <?= min(100, $budgetProgress['percentage']) ?>%"></div>
                                </div>
                            </div>
                            <?php
                            // Calculate ideal spending based on days elapsed
                            $idealSpendingPercentage = min(100, $percentElapsed);
                            $idealSpending = ($budgetProgress['total_budget'] * $idealSpendingPercentage) / 100;
                            $spendingDifference = $budgetProgress['total_spent'] - $idealSpending;
                            $isOverBudget = $spendingDifference > 0;
                            ?>
                            <div class="mt-2 text-xs">
                                <span class="<?= $isOverBudget ? 'text-red-500' : 'text-green-500' ?>">
                                    <?= $isOverBudget ? 'Rs ' . number_format(abs($spendingDifference), 2) . ' over ideal' : 'Rs ' . number_format(abs($spendingDifference), 2) . ' under ideal' ?>
                                </span>
                                <span class="text-gray-500 dark:text-gray-400 ml-1">
                                    (based on <?= number_format($percentElapsed, 0) ?>% of period)
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Category Breakdown -->
                <h3 class="text-base font-medium text-gray-900 dark:text-white mb-4">Category Breakdown</h3>
                <?php if (empty($budgetProgress['categories'])): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No budget categories defined</p>
                        <a href="/momentum/finances/budgets/edit/<?= $budget['id'] ?>" class="inline-flex items-center mt-2 text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                            <i class="fas fa-plus mr-1"></i> Add Categories
                        </a>
                    </div>
                <?php else: ?>
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-white sm:pl-6">Category</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">Budget</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">Spent</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">Remaining</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">Progress</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                                <?php foreach ($budgetProgress['categories'] as $category): ?>
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-white sm:pl-6">
                                            <?= View::escape($category['category']['category']) ?>
                                        </td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                                            <?= View::formatCurrency($category['category']['amount']) ?>
                                        </td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                                            <?= View::formatCurrency($category['spent']) ?>
                                        </td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm <?= $category['remaining'] < 0 ? 'text-red-500' : 'text-gray-500 dark:text-gray-400' ?>">
                                            <?= View::formatCurrency($category['remaining']) ?>
                                        </td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm">
                                            <div class="flex items-center">
                                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mr-2 flex-grow max-w-[100px]">
                                                    <div class="h-1.5 rounded-full <?= $category['percentage'] > 100 ? 'bg-red-500' : ($category['percentage'] > 90 ? 'bg-yellow-500' : 'bg-green-500') ?>" style="width: <?= min(100, $category['percentage']) ?>%"></div>
                                                </div>
                                                <span class="text-xs <?= $category['percentage'] > 100 ? 'text-red-500' : ($category['percentage'] > 90 ? 'text-yellow-500' : 'text-gray-500 dark:text-gray-400') ?>">
                                                    <?= number_format($category['percentage'], 1) ?>%
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <div class="mt-6 flex justify-end">
                    <a href="/momentum/finances/budgets" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 mr-3">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Budgets
                    </a>
                    <a href="/momentum/finances/budgets/edit/<?= $budget['id'] ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 mr-3">
                        <i class="fas fa-edit mr-2"></i> Edit Budget
                    </a>
                    <button type="button" onclick="confirmDeleteBudget(<?= $budget['id'] ?>, '<?= addslashes(View::escape($budget['name'])) ?>')" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                        <i class="fas fa-trash mr-2"></i> Delete Budget
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for budget deletion -->
<form id="delete-budget-form" method="GET" style="display: none;">
</form>

<script>
    function confirmDeleteBudget(id, name) {
        if (confirm('Are you sure you want to delete the budget "' + name + '"? This will also delete all categories and alerts associated with this budget.')) {
            const form = document.getElementById('delete-budget-form');
            form.action = `/momentum/finances/budgets/delete/${id}`;
            form.submit();
        }
    }
</script>
