<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Budget Planning</h1>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/finances/budgets/create" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i> New Budget
                </a>
                <a href="/momentum/finances/budgets/reports" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-chart-bar mr-2"></i> Reports
                </a>
                <a href="/momentum/finances" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Finances
                </a>
            </div>
        </div>

        <?php if ($activeBudget && $budgetProgress): ?>
            <!-- Active Budget Overview -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                            Active Budget: <?= View::escape($activeBudget['name']) ?>
                        </h2>
                        <div class="mt-2 md:mt-0 text-sm text-gray-500 dark:text-gray-400">
                            <?= View::formatDate($activeBudget['start_date']) ?> to <?= View::formatDate($activeBudget['end_date']) ?>
                        </div>
                    </div>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <!-- Overall Budget Progress -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="text-base font-medium text-gray-900 dark:text-white">Overall Budget</h3>
                            <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                <?= View::formatCurrency($budgetProgress['total_spent']) ?> / <?= View::formatCurrency($budgetProgress['total_budget']) ?>
                                <span class="ml-2 <?= $budgetProgress['percentage'] > 90 ? 'text-red-600 dark:text-red-400' : 'text-gray-500 dark:text-gray-400' ?>">
                                    (<?= number_format($budgetProgress['percentage'], 1) ?>%)
                                </span>
                            </div>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                            <div class="h-2.5 rounded-full <?= $budgetProgress['percentage'] > 90 ? 'bg-red-600' : 'bg-primary-600' ?>" style="width: <?= min(100, $budgetProgress['percentage']) ?>%"></div>
                        </div>
                    </div>

                    <!-- Category Progress -->
                    <?php if (!empty($budgetProgress['categories'])): ?>
                        <h3 class="text-base font-medium text-gray-900 dark:text-white mb-4">Category Breakdown</h3>
                        <div class="space-y-4">
                            <?php foreach ($budgetProgress['categories'] as $category): ?>
                                <div>
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?= View::escape($category['category']['category']) ?></span>
                                        <div class="text-sm">
                                            <span class="font-medium text-gray-700 dark:text-gray-300">
                                                <?= View::formatCurrency($category['spent']) ?> / <?= View::formatCurrency($category['category']['amount']) ?>
                                            </span>
                                            <span class="ml-2 <?= $category['percentage'] > 90 ? 'text-red-600 dark:text-red-400' : 'text-gray-500 dark:text-gray-400' ?>">
                                                (<?= number_format($category['percentage'], 1) ?>%)
                                            </span>
                                        </div>
                                    </div>
                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                        <div class="h-2.5 rounded-full <?= $category['percentage'] > 90 ? 'bg-red-600' : ($category['percentage'] > 75 ? 'bg-yellow-500' : 'bg-green-500') ?>" style="width: <?= min(100, $category['percentage']) ?>%"></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <p class="text-gray-500 dark:text-gray-400">No budget categories defined</p>
                            <a href="/momentum/finances/budgets/edit/<?= $activeBudget['id'] ?>" class="inline-flex items-center mt-2 text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                <i class="fas fa-plus mr-1"></i> Add Categories
                            </a>
                        </div>
                    <?php endif; ?>

                    <div class="mt-6 flex justify-end">
                        <a href="/momentum/finances/budgets/view/<?= $activeBudget['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-eye mr-2"></i> View Details
                        </a>
                        <a href="/momentum/finances/budgets/edit/<?= $activeBudget['id'] ?>" class="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-edit mr-2"></i> Edit Budget
                        </a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- No Active Budget -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                <div class="px-4 py-5 sm:p-6 text-center">
                    <div class="text-gray-500 dark:text-gray-400 mb-4">
                        <i class="fas fa-chart-pie text-4xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Active Budget</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">
                        You don't have an active budget for the current period.
                    </p>
                    <a href="/momentum/finances/budgets/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i> Create Your First Budget
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <!-- All Budgets -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">All Budgets</h2>
            </div>
            <?php if (empty($budgets)): ?>
                <div class="px-4 py-5 sm:p-6 text-center">
                    <p class="text-gray-500 dark:text-gray-400">No budgets found</p>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Name
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Period
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Status
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($budgets as $budget): ?>
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                        <?= View::escape($budget['name']) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <?= View::formatDate($budget['start_date']) ?> to <?= View::formatDate($budget['end_date']) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <?php
                                        $today = date('Y-m-d');
                                        $status = '';
                                        $statusClass = '';

                                        if ($budget['is_active'] && $today >= $budget['start_date'] && $today <= $budget['end_date']) {
                                            $status = 'Active';
                                            $statusClass = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                                        } elseif ($today < $budget['start_date']) {
                                            $status = 'Upcoming';
                                            $statusClass = 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
                                        } elseif ($today > $budget['end_date']) {
                                            $status = 'Past';
                                            $statusClass = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
                                        } else {
                                            $status = 'Inactive';
                                            $statusClass = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                                        }
                                        ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                                            <?= $status ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex justify-end space-x-2">
                                            <a href="/momentum/finances/budgets/view/<?= $budget['id'] ?>" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="/momentum/finances/budgets/edit/<?= $budget['id'] ?>" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="#" onclick="confirmDeleteBudget(<?= $budget['id'] ?>, '<?= addslashes(View::escape($budget['name'])) ?>')" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Hidden form for budget deletion -->
<form id="delete-budget-form" method="GET" style="display: none;">
</form>

<script>
    function confirmDeleteBudget(id, name) {
        if (confirm('Are you sure you want to delete the budget "' + name + '"? This will also delete all categories and alerts associated with this budget.')) {
            const form = document.getElementById('delete-budget-form');
            form.action = `/momentum/finances/budgets/delete/${id}`;
            form.submit();
        }
    }
</script>
