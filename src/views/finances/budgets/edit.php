<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/finances/budgets" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Edit Budget: <?= View::escape($budget['name']) ?>
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <form action="/momentum/finances/budgets/update/<?= $budget['id'] ?>" method="POST" id="budget-form" class="p-6">
                <!-- Display validation errors if any -->
                <?php if (isset($errors) && !empty($errors)): ?>
                    <div class="mb-4 bg-red-50 dark:bg-red-900 p-4 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-400 dark:text-red-300"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                    Please fix the following errors:
                                </h3>
                                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                    <ul class="list-disc pl-5 space-y-1">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?= $error ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Budget Details -->
                <div class="space-y-6">
                    <div>
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Budget Details</h2>
                        <div class="grid grid-cols-1 gap-6">
                            <!-- Budget Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Budget Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="name" id="name" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="e.g., Monthly Budget - June 2023" value="<?= View::escape($budget['name']) ?>" required>
                            </div>

                            <!-- Date Range -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Start Date <span class="text-red-500">*</span>
                                    </label>
                                    <input type="date" name="start_date" id="start_date" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= $budget['start_date'] ?>" required>
                                </div>
                                <div>
                                    <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        End Date <span class="text-red-500">*</span>
                                    </label>
                                    <input type="date" name="end_date" id="end_date" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= $budget['end_date'] ?>" required>
                                </div>
                            </div>

                            <!-- Description -->
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Description
                                </label>
                                <textarea name="description" id="description" rows="3" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="Optional description for this budget"><?= View::escape($budget['description'] ?? '') ?></textarea>
                            </div>

                            <!-- Is Active -->
                            <div class="flex items-center">
                                <input type="checkbox" name="is_active" id="is_active" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-gray-700 dark:bg-gray-700" <?= $budget['is_active'] ? 'checked' : '' ?>>
                                <label for="is_active" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Set as active budget
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button for Budget Details -->
                    <div class="flex justify-between">
                        <button type="button" onclick="confirmDeleteBudget(<?= $budget['id'] ?>, '<?= addslashes(View::escape($budget['name'])) ?>')" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                            <i class="fas fa-trash mr-2"></i> Delete Budget
                        </button>

                        <div>
                            <a href="/momentum/finances/budgets" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 mr-3">
                                Cancel
                            </a>
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                Update Budget
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Budget Categories -->
            <div class="border-t border-gray-200 dark:border-gray-700 p-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Budget Categories</h2>

                <!-- Category List -->
                <div class="mb-6">
                    <?php if (empty($budgetCategories)): ?>
                        <p class="text-gray-500 dark:text-gray-400 text-center py-4">No categories defined for this budget</p>
                    <?php else: ?>
                        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                            <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-white sm:pl-6">Category</th>
                                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">Amount</th>
                                        <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                                            <span class="sr-only">Actions</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                                    <?php foreach ($budgetCategories as $category): ?>
                                        <tr class="category-row" data-id="<?= $category['id'] ?>">
                                            <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-white sm:pl-6">
                                                <span class="category-display"><?= View::escape($category['category']) ?></span>
                                                <div class="category-edit hidden">
                                                    <select class="category-select rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                                        <?php foreach ($allCategories as $cat): ?>
                                                            <option value="<?= View::escape($cat) ?>" <?= $cat === $category['category'] ? 'selected' : '' ?>><?= View::escape($cat) ?></option>
                                                        <?php endforeach; ?>
                                                        <option value="other">Other (Custom)</option>
                                                    </select>
                                                </div>
                                            </td>
                                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                                                <span class="amount-display"><?= View::formatCurrency($category['amount']) ?></span>
                                                <div class="amount-edit hidden">
                                                    <div class="flex">
                                                        <div class="flex-none w-10 flex items-center justify-center bg-gray-100 dark:bg-gray-600 border border-r-0 border-gray-300 dark:border-gray-700 rounded-l-md">
                                                            <span class="text-gray-500 dark:text-gray-400 font-medium">Rs</span>
                                                        </div>
                                                        <input type="number" class="amount-input flex-grow rounded-none rounded-r-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" step="0.01" min="0" value="<?= $category['amount'] ?>">
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                                                <div class="flex justify-end space-x-2">
                                                    <button type="button" class="edit-category text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="save-category hidden text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button type="button" class="cancel-edit hidden text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                    <button type="button" class="delete-category text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Add New Category -->
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3">Add New Category</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Category
                            </label>
                            <select id="new-category" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                <option value="">Select a category</option>
                                <?php foreach ($allCategories as $category): ?>
                                    <option value="<?= View::escape($category) ?>"><?= View::escape($category) ?></option>
                                <?php endforeach; ?>
                                <option value="other">Other (Custom)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Amount (Rs)
                            </label>
                            <div class="flex">
                                <div class="flex-none w-10 flex items-center justify-center bg-gray-100 dark:bg-gray-600 border border-r-0 border-gray-300 dark:border-gray-700 rounded-l-md">
                                    <span class="text-gray-500 dark:text-gray-400 font-medium">Rs</span>
                                </div>
                                <input type="number" id="new-amount" step="0.01" min="0" class="flex-grow rounded-none rounded-r-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="0.00">
                            </div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center">
                        <input type="checkbox" id="enable-alert" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-gray-700 dark:bg-gray-700" checked>
                        <label for="enable-alert" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                            Enable budget alert at 80% of budget
                        </label>
                    </div>
                    <div class="mt-4">
                        <button type="button" id="add-category" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i> Add Category
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add new category
        const addCategoryBtn = document.getElementById('add-category');
        const newCategorySelect = document.getElementById('new-category');
        const newAmountInput = document.getElementById('new-amount');
        const enableAlertCheckbox = document.getElementById('enable-alert');

        addCategoryBtn.addEventListener('click', function() {
            const category = newCategorySelect.value;
            const amount = newAmountInput.value;
            const enableAlert = enableAlertCheckbox.checked ? 1 : 0;

            if (!category || !amount || isNaN(parseFloat(amount))) {
                alert('Please select a category and enter a valid amount');
                return;
            }

            // Send AJAX request to add category
            fetch(`/momentum/finances/budgets/add-category/<?= $budget['id'] ?>`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `category=${encodeURIComponent(category)}&amount=${encodeURIComponent(amount)}&enable_alert=${enableAlert}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to show the new category
                    window.location.reload();
                } else {
                    alert(data.message || 'Failed to add category');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while adding the category');
            });
        });

        // Handle custom category selection
        newCategorySelect.addEventListener('change', function() {
            if (this.value === 'other') {
                const customCategory = prompt('Enter custom category name:');
                if (customCategory) {
                    // Create a new option
                    const newOption = document.createElement('option');
                    newOption.value = customCategory;
                    newOption.textContent = customCategory;

                    // Add it to the select and select it
                    this.insertBefore(newOption, this.options[this.options.length - 1]);
                    this.value = customCategory;
                } else {
                    this.selectedIndex = 0;
                }
            }
        });

        // Edit category
        document.querySelectorAll('.edit-category').forEach(button => {
            button.addEventListener('click', function() {
                const row = this.closest('.category-row');

                // Show edit fields, hide display fields
                row.querySelector('.category-display').classList.add('hidden');
                row.querySelector('.category-edit').classList.remove('hidden');
                row.querySelector('.amount-display').classList.add('hidden');
                row.querySelector('.amount-edit').classList.remove('hidden');

                // Show save/cancel buttons, hide edit/delete buttons
                this.classList.add('hidden');
                row.querySelector('.delete-category').classList.add('hidden');
                row.querySelector('.save-category').classList.remove('hidden');
                row.querySelector('.cancel-edit').classList.remove('hidden');
            });
        });

        // Cancel edit
        document.querySelectorAll('.cancel-edit').forEach(button => {
            button.addEventListener('click', function() {
                const row = this.closest('.category-row');

                // Hide edit fields, show display fields
                row.querySelector('.category-display').classList.remove('hidden');
                row.querySelector('.category-edit').classList.add('hidden');
                row.querySelector('.amount-display').classList.remove('hidden');
                row.querySelector('.amount-edit').classList.add('hidden');

                // Hide save/cancel buttons, show edit/delete buttons
                row.querySelector('.edit-category').classList.remove('hidden');
                row.querySelector('.delete-category').classList.remove('hidden');
                this.classList.add('hidden');
                row.querySelector('.save-category').classList.add('hidden');
            });
        });

        // Save category changes
        document.querySelectorAll('.save-category').forEach(button => {
            button.addEventListener('click', function() {
                const row = this.closest('.category-row');
                const categoryId = row.dataset.id;
                const categorySelect = row.querySelector('.category-select');
                const amountInput = row.querySelector('.amount-input');

                const category = categorySelect.value;
                const amount = amountInput.value;

                if (!category || !amount || isNaN(parseFloat(amount))) {
                    alert('Please select a category and enter a valid amount');
                    return;
                }

                // Send AJAX request to update category
                fetch(`/momentum/finances/budgets/update-category/${categoryId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `category=${encodeURIComponent(category)}&amount=${encodeURIComponent(amount)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update display values
                        row.querySelector('.category-display').textContent = category;
                        row.querySelector('.amount-display').textContent = 'Rs ' + parseFloat(amount).toFixed(2);

                        // Hide edit fields, show display fields
                        row.querySelector('.category-display').classList.remove('hidden');
                        row.querySelector('.category-edit').classList.add('hidden');
                        row.querySelector('.amount-display').classList.remove('hidden');
                        row.querySelector('.amount-edit').classList.add('hidden');

                        // Hide save/cancel buttons, show edit/delete buttons
                        row.querySelector('.edit-category').classList.remove('hidden');
                        row.querySelector('.delete-category').classList.remove('hidden');
                        button.classList.add('hidden');
                        row.querySelector('.cancel-edit').classList.add('hidden');
                    } else {
                        alert(data.message || 'Failed to update category');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while updating the category');
                });
            });
        });

        // Delete category
        document.querySelectorAll('.delete-category').forEach(button => {
            button.addEventListener('click', function() {
                if (!confirm('Are you sure you want to delete this category?')) {
                    return;
                }

                const row = this.closest('.category-row');
                const categoryId = row.dataset.id;

                // Send AJAX request to delete category
                fetch(`/momentum/finances/budgets/delete-category/${categoryId}`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove the row from the table
                        row.remove();
                    } else {
                        alert(data.message || 'Failed to delete category');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the category');
                });
            });
        });

        // Handle custom category selection in edit mode
        document.querySelectorAll('.category-select').forEach(select => {
            select.addEventListener('change', function() {
                if (this.value === 'other') {
                    const customCategory = prompt('Enter custom category name:');
                    if (customCategory) {
                        // Create a new option
                        const newOption = document.createElement('option');
                        newOption.value = customCategory;
                        newOption.textContent = customCategory;

                        // Add it to the select and select it
                        this.insertBefore(newOption, this.options[this.options.length - 1]);
                        this.value = customCategory;
                    } else {
                        // Reset to the original value
                        const originalCategory = this.closest('.category-row').querySelector('.category-display').textContent.trim();
                        this.value = originalCategory;
                    }
                }
            });
        });
    });

    // Delete budget function
    function confirmDeleteBudget(id, name) {
        if (confirm('Are you sure you want to delete the budget "' + name + '"? This will also delete all categories and alerts associated with this budget.')) {
            const form = document.getElementById('delete-budget-form');
            form.action = `/momentum/finances/budgets/delete/${id}`;
            form.submit();
        }
    }
</script>

<!-- Hidden form for budget deletion -->
<form id="delete-budget-form" method="GET" style="display: none;">
</form>
