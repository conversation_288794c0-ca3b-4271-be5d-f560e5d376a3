<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Simple header with clear action buttons -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div class="flex items-center mb-3 md:mb-0">
                <a href="/momentum/finances" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Debt Tracking</h1>
            </div>
            <a href="/momentum/finances/create-debt" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i> Add New Debt
            </a>
        </div>

        <!-- Visual summary cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border-l-4 border-green-500">
                <div class="p-4">
                    <div class="flex items-center">
                        <div class="rounded-full bg-green-100 dark:bg-green-900 p-3 mr-4">
                            <i class="fas fa-hand-holding-usd text-green-600 dark:text-green-400"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Money Owed to You</h3>
                            <p class="text-2xl font-bold text-green-600 dark:text-green-400">Rs <?= number_format($debtSummary['total_receivable'], 2) ?></p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="font-medium"><?= $debtSummary['receivable_count'] ?></span> active receivables
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border-l-4 border-red-500">
                <div class="p-4">
                    <div class="flex items-center">
                        <div class="rounded-full bg-red-100 dark:bg-red-900 p-3 mr-4">
                            <i class="fas fa-hand-holding-usd text-red-600 dark:text-red-400 fa-flip-horizontal"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Money You Owe</h3>
                            <p class="text-2xl font-bold text-red-600 dark:text-red-400">Rs <?= number_format($debtSummary['total_payable'], 2) ?></p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="font-medium"><?= $debtSummary['payable_count'] ?></span> active payables
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Simple tab navigation -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="border-b border-gray-200 dark:border-gray-700">
                <nav class="flex -mb-px">
                    <button class="tab-btn active" data-tab="all">
                        All Debts
                    </button>
                    <button class="tab-btn" data-tab="receivable">
                        Money Owed to You
                    </button>
                    <button class="tab-btn" data-tab="payable">
                        Money You Owe
                    </button>
                </nav>
            </div>

            <!-- Simple filter controls -->
            <div class="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
                <form id="filter-form" class="flex flex-wrap items-center gap-3">
                    <div class="flex-1 min-w-[200px]">
                        <label for="status-filter" class="sr-only">Status</label>
                        <select id="status-filter" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="">All Statuses</option>
                            <option value="unpaid">Unpaid</option>
                            <option value="partially_paid">Partially Paid</option>
                            <option value="paid">Paid</option>
                        </select>
                    </div>
                    <button type="submit" class="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-filter mr-1"></i> Filter
                    </button>
                </form>
            </div>

            <!-- Debt list with visual status indicators -->
            <div class="tab-content active" id="all-tab">
                <?php if (empty($debts)): ?>
                    <div class="p-8 text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
                            <i class="fas fa-hand-holding-usd text-2xl text-gray-500 dark:text-gray-400"></i>
                        </div>
                        <p class="text-gray-500 dark:text-gray-400 mb-4">No debts found</p>
                        <a href="/momentum/finances/create-debt" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">Add Your First Debt</a>
                    </div>
                <?php else: ?>
                    <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                        <?php foreach ($debts as $debt): ?>
                            <li class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="rounded-full p-2 mr-3 <?= $debt['type'] === 'given' ? 'bg-green-100 dark:bg-green-900' : 'bg-red-100 dark:bg-red-900' ?>">
                                            <i class="fas fa-hand-holding-usd <?= $debt['type'] === 'given' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400 fa-flip-horizontal' ?>"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900 dark:text-white"><?= View::escape($debt['person_entity']) ?></p>
                                            <div class="flex items-center mt-1">
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium <?= $debt['type'] === 'given' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' ?>">
                                                    <?= $debt['type'] === 'given' ? 'Receivable' : 'Payable' ?>
                                                </span>
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ml-2
                                                    <?php if ($debt['status'] === 'paid'): ?>
                                                        bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                                    <?php elseif ($debt['status'] === 'partially_paid'): ?>
                                                        bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                                    <?php else: ?>
                                                        bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                                    <?php endif; ?>
                                                ">
                                                    <?= ucfirst(str_replace('_', ' ', $debt['status'])) ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-medium <?= $debt['type'] === 'given' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                                            Rs <?= number_format($debt['amount'], 2) ?>
                                        </p>
                                        <?php if ($debt['status'] === 'partially_paid'): ?>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                Paid: Rs <?= number_format($debt['amount_paid'], 2) ?>
                                            </p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                Remaining: Rs <?= number_format($debt['amount'] - $debt['amount_paid'], 2) ?>
                                            </p>
                                        <?php endif; ?>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            Due: <?= date('M j, Y', strtotime($debt['due_date'])) ?>
                                            <?php
                                                $daysUntilDue = (strtotime($debt['due_date']) - time()) / (60 * 60 * 24);
                                                if ($debt['status'] !== 'paid' && $daysUntilDue < 0):
                                            ?>
                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 ml-1">
                                                    Overdue
                                                </span>
                                            <?php elseif ($debt['status'] !== 'paid' && $daysUntilDue <= 7): ?>
                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 ml-1">
                                                    Soon
                                                </span>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="mt-3 flex justify-end space-x-2">
                                    <?php if ($debt['status'] !== 'paid'): ?>
                                        <button onclick="showPaymentModal(<?= $debt['id'] ?>, '<?= View::escape($debt['person_entity']) ?>', <?= $debt['amount'] - $debt['amount_paid'] ?>)" class="inline-flex items-center px-2 py-1 text-xs font-medium rounded hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 text-primary-600 dark:text-primary-400">
                                            <i class="fas fa-money-bill-wave mr-1"></i> Record Payment
                                        </button>
                                    <?php endif; ?>
                                    <a href="/momentum/finances/edit-debt/<?= $debt['id'] ?>" class="inline-flex items-center px-2 py-1 text-xs font-medium rounded hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 text-indigo-600 dark:text-indigo-400">
                                        <i class="fas fa-edit mr-1"></i> Edit
                                    </a>
                                    <button onclick="confirmDelete(<?= $debt['id'] ?>, '<?= View::escape($debt['person_entity']) ?>')" class="inline-flex items-center px-2 py-1 text-xs font-medium rounded hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 text-red-600 dark:text-red-400">
                                        <i class="fas fa-trash mr-1"></i> Delete
                                    </button>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div id="payment-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 transition-opacity"></div>
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full mx-auto mt-16 mb-16">
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Record Payment</h3>
                <button type="button" onclick="hidePaymentModal()" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="payment-form" action="" method="POST">
                <div class="px-4 py-5">
                    <p class="text-gray-500 dark:text-gray-400 mb-4">
                        Recording payment for <span id="payment-person" class="font-medium"></span>
                    </p>
                    <div>
                        <label for="payment_amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Payment Amount <span class="text-red-500">*</span>
                        </label>
                        <div class="flex">
                            <div class="flex-none w-12 flex items-center justify-center bg-gray-100 dark:bg-gray-600 border border-r-0 border-gray-300 dark:border-gray-700 rounded-l-md">
                                <span class="text-gray-500 dark:text-gray-400 font-medium">Rs</span>
                            </div>
                            <input type="number" name="payment_amount" id="payment_amount" step="0.01" min="0.01" class="flex-grow rounded-none rounded-r-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="0.00" required>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Maximum amount: <span id="payment-max-amount" class="font-medium"></span>
                        </p>
                    </div>
                </div>
                <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 flex justify-end space-x-3">
                    <button type="button" onclick="hidePaymentModal()" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        Cancel
                    </button>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        Record Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 transition-opacity"></div>
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full mx-auto mt-16 mb-16">
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Delete Debt</h3>
                <button type="button" onclick="hideDeleteModal()" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-4 py-5">
                <div class="flex items-center mb-4">
                    <div class="rounded-full bg-red-100 dark:bg-red-900 p-3 mr-3">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                    </div>
                    <p class="text-gray-700 dark:text-gray-300">
                        Are you sure you want to delete the debt for <span id="delete-person" class="font-medium"></span>?
                    </p>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                    This action cannot be undone.
                </p>
            </div>
            <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 flex justify-end space-x-3">
                <button type="button" onclick="hideDeleteModal()" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    Cancel
                </button>
                <form id="delete-form" action="" method="POST" class="inline">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Tab navigation
    document.querySelectorAll('.tab-btn').forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all tabs
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Add active class to clicked tab
            this.classList.add('active');

            // Filter the list based on tab
            const tabName = this.dataset.tab;
            filterDebts(tabName === 'all' ? null : tabName);
        });
    });

    // Filter debts based on type and status
    function filterDebts(type = null, status = null) {
        const items = document.querySelectorAll('ul li');
        let visibleCount = 0;

        items.forEach(item => {
            let show = true;

            // Filter by type if specified
            if (type) {
                const typeLabel = item.querySelector('.inline-flex').textContent.trim().toLowerCase();
                const isReceivable = typeLabel.includes('receivable');

                if ((type === 'receivable' && !isReceivable) || (type === 'given' && !isReceivable) ||
                    (type === 'payable' && isReceivable) || (type === 'received' && isReceivable)) {
                    show = false;
                }
            }

            // Filter by status if specified
            if (status && show) {
                const statusLabels = item.querySelectorAll('.inline-flex');
                if (statusLabels.length > 1) {
                    const statusText = statusLabels[1].textContent.trim().toLowerCase();
                    if (!statusText.includes(status.replace('_', ' '))) {
                        show = false;
                    }
                }
            }

            // Show or hide the item
            if (show) {
                item.classList.remove('hidden');
                visibleCount++;
            } else {
                item.classList.add('hidden');
            }
        });

        // Show empty state if no items visible
        const emptyState = document.querySelector('.p-8.text-center');
        const listContainer = document.querySelector('ul');

        if (visibleCount === 0 && listContainer) {
            listContainer.classList.add('hidden');
            if (emptyState) {
                emptyState.classList.remove('hidden');
            }
        } else {
            if (listContainer) listContainer.classList.remove('hidden');
            if (emptyState) emptyState.classList.add('hidden');
        }
    }

    // Status filter
    document.getElementById('status-filter').addEventListener('change', function() {
        const activeTab = document.querySelector('.tab-btn.active');
        const tabName = activeTab ? activeTab.dataset.tab : 'all';
        filterDebts(tabName === 'all' ? null : tabName, this.value);
    });

    // Payment Modal Functions
    function showPaymentModal(debtId, personName, maxAmount) {
        document.getElementById('payment-person').textContent = personName;
        document.getElementById('payment-max-amount').textContent = formatCurrency(maxAmount);
        document.getElementById('payment_amount').max = maxAmount;
        document.getElementById('payment_amount').value = maxAmount;
        document.getElementById('payment-form').action = `/momentum/finances/record-payment/${debtId}`;
        document.getElementById('payment-modal').classList.remove('hidden');
    }

    function hidePaymentModal() {
        document.getElementById('payment-modal').classList.add('hidden');
    }

    // Delete Modal Functions
    function confirmDelete(debtId, personName) {
        document.getElementById('delete-person').textContent = personName;
        document.getElementById('delete-form').action = `/momentum/finances/delete-debt/${debtId}`;
        document.getElementById('delete-modal').classList.remove('hidden');
    }

    function hideDeleteModal() {
        document.getElementById('delete-modal').classList.add('hidden');
    }

    // Format currency
    function formatCurrency(amount) {
        return 'Rs ' + parseFloat(amount).toFixed(2);
    }

    // Style the tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.add('px-4', 'py-3', 'border-b-2', 'border-transparent', 'text-sm', 'font-medium', 'text-gray-500', 'dark:text-gray-400', 'hover:text-gray-700', 'dark:hover:text-gray-300', 'hover:border-gray-300', 'dark:hover:border-gray-600', 'focus:outline-none');
    });

    document.querySelectorAll('.tab-btn.active').forEach(btn => {
        btn.classList.add('border-primary-500', 'text-primary-600', 'dark:text-primary-400');
    });
</script>
