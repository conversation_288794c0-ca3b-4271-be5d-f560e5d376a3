<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8"> <!-- Narrower container for focus -->
        <div class="flex items-center mb-6">
            <a href="/momentum/finances/debts" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Edit Debt
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <!-- Display validation errors if any -->
            <?php if (isset($errors) && !empty($errors)): ?>
                <div class="p-4 bg-red-50 dark:bg-red-900 border-b border-red-200 dark:border-red-800">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-400 dark:text-red-300"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                Please fix the following errors:
                            </h3>
                            <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                <ul class="list-disc pl-5 space-y-1">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?= $error ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <form action="/momentum/finances/edit-debt/<?= $debt['id'] ?>" method="POST" class="p-6 space-y-6">
                <!-- Debt Type Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">What type of debt is this?</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                        <div class="option-card <?= $debt['type'] === 'received' ? 'selected' : '' ?>" data-value="received" onclick="selectOption('type', 'received', this)">
                            <div class="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-full bg-red-100 dark:bg-red-900 mr-4">
                                <i class="fas fa-hand-holding-usd text-red-600 dark:text-red-400 fa-flip-horizontal"></i>
                            </div>
                            <div>
                                <h3 class="text-sm font-medium text-gray-900 dark:text-white">Money You Owe</h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400">You need to pay this back (Payable)</p>
                            </div>
                        </div>
                        <div class="option-card <?= $debt['type'] === 'given' ? 'selected' : '' ?>" data-value="given" onclick="selectOption('type', 'given', this)">
                            <div class="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-full bg-green-100 dark:bg-green-900 mr-4">
                                <i class="fas fa-hand-holding-usd text-green-600 dark:text-green-400"></i>
                            </div>
                            <div>
                                <h3 class="text-sm font-medium text-gray-900 dark:text-white">Money Owed to You</h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Someone needs to pay you back (Receivable)</p>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" name="type" id="type-input" value="<?= $debt['type'] ?>" required>
                </div>

                <!-- Person/Entity -->
                <div>
                    <label for="person_entity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Person or Entity <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="person_entity" id="person_entity" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="Who is involved in this debt?" value="<?= View::escape($debt['person_entity']) ?>" required>
                </div>

                <!-- Amount -->
                <div>
                    <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Total Amount <span class="text-red-500">*</span>
                    </label>
                    <div class="flex">
                        <div class="flex-none w-10 flex items-center justify-center bg-gray-100 dark:bg-gray-600 border border-r-0 border-gray-300 dark:border-gray-700 rounded-l-md">
                            <span class="text-gray-500 dark:text-gray-400 font-medium">Rs</span>
                        </div>
                        <input type="number" name="amount" id="amount" step="0.01" min="0.01" class="flex-grow rounded-none rounded-r-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="0.00" value="<?= $debt['amount'] ?>" required>
                    </div>
                </div>

                <?php if ($debt['status'] === 'partially_paid'): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Amount Paid So Far</label>
                        <div class="flex">
                            <div class="flex-none w-10 flex items-center justify-center bg-gray-100 dark:bg-gray-600 border border-r-0 border-gray-300 dark:border-gray-700 rounded-l-md">
                                <span class="text-gray-500 dark:text-gray-400 font-medium">Rs</span>
                            </div>
                            <input type="number" value="<?= $debt['amount_paid'] ?>" class="flex-grow rounded-none rounded-r-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" disabled>
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            Remaining: Rs <?= number_format($debt['amount'] - $debt['amount_paid'], 2) ?>
                        </p>
                    </div>
                <?php endif; ?>

                <!-- Due Date -->
                <div>
                    <label for="due_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Due Date <span class="text-red-500">*</span>
                    </label>
                    <input type="date" name="due_date" id="due_date" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= $debt['due_date'] ?>" required>
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Status <span class="text-red-500">*</span>
                    </label>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mt-2">
                        <div class="status-option <?= $debt['status'] === 'unpaid' ? 'selected' : '' ?>" data-value="unpaid" onclick="selectStatus('unpaid')">
                            <i class="fas fa-times-circle text-gray-600 dark:text-gray-400"></i>
                            <span>Unpaid</span>
                        </div>
                        <div class="status-option <?= $debt['status'] === 'partially_paid' ? 'selected' : '' ?>" data-value="partially_paid" onclick="selectStatus('partially_paid')">
                            <i class="fas fa-adjust text-yellow-600 dark:text-yellow-400"></i>
                            <span>Partially Paid</span>
                        </div>
                        <div class="status-option <?= $debt['status'] === 'paid' ? 'selected' : '' ?>" data-value="paid" onclick="selectStatus('paid')">
                            <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                            <span>Paid</span>
                        </div>
                    </div>
                    <input type="hidden" name="status" id="status-input" value="<?= $debt['status'] ?>" required>
                </div>

                <!-- Notes -->
                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notes (Optional)</label>
                    <textarea name="notes" id="notes" rows="3" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="Any additional details about this debt"><?= View::escape($debt['notes'] ?? '') ?></textarea>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                    <a href="/momentum/finances/debts" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        Update Debt
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    /* Using regular CSS instead of @apply */
    .option-card {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-width: 1px;
        border-color: #d1d5db;
        border-radius: 0.5rem;
        cursor: pointer;
        transition-property: color, background-color, border-color;
        transition-duration: 150ms;
    }

    .dark .option-card {
        border-color: #374151;
    }

    .option-card:hover {
        background-color: #f9fafb;
    }

    .dark .option-card:hover {
        background-color: #374151;
    }

    .option-card.selected {
        border-color: #0ea5e9;
        --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
        --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
        box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
        --tw-ring-color: #0ea5e9;
        --tw-ring-opacity: 1;
    }

    .status-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0.75rem;
        border-width: 1px;
        border-color: #d1d5db;
        border-radius: 0.5rem;
        cursor: pointer;
        color: #374151;
        transition-property: color, background-color, border-color;
        transition-duration: 150ms;
    }

    .dark .status-option {
        border-color: #374151;
        color: #d1d5db;
    }

    .status-option:hover {
        background-color: #f9fafb;
    }

    .dark .status-option:hover {
        background-color: #374151;
    }

    .status-option.selected {
        border-color: #0ea5e9;
        background-color: #f0f9ff;
        color: #0369a1;
    }

    .dark .status-option.selected {
        background-color: #0c4a6e;
        color: #7dd3fc;
    }

    .status-option i {
        font-size: 1.25rem;
        margin-bottom: 0.25rem;
    }

    .status-option span {
        font-size: 0.75rem;
    }
</style>

<script>
    // Selection functions
    function selectOption(field, value, element) {
        // Clear previous selections
        document.querySelectorAll(`.option-card[data-value]`).forEach(el => {
            if (el.dataset.value === 'received' || el.dataset.value === 'given') {
                if (field === 'type') {
                    el.classList.remove('selected');
                }
            }
        });

        // Set new selection
        element.classList.add('selected');
        document.getElementById(`${field}-input`).value = value;
    }

    function selectStatus(status) {
        // Clear previous selections
        document.querySelectorAll('.status-option').forEach(el => {
            el.classList.remove('selected');
        });

        // Set new selection
        document.querySelector(`.status-option[data-value="${status}"]`).classList.add('selected');
        document.getElementById('status-input').value = status;
    }
</script>
