<?php
require_once __DIR__ . '/../../utils/Session.php';
require_once __DIR__ . '/../../utils/View.php';
?>
<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/finances" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Receipts for Transaction
            </h1>
        </div>

        <!-- Transaction Details -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Transaction Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Type</p>
                        <p class="font-medium text-gray-900 dark:text-white">
                            <?php if ($transaction['type'] === 'income'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                    <i class="fas fa-arrow-down mr-1"></i> Income
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                                    <i class="fas fa-arrow-up mr-1"></i> Expense
                                </span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Amount</p>
                        <p class="font-medium text-gray-900 dark:text-white">
                            <?php if ($transaction['transaction_type'] === 'monetary'): ?>
                                Rs <?= number_format($transaction['amount'], 2) ?>
                            <?php else: ?>
                                Non-monetary (Value: Rs <?= number_format($transaction['fair_market_value'], 2) ?>)
                            <?php endif; ?>
                        </p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Category</p>
                        <p class="font-medium text-gray-900 dark:text-white"><?= View::escape($transaction['category']) ?></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Date</p>
                        <p class="font-medium text-gray-900 dark:text-white"><?= date('F j, Y', strtotime($transaction['date'])) ?></p>
                    </div>
                    <?php if (!empty($transaction['description'])): ?>
                    <div class="col-span-1 md:col-span-2">
                        <p class="text-sm text-gray-500 dark:text-gray-400">Description</p>
                        <p class="font-medium text-gray-900 dark:text-white"><?= View::escape($transaction['description']) ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Receipts -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Receipts</h3>
                    <a href="/momentum/finances/transactions/<?= $transaction['id'] ?>/edit" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-plus mr-1.5"></i> Add Receipt
                    </a>
                </div>

                <?php if (empty($receipts)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-receipt text-gray-400 dark:text-gray-500 text-4xl mb-4"></i>
                        <p class="text-gray-500 dark:text-gray-400 mb-4">No receipts found for this transaction</p>
                        <a href="/momentum/finances/transactions/<?= $transaction['id'] ?>/edit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i> Add Receipt
                        </a>
                    </div>
                <?php else: ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <?php foreach ($receipts as $receipt): ?>
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                                <div class="p-4">
                                    <?php if (in_array($receipt['file_type'], ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'])): ?>
                                        <div class="aspect-w-16 aspect-h-9 mb-3">
                                            <img src="/momentum/<?= $receipt['file_path'] ?>" alt="Receipt" class="object-cover rounded">
                                        </div>
                                    <?php elseif ($receipt['file_type'] === 'application/pdf'): ?>
                                        <div class="flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mb-3 aspect-w-16 aspect-h-9">
                                            <i class="fas fa-file-pdf text-red-500 text-4xl"></i>
                                        </div>
                                    <?php else: ?>
                                        <div class="flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mb-3 aspect-w-16 aspect-h-9">
                                            <i class="fas fa-file text-gray-500 text-4xl"></i>
                                        </div>
                                    <?php endif; ?>

                                    <div class="flex items-center justify-between">
                                        <div class="truncate">
                                            <p class="font-medium text-gray-900 dark:text-white truncate" title="<?= View::escape($receipt['file_name']) ?>">
                                                <?= View::escape($receipt['file_name']) ?>
                                            </p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                <?= date('M j, Y', strtotime($receipt['upload_date'])) ?> • <?= round($receipt['file_size'] / 1024, 1) ?> KB
                                            </p>
                                        </div>
                                        <div class="flex space-x-2">
                                            <a href="/momentum/<?= $receipt['file_path'] ?>" target="_blank" class="text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="/momentum/finances/receipts/<?= $receipt['id'] ?>/delete" onclick="return confirm('Are you sure you want to delete this receipt?')" class="text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400" title="Delete">
                                                <i class="fas fa-trash-alt"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
