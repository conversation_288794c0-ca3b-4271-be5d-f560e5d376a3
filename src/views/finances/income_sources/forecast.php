<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Income Forecast</h1>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/finances/income-sources" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Income Sources
                </a>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if (Session::hasFlash('success')): ?>
            <div class="mb-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 dark:bg-green-800 dark:text-green-100" role="alert">
                <p><?= Session::getFlash('success') ?></p>
            </div>
        <?php endif; ?>

        <?php if (Session::hasFlash('error')): ?>
            <div class="mb-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 dark:bg-red-800 dark:text-red-100" role="alert">
                <p><?= Session::getFlash('error') ?></p>
            </div>
        <?php endif; ?>

        <!-- Forecast Period Selector -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Forecast Period</h3>
                <form action="/momentum/finances/income-sources/forecast" method="get" class="flex flex-col sm:flex-row sm:items-end space-y-4 sm:space-y-0 sm:space-x-4">
                    <div class="w-full sm:w-auto">
                        <label for="months" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Number of Months</label>
                        <select name="months" id="months" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <?php for ($i = 1; $i <= 24; $i++): ?>
                                <option value="<?= $i ?>" <?= $months == $i ? 'selected' : '' ?>><?= $i ?> month<?= $i > 1 ? 's' : '' ?></option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-sync-alt mr-2"></i> Update Forecast
                    </button>
                </form>
            </div>
        </div>

        <!-- Forecast Summary -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Forecast Summary</h3>
                
                <?php if (empty($forecast)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-chart-line text-gray-400 dark:text-gray-500 text-4xl mb-4"></i>
                        <p class="text-gray-500 dark:text-gray-400 mb-4">No forecast data available</p>
                        <p class="text-gray-500 dark:text-gray-400">Add recurring income sources with expected amounts to see forecasts</p>
                    </div>
                <?php else: ?>
                    <!-- Forecast Chart -->
                    <div class="h-64 mb-6">
                        <canvas id="forecast-chart"></canvas>
                    </div>
                    
                    <!-- Forecast Table -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Month</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Expected Income</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Sources</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Details</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($forecast as $index => $month): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white"><?= View::escape($month['month_name']) ?></div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400"><?= View::formatDate($month['start_date']) ?> - <?= View::formatDate($month['end_date']) ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-green-600 dark:text-green-400"><?= View::formatCurrency($month['total_expected']) ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-white"><?= count($month['sources']) ?> source<?= count($month['sources']) !== 1 ? 's' : '' ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <button type="button" onclick="toggleDetails(<?= $index ?>)" class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300">
                                                <i class="fas fa-chevron-down" id="toggle-icon-<?= $index ?>"></i> View Details
                                            </button>
                                        </td>
                                    </tr>
                                    <tr id="details-row-<?= $index ?>" class="hidden">
                                        <td colspan="4" class="px-6 py-4 bg-gray-50 dark:bg-gray-700">
                                            <div class="text-sm text-gray-900 dark:text-white mb-2">Income Sources for <?= View::escape($month['month_name']) ?></div>
                                            <div class="overflow-x-auto">
                                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                                                    <thead class="bg-gray-100 dark:bg-gray-600">
                                                        <tr>
                                                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Source</th>
                                                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                                                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Recurrence</th>
                                                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Expected Amount</th>
                                                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Occurrences</th>
                                                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                                                        <?php foreach ($month['sources'] as $source): ?>
                                                            <tr>
                                                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                                                    <a href="/momentum/finances/income-sources/view/<?= $source['id'] ?>" class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300">
                                                                        <?= View::escape($source['name']) ?>
                                                                    </a>
                                                                </td>
                                                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                                                    <?php 
                                                                    $sourceTypeLabels = [
                                                                        'employment' => 'Employment',
                                                                        'business' => 'Business',
                                                                        'investment' => 'Investment',
                                                                        'rental' => 'Rental',
                                                                        'sales' => 'Sales',
                                                                        'other' => 'Other'
                                                                    ];
                                                                    echo View::escape($sourceTypeLabels[$source['source_type'] ?? 'other'] ?? 'Other');
                                                                    ?>
                                                                </td>
                                                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                                                    <?php 
                                                                    $patternLabels = [
                                                                        'daily' => 'Daily',
                                                                        'weekly' => 'Weekly',
                                                                        'biweekly' => 'Bi-weekly',
                                                                        'monthly' => 'Monthly',
                                                                        'quarterly' => 'Quarterly',
                                                                        'yearly' => 'Yearly',
                                                                        'irregular' => 'Irregular'
                                                                    ];
                                                                    echo View::escape($patternLabels[$source['recurrence_pattern']] ?? $source['recurrence_pattern']);
                                                                    ?>
                                                                </td>
                                                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                                                    <?= View::formatCurrency($source['expected_amount']) ?>
                                                                </td>
                                                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                                                    <?= $source['occurrences'] ?>
                                                                </td>
                                                                <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-green-600 dark:text-green-400">
                                                                    <?= View::formatCurrency($source['total']) ?>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    function toggleDetails(index) {
        const detailsRow = document.getElementById(`details-row-${index}`);
        const toggleIcon = document.getElementById(`toggle-icon-${index}`);
        
        if (detailsRow.classList.contains('hidden')) {
            detailsRow.classList.remove('hidden');
            toggleIcon.classList.remove('fa-chevron-down');
            toggleIcon.classList.add('fa-chevron-up');
        } else {
            detailsRow.classList.add('hidden');
            toggleIcon.classList.remove('fa-chevron-up');
            toggleIcon.classList.add('fa-chevron-down');
        }
    }
    
    <?php if (!empty($forecast)): ?>
    // Forecast chart
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('forecast-chart').getContext('2d');
        
        // Extract data from PHP
        const forecastData = <?= json_encode($forecast) ?>;
        const labels = forecastData.map(item => item.month_name);
        const values = forecastData.map(item => parseFloat(item.total_expected));
        
        // Create chart
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Expected Income',
                    data: values,
                    backgroundColor: 'rgba(34, 197, 94, 0.2)',
                    borderColor: 'rgba(34, 197, 94, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rs ' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Expected Income: Rs ' + context.raw.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    });
    <?php endif; ?>
</script>
