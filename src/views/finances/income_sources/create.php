<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/finances/income-sources" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Create Income Source
            </h1>
        </div>

        <!-- Form Card -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <form action="/momentum/finances/income-sources/create" method="post" class="p-6">
                <!-- Name -->
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="name" id="name" value="<?= $data['name'] ?? '' ?>" required
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
                    <?php if (isset($errors['name'])): ?>
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?= $errors['name'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- Source Type -->
                <div class="mb-4">
                    <label for="source_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Source Type
                    </label>
                    <select name="source_type" id="source_type"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
                        <option value="employment" <?= isset($data['source_type']) && $data['source_type'] === 'employment' ? 'selected' : '' ?>>Employment</option>
                        <option value="business" <?= isset($data['source_type']) && $data['source_type'] === 'business' ? 'selected' : '' ?>>Business</option>
                        <option value="investment" <?= isset($data['source_type']) && $data['source_type'] === 'investment' ? 'selected' : '' ?>>Investment</option>
                        <option value="rental" <?= isset($data['source_type']) && $data['source_type'] === 'rental' ? 'selected' : '' ?>>Rental</option>
                        <option value="sales" <?= isset($data['source_type']) && $data['source_type'] === 'sales' ? 'selected' : '' ?>>Sales</option>
                        <option value="other" <?= !isset($data['source_type']) || $data['source_type'] === 'other' ? 'selected' : '' ?>>Other</option>
                    </select>
                </div>

                <!-- Category -->
                <div class="mb-4">
                    <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Category
                    </label>
                    <input type="text" name="category" id="category" value="<?= $data['category'] ?? '' ?>" list="category-suggestions"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
                    <datalist id="category-suggestions">
                        <option value="Salary">
                        <option value="Freelance">
                        <option value="Business">
                        <option value="Investments">
                        <option value="Rental">
                        <option value="Sales">
                        <option value="Gifts">
                        <option value="Other">
                    </datalist>
                </div>

                <!-- Description -->
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Description
                    </label>
                    <textarea name="description" id="description" rows="3"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md"><?= $data['description'] ?? '' ?></textarea>
                </div>

                <!-- Expected Amount -->
                <div class="mb-4">
                    <label for="expected_amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Expected Amount (Rs)
                    </label>
                    <div class="mt-1 currency-input-wrapper">
                        <div class="currency-symbol">
                            <span>Rs</span>
                        </div>
                        <input type="number" name="expected_amount" id="expected_amount" value="<?= $data['expected_amount'] ?? '' ?>" step="0.01" min="0"
                            class="currency-input focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
                    </div>
                </div>

                <!-- Recurring Options -->
                <div class="mb-4">
                    <div class="flex items-center">
                        <input type="checkbox" name="is_recurring" id="is_recurring" value="1" <?= isset($data['is_recurring']) && $data['is_recurring'] ? 'checked' : '' ?>
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 dark:bg-gray-700 rounded">
                        <label for="is_recurring" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                            This is a recurring income source
                        </label>
                    </div>
                </div>

                <!-- Recurrence Pattern (shown only when is_recurring is checked) -->
                <div id="recurrence-pattern-container" class="mb-4 <?= isset($data['is_recurring']) && $data['is_recurring'] ? '' : 'hidden' ?>">
                    <label for="recurrence_pattern" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Recurrence Pattern
                    </label>
                    <select name="recurrence_pattern" id="recurrence_pattern"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
                        <option value="daily" <?= isset($data['recurrence_pattern']) && $data['recurrence_pattern'] === 'daily' ? 'selected' : '' ?>>Daily</option>
                        <option value="weekly" <?= isset($data['recurrence_pattern']) && $data['recurrence_pattern'] === 'weekly' ? 'selected' : '' ?>>Weekly</option>
                        <option value="biweekly" <?= isset($data['recurrence_pattern']) && $data['recurrence_pattern'] === 'biweekly' ? 'selected' : '' ?>>Bi-weekly</option>
                        <option value="monthly" <?= isset($data['recurrence_pattern']) && $data['recurrence_pattern'] === 'monthly' ? 'selected' : '' ?>>Monthly</option>
                        <option value="quarterly" <?= isset($data['recurrence_pattern']) && $data['recurrence_pattern'] === 'quarterly' ? 'selected' : '' ?>>Quarterly</option>
                        <option value="yearly" <?= isset($data['recurrence_pattern']) && $data['recurrence_pattern'] === 'yearly' ? 'selected' : '' ?>>Yearly</option>
                        <option value="irregular" <?= isset($data['recurrence_pattern']) && $data['recurrence_pattern'] === 'irregular' ? 'selected' : '' ?>>Irregular</option>
                    </select>
                </div>

                <!-- Payment Method -->
                <div class="mb-4">
                    <label for="payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Payment Method
                    </label>
                    <input type="text" name="payment_method" id="payment_method" value="<?= $data['payment_method'] ?? '' ?>" list="payment-method-suggestions"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
                    <datalist id="payment-method-suggestions">
                        <option value="Cash">
                        <option value="Bank Transfer">
                        <option value="Check">
                        <option value="Credit Card">
                        <option value="PayPal">
                        <option value="Other">
                    </datalist>
                </div>

                <!-- Location -->
                <div class="mb-4">
                    <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Location
                    </label>
                    <input type="text" name="location" id="location" value="<?= $data['location'] ?? '' ?>"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
                </div>

                <!-- Contact Person -->
                <div class="mb-4">
                    <label for="contact_person" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Contact Person
                    </label>
                    <input type="text" name="contact_person" id="contact_person" value="<?= $data['contact_person'] ?? '' ?>"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
                </div>

                <!-- Contact Info -->
                <div class="mb-4">
                    <label for="contact_info" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Contact Info
                    </label>
                    <input type="text" name="contact_info" id="contact_info" value="<?= $data['contact_info'] ?? '' ?>"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
                </div>

                <!-- Notes -->
                <div class="mb-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Notes
                    </label>
                    <textarea name="notes" id="notes" rows="3"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md"><?= $data['notes'] ?? '' ?></textarea>
                </div>

                <!-- Tags -->
                <div class="mb-4">
                    <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Tags (comma separated)
                    </label>
                    <input type="text" name="tags" id="tags" value="<?= $data['tags'] ?? '' ?>"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
                </div>

                <!-- Last Payment Date -->
                <div class="mb-4">
                    <label for="last_payment_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Last Payment Date
                    </label>
                    <input type="date" name="last_payment_date" id="last_payment_date" value="<?= $data['last_payment_date'] ?? '' ?>"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
                </div>

                <!-- Next Expected Date -->
                <div class="mb-4">
                    <label for="next_expected_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Next Expected Date
                    </label>
                    <input type="date" name="next_expected_date" id="next_expected_date" value="<?= $data['next_expected_date'] ?? '' ?>"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
                </div>

                <!-- Active Status -->
                <div class="mb-6">
                    <div class="flex items-center">
                        <input type="checkbox" name="is_active" id="is_active" value="1" <?= !isset($data['is_active']) || $data['is_active'] ? 'checked' : '' ?>
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 dark:bg-gray-700 rounded">
                        <label for="is_active" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                            Active income source
                        </label>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3">
                    <a href="/momentum/finances/income-sources" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        Create Income Source
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const isRecurringCheckbox = document.getElementById('is_recurring');
        const recurrencePatternContainer = document.getElementById('recurrence-pattern-container');

        // Toggle recurrence pattern visibility based on checkbox
        isRecurringCheckbox.addEventListener('change', function() {
            if (this.checked) {
                recurrencePatternContainer.classList.remove('hidden');
            } else {
                recurrencePatternContainer.classList.add('hidden');
            }
        });
    });
</script>
