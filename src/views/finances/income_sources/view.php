<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/finances/income-sources" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <?= View::escape($incomeSource['name']) ?>
            </h1>
            <div class="ml-auto flex space-x-2">
                <a href="/momentum/finances/income-sources/forecast" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200">
                    <i class="fas fa-chart-line mr-1.5"></i> Forecast
                </a>
                <a href="/momentum/finances/income-sources/edit/<?= $incomeSource['id'] ?>" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-edit mr-1.5"></i> Edit
                </a>
                <button type="button" onclick="confirmDelete(<?= $incomeSource['id'] ?>, '<?= addslashes(View::escape($incomeSource['name'])) ?>')" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                    <i class="fas fa-trash mr-1.5"></i> Delete
                </button>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if (Session::hasFlash('success')): ?>
            <div class="mb-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 dark:bg-green-800 dark:text-green-100" role="alert">
                <p><?= Session::getFlash('success') ?></p>
            </div>
        <?php endif; ?>

        <?php if (Session::hasFlash('error')): ?>
            <div class="mb-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 dark:bg-red-800 dark:text-red-100" role="alert">
                <p><?= Session::getFlash('error') ?></p>
            </div>
        <?php endif; ?>

        <!-- Income Source Details -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Income Source Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <dl>
                            <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?= View::escape($incomeSource['name']) ?></dd>
                            </div>
                            <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Category</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?= View::escape($incomeSource['category'] ?? 'N/A') ?></dd>
                            </div>
                            <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                                <dd class="mt-1 text-sm sm:mt-0 sm:col-span-2">
                                    <?php if ($incomeSource['is_active']): ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">Active</span>
                                    <?php else: ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">Inactive</span>
                                    <?php endif; ?>
                                </dd>
                            </div>
                            <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Recurring</dt>
                                <dd class="mt-1 text-sm sm:mt-0 sm:col-span-2">
                                    <?php if ($incomeSource['is_recurring']): ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                                            <?= View::escape($incomeSource['recurrence_pattern'] ?? 'Yes') ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">No</span>
                                    <?php endif; ?>
                                </dd>
                            </div>
                            <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Source Type</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                    <?php
                                    $sourceTypeLabels = [
                                        'employment' => 'Employment',
                                        'business' => 'Business',
                                        'investment' => 'Investment',
                                        'rental' => 'Rental',
                                        'sales' => 'Sales',
                                        'other' => 'Other'
                                    ];
                                    echo View::escape($sourceTypeLabels[$incomeSource['source_type'] ?? 'other'] ?? 'Other');
                                    ?>
                                </dd>
                            </div>

                            <?php if (!empty($incomeSource['description'])): ?>
                                <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?= nl2br(View::escape($incomeSource['description'])) ?></dd>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($incomeSource['payment_method'])): ?>
                                <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Payment Method</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?= View::escape($incomeSource['payment_method']) ?></dd>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($incomeSource['location'])): ?>
                                <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Location</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?= View::escape($incomeSource['location']) ?></dd>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($incomeSource['contact_person'])): ?>
                                <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Contact Person</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?= View::escape($incomeSource['contact_person']) ?></dd>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($incomeSource['contact_info'])): ?>
                                <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Contact Info</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?= View::escape($incomeSource['contact_info']) ?></dd>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($incomeSource['tags'])): ?>
                                <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Tags</dt>
                                    <dd class="mt-1 text-sm sm:mt-0 sm:col-span-2">
                                        <?php
                                        $tags = explode(',', $incomeSource['tags']);
                                        foreach ($tags as $tag):
                                            $tag = trim($tag);
                                            if (!empty($tag)):
                                        ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 mr-1 mb-1"><?= View::escape($tag) ?></span>
                                        <?php
                                            endif;
                                        endforeach;
                                        ?>
                                    </dd>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($incomeSource['notes'])): ?>
                                <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Notes</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?= nl2br(View::escape($incomeSource['notes'])) ?></dd>
                                </div>
                            <?php endif; ?>
                        </dl>
                    </div>
                    <div>
                        <dl>
                            <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Expected Amount</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                    <?= !empty($incomeSource['expected_amount']) ? View::formatCurrency($incomeSource['expected_amount']) : 'N/A' ?>
                                </dd>
                            </div>
                            <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Income</dt>
                                <dd class="mt-1 text-sm font-medium text-green-600 dark:text-green-400 sm:mt-0 sm:col-span-2">
                                    <?= View::formatCurrency($summary['total_income'] ?? 0) ?>
                                </dd>
                            </div>
                            <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Transaction Count</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                    <?= $summary['transaction_count'] ?? 0 ?>
                                </dd>
                            </div>
                            <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">First Transaction</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                    <?= !empty($summary['first_transaction_date']) ? View::formatDate($summary['first_transaction_date']) : 'N/A' ?>
                                </dd>
                            </div>
                            <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Transaction</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                    <?= !empty($summary['last_transaction_date']) ? View::formatDate($summary['last_transaction_date']) : 'N/A' ?>
                                </dd>
                            </div>

                            <?php if (!empty($incomeSource['last_payment_date'])): ?>
                            <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Payment Date</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                    <?= View::formatDate($incomeSource['last_payment_date']) ?>
                                </dd>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($incomeSource['next_expected_date'])): ?>
                            <div class="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Next Expected Date</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                    <?= View::formatDate($incomeSource['next_expected_date']) ?>
                                    <?php
                                    // Calculate days until next payment
                                    $today = new DateTime();
                                    $nextDate = new DateTime($incomeSource['next_expected_date']);
                                    $diff = $today->diff($nextDate);
                                    $daysUntil = $diff->days;

                                    if ($nextDate < $today) {
                                        echo ' <span class="text-red-500 dark:text-red-400">(Overdue by ' . $daysUntil . ' days)</span>';
                                    } else {
                                        echo ' <span class="text-gray-500 dark:text-gray-400">(' . $daysUntil . ' days from now)</span>';
                                    }
                                    ?>
                                </dd>
                            </div>
                            <?php endif; ?>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Income Chart -->
        <?php if (!empty($monthlyIncome)): ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Monthly Income</h3>
                    <div class="h-64">
                        <canvas id="monthly-income-chart"></canvas>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Transactions -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Transactions</h3>
                    <a href="/momentum/finances/create-transaction" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-plus mr-1.5"></i> Add Transaction
                    </a>
                </div>

                <?php if (empty($transactions)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-receipt text-gray-400 dark:text-gray-500 text-4xl mb-4"></i>
                        <p class="text-gray-500 dark:text-gray-400 mb-4">No transactions found for this income source</p>
                        <a href="/momentum/finances/create-transaction" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i> Add Transaction
                        </a>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Category</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Description</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Amount</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($transactions as $transaction): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            <?= View::formatDate($transaction['date']) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            <?= View::escape($transaction['category']) ?>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate">
                                            <?= View::escape($transaction['description'] ?? '') ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <?php if ($transaction['transaction_type'] === 'monetary'): ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">Monetary</span>
                                            <?php else: ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100">Non-monetary</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600 dark:text-green-400">
                                            <?php if ($transaction['transaction_type'] === 'monetary'): ?>
                                                <?= View::formatCurrency($transaction['amount']) ?>
                                            <?php else: ?>
                                                <?= View::formatCurrency($transaction['fair_market_value'] ?? $transaction['amount']) ?>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <a href="/momentum/finances/edit-transaction/<?= $transaction['id'] ?>" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 mr-3">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="/momentum/finances/delete-transaction/<?= $transaction['id'] ?>" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to delete this transaction?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="fixed z-50 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">Delete Income Source</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 dark:text-gray-400">Are you sure you want to delete the income source "<span id="delete-source-name"></span>"? This action cannot be undone.</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <form id="delete-form" action="" method="post">
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">Delete</button>
                </form>
                <button type="button" onclick="closeDeleteModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    function confirmDelete(id, name) {
        document.getElementById('delete-source-name').textContent = name;
        // Fix the URL path to ensure it's correct
        document.getElementById('delete-form').action = `/momentum/finances/income-sources/delete/${id}`;
        // Add debug to see what URL is being set
        console.log("Setting delete form action to:", `/momentum/finances/income-sources/delete/${id}`);
        document.getElementById('delete-modal').classList.remove('hidden');
    }

    function closeDeleteModal() {
        document.getElementById('delete-modal').classList.add('hidden');
    }

    <?php if (!empty($monthlyIncome)): ?>
    // Monthly income chart
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('monthly-income-chart').getContext('2d');

        // Extract data from PHP
        const monthlyData = <?= json_encode($monthlyIncome) ?>;
        const labels = monthlyData.map(item => {
            const [year, month] = item.month.split('-');
            return new Date(year, month - 1).toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        });
        const values = monthlyData.map(item => parseFloat(item.total_income));

        // Create chart
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Monthly Income',
                    data: values,
                    backgroundColor: 'rgba(34, 197, 94, 0.2)',
                    borderColor: 'rgba(34, 197, 94, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rs ' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Income: Rs ' + context.raw.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    });
    <?php endif; ?>
</script>
