<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/productivity/batch-templates" class="mr-4 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white"><?= htmlspecialchars($template['name']) ?></h1>
            <span class="ml-3 px-2 py-1 text-xs font-semibold rounded-full 
                <?php
                if ($template['energy_level'] === 'high') {
                    echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                } elseif ($template['energy_level'] === 'medium') {
                    echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                } else {
                    echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
                }
                ?>
            ">
                <?= ucfirst($template['energy_level']) ?> Energy
            </span>
            <?php if ($template['is_recurring']): ?>
                <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                    <?= ucfirst($template['recurrence_pattern']) ?> Recurring
                </span>
            <?php endif; ?>
        </div>
        
        <!-- Template Details -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- Template Info -->
            <div class="md:col-span-2 bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex justify-between items-start mb-4">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Template Details</h2>
                    <div class="flex space-x-2">
                        <button id="editTemplateBtn" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-edit mr-1"></i> Edit
                        </button>
                        <button id="deleteTemplateBtn" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            <i class="fas fa-trash-alt mr-1"></i> Delete
                        </button>
                    </div>
                </div>
                
                <?php if (!empty($template['description'])): ?>
                    <div class="mb-4">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</h3>
                        <p class="text-gray-600 dark:text-gray-400"><?= nl2br(htmlspecialchars($template['description'])) ?></p>
                    </div>
                <?php endif; ?>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Energy Level</h3>
                        <p class="text-gray-600 dark:text-gray-400"><?= ucfirst($template['energy_level']) ?></p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Estimated Time</h3>
                        <p class="text-gray-600 dark:text-gray-400"><?= $template['estimated_time'] ? $template['estimated_time'] . ' minutes' : 'Not specified' ?></p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Created</h3>
                        <p class="text-gray-600 dark:text-gray-400"><?= date('M j, Y', strtotime($template['created_at'])) ?></p>
                    </div>
                </div>
                
                <?php if ($template['is_recurring']): ?>
                    <div class="mb-4 p-3 bg-purple-50 dark:bg-purple-900 rounded-lg">
                        <h3 class="text-sm font-medium text-purple-800 dark:text-purple-200 mb-1">Recurring Schedule</h3>
                        <p class="text-purple-700 dark:text-purple-300">
                            <?php
                            switch ($template['recurrence_pattern']) {
                                case 'daily':
                                    echo 'This template will automatically generate a new batch every day.';
                                    break;
                                case 'weekdays':
                                    echo 'This template will automatically generate a new batch every weekday (Monday to Friday).';
                                    break;
                                case 'weekly':
                                    echo 'This template will automatically generate a new batch once a week.';
                                    break;
                                case 'monthly':
                                    echo 'This template will automatically generate a new batch once a month.';
                                    break;
                            }
                            ?>
                        </p>
                    </div>
                <?php endif; ?>
                
                <div class="flex justify-end">
                    <a href="/momentum/productivity/create-batch-from-template/<?= $template['id'] ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-play mr-1"></i> Generate Batch Now
                    </a>
                </div>
            </div>
            
            <!-- Add Item -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Add Item</h2>
                <form action="/momentum/productivity/add-item-to-template/<?= $template['id'] ?>" method="POST">
                    <div class="mb-4">
                        <label for="task_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Task Type</label>
                        <input type="text" id="task_type" name="task_type" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" placeholder="Email" required>
                    </div>
                    
                    <div class="mb-4">
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description (Optional)</label>
                        <textarea id="description" name="description" rows="2" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" placeholder="Check and respond to emails"></textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label for="estimated_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Estimated Time (minutes, optional)</label>
                        <input type="number" id="estimated_time" name="estimated_time" min="1" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" placeholder="15">
                    </div>
                    
                    <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-plus mr-1"></i> Add to Template
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Template Items -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Template Items</h2>
                <span class="text-sm text-gray-500 dark:text-gray-400"><?= count($template['items']) ?> items</span>
            </div>
            
            <?php if (empty($template['items'])): ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500 dark:text-gray-400">No items in this template yet</p>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        Add items using the form on the right
                    </p>
                </div>
            <?php else: ?>
                <ul id="template-items" class="divide-y divide-gray-200 dark:divide-gray-700">
                    <?php foreach ($template['items'] as $item): ?>
                        <li class="px-4 py-4 sm:px-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150" data-item-id="<?= $item['id'] ?>">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($item['task_type']) ?></p>
                                    <?php if (!empty($item['description'])): ?>
                                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1"><?= htmlspecialchars($item['description']) ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($item['estimated_time'])): ?>
                                        <span class="inline-flex items-center mt-2 px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                            <i class="fas fa-clock mr-1"></i> <?= $item['estimated_time'] ?> min
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-2">
                                    <a href="/momentum/productivity/remove-item-from-template/<?= $template['id'] ?>/<?= $item['id'] ?>" class="text-red-500 hover:text-red-700 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to remove this item from the template?')">
                                        <i class="fas fa-times"></i>
                                    </a>
                                </div>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Edit Template Modal -->
<div id="editTemplateModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Edit Template</h3>
            <form id="editTemplateForm" action="/momentum/productivity/update-template/<?= $template['id'] ?>" method="POST">
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Template Name</label>
                    <input type="text" id="name" name="name" value="<?= htmlspecialchars($template['name']) ?>" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" required>
                </div>
                
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description (Optional)</label>
                    <textarea id="description" name="description" rows="2" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"><?= htmlspecialchars($template['description']) ?></textarea>
                </div>
                
                <div class="mb-4">
                    <label for="energy_level" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Energy Level Required</label>
                    <select id="energy_level" name="energy_level" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                        <option value="high" <?= $template['energy_level'] === 'high' ? 'selected' : '' ?>>High Energy</option>
                        <option value="medium" <?= $template['energy_level'] === 'medium' ? 'selected' : '' ?>>Medium Energy</option>
                        <option value="low" <?= $template['energy_level'] === 'low' ? 'selected' : '' ?>>Low Energy</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label for="estimated_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Estimated Time (minutes, optional)</label>
                    <input type="number" id="estimated_time" name="estimated_time" min="1" value="<?= $template['estimated_time'] ?>" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                </div>
                
                <div class="mb-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="is_recurring" name="is_recurring" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 rounded" <?= $template['is_recurring'] ? 'checked' : '' ?>>
                        <label for="is_recurring" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                            Make this a recurring template
                        </label>
                    </div>
                    <div id="recurrenceOptions" class="mt-2 pl-6 <?= $template['is_recurring'] ? '' : 'hidden' ?>">
                        <label for="recurrence_pattern" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Recurrence Pattern</label>
                        <select id="recurrence_pattern" name="recurrence_pattern" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                            <option value="daily" <?= $template['recurrence_pattern'] === 'daily' ? 'selected' : '' ?>>Daily</option>
                            <option value="weekdays" <?= $template['recurrence_pattern'] === 'weekdays' ? 'selected' : '' ?>>Weekdays (Mon-Fri)</option>
                            <option value="weekly" <?= $template['recurrence_pattern'] === 'weekly' ? 'selected' : '' ?>>Weekly</option>
                            <option value="monthly" <?= $template['recurrence_pattern'] === 'monthly' ? 'selected' : '' ?>>Monthly</option>
                        </select>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelEditBtn" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Cancel
                    </button>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const editModal = document.getElementById('editTemplateModal');
    const editForm = document.getElementById('editTemplateForm');
    const editBtn = document.getElementById('editTemplateBtn');
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    const deleteBtn = document.getElementById('deleteTemplateBtn');
    const isRecurringCheckbox = document.getElementById('is_recurring');
    const recurrenceOptions = document.getElementById('recurrenceOptions');
    const templateItemsList = document.getElementById('template-items');
    
    // Show/hide recurrence options based on checkbox
    if (isRecurringCheckbox && recurrenceOptions) {
        isRecurringCheckbox.addEventListener('change', function() {
            recurrenceOptions.classList.toggle('hidden', !this.checked);
        });
    }
    
    // Open edit modal
    if (editBtn && editModal) {
        editBtn.addEventListener('click', function() {
            editModal.classList.remove('hidden');
        });
    }
    
    // Close edit modal
    if (cancelEditBtn && editModal) {
        cancelEditBtn.addEventListener('click', function() {
            editModal.classList.add('hidden');
        });
    }
    
    // Delete template confirmation
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to delete this template? This action cannot be undone.')) {
                window.location.href = '/momentum/productivity/delete-template/<?= $template['id'] ?>';
            }
        });
    }
    
    // Make items sortable
    if (templateItemsList) {
        new Sortable(templateItemsList, {
            animation: 150,
            ghostClass: 'bg-gray-100 dark:bg-gray-600',
            onEnd: function(evt) {
                // Get all item IDs in new order
                const itemIds = Array.from(templateItemsList.querySelectorAll('li')).map(li => li.getAttribute('data-item-id'));
                
                // Update item order via AJAX
                fetch(`/momentum/productivity/reorder-template-items/<?= $template['id'] ?>`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ item_ids: itemIds })
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        console.error('Error reordering items:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
        });
    }
});
</script>
