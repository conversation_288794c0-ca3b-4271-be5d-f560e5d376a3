<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Task Batching</h1>
            <div class="flex space-x-2">
                <button id="createBatchBtn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-plus mr-2"></i> Create Batch
                </button>
            </div>
        </div>

        <!-- Introduction -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-2">What is Task Batching?</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Task batching is a productivity technique where you group similar tasks together and complete them in a single session.
                This reduces context switching and helps you maintain focus, which is especially beneficial for ADHD.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-green-50 dark:bg-green-900 p-3 rounded-lg">
                    <h3 class="font-medium text-green-800 dark:text-green-200 mb-1">High Energy Tasks</h3>
                    <p class="text-sm text-green-700 dark:text-green-300">
                        Tasks that require deep focus, creativity, or complex problem-solving. Schedule these during your peak energy hours.
                    </p>
                </div>
                <div class="bg-yellow-50 dark:bg-yellow-900 p-3 rounded-lg">
                    <h3 class="font-medium text-yellow-800 dark:text-yellow-200 mb-1">Medium Energy Tasks</h3>
                    <p class="text-sm text-yellow-700 dark:text-yellow-300">
                        Routine tasks that require moderate focus. Good for average energy levels throughout the day.
                    </p>
                </div>
                <div class="bg-red-50 dark:bg-red-900 p-3 rounded-lg">
                    <h3 class="font-medium text-red-800 dark:text-red-200 mb-1">Low Energy Tasks</h3>
                    <p class="text-sm text-red-700 dark:text-red-300">
                        Simple, repetitive tasks that don't require much mental effort. Perfect for low energy periods.
                    </p>
                </div>
            </div>
            <div class="mt-4 flex justify-end">
                <a href="/momentum/productivity/batch-templates" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-copy mr-1"></i> Manage Batch Templates
                </a>
            </div>
        </div>

        <!-- Current Energy Level & Recommendations -->
        <?php if ($latestEnergyLevel): ?>
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Current Energy Level & Recommendations</h2>
                <a href="/momentum/productivity/energy-tracking" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                    Update energy level
                </a>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Current Energy Level -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                    <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Current Energy Level</h3>
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold mr-3
                            <?php
                            if ($latestEnergyLevel['level'] >= 8) {
                                echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                            } elseif ($latestEnergyLevel['level'] >= 5) {
                                echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                            } else {
                                echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
                            }
                            ?>
                        ">
                            <?= $latestEnergyLevel['level'] ?>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900 dark:text-white">
                                <?php
                                if ($latestEnergyLevel['level'] >= 8) {
                                    echo 'High Energy';
                                } elseif ($latestEnergyLevel['level'] >= 5) {
                                    echo 'Medium Energy';
                                } else {
                                    echo 'Low Energy';
                                }
                                ?>
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                Recorded: <?= date('g:i A', strtotime($latestEnergyLevel['recorded_at'])) ?>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Recommended Batches -->
                <div class="md:col-span-2 bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                    <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Recommended Batches</h3>
                    <?php if (!empty($recommendedBatches['batches'])): ?>
                        <div class="space-y-2">
                            <?php foreach ($recommendedBatches['batches'] as $batch): ?>
                                <a href="/momentum/productivity/view-batch/<?= $batch['id'] ?>" class="block p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-150">
                                    <div class="flex justify-between items-center">
                                        <div class="flex items-center">
                                            <div class="w-2 h-8 rounded-sm mr-3
                                                <?php
                                                if ($batch['energy_level'] === 'high') {
                                                    echo 'bg-green-500';
                                                } elseif ($batch['energy_level'] === 'medium') {
                                                    echo 'bg-yellow-500';
                                                } else {
                                                    echo 'bg-red-500';
                                                }
                                                ?>
                                            "></div>
                                            <div>
                                                <p class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($batch['name']) ?></p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                                    <?= $batch['task_count'] ?> tasks (<?= $batch['completed_count'] ?> completed)
                                                </p>
                                            </div>
                                        </div>
                                        <div class="text-gray-400 dark:text-gray-500">
                                            <i class="fas fa-chevron-right"></i>
                                        </div>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <p class="text-gray-500 dark:text-gray-400">No <?= $recommendedBatches['energy_level'] ?> energy batches found</p>
                            <button id="createRecommendedBatchBtn" class="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" data-energy-level="<?= $recommendedBatches['energy_level'] ?>">
                                <i class="fas fa-plus mr-1"></i> Create <?= ucfirst($recommendedBatches['energy_level']) ?> Energy Batch
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Batch Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-md p-3">
                        <i class="fas fa-layer-group text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Total Batches</h3>
                        <p class="text-2xl font-semibold text-gray-700 dark:text-gray-300"><?= $statistics['total_batches'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-3">
                        <i class="fas fa-bolt text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">High Energy</h3>
                        <p class="text-2xl font-semibold text-gray-700 dark:text-gray-300"><?= $statistics['high_energy_batches'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-yellow-100 dark:bg-yellow-900 rounded-md p-3">
                        <i class="fas fa-check text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Medium Energy</h3>
                        <p class="text-2xl font-semibold text-gray-700 dark:text-gray-300"><?= $statistics['medium_energy_batches'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-red-100 dark:bg-red-900 rounded-md p-3">
                        <i class="fas fa-battery-quarter text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Low Energy</h3>
                        <p class="text-2xl font-semibold text-gray-700 dark:text-gray-300"><?= $statistics['low_energy_batches'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Batches -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Your Task Batches</h2>
            </div>

            <?php if (empty($batches)): ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500 dark:text-gray-400 mb-4">You haven't created any task batches yet</p>
                    <button id="getStartedBtn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-plus mr-2"></i> Get Started
                    </button>
                </div>
            <?php else: ?>
                <div class="divide-y divide-gray-200 dark:divide-gray-700">
                    <?php foreach ($batches as $batch): ?>
                        <a href="/momentum/productivity/view-batch/<?= $batch['id'] ?>" class="block hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                            <div class="px-4 py-4 sm:px-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-2 h-12 rounded-sm mr-3
                                            <?php
                                            if ($batch['energy_level'] === 'high') {
                                                echo 'bg-green-500';
                                            } elseif ($batch['energy_level'] === 'medium') {
                                                echo 'bg-yellow-500';
                                            } else {
                                                echo 'bg-red-500';
                                            }
                                            ?>
                                        "></div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($batch['name']) ?></p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                <?= ucfirst($batch['energy_level']) ?> Energy •
                                                <?= $batch['task_count'] ?> tasks (<?= $batch['completed_count'] ?> completed)
                                                <?php if ($batch['estimated_time']): ?>
                                                    • <?= $batch['estimated_time'] ?> min
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                            <?php
                                            if ($batch['status'] === 'active') {
                                                echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                                            } elseif ($batch['status'] === 'completed') {
                                                echo 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
                                            } else {
                                                echo 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
                                            }
                                            ?>
                                        ">
                                            <?= ucfirst($batch['status']) ?>
                                        </span>
                                        <div class="ml-4 text-gray-400 dark:text-gray-500">
                                            <i class="fas fa-chevron-right"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Create Batch Modal -->
<div id="batchModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-4 py-5 sm:p-6">
            <h3 id="modal-title" class="text-lg font-medium text-gray-900 dark:text-white mb-4">Create Task Batch</h3>
            <form id="batchForm" action="/momentum/productivity/create-batch" method="POST">
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Batch Name</label>
                    <input type="text" id="name" name="name" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" required>
                </div>

                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description (Optional)</label>
                    <textarea id="description" name="description" rows="2" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"></textarea>
                </div>

                <div class="mb-4">
                    <label for="energy_level" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Energy Level Required</label>
                    <select id="energy_level" name="energy_level" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                        <option value="high">High Energy</option>
                        <option value="medium" selected>Medium Energy</option>
                        <option value="low">Low Energy</option>
                    </select>
                </div>

                <div class="mb-4">
                    <label for="estimated_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Estimated Time (minutes, optional)</label>
                    <input type="number" id="estimated_time" name="estimated_time" min="1" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                </div>

                <div class="mb-4">
                    <label for="task_selector" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Add Tasks (Optional)</label>
                    <select id="task_selector" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                        <option value="">Select a task to add</option>
                        <?php foreach ($unbatchedTasks as $task): ?>
                            <option value="<?= $task['id'] ?>"><?= htmlspecialchars($task['title']) ?></option>
                        <?php endforeach; ?>
                    </select>
                    <input type="hidden" id="task_ids" name="task_ids" value="">

                    <div id="selected_tasks" class="mt-2 space-y-2"></div>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelBtn" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Cancel
                    </button>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Create Batch
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const modal = document.getElementById('batchModal');
    const form = document.getElementById('batchForm');
    const createBtn = document.getElementById('createBatchBtn');
    const getStartedBtn = document.getElementById('getStartedBtn');
    const createRecommendedBtn = document.getElementById('createRecommendedBatchBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const taskSelector = document.getElementById('task_selector');
    const selectedTasksContainer = document.getElementById('selected_tasks');
    const taskIdsInput = document.getElementById('task_ids');

    // Selected tasks array
    let selectedTasks = [];

    // Open modal for creating a new batch
    function openCreateModal(energyLevel = 'medium') {
        // Reset form
        form.reset();
        document.getElementById('energy_level').value = energyLevel;
        selectedTasks = [];
        selectedTasksContainer.innerHTML = '';
        taskIdsInput.value = '';

        // Show modal
        modal.classList.remove('hidden');
    }

    if (createBtn) {
        createBtn.addEventListener('click', function() {
            openCreateModal();
        });
    }

    if (getStartedBtn) {
        getStartedBtn.addEventListener('click', function() {
            openCreateModal();
        });
    }

    if (createRecommendedBtn) {
        createRecommendedBtn.addEventListener('click', function() {
            const energyLevel = this.getAttribute('data-energy-level');
            openCreateModal(energyLevel);
        });
    }

    // Close modal
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            modal.classList.add('hidden');
        });
    }

    // Add task to batch
    if (taskSelector) {
        taskSelector.addEventListener('change', function() {
            const taskId = this.value;
            if (!taskId) return;

            // Check if task is already selected
            if (selectedTasks.includes(taskId)) {
                this.value = '';
                return;
            }

            // Add task to selected tasks
            selectedTasks.push(taskId);

            // Update hidden input
            taskIdsInput.value = selectedTasks.join(',');

            // Add task to UI
            const taskText = this.options[this.selectedIndex].text;
            const taskElement = document.createElement('div');
            taskElement.className = 'flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded';
            taskElement.innerHTML = `
                <span class="text-sm text-gray-700 dark:text-gray-300">${taskText}</span>
                <button type="button" class="text-red-500 hover:text-red-700 dark:hover:text-red-300" data-task-id="${taskId}">
                    <i class="fas fa-times"></i>
                </button>
            `;

            // Add remove event listener
            const removeBtn = taskElement.querySelector('button');
            removeBtn.addEventListener('click', function() {
                const taskId = this.getAttribute('data-task-id');

                // Remove task from selected tasks
                selectedTasks = selectedTasks.filter(id => id !== taskId);

                // Update hidden input
                taskIdsInput.value = selectedTasks.join(',');

                // Remove task from UI
                this.closest('div').remove();
            });

            selectedTasksContainer.appendChild(taskElement);

            // Reset select
            this.value = '';
        });
    }
});
</script>
