<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Focus Timer</h1>
            <a href="/momentum/productivity/focus-mode" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-expand mr-2"></i> Focus Mode
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <!-- Timer Display -->
                <div class="text-center mb-8">
                    <div class="text-6xl font-bold text-gray-900 dark:text-white mb-4 font-mono" id="timer-display">25:00</div>
                    <div class="flex justify-center space-x-4">
                        <button id="start-btn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-play mr-2"></i> Start
                        </button>
                        <button id="pause-btn" class="hidden inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200">
                            <i class="fas fa-pause mr-2"></i> Pause
                        </button>
                        <button id="reset-btn" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-redo mr-2"></i> Reset
                        </button>
                    </div>
                </div>

                <!-- Timer Settings -->
                <div class="mb-8">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Timer Settings</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="focus-time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Focus Time (minutes)</label>
                            <input type="number" id="focus-time" min="1" max="60" value="25" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                        </div>
                        <div>
                            <label for="short-break" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Short Break (minutes)</label>
                            <input type="number" id="short-break" min="1" max="30" value="5" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                        </div>
                        <div>
                            <label for="long-break" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Long Break (minutes)</label>
                            <input type="number" id="long-break" min="5" max="60" value="15" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                        </div>
                    </div>
                </div>

                <!-- Current Task -->
                <div class="mb-8">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Current Task</h3>
                    <?php if ($task): ?>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2"><?= View::escape($task['title']) ?></h4>
                            <?php if (!empty($task['description'])): ?>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mb-2"><?= nl2br(View::escape($task['description'])) ?></p>
                            <?php endif; ?>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <?php if ($task['priority'] === 'high'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 mr-2">
                                            High Priority
                                        </span>
                                    <?php elseif ($task['priority'] === 'urgent'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 mr-2">
                                            Urgent
                                        </span>
                                    <?php endif; ?>
                                    <?php if (!empty($task['category_id']) && !empty($task['category_color'])): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" style="background-color: <?= $task['category_color'] ?>25; color: <?= $task['category_color'] ?>;">
                                            <?= View::escape($task['category_name'] ?? 'Category') ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <a href="/momentum/tasks/view/<?= $task['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                    View Task <i class="fas fa-arrow-right ml-1"></i>
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">No task selected</p>
                                </div>
                                <a href="/momentum/tasks" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                    Select a task <i class="fas fa-arrow-right ml-1"></i>
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Today's Tasks -->
                <?php if (!empty($todayTasks)): ?>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Today's Tasks</h3>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                            <ul class="divide-y divide-gray-200 dark:divide-gray-600">
                                <?php foreach ($todayTasks as $todayTask): ?>
                                    <li class="px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-150">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <input type="checkbox" data-task-id="<?= $todayTask['id'] ?>" class="task-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 rounded" <?= $todayTask['status'] === 'done' ? 'checked' : '' ?>>
                                                <a href="/momentum/tasks/view/<?= $todayTask['id'] ?>" class="ml-3 text-sm font-medium text-gray-900 dark:text-white <?= $todayTask['status'] === 'done' ? 'line-through text-gray-500 dark:text-gray-400' : '' ?>">
                                                    <?= View::escape($todayTask['title']) ?>
                                                </a>
                                                <?php if ($todayTask['priority'] === 'high' || $todayTask['priority'] === 'urgent'): ?>
                                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $todayTask['priority'] === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' ?>">
                                                        <?= ucfirst($todayTask['priority']) ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <a href="/momentum/productivity/focus-timer?task_id=<?= $todayTask['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                                <i class="fas fa-clock"></i> Focus
                                            </a>
                                        </div>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Timer elements
        const timerDisplay = document.getElementById('timer-display');
        const startBtn = document.getElementById('start-btn');
        const pauseBtn = document.getElementById('pause-btn');
        const resetBtn = document.getElementById('reset-btn');
        const focusTimeInput = document.getElementById('focus-time');
        const shortBreakInput = document.getElementById('short-break');
        const longBreakInput = document.getElementById('long-break');

        // Timer variables
        let timer;
        let timeLeft;
        let isRunning = false;
        let mode = 'focus'; // focus, shortBreak, longBreak
        let pomodoroCount = 0;
        let startTime = 0; // Track when the timer was started
        let pausedAt = 0; // Track when the timer was paused

        // Audio notification
        const audio = new Audio('https://assets.mixkit.co/sfx/preview/mixkit-alarm-digital-clock-beep-989.mp3');

        // Save timer state to localStorage
        function saveTimerState() {
            const timerState = {
                timeLeft: timeLeft,
                isRunning: isRunning,
                mode: mode,
                pomodoroCount: pomodoroCount,
                startTime: startTime,
                pausedAt: pausedAt,
                focusTime: focusTimeInput.value,
                shortBreak: shortBreakInput.value,
                longBreak: longBreakInput.value
            };
            localStorage.setItem('focusTimerState', JSON.stringify(timerState));
        }

        // Load timer state from localStorage
        function loadTimerState() {
            const savedState = localStorage.getItem('focusTimerState');
            if (savedState) {
                const timerState = JSON.parse(savedState);

                // Set input values
                focusTimeInput.value = timerState.focusTime;
                shortBreakInput.value = timerState.shortBreak;
                longBreakInput.value = timerState.longBreak;

                // Restore timer state
                mode = timerState.mode;
                pomodoroCount = timerState.pomodoroCount;

                // Calculate time left if timer was running
                if (timerState.isRunning && timerState.startTime > 0) {
                    const elapsedSeconds = Math.floor((Date.now() - timerState.startTime) / 1000);
                    timeLeft = Math.max(0, timerState.timeLeft - elapsedSeconds);

                    // If timer should have completed while away, reset it
                    if (timeLeft <= 0) {
                        resetTimer();
                        return;
                    }

                    // Resume the timer
                    isRunning = true;
                    startBtn.classList.add('hidden');
                    pauseBtn.classList.remove('hidden');
                    startTime = timerState.startTime;

                    // Start the timer
                    timer = setInterval(function() {
                        timeLeft--;
                        updateTimerDisplay();
                        saveTimerState();

                        if (timeLeft <= 0) {
                            handleTimerComplete();
                        }
                    }, 1000);
                } else if (timerState.pausedAt > 0) {
                    // Timer was paused
                    timeLeft = timerState.timeLeft;
                    pausedAt = timerState.pausedAt;
                } else {
                    // Timer was reset or not started
                    timeLeft = timerState.timeLeft;
                }

                // Update display with correct colors
                updateTimerDisplay();
                updateTimerColors();

                return true;
            }
            return false;
        }

        // Update timer colors based on mode
        function updateTimerColors() {
            // Reset all classes first
            timerDisplay.classList.remove('text-gray-900', 'dark:text-white', 'text-green-600', 'dark:text-green-400', 'text-blue-600', 'dark:text-blue-400');

            // Add appropriate classes based on mode
            if (mode === 'focus') {
                timerDisplay.classList.add('text-gray-900', 'dark:text-white');
            } else if (mode === 'shortBreak') {
                timerDisplay.classList.add('text-green-600', 'dark:text-green-400');
            } else if (mode === 'longBreak') {
                timerDisplay.classList.add('text-blue-600', 'dark:text-blue-400');
            }
        }

        // Initialize timer
        function initTimer() {
            // Try to load saved state first
            if (!loadTimerState()) {
                // If no saved state, initialize with default values
                timeLeft = focusTimeInput.value * 60;
                updateTimerDisplay();
                saveTimerState();
            }
        }

        // Update timer display
        function updateTimerDisplay() {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            timerDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            // Update document title
            document.title = `(${timerDisplay.textContent}) Focus Timer - ADHD PMS`;
        }

        // Handle timer completion
        function handleTimerComplete() {
            clearInterval(timer);
            isRunning = false;
            audio.play();

            // Show notification if browser supports it
            if (Notification.permission === 'granted') {
                const notification = new Notification('Timer Completed', {
                    body: mode === 'focus' ? 'Time for a break!' : 'Time to focus!',
                    icon: '/favicon.ico'
                });
            }

            // Switch modes
            if (mode === 'focus') {
                pomodoroCount++;
                if (pomodoroCount % 4 === 0) {
                    mode = 'longBreak';
                    timeLeft = longBreakInput.value * 60;
                    updateTimerColors();
                } else {
                    mode = 'shortBreak';
                    timeLeft = shortBreakInput.value * 60;
                    updateTimerColors();
                }
            } else {
                mode = 'focus';
                timeLeft = focusTimeInput.value * 60;
                updateTimerColors();

                // Log focus session if task is selected
                const taskId = <?= $task ? $task['id'] : 'null' ?>;
                if (taskId) {
                    logFocusSession(taskId, focusTimeInput.value);
                }
            }

            updateTimerDisplay();
            startBtn.classList.remove('hidden');
            pauseBtn.classList.add('hidden');
            startTime = 0;
            pausedAt = 0;
            saveTimerState();
        }

        // Start timer
        function startTimer() {
            if (!isRunning) {
                isRunning = true;
                startBtn.classList.add('hidden');
                pauseBtn.classList.remove('hidden');

                // If resuming from pause, adjust the start time to account for the paused duration
                if (pausedAt > 0) {
                    startTime = Date.now() - (pausedAt - startTime);
                    pausedAt = 0;
                } else {
                    startTime = Date.now();
                }

                timer = setInterval(function() {
                    timeLeft--;
                    updateTimerDisplay();
                    saveTimerState();

                    if (timeLeft <= 0) {
                        handleTimerComplete();
                    }
                }, 1000);

                saveTimerState();
            }
        }

        // Pause timer
        function pauseTimer() {
            if (isRunning) {
                clearInterval(timer);
                isRunning = false;
                pausedAt = Date.now();
                startBtn.classList.remove('hidden');
                pauseBtn.classList.add('hidden');
                saveTimerState();
            }
        }

        // Reset timer
        function resetTimer() {
            clearInterval(timer);
            isRunning = false;
            mode = 'focus';
            pomodoroCount = 0;
            timeLeft = focusTimeInput.value * 60;
            startTime = 0;
            pausedAt = 0;
            updateTimerDisplay();
            updateTimerColors();
            startBtn.classList.remove('hidden');
            pauseBtn.classList.add('hidden');
            saveTimerState();
        }

        // Log focus session
        function logFocusSession(taskId, duration) {
            fetch('/momentum/productivity/log-focus-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    task_id: taskId,
                    duration: duration
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Focus session logged successfully');
                } else {
                    console.error('Failed to log focus session:', data.message);
                }
            })
            .catch(error => {
                console.error('Error logging focus session:', error);
            });
        }

        // Event listeners
        startBtn.addEventListener('click', startTimer);
        pauseBtn.addEventListener('click', pauseTimer);
        resetBtn.addEventListener('click', resetTimer);

        focusTimeInput.addEventListener('change', function() {
            if (mode === 'focus' && !isRunning) {
                timeLeft = this.value * 60;
                updateTimerDisplay();
                saveTimerState();
            }
        });

        shortBreakInput.addEventListener('change', function() {
            if (mode === 'shortBreak' && !isRunning) {
                timeLeft = this.value * 60;
                updateTimerDisplay();
                saveTimerState();
            }
        });

        longBreakInput.addEventListener('change', function() {
            if (mode === 'longBreak' && !isRunning) {
                timeLeft = this.value * 60;
                updateTimerDisplay();
                saveTimerState();
            }
        });

        // Request notification permission
        if (Notification.permission !== 'granted' && Notification.permission !== 'denied') {
            Notification.requestPermission();
        }

        // Initialize timer
        initTimer();

        // Save timer state when user navigates away
        window.addEventListener('beforeunload', function() {
            saveTimerState();
        });

        // Handle task checkboxes
        const taskCheckboxes = document.querySelectorAll('.task-checkbox');

        taskCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const taskId = this.getAttribute('data-task-id');

                if (this.checked) {
                    // Mark task as complete
                    fetch(`/momentum/tasks/complete/${taskId}`, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update UI
                            const taskTitle = this.nextElementSibling;
                            taskTitle.classList.add('line-through', 'text-gray-500', 'dark:text-gray-400');

                            // Show success message
                            const successMessage = document.createElement('div');
                            successMessage.className = 'fixed bottom-4 right-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md animate-fade-in z-50';
                            successMessage.innerHTML = `
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm">Task marked as complete!</p>
                                    </div>
                                </div>
                            `;
                            document.body.appendChild(successMessage);

                            // Remove message after 3 seconds
                            setTimeout(() => {
                                successMessage.classList.add('opacity-0', 'transition-opacity', 'duration-500');
                                setTimeout(() => {
                                    successMessage.remove();
                                }, 500);
                            }, 3000);
                        } else {
                            // Uncheck the checkbox if there was an error
                            this.checked = false;
                            alert('Failed to mark task as complete. Please try again.');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        this.checked = false;
                        alert('An error occurred. Please try again.');
                    });
                }
            });
        });
    });
</script>
