<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Energy Level Tracking</h1>
            <div class="flex space-x-2">
                <button id="recordEnergyBtn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-plus mr-2"></i> Record Energy Level
                </button>
            </div>
        </div>
        
        <!-- Period Selection -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Energy Level History</h2>
                <div class="flex space-x-2">
                    <a href="?period=day" class="px-3 py-1 text-sm rounded-md <?= $period === 'day' ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-400' ?>">Today</a>
                    <a href="?period=week" class="px-3 py-1 text-sm rounded-md <?= $period === 'week' ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-400' ?>">Week</a>
                    <a href="?period=month" class="px-3 py-1 text-sm rounded-md <?= $period === 'month' ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-400' ?>">Month</a>
                </div>
            </div>
        </div>
        
        <!-- Current Energy Level -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Current Energy Level</h3>
                <?php if ($latestEnergyLevel): ?>
                    <div class="flex items-center justify-center">
                        <div class="w-32 h-32 rounded-full flex items-center justify-center text-4xl font-bold
                            <?php
                            if ($latestEnergyLevel['level'] >= 8) {
                                echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                            } elseif ($latestEnergyLevel['level'] >= 5) {
                                echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                            } else {
                                echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
                            }
                            ?>
                        ">
                            <?= $latestEnergyLevel['level'] ?>/10
                        </div>
                    </div>
                    <div class="mt-4 text-center">
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            Recorded: <?= date('M j, Y g:i A', strtotime($latestEnergyLevel['recorded_at'])) ?>
                        </p>
                        <?php if (!empty($latestEnergyLevel['notes'])): ?>
                            <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
                                <?= htmlspecialchars($latestEnergyLevel['notes']) ?>
                            </p>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <p class="text-gray-500 dark:text-gray-400">No energy levels recorded yet</p>
                        <button id="recordFirstEnergyBtn" class="mt-4 inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-plus mr-1"></i> Record Your First
                        </button>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Energy Level by Hour -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Best Hours</h3>
                <?php if (!empty($hourlyAverages)): ?>
                    <div class="space-y-2">
                        <?php
                        // Sort hours by energy level (highest first)
                        arsort($hourlyAverages);
                        $topHours = array_slice($hourlyAverages, 0, 5, true);
                        
                        foreach ($topHours as $hour => $level):
                            $hourFormatted = date('g A', strtotime("$hour:00"));
                        ?>
                            <div class="flex items-center">
                                <div class="w-16 text-sm text-gray-700 dark:text-gray-300"><?= $hourFormatted ?></div>
                                <div class="flex-1">
                                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                        <div class="h-full rounded-full
                                            <?php
                                            if ($level >= 8) {
                                                echo 'bg-green-500';
                                            } elseif ($level >= 5) {
                                                echo 'bg-yellow-500';
                                            } else {
                                                echo 'bg-red-500';
                                            }
                                            ?>
                                        " style="width: <?= $level * 10 ?>%"></div>
                                    </div>
                                </div>
                                <div class="w-10 text-right text-sm font-medium text-gray-700 dark:text-gray-300">
                                    <?= $level ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="mt-4 text-center">
                        <a href="#hourly-chart" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                            View full chart
                        </a>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <p class="text-gray-500 dark:text-gray-400">Not enough data to show patterns</p>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                            Record your energy levels regularly to see your best hours
                        </p>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Energy Level by Day of Week -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Best Days</h3>
                <?php if (!empty($dayOfWeekAverages)): ?>
                    <div class="space-y-2">
                        <?php
                        // Sort days by energy level (highest first)
                        arsort($dayOfWeekAverages);
                        
                        $dayNames = [
                            1 => 'Sunday',
                            2 => 'Monday',
                            3 => 'Tuesday',
                            4 => 'Wednesday',
                            5 => 'Thursday',
                            6 => 'Friday',
                            7 => 'Saturday'
                        ];
                        
                        foreach ($dayOfWeekAverages as $dayOfWeek => $level):
                        ?>
                            <div class="flex items-center">
                                <div class="w-24 text-sm text-gray-700 dark:text-gray-300"><?= $dayNames[$dayOfWeek] ?></div>
                                <div class="flex-1">
                                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                        <div class="h-full rounded-full
                                            <?php
                                            if ($level >= 8) {
                                                echo 'bg-green-500';
                                            } elseif ($level >= 5) {
                                                echo 'bg-yellow-500';
                                            } else {
                                                echo 'bg-red-500';
                                            }
                                            ?>
                                        " style="width: <?= $level * 10 ?>%"></div>
                                    </div>
                                </div>
                                <div class="w-10 text-right text-sm font-medium text-gray-700 dark:text-gray-300">
                                    <?= $level ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <p class="text-gray-500 dark:text-gray-400">Not enough data to show patterns</p>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                            Record your energy levels regularly to see your best days
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Energy Level History -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Energy Level History</h3>
            <?php if (!empty($energyLevels)): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead>
                            <tr>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date & Time</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Level</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Notes</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($energyLevels as $energyLevel): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        <?= date('M j, Y g:i A', strtotime($energyLevel['recorded_at'])) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                            <?php
                                            if ($energyLevel['level'] >= 8) {
                                                echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                                            } elseif ($energyLevel['level'] >= 5) {
                                                echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                                            } else {
                                                echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
                                            }
                                            ?>
                                        ">
                                            <?= $energyLevel['level'] ?>/10
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        <?= htmlspecialchars($energyLevel['notes']) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <button class="edit-energy-btn text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300 mr-3" data-id="<?= $energyLevel['id'] ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <a href="/momentum/productivity/delete-energy-level/<?= $energyLevel['id'] ?>" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to delete this record?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">No energy levels recorded for this period</p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Hourly Chart -->
        <div id="hourly-chart" class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Energy Level by Hour</h3>
            <?php if (!empty($hourlyAverages)): ?>
                <div class="h-64">
                    <canvas id="hourlyChart"></canvas>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">Not enough data to show patterns</p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Recommendations -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Scheduling Recommendations</h3>
            <?php if (!empty($recommendations)): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                        <h4 class="font-medium text-green-800 dark:text-green-200 mb-2">High Energy Tasks</h4>
                        <p class="text-sm text-green-700 dark:text-green-300 mb-3">
                            Schedule challenging tasks during your high energy hours:
                        </p>
                        <ul class="space-y-1 text-sm">
                            <?php
                            $highEnergyHours = array_filter($recommendations, function($value) {
                                return $value === 'high';
                            });
                            
                            if (!empty($highEnergyHours)) {
                                foreach (array_keys($highEnergyHours) as $hour) {
                                    echo '<li class="flex items-center">';
                                    echo '<i class="fas fa-check-circle text-green-500 mr-2"></i>';
                                    echo date('g A', strtotime("$hour:00"));
                                    echo '</li>';
                                }
                            } else {
                                echo '<li class="text-green-700 dark:text-green-300">No high energy hours identified yet</li>';
                            }
                            ?>
                        </ul>
                    </div>
                    
                    <div class="bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg">
                        <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-2">Medium Energy Tasks</h4>
                        <p class="text-sm text-yellow-700 dark:text-yellow-300 mb-3">
                            Schedule routine tasks during your medium energy hours:
                        </p>
                        <ul class="space-y-1 text-sm">
                            <?php
                            $mediumEnergyHours = array_filter($recommendations, function($value) {
                                return $value === 'medium';
                            });
                            
                            if (!empty($mediumEnergyHours)) {
                                foreach (array_keys($mediumEnergyHours) as $hour) {
                                    echo '<li class="flex items-center">';
                                    echo '<i class="fas fa-check-circle text-yellow-500 mr-2"></i>';
                                    echo date('g A', strtotime("$hour:00"));
                                    echo '</li>';
                                }
                            } else {
                                echo '<li class="text-yellow-700 dark:text-yellow-300">No medium energy hours identified yet</li>';
                            }
                            ?>
                        </ul>
                    </div>
                    
                    <div class="bg-red-50 dark:bg-red-900 p-4 rounded-lg">
                        <h4 class="font-medium text-red-800 dark:text-red-200 mb-2">Low Energy Tasks</h4>
                        <p class="text-sm text-red-700 dark:text-red-300 mb-3">
                            Schedule simple tasks during your low energy hours:
                        </p>
                        <ul class="space-y-1 text-sm">
                            <?php
                            $lowEnergyHours = array_filter($recommendations, function($value) {
                                return $value === 'low';
                            });
                            
                            if (!empty($lowEnergyHours)) {
                                foreach (array_keys($lowEnergyHours) as $hour) {
                                    echo '<li class="flex items-center">';
                                    echo '<i class="fas fa-check-circle text-red-500 mr-2"></i>';
                                    echo date('g A', strtotime("$hour:00"));
                                    echo '</li>';
                                }
                            } else {
                                echo '<li class="text-red-700 dark:text-red-300">No low energy hours identified yet</li>';
                            }
                            ?>
                        </ul>
                    </div>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">Not enough data to provide recommendations</p>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        Record your energy levels regularly to get personalized scheduling recommendations
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Energy Level Modal -->
<div id="energyLevelModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-4 py-5 sm:p-6">
            <h3 id="modal-title" class="text-lg font-medium text-gray-900 dark:text-white mb-4">Record Energy Level</h3>
            <form id="energyLevelForm" action="/momentum/productivity/record-energy-level" method="POST">
                <input type="hidden" id="energyLevelId" name="id" value="">
                
                <div class="mb-4">
                    <label for="level" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Energy Level (1-10)</label>
                    <div class="flex items-center space-x-2">
                        <input type="range" id="level" name="level" min="1" max="10" value="5" class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                        <span id="levelValue" class="text-lg font-medium text-gray-900 dark:text-white">5</span>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="recorded_at" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Date & Time</label>
                    <input type="datetime-local" id="recorded_at" name="recorded_at" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                </div>
                
                <div class="mb-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notes</label>
                    <textarea id="notes" name="notes" rows="3" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"></textarea>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelBtn" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Cancel
                    </button>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Save
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const modal = document.getElementById('energyLevelModal');
    const form = document.getElementById('energyLevelForm');
    const recordBtn = document.getElementById('recordEnergyBtn');
    const recordFirstBtn = document.getElementById('recordFirstEnergyBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const levelInput = document.getElementById('level');
    const levelValue = document.getElementById('levelValue');
    const editBtns = document.querySelectorAll('.edit-energy-btn');
    
    // Show level value
    levelInput.addEventListener('input', function() {
        levelValue.textContent = this.value;
    });
    
    // Open modal for recording new energy level
    function openRecordModal() {
        // Reset form
        form.reset();
        form.action = '/momentum/productivity/record-energy-level';
        document.getElementById('modal-title').textContent = 'Record Energy Level';
        document.getElementById('energyLevelId').value = '';
        
        // Set default date and time to now
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        
        document.getElementById('recorded_at').value = `${year}-${month}-${day}T${hours}:${minutes}`;
        
        // Show modal
        modal.classList.remove('hidden');
    }
    
    if (recordBtn) {
        recordBtn.addEventListener('click', openRecordModal);
    }
    
    if (recordFirstBtn) {
        recordFirstBtn.addEventListener('click', openRecordModal);
    }
    
    // Close modal
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            modal.classList.add('hidden');
        });
    }
    
    // Handle edit buttons
    editBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            // In a real app, you would fetch the energy level data via AJAX
            // For this demo, we'll just open the edit form
            form.action = `/momentum/productivity/update-energy-level/${id}`;
            document.getElementById('modal-title').textContent = 'Edit Energy Level';
            document.getElementById('energyLevelId').value = id;
            
            // Show modal
            modal.classList.remove('hidden');
        });
    });
    
    // Initialize hourly chart if data exists
    const hourlyChartCanvas = document.getElementById('hourlyChart');
    if (hourlyChartCanvas) {
        const hourlyData = <?= json_encode($hourlyAverages) ?>;
        
        if (Object.keys(hourlyData).length > 0) {
            const labels = [];
            const data = [];
            const backgroundColors = [];
            
            for (let hour = 0; hour < 24; hour++) {
                const hourFormatted = new Date(2022, 0, 1, hour).toLocaleTimeString([], { hour: 'numeric' });
                labels.push(hourFormatted);
                
                const level = hourlyData[hour] || 0;
                data.push(level);
                
                // Set color based on energy level
                if (level >= 8) {
                    backgroundColors.push('rgba(34, 197, 94, 0.7)'); // Green
                } else if (level >= 5) {
                    backgroundColors.push('rgba(234, 179, 8, 0.7)'); // Yellow
                } else if (level > 0) {
                    backgroundColors.push('rgba(239, 68, 68, 0.7)'); // Red
                } else {
                    backgroundColors.push('rgba(209, 213, 219, 0.7)'); // Gray
                }
            }
            
            new Chart(hourlyChartCanvas, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Average Energy Level',
                        data: data,
                        backgroundColor: backgroundColors,
                        borderColor: backgroundColors.map(color => color.replace('0.7', '1')),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 10,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }
    }
});
</script>
