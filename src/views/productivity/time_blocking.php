<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Time Blocking</h1>
            <div class="flex space-x-2">
                <button id="createTimeBlockBtn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-plus mr-2"></i> Create Time Block
                </button>
            </div>
        </div>

        <style>
            .time-block {
                cursor: grab;
                transition: box-shadow 0.2s ease;
                position: relative;
            }
            .time-block:hover {
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            }
            .time-block.dragging {
                cursor: grabbing;
                opacity: 0.8;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                z-index: 50;
            }
            .time-block.resizing {
                cursor: ns-resize;
                opacity: 0.9;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                z-index: 50;
            }
            .resize-handle {
                opacity: 0;
                transition: opacity 0.2s ease;
            }
            .time-block:hover .resize-handle {
                opacity: 1;
            }

            /* Mobile responsiveness */
            @media (max-width: 640px) {
                .day-view-container {
                    overflow-x: auto;
                }
                .week-view-container {
                    overflow-x: auto;
                }
                .week-view-grid {
                    min-width: 800px;
                }
                .month-view-day {
                    min-height: 80px;
                }
                .resize-handle {
                    height: 15px !important; /* Larger touch target for mobile */
                }
            }
        </style>

        <!-- Calendar View Controls -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex space-x-2">
                    <?php
                    $prevDate = '';
                    $nextDate = '';

                    switch ($viewType) {
                        case 'day':
                            $prevDate = date('Y-m-d', strtotime('-1 day', strtotime($date)));
                            $nextDate = date('Y-m-d', strtotime('+1 day', strtotime($date)));
                            break;
                        case 'week':
                            $prevDate = date('Y-m-d', strtotime('-1 week', strtotime($date)));
                            $nextDate = date('Y-m-d', strtotime('+1 week', strtotime($date)));
                            break;
                        case 'month':
                            $prevDate = date('Y-m-d', strtotime('-1 month', strtotime($date)));
                            $nextDate = date('Y-m-d', strtotime('+1 month', strtotime($date)));
                            break;
                    }
                    ?>
                    <a href="?view=<?= $viewType ?>&date=<?= $prevDate ?>" class="btn">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    <h2 class="text-lg font-medium">
                        <?php
                        switch ($viewType) {
                            case 'day':
                                echo date('F j, Y', strtotime($date));
                                break;
                            case 'week':
                                $startOfWeek = date('M j', strtotime('monday this week', strtotime($date)));
                                $endOfWeek = date('M j, Y', strtotime('sunday this week', strtotime($date)));
                                echo $startOfWeek . ' - ' . $endOfWeek;
                                break;
                            case 'month':
                                echo date('F Y', strtotime($date));
                                break;
                        }
                        ?>
                    </h2>
                    <a href="?view=<?= $viewType ?>&date=<?= $nextDate ?>" class="btn">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex space-x-2">
                        <a href="?view=day&date=<?= date('Y-m-d') ?>" class="px-3 py-1 text-sm rounded-md <?= $viewType === 'day' ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-400' ?>">Day</a>
                        <a href="?view=week&date=<?= date('Y-m-d') ?>" class="px-3 py-1 text-sm rounded-md <?= $viewType === 'week' ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-400' ?>">Week</a>
                        <a href="?view=month&date=<?= date('Y-m-d') ?>" class="px-3 py-1 text-sm rounded-md <?= $viewType === 'month' ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-400' ?>">Month</a>
                    </div>
                    <button id="show-shortcuts" class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 flex items-center">
                        <i class="fas fa-keyboard mr-1"></i> Shortcuts
                    </button>
                </div>
            </div>
        </div>

        <!-- Energy Level Insights -->
        <?php if ($viewType === 'day' && !empty($energyRecommendations)): ?>
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Energy Level Insights</h3>
                <a href="/momentum/productivity/energy-tracking" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                    View all data
                </a>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Current Energy Level -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Current Energy Level</h4>
                    <?php if ($latestEnergyLevel): ?>
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold mr-3
                                <?php
                                if ($latestEnergyLevel['level'] >= 8) {
                                    echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                                } elseif ($latestEnergyLevel['level'] >= 5) {
                                    echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                                } else {
                                    echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
                                }
                                ?>
                            ">
                                <?= $latestEnergyLevel['level'] ?>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white">
                                    <?php
                                    if ($latestEnergyLevel['level'] >= 8) {
                                        echo 'High Energy';
                                    } elseif ($latestEnergyLevel['level'] >= 5) {
                                        echo 'Medium Energy';
                                    } else {
                                        echo 'Low Energy';
                                    }
                                    ?>
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    Recorded: <?= date('g:i A', strtotime($latestEnergyLevel['recorded_at'])) ?>
                                </p>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="flex items-center justify-between">
                            <p class="text-sm text-gray-500 dark:text-gray-400">No data recorded</p>
                            <a href="/momentum/productivity/energy-tracking" class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                Record now
                            </a>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Best Hours -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Best Hours Today</h4>
                    <div class="space-y-1">
                        <?php
                        // Get current hour
                        $currentHour = (int)date('G');

                        // Get the next 3 hours
                        $upcomingHours = [];
                        for ($i = 0; $i < 3; $i++) {
                            $hour = ($currentHour + $i) % 24;
                            $recommendation = $energyRecommendations[$hour] ?? 'unknown';
                            $hourFormatted = date('g A', strtotime("$hour:00"));

                            $bgColor = '';
                            $textColor = '';
                            $icon = '';

                            switch ($recommendation) {
                                case 'high':
                                    $bgColor = 'bg-green-100 dark:bg-green-900';
                                    $textColor = 'text-green-800 dark:text-green-200';
                                    $icon = 'fa-bolt';
                                    break;
                                case 'medium':
                                    $bgColor = 'bg-yellow-100 dark:bg-yellow-900';
                                    $textColor = 'text-yellow-800 dark:text-yellow-200';
                                    $icon = 'fa-check';
                                    break;
                                case 'low':
                                    $bgColor = 'bg-red-100 dark:bg-red-900';
                                    $textColor = 'text-red-800 dark:text-red-200';
                                    $icon = 'fa-battery-quarter';
                                    break;
                                default:
                                    $bgColor = 'bg-gray-100 dark:bg-gray-800';
                                    $textColor = 'text-gray-800 dark:text-gray-200';
                                    $icon = 'fa-question';
                            }

                            echo '<div class="flex items-center p-1 rounded ' . $bgColor . ' ' . $textColor . '">';
                            echo '<i class="fas ' . $icon . ' mr-2"></i>';
                            echo '<span class="text-sm">' . $hourFormatted . '</span>';
                            echo '<span class="ml-auto text-xs">' . ucfirst($recommendation) . '</span>';
                            echo '</div>';
                        }
                        ?>
                    </div>
                </div>

                <!-- Scheduling Tips -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Scheduling Tips</h4>
                    <ul class="text-xs space-y-1 text-gray-600 dark:text-gray-400">
                        <li class="flex items-start">
                            <i class="fas fa-circle text-green-500 mt-1 mr-2 text-xs"></i>
                            <span>Schedule challenging tasks during high energy hours</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-circle text-yellow-500 mt-1 mr-2 text-xs"></i>
                            <span>Use medium energy hours for routine work</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-circle text-red-500 mt-1 mr-2 text-xs"></i>
                            <span>Save simple tasks for low energy periods</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Keyboard Shortcuts Help -->
        <div id="shortcuts-help" class="hidden bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Keyboard Shortcuts</h3>
                <button id="close-shortcuts" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div>
                    <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">Navigation</h4>
                    <ul class="space-y-2 text-sm">
                        <li class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs">←</kbd>
                            <span>Previous period</span>
                        </li>
                        <li class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs">→</kbd>
                            <span>Next period</span>
                        </li>
                        <li class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs">T</kbd>
                            <span>Today</span>
                        </li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">Views</h4>
                    <ul class="space-y-2 text-sm">
                        <li class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs">D</kbd>
                            <span>Day view</span>
                        </li>
                        <li class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs">W</kbd>
                            <span>Week view</span>
                        </li>
                        <li class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs">M</kbd>
                            <span>Month view</span>
                        </li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">Actions</h4>
                    <ul class="space-y-2 text-sm">
                        <li class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs">N</kbd>
                            <span>New time block</span>
                        </li>
                        <li class="flex items-center">
                            <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs">Esc</kbd>
                            <span>Close modal</span>
                        </li>
                        <li class="flex items-center">
                            <span class="text-xs text-gray-500 dark:text-gray-400">Drag & Drop to reschedule</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Calendar Grid -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <?php if ($viewType === 'day'): ?>
                <!-- Day View -->
                <div class="p-4 day-view-container">
                    <div class="grid grid-cols-1 gap-4 day-view">
                        <?php
                        // Create time slots from 6 AM to 10 PM
                        $startHour = 6;
                        $endHour = 22;

                        for ($hour = $startHour; $hour <= $endHour; $hour++) {
                            $timeLabel = date('g:i A', strtotime("$hour:00"));
                            $hourStart = date('Y-m-d H:i:s', strtotime($date . " $hour:00:00"));
                            $hourEnd = date('Y-m-d H:i:s', strtotime($date . " " . ($hour + 1) . ":00:00"));

                            // Find time blocks for this hour
                            $hourBlocks = array_filter($timeBlocks, function($block) use ($hourStart, $hourEnd) {
                                return (
                                    (strtotime($block['start_time']) >= strtotime($hourStart) && strtotime($block['start_time']) < strtotime($hourEnd)) ||
                                    (strtotime($block['end_time']) > strtotime($hourStart) && strtotime($block['end_time']) <= strtotime($hourEnd)) ||
                                    (strtotime($block['start_time']) <= strtotime($hourStart) && strtotime($block['end_time']) >= strtotime($hourEnd))
                                );
                            });
                        ?>
                            <div class="flex">
                                <div class="w-20 py-2 text-sm text-gray-600 dark:text-gray-400">
                                    <?= $timeLabel ?>
                                </div>
                                <div class="flex-1 min-h-[60px] border-t border-gray-200 dark:border-gray-700 relative">
                                    <?php foreach ($hourBlocks as $block): ?>
                                        <div class="absolute rounded-md p-2 text-sm text-white overflow-hidden time-block"
                                             style="background-color: <?= htmlspecialchars($block['color']) ?>; top: <?= max(0, (strtotime($block['start_time']) - strtotime($hourStart)) / 3600 * 100) ?>%; height: <?= min(100, (strtotime($block['end_time']) - strtotime($block['start_time'])) / 3600 * 100) ?>%; left: 0; right: 0;"
                                             data-block-id="<?= $block['id'] ?>">
                                            <div class="font-semibold"><?= htmlspecialchars($block['title']) ?></div>
                                            <?php if (!empty($block['task_title'])): ?>
                                                <div class="text-xs">Task: <?= htmlspecialchars($block['task_title']) ?></div>
                                            <?php endif; ?>
                                            <div class="text-xs">
                                                <?= date('g:i A', strtotime($block['start_time'])) ?> -
                                                <?= date('g:i A', strtotime($block['end_time'])) ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            <?php elseif ($viewType === 'week'): ?>
                <!-- Week View -->
                <div class="p-4 week-view-container">
                    <div class="grid grid-cols-8 gap-4 week-view-grid">
                        <!-- Time column -->
                        <div class="col-span-1">
                            <div class="h-10"></div> <!-- Empty header cell -->
                            <?php
                            // Create time slots from 6 AM to 10 PM
                            $startHour = 6;
                            $endHour = 22;

                            for ($hour = $startHour; $hour <= $endHour; $hour++) {
                                $timeLabel = date('g:i A', strtotime("$hour:00"));
                            ?>
                                <div class="h-20 flex items-start justify-end pr-2 text-sm text-gray-600 dark:text-gray-400">
                                    <?= $timeLabel ?>
                                </div>
                            <?php } ?>
                        </div>

                        <!-- Days of the week -->
                        <?php
                        $startOfWeek = date('Y-m-d', strtotime('monday this week', strtotime($date)));
                        $daysOfWeek = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

                        for ($dayIndex = 0; $dayIndex < 7; $dayIndex++) {
                            $currentDate = date('Y-m-d', strtotime("+$dayIndex day", strtotime($startOfWeek)));
                            $isToday = $currentDate === date('Y-m-d');
                            $dayLabel = $daysOfWeek[$dayIndex] . ' ' . date('j', strtotime($currentDate));
                        ?>
                            <div class="col-span-1">
                                <div class="h-10 flex items-center justify-center font-medium <?= $isToday ? 'text-primary-600 dark:text-primary-400' : 'text-gray-700 dark:text-gray-300' ?>">
                                    <?= $dayLabel ?>
                                </div>

                                <?php
                                // Create time slots for each hour
                                for ($hour = $startHour; $hour <= $endHour; $hour++) {
                                    $hourStart = date('Y-m-d H:i:s', strtotime($currentDate . " $hour:00:00"));
                                    $hourEnd = date('Y-m-d H:i:s', strtotime($currentDate . " " . ($hour + 1) . ":00:00"));

                                    // Find time blocks for this hour and day
                                    $hourBlocks = array_filter($timeBlocks, function($block) use ($hourStart, $hourEnd) {
                                        return (
                                            (strtotime($block['start_time']) >= strtotime($hourStart) && strtotime($block['start_time']) < strtotime($hourEnd)) ||
                                            (strtotime($block['end_time']) > strtotime($hourStart) && strtotime($block['end_time']) <= strtotime($hourEnd)) ||
                                            (strtotime($block['start_time']) <= strtotime($hourStart) && strtotime($block['end_time']) >= strtotime($hourEnd))
                                        );
                                    });
                                ?>
                                    <div class="h-20 border-t border-gray-200 dark:border-gray-700 relative">
                                        <?php foreach ($hourBlocks as $block): ?>
                                            <div class="absolute rounded-md p-1 text-xs text-white overflow-hidden time-block"
                                                 style="background-color: <?= htmlspecialchars($block['color']) ?>; top: <?= max(0, (strtotime($block['start_time']) - strtotime($hourStart)) / 3600 * 100) ?>%; height: <?= min(100, (strtotime($block['end_time']) - strtotime($block['start_time'])) / 3600 * 100) ?>%; left: 2px; right: 2px;"
                                                 data-block-id="<?= $block['id'] ?>">
                                                <div class="font-semibold truncate"><?= htmlspecialchars($block['title']) ?></div>
                                                <div class="truncate">
                                                    <?= date('g:i A', strtotime($block['start_time'])) ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php } ?>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            <?php else: ?>
                <!-- Month View -->
                <div class="grid grid-cols-7 gap-px bg-gray-200 dark:bg-gray-700">
                    <?php
                    $days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                    foreach ($days as $day): ?>
                        <div class="p-2 text-center text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800">
                            <?= $day ?>
                        </div>
                    <?php endforeach; ?>
                </div>
                <div class="grid grid-cols-7 gap-px bg-gray-200 dark:bg-gray-700 month-view-grid">
                    <?php
                    $startOfMonth = date('Y-m-01', strtotime($date));
                    $endOfMonth = date('Y-m-t', strtotime($date));
                    $firstDayOfWeek = date('w', strtotime($startOfMonth));
                    $lastDay = date('j', strtotime($endOfMonth));

                    // Add empty cells for days before the first day of the month
                    for ($i = 0; $i < $firstDayOfWeek; $i++): ?>
                        <div class="bg-white dark:bg-gray-800 p-2 min-h-[100px] month-view-day"></div>
                    <?php endfor; ?>

                    <?php // Days of the month
                    for ($day = 1; $day <= $lastDay; $day++) {
                        $currentDate = date('Y-m-d', strtotime($date . "-$day"));
                        $isToday = $currentDate === date('Y-m-d');

                        // Get time blocks for this day
                        $dayStart = $currentDate . ' 00:00:00';
                        $dayEnd = $currentDate . ' 23:59:59';

                        $dayBlocks = array_filter($timeBlocks, function($block) use ($dayStart, $dayEnd) {
                            return (
                                (strtotime($block['start_time']) >= strtotime($dayStart) && strtotime($block['start_time']) <= strtotime($dayEnd)) ||
                                (strtotime($block['end_time']) >= strtotime($dayStart) && strtotime($block['end_time']) <= strtotime($dayEnd)) ||
                                (strtotime($block['start_time']) <= strtotime($dayStart) && strtotime($block['end_time']) >= strtotime($dayEnd))
                            );
                        });
                    ?>
                        <div class="bg-white dark:bg-gray-800 p-2 min-h-[100px] month-view-day">
                            <div class="font-medium text-sm <?= $isToday ? 'text-primary-600 dark:text-primary-400' : 'text-gray-700 dark:text-gray-300' ?>">
                                <?= $day ?>
                            </div>
                            <div class="mt-1 space-y-1">
                                <?php foreach ($dayBlocks as $block): ?>
                                    <div class="text-xs p-1 rounded truncate text-white time-block"
                                         style="background-color: <?= htmlspecialchars($block['color']) ?>;"
                                         data-block-id="<?= $block['id'] ?>">
                                        <?= date('g:i A', strtotime($block['start_time'])) ?> - <?= htmlspecialchars($block['title']) ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Create/Edit Time Block Modal -->
<div id="timeBlockModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <form id="timeBlockForm" method="post" action="/momentum/productivity/create-time-block">
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">Create Time Block</h3>
                    <div class="mt-4 space-y-4">
                        <input type="hidden" id="timeBlockId" name="id">

                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Title</label>
                            <input type="text" name="title" id="title" class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" required>
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description (optional)</label>
                            <textarea name="description" id="description" rows="2" class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"></textarea>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                                <input type="date" name="start_date" id="start_date" class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" required>
                            </div>
                            <div>
                                <label for="start_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Time</label>
                                <input type="time" name="start_time" id="start_time" class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" required>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">End Date</label>
                                <input type="date" name="end_date" id="end_date" class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" required>
                            </div>
                            <div>
                                <label for="end_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">End Time</label>
                                <input type="time" name="end_time" id="end_time" class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" required>
                            </div>
                        </div>

                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Category (optional)</label>
                            <input type="text" name="category" id="category" class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                        </div>

                        <div>
                            <label for="color" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Color</label>
                            <input type="color" name="color" id="color" value="#4F46E5" class="mt-1 block w-full h-10 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                        </div>

                        <div>
                            <label for="task_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Associated Task (optional)</label>
                            <select name="task_id" id="task_id" class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                                <option value="">None</option>
                                <?php foreach ($tasks as $task): ?>
                                    <option value="<?= $task['id'] ?>"><?= htmlspecialchars($task['title']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" name="is_recurring" id="is_recurring" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                            <label for="is_recurring" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">Recurring</label>
                        </div>

                        <div id="recurrenceOptions" class="hidden">
                            <label for="recurrence_pattern" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Recurrence Pattern</label>
                            <select name="recurrence_pattern" id="recurrence_pattern" class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                                <option value="daily">Daily</option>
                                <option value="weekdays">Weekdays (Mon-Fri)</option>
                                <option value="weekly">Weekly</option>
                                <option value="biweekly">Bi-weekly</option>
                                <option value="monthly">Monthly</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Save
                    </button>
                    <button type="button" id="cancelBtn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                    <button type="button" id="deleteBtn" class="mt-3 w-full inline-flex justify-center rounded-md border border-red-300 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-red-700 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:w-auto sm:text-sm hidden">
                        Delete
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="/momentum/public/assets/js/time-blocking.js"></script>
