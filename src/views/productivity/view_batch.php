<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/productivity/task-batching" class="mr-4 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white"><?= htmlspecialchars($batch['name']) ?></h1>
            <span class="ml-3 px-2 py-1 text-xs font-semibold rounded-full 
                <?php
                if ($batch['energy_level'] === 'high') {
                    echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                } elseif ($batch['energy_level'] === 'medium') {
                    echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                } else {
                    echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
                }
                ?>
            ">
                <?= ucfirst($batch['energy_level']) ?> Energy
            </span>
            <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full 
                <?php
                if ($batch['status'] === 'active') {
                    echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                } elseif ($batch['status'] === 'completed') {
                    echo 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
                } else {
                    echo 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
                }
                ?>
            ">
                <?= ucfirst($batch['status']) ?>
            </span>
        </div>
        
        <!-- Batch Details -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- Batch Info -->
            <div class="md:col-span-2 bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex justify-between items-start mb-4">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Batch Details</h2>
                    <div class="flex space-x-2">
                        <button id="editBatchBtn" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-edit mr-1"></i> Edit
                        </button>
                        <button id="deleteBatchBtn" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            <i class="fas fa-trash-alt mr-1"></i> Delete
                        </button>
                    </div>
                </div>
                
                <?php if (!empty($batch['description'])): ?>
                    <div class="mb-4">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</h3>
                        <p class="text-gray-600 dark:text-gray-400"><?= nl2br(htmlspecialchars($batch['description'])) ?></p>
                    </div>
                <?php endif; ?>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Energy Level</h3>
                        <p class="text-gray-600 dark:text-gray-400"><?= ucfirst($batch['energy_level']) ?></p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Estimated Time</h3>
                        <p class="text-gray-600 dark:text-gray-400"><?= $batch['estimated_time'] ? $batch['estimated_time'] . ' minutes' : 'Not specified' ?></p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Created</h3>
                        <p class="text-gray-600 dark:text-gray-400"><?= date('M j, Y', strtotime($batch['created_at'])) ?></p>
                    </div>
                </div>
                
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Progress</h3>
                        <div class="flex items-center">
                            <div class="w-48 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                <?php 
                                $progress = $batch['task_count'] > 0 ? ($batch['completed_count'] / $batch['task_count']) * 100 : 0;
                                ?>
                                <div class="h-full bg-primary-500" style="width: <?= $progress ?>%"></div>
                            </div>
                            <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">
                                <?= $batch['completed_count'] ?>/<?= $batch['task_count'] ?> tasks
                            </span>
                        </div>
                    </div>
                    <button id="scheduleBatchBtn" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-calendar-alt mr-1"></i> Schedule
                    </button>
                </div>
            </div>
            
            <!-- Add Task -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Add Task</h2>
                <form action="/momentum/productivity/add-task-to-batch/<?= $batch['id'] ?>" method="POST">
                    <div class="mb-4">
                        <label for="task_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Select Task</label>
                        <select id="task_id" name="task_id" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" required>
                            <option value="">Select a task</option>
                            <?php foreach ($unbatchedTasks as $task): ?>
                                <option value="<?= $task['id'] ?>"><?= htmlspecialchars($task['title']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-plus mr-1"></i> Add to Batch
                    </button>
                </form>
                
                <?php if (empty($unbatchedTasks)): ?>
                    <div class="mt-4 text-center">
                        <p class="text-sm text-gray-500 dark:text-gray-400">No unbatched tasks available</p>
                        <a href="/momentum/tasks/create" class="mt-2 inline-flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                            <i class="fas fa-plus mr-1"></i> Create a new task
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Tasks in Batch -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Tasks in Batch</h2>
                <span class="text-sm text-gray-500 dark:text-gray-400"><?= $batch['task_count'] ?> tasks</span>
            </div>
            
            <?php if (empty($batch['tasks'])): ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500 dark:text-gray-400">No tasks in this batch yet</p>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        Add tasks using the form on the right
                    </p>
                </div>
            <?php else: ?>
                <ul id="batch-tasks" class="divide-y divide-gray-200 dark:divide-gray-700">
                    <?php foreach ($batch['tasks'] as $task): ?>
                        <li class="px-4 py-4 sm:px-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150" data-task-id="<?= $task['id'] ?>">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center flex-1">
                                    <input type="checkbox" data-task-id="<?= $task['id'] ?>" class="task-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 rounded" <?= $task['status'] === 'done' ? 'checked' : '' ?>>
                                    <div class="ml-3 flex-1">
                                        <a href="/momentum/tasks/view/<?= $task['id'] ?>" class="text-sm font-medium text-gray-900 dark:text-white <?= $task['status'] === 'done' ? 'line-through text-gray-500 dark:text-gray-400' : '' ?>">
                                            <?= htmlspecialchars($task['title']) ?>
                                        </a>
                                        <div class="flex flex-wrap items-center mt-1">
                                            <?php if (!empty($task['due_date'])): ?>
                                                <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">
                                                    <i class="far fa-calendar"></i> <?= date('M j, Y', strtotime($task['due_date'])) ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if (!empty($task['estimated_time'])): ?>
                                                <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">
                                                    <i class="fas fa-hourglass-half"></i> <?= $task['estimated_time'] ?> min
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($task['priority'] === 'high'): ?>
                                                <span class="text-orange-500 dark:text-orange-400 text-xs" title="High Priority">
                                                    <i class="fas fa-arrow-up"></i> High
                                                </span>
                                            <?php elseif ($task['priority'] === 'urgent'): ?>
                                                <span class="text-red-500 dark:text-red-400 text-xs" title="Urgent">
                                                    <i class="fas fa-exclamation-circle"></i> Urgent
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="ml-2">
                                    <a href="/momentum/productivity/remove-task-from-batch/<?= $batch['id'] ?>/<?= $task['id'] ?>" class="text-red-500 hover:text-red-700 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to remove this task from the batch?')">
                                        <i class="fas fa-times"></i>
                                    </a>
                                </div>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Edit Batch Modal -->
<div id="editBatchModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Edit Batch</h3>
            <form id="editBatchForm" action="/momentum/productivity/update-batch/<?= $batch['id'] ?>" method="POST">
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Batch Name</label>
                    <input type="text" id="name" name="name" value="<?= htmlspecialchars($batch['name']) ?>" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" required>
                </div>
                
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description (Optional)</label>
                    <textarea id="description" name="description" rows="2" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"><?= htmlspecialchars($batch['description']) ?></textarea>
                </div>
                
                <div class="mb-4">
                    <label for="energy_level" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Energy Level Required</label>
                    <select id="energy_level" name="energy_level" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                        <option value="high" <?= $batch['energy_level'] === 'high' ? 'selected' : '' ?>>High Energy</option>
                        <option value="medium" <?= $batch['energy_level'] === 'medium' ? 'selected' : '' ?>>Medium Energy</option>
                        <option value="low" <?= $batch['energy_level'] === 'low' ? 'selected' : '' ?>>Low Energy</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label for="estimated_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Estimated Time (minutes, optional)</label>
                    <input type="number" id="estimated_time" name="estimated_time" min="1" value="<?= $batch['estimated_time'] ?>" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                </div>
                
                <div class="mb-4">
                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                    <select id="status" name="status" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                        <option value="active" <?= $batch['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                        <option value="completed" <?= $batch['status'] === 'completed' ? 'selected' : '' ?>>Completed</option>
                        <option value="archived" <?= $batch['status'] === 'archived' ? 'selected' : '' ?>>Archived</option>
                    </select>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelEditBtn" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Cancel
                    </button>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Schedule Batch Modal -->
<div id="scheduleBatchModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Schedule Batch</h3>
            <form id="scheduleBatchForm" action="/momentum/productivity/schedule-batch/<?= $batch['id'] ?>" method="POST">
                <div class="mb-4">
                    <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Date</label>
                    <input type="date" id="start_date" name="start_date" value="<?= date('Y-m-d') ?>" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" required>
                </div>
                
                <div class="mb-4">
                    <label for="start_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Start Time</label>
                    <input type="time" id="start_time" name="start_time" value="<?= date('H:i') ?>" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" required>
                </div>
                
                <div class="mb-4">
                    <label for="duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Duration (minutes)</label>
                    <input type="number" id="duration" name="duration" min="15" value="<?= $batch['estimated_time'] ?: 60 ?>" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" required>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelScheduleBtn" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Cancel
                    </button>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Schedule
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const editModal = document.getElementById('editBatchModal');
    const editForm = document.getElementById('editBatchForm');
    const editBtn = document.getElementById('editBatchBtn');
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    
    const scheduleModal = document.getElementById('scheduleBatchModal');
    const scheduleForm = document.getElementById('scheduleBatchForm');
    const scheduleBtn = document.getElementById('scheduleBatchBtn');
    const cancelScheduleBtn = document.getElementById('cancelScheduleBtn');
    
    const deleteBtn = document.getElementById('deleteBatchBtn');
    const taskCheckboxes = document.querySelectorAll('.task-checkbox');
    const batchTasksList = document.getElementById('batch-tasks');
    
    // Open edit modal
    if (editBtn && editModal) {
        editBtn.addEventListener('click', function() {
            editModal.classList.remove('hidden');
        });
    }
    
    // Close edit modal
    if (cancelEditBtn && editModal) {
        cancelEditBtn.addEventListener('click', function() {
            editModal.classList.add('hidden');
        });
    }
    
    // Open schedule modal
    if (scheduleBtn && scheduleModal) {
        scheduleBtn.addEventListener('click', function() {
            scheduleModal.classList.remove('hidden');
        });
    }
    
    // Close schedule modal
    if (cancelScheduleBtn && scheduleModal) {
        cancelScheduleBtn.addEventListener('click', function() {
            scheduleModal.classList.add('hidden');
        });
    }
    
    // Delete batch confirmation
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to delete this batch? This action cannot be undone.')) {
                window.location.href = '/momentum/productivity/delete-batch/<?= $batch['id'] ?>';
            }
        });
    }
    
    // Task checkboxes
    taskCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const taskId = this.getAttribute('data-task-id');
            const status = this.checked ? 'done' : 'pending';
            
            // Update task status via AJAX
            fetch(`/momentum/tasks/update-status/${taskId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update UI
                    const taskTitle = this.closest('li').querySelector('a');
                    if (status === 'done') {
                        taskTitle.classList.add('line-through', 'text-gray-500', 'dark:text-gray-400');
                    } else {
                        taskTitle.classList.remove('line-through', 'text-gray-500', 'dark:text-gray-400');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Revert checkbox state on error
                this.checked = !this.checked;
            });
        });
    });
    
    // Make tasks sortable
    if (batchTasksList) {
        new Sortable(batchTasksList, {
            animation: 150,
            ghostClass: 'bg-gray-100 dark:bg-gray-600',
            onEnd: function(evt) {
                // Get all task IDs in new order
                const taskIds = Array.from(batchTasksList.querySelectorAll('li')).map(li => li.getAttribute('data-task-id'));
                
                // Update task order via AJAX
                fetch(`/momentum/productivity/reorder-batch-tasks/<?= $batch['id'] ?>`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ task_ids: taskIds })
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        console.error('Error reordering tasks:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
        });
    }
});
</script>
