<div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-medium text-gray-900 dark:text-white">Today's Schedule</h2>
        <a href="/momentum/productivity/time-blocking" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
            View Calendar <i class="fas fa-arrow-right ml-1"></i>
        </a>
    </div>
    
    <?php if (empty($timeBlocks)): ?>
        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
            <i class="fas fa-calendar-day text-2xl mb-2"></i>
            <p>No time blocks scheduled for today</p>
            <a href="/momentum/productivity/time-blocking" class="inline-flex items-center mt-2 px-3 py-1 text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                <i class="fas fa-plus mr-1"></i> Create Time Block
            </a>
        </div>
    <?php else: ?>
        <div class="space-y-3 max-h-64 overflow-y-auto pr-1">
            <?php 
            // Sort time blocks by start time
            usort($timeBlocks, function($a, $b) {
                return strtotime($a['start_time']) - strtotime($b['start_time']);
            });
            
            $currentTime = time();
            $hasCurrentBlock = false;
            
            foreach ($timeBlocks as $block): 
                $startTime = strtotime($block['start_time']);
                $endTime = strtotime($block['end_time']);
                $isCurrent = ($currentTime >= $startTime && $currentTime <= $endTime);
                $isPast = ($currentTime > $endTime);
                $isFuture = ($currentTime < $startTime);
                
                if ($isCurrent) {
                    $hasCurrentBlock = true;
                }
                
                $statusClass = $isCurrent ? 'border-l-4 border-green-500 dark:border-green-400' : 
                              ($isPast ? 'opacity-60' : '');
            ?>
                <div class="flex items-start p-2 rounded-md bg-gray-50 dark:bg-gray-700 <?= $statusClass ?>">
                    <div class="flex-shrink-0 w-12 text-center">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                            <?= date('g:i', $startTime) ?>
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            <?= date('A', $startTime) ?>
                        </div>
                    </div>
                    <div class="ml-3 flex-1">
                        <div class="flex items-center justify-between">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                <?= htmlspecialchars($block['title']) ?>
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                <?= date('g:i A', $startTime) ?> - <?= date('g:i A', $endTime) ?>
                            </div>
                        </div>
                        <?php if (!empty($block['task_title'])): ?>
                            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                <i class="fas fa-tasks mr-1"></i> <?= htmlspecialchars($block['task_title']) ?>
                            </div>
                        <?php endif; ?>
                        <?php if ($isCurrent): ?>
                            <div class="mt-1 text-xs text-green-600 dark:text-green-400">
                                <i class="fas fa-play-circle mr-1"></i> In progress
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <?php if (!$hasCurrentBlock): ?>
            <div class="mt-4 text-center text-sm text-gray-500 dark:text-gray-400">
                <i class="fas fa-info-circle mr-1"></i> No active time block right now
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>
