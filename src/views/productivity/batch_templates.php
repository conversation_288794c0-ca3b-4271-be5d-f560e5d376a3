<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Bat<PERSON> Templates</h1>
            <div class="flex space-x-2">
                <button id="createTemplateBtn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-plus mr-2"></i> Create Template
                </button>
            </div>
        </div>
        
        <!-- Introduction -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-2">What are Batch Templates?</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Batch templates allow you to create reusable task groupings for common workflows. 
                You can quickly generate new batches from templates and even set up recurring batches that are automatically created on a schedule.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-blue-50 dark:bg-blue-900 p-3 rounded-lg">
                    <h3 class="font-medium text-blue-800 dark:text-blue-200 mb-1">Regular Templates</h3>
                    <p class="text-sm text-blue-700 dark:text-blue-300">
                        Create templates for common workflows that you can manually generate batches from whenever needed.
                    </p>
                </div>
                <div class="bg-purple-50 dark:bg-purple-900 p-3 rounded-lg">
                    <h3 class="font-medium text-purple-800 dark:text-purple-200 mb-1">Recurring Templates</h3>
                    <p class="text-sm text-purple-700 dark:text-purple-300">
                        Set up templates that automatically generate new batches on a daily, weekday, weekly, or monthly schedule.
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Template Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-md p-3">
                        <i class="fas fa-copy text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Total Templates</h3>
                        <p class="text-2xl font-semibold text-gray-700 dark:text-gray-300"><?= $statistics['total_templates'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-purple-100 dark:bg-purple-900 rounded-md p-3">
                        <i class="fas fa-sync-alt text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Recurring</h3>
                        <p class="text-2xl font-semibold text-gray-700 dark:text-gray-300"><?= $statistics['recurring_templates'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-3">
                        <i class="fas fa-bolt text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">High Energy</h3>
                        <p class="text-2xl font-semibold text-gray-700 dark:text-gray-300"><?= $statistics['high_energy_templates'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-yellow-100 dark:bg-yellow-900 rounded-md p-3">
                        <i class="fas fa-check text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Medium Energy</h3>
                        <p class="text-2xl font-semibold text-gray-700 dark:text-gray-300"><?= $statistics['medium_energy_templates'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Templates List -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Your Templates</h2>
            </div>
            
            <?php if (empty($templates)): ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500 dark:text-gray-400 mb-4">You haven't created any templates yet</p>
                    <button id="getStartedBtn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-plus mr-2"></i> Get Started
                    </button>
                </div>
            <?php else: ?>
                <div class="divide-y divide-gray-200 dark:divide-gray-700">
                    <?php foreach ($templates as $template): ?>
                        <a href="/momentum/productivity/view-template/<?= $template['id'] ?>" class="block hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                            <div class="px-4 py-4 sm:px-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-2 h-12 rounded-sm mr-3
                                            <?php
                                            if ($template['energy_level'] === 'high') {
                                                echo 'bg-green-500';
                                            } elseif ($template['energy_level'] === 'medium') {
                                                echo 'bg-yellow-500';
                                            } else {
                                                echo 'bg-red-500';
                                            }
                                            ?>
                                        "></div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($template['name']) ?></p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                <?= ucfirst($template['energy_level']) ?> Energy • 
                                                <?= $template['item_count'] ?> items
                                                <?php if ($template['estimated_time']): ?>
                                                    • <?= $template['estimated_time'] ?> min
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <?php if ($template['is_recurring']): ?>
                                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 mr-2">
                                                <?= ucfirst($template['recurrence_pattern']) ?>
                                            </span>
                                        <?php endif; ?>
                                        <div class="ml-2 text-gray-400 dark:text-gray-500">
                                            <i class="fas fa-chevron-right"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Create Template Modal -->
<div id="templateModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-4 py-5 sm:p-6">
            <h3 id="modal-title" class="text-lg font-medium text-gray-900 dark:text-white mb-4">Create Template</h3>
            <form id="templateForm" action="/momentum/productivity/create-template" method="POST">
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Template Name</label>
                    <input type="text" id="name" name="name" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" required>
                </div>
                
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description (Optional)</label>
                    <textarea id="description" name="description" rows="2" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"></textarea>
                </div>
                
                <div class="mb-4">
                    <label for="energy_level" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Energy Level Required</label>
                    <select id="energy_level" name="energy_level" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                        <option value="high">High Energy</option>
                        <option value="medium" selected>Medium Energy</option>
                        <option value="low">Low Energy</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label for="estimated_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Estimated Time (minutes, optional)</label>
                    <input type="number" id="estimated_time" name="estimated_time" min="1" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                </div>
                
                <div class="mb-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="is_recurring" name="is_recurring" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 rounded">
                        <label for="is_recurring" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                            Make this a recurring template
                        </label>
                    </div>
                    <div id="recurrenceOptions" class="mt-2 pl-6 hidden">
                        <label for="recurrence_pattern" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Recurrence Pattern</label>
                        <select id="recurrence_pattern" name="recurrence_pattern" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                            <option value="daily">Daily</option>
                            <option value="weekdays">Weekdays (Mon-Fri)</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Template Items</label>
                    <div id="template-items" class="space-y-2">
                        <div class="template-item bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                            <div class="grid grid-cols-12 gap-2">
                                <div class="col-span-5">
                                    <label class="block text-xs text-gray-500 dark:text-gray-400">Task Type</label>
                                    <input type="text" name="task_types[]" class="mt-1 block w-full text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" placeholder="Email">
                                </div>
                                <div class="col-span-5">
                                    <label class="block text-xs text-gray-500 dark:text-gray-400">Description</label>
                                    <input type="text" name="item_descriptions[]" class="mt-1 block w-full text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" placeholder="Check and respond to emails">
                                </div>
                                <div class="col-span-2">
                                    <label class="block text-xs text-gray-500 dark:text-gray-400">Time (min)</label>
                                    <input type="number" name="item_times[]" min="1" class="mt-1 block w-full text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" placeholder="15">
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="button" id="add-item-btn" class="mt-2 inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-plus mr-1"></i> Add Item
                    </button>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelBtn" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Cancel
                    </button>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Create Template
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const modal = document.getElementById('templateModal');
    const form = document.getElementById('templateForm');
    const createBtn = document.getElementById('createTemplateBtn');
    const getStartedBtn = document.getElementById('getStartedBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const isRecurringCheckbox = document.getElementById('is_recurring');
    const recurrenceOptions = document.getElementById('recurrenceOptions');
    const templateItems = document.getElementById('template-items');
    const addItemBtn = document.getElementById('add-item-btn');
    
    // Show/hide recurrence options based on checkbox
    if (isRecurringCheckbox && recurrenceOptions) {
        isRecurringCheckbox.addEventListener('change', function() {
            recurrenceOptions.classList.toggle('hidden', !this.checked);
        });
    }
    
    // Open modal for creating a new template
    function openCreateModal() {
        // Reset form
        form.reset();
        
        // Clear template items except the first one
        const items = templateItems.querySelectorAll('.template-item');
        for (let i = 1; i < items.length; i++) {
            items[i].remove();
        }
        
        // Reset the first item's inputs
        const firstItem = templateItems.querySelector('.template-item');
        if (firstItem) {
            const inputs = firstItem.querySelectorAll('input');
            inputs.forEach(input => input.value = '');
        }
        
        // Show modal
        modal.classList.remove('hidden');
    }
    
    if (createBtn) {
        createBtn.addEventListener('click', openCreateModal);
    }
    
    if (getStartedBtn) {
        getStartedBtn.addEventListener('click', openCreateModal);
    }
    
    // Close modal
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            modal.classList.add('hidden');
        });
    }
    
    // Add template item
    if (addItemBtn && templateItems) {
        addItemBtn.addEventListener('click', function() {
            const newItem = document.createElement('div');
            newItem.className = 'template-item bg-gray-50 dark:bg-gray-700 p-3 rounded-lg';
            newItem.innerHTML = `
                <div class="grid grid-cols-12 gap-2">
                    <div class="col-span-5">
                        <label class="block text-xs text-gray-500 dark:text-gray-400">Task Type</label>
                        <input type="text" name="task_types[]" class="mt-1 block w-full text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" placeholder="Email">
                    </div>
                    <div class="col-span-5">
                        <label class="block text-xs text-gray-500 dark:text-gray-400">Description</label>
                        <input type="text" name="item_descriptions[]" class="mt-1 block w-full text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" placeholder="Check and respond to emails">
                    </div>
                    <div class="col-span-2">
                        <label class="block text-xs text-gray-500 dark:text-gray-400">Time (min)</label>
                        <input type="number" name="item_times[]" min="1" class="mt-1 block w-full text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" placeholder="15">
                    </div>
                </div>
                <button type="button" class="remove-item-btn mt-2 text-xs text-red-500 hover:text-red-700 dark:hover:text-red-300">
                    <i class="fas fa-times mr-1"></i> Remove
                </button>
            `;
            
            templateItems.appendChild(newItem);
            
            // Add remove event listener
            const removeBtn = newItem.querySelector('.remove-item-btn');
            if (removeBtn) {
                removeBtn.addEventListener('click', function() {
                    newItem.remove();
                });
            }
        });
    }
});
</script>
