<?php
/**
 * Main Navigation Header
 *
 * This partial contains the main navigation header with tab buttons
 * for easy access to different sections of the application.
 */

// Get the current path to determine active tab
$currentPath = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
$basePath = '/momentum';

// Function to check if a path is active
if (!function_exists('isActive')) {
    function isActive($path) {
        global $currentPath, $basePath;

    // Ensure $currentPath is a string to avoid deprecated warning with strpos()
    if (!is_string($currentPath)) {
        $currentPath = (string)$currentPath;
    }

    if ($path === '/dashboard' && ($currentPath === $basePath || $currentPath === $basePath . '/')) {
        return true;
    }

    // Make sure we have a valid string for both parameters
    $fullPath = $basePath . $path;
    return is_string($fullPath) && strpos($currentPath, $fullPath) === 0;
    }
}

// Function to generate active class
if (!function_exists('activeClass')) {
    function activeClass($path) {
        return isActive($path)
            ? 'border-primary-500 text-primary-600 dark:text-primary-400'
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600';
    }
}
?>

<!-- Dropdown styles are now in fixed-dropdown.css -->

<!-- Dropdown overlay for capturing clicks outside -->
<div id="dropdown-overlay" class="dropdown-overlay"></div>

<!-- ADHD Dropdown Menu (fixed position) -->
<div id="adhd-dropdown-menu" class="dropdown-menu">
    <a href="/momentum/adhd">
        <i class="fas fa-tachometer-alt mr-2"></i> ADHD Dashboard
    </a>
    <a href="/momentum/adhd/symptom-tracker">
        <i class="fas fa-chart-line mr-2"></i> Symptom Tracker
    </a>
    <a href="/momentum/adhd/cbt/thought-records">
        <i class="fas fa-comments mr-2"></i> Thought Records
    </a>
    <a href="/momentum/adhd/productivity/strategies">
        <i class="fas fa-lightbulb mr-2"></i> Productivity Strategies
    </a>
    <a href="/momentum/adhd/mindfulness">
        <i class="fas fa-spa mr-2"></i> Mindfulness Exercises
    </a>
    <a href="/momentum/adhd/consistency/trackers">
        <i class="fas fa-calendar-check mr-2"></i> Consistency Trackers
    </a>
    <div class="border-t border-gray-200 dark:border-gray-600 my-2"></div>
    <div class="px-4 py-2">
        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">Coming Soon</span>
    </div>
    <a href="/momentum/medical/medication">
        <i class="fas fa-pills mr-2"></i> Medication Tracker
    </a>
    <div class="px-4 py-2 flex items-center justify-between text-gray-500 dark:text-gray-400">
        <span><i class="fas fa-exclamation-triangle mr-2"></i> Trigger Identification</span>
        <span class="px-2 py-1 text-xs rounded-md bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">Planned</span>
    </div>
    <div class="px-4 py-2 flex items-center justify-between text-gray-500 dark:text-gray-400">
        <span><i class="fas fa-brain mr-2"></i> Executive Function Exercises</span>
        <span class="px-2 py-1 text-xs rounded-md bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">Concept</span>
    </div>
</div>

<!-- Productivity Dropdown Menu (fixed position) -->
<div id="productivity-dropdown-menu" class="dropdown-menu">
    <a href="/momentum/productivity/tools">
        <i class="fas fa-th-large mr-2"></i> Productivity Tools
    </a>
    <div class="border-t border-gray-200 dark:border-gray-600 my-2"></div>
    <a href="/momentum/productivity/focus-timer">
        <i class="fas fa-stopwatch mr-2"></i> Focus Timer
    </a>
    <a href="/momentum/productivity/focus-mode">
        <i class="fas fa-expand mr-2"></i> Focus Mode
    </a>
    <a href="/momentum/productivity/time-blocking">
        <i class="fas fa-calendar-alt mr-2"></i> Time Blocking
    </a>
    <a href="/momentum/productivity/task-batching">
        <i class="fas fa-layer-group mr-2"></i> Task Batching
    </a>
    <a href="/momentum/productivity/batch-templates">
        <i class="fas fa-copy mr-2"></i> Batch Templates
    </a>
    <a href="/momentum/productivity/energy-tracking">
        <i class="fas fa-bolt mr-2"></i> Energy Tracking
    </a>
</div>

<!-- Tools Dropdown Menu (fixed position) -->
<div id="tools-dropdown-menu" class="dropdown-menu">
    <a href="/momentum/tools/currency-converter">
        <i class="fas fa-exchange-alt mr-2"></i> Currency Converter
    </a>
</div>

<!-- User Dropdown Menu (fixed position) -->
<div id="user-dropdown-menu" class="dropdown-menu">
    <a href="/momentum/dashboard/toggle-theme">
        <i class="fas fa-adjust mr-2"></i> Toggle Theme
    </a>
    <a href="/momentum/logout">
        <i class="fas fa-sign-out-alt mr-2"></i> Sign out
    </a>
</div>

<div class="bg-white dark:bg-gray-800 shadow" style="position: relative; z-index: 50;">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <!-- Logo and brand -->
            <div class="flex">
                <div class="flex-shrink-0 flex items-center">
                    <a href="/momentum/dashboard" class="text-xl font-bold text-primary-600 dark:text-primary-400">
                        <i class="fas fa-bolt mr-2"></i>Momentum
                    </a>
                </div>
            </div>

            <!-- Mobile menu button -->
            <div class="flex items-center -mr-2 md:hidden">
                <button type="button" id="mobile-menu-button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500">
                    <span class="sr-only">Open main menu</span>
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <!-- Desktop navigation -->
            <div class="hidden md:flex md:items-center md:space-x-4" id="desktop-navigation" style="display: flex !important;">
                <nav class="flex space-x-4">
                    <a href="/momentum/dashboard" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/dashboard') ?>">
                        <i class="fas fa-home mr-1"></i> Dashboard
                    </a>
                    <a href="/momentum/tasks" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/tasks') ?>">
                        <i class="fas fa-tasks mr-1"></i> Tasks
                    </a>
                    <a href="/momentum/projects" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/projects') ?>">
                        <i class="fas fa-project-diagram mr-1"></i> Projects
                    </a>
                    <a href="/momentum/reports" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/reports') ?>">
                        <i class="fas fa-chart-line mr-1"></i> Reports
                    </a>
                    <a href="/momentum/ideas" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/ideas') ?>">
                        <i class="fas fa-lightbulb mr-1"></i> Ideas
                    </a>
                    <a href="/momentum/notes" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/notes') ?>">
                        <i class="fas fa-sticky-note mr-1"></i> Notes
                    </a>
                    <a href="/momentum/finances" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/finances') ?>">
                        <i class="fas fa-dollar-sign mr-1"></i> Finances
                    </a>
                    <a href="/momentum/checklists" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/checklists') ?>">
                        <i class="fas fa-check-square mr-1"></i> Checklists
                    </a>
                    <?php if (class_exists('YouTubeAgentController')): ?>
                    <a href="/momentum/youtube-agent" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/youtube-agent') ?>">
                        <i class="fab fa-youtube mr-1"></i> YouTube Agent
                    </a>
                    <?php endif; ?>
                    <div class="relative inline-block text-left" id="adhd-dropdown">
                        <button type="button" id="adhd-dropdown-button" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= isActive('/adhd') ? activeClass('/adhd') : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600' ?>">
                            <i class="fas fa-brain mr-1"></i> ADHD <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                    </div>
                    <div class="relative inline-block text-left" id="productivity-dropdown">
                        <button type="button" id="productivity-dropdown-button" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= isActive('/productivity') ? activeClass('/productivity') : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600' ?>">
                            <i class="fas fa-clock mr-1"></i> Productivity <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                    </div>
                    <a href="/momentum/productivity/tools" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                        <i class="fas fa-th-large mr-1"></i> Productivity Tools
                    </a>
                    <div class="relative inline-block text-left" id="tools-dropdown">
                        <button type="button" id="tools-dropdown-button" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= isActive('/tools') ? activeClass('/tools') : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600' ?>">
                            <i class="fas fa-tools mr-1"></i> Tools <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                    </div>
                    <a href="/momentum/help" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/help') ?>">
                        <i class="fas fa-question-circle mr-1"></i> Help
                    </a>
                </nav>

                <!-- Tools icon -->
                <div class="ml-4 relative flex-shrink-0">
                    <a href="/momentum/tools/currency-converter" class="bg-white dark:bg-gray-700 rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 p-2 hover:bg-gray-100 dark:hover:bg-gray-600" title="Quick Currency Converter">
                        <span class="sr-only">Currency Converter</span>
                        <div class="h-5 w-5 flex items-center justify-center text-primary-600 dark:text-primary-300">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                    </a>
                </div>

                <!-- User menu -->
                <div class="ml-4 relative flex-shrink-0">
                    <div>
                        <button type="button" id="user-menu-button" class="bg-white dark:bg-gray-700 rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <span class="sr-only">Open user menu</span>
                            <div class="h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-800 flex items-center justify-center text-primary-600 dark:text-primary-300">
                                <i class="fas fa-user"></i>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile menu, show/hide based on menu state -->
    <div id="mobile-menu" class="hidden md:hidden" style="position: relative; z-index: 50;">
        <div class="pt-2 pb-3 space-y-1">
            <a href="/momentum/dashboard" class="<?= isActive('/dashboard') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-home mr-2"></i> Dashboard
            </a>
            <a href="/momentum/tasks" class="<?= isActive('/tasks') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-tasks mr-2"></i> Tasks
            </a>
            <a href="/momentum/projects" class="<?= isActive('/projects') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-project-diagram mr-2"></i> Projects
            </a>
            <a href="/momentum/reports" class="<?= isActive('/reports') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-chart-line mr-2"></i> Reports
            </a>
            <a href="/momentum/ideas" class="<?= isActive('/ideas') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-lightbulb mr-2"></i> Ideas
            </a>
            <a href="/momentum/notes" class="<?= isActive('/notes') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-sticky-note mr-2"></i> Notes
            </a>
            <a href="/momentum/finances" class="<?= isActive('/finances') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-dollar-sign mr-2"></i> Finances
            </a>
            <a href="/momentum/checklists" class="<?= isActive('/checklists') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-check-square mr-2"></i> Checklists
            </a>
            <?php if (class_exists('YouTubeAgentController')): ?>
            <a href="/momentum/youtube-agent" class="<?= isActive('/youtube-agent') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fab fa-youtube mr-2"></i> YouTube Agent
            </a>
            <?php endif; ?>
            <div class="border-l-4 <?= isActive('/adhd') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500' : 'border-transparent' ?>">
                <button id="mobile-adhd-button" class="w-full flex justify-between items-center pl-3 pr-4 py-2 <?= isActive('/adhd') ? 'text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-300' ?> text-base font-medium">
                    <span><i class="fas fa-brain mr-2"></i> ADHD</span>
                    <i class="fas fa-chevron-down text-xs"></i>
                </button>
                <div id="mobile-adhd-menu" class="hidden pl-6 pr-4 py-2 space-y-1">
                    <a href="/momentum/adhd" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-tachometer-alt mr-2"></i> ADHD Dashboard
                    </a>
                    <a href="/momentum/adhd/symptom-tracker" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-chart-line mr-2"></i> Symptom Tracker
                    </a>
                    <a href="/momentum/adhd/cbt/thought-records" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-comments mr-2"></i> Thought Records
                    </a>
                    <a href="/momentum/adhd/productivity/strategies" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-lightbulb mr-2"></i> Productivity Strategies
                    </a>
                    <a href="/momentum/adhd/mindfulness" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-spa mr-2"></i> Mindfulness Exercises
                    </a>
                    <a href="/momentum/adhd/consistency/trackers" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-calendar-check mr-2"></i> Consistency Trackers
                    </a>
                    <div class="border-t border-gray-200 dark:border-gray-600 my-2"></div>
                    <div class="py-2">
                        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">Coming Soon</span>
                    </div>
                    <a href="/momentum/medical/medication" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-pills mr-2"></i> Medication Tracker
                    </a>
                    <div class="py-2 flex items-center justify-between text-gray-500 dark:text-gray-400">
                        <span><i class="fas fa-exclamation-triangle mr-2"></i> Trigger Identification</span>
                        <span class="px-2 py-1 text-xs rounded-md bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">Planned</span>
                    </div>
                    <div class="py-2 flex items-center justify-between text-gray-500 dark:text-gray-400">
                        <span><i class="fas fa-brain mr-2"></i> Executive Function Exercises</span>
                        <span class="px-2 py-1 text-xs rounded-md bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">Concept</span>
                    </div>
                </div>
            </div>
            <div class="border-l-4 <?= isActive('/productivity') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500' : 'border-transparent' ?>">
                <button id="mobile-productivity-button" class="w-full flex justify-between items-center pl-3 pr-4 py-2 <?= isActive('/productivity') ? 'text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-300' ?> text-base font-medium">
                    <span><i class="fas fa-clock mr-2"></i> Productivity</span>
                    <i class="fas fa-chevron-down text-xs"></i>
                </button>
                <div id="mobile-productivity-menu" class="hidden pl-6 pr-4 py-2 space-y-1">
                    <a href="/momentum/productivity/tools" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-th-large mr-2"></i> Productivity Tools
                    </a>
                    <div class="border-t border-gray-200 dark:border-gray-600 my-2"></div>
                    <a href="/momentum/productivity/focus-timer" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-stopwatch mr-2"></i> Focus Timer
                    </a>
                    <a href="/momentum/productivity/focus-mode" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-expand mr-2"></i> Focus Mode
                    </a>
                    <a href="/momentum/productivity/time-blocking" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-calendar-alt mr-2"></i> Time Blocking
                    </a>
                    <a href="/momentum/productivity/task-batching" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-layer-group mr-2"></i> Task Batching
                    </a>
                    <a href="/momentum/productivity/batch-templates" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-copy mr-2"></i> Batch Templates
                    </a>
                    <a href="/momentum/productivity/energy-tracking" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-bolt mr-2"></i> Energy Tracking
                    </a>
                </div>
            </div>
            <a href="/momentum/productivity/tools" class="border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-th-large mr-2"></i> Productivity Tools
            </a>
            <div class="border-l-4 <?= isActive('/tools') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500' : 'border-transparent' ?>">
                <button id="mobile-tools-button" class="w-full flex justify-between items-center pl-3 pr-4 py-2 <?= isActive('/tools') ? 'text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-300' ?> text-base font-medium">
                    <span><i class="fas fa-tools mr-2"></i> Tools</span>
                    <i class="fas fa-chevron-down text-xs"></i>
                </button>
                <div id="mobile-tools-menu" class="hidden pl-6 pr-4 py-2 space-y-1">
                    <a href="/momentum/tools/currency-converter" class="block py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-300">
                        <i class="fas fa-exchange-alt mr-2"></i> Currency Converter
                    </a>
                </div>
            </div>
            <a href="/momentum/help" class="<?= isActive('/help') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-question-circle mr-2"></i> Help
            </a>
        </div>
        <div class="pt-4 pb-3 border-t border-gray-200 dark:border-gray-600">
            <div class="flex items-center px-4">
                <div class="flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-800 flex items-center justify-center text-primary-600 dark:text-primary-300">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <div class="text-base font-medium text-gray-800 dark:text-white">
                        <?= isset($_SESSION['user']['name']) ? $_SESSION['user']['name'] : 'User' ?>
                    </div>
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        <?= isset($_SESSION['user']['email']) ? $_SESSION['user']['email'] : '' ?>
                    </div>
                </div>
            </div>
            <div class="mt-3 space-y-1">
                <a href="/momentum/dashboard/toggle-theme" class="block px-4 py-2 text-base font-medium text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-adjust mr-2"></i> Toggle Theme
                </a>
                <a href="/momentum/logout" class="block px-4 py-2 text-base font-medium text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-sign-out-alt mr-2"></i> Sign out
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    // Mobile menu toggle
    document.addEventListener('DOMContentLoaded', function() {
        // Ensure desktop navigation is always visible
        const desktopNavigation = document.getElementById('desktop-navigation');
        if (desktopNavigation) {
            // Force display to be flex for desktop navigation
            desktopNavigation.style.display = 'flex';

            // Create a MutationObserver to watch for style changes
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.attributeName === 'style' || mutation.attributeName === 'class') {
                        // Force display to be flex again if it was changed
                        desktopNavigation.style.display = 'flex';
                    }
                });
            });

            // Start observing the desktop navigation for style changes
            observer.observe(desktopNavigation, { attributes: true });
        }

        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');

        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });
        }

        // Mobile ADHD submenu toggle
        const mobileADHDButton = document.getElementById('mobile-adhd-button');
        const mobileADHDMenu = document.getElementById('mobile-adhd-menu');

        if (mobileADHDButton && mobileADHDMenu) {
            mobileADHDButton.addEventListener('click', function() {
                mobileADHDMenu.classList.toggle('hidden');
            });
        }

        // Mobile productivity submenu toggle
        const mobileProductivityButton = document.getElementById('mobile-productivity-button');
        const mobileProductivityMenu = document.getElementById('mobile-productivity-menu');

        if (mobileProductivityButton && mobileProductivityMenu) {
            mobileProductivityButton.addEventListener('click', function() {
                mobileProductivityMenu.classList.toggle('hidden');
            });
        }

        // Mobile tools submenu toggle
        const mobileToolsButton = document.getElementById('mobile-tools-button');
        const mobileToolsMenu = document.getElementById('mobile-tools-menu');

        if (mobileToolsButton && mobileToolsMenu) {
            mobileToolsButton.addEventListener('click', function() {
                mobileToolsMenu.classList.toggle('hidden');
            });
        }
    });
</script>
