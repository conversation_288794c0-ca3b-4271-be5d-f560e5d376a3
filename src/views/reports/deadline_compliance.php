<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Deadline Compliance Report</h1>
                <p class="text-gray-500 dark:text-gray-400 mt-1"><?= View::escape($project['name']) ?></p>
            </div>
            <div>
                <a href="/momentum/reports" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Reports
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <!-- On-Time Completion Card -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-12 w-12 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                        <i class="fas fa-check text-green-600 dark:text-green-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white">On-Time</h2>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= $onTime ?></p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">tasks completed on time</p>
                    </div>
                </div>
            </div>

            <!-- Late Completion Card -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-12 w-12 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
                        <i class="fas fa-clock text-red-600 dark:text-red-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white">Late</h2>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= $late ?></p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">tasks completed late</p>
                    </div>
                </div>
            </div>

            <!-- Pending Tasks Card -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-12 w-12 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center">
                        <i class="fas fa-hourglass-half text-yellow-600 dark:text-yellow-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white">Pending</h2>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= $pending ?></p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">tasks with due dates</p>
                    </div>
                </div>
            </div>

            <!-- No Due Date Card -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                        <i class="fas fa-calendar-times text-gray-600 dark:text-gray-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white">No Due Date</h2>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= $noDueDate ?></p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">tasks without due dates</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Deadline Compliance Chart -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Deadline Compliance</h2>
            <div id="deadline-compliance-chart" style="height: 300px;"></div>
        </div>

        <!-- Overdue Tasks -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Overdue Tasks</h2>
            <?php if (empty($overdueDeadlines)): ?>
                <p class="text-gray-500 dark:text-gray-400">No overdue tasks.</p>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Task</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Due Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Priority</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Days Overdue</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($overdueDeadlines as $task): ?>
                                <?php
                                    $dueDate = new DateTime($task['due_date']);
                                    $today = new DateTime();
                                    $daysOverdue = $today->diff($dueDate)->days;
                                ?>
                                <tr>
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            <a href="/momentum/tasks/view/<?= $task['id'] ?>" class="hover:text-primary-600 dark:hover:text-primary-400">
                                                <?= View::escape($task['title']) ?>
                                            </a>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <?= date('M j, Y', strtotime($task['due_date'])) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $statusClasses = [
                                            'todo' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
                                            'in_progress' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                            'done' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                        ];
                                        $statusClass = $statusClasses[$task['status']] ?? $statusClasses['todo'];
                                        $statusLabel = [
                                            'todo' => 'To Do',
                                            'in_progress' => 'In Progress',
                                            'done' => 'Done'
                                        ][$task['status']] ?? 'To Do';
                                        ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                                            <?= $statusLabel ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $priorityClasses = [
                                            'low' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
                                            'medium' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                            'high' => 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
                                            'urgent' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                                        ];
                                        $priorityClass = $priorityClasses[$task['priority']] ?? $priorityClasses['medium'];
                                        ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $priorityClass ?>">
                                            <?= ucfirst($task['priority']) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                                            <?= $daysOverdue ?> days
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <!-- Upcoming Deadlines -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Upcoming Deadlines (Next 7 Days)</h2>
            <?php if (empty($upcomingDeadlines)): ?>
                <p class="text-gray-500 dark:text-gray-400">No upcoming deadlines in the next 7 days.</p>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Task</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Due Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Priority</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Days Left</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($upcomingDeadlines as $task): ?>
                                <?php
                                    $dueDate = new DateTime($task['due_date']);
                                    $today = new DateTime();
                                    $daysLeft = $today->diff($dueDate)->days;
                                ?>
                                <tr>
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            <a href="/momentum/tasks/view/<?= $task['id'] ?>" class="hover:text-primary-600 dark:hover:text-primary-400">
                                                <?= View::escape($task['title']) ?>
                                            </a>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <?= date('M j, Y', strtotime($task['due_date'])) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $statusClasses = [
                                            'todo' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
                                            'in_progress' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                            'done' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                        ];
                                        $statusClass = $statusClasses[$task['status']] ?? $statusClasses['todo'];
                                        $statusLabel = [
                                            'todo' => 'To Do',
                                            'in_progress' => 'In Progress',
                                            'done' => 'Done'
                                        ][$task['status']] ?? 'To Do';
                                        ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                                            <?= $statusLabel ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $priorityClasses = [
                                            'low' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
                                            'medium' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                            'high' => 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
                                            'urgent' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                                        ];
                                        $priorityClass = $priorityClasses[$task['priority']] ?? $priorityClasses['medium'];
                                        ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $priorityClass ?>">
                                            <?= ucfirst($task['priority']) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            <?= $daysLeft <= 1 ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                               ($daysLeft <= 3 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                               'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300') ?>">
                                            <?= $daysLeft ?> day<?= $daysLeft !== 1 ? 's' : '' ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <!-- Export Options -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Export Report</h2>
            <div class="flex space-x-4">
                <button id="export-pdf" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-file-pdf mr-2"></i> Export as PDF
                </button>
                <button id="export-csv" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-file-csv mr-2"></i> Export as CSV
                </button>
            </div>
        </div>

<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Deadline Compliance Chart
        const deadlineComplianceOptions = {
            series: [<?= $onTime ?>, <?= $late ?>, <?= $pending ?>],
            chart: {
                type: 'donut',
                height: 300,
                fontFamily: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            },
            labels: ['On Time', 'Late', 'Pending'],
            colors: ['#10b981', '#ef4444', '#f59e0b'],
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }],
            tooltip: {
                y: {
                    formatter: function(value) {
                        return value + " tasks";
                    }
                }
            }
        };

        const deadlineComplianceChart = new ApexCharts(document.querySelector("#deadline-compliance-chart"), deadlineComplianceOptions);
        deadlineComplianceChart.render();

        // Export buttons
        document.getElementById('export-pdf').addEventListener('click', function() {
            window.location.href = '/momentum/reports/export/deadline-compliance/<?= $project['id'] ?>/pdf';
        });

        document.getElementById('export-csv').addEventListener('click', function() {
            window.location.href = '/momentum/reports/export/deadline-compliance/<?= $project['id'] ?>/csv';
        });
    });
</script>

    </div>
</div>
