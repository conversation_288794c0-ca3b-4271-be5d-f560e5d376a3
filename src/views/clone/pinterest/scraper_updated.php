<?php
/**
 * Pinterest Scraper View
 *
 * This view displays the Pinterest scraper interface.
 */

// Set page title
$title = $title ?? 'Pinterest Scraper';
?>

<div class="container mx-auto px-4 py-8">
    <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            <i class="fab fa-pinterest text-red-600 mr-2"></i> Pinterest Scraper
        </h1>
        <a href="/momentum/clone/pinterest" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
        </a>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Scraper Form -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-search mr-2"></i> Configure Scraper
                </h2>

                <form action="/momentum/clone/pinterest/process-scrape" method="POST" id="scraper-form">
                    <!-- Search Term -->
                    <div class="mb-6">
                        <label for="search_term" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Search Term <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="search_term" name="search_term" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white" placeholder="e.g., home decor, digital marketing, fitness" required>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Enter a keyword or phrase to search for on Pinterest
                        </p>
                    </div>

                    <!-- Scraping Progress (hidden by default) -->
                    <div id="scraping-progress" class="mb-6 hidden">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Scraping Progress</h3>
                        <div class="relative pt-1">
                            <div class="flex mb-2 items-center justify-between">
                                <div>
                                    <span id="progress-status" class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-blue-600 bg-blue-200 dark:text-blue-200 dark:bg-blue-800">
                                        Initializing...
                                    </span>
                                </div>
                                <div class="text-right">
                                    <span id="progress-percentage" class="text-xs font-semibold inline-block text-blue-600 dark:text-blue-400">
                                        0%
                                    </span>
                                </div>
                            </div>
                            <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-blue-200 dark:bg-blue-700">
                                <div id="progress-bar" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500" style="width: 0%"></div>
                            </div>
                            <p id="progress-message" class="text-sm text-gray-600 dark:text-gray-400">
                                Preparing to scrape Pinterest for data...
                            </p>
                        </div>
                    </div>

                    <!-- Data Collection Parameters -->
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Data Collection Parameters</h3>

                        <div class="space-y-4">
                            <!-- Maximum Pins -->
                            <div>
                                <label for="max_pins" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Maximum Pins
                                </label>
                                <input type="number" id="max_pins" name="max_pins" min="10" max="500" value="100" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    Number of pins to collect (10-500)
                                </p>
                            </div>

                            <!-- Sort By -->
                            <div>
                                <label for="sort_by" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Sort By
                                </label>
                                <select id="sort_by" name="sort_by" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                    <option value="relevance">Relevance</option>
                                    <option value="recent">Most Recent</option>
                                    <option value="popular">Most Popular</option>
                                </select>
                            </div>

                            <!-- Collect Engagement Metrics -->
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input id="collect_engagement" name="collect_engagement" type="checkbox" checked class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600">
                                </div>
                                <div class="ml-3 text-sm">
                                    <label for="collect_engagement" class="font-medium text-gray-700 dark:text-gray-300">Collect Engagement Metrics</label>
                                    <p class="text-gray-500 dark:text-gray-400">Store saves, clicks, and comments data</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Options -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Advanced Options</h3>
                            <button type="button" id="toggle-advanced" class="text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                <span id="advanced-show">Show</span>
                                <span id="advanced-hide" class="hidden">Hide</span>
                            </button>
                        </div>

                        <div id="advanced-options" class="hidden space-y-4">
                            <!-- Real Scraping Toggle -->
                            <div class="border-b border-gray-200 dark:border-gray-700 pb-4 mb-4">
                                <div class="flex items-center mb-2">
                                    <input type="checkbox" id="use_real_scraping" name="use_real_scraping" value="true" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="use_real_scraping" class="ml-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Use Real Pinterest Scraping
                                    </label>
                                </div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    Enable this to use real Pinterest data instead of simulated data
                                </p>

                                <!-- Pinterest Credentials (hidden by default) -->
                                <div id="pinterest-credentials" class="mt-4 hidden space-y-4">
                                    <div>
                                        <label for="pinterest_username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Pinterest Username/Email
                                        </label>
                                        <input type="text" id="pinterest_username" name="pinterest_username" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                    </div>
                                    <div>
                                        <label for="pinterest_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Pinterest Password
                                        </label>
                                        <input type="password" id="pinterest_password" name="pinterest_password" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                            Your credentials are used only for this scraping session and are not stored permanently
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Delay Between Requests -->
                            <div>
                                <label for="request_delay" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Delay Between Requests (seconds)
                                </label>
                                <input type="number" id="request_delay" name="request_delay" min="1" max="10" value="2" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    Time to wait between requests to avoid rate limiting
                                </p>
                            </div>

                            <!-- User Agent -->
                            <div>
                                <label for="user_agent" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    User Agent
                                </label>
                                <select id="user_agent" name="user_agent" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                    <option value="default">Default Browser</option>
                                    <option value="mobile">Mobile Device</option>
                                    <option value="tablet">Tablet Device</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                            <i class="fas fa-play mr-2"></i> Start Scraping
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Ethical Guidelines -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-shield-alt mr-2"></i> Ethical Guidelines
                </h2>

                <ul class="space-y-2">
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                        <span class="text-gray-700 dark:text-gray-300">This tool implements rate limiting to prevent server overload</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                        <span class="text-gray-700 dark:text-gray-300">Only publicly available data is collected</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                        <span class="text-gray-700 dark:text-gray-300">All user-specific information is anonymized</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                        <span class="text-gray-700 dark:text-gray-300">Proper attribution is included for all content</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                        <span class="text-gray-700 dark:text-gray-300">Robots.txt directives are respected</span>
                    </li>
                </ul>

                <div class="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-800 rounded-md">
                    <p class="text-sm text-yellow-800 dark:text-yellow-200">
                        <i class="fas fa-exclamation-triangle mr-1"></i> Use this tool responsibly and in accordance with Pinterest's terms of service.
                    </p>
                </div>
            </div>

            <!-- Scraping Tips -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-lightbulb mr-2"></i> Scraping Tips
                </h2>

                <ul class="space-y-3 text-gray-600 dark:text-gray-400">
                    <li class="flex">
                        <i class="fas fa-info-circle text-blue-500 mt-1 mr-2"></i>
                        <span>Use specific search terms for more targeted results</span>
                    </li>
                    <li class="flex">
                        <i class="fas fa-info-circle text-blue-500 mt-1 mr-2"></i>
                        <span>Limit pin count to 100-200 for faster processing</span>
                    </li>
                    <li class="flex">
                        <i class="fas fa-info-circle text-blue-500 mt-1 mr-2"></i>
                        <span>Set appropriate delays to avoid rate limiting</span>
                    </li>
                    <li class="flex">
                        <i class="fas fa-info-circle text-blue-500 mt-1 mr-2"></i>
                        <span>Try different sorting options for varied results</span>
                    </li>
                    <li class="flex">
                        <i class="fas fa-info-circle text-blue-500 mt-1 mr-2"></i>
                        <span>Run multiple smaller scrapes rather than one large one</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script src="/momentum/js/pinterest-direct-toggle.js"></script>

<!-- All JavaScript functionality is now handled by the direct toggle script -->
