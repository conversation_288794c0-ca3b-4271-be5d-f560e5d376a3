<!-- Enhanced Notes Dashboard with ADHD-friendly features -->
<div class="py-6 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header with Quick Stats -->
        <div class="mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                        <i class="fas fa-sticky-note mr-3 text-blue-600"></i>My Notes
                    </h1>
                    <p class="text-gray-600 dark:text-gray-300">Organize your thoughts with ADHD-friendly features</p>
                </div>

                <!-- Quick Stats for ADHD motivation -->
                <div class="mt-4 lg:mt-0 grid grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="text-2xl font-bold text-blue-600"><?= $stats['total_notes'] ?? 0 ?></div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Total Notes</div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="text-2xl font-bold text-yellow-600"><?= $stats['pinned_notes'] ?? 0 ?></div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Pinned</div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="text-2xl font-bold text-red-600"><?= $stats['high_priority'] ?? 0 ?></div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">High Priority</div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="text-2xl font-bold text-green-600"><?= $stats['today_notes'] ?? 0 ?></div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Today</div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Search and Filters -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
                <form action="/momentum/notes/search" method="GET" class="space-y-4">
                    <div class="flex flex-col lg:flex-row gap-4">
                        <!-- Main Search -->
                        <div class="flex-1">
                            <div class="relative">
                                <input type="text" name="q" placeholder="🔍 Search notes, tags, or content..."
                                       class="w-full pl-10 pr-4 py-3 rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-lg dark:bg-gray-700 dark:text-white transition-all duration-200"
                                       value="<?= isset($filters['q']) ? View::escape($filters['q']) : '' ?>">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>

                        <!-- Quick Filters -->
                        <div class="flex gap-2">
                            <select name="category" class="rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                                <option value="">All Categories</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= View::escape($category['category']) ?>"
                                            <?= (isset($filters['category']) && $filters['category'] === $category['category']) ? 'selected' : '' ?>>
                                        <?= View::escape($category['category']) ?> (<?= $category['note_count'] ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>

                            <select name="priority" class="rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                                <option value="">All Priorities</option>
                                <option value="high" <?= (isset($filters['priority']) && $filters['priority'] === 'high') ? 'selected' : '' ?>>🔴 High</option>
                                <option value="medium" <?= (isset($filters['priority']) && $filters['priority'] === 'medium') ? 'selected' : '' ?>>🟡 Medium</option>
                                <option value="low" <?= (isset($filters['priority']) && $filters['priority'] === 'low') ? 'selected' : '' ?>>🟢 Low</option>
                            </select>

                            <button type="submit" class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md">
                                <i class="fas fa-filter mr-2"></i>Filter
                            </button>
                        </div>
                    </div>

                    <!-- Popular Tags for Quick Access -->
                    <?php if (!empty($popularTags)): ?>
                        <div class="flex flex-wrap gap-2 pt-2 border-t border-gray-200 dark:border-gray-600">
                            <span class="text-sm text-gray-500 dark:text-gray-400 mr-2">Popular tags:</span>
                            <?php foreach (array_slice($popularTags, 0, 8, true) as $tag => $count): ?>
                                <button type="button" onclick="addTagToSearch('<?= View::escape($tag) ?>')"
                                        class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-200 cursor-pointer">
                                    #<?= View::escape($tag) ?> <span class="ml-1 text-blue-600 dark:text-blue-300">(<?= $count ?>)</span>
                                </button>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </form>
            </div>

            <!-- Quick Actions -->
            <div class="flex flex-wrap gap-3 mb-6">
                <a href="/momentum/notes/create" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1">
                    <i class="fas fa-plus mr-2"></i>New Note
                </a>
                <button onclick="showTemplateModal()" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1">
                    <i class="fas fa-file-alt mr-2"></i>From Template
                </button>
                <button onclick="toggleFavoritesOnly()" id="favoritesToggle" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-pink-600 to-pink-700 hover:from-pink-700 hover:to-pink-800 text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1">
                    <i class="fas fa-heart mr-2"></i>Favorites Only
                </button>
                <button onclick="showQuickCapture()" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1">
                    <i class="fas fa-bolt mr-2"></i>Quick Capture
                </button>
            </div>
        </div>

        <!-- Notes Content -->

        <!-- Search Results -->
        <?php if (isset($searchTerm)): ?>
            <div class="mb-6 bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        Search Results for "<?= View::escape($searchTerm) ?>"
                    </h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                        Found <?= count($notes) ?> note<?= count($notes) !== 1 ? 's' : '' ?>
                    </p>
                    <a href="/momentum/notes" class="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                        <i class="fas fa-times mr-1"></i> Clear Search
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <?php if (empty($notes)): ?>
            <!-- Empty State -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-12 text-center">
                <div class="text-gray-400 dark:text-gray-500 mb-6">
                    <i class="fas fa-sticky-note text-6xl"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">No notes yet</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                    Start capturing your thoughts and ideas. Your ADHD-friendly note-taking journey begins here!
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="/momentum/notes/create" class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-semibold transition-all duration-200">
                        <i class="fas fa-plus mr-2"></i>Create Your First Note
                    </a>
                    <button onclick="showTemplateModal()" class="inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-semibold transition-all duration-200">
                        <i class="fas fa-file-alt mr-2"></i>Use a Template
                    </button>
                </div>
            </div>
        <?php else: ?>
            <!-- Pinned Notes Section -->
            <?php if (!empty($pinnedNotes)): ?>
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                            <i class="fas fa-thumbtack mr-2 text-yellow-500"></i>Pinned Notes
                        </h2>
                        <span class="ml-3 px-2 py-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 text-xs font-medium rounded-full">
                            <?= count($pinnedNotes) ?>
                        </span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php foreach ($pinnedNotes as $note): ?>
                            <?php include 'note_card.php'; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Regular Notes Section -->
            <?php if (!empty($otherNotes)): ?>
                <div>
                    <?php if (!empty($pinnedNotes)): ?>
                        <div class="flex items-center mb-4">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                                <i class="fas fa-file-alt mr-2 text-blue-500"></i>All Notes
                            </h2>
                            <span class="ml-3 px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium rounded-full">
                                <?= count($otherNotes) ?>
                            </span>
                        </div>
                    <?php endif; ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php foreach ($otherNotes as $note): ?>
                            <?php include 'note_card.php'; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Pagination -->
            <?php if ($hasMorePages || $currentPage > 1): ?>
                <div class="mt-8 flex justify-center">
                    <nav class="flex items-center space-x-2">
                        <?php if ($currentPage > 1): ?>
                            <a href="?page=<?= $currentPage - 1 ?><?= !empty($filters['q']) ? '&q=' . urlencode($filters['q']) : '' ?><?= !empty($filters['category']) ? '&category=' . urlencode($filters['category']) : '' ?><?= !empty($filters['priority']) ? '&priority=' . urlencode($filters['priority']) : '' ?>"
                               class="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                <i class="fas fa-chevron-left mr-1"></i>Previous
                            </a>
                        <?php endif; ?>

                        <span class="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium">
                            Page <?= $currentPage ?>
                        </span>

                        <?php if ($hasMorePages): ?>
                            <a href="?page=<?= $currentPage + 1 ?><?= !empty($filters['q']) ? '&q=' . urlencode($filters['q']) : '' ?><?= !empty($filters['category']) ? '&category=' . urlencode($filters['category']) : '' ?><?= !empty($filters['priority']) ? '&priority=' . urlencode($filters['priority']) : '' ?>"
                               class="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                Next<i class="fas fa-chevron-right ml-1"></i>
                            </a>
                        <?php endif; ?>
                    </nav>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Template Modal -->
<div id="templateModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-2xl w-full max-h-96 overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white">Choose a Template</h3>
                    <button onclick="hideTemplateModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="templateList" class="space-y-3">
                    <!-- Templates will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Capture Modal -->
<div id="quickCaptureModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-lg w-full">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white">Quick Capture</h3>
                    <button onclick="hideQuickCapture()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="quickCaptureForm" class="space-y-4">
                    <input type="text" id="quickTitle" placeholder="Quick note title..."
                           class="w-full px-4 py-3 rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    <textarea id="quickContent" rows="4" placeholder="What's on your mind?"
                              class="w-full px-4 py-3 rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white resize-none"></textarea>
                    <div class="flex gap-3">
                        <button type="submit" class="flex-1 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200">
                            <i class="fas fa-save mr-2"></i>Save Note
                        </button>
                        <button type="button" onclick="hideQuickCapture()" class="px-4 py-3 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-lg font-medium transition-colors duration-200">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Include Enhanced Notes JavaScript -->
<script src="/momentum/public/js/notes-enhanced.js"></script>
