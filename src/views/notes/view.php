<!-- Enhanced ADHD-Friendly Note View -->
<div class="py-6 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 min-h-screen">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Enhanced <PERSON><PERSON> with Visual Hierarchy -->
        <div class="mb-8">
            <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
                <!-- Title Section -->
                <div class="flex-1">
                    <div class="flex items-center mb-4">
                        <a href="/momentum/notes" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-4 text-xl transition-all duration-200 hover:scale-110">
                            <i class="fas fa-arrow-left"></i>
                        </a>
                        <div class="flex-1">
                            <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2 leading-tight">
                                <?= View::escape($note['title']) ?>
                            </h1>
                            <!-- Enhanced Metadata Bar -->
                            <div class="flex flex-wrap items-center gap-3 text-sm text-gray-600 dark:text-gray-300">
                                <div class="flex items-center">
                                    <i class="fas fa-clock mr-2 text-blue-500"></i>
                                    <span>Updated <?= View::formatDateTime($note['updated_at']) ?></span>
                                </div>
                                <?php if (!empty($note['word_count'])): ?>
                                <div class="flex items-center">
                                    <i class="fas fa-file-word mr-2 text-green-500"></i>
                                    <span><?= number_format($note['word_count']) ?> words</span>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($note['reading_time'])): ?>
                                <div class="flex items-center">
                                    <i class="fas fa-stopwatch mr-2 text-purple-500"></i>
                                    <span><?= $note['reading_time'] ?> min read</span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Action Buttons -->
                <div class="flex flex-wrap gap-3 mt-4 lg:mt-0">
                    <!-- Quick Edit Button -->
                    <a href="/momentum/notes/edit/<?= $note['id'] ?>"
                       class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Note
                    </a>

                    <!-- Pin/Unpin Button -->
                    <button onclick="togglePin(<?= $note['id'] ?>)"
                            class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white <?= $note['is_pinned'] ? 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500' : 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500' ?> focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg">
                        <i class="fas fa-thumbtack mr-2"></i>
                        <?= $note['is_pinned'] ? 'Unpin' : 'Pin' ?>
                    </button>

                    <!-- Favorite Button -->
                    <button onclick="toggleFavorite(<?= $note['id'] ?>)"
                            class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white <?= ($note['is_favorite'] ?? 0) ? 'bg-pink-600 hover:bg-pink-700 focus:ring-pink-500' : 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500' ?> focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg">
                        <i class="fas fa-heart mr-2"></i>
                        <?= ($note['is_favorite'] ?? 0) ? 'Favorited' : 'Favorite' ?>
                    </button>
                </div>
            </div>
        </div>

        <!-- Enhanced Main Content Area -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Main Content Column -->
            <div class="lg:col-span-3">
                <!-- Enhanced Status Bar -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
                    <div class="flex flex-wrap items-center gap-4">
                        <!-- Priority Badge -->
                        <?php
                        $priorityColors = [
                            'high' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 border-red-200 dark:border-red-800',
                            'medium' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 border-yellow-200 dark:border-yellow-800',
                            'low' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 border-green-200 dark:border-green-800'
                        ];
                        $priority = $note['priority_level'] ?? 'medium';
                        $priorityEmojis = ['high' => '🔴', 'medium' => '🟡', 'low' => '🟢'];
                        ?>
                        <div class="flex items-center px-4 py-2 rounded-lg border-2 <?= $priorityColors[$priority] ?>">
                            <span class="text-lg mr-2"><?= $priorityEmojis[$priority] ?></span>
                            <span class="font-semibold"><?= ucfirst($priority) ?> Priority</span>
                        </div>

                        <!-- Status Badges -->
                        <?php if ($note['is_pinned']): ?>
                            <div class="flex items-center px-4 py-2 rounded-lg border-2 bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 border-orange-200 dark:border-orange-800">
                                <i class="fas fa-thumbtack mr-2 text-lg"></i>
                                <span class="font-semibold">Pinned</span>
                            </div>
                        <?php endif; ?>

                        <?php if ($note['is_favorite'] ?? 0): ?>
                            <div class="flex items-center px-4 py-2 rounded-lg border-2 bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200 border-pink-200 dark:border-pink-800">
                                <i class="fas fa-heart mr-2 text-lg"></i>
                                <span class="font-semibold">Favorite</span>
                            </div>
                        <?php endif; ?>

                        <!-- Category Badge -->
                        <?php if (!empty($note['category'])): ?>
                            <a href="/momentum/notes?category=<?= urlencode($note['category']) ?>"
                               class="flex items-center px-4 py-2 rounded-lg border-2 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 border-blue-200 dark:border-blue-800 hover:bg-blue-200 dark:hover:bg-blue-800 transition-all duration-200">
                                <i class="fas fa-folder mr-2 text-lg"></i>
                                <span class="font-semibold"><?= View::escape($note['category']) ?></span>
                            </a>
                        <?php endif; ?>

                        <!-- Color Indicator -->
                        <?php if (!empty($note['color_code'])): ?>
                            <div class="flex items-center px-4 py-2 rounded-lg border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                                <div class="w-6 h-6 rounded-full mr-3 border-2 border-gray-300 dark:border-gray-600"
                                     style="background-color: <?= $note['color_code'] ?>"></div>
                                <span class="font-semibold text-gray-700 dark:text-gray-300">Color Coded</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Enhanced Note Content with ADHD-Optimized Spacing -->
                <?php if (!empty($note['content'])): ?>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden mb-6">
                        <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <div class="flex items-center justify-between">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                                    <i class="fas fa-file-alt mr-3 text-green-500"></i>
                                    Note Content
                                </h2>
                                <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                                    <?php if (!empty($note['word_count'])): ?>
                                    <div class="flex items-center">
                                        <i class="fas fa-file-word mr-1 text-green-500"></i>
                                        <span><?= number_format($note['word_count']) ?> words</span>
                                    </div>
                                    <?php endif; ?>
                                    <?php if (!empty($note['reading_time'])): ?>
                                    <div class="flex items-center">
                                        <i class="fas fa-clock mr-1 text-green-500"></i>
                                        <span><?= $note['reading_time'] ?> min read</span>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="p-8">
                            <div class="prose prose-lg max-w-none dark:prose-invert">
                                <div class="note-content text-gray-800 dark:text-gray-200 whitespace-pre-wrap text-lg font-medium"
                                     style="line-height: 2.0; letter-spacing: 0.025em; word-spacing: 0.1em; <?= !empty($note['color_code']) ? 'border-left: 4px solid ' . $note['color_code'] . '; padding-left: 1.5rem; margin-left: 0.5rem;' : '' ?>">
                                    <?= nl2br(View::escape($note['content'])) ?>
                                </div>
                            </div>

                            <!-- ADHD Reading Controls -->
                            <div class="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                                    <div class="flex items-center text-sm text-blue-700 dark:text-blue-300">
                                        <i class="fas fa-brain mr-2 text-lg"></i>
                                        <span class="font-semibold">ADHD Reading Tools:</span>
                                    </div>

                                    <div class="flex items-center space-x-4">
                                        <!-- Font Size Controls -->
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xs text-gray-600 dark:text-gray-400 font-medium">Font Size:</span>
                                            <button onclick="adjustFontSize('smaller')"
                                                    class="px-3 py-1 text-xs bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <button onclick="adjustFontSize('larger')"
                                                    class="px-3 py-1 text-xs bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>

                                        <!-- Line Spacing Controls -->
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xs text-gray-600 dark:text-gray-400 font-medium">Spacing:</span>
                                            <button onclick="adjustLineSpacing('tighter')"
                                                    class="px-3 py-1 text-xs bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                                                <i class="fas fa-compress-alt"></i>
                                            </button>
                                            <button onclick="adjustLineSpacing('looser')"
                                                    class="px-3 py-1 text-xs bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                                                <i class="fas fa-expand-alt"></i>
                                            </button>
                                        </div>

                                        <!-- Focus Mode Toggle -->
                                        <button onclick="toggleFocusMode()"
                                                class="px-4 py-2 text-xs font-semibold bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                            <i class="fas fa-eye mr-1"></i>
                                            Focus Mode
                                        </button>
                                    </div>
                                </div>

                                <div class="mt-4 text-xs text-blue-600 dark:text-blue-400">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    <strong>Tip:</strong> Adjust font size and spacing for comfortable reading. Focus mode hides distractions.
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-8 mb-6 text-center">
                        <div class="text-gray-400 dark:text-gray-500">
                            <i class="fas fa-file-alt text-6xl mb-4"></i>
                            <p class="text-xl font-medium">No content yet</p>
                            <p class="text-sm mt-2">This note doesn't have any content. Click "Edit Note" to add some!</p>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Enhanced Tags Section -->
                <?php if (!empty($note['tags'])): ?>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden mb-6">
                        <div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-tags mr-3 text-purple-500"></i>
                                Tags
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="flex flex-wrap gap-3">
                                <?php
                                $tags = explode(',', $note['tags']);
                                foreach ($tags as $tag):
                                    $tag = trim($tag);
                                    if (!empty($tag)):
                                ?>
                                    <a href="/momentum/notes?tag=<?= urlencode($tag) ?>"
                                       class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-semibold bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 hover:bg-purple-200 dark:hover:bg-purple-800 border-2 border-purple-200 dark:border-purple-700 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-md">
                                        <i class="fas fa-hashtag mr-2"></i>
                                        <?= View::escape($tag) ?>
                                    </a>
                                <?php
                                    endif;
                                endforeach;
                                ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Enhanced Sidebar -->
            <div class="lg:col-span-1">
                <!-- Quick Actions Card -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden mb-6">
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-bolt mr-3 text-blue-500"></i>
                            Quick Actions
                        </h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <a href="/momentum/notes/edit/<?= $note['id'] ?>"
                           class="w-full inline-flex items-center justify-center px-4 py-3 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:-translate-y-1">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Note
                        </a>

                        <button onclick="duplicateNote(<?= $note['id'] ?>)"
                                class="w-full inline-flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-semibold text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 transform hover:-translate-y-1">
                            <i class="fas fa-copy mr-2"></i>
                            Duplicate
                        </button>

                        <button onclick="shareNote(<?= $note['id'] ?>)"
                                class="w-full inline-flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-semibold text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 transform hover:-translate-y-1">
                            <i class="fas fa-share mr-2"></i>
                            Share
                        </button>

                        <button onclick="deleteNote(<?= $note['id'] ?>)"
                                class="w-full inline-flex items-center justify-center px-4 py-3 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 transform hover:-translate-y-1">
                            <i class="fas fa-trash mr-2"></i>
                            Delete
                        </button>
                    </div>
                </div>

                <!-- Note Statistics Card -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden mb-6">
                    <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-chart-bar mr-3 text-green-500"></i>
                            Statistics
                        </h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Created</span>
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">
                                <?= date('M j, Y', strtotime($note['created_at'])) ?>
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Last Updated</span>
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">
                                <?= date('M j, Y', strtotime($note['updated_at'])) ?>
                            </span>
                        </div>
                        <?php if (!empty($note['word_count'])): ?>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Word Count</span>
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">
                                <?= number_format($note['word_count']) ?>
                            </span>
                        </div>
                        <?php endif; ?>
                        <?php if (!empty($note['reading_time'])): ?>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Reading Time</span>
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">
                                <?= $note['reading_time'] ?> min
                            </span>
                        </div>
                        <?php endif; ?>
                        <?php if (!empty($note['last_accessed'])): ?>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Last Accessed</span>
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">
                                <?= date('M j, Y', strtotime($note['last_accessed'])) ?>
                            </span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Related Notes Card -->
                <?php if (!empty($relatedNotes)): ?>
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <div class="bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-link mr-3 text-orange-500"></i>
                            Related Notes
                        </h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <?php foreach ($relatedNotes as $relatedNote): ?>
                        <a href="/momentum/notes/view/<?= $relatedNote['id'] ?>"
                           class="block p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200">
                            <div class="font-medium text-gray-900 dark:text-white text-sm truncate">
                                <?= View::escape($relatedNote['title']) ?>
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                <?= date('M j', strtotime($relatedNote['updated_at'])) ?>
                            </div>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced JavaScript for ADHD-friendly interactions -->
<script>
class NoteViewManager {
    constructor() {
        this.noteId = <?= $note['id'] ?>;
        this.init();
    }

    init() {
        this.setupKeyboardShortcuts();
        this.trackLastAccessed();
        this.setupReadingControls();
    }

    setupReadingControls() {
        // Load saved reading preferences
        const savedFontSize = localStorage.getItem('adhd-font-size') || '18';
        const savedLineHeight = localStorage.getItem('adhd-line-height') || '2.0';

        this.applyReadingSettings(savedFontSize, savedLineHeight);
    }

    applyReadingSettings(fontSize, lineHeight) {
        const contentDiv = document.querySelector('.note-content');
        if (contentDiv) {
            contentDiv.style.fontSize = fontSize + 'px';
            contentDiv.style.lineHeight = lineHeight;
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // E key for edit
            if (e.key === 'e' && !e.ctrlKey && !e.metaKey && !this.isInputFocused()) {
                e.preventDefault();
                window.location.href = `/momentum/notes/edit/${this.noteId}`;
            }

            // Escape key to go back
            if (e.key === 'Escape') {
                e.preventDefault();
                window.location.href = '/momentum/notes';
            }

            // D key for duplicate
            if (e.key === 'd' && !e.ctrlKey && !e.metaKey && !this.isInputFocused()) {
                e.preventDefault();
                this.duplicateNote();
            }
        });
    }

    isInputFocused() {
        const activeElement = document.activeElement;
        return activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.contentEditable === 'true'
        );
    }

    async trackLastAccessed() {
        try {
            await fetch(`/momentum/notes/track-access/${this.noteId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
        } catch (error) {
            console.log('Could not track access time');
        }
    }

    async duplicateNote() {
        try {
            const response = await fetch(`/momentum/notes/duplicate/${this.noteId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Note duplicated successfully!', 'success');
                setTimeout(() => {
                    window.location.href = `/momentum/notes/edit/${result.note_id}`;
                }, 1000);
            } else {
                this.showNotification('Failed to duplicate note', 'error');
            }
        } catch (error) {
            this.showNotification('Error duplicating note', 'error');
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg text-white font-medium z-50 transition-all duration-300`;

        switch (type) {
            case 'success':
                notification.classList.add('bg-green-500');
                break;
            case 'error':
                notification.classList.add('bg-red-500');
                break;
            default:
                notification.classList.add('bg-blue-500');
        }

        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} mr-2"></i>
                ${message}
            </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.add('opacity-0', 'translate-x-full');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Global functions for button actions
async function togglePin(noteId) {
    try {
        const response = await fetch(`/momentum/notes/toggle-pin/${noteId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            location.reload();
        } else {
            alert('Failed to toggle pin status');
        }
    } catch (error) {
        alert('Error toggling pin status');
    }
}

async function toggleFavorite(noteId) {
    try {
        const response = await fetch(`/momentum/notes/toggle-favorite/${noteId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            location.reload();
        } else {
            alert('Failed to toggle favorite status');
        }
    } catch (error) {
        alert('Error toggling favorite status');
    }
}

function duplicateNote(noteId) {
    if (window.noteViewManager) {
        window.noteViewManager.duplicateNote();
    }
}

function shareNote(noteId) {
    if (navigator.share) {
        navigator.share({
            title: document.title,
            url: window.location.href
        });
    } else {
        navigator.clipboard.writeText(window.location.href);
        alert('Note URL copied to clipboard!');
    }
}

function deleteNote(noteId) {
    if (confirm('Are you sure you want to delete this note? This action cannot be undone.')) {
        window.location.href = `/momentum/notes/delete/${noteId}`;
    }
}

// ADHD Reading Control Functions
function adjustFontSize(direction) {
    const contentDiv = document.querySelector('.note-content');
    if (!contentDiv) return;

    const currentSize = parseInt(window.getComputedStyle(contentDiv).fontSize);
    let newSize = currentSize;

    if (direction === 'larger' && currentSize < 28) {
        newSize = currentSize + 2;
    } else if (direction === 'smaller' && currentSize > 12) {
        newSize = currentSize - 2;
    }

    contentDiv.style.fontSize = newSize + 'px';
    localStorage.setItem('adhd-font-size', newSize);

    // Show feedback
    showReadingFeedback(`Font size: ${newSize}px`);
}

function adjustLineSpacing(direction) {
    const contentDiv = document.querySelector('.note-content');
    if (!contentDiv) return;

    const currentHeight = parseFloat(window.getComputedStyle(contentDiv).lineHeight);
    let newHeight = currentHeight;

    if (direction === 'looser' && currentHeight < 3.0) {
        newHeight = currentHeight + 0.2;
    } else if (direction === 'tighter' && currentHeight > 1.2) {
        newHeight = currentHeight - 0.2;
    }

    contentDiv.style.lineHeight = newHeight;
    localStorage.setItem('adhd-line-height', newHeight);

    // Show feedback
    showReadingFeedback(`Line spacing: ${newHeight.toFixed(1)}`);
}

function toggleFocusMode() {
    const sidebar = document.querySelector('.lg\\:col-span-1');
    const mainContent = document.querySelector('.lg\\:col-span-3');
    const focusButton = event.target.closest('button');

    if (sidebar && mainContent) {
        const isHidden = sidebar.style.display === 'none';

        if (isHidden) {
            // Exit focus mode
            sidebar.style.display = 'block';
            mainContent.classList.remove('lg:col-span-4');
            mainContent.classList.add('lg:col-span-3');
            focusButton.innerHTML = '<i class="fas fa-eye mr-1"></i>Focus Mode';
            focusButton.classList.remove('bg-red-600', 'hover:bg-red-700');
            focusButton.classList.add('bg-blue-600', 'hover:bg-blue-700');
            showReadingFeedback('Focus mode disabled');
        } else {
            // Enter focus mode
            sidebar.style.display = 'none';
            mainContent.classList.remove('lg:col-span-3');
            mainContent.classList.add('lg:col-span-4');
            focusButton.innerHTML = '<i class="fas fa-eye-slash mr-1"></i>Exit Focus';
            focusButton.classList.remove('bg-blue-600', 'hover:bg-blue-700');
            focusButton.classList.add('bg-red-600', 'hover:bg-red-700');
            showReadingFeedback('Focus mode enabled - distractions hidden');
        }
    }
}

function showReadingFeedback(message) {
    // Remove existing feedback
    const existing = document.querySelector('.reading-feedback');
    if (existing) {
        existing.remove();
    }

    // Create new feedback
    const feedback = document.createElement('div');
    feedback.className = 'reading-feedback fixed top-20 right-4 px-4 py-2 bg-blue-600 text-white text-sm rounded-lg shadow-lg z-50 transition-all duration-300';
    feedback.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-check mr-2"></i>
            ${message}
        </div>
    `;

    document.body.appendChild(feedback);

    // Auto-remove after 2 seconds
    setTimeout(() => {
        feedback.classList.add('opacity-0', 'translate-x-full');
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
        }, 300);
    }, 2000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.noteViewManager = new NoteViewManager();
});
</script>

<!-- Include enhanced notes JavaScript for additional functionality -->
<script src="/momentum/public/js/notes-enhanced.js"></script>
