<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div class="flex items-center">
                <a href="/momentum/notes" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                    <?= View::escape($note['title']) ?>
                </h1>
            </div>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 mt-4 md:mt-0">
                <a href="/momentum/notes/edit/<?= $note['id'] ?>" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-edit mr-2"></i> Edit
                </a>
                <a href="/momentum/notes/toggle-pin/<?= $note['id'] ?>" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white <?= $note['is_pinned'] ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-primary-600 hover:bg-primary-700' ?> focus:outline-none focus:ring-2 focus:ring-offset-2 <?= $note['is_pinned'] ? 'focus:ring-yellow-500' : 'focus:ring-primary-500' ?> transition-colors duration-200">
                    <i class="fas fa-thumbtack mr-2"></i> <?= $note['is_pinned'] ? 'Unpin' : 'Pin' ?>
                </a>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <!-- Note Metadata -->
                <div class="flex flex-wrap items-center gap-2 mb-6">
                    <?php if ($note['is_pinned']): ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                            <i class="fas fa-thumbtack mr-1"></i> Pinned
                        </span>
                    <?php endif; ?>
                    
                    <?php if (!empty($note['category'])): ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            <?= View::escape($note['category']) ?>
                        </span>
                    <?php endif; ?>
                    
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                        <i class="far fa-clock mr-1"></i> Updated: <?= View::formatDateTime($note['updated_at']) ?>
                    </span>
                </div>

                <!-- Note Content -->
                <?php if (!empty($note['content'])): ?>
                    <div class="mb-6">
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                            <?= nl2br(View::escape($note['content'])) ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Tags -->
                <?php if (!empty($note['tags'])): ?>
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Tags</h3>
                        <div class="flex flex-wrap gap-2">
                            <?php 
                            $tags = explode(',', $note['tags']);
                            foreach ($tags as $tag):
                                $tag = trim($tag);
                                if (!empty($tag)):
                            ?>
                                <a href="/momentum/notes?tag=<?= urlencode($tag) ?>" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                                    #<?= View::escape($tag) ?>
                                </a>
                            <?php 
                                endif;
                            endforeach; 
                            ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Note Actions -->
                <div class="flex flex-wrap gap-2">
                    <a href="/momentum/notes/edit/<?= $note['id'] ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                        <i class="fas fa-edit mr-2"></i> Edit
                    </a>
                    <a href="/momentum/notes/delete/<?= $note['id'] ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200" onclick="return confirm('Are you sure you want to delete this note?')">
                        <i class="fas fa-trash mr-2"></i> Delete
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
