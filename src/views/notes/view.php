<!-- Enhanced ADHD-Friendly Note View -->
<div class="py-6 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 min-h-screen">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Enhanced <PERSON><PERSON> with Visual Hierarchy -->
        <div class="mb-8">
            <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
                <!-- Title Section -->
                <div class="flex-1">
                    <div class="flex items-center mb-4">
                        <a href="/momentum/notes" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-4 text-xl transition-all duration-200 hover:scale-110">
                            <i class="fas fa-arrow-left"></i>
                        </a>
                        <div class="flex-1">
                            <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2 leading-tight">
                                <?= View::escape($note['title']) ?>
                            </h1>
                            <!-- Enhanced Metadata Bar -->
                            <div class="flex flex-wrap items-center gap-3 text-sm text-gray-600 dark:text-gray-300">
                                <div class="flex items-center">
                                    <i class="fas fa-clock mr-2 text-blue-500"></i>
                                    <span>Updated <?= View::formatDateTime($note['updated_at']) ?></span>
                                </div>
                                <?php if (!empty($note['word_count'])): ?>
                                <div class="flex items-center">
                                    <i class="fas fa-file-word mr-2 text-green-500"></i>
                                    <span><?= number_format($note['word_count']) ?> words</span>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($note['reading_time'])): ?>
                                <div class="flex items-center">
                                    <i class="fas fa-stopwatch mr-2 text-purple-500"></i>
                                    <span><?= $note['reading_time'] ?> min read</span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Action Buttons -->
                <div class="flex flex-wrap gap-3 mt-4 lg:mt-0">
                    <!-- Quick Edit Button -->
                    <a href="/momentum/notes/edit/<?= $note['id'] ?>"
                       class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Note
                    </a>

                    <!-- Pin/Unpin Button -->
                    <button onclick="togglePin(<?= $note['id'] ?>)"
                            class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white <?= $note['is_pinned'] ? 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500' : 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500' ?> focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg">
                        <i class="fas fa-thumbtack mr-2"></i>
                        <?= $note['is_pinned'] ? 'Unpin' : 'Pin' ?>
                    </button>

                    <!-- Favorite Button -->
                    <button onclick="toggleFavorite(<?= $note['id'] ?>)"
                            class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white <?= ($note['is_favorite'] ?? 0) ? 'bg-pink-600 hover:bg-pink-700 focus:ring-pink-500' : 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500' ?> focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg">
                        <i class="fas fa-heart mr-2"></i>
                        <?= ($note['is_favorite'] ?? 0) ? 'Favorited' : 'Favorite' ?>
                    </button>
                </div>
            </div>
        </div>

        <!-- Enhanced Main Content Area -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Main Content Column -->
            <div class="lg:col-span-3">
                <!-- Enhanced Status Bar -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
                    <div class="flex flex-wrap items-center gap-4">
                        <!-- Priority Badge -->
                        <?php
                        $priorityColors = [
                            'high' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 border-red-200 dark:border-red-800',
                            'medium' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 border-yellow-200 dark:border-yellow-800',
                            'low' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 border-green-200 dark:border-green-800'
                        ];
                        $priority = $note['priority_level'] ?? 'medium';
                        $priorityEmojis = ['high' => '🔴', 'medium' => '🟡', 'low' => '🟢'];
                        ?>
                        <div class="flex items-center px-4 py-2 rounded-lg border-2 <?= $priorityColors[$priority] ?>">
                            <span class="text-lg mr-2"><?= $priorityEmojis[$priority] ?></span>
                            <span class="font-semibold"><?= ucfirst($priority) ?> Priority</span>
                        </div>

                        <!-- Status Badges -->
                        <?php if ($note['is_pinned']): ?>
                            <div class="flex items-center px-4 py-2 rounded-lg border-2 bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 border-orange-200 dark:border-orange-800">
                                <i class="fas fa-thumbtack mr-2 text-lg"></i>
                                <span class="font-semibold">Pinned</span>
                            </div>
                        <?php endif; ?>

                        <?php if ($note['is_favorite'] ?? 0): ?>
                            <div class="flex items-center px-4 py-2 rounded-lg border-2 bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200 border-pink-200 dark:border-pink-800">
                                <i class="fas fa-heart mr-2 text-lg"></i>
                                <span class="font-semibold">Favorite</span>
                            </div>
                        <?php endif; ?>

                        <!-- Category Badge -->
                        <?php if (!empty($note['category'])): ?>
                            <a href="/momentum/notes?category=<?= urlencode($note['category']) ?>"
                               class="flex items-center px-4 py-2 rounded-lg border-2 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 border-blue-200 dark:border-blue-800 hover:bg-blue-200 dark:hover:bg-blue-800 transition-all duration-200">
                                <i class="fas fa-folder mr-2 text-lg"></i>
                                <span class="font-semibold"><?= View::escape($note['category']) ?></span>
                            </a>
                        <?php endif; ?>

                        <!-- Color Indicator -->
                        <?php if (!empty($note['color_code'])): ?>
                            <div class="flex items-center px-4 py-2 rounded-lg border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                                <div class="w-6 h-6 rounded-full mr-3 border-2 border-gray-300 dark:border-gray-600"
                                     style="background-color: <?= $note['color_code'] ?>"></div>
                                <span class="font-semibold text-gray-700 dark:text-gray-300">Color Coded</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Enhanced Note Content -->
                <?php if (!empty($note['content'])): ?>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden mb-6">
                        <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-file-alt mr-3 text-green-500"></i>
                                Note Content
                            </h2>
                        </div>
                        <div class="p-8">
                            <div class="prose prose-lg max-w-none dark:prose-invert">
                                <div class="text-gray-800 dark:text-gray-200 leading-relaxed whitespace-pre-wrap text-lg"
                                     style="line-height: 1.8; <?= !empty($note['color_code']) ? 'border-left: 4px solid ' . $note['color_code'] . '; padding-left: 1rem;' : '' ?>">
                                    <?= nl2br(View::escape($note['content'])) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-8 mb-6 text-center">
                        <div class="text-gray-400 dark:text-gray-500">
                            <i class="fas fa-file-alt text-6xl mb-4"></i>
                            <p class="text-xl font-medium">No content yet</p>
                            <p class="text-sm mt-2">This note doesn't have any content. Click "Edit Note" to add some!</p>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Tags -->
                <?php if (!empty($note['tags'])): ?>
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Tags</h3>
                        <div class="flex flex-wrap gap-2">
                            <?php 
                            $tags = explode(',', $note['tags']);
                            foreach ($tags as $tag):
                                $tag = trim($tag);
                                if (!empty($tag)):
                            ?>
                                <a href="/momentum/notes?tag=<?= urlencode($tag) ?>" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                                    #<?= View::escape($tag) ?>
                                </a>
                            <?php 
                                endif;
                            endforeach; 
                            ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Note Actions -->
                <div class="flex flex-wrap gap-2">
                    <a href="/momentum/notes/edit/<?= $note['id'] ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                        <i class="fas fa-edit mr-2"></i> Edit
                    </a>
                    <a href="/momentum/notes/delete/<?= $note['id'] ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200" onclick="return confirm('Are you sure you want to delete this note?')">
                        <i class="fas fa-trash mr-2"></i> Delete
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
