<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Create New Project</h1>
            <a href="/momentum/projects" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i> Back to Projects
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Project Details</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Fill in the details below to create a new project.
                </p>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (isset($errors) && !empty($errors)): ?>
                    <div class="mb-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-400 dark:text-red-500"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">There were errors with your submission</h3>
                                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                    <ul class="list-disc pl-5 space-y-1">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?= $error ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <form action="/momentum/projects/create" method="POST">
                    <!-- Project Name -->
                    <div class="mb-4">
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Project Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="Enter project name" value="<?= isset($data['name']) ? View::escape($data['name']) : '' ?>" required>
                    </div>

                    <!-- Project Description -->
                    <div class="mb-4">
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Description
                        </label>
                        <textarea name="description" id="description" rows="3" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="Enter project description"><?= isset($data['description']) ? View::escape($data['description']) : '' ?></textarea>
                    </div>

                    <!-- Project Dates -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Start Date
                            </label>
                            <input type="date" name="start_date" id="start_date" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($data['start_date']) ? View::escape($data['start_date']) : date('Y-m-d') ?>">
                        </div>
                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                End Date
                            </label>
                            <input type="date" name="end_date" id="end_date" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($data['end_date']) ? View::escape($data['end_date']) : '' ?>">
                        </div>
                    </div>

                    <!-- Project Status -->
                    <div class="mb-4">
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Status
                        </label>
                        <select name="status" id="status" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="planning" <?= (isset($data['status']) && $data['status'] === 'planning') ? 'selected' : '' ?>>Planning</option>
                            <option value="in_progress" <?= (isset($data['status']) && $data['status'] === 'in_progress') ? 'selected' : '' ?>>In Progress</option>
                            <option value="on_hold" <?= (isset($data['status']) && $data['status'] === 'on_hold') ? 'selected' : '' ?>>On Hold</option>
                            <option value="completed" <?= (isset($data['status']) && $data['status'] === 'completed') ? 'selected' : '' ?>>Completed</option>
                        </select>
                    </div>

                    <!-- Template Option -->
                    <div class="mb-4">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_template" id="is_template" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 dark:bg-gray-700 rounded" value="1" <?= (isset($data['is_template']) && $data['is_template']) ? 'checked' : '' ?>>
                            <label for="is_template" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                Save as template for future projects
                            </label>
                        </div>
                    </div>

                    <!-- Create from Template -->
                    <?php if (!empty($templates)): ?>
                        <div class="mt-6 mb-4 border-t border-gray-200 dark:border-gray-700 pt-4">
                            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">Create from Template</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">
                                Optionally, you can create this project based on an existing template.
                            </p>
                            <div class="mb-4">
                                <label for="template_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Template
                                </label>
                                <select name="template_id" id="template_id" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                    <option value="">-- Select a template --</option>
                                    <?php foreach ($templates as $template): ?>
                                        <option value="<?= $template['id'] ?>" <?= (isset($data['template_id']) && $data['template_id'] == $template['id']) ? 'selected' : '' ?>>
                                            <?= View::escape($template['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Submit Button -->
                    <div class="mt-6 flex justify-end">
                        <a href="/momentum/projects" class="mr-3 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            Cancel
                        </a>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            Create Project
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
