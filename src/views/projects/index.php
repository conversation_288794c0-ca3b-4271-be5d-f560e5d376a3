<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Projects</h1>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/projects/create" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i> New Project
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Filters</h3>
                <form action="/momentum/projects" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4" id="filter-form">
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                        <select id="status" name="status" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="">All Statuses</option>
                            <option value="planning" <?= (isset($filters['status']) && $filters['status'] === 'planning') ? 'selected' : '' ?>>Planning</option>
                            <option value="in_progress" <?= (isset($filters['status']) && $filters['status'] === 'in_progress') ? 'selected' : '' ?>>In Progress</option>
                            <option value="on_hold" <?= (isset($filters['status']) && $filters['status'] === 'on_hold') ? 'selected' : '' ?>>On Hold</option>
                            <option value="completed" <?= (isset($filters['status']) && $filters['status'] === 'completed') ? 'selected' : '' ?>>Completed</option>
                            <option value="archived" <?= (isset($filters['status']) && $filters['status'] === 'archived') ? 'selected' : '' ?>>Archived</option>
                        </select>
                    </div>
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search</label>
                        <input type="text" id="search" name="search" placeholder="Search projects..." class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($filters['search']) ? View::escape($filters['search']) : '' ?>">
                    </div>
                    <div>
                        <label for="is_template" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Type</label>
                        <select id="is_template" name="is_template" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="">All Types</option>
                            <option value="0" <?= (isset($filters['is_template']) && $filters['is_template'] === '0') ? 'selected' : '' ?>>Projects</option>
                            <option value="1" <?= (isset($filters['is_template']) && $filters['is_template'] === '1') ? 'selected' : '' ?>>Templates</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-filter mr-2"></i> Apply Filters
                        </button>
                        <button type="button" id="clear-filters-btn" class="ml-2 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-times mr-2"></i> Clear
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Projects List -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Projects</h3>
            </div>
            <?php if (empty($projects)): ?>
                <div class="px-4 py-5 sm:p-6 text-center">
                    <div class="text-gray-500 dark:text-gray-400 mb-4">
                        <i class="fas fa-project-diagram text-4xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No projects found</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">
                        <?php if (!empty($filters)): ?>
                            Try adjusting your filters or
                        <?php endif; ?>
                        create a new project to get started.
                    </p>
                    <a href="/momentum/projects/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i> Create Project
                    </a>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
                    <?php foreach ($projects as $project): ?>
                        <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                            <div class="p-4">
                                <div class="flex justify-between items-start">
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                        <a href="/momentum/projects/view/<?= $project['id'] ?>" class="hover:text-primary-600 dark:hover:text-primary-400">
                                            <?= View::escape($project['name']) ?>
                                        </a>
                                    </h3>
                                    <?php if ($project['is_template']): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                            Template
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                                    <?= empty($project['description']) ? 'No description' : View::escape($project['description']) ?>
                                </p>
                                
                                <div class="mt-3">
                                    <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                                        <span>Progress</span>
                                        <span><?= $project['progress'] ?>%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                        <div class="bg-primary-600 h-2 rounded-full" style="width: <?= $project['progress'] ?>%"></div>
                                    </div>
                                </div>
                                
                                <div class="mt-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            <?php
                                            switch ($project['status']) {
                                                case 'planning':
                                                    echo 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
                                                    break;
                                                case 'in_progress':
                                                    echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                                                    break;
                                                case 'on_hold':
                                                    echo 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
                                                    break;
                                                case 'completed':
                                                    echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                                                    break;
                                                case 'archived':
                                                    echo 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
                                                    break;
                                                default:
                                                    echo 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
                                            }
                                            ?>">
                                            <?= ucfirst($project['status']) ?>
                                        </span>
                                    </div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        <?= $project['total_tasks'] ?> tasks (<?= $project['completed_tasks'] ?> completed)
                                    </div>
                                </div>
                                
                                <div class="mt-4 flex justify-end space-x-2">
                                    <a href="/momentum/projects/view/<?= $project['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="/momentum/projects/edit/<?= $project['id'] ?>" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="/momentum/projects/delete/<?= $project['id'] ?>" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" title="Delete" onclick="return confirm('Are you sure you want to delete this project? This will also remove all associated tasks.')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle clear filters button
        const clearFiltersBtn = document.getElementById('clear-filters-btn');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', function(e) {
                e.preventDefault();
                window.location.href = '/momentum/projects';
            });
        }
    });
</script>
