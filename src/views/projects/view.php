<link rel="stylesheet" href="/momentum/public/css/tools.css">
<link rel="stylesheet" href="/momentum/public/css/gallery-enhanced.css">

<div class="tools-container">
    <!-- Header -->
    <div class="gallery-header">
        <div class="flex items-center">
            <a href="/momentum/projects" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 mr-4 text-xl">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="gallery-title"><?= View::escape($project['name']) ?></h1>
                <p class="gallery-subtitle">Project management and task tracking</p>
            </div>
            <?php if ($project['is_template']): ?>
                <span class="ml-4 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                    <i class="fas fa-layer-group mr-2"></i>
                    Template
                </span>
            <?php endif; ?>
        </div>

        <div class="flex space-x-3">
            <a href="/momentum/projects/edit/<?= $project['id'] ?>" class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-edit mr-2"></i>
                Edit Project
            </a>
            <a href="/momentum/projects/delete/<?= $project['id'] ?>" class="inline-flex items-center px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1" onclick="return confirm('Are you sure you want to delete this project? This will also remove all associated tasks.')">
                <i class="fas fa-trash mr-2"></i>
                Delete Project
            </a>
        </div>
    </div>

    <!-- Project Details -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden mb-8">
        <div class="px-6 py-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                        <i class="fas fa-info-circle mr-3 text-blue-500"></i>
                        Project Details
                    </h3>
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</h4>
                                <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                    <?= empty($project['description']) ? 'No description' : nl2br(View::escape($project['description'])) ?>
                                </p>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</h4>
                                <div class="mt-1">
                                    <?php
                                    $statusClasses = [
                                        'planning' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
                                        'in_progress' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                                        'on_hold' => 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
                                        'completed' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                                        'archived' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                                    ];
                                    $statusLabels = [
                                        'planning' => 'Planning',
                                        'in_progress' => 'In Progress',
                                        'on_hold' => 'On Hold',
                                        'completed' => 'Completed',
                                        'archived' => 'Archived'
                                    ];
                                    $statusClass = $statusClasses[$project['status']] ?? $statusClasses['planning'];
                                    $statusLabel = $statusLabels[$project['status']] ?? 'Planning';
                                    ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                                        <?= $statusLabel ?>
                                    </span>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Start Date</h4>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                        <?= empty($project['start_date']) ? 'Not set' : date('M j, Y', strtotime($project['start_date'])) ?>
                                    </p>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">End Date</h4>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                        <?= empty($project['end_date']) ? 'Not set' : date('M j, Y', strtotime($project['end_date'])) ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                        <i class="fas fa-chart-line mr-3 text-green-500"></i>
                        Progress
                    </h3>
                        <div class="space-y-4">
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Task Completion</h4>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                                        <?= $project['completed_tasks'] ?>/<?= $project['total_tasks'] ?> tasks
                                    </span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                    <?php
                                    $progressPercent = $project['total_tasks'] > 0
                                        ? round(($project['completed_tasks'] / $project['total_tasks']) * 100)
                                        : 0;
                                    ?>
                                    <div class="bg-primary-600 h-2.5 rounded-full" style="width: <?= $progressPercent ?>%"></div>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    <?= $progressPercent ?>% complete
                                </p>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Task Status Breakdown</h4>
                                <div class="mt-2 grid grid-cols-3 gap-2 text-center">
                                    <div class="bg-gray-100 dark:bg-gray-700 p-2 rounded">
                                        <span class="block text-sm font-medium text-gray-900 dark:text-white"><?= $project['todo_tasks'] ?? 0 ?></span>
                                        <span class="text-xs text-gray-500 dark:text-gray-400">To Do</span>
                                    </div>
                                    <div class="bg-blue-100 dark:bg-blue-900 p-2 rounded">
                                        <span class="block text-sm font-medium text-blue-800 dark:text-blue-200"><?= $project['in_progress_tasks'] ?? 0 ?></span>
                                        <span class="text-xs text-blue-600 dark:text-blue-400">In Progress</span>
                                    </div>
                                    <div class="bg-green-100 dark:bg-green-900 p-2 rounded">
                                        <span class="block text-sm font-medium text-green-800 dark:text-green-200"><?= $project['completed_tasks'] ?? 0 ?></span>
                                        <span class="text-xs text-green-600 dark:text-green-400">Completed</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!-- Project Tasks and Gantt Chart Tabs -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden mb-8">
        <div class="px-6 py-6 border-b border-gray-200 dark:border-gray-700 flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
            <div class="flex flex-wrap border-b border-gray-200 dark:border-gray-700 lg:border-b-0">
                <button type="button" id="tasks-tab-btn" class="px-4 py-3 text-sm font-semibold text-blue-600 dark:text-blue-400 border-b-2 border-blue-500 dark:border-blue-400 focus:outline-none transition-all duration-200">
                    <i class="fas fa-tasks mr-2"></i> Tasks
                </button>
                <button type="button" id="gantt-tab-btn" class="px-4 py-3 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none transition-all duration-200">
                    <i class="fas fa-chart-bar mr-2"></i> Gantt Chart
                </button>
                <button type="button" id="team-tab-btn" class="px-4 py-3 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none transition-all duration-200">
                    <i class="fas fa-users mr-2"></i> Team
                </button>
                <button type="button" id="comments-tab-btn" class="px-4 py-3 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none transition-all duration-200">
                    <i class="fas fa-comments mr-2"></i> Comments
                </button>
                <button type="button" id="checklists-tab-btn" class="px-4 py-3 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none transition-all duration-200">
                    <i class="fas fa-check-square mr-2"></i> Checklists
                </button>
            </div>
            <div class="flex flex-wrap gap-2">
                <button type="button" id="add-task-btn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-semibold rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:-translate-y-1">
                    <i class="fas fa-plus mr-2"></i> Add Task
                </button>
                <button type="button" id="manage-dependencies-btn" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg shadow-sm text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                    <i class="fas fa-project-diagram mr-2"></i> Dependencies
                </button>
                <a href="/momentum/projects/<?= $project['id'] ?>/checklists/create" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg shadow-sm text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                    <i class="fas fa-check-square mr-2"></i> Add Checklist
                </a>
            </div>
        </div>
        <div class="px-6 py-6">
            <!-- Add Task Form (hidden by default) -->
            <div id="add-task-form" class="hidden mb-8 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-600">
                <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                    <i class="fas fa-plus-circle mr-3 text-blue-500"></i>
                    Add New Task
                </h4>
                    <form action="/momentum/projects/<?= $project['id'] ?>/add-task" method="POST">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="md:col-span-2">
                                <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Title</label>
                                <input type="text" name="title" id="title" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-800 dark:text-white" required>
                            </div>
                            <div class="md:col-span-2">
                                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                                <textarea name="description" id="description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-800 dark:text-white"></textarea>
                            </div>
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                                <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-800 dark:text-white">
                                    <option value="todo">To Do</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="done">Done</option>
                                </select>
                            </div>
                            <div>
                                <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Priority</label>
                                <select name="priority" id="priority" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-800 dark:text-white">
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                            <div>
                                <label for="due_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Due Date</label>
                                <input type="date" name="due_date" id="due_date" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-800 dark:text-white">
                            </div>
                            <div>
                                <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Category</label>
                                <select name="category_id" id="category_id" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-800 dark:text-white">
                                    <option value="">No Category</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?= $category['id'] ?>"><?= View::escape($category['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="md:col-span-2 flex justify-end space-x-3 pt-4">
                                <button type="button" id="cancel-add-task" class="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-semibold text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                                    <i class="fas fa-times mr-2"></i>
                                    Cancel
                                </button>
                                <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:-translate-y-1">
                                    <i class="fas fa-plus mr-2"></i>
                                    Add Task
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Tasks Tab Content -->
                <div id="tasks-tab-content">
                <?php if (empty($tasks)): ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <h3 class="empty-state-title">No tasks yet</h3>
                        <p class="empty-state-description">
                            Add your first task to get started with project management.
                        </p>
                        <button onclick="document.getElementById('add-task-btn').click()" class="inline-flex items-center px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                            <i class="fas fa-plus mr-3"></i>
                            Add First Task
                        </button>
                    </div>
                    <?php else: ?>
                    <div class="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800">
                                <tr>
                                    <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Task</th>
                                    <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Priority</th>
                                    <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Due Date</th>
                                    <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Category</th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <?php foreach ($tasks as $task): ?>
                                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <input type="checkbox" data-task-id="<?= $task['id'] ?>" class="task-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 rounded" <?= $task['status'] === 'done' ? 'checked' : '' ?>>
                                                    <span class="ml-2 text-xs">
                                                        <?php if ($task['status'] === 'todo'): ?>
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                                                To Do
                                                            </span>
                                                        <?php elseif ($task['status'] === 'in_progress'): ?>
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                                In Progress
                                                            </span>
                                                        <?php elseif ($task['status'] === 'done'): ?>
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                                Done
                                                            </span>
                                                        <?php endif; ?>
                                                    </span>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="flex items-center">
                                                    <a href="/momentum/tasks/view/<?= $task['id'] ?>" class="text-sm font-medium text-gray-900 dark:text-white <?= $task['status'] === 'done' ? 'line-through text-gray-500 dark:text-gray-400' : '' ?>">
                                                        <?= View::escape($task['title']) ?>
                                                    </a>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php
                                                $priorityClasses = [
                                                    'low' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
                                                    'medium' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                                    'high' => 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
                                                    'urgent' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                                                ];
                                                $priorityClass = $priorityClasses[$task['priority']] ?? $priorityClasses['medium'];
                                                ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $priorityClass ?>">
                                                    <?= ucfirst($task['priority']) ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                <?= empty($task['due_date']) ? '-' : date('M j, Y', strtotime($task['due_date'])) ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php if (!empty($task['category_id'])): ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" style="background-color: <?= $task['category_color'] ?>25; color: <?= $task['category_color'] ?>;">
                                                        <?= View::escape($task['category_name']) ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-gray-400 dark:text-gray-500">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <a href="/momentum/tasks/view/<?= $task['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300 mr-3" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="/momentum/tasks/edit/<?= $task['id'] ?>" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-3" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="/momentum/tasks/delete/<?= $task['id'] ?>" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" title="Delete" onclick="return confirm('Are you sure you want to delete this task?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>

            <!-- Gantt Chart Tab Content (hidden by default) -->
            <div id="gantt-tab-content" class="hidden">
                <div class="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                    <?php if (empty($tasks)): ?>
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h3 class="empty-state-title">No tasks to display</h3>
                            <p class="empty-state-description">
                                Add tasks with due dates to see the Gantt chart visualization.
                            </p>
                            <button onclick="document.getElementById('add-task-btn').click()" class="inline-flex items-center px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                <i class="fas fa-plus mr-3"></i>
                                Add Tasks
                            </button>
                        </div>
                    <?php else: ?>
                        <div id="gantt-container" class="w-full rounded-lg border border-gray-200 dark:border-gray-700" style="height: 500px;"></div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Team Tab Content (hidden by default) -->
            <div id="team-tab-content" class="hidden">
                <div class="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-users mr-3 text-purple-500"></i>
                            Project Team Members
                        </h3>
                        <button type="button" id="add-member-btn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-semibold rounded-lg shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 transform hover:-translate-y-1">
                            <i class="fas fa-user-plus mr-2"></i> Add Member
                        </button>
                    </div>

                    <?php if (empty($projectMembers)): ?>
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h3 class="empty-state-title">No team members yet</h3>
                            <p class="empty-state-description">
                                Add team members to collaborate on this project and share responsibilities.
                            </p>
                            <button onclick="document.getElementById('add-member-btn').click()" class="inline-flex items-center px-8 py-4 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                <i class="fas fa-user-plus mr-3"></i>
                                Add Team Member
                            </button>
                        </div>
                        <?php else: ?>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">User</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Role</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Joined</th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        <?php foreach ($projectMembers as $member): ?>
                                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="flex items-center">
                                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                                            <span class="text-gray-500 dark:text-gray-400 font-medium">
                                                                <?= strtoupper(substr($member['name'], 0, 1)) ?>
                                                            </span>
                                                        </div>
                                                        <div class="ml-4">
                                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                                <?= View::escape($member['name']) ?>
                                                            </div>
                                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                                <?= View::escape($member['email']) ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                        <?= $member['role'] === 'owner' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                                                           ($member['role'] === 'admin' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                                                           'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200') ?>">
                                                        <?= ucfirst($member['role']) ?>
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                    <?= date('M j, Y', strtotime($member['joined_at'])) ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <?php if ($member['role'] !== 'owner'): ?>
                                                        <button type="button" class="change-role-btn text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-3"
                                                                data-member-id="<?= $member['id'] ?>" data-current-role="<?= $member['role'] ?>">
                                                            <i class="fas fa-user-edit"></i>
                                                        </button>
                                                        <button type="button" class="remove-member-btn text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                                                                data-member-id="<?= $member['id'] ?>">
                                                            <i class="fas fa-user-minus"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            <!-- Comments Tab Content (hidden by default) -->
            <div id="comments-tab-content" class="hidden">
                <div class="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-comments mr-3 text-green-500"></i>
                            Project Comments
                        </h3>
                    </div>

                    <!-- Add Comment Form -->
                    <div class="mb-6 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-600">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                            <i class="fas fa-comment-plus mr-3 text-green-500"></i>
                            Add Comment
                        </h4>
                        <form id="comment-form" class="space-y-4">
                            <div>
                                <label for="comment" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Comment</label>
                                <textarea id="comment" name="comment" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-800 dark:text-white" placeholder="Add your comment..." required></textarea>
                            </div>
                            <div class="flex justify-end">
                                <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 transform hover:-translate-y-1">
                                    <i class="fas fa-comment mr-2"></i>
                                    Add Comment
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Comments List -->
                    <div class="space-y-4">
                        <?php if (empty($projectComments)): ?>
                            <div class="empty-state">
                                <div class="empty-state-icon">
                                    <i class="fas fa-comments"></i>
                                </div>
                                <h3 class="empty-state-title">No comments yet</h3>
                                <p class="empty-state-description">
                                    Start a discussion about this project by adding the first comment.
                                </p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($projectComments as $comment): ?>
                                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                    <div class="flex justify-between items-start mb-2">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                                                <span class="text-gray-500 dark:text-gray-400 font-medium text-sm">
                                                    <?= strtoupper(substr($comment['user_name'], 0, 1)) ?>
                                                </span>
                                            </div>
                                            <div class="ml-3">
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                    <?= View::escape($comment['user_name']) ?>
                                                </div>
                                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                                    <?= date('M j, Y \a\t g:i A', strtotime($comment['created_at'])) ?>
                                                </div>
                                            </div>
                                        </div>
                                        <?php if ($comment['user_id'] == $userId): ?>
                                            <div class="flex space-x-2">
                                                <button type="button" class="edit-comment-btn text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" data-comment-id="<?= $comment['id'] ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="delete-comment-btn text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" data-comment-id="<?= $comment['id'] ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                                        <?= View::escape($comment['content']) ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Checklists Tab Content (hidden by default) -->
            <div id="checklists-tab-content" class="hidden">
                <div class="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-check-square mr-3 text-orange-500"></i>
                            Project Checklists
                        </h3>
                        <a href="/momentum/projects/<?= $project['id'] ?>/checklists/create" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-semibold rounded-lg shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 transform hover:-translate-y-1">
                            <i class="fas fa-plus mr-2"></i> Add Checklist
                        </a>
                    </div>

                    <div id="project-checklists">
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-check-square"></i>
                            </div>
                            <h3 class="empty-state-title">No checklists yet</h3>
                            <p class="empty-state-description">
                                Create a checklist to track your project progress and ensure nothing is missed.
                            </p>
                            <a href="/momentum/projects/<?= $project['id'] ?>/checklists/create" class="inline-flex items-center px-8 py-4 bg-orange-600 hover:bg-orange-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                <i class="fas fa-plus mr-3"></i>
                                Create Checklist
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Task Dependencies Visualization -->
    <?php if (!empty($dependencies)): ?>
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Task Dependencies</h3>
        </div>
        <div class="px-4 py-5 sm:p-6">
            <div id="dependency-graph" class="w-full h-64 md:h-96"></div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Dependency Management Modal -->
<div id="dependency-modal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" id="dependency-modal-backdrop"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-project-diagram text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            Manage Task Dependencies
                        </h3>
                        <div class="mt-4">
                            <form id="dependency-form" class="space-y-4">
                                <div>
                                    <label for="task_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Task</label>
                                    <select id="task_id" name="task_id" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                        <option value="">Select a task</option>
                                        <?php foreach ($tasks as $task): ?>
                                            <option value="<?= $task['id'] ?>"><?= View::escape($task['title']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div>
                                    <label for="dependency_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Dependency Type</label>
                                    <select id="dependency_type" name="dependency_type" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                        <option value="finish_to_start">Finish-to-Start (FS)</option>
                                        <option value="start_to_start">Start-to-Start (SS)</option>
                                        <option value="finish_to_finish">Finish-to-Finish (FF)</option>
                                        <option value="start_to_finish">Start-to-Finish (SF)</option>
                                    </select>
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                        <span class="font-medium">FS</span>: Task can't start until the dependency is finished<br>
                                        <span class="font-medium">SS</span>: Task can't start until the dependency starts<br>
                                        <span class="font-medium">FF</span>: Task can't finish until the dependency finishes<br>
                                        <span class="font-medium">SF</span>: Task can't finish until the dependency starts
                                    </p>
                                </div>
                                <div>
                                    <label for="depends_on_task_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Depends On</label>
                                    <select id="depends_on_task_id" name="depends_on_task_id" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                        <option value="">Select a task</option>
                                        <?php foreach ($tasks as $task): ?>
                                            <option value="<?= $task['id'] ?>"><?= View::escape($task['title']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <!-- Existing Dependencies Section -->
                                <div class="mt-4">
                                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Existing Dependencies</h4>
                                    <?php if (empty($dependencies)): ?>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">No dependencies yet.</p>
                                    <?php else: ?>
                                        <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                                            <ul class="space-y-2">
                                                <?php foreach ($dependencies as $dependency): ?>
                                                    <?php
                                                    $taskTitle = '';
                                                    $dependsOnTaskTitle = '';
                                                    $dependencyTypeLabel = '';

                                                    // Find task titles
                                                    foreach ($tasks as $task) {
                                                        if ($task['id'] == $dependency['task_id']) {
                                                            $taskTitle = $task['title'];
                                                        }
                                                        if ($task['id'] == $dependency['depends_on_task_id']) {
                                                            $dependsOnTaskTitle = $task['title'];
                                                        }
                                                    }

                                                    // Get dependency type label
                                                    switch ($dependency['dependency_type']) {
                                                        case 'finish_to_start':
                                                            $dependencyTypeLabel = 'FS';
                                                            break;
                                                        case 'start_to_start':
                                                            $dependencyTypeLabel = 'SS';
                                                            break;
                                                        case 'finish_to_finish':
                                                            $dependencyTypeLabel = 'FF';
                                                            break;
                                                        case 'start_to_finish':
                                                            $dependencyTypeLabel = 'SF';
                                                            break;
                                                    }
                                                    ?>
                                                    <li class="flex justify-between items-center text-sm">
                                                        <span class="text-gray-700 dark:text-gray-300">
                                                            <span class="font-medium"><?= View::escape($taskTitle) ?></span>
                                                            <span class="text-gray-500 dark:text-gray-400">depends on</span>
                                                            <span class="font-medium"><?= View::escape($dependsOnTaskTitle) ?></span>
                                                            <span class="ml-1 px-1.5 py-0.5 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded text-xs"><?= $dependencyTypeLabel ?></span>
                                                        </span>
                                                        <button type="button" class="delete-dependency-btn text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" data-dependency-id="<?= $dependency['id'] ?>">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <label for="lag_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Lag Time (minutes)</label>
                                    <input type="number" id="lag_time" name="lag_time" min="0" value="0" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                        Time delay between the dependency and the task
                                    </p>
                                </div>
                                <div id="dependency-error" class="text-red-600 dark:text-red-400 text-sm hidden"></div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="add-dependency-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                    Add Dependency
                </button>
                <button type="button" id="close-dependency-modal-btn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Team Member Modal -->
<div id="add-member-modal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" id="add-member-modal-backdrop"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-user-plus text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            Add Team Member
                        </h3>
                        <div class="mt-4">
                            <form id="add-member-form" class="space-y-4">
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email Address</label>
                                    <input type="email" id="email" name="email" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="Enter email address" required>
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                        Enter the email address of the user you want to add to the project.
                                    </p>
                                </div>
                                <div>
                                    <label for="role" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Role</label>
                                    <select id="role" name="role" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                        <option value="member">Member</option>
                                        <option value="admin">Admin</option>
                                    </select>
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                        <span class="font-medium">Member</span>: Can view and edit tasks<br>
                                        <span class="font-medium">Admin</span>: Can manage project settings and members
                                    </p>
                                </div>
                                <div id="add-member-error" class="text-red-600 dark:text-red-400 text-sm hidden"></div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="add-member-submit-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                    Add Member
                </button>
                <button type="button" id="close-add-member-modal-btn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Change Role Modal -->
<div id="change-role-modal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" id="change-role-modal-backdrop"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-user-edit text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            Change Member Role
                        </h3>
                        <div class="mt-4">
                            <form id="change-role-form" class="space-y-4">
                                <input type="hidden" id="member_id" name="member_id">
                                <div>
                                    <label for="new_role" class="block text-sm font-medium text-gray-700 dark:text-gray-300">New Role</label>
                                    <select id="new_role" name="new_role" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                        <option value="member">Member</option>
                                        <option value="admin">Admin</option>
                                    </select>
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                        <span class="font-medium">Member</span>: Can view and edit tasks<br>
                                        <span class="font-medium">Admin</span>: Can manage project settings and members
                                    </p>
                                </div>
                                <div id="change-role-error" class="text-red-600 dark:text-red-400 text-sm hidden"></div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="change-role-submit-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                    Change Role
                </button>
                <button type="button" id="close-change-role-modal-btn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Comment Modal -->
<div id="edit-comment-modal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" id="edit-comment-modal-backdrop"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-comment-edit text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            Edit Comment
                        </h3>
                        <div class="mt-4">
                            <form id="edit-comment-form" class="space-y-4">
                                <input type="hidden" id="comment_id" name="comment_id">
                                <div>
                                    <label for="edit_comment" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Comment</label>
                                    <textarea id="edit_comment" name="edit_comment" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" required></textarea>
                                </div>
                                <div id="edit-comment-error" class="text-red-600 dark:text-red-400 text-sm hidden"></div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="edit-comment-submit-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                    Save Changes
                </button>
                <button type="button" id="close-edit-comment-modal-btn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Task Form Toggle
        const addTaskBtn = document.getElementById('add-task-btn');
        const addTaskForm = document.getElementById('add-task-form');
        const cancelAddTask = document.getElementById('cancel-add-task');

        if (addTaskBtn && addTaskForm) {
            addTaskBtn.addEventListener('click', function() {
                addTaskForm.classList.toggle('hidden');
                addTaskBtn.classList.toggle('hidden');
            });
        }

        if (cancelAddTask && addTaskForm && addTaskBtn) {
            cancelAddTask.addEventListener('click', function() {
                addTaskForm.classList.add('hidden');
                addTaskBtn.classList.remove('hidden');
            });
        }

        // Tab Switching
        const tasksTabBtn = document.getElementById('tasks-tab-btn');
        const ganttTabBtn = document.getElementById('gantt-tab-btn');
        const teamTabBtn = document.getElementById('team-tab-btn');
        const commentsTabBtn = document.getElementById('comments-tab-btn');
        const checklistsTabBtn = document.getElementById('checklists-tab-btn');

        const tasksTabContent = document.getElementById('tasks-tab-content');
        const ganttTabContent = document.getElementById('gantt-tab-content');
        const teamTabContent = document.getElementById('team-tab-content');
        const commentsTabContent = document.getElementById('comments-tab-content');
        const checklistsTabContent = document.getElementById('checklists-tab-content');

        // Function to reset all tabs
        function resetAllTabs() {
            // Hide all tab contents
            [tasksTabContent, ganttTabContent, teamTabContent, commentsTabContent, checklistsTabContent].forEach(tab => {
                if (tab) tab.classList.add('hidden');
            });

            // Reset all tab buttons
            [tasksTabBtn, ganttTabBtn, teamTabBtn, commentsTabBtn, checklistsTabBtn].forEach(btn => {
                if (btn) {
                    btn.classList.remove('text-primary-600', 'dark:text-primary-400', 'border-b-2', 'border-primary-500', 'dark:border-primary-400');
                    btn.classList.add('text-gray-500', 'dark:text-gray-400', 'hover:text-gray-700', 'dark:hover:text-gray-300');
                }
            });
        }

        // Tasks Tab
        if (tasksTabBtn && tasksTabContent) {
            tasksTabBtn.addEventListener('click', function() {
                resetAllTabs();

                // Activate tasks tab
                tasksTabBtn.classList.add('text-primary-600', 'dark:text-primary-400', 'border-b-2', 'border-primary-500', 'dark:border-primary-400');
                tasksTabBtn.classList.remove('text-gray-500', 'dark:text-gray-400', 'hover:text-gray-700', 'dark:hover:text-gray-300');

                // Show tasks content
                tasksTabContent.classList.remove('hidden');
            });
        }

        // Gantt Chart Tab
        if (ganttTabBtn && ganttTabContent) {
            ganttTabBtn.addEventListener('click', function() {
                resetAllTabs();

                // Activate gantt tab
                ganttTabBtn.classList.add('text-primary-600', 'dark:text-primary-400', 'border-b-2', 'border-primary-500', 'dark:border-primary-400');
                ganttTabBtn.classList.remove('text-gray-500', 'dark:text-gray-400', 'hover:text-gray-700', 'dark:hover:text-gray-300');

                // Show gantt content
                ganttTabContent.classList.remove('hidden');

                // Initialize Gantt chart if not already initialized
                if (!window.ganttInitialized && document.getElementById('gantt-container')) {
                    initGanttChart();
                }
            });
        }

        // Team Tab
        if (teamTabBtn && teamTabContent) {
            teamTabBtn.addEventListener('click', function() {
                resetAllTabs();

                // Activate team tab
                teamTabBtn.classList.add('text-primary-600', 'dark:text-primary-400', 'border-b-2', 'border-primary-500', 'dark:border-primary-400');
                teamTabBtn.classList.remove('text-gray-500', 'dark:text-gray-400', 'hover:text-gray-700', 'dark:hover:text-gray-300');

                // Show team content
                teamTabContent.classList.remove('hidden');
            });
        }

        // Comments Tab
        if (commentsTabBtn && commentsTabContent) {
            commentsTabBtn.addEventListener('click', function() {
                resetAllTabs();

                // Activate comments tab
                commentsTabBtn.classList.add('text-primary-600', 'dark:text-primary-400', 'border-b-2', 'border-primary-500', 'dark:border-primary-400');
                commentsTabBtn.classList.remove('text-gray-500', 'dark:text-gray-400', 'hover:text-gray-700', 'dark:hover:text-gray-300');

                // Show comments content
                commentsTabContent.classList.remove('hidden');
            });
        }

        // Checklists Tab
        if (checklistsTabBtn && checklistsTabContent) {
            checklistsTabBtn.addEventListener('click', function() {
                resetAllTabs();

                // Activate checklists tab
                checklistsTabBtn.classList.add('text-primary-600', 'dark:text-primary-400', 'border-b-2', 'border-primary-500', 'dark:border-primary-400');
                checklistsTabBtn.classList.remove('text-gray-500', 'dark:text-gray-400', 'hover:text-gray-700', 'dark:hover:text-gray-300');

                // Show checklists content
                checklistsTabContent.classList.remove('hidden');

                // Load project checklists via AJAX
                fetch('/momentum/projects/<?= $project['id'] ?>/checklists', {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.text())
                .then(html => {
                    document.getElementById('project-checklists').innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading project checklists:', error);
                    document.getElementById('project-checklists').innerHTML = '<div class="text-center py-8 text-red-600 dark:text-red-400">Error loading checklists</div>';
                });
            });
        }

        // Task Checkbox Functionality
        const taskCheckboxes = document.querySelectorAll('.task-checkbox');
        taskCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const taskId = this.getAttribute('data-task-id');
                if (this.checked) {
                    window.location.href = `/momentum/tasks/complete/${taskId}?redirect=/momentum/projects/view/<?= $project['id'] ?>`;
                }
            });
        });

        // Dependency Management Modal
        const manageDependenciesBtn = document.getElementById('manage-dependencies-btn');
        const dependencyModal = document.getElementById('dependency-modal');
        const closeDependencyModalBtn = document.getElementById('close-dependency-modal-btn');
        const dependencyModalBackdrop = document.getElementById('dependency-modal-backdrop');
        const addDependencyBtn = document.getElementById('add-dependency-btn');
        const dependencyForm = document.getElementById('dependency-form');
        const dependencyError = document.getElementById('dependency-error');
        const taskIdSelect = document.getElementById('task_id');
        const dependsOnTaskIdSelect = document.getElementById('depends_on_task_id');

        // Open dependency modal
        if (manageDependenciesBtn && dependencyModal) {
            manageDependenciesBtn.addEventListener('click', function() {
                dependencyModal.classList.remove('hidden');
                document.body.classList.add('overflow-hidden');
            });
        }

        // Close dependency modal
        if (closeDependencyModalBtn && dependencyModal) {
            closeDependencyModalBtn.addEventListener('click', function() {
                dependencyModal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
                dependencyError.classList.add('hidden');
                dependencyForm.reset();
            });
        }

        // Close modal when clicking on backdrop
        if (dependencyModalBackdrop && dependencyModal) {
            dependencyModalBackdrop.addEventListener('click', function() {
                dependencyModal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
                dependencyError.classList.add('hidden');
                dependencyForm.reset();
            });
        }

        // Prevent selecting the same task for both fields
        if (taskIdSelect && dependsOnTaskIdSelect) {
            taskIdSelect.addEventListener('change', function() {
                const selectedValue = this.value;

                // Enable all options in the other select
                Array.from(dependsOnTaskIdSelect.options).forEach(option => {
                    option.disabled = false;
                });

                // Disable the selected option in the other select
                if (selectedValue) {
                    const optionToDisable = dependsOnTaskIdSelect.querySelector(`option[value="${selectedValue}"]`);
                    if (optionToDisable) {
                        optionToDisable.disabled = true;
                    }

                    // If the currently selected option in the other select is now disabled, reset it
                    if (dependsOnTaskIdSelect.value === selectedValue) {
                        dependsOnTaskIdSelect.value = '';
                    }
                }
            });

            dependsOnTaskIdSelect.addEventListener('change', function() {
                const selectedValue = this.value;

                // Enable all options in the other select
                Array.from(taskIdSelect.options).forEach(option => {
                    option.disabled = false;
                });

                // Disable the selected option in the other select
                if (selectedValue) {
                    const optionToDisable = taskIdSelect.querySelector(`option[value="${selectedValue}"]`);
                    if (optionToDisable) {
                        optionToDisable.disabled = true;
                    }

                    // If the currently selected option in the other select is now disabled, reset it
                    if (taskIdSelect.value === selectedValue) {
                        taskIdSelect.value = '';
                    }
                }
            });
        }

        // Add dependency
        if (addDependencyBtn && dependencyForm) {
            addDependencyBtn.addEventListener('click', function() {
                const taskId = taskIdSelect.value;
                const dependsOnTaskId = dependsOnTaskIdSelect.value;
                const dependencyType = document.getElementById('dependency_type').value;
                const lagTime = document.getElementById('lag_time').value;

                // Validate form
                if (!taskId || !dependsOnTaskId) {
                    dependencyError.textContent = 'Please select both tasks';
                    dependencyError.classList.remove('hidden');
                    return;
                }

                // Hide error message
                dependencyError.classList.add('hidden');

                // Disable button to prevent multiple submissions
                addDependencyBtn.disabled = true;
                addDependencyBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Adding...';

                // Send AJAX request to create dependency
                fetch('/momentum/api/task-dependencies/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        task_id: taskId,
                        depends_on_task_id: dependsOnTaskId,
                        dependency_type: dependencyType,
                        lag_time: lagTime
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload page to show new dependency
                        window.location.reload();
                    } else {
                        // Show error message
                        dependencyError.textContent = data.message || 'Failed to create dependency';
                        dependencyError.classList.remove('hidden');

                        // Re-enable button
                        addDependencyBtn.disabled = false;
                        addDependencyBtn.innerHTML = 'Add Dependency';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    dependencyError.textContent = 'An error occurred. Please try again.';
                    dependencyError.classList.remove('hidden');

                    // Re-enable button
                    addDependencyBtn.disabled = false;
                    addDependencyBtn.innerHTML = 'Add Dependency';
                });
            });
        }

        // Delete dependency
        const deleteDependencyButtons = document.querySelectorAll('.delete-dependency-btn');

        if (deleteDependencyButtons.length > 0) {
            deleteDependencyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const dependencyId = this.getAttribute('data-dependency-id');

                    if (confirm('Are you sure you want to delete this dependency?')) {
                        // Send AJAX request to delete dependency
                        fetch('/momentum/api/task-dependencies/delete', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: JSON.stringify({
                                dependency_id: dependencyId
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Reload page to update dependencies
                                window.location.reload();
                            } else {
                                alert(data.message || 'Failed to delete dependency');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred. Please try again.');
                        });
                    }
                });
            });
        }

        // Team Member Management
        const addMemberBtn = document.getElementById('add-member-btn');
        const addMemberModal = document.getElementById('add-member-modal');
        const closeAddMemberModalBtn = document.getElementById('close-add-member-modal-btn');
        const addMemberModalBackdrop = document.getElementById('add-member-modal-backdrop');
        const addMemberSubmitBtn = document.getElementById('add-member-submit-btn');
        const addMemberForm = document.getElementById('add-member-form');
        const addMemberError = document.getElementById('add-member-error');

        // Open add member modal
        if (addMemberBtn && addMemberModal) {
            addMemberBtn.addEventListener('click', function() {
                addMemberModal.classList.remove('hidden');
                document.body.classList.add('overflow-hidden');
            });
        }

        // Close add member modal
        if (closeAddMemberModalBtn && addMemberModal) {
            closeAddMemberModalBtn.addEventListener('click', function() {
                addMemberModal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
                addMemberError.classList.add('hidden');
                addMemberForm.reset();
            });
        }

        // Close modal when clicking on backdrop
        if (addMemberModalBackdrop && addMemberModal) {
            addMemberModalBackdrop.addEventListener('click', function() {
                addMemberModal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
                addMemberError.classList.add('hidden');
                addMemberForm.reset();
            });
        }

        // Add member
        if (addMemberSubmitBtn && addMemberForm) {
            addMemberSubmitBtn.addEventListener('click', function() {
                const email = document.getElementById('email').value;
                const role = document.getElementById('role').value;

                // Validate form
                if (!email) {
                    addMemberError.textContent = 'Please enter an email address';
                    addMemberError.classList.remove('hidden');
                    return;
                }

                // Hide error message
                addMemberError.classList.add('hidden');

                // Disable button to prevent multiple submissions
                addMemberSubmitBtn.disabled = true;
                addMemberSubmitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Adding...';

                // Send AJAX request to add member
                fetch('/momentum/api/project-members/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        project_id: <?= $project['id'] ?>,
                        email: email,
                        role: role
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload page to show new member
                        window.location.reload();
                    } else {
                        // Show error message
                        addMemberError.textContent = data.message || 'Failed to add member';
                        addMemberError.classList.remove('hidden');

                        // Re-enable button
                        addMemberSubmitBtn.disabled = false;
                        addMemberSubmitBtn.innerHTML = 'Add Member';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    addMemberError.textContent = 'An error occurred. Please try again.';
                    addMemberError.classList.remove('hidden');

                    // Re-enable button
                    addMemberSubmitBtn.disabled = false;
                    addMemberSubmitBtn.innerHTML = 'Add Member';
                });
            });
        }

        // Change Role Modal
        const changeRoleButtons = document.querySelectorAll('.change-role-btn');
        const changeRoleModal = document.getElementById('change-role-modal');
        const closeChangeRoleModalBtn = document.getElementById('close-change-role-modal-btn');
        const changeRoleModalBackdrop = document.getElementById('change-role-modal-backdrop');
        const changeRoleSubmitBtn = document.getElementById('change-role-submit-btn');
        const changeRoleForm = document.getElementById('change-role-form');
        const changeRoleError = document.getElementById('change-role-error');
        const memberIdInput = document.getElementById('member_id');
        const newRoleSelect = document.getElementById('new_role');

        // Open change role modal
        if (changeRoleButtons.length > 0 && changeRoleModal) {
            changeRoleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const memberId = this.getAttribute('data-member-id');
                    const currentRole = this.getAttribute('data-current-role');

                    memberIdInput.value = memberId;
                    newRoleSelect.value = currentRole;

                    changeRoleModal.classList.remove('hidden');
                    document.body.classList.add('overflow-hidden');
                });
            });
        }

        // Close change role modal
        if (closeChangeRoleModalBtn && changeRoleModal) {
            closeChangeRoleModalBtn.addEventListener('click', function() {
                changeRoleModal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
                changeRoleError.classList.add('hidden');
                changeRoleForm.reset();
            });
        }

        // Close modal when clicking on backdrop
        if (changeRoleModalBackdrop && changeRoleModal) {
            changeRoleModalBackdrop.addEventListener('click', function() {
                changeRoleModal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
                changeRoleError.classList.add('hidden');
                changeRoleForm.reset();
            });
        }

        // Change role
        if (changeRoleSubmitBtn && changeRoleForm) {
            changeRoleSubmitBtn.addEventListener('click', function() {
                const memberId = memberIdInput.value;
                const newRole = newRoleSelect.value;

                // Validate form
                if (!memberId || !newRole) {
                    changeRoleError.textContent = 'Please select a role';
                    changeRoleError.classList.remove('hidden');
                    return;
                }

                // Hide error message
                changeRoleError.classList.add('hidden');

                // Disable button to prevent multiple submissions
                changeRoleSubmitBtn.disabled = true;
                changeRoleSubmitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Updating...';

                // Send AJAX request to change role
                fetch('/momentum/api/project-members/change-role', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        member_id: memberId,
                        role: newRole
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload page to show updated role
                        window.location.reload();
                    } else {
                        // Show error message
                        changeRoleError.textContent = data.message || 'Failed to change role';
                        changeRoleError.classList.remove('hidden');

                        // Re-enable button
                        changeRoleSubmitBtn.disabled = false;
                        changeRoleSubmitBtn.innerHTML = 'Change Role';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    changeRoleError.textContent = 'An error occurred. Please try again.';
                    changeRoleError.classList.remove('hidden');

                    // Re-enable button
                    changeRoleSubmitBtn.disabled = false;
                    changeRoleSubmitBtn.innerHTML = 'Change Role';
                });
            });
        }

        // Remove Member
        const removeMemberButtons = document.querySelectorAll('.remove-member-btn');

        if (removeMemberButtons.length > 0) {
            removeMemberButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const memberId = this.getAttribute('data-member-id');

                    if (confirm('Are you sure you want to remove this member from the project?')) {
                        // Send AJAX request to remove member
                        fetch('/momentum/api/project-members/remove', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: JSON.stringify({
                                member_id: memberId
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Reload page to update member list
                                window.location.reload();
                            } else {
                                alert(data.message || 'Failed to remove member');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred. Please try again.');
                        });
                    }
                });
            });
        }

        // Project Comments
        const commentForm = document.getElementById('comment-form');
        const commentInput = document.getElementById('comment');

        if (commentForm) {
            commentForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const comment = commentInput.value.trim();

                if (!comment) {
                    return;
                }

                // Send AJAX request to add comment
                fetch('/momentum/api/project-comments/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        project_id: <?= $project['id'] ?>,
                        content: comment
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload page to show new comment
                        window.location.reload();
                    } else {
                        alert(data.message || 'Failed to add comment');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
            });
        }

        // Edit Comment
        const editCommentButtons = document.querySelectorAll('.edit-comment-btn');
        const editCommentModal = document.getElementById('edit-comment-modal');
        const closeEditCommentModalBtn = document.getElementById('close-edit-comment-modal-btn');
        const editCommentModalBackdrop = document.getElementById('edit-comment-modal-backdrop');
        const editCommentSubmitBtn = document.getElementById('edit-comment-submit-btn');
        const editCommentForm = document.getElementById('edit-comment-form');
        const editCommentError = document.getElementById('edit-comment-error');
        const commentIdInput = document.getElementById('comment_id');
        const editCommentTextarea = document.getElementById('edit_comment');

        // Open edit comment modal
        if (editCommentButtons.length > 0 && editCommentModal) {
            editCommentButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const commentId = this.getAttribute('data-comment-id');
                    const commentContent = this.closest('.bg-gray-50').querySelector('.whitespace-pre-wrap').textContent;

                    commentIdInput.value = commentId;
                    editCommentTextarea.value = commentContent;

                    editCommentModal.classList.remove('hidden');
                    document.body.classList.add('overflow-hidden');
                });
            });
        }

        // Close edit comment modal
        if (closeEditCommentModalBtn && editCommentModal) {
            closeEditCommentModalBtn.addEventListener('click', function() {
                editCommentModal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
                editCommentError.classList.add('hidden');
                editCommentForm.reset();
            });
        }

        // Close modal when clicking on backdrop
        if (editCommentModalBackdrop && editCommentModal) {
            editCommentModalBackdrop.addEventListener('click', function() {
                editCommentModal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
                editCommentError.classList.add('hidden');
                editCommentForm.reset();
            });
        }

        // Edit comment
        if (editCommentSubmitBtn && editCommentForm) {
            editCommentSubmitBtn.addEventListener('click', function() {
                const commentId = commentIdInput.value;
                const content = editCommentTextarea.value.trim();

                // Validate form
                if (!content) {
                    editCommentError.textContent = 'Please enter a comment';
                    editCommentError.classList.remove('hidden');
                    return;
                }

                // Hide error message
                editCommentError.classList.add('hidden');

                // Disable button to prevent multiple submissions
                editCommentSubmitBtn.disabled = true;
                editCommentSubmitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Saving...';

                // Send AJAX request to edit comment
                fetch('/momentum/api/project-comments/edit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        comment_id: commentId,
                        content: content
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload page to show updated comment
                        window.location.reload();
                    } else {
                        // Show error message
                        editCommentError.textContent = data.message || 'Failed to edit comment';
                        editCommentError.classList.remove('hidden');

                        // Re-enable button
                        editCommentSubmitBtn.disabled = false;
                        editCommentSubmitBtn.innerHTML = 'Save Changes';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    editCommentError.textContent = 'An error occurred. Please try again.';
                    editCommentError.classList.remove('hidden');

                    // Re-enable button
                    editCommentSubmitBtn.disabled = false;
                    editCommentSubmitBtn.innerHTML = 'Save Changes';
                });
            });
        }

        // Delete Comment
        const deleteCommentButtons = document.querySelectorAll('.delete-comment-btn');

        if (deleteCommentButtons.length > 0) {
            deleteCommentButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const commentId = this.getAttribute('data-comment-id');

                    if (confirm('Are you sure you want to delete this comment?')) {
                        // Send AJAX request to delete comment
                        fetch('/momentum/api/project-comments/delete', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: JSON.stringify({
                                comment_id: commentId
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Reload page to update comments
                                window.location.reload();
                            } else {
                                alert(data.message || 'Failed to delete comment');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred. Please try again.');
                        });
                    }
                });
            });
        }

        <?php if (!empty($dependencies)): ?>
        // Task Dependencies Visualization using vis.js
        // Load vis.js from CDN
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/vis-network/standalone/umd/vis-network.min.js';
        document.head.appendChild(script);

        script.onload = function() {
            const dependencyGraph = document.getElementById('dependency-graph');
            if (dependencyGraph) {
                // Create nodes and edges from dependencies
                const nodes = new vis.DataSet();
                const edges = new vis.DataSet();

                // Map to keep track of added nodes
                const addedNodes = new Map();

                // Process dependencies
                <?php foreach ($dependencies as $dependency): ?>
                    // Add task node if not already added
                    if (!addedNodes.has(<?= $dependency['task_id'] ?>)) {
                        nodes.add({
                            id: <?= $dependency['task_id'] ?>,
                            label: '<?= addslashes($dependency['task_title']) ?>',
                            color: {
                                background: '<?= $dependency['task_status'] === 'done' ? '#10B981' : ($dependency['task_status'] === 'in_progress' ? '#3B82F6' : '#9CA3AF') ?>',
                                border: '<?= $dependency['task_status'] === 'done' ? '#059669' : ($dependency['task_status'] === 'in_progress' ? '#2563EB' : '#6B7280') ?>'
                            }
                        });
                        addedNodes.set(<?= $dependency['task_id'] ?>, true);
                    }

                    // Add dependent task node if not already added
                    if (!addedNodes.has(<?= $dependency['depends_on_task_id'] ?>)) {
                        nodes.add({
                            id: <?= $dependency['depends_on_task_id'] ?>,
                            label: '<?= addslashes($dependency['depends_on_title']) ?>',
                            color: {
                                background: '<?= $dependency['depends_on_status'] === 'done' ? '#10B981' : ($dependency['depends_on_status'] === 'in_progress' ? '#3B82F6' : '#9CA3AF') ?>',
                                border: '<?= $dependency['depends_on_status'] === 'done' ? '#059669' : ($dependency['depends_on_status'] === 'in_progress' ? '#2563EB' : '#6B7280') ?>'
                            }
                        });
                        addedNodes.set(<?= $dependency['depends_on_task_id'] ?>, true);
                    }

                    // Add edge based on dependency type
                    let arrows = 'to';
                    let label = 'FS'; // Default: Finish-to-Start

                    if ('<?= $dependency['dependency_type'] ?>' === 'start_to_start') {
                        label = 'SS';
                    } else if ('<?= $dependency['dependency_type'] ?>' === 'finish_to_finish') {
                        label = 'FF';
                    } else if ('<?= $dependency['dependency_type'] ?>' === 'start_to_finish') {
                        label = 'SF';
                        arrows = 'from';
                    }

                    edges.add({
                        from: <?= $dependency['depends_on_task_id'] ?>,
                        to: <?= $dependency['task_id'] ?>,
                        arrows: arrows,
                        label: label,
                        color: {
                            color: '#6B7280',
                            highlight: '#4B5563'
                        }
                    });
                <?php endforeach; ?>

                // Create network
                const data = {
                    nodes: nodes,
                    edges: edges
                };

                const options = {
                    layout: {
                        hierarchical: {
                            direction: 'LR',
                            sortMethod: 'directed',
                            levelSeparation: 150
                        }
                    },
                    physics: {
                        hierarchicalRepulsion: {
                            nodeDistance: 150
                        }
                    },
                    nodes: {
                        shape: 'box',
                        margin: 10,
                        font: {
                            size: 14
                        }
                    },
                    edges: {
                        smooth: {
                            type: 'cubicBezier',
                            forceDirection: 'horizontal'
                        },
                        font: {
                            size: 12,
                            align: 'middle'
                        }
                    },
                    interaction: {
                        hover: true,
                        navigationButtons: true,
                        keyboard: true
                    }
                };

                new vis.Network(dependencyGraph, data, options);

                // Add legend
                const legendDiv = document.createElement('div');
                legendDiv.className = 'mt-4 grid grid-cols-2 md:grid-cols-4 gap-2 text-sm';
                legendDiv.innerHTML = `
                    <div class="flex items-center">
                        <span class="inline-block w-4 h-4 rounded mr-2" style="background-color: #9CA3AF;"></span>
                        <span>To Do</span>
                    </div>
                    <div class="flex items-center">
                        <span class="inline-block w-4 h-4 rounded mr-2" style="background-color: #3B82F6;"></span>
                        <span>In Progress</span>
                    </div>
                    <div class="flex items-center">
                        <span class="inline-block w-4 h-4 rounded mr-2" style="background-color: #10B981;"></span>
                        <span>Done</span>
                    </div>
                    <div class="flex items-center">
                        <span class="inline-block px-1 border border-gray-400 rounded mr-2 text-xs">FS/SS/FF/SF</span>
                        <span>Dependency Type</span>
                    </div>
                `;
                dependencyGraph.parentNode.appendChild(legendDiv);
            }
        };
        <?php endif; ?>

        // Function to initialize the Gantt chart
        function initGanttChart() {
            // Load DHTMLX Gantt Chart library
            if (!window.gantt) {
                // Load CSS
                const ganttCSS = document.createElement('link');
                ganttCSS.rel = 'stylesheet';
                ganttCSS.href = 'https://cdn.dhtmlx.com/gantt/edge/dhtmlxgantt.css';
                document.head.appendChild(ganttCSS);

                // Load JS
                const ganttScript = document.createElement('script');
                ganttScript.src = 'https://cdn.dhtmlx.com/gantt/edge/dhtmlxgantt.js';
                document.head.appendChild(ganttScript);

                ganttScript.onload = function() {
                    renderGanttChart();
                };
            } else {
                renderGanttChart();
            }
        }

        // Function to render the Gantt chart
        function renderGanttChart() {
            const ganttContainer = document.getElementById('gantt-container');
            if (!ganttContainer) return;

            // Configure Gantt
            gantt.config.date_format = "%Y-%m-%d";
            gantt.config.scale_unit = "day";
            gantt.config.duration_unit = "day";
            gantt.config.row_height = 30;
            gantt.config.min_column_width = 40;

            // Configure columns in the left-side grid
            gantt.config.columns = [
                {name: "text", label: "Task name", tree: true, width: '*'},
                {name: "start_date", label: "Start date", align: "center", width: 100},
                {name: "duration", label: "Duration", align: "center", width: 70},
                {
                    name: "progress", label: "Progress", align: "center", width: 70,
                    template: function(task) {
                        return Math.round(task.progress * 100) + "%";
                    }
                }
            ];

            // Initialize Gantt in the container
            gantt.init(ganttContainer);

            // Fetch project data
            fetch('/momentum/api/task-dependencies/project/<?= $project['id'] ?>', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(response => {
                if (response.success) {
                    // Load data into Gantt chart
                    gantt.parse(response.data);

                    // Mark as initialized
                    window.ganttInitialized = true;
                } else {
                    console.error('Failed to load project data:', response.message);
                    ganttContainer.innerHTML = '<div class="text-center py-8 text-red-600 dark:text-red-400">Failed to load project data</div>';
                }
            })
            .catch(error => {
                console.error('Error loading project data:', error);
                ganttContainer.innerHTML = '<div class="text-center py-8 text-red-600 dark:text-red-400">Error loading project data</div>';
            });
        }
    });
</script>