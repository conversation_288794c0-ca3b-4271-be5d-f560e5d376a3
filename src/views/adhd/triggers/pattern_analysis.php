<?php
/**
 * Trigger Pattern Analysis Dashboard
 * 
 * Advanced visualization and analysis of ADHD trigger patterns
 */

require_once '../../../utils/TriggerPatternAnalyzer.php';

$analyzer = new TriggerPatternAnalyzer();
$userId = Session::get('user_id');

// Get analysis data
$days = isset($_GET['days']) ? (int)$_GET['days'] : 30;
$analysis = $analyzer->analyzeTriggerPatterns($userId, $days);
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Trigger Pattern Analysis</h1>
                <p class="mt-2 text-gray-600 dark:text-gray-300">
                    Advanced insights into your ADHD trigger patterns and correlations
                </p>
            </div>
            
            <div class="flex items-center space-x-4">
                <!-- Time Period Selector -->
                <select id="time-period" onchange="updateTimePeriod(this.value)" 
                        class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                    <option value="7" <?= $days == 7 ? 'selected' : '' ?>>Last 7 days</option>
                    <option value="30" <?= $days == 30 ? 'selected' : '' ?>>Last 30 days</option>
                    <option value="90" <?= $days == 90 ? 'selected' : '' ?>>Last 90 days</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Frequency Analysis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Top Triggers -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Most Frequent Triggers</h3>
            </div>
            <div class="px-6 py-4">
                <?php if (!empty($analysis['frequency_analysis']['by_trigger'])): ?>
                    <div class="space-y-4">
                        <?php foreach (array_slice($analysis['frequency_analysis']['by_trigger'], 0, 5) as $trigger => $data): ?>
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                                        <?= htmlspecialchars($trigger) ?>
                                    </span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">
                                        <?= $data['count'] ?> times (<?= $data['percentage'] ?>%)
                                    </span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-red-500 h-2 rounded-full" style="width: <?= $data['percentage'] ?>%"></div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500 dark:text-gray-400 text-center py-8">
                        No trigger data available for this period.
                    </p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Categories -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Trigger Categories</h3>
            </div>
            <div class="px-6 py-4">
                <?php if (!empty($analysis['frequency_analysis']['by_category'])): ?>
                    <div class="space-y-4">
                        <?php 
                        $colors = ['bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-purple-500', 'bg-pink-500'];
                        $colorIndex = 0;
                        foreach ($analysis['frequency_analysis']['by_category'] as $category => $data): 
                        ?>
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-900 dark:text-white capitalize">
                                        <?= htmlspecialchars($category) ?>
                                    </span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">
                                        <?= $data['count'] ?> times (<?= $data['percentage'] ?>%)
                                    </span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="<?= $colors[$colorIndex % count($colors)] ?> h-2 rounded-full" 
                                         style="width: <?= $data['percentage'] ?>%"></div>
                                </div>
                            </div>
                        </div>
                        <?php 
                        $colorIndex++;
                        endforeach; 
                        ?>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500 dark:text-gray-400 text-center py-8">
                        No category data available.
                    </p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Temporal Patterns -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-8">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Temporal Patterns</h3>
        </div>
        <div class="px-6 py-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Hourly Distribution -->
                <div>
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">Triggers by Hour of Day</h4>
                    <div class="space-y-2">
                        <?php 
                        $maxHourly = max($analysis['temporal_patterns']['hourly_distribution']);
                        for ($hour = 0; $hour < 24; $hour++): 
                            $count = $analysis['temporal_patterns']['hourly_distribution'][$hour];
                            $percentage = $maxHourly > 0 ? ($count / $maxHourly) * 100 : 0;
                        ?>
                        <div class="flex items-center">
                            <div class="w-12 text-xs text-gray-500 dark:text-gray-400">
                                <?= sprintf('%02d:00', $hour) ?>
                            </div>
                            <div class="flex-1 mx-2">
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: <?= $percentage ?>%"></div>
                                </div>
                            </div>
                            <div class="w-8 text-xs text-gray-500 dark:text-gray-400 text-right">
                                <?= $count ?>
                            </div>
                        </div>
                        <?php endfor; ?>
                    </div>
                    
                    <?php if (!empty($analysis['temporal_patterns']['peak_hours'])): ?>
                    <div class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                        <p class="text-sm text-blue-800 dark:text-blue-200">
                            <strong>Peak Hours:</strong> 
                            <?= implode(', ', array_map(function($h) { return sprintf('%02d:00', $h); }, $analysis['temporal_patterns']['peak_hours'])) ?>
                        </p>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Daily Distribution -->
                <div>
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">Triggers by Day of Week</h4>
                    <div class="space-y-2">
                        <?php 
                        $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                        $maxDaily = max($analysis['temporal_patterns']['daily_distribution']);
                        for ($day = 1; $day <= 7; $day++): 
                            $count = $analysis['temporal_patterns']['daily_distribution'][$day];
                            $percentage = $maxDaily > 0 ? ($count / $maxDaily) * 100 : 0;
                        ?>
                        <div class="flex items-center">
                            <div class="w-20 text-xs text-gray-500 dark:text-gray-400">
                                <?= $days[$day - 1] ?>
                            </div>
                            <div class="flex-1 mx-2">
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: <?= $percentage ?>%"></div>
                                </div>
                            </div>
                            <div class="w-8 text-xs text-gray-500 dark:text-gray-400 text-right">
                                <?= $count ?>
                            </div>
                        </div>
                        <?php endfor; ?>
                    </div>
                    
                    <?php if (!empty($analysis['temporal_patterns']['peak_days'])): ?>
                    <div class="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-md">
                        <p class="text-sm text-green-800 dark:text-green-200">
                            <strong>Peak Days:</strong> 
                            <?= implode(', ', $analysis['temporal_patterns']['peak_days']) ?>
                        </p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Severity Correlations -->
    <?php if (!empty($analysis['severity_correlation'])): ?>
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-8">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Trigger-Symptom Correlations</h3>
        </div>
        <div class="px-6 py-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <?php foreach (array_slice($analysis['severity_correlation'], 0, 6) as $trigger => $correlation): ?>
                <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">
                        <?= htmlspecialchars($trigger) ?>
                    </h4>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Average Impact:</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                                <?= $correlation['average_impact'] ?>/10
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Occurrences:</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                                <?= $correlation['occurrences'] ?>
                            </span>
                        </div>
                        
                        <?php if (!empty($correlation['symptom_increases'])): ?>
                        <div class="mt-3">
                            <p class="text-xs text-gray-500 dark:text-gray-400 mb-2">Symptom Impact:</p>
                            <?php foreach ($correlation['symptom_increases'] as $symptom => $severity): ?>
                            <div class="flex items-center justify-between text-xs">
                                <span class="capitalize text-gray-600 dark:text-gray-400"><?= $symptom ?>:</span>
                                <span class="font-medium <?= $severity > 5 ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400' ?>">
                                    <?= $severity ?>/10
                                </span>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Trigger Combinations -->
    <?php if (!empty($analysis['trigger_combinations'])): ?>
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-8">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Common Trigger Combinations</h3>
        </div>
        <div class="px-6 py-4">
            <div class="space-y-3">
                <?php foreach ($analysis['trigger_combinations'] as $combination => $count): ?>
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                    <span class="text-sm text-gray-900 dark:text-white">
                        <?= htmlspecialchars($combination) ?>
                    </span>
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                        <?= $count ?> times
                    </span>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Recommendations -->
    <?php if (!empty($analysis['recommendations'])): ?>
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">AI-Powered Recommendations</h3>
        </div>
        <div class="px-6 py-4">
            <div class="space-y-6">
                <?php foreach ($analysis['recommendations'] as $recommendation): ?>
                <div class="border-l-4 <?= 
                    $recommendation['priority'] === 'high' ? 'border-red-500' :
                    ($recommendation['priority'] === 'medium' ? 'border-yellow-500' : 'border-blue-500') 
                ?> pl-4">
                    <h4 class="font-medium text-gray-900 dark:text-white">
                        <?= htmlspecialchars($recommendation['title']) ?>
                    </h4>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                        <?= htmlspecialchars($recommendation['description']) ?>
                    </p>
                    
                    <?php if (!empty($recommendation['strategies'])): ?>
                    <div class="mt-3">
                        <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Suggested Strategies:</p>
                        <ul class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                            <?php foreach ($recommendation['strategies'] as $strategy): ?>
                            <li class="flex items-start">
                                <span class="text-blue-500 mr-2">•</span>
                                <?= htmlspecialchars($strategy) ?>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function updateTimePeriod(days) {
    window.location.href = `?days=${days}`;
}
</script>
