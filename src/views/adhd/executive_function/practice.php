<?php
/**
 * Executive Function Exercise Practice
 * 
 * Interface for practicing executive function exercises
 */
?>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back navigation -->
        <div class="mb-6">
            <a href="/momentum/adhd/executive-function/view/<?= $exercise['id'] ?>" class="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">
                <i class="fas fa-arrow-left mr-1"></i> Back to Exercise Details
            </a>
        </div>
        
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:p-6">
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                    Practice: <?= htmlspecialchars($exercise['name']) ?>
                </h1>
                
                <div class="flex flex-wrap gap-2 mb-4">
                    <?php 
                    $categoryColors = [
                        'working_memory' => 'blue',
                        'task_initiation' => 'green',
                        'planning' => 'purple',
                        'organization' => 'indigo',
                        'time_management' => 'yellow',
                        'emotional_regulation' => 'pink'
                    ];
                    $color = $categoryColors[$exercise['category']] ?? 'gray';
                    ?>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<?= $color ?>-100 text-<?= $color ?>-800 dark:bg-<?= $color ?>-800 dark:text-<?= $color ?>-100">
                        <?= ucfirst(str_replace('_', ' ', $exercise['category'])) ?>
                    </span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                        <i class="fas fa-clock mr-1"></i> <?= $exercise['estimated_time'] ?? '5-10' ?> minutes
                    </span>
                </div>
                
                <!-- Exercise Instructions -->
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md mb-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Instructions</h3>
                    <p class="text-gray-600 dark:text-gray-300">
                        <?= nl2br(htmlspecialchars($exercise['instructions'])) ?>
                    </p>
                </div>
                
                <!-- Exercise Interface -->
                <div id="exercise-interface" class="mb-6">
                    <!-- This section will be populated with exercise-specific content via JavaScript -->
                    <div class="text-center py-8">
                        <button id="start-exercise" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-play mr-1"></i> Start Exercise
                        </button>
                    </div>
                    
                    <div id="exercise-content" class="hidden">
                        <!-- Exercise content will be loaded here -->
                    </div>
                </div>
                
                <!-- Results Form (initially hidden) -->
                <div id="results-form" class="hidden border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Record Your Results</h3>
                    
                    <form id="exercise-result-form" class="space-y-4">
                        <input type="hidden" name="exercise_id" value="<?= $exercise['id'] ?>">
                        
                        <div>
                            <label for="score" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Your Score (1-10)</label>
                            <div class="mt-1">
                                <input type="range" id="score" name="score" min="1" max="10" value="5" class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 px-1">
                                    <span>1</span>
                                    <span>2</span>
                                    <span>3</span>
                                    <span>4</span>
                                    <span>5</span>
                                    <span>6</span>
                                    <span>7</span>
                                    <span>8</span>
                                    <span>9</span>
                                    <span>10</span>
                                </div>
                                <p class="text-center mt-2 text-sm font-medium" id="score-display">5</p>
                            </div>
                        </div>
                        
                        <div>
                            <label for="time_taken" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Time Taken (seconds)</label>
                            <div class="mt-1">
                                <input type="number" name="time_taken" id="time_taken" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-md" min="1" required>
                            </div>
                        </div>
                        
                        <div>
                            <label for="difficulty_rating" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Difficulty Rating (1-5)</label>
                            <div class="mt-1">
                                <select id="difficulty_rating" name="difficulty_rating" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-md">
                                    <option value="1">1 - Very Easy</option>
                                    <option value="2">2 - Easy</option>
                                    <option value="3" selected>3 - Moderate</option>
                                    <option value="4">4 - Difficult</option>
                                    <option value="5">5 - Very Difficult</option>
                                </select>
                            </div>
                        </div>
                        
                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes (Optional)</label>
                            <div class="mt-1">
                                <textarea id="notes" name="notes" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-md"></textarea>
                            </div>
                        </div>
                        
                        <div class="flex justify-end">
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <i class="fas fa-save mr-1"></i> Save Results
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Success Message (initially hidden) -->
                <div id="success-message" class="hidden mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-400 dark:text-green-300"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800 dark:text-green-200">Results Saved!</h3>
                            <div class="mt-2 text-sm text-green-700 dark:text-green-300">
                                <p>Your exercise results have been saved successfully.</p>
                            </div>
                            <div class="mt-4">
                                <div class="-mx-2 -my-1.5 flex">
                                    <a href="/momentum/adhd/executive-function" class="bg-green-50 dark:bg-green-900 px-2 py-1.5 rounded-md text-sm font-medium text-green-800 dark:text-green-200 hover:bg-green-100 dark:hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        Return to Exercises
                                    </a>
                                    <a href="/momentum/adhd/executive-function/progress" class="ml-3 bg-green-50 dark:bg-green-900 px-2 py-1.5 rounded-md text-sm font-medium text-green-800 dark:text-green-200 hover:bg-green-100 dark:hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        View Progress
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const startButton = document.getElementById('start-exercise');
    const exerciseContent = document.getElementById('exercise-content');
    const resultsForm = document.getElementById('results-form');
    const scoreInput = document.getElementById('score');
    const scoreDisplay = document.getElementById('score-display');
    const exerciseResultForm = document.getElementById('exercise-result-form');
    const successMessage = document.getElementById('success-message');
    
    // Timer variables
    let startTime;
    let timerInterval;
    
    // Update score display when slider changes
    scoreInput.addEventListener('input', function() {
        scoreDisplay.textContent = this.value;
    });
    
    // Start exercise button click handler
    startButton.addEventListener('click', function() {
        startExercise();
    });
    
    // Start the exercise
    function startExercise() {
        startButton.classList.add('hidden');
        exerciseContent.classList.remove('hidden');
        
        // Record start time
        startTime = new Date();
        
        // Load exercise content based on category
        const category = '<?= $exercise['category'] ?>';
        loadExerciseContent(category);
    }
    
    // Load exercise content based on category
    function loadExerciseContent(category) {
        let content = '';
        
        switch(category) {
            case 'working_memory':
                content = createWorkingMemoryExercise();
                break;
            case 'task_initiation':
                content = createTaskInitiationExercise();
                break;
            case 'planning':
                content = createPlanningExercise();
                break;
            case 'organization':
                content = createOrganizationExercise();
                break;
            case 'time_management':
                content = createTimeManagementExercise();
                break;
            case 'emotional_regulation':
                content = createEmotionalRegulationExercise();
                break;
            default:
                content = createGenericExercise();
        }
        
        exerciseContent.innerHTML = content;
        
        // Add finish button event listener
        const finishButton = document.getElementById('finish-exercise');
        if (finishButton) {
            finishButton.addEventListener('click', finishExercise);
        }
    }
    
    // Finish the exercise
    function finishExercise() {
        // Calculate time taken
        const endTime = new Date();
        const timeTaken = Math.round((endTime - startTime) / 1000); // in seconds
        
        // Set the time taken in the form
        document.getElementById('time_taken').value = timeTaken;
        
        // Show results form
        resultsForm.classList.remove('hidden');
        
        // Scroll to results form
        resultsForm.scrollIntoView({ behavior: 'smooth' });
    }
    
    // Handle form submission
    exerciseResultForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(exerciseResultForm);
        
        // Send data to server
        fetch('/momentum/adhd/executive-function/save-result', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hide form and show success message
                resultsForm.classList.add('hidden');
                exerciseContent.classList.add('hidden');
                successMessage.classList.remove('hidden');
            } else {
                alert('Error: ' + (data.message || 'Failed to save results'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while saving results');
        });
    });
    
    // Exercise content generators
    function createGenericExercise() {
        return `
            <div class="text-center py-8">
                <p class="text-lg text-gray-700 dark:text-gray-300 mb-6">
                    Please follow the instructions above to complete this exercise.
                </p>
                <button id="finish-exercise" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                    <i class="fas fa-check mr-1"></i> Finish Exercise
                </button>
            </div>
        `;
    }
    
    // Create specific exercise interfaces based on category
    function createWorkingMemoryExercise() {
        const sequences = [
            [3, 7, 2, 9, 1],
            [8, 4, 6, 1, 5, 3],
            [2, 9, 7, 4, 1, 8, 6],
            [5, 1, 9, 3, 7, 2, 8, 4],
            [6, 2, 8, 1, 5, 9, 3, 7, 4]
        ];

        return `
            <div class="working-memory-exercise">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Number Sequence Memory</h3>
                    <p class="text-gray-600 dark:text-gray-300">Remember the sequence of numbers that appears, then enter them in order.</p>
                </div>

                <div class="max-w-md mx-auto">
                    <div id="sequence-display" class="text-center p-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg mb-4 hidden">
                        <div id="sequence-numbers" class="text-4xl font-bold text-blue-600 dark:text-blue-400 tracking-wider"></div>
                        <div id="countdown" class="text-sm text-gray-500 dark:text-gray-400 mt-2"></div>
                    </div>

                    <div id="input-phase" class="hidden">
                        <div class="text-center mb-4">
                            <p class="text-lg font-medium text-gray-900 dark:text-white">Enter the sequence:</p>
                        </div>
                        <div class="flex flex-wrap gap-2 justify-center mb-4" id="number-inputs"></div>
                        <div class="text-center">
                            <button id="check-sequence" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                Check Answer
                            </button>
                        </div>
                    </div>

                    <div id="start-memory-test" class="text-center">
                        <button class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-lg font-medium">
                            Start Memory Test
                        </button>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">Level 1: 5 numbers</p>
                    </div>

                    <div id="memory-results" class="hidden text-center mt-4">
                        <div id="result-message" class="text-lg font-medium mb-2"></div>
                        <div id="score-display-memory" class="text-sm text-gray-600 dark:text-gray-300 mb-4"></div>
                        <button id="next-level" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors mr-2">
                            Next Level
                        </button>
                        <button id="finish-exercise" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                            Finish Exercise
                        </button>
                    </div>
                </div>
            </div>

            <script>
            (function() {
                let currentLevel = 0;
                let currentSequence = [];
                let score = 0;
                let totalAttempts = 0;

                const sequences = ${JSON.stringify(sequences)};

                document.getElementById('start-memory-test').addEventListener('click', startMemoryTest);

                function startMemoryTest() {
                    document.getElementById('start-memory-test').style.display = 'none';
                    showSequence();
                }

                function showSequence() {
                    currentSequence = sequences[currentLevel];
                    const display = document.getElementById('sequence-display');
                    const numbers = document.getElementById('sequence-numbers');
                    const countdown = document.getElementById('countdown');

                    display.classList.remove('hidden');
                    numbers.textContent = currentSequence.join(' ');

                    let timeLeft = 5;
                    countdown.textContent = timeLeft + 's';

                    const timer = setInterval(() => {
                        timeLeft--;
                        countdown.textContent = timeLeft + 's';

                        if (timeLeft <= 0) {
                            clearInterval(timer);
                            display.classList.add('hidden');
                            showInputPhase();
                        }
                    }, 1000);
                }

                function showInputPhase() {
                    const inputPhase = document.getElementById('input-phase');
                    const inputsContainer = document.getElementById('number-inputs');

                    inputsContainer.innerHTML = '';

                    for (let i = 0; i < currentSequence.length; i++) {
                        const input = document.createElement('input');
                        input.type = 'number';
                        input.className = 'w-12 h-12 text-center border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-800 dark:text-white';
                        input.min = '0';
                        input.max = '9';
                        input.dataset.index = i;
                        inputsContainer.appendChild(input);
                    }

                    inputPhase.classList.remove('hidden');

                    document.getElementById('check-sequence').onclick = checkSequence;
                }

                function checkSequence() {
                    const inputs = document.querySelectorAll('#number-inputs input');
                    const userSequence = Array.from(inputs).map(input => parseInt(input.value) || 0);

                    totalAttempts++;
                    const correct = JSON.stringify(userSequence) === JSON.stringify(currentSequence);

                    if (correct) {
                        score += (currentLevel + 1) * 10;
                    }

                    showResults(correct);
                }

                function showResults(correct) {
                    document.getElementById('input-phase').classList.add('hidden');
                    const results = document.getElementById('memory-results');
                    const message = document.getElementById('result-message');
                    const scoreDisplay = document.getElementById('score-display-memory');

                    message.textContent = correct ? '✅ Correct!' : '❌ Incorrect';
                    message.className = correct ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';

                    scoreDisplay.textContent = 'Score: ' + score + ' | Level: ' + (currentLevel + 1) + '/5';

                    results.classList.remove('hidden');

                    document.getElementById('next-level').onclick = nextLevel;
                    document.getElementById('finish-exercise').onclick = finishExercise;

                    if (currentLevel >= sequences.length - 1) {
                        document.getElementById('next-level').style.display = 'none';
                    }
                }

                function nextLevel() {
                    if (currentLevel < sequences.length - 1) {
                        currentLevel++;
                        document.getElementById('memory-results').classList.add('hidden');
                        showSequence();
                    }
                }

                function finishExercise() {
                    // Set final score
                    const accuracy = totalAttempts > 0 ? Math.round((score / (totalAttempts * 10)) * 10) : 5;
                    document.getElementById('score').value = Math.min(10, Math.max(1, accuracy));
                    document.getElementById('score-display').textContent = document.getElementById('score').value;

                    // Trigger the main finish function
                    window.finishExercise();
                }
            })();
            </script>
        `;
    }

    function createTaskInitiationExercise() {
        const sampleTasks = [
            "Write a 5-page research paper on climate change",
            "Organize my entire bedroom and closet",
            "Plan a birthday party for 20 people",
            "Learn to play guitar",
            "Start a small online business"
        ];

        return `
            <div class="task-initiation-exercise">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Task Breakdown Challenge</h3>
                    <p class="text-gray-600 dark:text-gray-300">Break down a large task into smaller, manageable steps using the 2-minute rule.</p>
                </div>

                <div class="max-w-2xl mx-auto">
                    <div id="task-selection" class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Choose a task to break down:
                        </label>
                        <select id="task-selector" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-800 dark:text-white">
                            <option value="">Select a task...</option>
                            ${sampleTasks.map((task, index) => `<option value="${index}">${task}</option>`).join('')}
                            <option value="custom">Enter my own task...</option>
                        </select>

                        <div id="custom-task-input" class="hidden mt-3">
                            <input type="text" id="custom-task" placeholder="Enter your task..."
                                   class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-800 dark:text-white">
                        </div>

                        <button id="start-breakdown" class="mt-4 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50" disabled>
                            Start Breaking Down
                        </button>
                    </div>

                    <div id="breakdown-interface" class="hidden">
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
                            <h4 class="font-medium text-gray-900 dark:text-white mb-2">Main Task:</h4>
                            <p id="selected-task" class="text-gray-700 dark:text-gray-300"></p>
                        </div>

                        <div class="mb-6">
                            <h4 class="font-medium text-gray-900 dark:text-white mb-3">Break it down into steps:</h4>
                            <div id="steps-container" class="space-y-3">
                                <div class="step-input-group flex gap-2">
                                    <input type="text" placeholder="Step 1: What's the first thing you need to do?"
                                           class="flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-800 dark:text-white step-input">
                                    <button class="add-step px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mt-4 text-center">
                                <button id="analyze-steps" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
                                    Analyze My Steps
                                </button>
                            </div>
                        </div>

                        <div id="analysis-results" class="hidden">
                            <h4 class="font-medium text-gray-900 dark:text-white mb-3">Step Analysis:</h4>
                            <div id="step-analysis" class="space-y-3 mb-4"></div>

                            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-4">
                                <h5 class="font-medium text-blue-900 dark:text-blue-100 mb-2">💡 ADHD-Friendly Tips:</h5>
                                <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                                    <li>• Each step should take 2-25 minutes maximum</li>
                                    <li>• Start with the easiest step to build momentum</li>
                                    <li>• Set a timer for each step to maintain focus</li>
                                    <li>• Celebrate completing each step!</li>
                                </ul>
                            </div>

                            <div class="text-center">
                                <button id="finish-exercise" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                                    Finish Exercise
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <script>
            (function() {
                const sampleTasks = ${JSON.stringify(sampleTasks)};
                let stepCount = 1;
                let totalSteps = 0;
                let goodSteps = 0;

                const taskSelector = document.getElementById('task-selector');
                const customTaskInput = document.getElementById('custom-task-input');
                const startButton = document.getElementById('start-breakdown');
                const breakdownInterface = document.getElementById('breakdown-interface');

                taskSelector.addEventListener('change', function() {
                    if (this.value === 'custom') {
                        customTaskInput.classList.remove('hidden');
                        startButton.disabled = false;
                    } else if (this.value !== '') {
                        customTaskInput.classList.add('hidden');
                        startButton.disabled = false;
                    } else {
                        customTaskInput.classList.add('hidden');
                        startButton.disabled = true;
                    }
                });

                startButton.addEventListener('click', function() {
                    const selectedTask = taskSelector.value === 'custom'
                        ? document.getElementById('custom-task').value
                        : sampleTasks[parseInt(taskSelector.value)];

                    if (!selectedTask.trim()) return;

                    document.getElementById('selected-task').textContent = selectedTask;
                    document.getElementById('task-selection').classList.add('hidden');
                    breakdownInterface.classList.remove('hidden');

                    setupStepInputs();
                });

                function setupStepInputs() {
                    document.addEventListener('click', function(e) {
                        if (e.target.classList.contains('add-step')) {
                            addNewStep();
                        }
                    });

                    document.getElementById('analyze-steps').addEventListener('click', analyzeSteps);
                }

                function addNewStep() {
                    stepCount++;
                    const container = document.getElementById('steps-container');
                    const newStep = document.createElement('div');
                    newStep.className = 'step-input-group flex gap-2';
                    newStep.innerHTML = \`
                        <input type="text" placeholder="Step \${stepCount}: What's next?"
                               class="flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-800 dark:text-white step-input">
                        <button class="remove-step px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                            <i class="fas fa-minus"></i>
                        </button>
                    \`;

                    container.appendChild(newStep);

                    newStep.querySelector('.remove-step').addEventListener('click', function() {
                        newStep.remove();
                    });
                }

                function analyzeSteps() {
                    const stepInputs = document.querySelectorAll('.step-input');
                    const steps = Array.from(stepInputs).map(input => input.value.trim()).filter(step => step);

                    if (steps.length === 0) {
                        alert('Please add at least one step!');
                        return;
                    }

                    totalSteps = steps.length;
                    goodSteps = 0;

                    const analysisContainer = document.getElementById('step-analysis');
                    analysisContainer.innerHTML = '';

                    steps.forEach((step, index) => {
                        const isGoodStep = analyzeStepQuality(step);
                        if (isGoodStep) goodSteps++;

                        const stepDiv = document.createElement('div');
                        stepDiv.className = \`p-3 rounded-lg border \${isGoodStep ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' : 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'}\`;
                        stepDiv.innerHTML = \`
                            <div class="flex items-start gap-2">
                                <span class="\${isGoodStep ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'}">
                                    \${isGoodStep ? '✅' : '⚠️'}
                                </span>
                                <div class="flex-1">
                                    <p class="font-medium text-gray-900 dark:text-white">Step \${index + 1}: \${step}</p>
                                    <p class="text-sm \${isGoodStep ? 'text-green-700 dark:text-green-300' : 'text-yellow-700 dark:text-yellow-300'} mt-1">
                                        \${isGoodStep ? 'Good! This step is specific and actionable.' : 'Consider making this step more specific and smaller.'}
                                    </p>
                                </div>
                            </div>
                        \`;
                        analysisContainer.appendChild(stepDiv);
                    });

                    document.getElementById('analysis-results').classList.remove('hidden');

                    // Calculate score based on step quality
                    const score = Math.round((goodSteps / totalSteps) * 10);
                    document.getElementById('score').value = Math.max(1, score);
                    document.getElementById('score-display').textContent = document.getElementById('score').value;
                }

                function analyzeStepQuality(step) {
                    // Simple heuristics for good steps
                    const hasActionVerb = /^(write|create|research|call|email|buy|organize|clean|plan|schedule|review|draft|outline|gather|collect|contact|visit|download|install|setup|configure|test|practice|study|read|watch|listen|measure|calculate|design|draw|sketch|build|make|prepare|cook|wash|sort|file|label|update|backup|delete|remove|add|insert|edit|revise|proofread|submit|send|share|post|publish|upload|save|print|scan|copy|move|transfer|convert|format|compress|extract|search|find|locate|identify|compare|analyze|evaluate|assess|check|verify|confirm|approve|reject|accept|decline|respond|reply|answer|ask|request|order|purchase|pay|invoice|bill|charge|refund|return|exchange|ship|deliver|receive|pickup|drop|off|on|start|begin|initiate|launch|open|close|finish|complete|end|stop|pause|resume|continue|restart|reset|refresh|reload|update|upgrade|downgrade|install|uninstall|enable|disable|activate|deactivate|turn|switch|toggle|adjust|modify|change|alter|improve|enhance|optimize|fix|repair|troubleshoot|debug|solve|resolve|address|handle|manage|coordinate|supervise|monitor|track|log|record|document|note|list|enumerate|count|tally|sum|total|average|calculate|compute|determine|decide|choose|select|pick|opt|prefer|recommend|suggest|propose|offer|provide|supply|deliver|give|take|get|obtain|acquire|gain|earn|win|lose|miss|skip|ignore|avoid|prevent|protect|secure|lock|unlock|hide|show|display|present|demonstrate|explain|describe|define|clarify|specify|detail|elaborate|expand|extend|stretch|reach|achieve|accomplish|attain|realize|fulfill|satisfy|meet|exceed|surpass|beat|defeat|overcome|conquer|master|learn|understand|comprehend|grasp|absorb|retain|remember|recall|recognize|identify|distinguish|differentiate|separate|divide|split|combine|merge|join|connect|link|attach|detach|remove|delete|erase|clear|clean|wash|rinse|dry|wipe|dust|vacuum|sweep|mop|scrub|polish|shine|paint|color|decorate|arrange|rearrange|reorganize|restructure|rebuild|reconstruct|renovate|remodel|refurbish|restore|repair|maintain|service|inspect|examine|investigate|explore|discover|uncover|reveal|expose|hide|conceal|cover|wrap|unwrap|pack|unpack|load|unload|fill|empty|pour|drain|flow|stream|run|walk|jog|sprint|jump|leap|climb|descend|ascend|rise|fall|drop|lift|raise|lower|push|pull|drag|carry|transport|move|shift|slide|roll|spin|rotate|turn|twist|bend|fold|unfold|stretch|compress|expand|contract|extend|retract|project|reflect|refract|absorb|emit|radiate|transmit|receive|send|broadcast|publish|announce|declare|proclaim|state|say|tell|speak|talk|communicate|express|convey|relay|forward|pass|hand|give|offer|present|show|demonstrate|exhibit|display|reveal|expose|uncover|discover|find|locate|search|seek|look|see|observe|watch|monitor|supervise|oversee|manage|control|direct|guide|lead|follow|accompany|escort|attend|participate|join|enter|exit|leave|arrive|depart|go|come|return|visit|travel|journey|trip|tour|explore|adventure|experience|enjoy|appreciate|value|treasure|cherish|love|like|prefer|favor|choose|select|pick|decide|determine|resolve|conclude|finish|complete|end|stop|cease|halt|pause|break|rest|relax|calm|soothe|comfort|console|support|help|assist|aid|serve|provide|supply|offer|give|donate|contribute|share|distribute|allocate|assign|designate|appoint|nominate|elect|vote|choose|select|pick|decide|determine|judge|evaluate|assess|rate|rank|score|grade|mark|label|tag|categorize|classify|group|sort|arrange|organize|structure|format|style|design|create|make|build|construct|develop|produce|generate|manufacture|fabricate|assemble|compile|compose|write|author|draft|edit|revise|review|proofread|correct|fix|repair|mend|heal|cure|treat|remedy|solve|resolve|address|handle|deal|cope|manage|control|regulate|adjust|modify|change|alter|transform|convert|translate|interpret|explain|clarify|simplify|complicate|complex|difficult|easy|simple|basic|advanced|expert|professional|amateur|beginner|novice|experienced|skilled|talented|gifted|capable|able|competent|qualified|certified|licensed|authorized|permitted|allowed|approved|accepted|rejected|denied|refused|declined|dismissed|ignored|overlooked|missed|forgotten|remembered|recalled|recognized|identified|distinguished|differentiated|separated|divided|split|combined|merged|joined|connected|linked|attached|detached|removed|deleted|erased|cleared|cleaned|washed|rinsed|dried|wiped|dusted|vacuumed|swept|mopped|scrubbed|polished|shined|painted|colored|decorated|arranged|rearranged|reorganized|restructured|rebuilt|reconstructed|renovated|remodeled|refurbished|restored|repaired|maintained|serviced|inspected|examined|investigated|explored|discovered|uncovered|revealed|exposed|hidden|concealed|covered|wrapped|unwrapped|packed|unpacked|loaded|unloaded|filled|emptied|poured|drained|flowed|streamed|ran|walked|jogged|sprinted|jumped|leaped|climbed|descended|ascended|rose|fell|dropped|lifted|raised|lowered|pushed|pulled|dragged|carried|transported|moved|shifted|slid|rolled|spun|rotated|turned|twisted|bent|folded|unfolded|stretched|compressed|expanded|contracted|extended|retracted|projected|reflected|refracted|absorbed|emitted|radiated|transmitted|received|sent|broadcasted|published|announced|declared|proclaimed|stated|said|told|spoke|talked|communicated|expressed|conveyed|relayed|forwarded|passed|handed|gave|offered|presented|showed|demonstrated|exhibited|displayed|revealed|exposed|uncovered|discovered|found|located|searched|sought|looked|saw|observed|watched|monitored|supervised|oversaw|managed|controlled|directed|guided|led|followed|accompanied|escorted|attended|participated|joined|entered|exited|left|arrived|departed|went|came|returned|visited|traveled|journeyed|tripped|toured|explored|adventured|experienced|enjoyed|appreciated|valued|treasured|cherished|loved|liked|preferred|favored|chose|selected|picked|decided|determined|resolved|concluded|finished|completed|ended|stopped|ceased|halted|paused|broke|rested|relaxed|calmed|soothed|comforted|consoled|supported|helped|assisted|aided|served|provided|supplied|offered|gave|donated|contributed|shared|distributed|allocated|assigned|designated|appointed|nominated|elected|voted)/i.test(step);
                    const isSpecific = step.length > 10 && step.length < 100;
                    const hasNoVagueWords = !/\\b(some|many|few|several|various|different|stuff|things|everything|anything|something|nothing|whatever|whenever|however|somewhere|anywhere|everywhere|nowhere)\\b/i.test(step);

                    return hasActionVerb && isSpecific && hasNoVagueWords;
                }

                document.getElementById('finish-exercise').addEventListener('click', function() {
                    window.finishExercise();
                });
            })();
            </script>
        `;
    }

    function createPlanningExercise() {
        return `
            <div class="planning-exercise">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Priority Matrix Planner</h3>
                    <p class="text-gray-600 dark:text-gray-300">Organize your tasks using the Eisenhower Matrix (Important vs Urgent).</p>
                </div>

                <div class="max-w-4xl mx-auto">
                    <div id="task-input-section" class="mb-6">
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
                            <h4 class="font-medium text-gray-900 dark:text-white mb-2">Add Your Tasks</h4>
                            <div class="flex gap-2">
                                <input type="text" id="new-task-input" placeholder="Enter a task or goal..."
                                       class="flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-800 dark:text-white">
                                <button id="add-task-btn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                    Add Task
                                </button>
                            </div>
                        </div>

                        <div id="task-list" class="mb-4">
                            <h5 class="font-medium text-gray-900 dark:text-white mb-2">Tasks to Organize:</h5>
                            <div id="unorganized-tasks" class="space-y-2 min-h-[50px] p-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                                <p class="text-gray-500 dark:text-gray-400 text-center">Add tasks above to get started</p>
                            </div>
                        </div>

                        <div class="text-center">
                            <button id="start-organizing" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50" disabled>
                                Start Organizing
                            </button>
                        </div>
                    </div>

                    <div id="matrix-section" class="hidden">
                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <!-- Quadrant 1: Important & Urgent -->
                            <div class="quadrant bg-red-50 dark:bg-red-900/20 border-2 border-red-200 dark:border-red-800 rounded-lg p-4 min-h-[200px]" data-quadrant="1">
                                <h4 class="font-bold text-red-800 dark:text-red-200 mb-2 text-center">
                                    🔥 DO FIRST
                                </h4>
                                <p class="text-sm text-red-700 dark:text-red-300 mb-3 text-center">Important & Urgent</p>
                                <div class="task-drop-zone min-h-[120px] space-y-2" data-zone="urgent-important"></div>
                            </div>

                            <!-- Quadrant 2: Important & Not Urgent -->
                            <div class="quadrant bg-green-50 dark:bg-green-900/20 border-2 border-green-200 dark:border-green-800 rounded-lg p-4 min-h-[200px]" data-quadrant="2">
                                <h4 class="font-bold text-green-800 dark:text-green-200 mb-2 text-center">
                                    📅 SCHEDULE
                                </h4>
                                <p class="text-sm text-green-700 dark:text-green-300 mb-3 text-center">Important & Not Urgent</p>
                                <div class="task-drop-zone min-h-[120px] space-y-2" data-zone="important-not-urgent"></div>
                            </div>

                            <!-- Quadrant 3: Not Important & Urgent -->
                            <div class="quadrant bg-yellow-50 dark:bg-yellow-900/20 border-2 border-yellow-200 dark:border-yellow-800 rounded-lg p-4 min-h-[200px]" data-quadrant="3">
                                <h4 class="font-bold text-yellow-800 dark:text-yellow-200 mb-2 text-center">
                                    👥 DELEGATE
                                </h4>
                                <p class="text-sm text-yellow-700 dark:text-yellow-300 mb-3 text-center">Not Important & Urgent</p>
                                <div class="task-drop-zone min-h-[120px] space-y-2" data-zone="urgent-not-important"></div>
                            </div>

                            <!-- Quadrant 4: Not Important & Not Urgent -->
                            <div class="quadrant bg-gray-50 dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-lg p-4 min-h-[200px]" data-quadrant="4">
                                <h4 class="font-bold text-gray-800 dark:text-gray-200 mb-2 text-center">
                                    🗑️ ELIMINATE
                                </h4>
                                <p class="text-sm text-gray-700 dark:text-gray-300 mb-3 text-center">Not Important & Not Urgent</p>
                                <div class="task-drop-zone min-h-[120px] space-y-2" data-zone="not-urgent-not-important"></div>
                            </div>
                        </div>

                        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-4">
                            <h5 class="font-medium text-blue-900 dark:text-blue-100 mb-2">💡 Planning Tips:</h5>
                            <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                                <li>• <strong>DO FIRST:</strong> Crisis situations, deadlines today</li>
                                <li>• <strong>SCHEDULE:</strong> Important goals, prevention, planning</li>
                                <li>• <strong>DELEGATE:</strong> Interruptions, some emails, some calls</li>
                                <li>• <strong>ELIMINATE:</strong> Time wasters, excessive social media, busy work</li>
                            </ul>
                        </div>

                        <div class="text-center">
                            <button id="analyze-matrix" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
                                Analyze My Matrix
                            </button>
                        </div>

                        <div id="matrix-analysis" class="hidden mt-6">
                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border">
                                <h5 class="font-medium text-gray-900 dark:text-white mb-3">Matrix Analysis:</h5>
                                <div id="analysis-content"></div>
                                <div class="text-center mt-4">
                                    <button id="finish-exercise" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                                        Finish Exercise
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <script>
            (function() {
                let tasks = [];
                let taskCounter = 0;
                let matrixData = {
                    'urgent-important': [],
                    'important-not-urgent': [],
                    'urgent-not-important': [],
                    'not-urgent-not-important': []
                };

                const newTaskInput = document.getElementById('new-task-input');
                const addTaskBtn = document.getElementById('add-task-btn');
                const unorganizedTasks = document.getElementById('unorganized-tasks');
                const startOrganizing = document.getElementById('start-organizing');
                const matrixSection = document.getElementById('matrix-section');

                // Add task functionality
                addTaskBtn.addEventListener('click', addTask);
                newTaskInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') addTask();
                });

                function addTask() {
                    const taskText = newTaskInput.value.trim();
                    if (!taskText) return;

                    taskCounter++;
                    const task = {
                        id: taskCounter,
                        text: taskText
                    };

                    tasks.push(task);

                    const taskElement = createTaskElement(task);

                    if (unorganizedTasks.querySelector('p')) {
                        unorganizedTasks.innerHTML = '';
                    }

                    unorganizedTasks.appendChild(taskElement);
                    newTaskInput.value = '';

                    startOrganizing.disabled = false;
                }

                function createTaskElement(task, isDraggable = true) {
                    const taskDiv = document.createElement('div');
                    taskDiv.className = 'task-item bg-white dark:bg-gray-800 p-3 rounded-md border border-gray-200 dark:border-gray-600 cursor-move shadow-sm';
                    taskDiv.draggable = isDraggable;
                    taskDiv.dataset.taskId = task.id;
                    taskDiv.innerHTML = \`
                        <div class="flex items-center justify-between">
                            <span class="text-gray-900 dark:text-white">\${task.text}</span>
                            <button class="remove-task text-red-500 hover:text-red-700 ml-2" data-task-id="\${task.id}">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    \`;

                    if (isDraggable) {
                        taskDiv.addEventListener('dragstart', handleDragStart);
                    }

                    taskDiv.querySelector('.remove-task').addEventListener('click', function() {
                        removeTask(task.id);
                    });

                    return taskDiv;
                }

                function removeTask(taskId) {
                    tasks = tasks.filter(t => t.id !== taskId);
                    document.querySelector(\`[data-task-id="\${taskId}"]\`).remove();

                    if (tasks.length === 0) {
                        unorganizedTasks.innerHTML = '<p class="text-gray-500 dark:text-gray-400 text-center">Add tasks above to get started</p>';
                        startOrganizing.disabled = true;
                    }
                }

                startOrganizing.addEventListener('click', function() {
                    document.getElementById('task-input-section').classList.add('hidden');
                    matrixSection.classList.remove('hidden');
                    setupDragAndDrop();
                });

                function setupDragAndDrop() {
                    const dropZones = document.querySelectorAll('.task-drop-zone');

                    dropZones.forEach(zone => {
                        zone.addEventListener('dragover', handleDragOver);
                        zone.addEventListener('drop', handleDrop);
                    });
                }

                function handleDragStart(e) {
                    e.dataTransfer.setData('text/plain', e.target.dataset.taskId);
                }

                function handleDragOver(e) {
                    e.preventDefault();
                    e.currentTarget.classList.add('bg-blue-100', 'dark:bg-blue-900/30');
                }

                function handleDrop(e) {
                    e.preventDefault();
                    e.currentTarget.classList.remove('bg-blue-100', 'dark:bg-blue-900/30');

                    const taskId = parseInt(e.dataTransfer.getData('text/plain'));
                    const task = tasks.find(t => t.id === taskId);
                    const zone = e.currentTarget.dataset.zone;

                    if (task && zone) {
                        // Remove from previous location
                        Object.keys(matrixData).forEach(key => {
                            matrixData[key] = matrixData[key].filter(t => t.id !== taskId);
                        });

                        // Add to new zone
                        matrixData[zone].push(task);

                        // Update UI
                        const taskElement = createTaskElement(task, false);
                        e.currentTarget.appendChild(taskElement);

                        // Remove from unorganized or previous zone
                        const oldElement = document.querySelector(\`[data-task-id="\${taskId}"]\`);
                        if (oldElement && oldElement !== taskElement) {
                            oldElement.remove();
                        }
                    }
                }

                document.getElementById('analyze-matrix').addEventListener('click', function() {
                    analyzeMatrix();
                });

                function analyzeMatrix() {
                    const analysis = document.getElementById('analysis-content');
                    const totalTasks = Object.values(matrixData).reduce((sum, arr) => sum + arr.length, 0);

                    if (totalTasks === 0) {
                        analysis.innerHTML = '<p class="text-red-600 dark:text-red-400">Please organize at least one task in the matrix first.</p>';
                        document.getElementById('matrix-analysis').classList.remove('hidden');
                        return;
                    }

                    let score = 0;
                    let feedback = [];

                    // Analyze distribution
                    const q1Count = matrixData['urgent-important'].length;
                    const q2Count = matrixData['important-not-urgent'].length;
                    const q3Count = matrixData['urgent-not-important'].length;
                    const q4Count = matrixData['not-urgent-not-important'].length;

                    // Scoring logic
                    if (q2Count > q1Count) {
                        score += 3;
                        feedback.push('✅ Great! You have more planned tasks than crisis tasks.');
                    } else if (q1Count > totalTasks * 0.5) {
                        feedback.push('⚠️ Too many urgent tasks - consider better planning to prevent crises.');
                    }

                    if (q4Count > 0) {
                        feedback.push('💡 Consider eliminating tasks in the "Delete" quadrant to free up time.');
                    }

                    if (q2Count > 0) {
                        score += 2;
                        feedback.push('✅ Excellent! You identified important non-urgent tasks for scheduling.');
                    }

                    if (q3Count > 0) {
                        score += 1;
                        feedback.push('👥 Good identification of tasks that could be delegated.');
                    }

                    score = Math.min(10, Math.max(1, score + Math.floor(totalTasks / 2)));

                    analysis.innerHTML = \`
                        <div class="space-y-3">
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>Do First: \${q1Count} tasks</div>
                                <div>Schedule: \${q2Count} tasks</div>
                                <div>Delegate: \${q3Count} tasks</div>
                                <div>Eliminate: \${q4Count} tasks</div>
                            </div>
                            <div class="border-t pt-3">
                                \${feedback.map(f => \`<p class="mb-1">\${f}</p>\`).join('')}
                            </div>
                        </div>
                    \`;

                    document.getElementById('matrix-analysis').classList.remove('hidden');

                    // Set score
                    document.getElementById('score').value = score;
                    document.getElementById('score-display').textContent = score;
                }

                document.getElementById('finish-exercise').addEventListener('click', function() {
                    window.finishExercise();
                });
            })();
            </script>
        `;
    }

    function createOrganizationExercise() {
        return `
            <div class="organization-exercise">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Digital File Organization</h3>
                    <p class="text-gray-600 dark:text-gray-300">Practice organizing files and folders using ADHD-friendly systems.</p>
                </div>

                <div class="max-w-3xl mx-auto">
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
                        <h4 class="font-medium text-gray-900 dark:text-white mb-3">Scenario: Organize Your Digital Workspace</h4>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            You have a messy collection of files that need to be organized. Drag and drop them into appropriate folders using the PARA method (Projects, Areas, Resources, Archive).
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Unsorted Files -->
                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border">
                            <h5 class="font-medium text-gray-900 dark:text-white mb-3">📁 Unsorted Files</h5>
                            <div id="unsorted-files" class="space-y-2 min-h-[300px] border-2 border-dashed border-gray-300 dark:border-gray-600 rounded p-3">
                                <!-- Files will be populated here -->
                            </div>
                        </div>

                        <!-- Folder Structure -->
                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border">
                            <h5 class="font-medium text-gray-900 dark:text-white mb-3">📂 PARA Folders</h5>
                            <div class="space-y-3">
                                <div class="folder-zone bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded p-3" data-folder="projects">
                                    <h6 class="font-medium text-blue-800 dark:text-blue-200 mb-2">🎯 Projects</h6>
                                    <p class="text-xs text-blue-600 dark:text-blue-300 mb-2">Things with deadlines</p>
                                    <div class="file-drop-zone min-h-[60px] space-y-1"></div>
                                </div>

                                <div class="folder-zone bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded p-3" data-folder="areas">
                                    <h6 class="font-medium text-green-800 dark:text-green-200 mb-2">🏠 Areas</h6>
                                    <p class="text-xs text-green-600 dark:text-green-300 mb-2">Ongoing responsibilities</p>
                                    <div class="file-drop-zone min-h-[60px] space-y-1"></div>
                                </div>

                                <div class="folder-zone bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded p-3" data-folder="resources">
                                    <h6 class="font-medium text-purple-800 dark:text-purple-200 mb-2">📚 Resources</h6>
                                    <p class="text-xs text-purple-600 dark:text-purple-300 mb-2">Future reference</p>
                                    <div class="file-drop-zone min-h-[60px] space-y-1"></div>
                                </div>

                                <div class="folder-zone bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded p-3" data-folder="archive">
                                    <h6 class="font-medium text-gray-800 dark:text-gray-200 mb-2">📦 Archive</h6>
                                    <p class="text-xs text-gray-600 dark:text-gray-300 mb-2">Completed/inactive</p>
                                    <div class="file-drop-zone min-h-[60px] space-y-1"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 text-center">
                        <button id="check-organization" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
                            Check My Organization
                        </button>
                    </div>

                    <div id="organization-feedback" class="hidden mt-6 bg-white dark:bg-gray-800 p-4 rounded-lg border">
                        <h5 class="font-medium text-gray-900 dark:text-white mb-3">Organization Analysis:</h5>
                        <div id="feedback-content"></div>
                        <div class="text-center mt-4">
                            <button id="finish-exercise" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                                Finish Exercise
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <script>
            (function() {
                const sampleFiles = [
                    { name: 'Budget_2024.xlsx', type: 'spreadsheet', correctFolder: 'areas', description: 'Personal finance tracking' },
                    { name: 'Website_Redesign_Mockup.psd', type: 'design', correctFolder: 'projects', description: 'Client project due next month' },
                    { name: 'ADHD_Research_Article.pdf', type: 'document', correctFolder: 'resources', description: 'Research for future reference' },
                    { name: 'Old_Resume_2022.docx', type: 'document', correctFolder: 'archive', description: 'Outdated resume' },
                    { name: 'Grocery_List_Template.docx', type: 'document', correctFolder: 'resources', description: 'Reusable template' },
                    { name: 'Meeting_Notes_ProjectX.txt', type: 'text', correctFolder: 'projects', description: 'Active project notes' },
                    { name: 'Health_Insurance_Info.pdf', type: 'document', correctFolder: 'areas', description: 'Important personal documents' },
                    { name: 'Completed_Tax_Return_2023.pdf', type: 'document', correctFolder: 'archive', description: 'Last year\'s taxes' },
                    { name: 'Workout_Routine.pdf', type: 'document', correctFolder: 'areas', description: 'Personal fitness plan' },
                    { name: 'JavaScript_Cheatsheet.pdf', type: 'document', correctFolder: 'resources', description: 'Programming reference' }
                ];

                let organizationData = {
                    projects: [],
                    areas: [],
                    resources: [],
                    archive: []
                };

                // Initialize files
                function initializeFiles() {
                    const unsortedContainer = document.getElementById('unsorted-files');
                    unsortedContainer.innerHTML = '';

                    sampleFiles.forEach((file, index) => {
                        const fileElement = createFileElement(file, index);
                        unsortedContainer.appendChild(fileElement);
                    });

                    setupDragAndDrop();
                }

                function createFileElement(file, index) {
                    const fileDiv = document.createElement('div');
                    fileDiv.className = 'file-item bg-white dark:bg-gray-700 p-2 rounded border border-gray-200 dark:border-gray-600 cursor-move shadow-sm';
                    fileDiv.draggable = true;
                    fileDiv.dataset.fileIndex = index;

                    const iconMap = {
                        'spreadsheet': '📊',
                        'design': '🎨',
                        'document': '📄',
                        'text': '📝'
                    };

                    fileDiv.innerHTML = \`
                        <div class="flex items-center gap-2">
                            <span class="text-lg">\${iconMap[file.type] || '📄'}</span>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">\${file.name}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 truncate">\${file.description}</p>
                            </div>
                        </div>
                    \`;

                    fileDiv.addEventListener('dragstart', handleDragStart);
                    return fileDiv;
                }

                function setupDragAndDrop() {
                    const dropZones = document.querySelectorAll('.file-drop-zone');

                    dropZones.forEach(zone => {
                        zone.addEventListener('dragover', handleDragOver);
                        zone.addEventListener('drop', handleDrop);
                    });
                }

                function handleDragStart(e) {
                    e.dataTransfer.setData('text/plain', e.target.dataset.fileIndex);
                }

                function handleDragOver(e) {
                    e.preventDefault();
                    e.currentTarget.classList.add('bg-blue-100', 'dark:bg-blue-900/30');
                }

                function handleDrop(e) {
                    e.preventDefault();
                    e.currentTarget.classList.remove('bg-blue-100', 'dark:bg-blue-900/30');

                    const fileIndex = parseInt(e.dataTransfer.getData('text/plain'));
                    const file = sampleFiles[fileIndex];
                    const folder = e.currentTarget.closest('.folder-zone').dataset.folder;

                    if (file && folder) {
                        // Remove from previous location
                        Object.keys(organizationData).forEach(key => {
                            organizationData[key] = organizationData[key].filter(f => f.index !== fileIndex);
                        });

                        // Add to new folder
                        organizationData[folder].push({ ...file, index: fileIndex });

                        // Update UI
                        const fileElement = createFileElement(file, fileIndex);
                        fileElement.draggable = false;
                        fileElement.classList.add('text-xs');
                        e.currentTarget.appendChild(fileElement);

                        // Remove from previous location
                        const oldElement = document.querySelector(\`[data-file-index="\${fileIndex}"]\`);
                        if (oldElement && oldElement !== fileElement) {
                            oldElement.remove();
                        }
                    }
                }

                document.getElementById('check-organization').addEventListener('click', function() {
                    checkOrganization();
                });

                function checkOrganization() {
                    const feedback = document.getElementById('feedback-content');
                    const totalFiles = Object.values(organizationData).reduce((sum, arr) => sum + arr.length, 0);

                    if (totalFiles === 0) {
                        feedback.innerHTML = '<p class="text-red-600 dark:text-red-400">Please organize at least one file first.</p>';
                        document.getElementById('organization-feedback').classList.remove('hidden');
                        return;
                    }

                    let correctPlacements = 0;
                    let feedbackItems = [];

                    Object.keys(organizationData).forEach(folder => {
                        organizationData[folder].forEach(file => {
                            if (file.correctFolder === folder) {
                                correctPlacements++;
                                feedbackItems.push(\`✅ \${file.name} correctly placed in \${folder.toUpperCase()}\`);
                            } else {
                                feedbackItems.push(\`❌ \${file.name} should be in \${file.correctFolder.toUpperCase()}, not \${folder.toUpperCase()}\`);
                            }
                        });
                    });

                    const score = Math.round((correctPlacements / totalFiles) * 10);

                    feedback.innerHTML = \`
                        <div class="space-y-2">
                            <p class="font-medium">Score: \${correctPlacements}/\${totalFiles} correct (\${score}/10)</p>
                            <div class="text-sm space-y-1">
                                \${feedbackItems.map(item => \`<p>\${item}</p>\`).join('')}
                            </div>
                            <div class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
                                <h6 class="font-medium text-blue-900 dark:text-blue-100 mb-2">💡 PARA Method Tips:</h6>
                                <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                                    <li>• <strong>Projects:</strong> Has a deadline and specific outcome</li>
                                    <li>• <strong>Areas:</strong> Ongoing responsibility to maintain</li>
                                    <li>• <strong>Resources:</strong> Topics of ongoing interest</li>
                                    <li>• <strong>Archive:</strong> Inactive items from the other categories</li>
                                </ul>
                            </div>
                        </div>
                    \`;

                    document.getElementById('organization-feedback').classList.remove('hidden');

                    // Set score
                    document.getElementById('score').value = Math.max(1, score);
                    document.getElementById('score-display').textContent = document.getElementById('score').value;
                }

                document.getElementById('finish-exercise').addEventListener('click', function() {
                    window.finishExercise();
                });

                // Initialize the exercise
                initializeFiles();
            })();
            </script>
        `;
    }

    function createTimeManagementExercise() {
        return `
            <div class="time-management-exercise">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Pomodoro Focus Timer</h3>
                    <p class="text-gray-600 dark:text-gray-300">Practice the Pomodoro Technique with built-in ADHD accommodations.</p>
                </div>

                <div class="max-w-2xl mx-auto">
                    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border shadow-sm">
                        <!-- Timer Display -->
                        <div class="text-center mb-6">
                            <div id="timer-display" class="text-6xl font-bold text-blue-600 dark:text-blue-400 mb-2">25:00</div>
                            <div id="timer-status" class="text-lg text-gray-600 dark:text-gray-300">Ready to Focus</div>
                            <div id="session-info" class="text-sm text-gray-500 dark:text-gray-400 mt-2">Session 1 of 4</div>
                        </div>

                        <!-- Timer Controls -->
                        <div class="flex justify-center gap-4 mb-6">
                            <button id="start-timer" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium">
                                <i class="fas fa-play mr-2"></i>Start Focus
                            </button>
                            <button id="pause-timer" class="px-6 py-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors font-medium hidden">
                                <i class="fas fa-pause mr-2"></i>Pause
                            </button>
                            <button id="reset-timer" class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium">
                                <i class="fas fa-refresh mr-2"></i>Reset
                            </button>
                        </div>

                        <!-- Task Input -->
                        <div class="mb-6">
                            <label for="focus-task" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                What will you focus on?
                            </label>
                            <input type="text" id="focus-task" placeholder="Enter your focus task..."
                                   class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
                        </div>

                        <!-- Progress Tracking -->
                        <div class="mb-6">
                            <h4 class="font-medium text-gray-900 dark:text-white mb-3">Session Progress</h4>
                            <div class="grid grid-cols-4 gap-2">
                                <div id="session-1" class="session-indicator h-8 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center text-sm font-medium">1</div>
                                <div id="session-2" class="session-indicator h-8 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center text-sm font-medium">2</div>
                                <div id="session-3" class="session-indicator h-8 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center text-sm font-medium">3</div>
                                <div id="session-4" class="session-indicator h-8 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center text-sm font-medium">4</div>
                            </div>
                        </div>

                        <!-- ADHD Tips -->
                        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-6">
                            <h5 class="font-medium text-blue-900 dark:text-blue-100 mb-2">🧠 ADHD-Friendly Tips:</h5>
                            <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                                <li>• It's okay to take micro-breaks if you need to fidget</li>
                                <li>• If you hyperfocus, the timer will gently remind you to take breaks</li>
                                <li>• Adjust the timer length if 25 minutes feels too long/short</li>
                                <li>• Celebrate completing each session!</li>
                            </ul>
                        </div>

                        <!-- Session Log -->
                        <div id="session-log" class="hidden">
                            <h4 class="font-medium text-gray-900 dark:text-white mb-3">Session Summary</h4>
                            <div id="log-content" class="space-y-2 text-sm"></div>
                            <div class="text-center mt-4">
                                <button id="finish-exercise" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                                    Finish Exercise
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <script>
            (function() {
                let timerInterval;
                let timeLeft = 25 * 60; // 25 minutes in seconds
                let isRunning = false;
                let currentSession = 1;
                let completedSessions = 0;
                let sessionLog = [];
                let sessionStartTime;

                const timerDisplay = document.getElementById('timer-display');
                const timerStatus = document.getElementById('timer-status');
                const sessionInfo = document.getElementById('session-info');
                const startBtn = document.getElementById('start-timer');
                const pauseBtn = document.getElementById('pause-timer');
                const resetBtn = document.getElementById('reset-timer');
                const focusTaskInput = document.getElementById('focus-task');

                function updateDisplay() {
                    const minutes = Math.floor(timeLeft / 60);
                    const seconds = timeLeft % 60;
                    timerDisplay.textContent = \`\${minutes.toString().padStart(2, '0')}:\${seconds.toString().padStart(2, '0')}\`;
                }

                function updateSessionIndicator() {
                    document.querySelectorAll('.session-indicator').forEach((indicator, index) => {
                        indicator.classList.remove('bg-green-500', 'bg-blue-500', 'text-white');
                        indicator.classList.add('bg-gray-200', 'dark:bg-gray-600');

                        if (index < completedSessions) {
                            indicator.classList.remove('bg-gray-200', 'dark:bg-gray-600');
                            indicator.classList.add('bg-green-500', 'text-white');
                        } else if (index === currentSession - 1) {
                            indicator.classList.remove('bg-gray-200', 'dark:bg-gray-600');
                            indicator.classList.add('bg-blue-500', 'text-white');
                        }
                    });
                }

                function startTimer() {
                    if (!isRunning) {
                        isRunning = true;
                        sessionStartTime = new Date();
                        startBtn.classList.add('hidden');
                        pauseBtn.classList.remove('hidden');
                        timerStatus.textContent = 'Focusing...';

                        timerInterval = setInterval(() => {
                            timeLeft--;
                            updateDisplay();

                            if (timeLeft <= 0) {
                                completeSession();
                            }
                        }, 1000);
                    }
                }

                function pauseTimer() {
                    if (isRunning) {
                        isRunning = false;
                        clearInterval(timerInterval);
                        startBtn.classList.remove('hidden');
                        pauseBtn.classList.add('hidden');
                        timerStatus.textContent = 'Paused';
                    }
                }

                function resetTimer() {
                    isRunning = false;
                    clearInterval(timerInterval);
                    timeLeft = 25 * 60;
                    updateDisplay();
                    startBtn.classList.remove('hidden');
                    pauseBtn.classList.add('hidden');
                    timerStatus.textContent = 'Ready to Focus';
                }

                function completeSession() {
                    isRunning = false;
                    clearInterval(timerInterval);

                    const sessionDuration = sessionStartTime ? Math.round((new Date() - sessionStartTime) / 1000 / 60) : 25;
                    const task = focusTaskInput.value.trim() || 'Focus session';

                    sessionLog.push({
                        session: currentSession,
                        task: task,
                        duration: sessionDuration,
                        completed: true
                    });

                    completedSessions++;
                    updateSessionIndicator();

                    if (completedSessions >= 4) {
                        // All sessions completed
                        timerStatus.textContent = 'All Sessions Complete! 🎉';
                        sessionInfo.textContent = 'Great job completing all 4 sessions!';
                        showSessionLog();
                    } else {
                        // Start break
                        currentSession++;
                        sessionInfo.textContent = \`Session \${currentSession} of 4\`;
                        timerStatus.textContent = 'Take a 5-minute break!';

                        // Auto-start break timer
                        timeLeft = 5 * 60; // 5 minute break
                        updateDisplay();

                        setTimeout(() => {
                            timeLeft = 25 * 60; // Reset to 25 minutes for next session
                            updateDisplay();
                            timerStatus.textContent = 'Ready for next session';
                            startBtn.classList.remove('hidden');
                            pauseBtn.classList.add('hidden');
                        }, 5000); // Show break message for 5 seconds
                    }
                }

                function showSessionLog() {
                    const logContent = document.getElementById('log-content');
                    const totalTime = sessionLog.reduce((sum, session) => sum + session.duration, 0);

                    logContent.innerHTML = \`
                        <div class="bg-green-50 dark:bg-green-900/20 p-3 rounded">
                            <p class="font-medium text-green-800 dark:text-green-200">🎉 Congratulations!</p>
                            <p class="text-green-700 dark:text-green-300">You completed \${completedSessions} Pomodoro sessions</p>
                            <p class="text-green-700 dark:text-green-300">Total focus time: \${totalTime} minutes</p>
                        </div>
                        <div class="space-y-1">
                            \${sessionLog.map(session => \`
                                <div class="flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                                    <span>Session \${session.session}: \${session.task}</span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">\${session.duration}min</span>
                                </div>
                            \`).join('')}
                        </div>
                    \`;

                    document.getElementById('session-log').classList.remove('hidden');

                    // Calculate score based on completed sessions
                    const score = Math.min(10, completedSessions * 2.5);
                    document.getElementById('score').value = Math.max(1, Math.round(score));
                    document.getElementById('score-display').textContent = document.getElementById('score').value;
                }

                // Event listeners
                startBtn.addEventListener('click', startTimer);
                pauseBtn.addEventListener('click', pauseTimer);
                resetBtn.addEventListener('click', resetTimer);

                document.getElementById('finish-exercise').addEventListener('click', function() {
                    window.finishExercise();
                });

                // Initialize
                updateDisplay();
                updateSessionIndicator();
            })();
            </script>
        `;
    }

    function createEmotionalRegulationExercise() {
        return `
            <div class="emotional-regulation-exercise">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Emotion Regulation Practice</h3>
                    <p class="text-gray-600 dark:text-gray-300">Practice identifying, understanding, and managing emotions using ADHD-friendly techniques.</p>
                </div>

                <div class="max-w-3xl mx-auto">
                    <!-- Current Emotion Check-in -->
                    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border shadow-sm mb-6">
                        <h4 class="font-medium text-gray-900 dark:text-white mb-4">Step 1: Emotion Check-in</h4>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">How are you feeling right now? Click on the emotions that match your current state:</p>

                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                            <button class="emotion-btn p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" data-emotion="happy">
                                <div class="text-2xl mb-1">😊</div>
                                <div class="text-sm">Happy</div>
                            </button>
                            <button class="emotion-btn p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" data-emotion="sad">
                                <div class="text-2xl mb-1">😢</div>
                                <div class="text-sm">Sad</div>
                            </button>
                            <button class="emotion-btn p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" data-emotion="angry">
                                <div class="text-2xl mb-1">😠</div>
                                <div class="text-sm">Angry</div>
                            </button>
                            <button class="emotion-btn p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" data-emotion="anxious">
                                <div class="text-2xl mb-1">😰</div>
                                <div class="text-sm">Anxious</div>
                            </button>
                            <button class="emotion-btn p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" data-emotion="frustrated">
                                <div class="text-2xl mb-1">😤</div>
                                <div class="text-sm">Frustrated</div>
                            </button>
                            <button class="emotion-btn p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" data-emotion="overwhelmed">
                                <div class="text-2xl mb-1">🤯</div>
                                <div class="text-sm">Overwhelmed</div>
                            </button>
                            <button class="emotion-btn p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" data-emotion="excited">
                                <div class="text-2xl mb-1">🤩</div>
                                <div class="text-sm">Excited</div>
                            </button>
                            <button class="emotion-btn p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" data-emotion="calm">
                                <div class="text-2xl mb-1">😌</div>
                                <div class="text-sm">Calm</div>
                            </button>
                        </div>

                        <div class="mb-4">
                            <label for="emotion-intensity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Intensity Level (1-10):
                            </label>
                            <input type="range" id="emotion-intensity" min="1" max="10" value="5"
                                   class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                            <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 px-1 mt-1">
                                <span>1 (Mild)</span>
                                <span id="intensity-display">5</span>
                                <span>10 (Intense)</span>
                            </div>
                        </div>

                        <button id="continue-to-breathing" class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50" disabled>
                            Continue to Breathing Exercise
                        </button>
                    </div>

                    <!-- Breathing Exercise -->
                    <div id="breathing-section" class="hidden bg-white dark:bg-gray-800 p-6 rounded-lg border shadow-sm mb-6">
                        <h4 class="font-medium text-gray-900 dark:text-white mb-4">Step 2: 4-7-8 Breathing Exercise</h4>
                        <p class="text-gray-600 dark:text-gray-300 mb-6">Follow the breathing pattern to help regulate your emotions:</p>

                        <div class="text-center mb-6">
                            <div id="breathing-circle" class="w-32 h-32 mx-auto rounded-full border-4 border-blue-500 flex items-center justify-center mb-4 transition-all duration-1000">
                                <span id="breathing-text" class="text-lg font-medium text-blue-600 dark:text-blue-400">Ready</span>
                            </div>
                            <div id="breathing-instruction" class="text-lg text-gray-700 dark:text-gray-300 mb-2">Click Start to begin</div>
                            <div id="breathing-count" class="text-sm text-gray-500 dark:text-gray-400">Round 1 of 4</div>
                        </div>

                        <div class="flex justify-center gap-4 mb-6">
                            <button id="start-breathing" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                Start Breathing
                            </button>
                            <button id="stop-breathing" class="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors hidden">
                                Stop
                            </button>
                        </div>

                        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-4">
                            <h5 class="font-medium text-blue-900 dark:text-blue-100 mb-2">🫁 Breathing Pattern:</h5>
                            <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                                <li>• <strong>Inhale</strong> for 4 counts through your nose</li>
                                <li>• <strong>Hold</strong> for 7 counts</li>
                                <li>• <strong>Exhale</strong> for 8 counts through your mouth</li>
                                <li>• Repeat 4 times</li>
                            </ul>
                        </div>

                        <button id="continue-to-reflection" class="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors hidden">
                            Continue to Reflection
                        </button>
                    </div>

                    <!-- Reflection -->
                    <div id="reflection-section" class="hidden bg-white dark:bg-gray-800 p-6 rounded-lg border shadow-sm">
                        <h4 class="font-medium text-gray-900 dark:text-white mb-4">Step 3: Reflection</h4>

                        <div class="space-y-4 mb-6">
                            <div>
                                <label for="emotion-after" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    How do you feel now? (1-10):
                                </label>
                                <input type="range" id="emotion-after" min="1" max="10" value="5"
                                       class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 px-1 mt-1">
                                    <span>1 (Much Worse)</span>
                                    <span id="after-intensity-display">5</span>
                                    <span>10 (Much Better)</span>
                                </div>
                            </div>

                            <div>
                                <label for="what-helped" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    What helped the most?
                                </label>
                                <textarea id="what-helped" rows="3" placeholder="Describe what was most helpful during this exercise..."
                                          class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"></textarea>
                            </div>

                            <div>
                                <label for="coping-strategy" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    What will you try next time you feel this way?
                                </label>
                                <textarea id="coping-strategy" rows="3" placeholder="Write down a strategy you can use in the future..."
                                          class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"></textarea>
                            </div>
                        </div>

                        <div class="text-center">
                            <button id="finish-exercise" class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                                Finish Exercise
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <script>
            (function() {
                let selectedEmotions = [];
                let initialIntensity = 5;
                let breathingInterval;
                let breathingRound = 0;
                let breathingPhase = 'ready'; // ready, inhale, hold, exhale
                let phaseCount = 0;

                // Emotion selection
                document.querySelectorAll('.emotion-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const emotion = this.dataset.emotion;

                        if (selectedEmotions.includes(emotion)) {
                            selectedEmotions = selectedEmotions.filter(e => e !== emotion);
                            this.classList.remove('bg-blue-500', 'text-white');
                        } else {
                            selectedEmotions.push(emotion);
                            this.classList.add('bg-blue-500', 'text-white');
                        }

                        document.getElementById('continue-to-breathing').disabled = selectedEmotions.length === 0;
                    });
                });

                // Intensity slider
                document.getElementById('emotion-intensity').addEventListener('input', function() {
                    initialIntensity = parseInt(this.value);
                    document.getElementById('intensity-display').textContent = this.value;
                });

                document.getElementById('emotion-after').addEventListener('input', function() {
                    document.getElementById('after-intensity-display').textContent = this.value;
                });

                // Continue to breathing
                document.getElementById('continue-to-breathing').addEventListener('click', function() {
                    document.getElementById('breathing-section').classList.remove('hidden');
                    this.closest('.bg-white').classList.add('hidden');
                });

                // Breathing exercise
                document.getElementById('start-breathing').addEventListener('click', startBreathing);
                document.getElementById('stop-breathing').addEventListener('click', stopBreathing);

                function startBreathing() {
                    document.getElementById('start-breathing').classList.add('hidden');
                    document.getElementById('stop-breathing').classList.remove('hidden');

                    breathingRound = 1;
                    startBreathingCycle();
                }

                function startBreathingCycle() {
                    if (breathingRound > 4) {
                        completeBreathing();
                        return;
                    }

                    document.getElementById('breathing-count').textContent = \`Round \${breathingRound} of 4\`;

                    // Inhale phase
                    breathingPhase = 'inhale';
                    phaseCount = 4;
                    updateBreathingDisplay();

                    breathingInterval = setInterval(() => {
                        phaseCount--;
                        updateBreathingDisplay();

                        if (phaseCount <= 0) {
                            clearInterval(breathingInterval);

                            if (breathingPhase === 'inhale') {
                                // Hold phase
                                breathingPhase = 'hold';
                                phaseCount = 7;
                                updateBreathingDisplay();

                                breathingInterval = setInterval(() => {
                                    phaseCount--;
                                    updateBreathingDisplay();

                                    if (phaseCount <= 0) {
                                        clearInterval(breathingInterval);

                                        // Exhale phase
                                        breathingPhase = 'exhale';
                                        phaseCount = 8;
                                        updateBreathingDisplay();

                                        breathingInterval = setInterval(() => {
                                            phaseCount--;
                                            updateBreathingDisplay();

                                            if (phaseCount <= 0) {
                                                clearInterval(breathingInterval);
                                                breathingRound++;
                                                setTimeout(startBreathingCycle, 1000);
                                            }
                                        }, 1000);
                                    }
                                }, 1000);
                            }
                        }
                    }, 1000);
                }

                function updateBreathingDisplay() {
                    const circle = document.getElementById('breathing-circle');
                    const text = document.getElementById('breathing-text');
                    const instruction = document.getElementById('breathing-instruction');

                    switch(breathingPhase) {
                        case 'inhale':
                            circle.style.transform = 'scale(1.3)';
                            circle.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
                            text.textContent = phaseCount;
                            instruction.textContent = 'Breathe in through your nose...';
                            break;
                        case 'hold':
                            circle.style.transform = 'scale(1.3)';
                            circle.style.backgroundColor = 'rgba(168, 85, 247, 0.1)';
                            text.textContent = phaseCount;
                            instruction.textContent = 'Hold your breath...';
                            break;
                        case 'exhale':
                            circle.style.transform = 'scale(1)';
                            circle.style.backgroundColor = 'rgba(34, 197, 94, 0.1)';
                            text.textContent = phaseCount;
                            instruction.textContent = 'Breathe out through your mouth...';
                            break;
                    }
                }

                function stopBreathing() {
                    clearInterval(breathingInterval);
                    document.getElementById('start-breathing').classList.remove('hidden');
                    document.getElementById('stop-breathing').classList.add('hidden');
                    document.getElementById('breathing-text').textContent = 'Ready';
                    document.getElementById('breathing-instruction').textContent = 'Click Start to begin';
                    document.getElementById('breathing-circle').style.transform = 'scale(1)';
                    document.getElementById('breathing-circle').style.backgroundColor = 'transparent';
                }

                function completeBreathing() {
                    stopBreathing();
                    document.getElementById('continue-to-reflection').classList.remove('hidden');
                    document.getElementById('breathing-instruction').textContent = 'Great job! You completed 4 rounds.';
                    document.getElementById('breathing-text').textContent = '✅';
                }

                document.getElementById('continue-to-reflection').addEventListener('click', function() {
                    document.getElementById('reflection-section').classList.remove('hidden');
                    this.closest('.bg-white').classList.add('hidden');
                });

                document.getElementById('finish-exercise').addEventListener('click', function() {
                    // Calculate score based on improvement and completion
                    const afterIntensity = parseInt(document.getElementById('emotion-after').value);
                    const improvement = Math.max(0, afterIntensity - 5); // 5 is neutral
                    const completionBonus = breathingRound > 4 ? 3 : 0;
                    const score = Math.min(10, Math.max(1, 5 + improvement + completionBonus));

                    document.getElementById('score').value = score;
                    document.getElementById('score-display').textContent = score;

                    window.finishExercise();
                });
            })();
            </script>
        `;
    }
});
</script>
