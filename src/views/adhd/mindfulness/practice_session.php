<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/adhd/mindfulness" class="inline-flex items-center text-sm text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 mr-4">
                <i class="fas fa-arrow-left mr-1"></i> Back to Mindfulness
            </a>
        </div>
        
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:p-6">
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                    Practice: <?= View::escape($exercise['name']) ?>
                </h1>
                
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                        <?= ucfirst(str_replace('_', ' ', $exercise['category'])) ?>
                    </span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                        <?= $exercise['duration_minutes'] ?> minutes
                    </span>
                </div>
                
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Instructions</h2>
                    <p class="text-gray-600 dark:text-gray-300"><?= nl2br(View::escape($exercise['instructions'])) ?></p>
                </div>
                
                <!-- Timer Section -->
                <div class="text-center mb-8">
                    <div class="text-6xl font-bold text-gray-900 dark:text-white mb-4" id="timer">
                        <?= str_pad($exercise['duration_minutes'], 2, '0', STR_PAD_LEFT) ?>:00
                    </div>
                    
                    <div class="flex justify-center gap-4">
                        <button id="startBtn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-play mr-1"></i> Start
                        </button>
                        <button id="pauseBtn" class="hidden inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200">
                            <i class="fas fa-pause mr-1"></i> Pause
                        </button>
                        <button id="resetBtn" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-redo mr-1"></i> Reset
                        </button>
                    </div>
                </div>
                
                <!-- Log Practice Form -->
                <div id="logForm" class="hidden">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Log Your Practice</h2>
                    
                    <form action="/momentum/adhd/mindfulness/log-practice" method="POST">
                        <input type="hidden" name="exercise_id" value="<?= $exercise['id'] ?>">
                        <input type="hidden" name="duration_minutes" id="duration_minutes" value="<?= $exercise['duration_minutes'] ?>">
                        
                        <div class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="mood_before" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        How did you feel before the practice?
                                    </label>
                                    <select id="mood_before" name="mood_before" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                                        <option value="very_poor">Very Poor</option>
                                        <option value="poor">Poor</option>
                                        <option value="neutral" selected>Neutral</option>
                                        <option value="good">Good</option>
                                        <option value="very_good">Very Good</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="mood_after" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        How do you feel after the practice?
                                    </label>
                                    <select id="mood_after" name="mood_after" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                                        <option value="very_poor">Very Poor</option>
                                        <option value="poor">Poor</option>
                                        <option value="neutral">Neutral</option>
                                        <option value="good" selected>Good</option>
                                        <option value="very_good">Very Good</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div>
                                <label for="focus_improvement" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Rate your focus improvement (1-10)
                                </label>
                                <div class="mt-1">
                                    <input type="range" id="focus_improvement" name="focus_improvement" min="1" max="10" value="5" class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 px-1">
                                        <span>1</span>
                                        <span>2</span>
                                        <span>3</span>
                                        <span>4</span>
                                        <span>5</span>
                                        <span>6</span>
                                        <span>7</span>
                                        <span>8</span>
                                        <span>9</span>
                                        <span>10</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Notes (optional)
                                </label>
                                <div class="mt-1">
                                    <textarea id="notes" name="notes" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="How was your experience? Any challenges or insights?"></textarea>
                                </div>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                    <i class="fas fa-save mr-1"></i> Save Practice Log
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const startBtn = document.getElementById('startBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    const resetBtn = document.getElementById('resetBtn');
    const timerDisplay = document.getElementById('timer');
    const logForm = document.getElementById('logForm');
    const durationInput = document.getElementById('duration_minutes');
    
    let totalSeconds = <?= $exercise['duration_minutes'] * 60 ?>;
    let timerInterval;
    let isRunning = false;
    let actualDuration = 0;
    
    function updateTimerDisplay() {
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        timerDisplay.textContent = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    }
    
    function startTimer() {
        if (!isRunning) {
            isRunning = true;
            startBtn.classList.add('hidden');
            pauseBtn.classList.remove('hidden');
            
            timerInterval = setInterval(function() {
                if (totalSeconds > 0) {
                    totalSeconds--;
                    actualDuration++;
                    updateTimerDisplay();
                } else {
                    clearInterval(timerInterval);
                    completeTimer();
                }
            }, 1000);
        }
    }
    
    function pauseTimer() {
        if (isRunning) {
            isRunning = false;
            clearInterval(timerInterval);
            startBtn.classList.remove('hidden');
            pauseBtn.classList.add('hidden');
        }
    }
    
    function resetTimer() {
        pauseTimer();
        totalSeconds = <?= $exercise['duration_minutes'] * 60 ?>;
        actualDuration = 0;
        updateTimerDisplay();
        logForm.classList.add('hidden');
    }
    
    function completeTimer() {
        pauseTimer();
        // Show completion message
        timerDisplay.textContent = "Complete!";
        timerDisplay.classList.add('text-green-600', 'dark:text-green-400');
        
        // Update actual duration
        const minutesPracticed = Math.ceil(actualDuration / 60);
        durationInput.value = minutesPracticed;
        
        // Show log form
        logForm.classList.remove('hidden');
        
        // Scroll to log form
        logForm.scrollIntoView({ behavior: 'smooth' });
    }
    
    startBtn.addEventListener('click', startTimer);
    pauseBtn.addEventListener('click', pauseTimer);
    resetBtn.addEventListener('click', resetTimer);
    
    // Initialize timer display
    updateTimerDisplay();
});
</script>
