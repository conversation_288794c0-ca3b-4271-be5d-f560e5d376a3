<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/passive-income" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
                <span class="ml-1">Back to Passive Income</span>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Add New Passive Income Stream</h1>
        </div>

        <!-- Form Card -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <form action="/momentum/passive-income/store" method="post" class="p-6">
                <!-- Basic Information -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="name" id="name" value="<?= $data['name'] ?? '' ?>" required
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            <?php if (isset($errors['name'])): ?>
                                <p class="mt-1 text-sm text-red-600 dark:text-red-500"><?= $errors['name'] ?></p>
                            <?php endif; ?>
                        </div>

                        <!-- Category -->
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Category <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="category" id="category" value="<?= $data['category'] ?? '' ?>" required
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                                placeholder="e.g., Digital Products, Investments, Content Creation">
                            <?php if (isset($errors['category'])): ?>
                                <p class="mt-1 text-sm text-red-600 dark:text-red-500"><?= $errors['category'] ?></p>
                            <?php endif; ?>
                        </div>

                        <!-- Platform -->
                        <div>
                            <label for="platform" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Platform
                            </label>
                            <input type="text" name="platform" id="platform" value="<?= $data['platform'] ?? '' ?>"
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                                placeholder="e.g., Etsy, YouTube, Stock Market">
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Status <span class="text-red-500">*</span>
                            </label>
                            <select name="status" id="status" required
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                <option value="setup" <?= isset($data['status']) && $data['status'] === 'setup' ? 'selected' : '' ?>>Setup</option>
                                <option value="growing" <?= isset($data['status']) && $data['status'] === 'growing' ? 'selected' : '' ?>>Growing</option>
                                <option value="stable" <?= isset($data['status']) && $data['status'] === 'stable' ? 'selected' : '' ?>>Stable</option>
                                <option value="declining" <?= isset($data['status']) && $data['status'] === 'declining' ? 'selected' : '' ?>>Declining</option>
                                <option value="inactive" <?= isset($data['status']) && $data['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                            <?php if (isset($errors['status'])): ?>
                                <p class="mt-1 text-sm text-red-600 dark:text-red-500"><?= $errors['status'] ?></p>
                            <?php endif; ?>
                        </div>

                        <!-- Setup Date -->
                        <div>
                            <label for="setup_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Setup Date
                            </label>
                            <input type="date" name="setup_date" id="setup_date" value="<?= $data['setup_date'] ?? '' ?>"
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>

                        <!-- Related Opportunity -->
                        <div>
                            <label for="opportunity_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Related Opportunity
                            </label>
                            <select name="opportunity_id" id="opportunity_id"
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                <option value="">None</option>
                                <?php if (!empty($opportunities)): ?>
                                    <?php foreach ($opportunities as $opportunity): ?>
                                        <option value="<?= $opportunity['id'] ?>" <?= isset($data['opportunity_id']) && $data['opportunity_id'] == $opportunity['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($opportunity['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Financial Details -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Financial Details</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Initial Investment -->
                        <div>
                            <label for="initial_investment" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Initial Investment (Rs)
                            </label>
                            <input type="number" name="initial_investment" id="initial_investment" value="<?= $data['initial_investment'] ?? '' ?>" step="0.01" min="0"
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>

                        <!-- Maintenance Hours Per Month -->
                        <div>
                            <label for="maintenance_hours_per_month" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Maintenance Hours Per Month
                            </label>
                            <input type="number" name="maintenance_hours_per_month" id="maintenance_hours_per_month" value="<?= $data['maintenance_hours_per_month'] ?? '' ?>" step="0.1" min="0"
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Description
                    </label>
                    <textarea name="description" id="description" rows="3"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"><?= $data['description'] ?? '' ?></textarea>
                </div>

                <!-- Notes -->
                <div class="mb-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Notes
                    </label>
                    <textarea name="notes" id="notes" rows="3"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"><?= $data['notes'] ?? '' ?></textarea>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <a href="/momentum/passive-income" class="mr-3 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i> Save Stream
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
