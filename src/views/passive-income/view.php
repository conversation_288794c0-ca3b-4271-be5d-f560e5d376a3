<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/passive-income" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <?= htmlspecialchars($stream['name']) ?>
            </h1>
            <div class="ml-auto flex space-x-2">
                <a href="/momentum/passive-income/edit/<?= $stream['id'] ?>" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-edit mr-1.5"></i> Edit
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Column -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Stream Details -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Stream Details</h3>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
                            <div class="sm:col-span-2">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                    <?= !empty($stream['description']) ? nl2br(htmlspecialchars($stream['description'])) : 'No description provided' ?>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Category</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                    <?= htmlspecialchars($stream['category']) ?>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Platform</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                    <?= !empty($stream['platform']) ? htmlspecialchars($stream['platform']) : 'N/A' ?>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                                <dd class="mt-1 text-sm">
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                        <?php if ($stream['status'] === 'setup'): ?>
                                            bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300
                                        <?php elseif ($stream['status'] === 'growing'): ?>
                                            bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300
                                        <?php elseif ($stream['status'] === 'stable'): ?>
                                            bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300
                                        <?php elseif ($stream['status'] === 'declining'): ?>
                                            bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-300
                                        <?php else: ?>
                                            bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300
                                        <?php endif; ?>
                                    ">
                                        <?= ucfirst(htmlspecialchars($stream['status'])) ?>
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Setup Date</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                    <?= !empty($stream['setup_date']) ? date('M j, Y', strtotime($stream['setup_date'])) : 'N/A' ?>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Initial Investment</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                    <?= !empty($stream['initial_investment']) ? 'Rs ' . number_format($stream['initial_investment'], 2) : 'N/A' ?>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Monthly Maintenance</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                    <?= !empty($stream['maintenance_hours_per_month']) ? number_format($stream['maintenance_hours_per_month'], 1) . ' hours' : 'N/A' ?>
                                </dd>
                            </div>
                            <?php if (!empty($stream['opportunity_name'])): ?>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Related Opportunity</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                        <a href="/momentum/income-opportunities/view/<?= $stream['opportunity_id'] ?>" class="text-primary-600 dark:text-primary-400 hover:underline">
                                            <?= htmlspecialchars($stream['opportunity_name']) ?>
                                        </a>
                                    </dd>
                                </div>
                            <?php endif; ?>
                            <div class="sm:col-span-2">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Notes</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                    <?= !empty($stream['notes']) ? nl2br(htmlspecialchars($stream['notes'])) : 'No notes provided' ?>
                                </dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Earnings History -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Earnings History</h3>
                        <button type="button" onclick="openEarningModal()" class="inline-flex items-center px-3 py-1 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-1.5"></i> Add Earning
                        </button>
                    </div>
                    <div class="p-4">
                        <?php if (empty($earnings)): ?>
                            <div class="text-center py-6">
                                <p class="text-gray-500 dark:text-gray-400 mb-4">No earnings recorded yet</p>
                                <button type="button" onclick="openEarningModal()" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                                    <i class="fas fa-plus mr-2"></i> Record First Earning
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Amount</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Notes</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        <?php foreach ($earnings as $earning): ?>
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                                    <?= date('M j, Y', strtotime($earning['earning_date'])) ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600 dark:text-green-400">
                                                    Rs <?= number_format($earning['amount'], 2) ?>
                                                </td>
                                                <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                                                    <?= !empty($earning['notes']) ? htmlspecialchars($earning['notes']) : '-' ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Maintenance Records -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Maintenance Records</h3>
                        <button type="button" onclick="openMaintenanceModal()" class="inline-flex items-center px-3 py-1 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-1.5"></i> Add Record
                        </button>
                    </div>
                    <div class="p-4">
                        <?php if (empty($maintenanceRecords)): ?>
                            <div class="text-center py-6">
                                <p class="text-gray-500 dark:text-gray-400 mb-4">No maintenance records yet</p>
                                <button type="button" onclick="openMaintenanceModal()" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200">
                                    <i class="fas fa-plus mr-2"></i> Add First Record
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Hours</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Tasks</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Next Date</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        <?php foreach ($maintenanceRecords as $record): ?>
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                                    <?= date('M j, Y', strtotime($record['maintenance_date'])) ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                                    <?= number_format($record['hours_spent'], 1) ?>
                                                </td>
                                                <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                                                    <?= htmlspecialchars($record['tasks_performed']) ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                                    <?= !empty($record['next_maintenance_date']) ? date('M j, Y', strtotime($record['next_maintenance_date'])) : '-' ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Metrics -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Performance Metrics</h3>
                    </div>
                    <div class="p-6">
                        <dl class="grid grid-cols-1 gap-x-4 gap-y-6">
                            <div class="sm:col-span-1">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Earnings</dt>
                                <dd class="mt-1 text-2xl font-semibold text-green-600 dark:text-green-400">
                                    Rs <?= number_format($totalEarnings, 2) ?>
                                </dd>
                            </div>
                            <?php if (!empty($stream['initial_investment']) && $stream['initial_investment'] > 0): ?>
                                <div class="sm:col-span-1">
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">ROI</dt>
                                    <dd class="mt-1 text-2xl font-semibold text-blue-600 dark:text-blue-400">
                                        <?= number_format($roi, 2) ?>%
                                    </dd>
                                </div>
                            <?php endif; ?>
                        </dl>
                    </div>
                </div>

                <!-- Earnings Chart -->
                <?php if (!empty($monthlyEarnings)): ?>
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mt-6">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Monthly Earnings</h3>
                    </div>
                    <div class="p-6">
                        <canvas id="earningsChart" height="250"></canvas>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add Earning Modal -->
    <div id="earningModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Add Earning</h3>
                <button type="button" onclick="closeEarningModal()" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="earningForm" action="/momentum/passive-income/add-earning/<?= $stream['id'] ?>" method="post">
                <div class="p-6 space-y-4">
                    <div>
                        <label for="earning_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Date <span class="text-red-500">*</span>
                        </label>
                        <input type="date" name="earning_date" id="earning_date" required
                            class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                    </div>
                    <div>
                        <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Amount (Rs) <span class="text-red-500">*</span>
                        </label>
                        <input type="number" name="amount" id="amount" step="0.01" min="0" required
                            class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                    </div>
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Notes
                        </label>
                        <textarea name="notes" id="notes" rows="3"
                            class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"></textarea>
                    </div>
                </div>
                <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-right sm:px-6 border-t border-gray-200 dark:border-gray-600">
                    <button type="button" onclick="closeEarningModal()" class="inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 mr-2">
                        Cancel
                    </button>
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Save
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Maintenance Modal -->
    <div id="maintenanceModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Add Maintenance Record</h3>
                <button type="button" onclick="closeMaintenanceModal()" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="maintenanceForm" action="/momentum/passive-income/add-maintenance/<?= $stream['id'] ?>" method="post">
                <div class="p-6 space-y-4">
                    <div>
                        <label for="maintenance_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Date <span class="text-red-500">*</span>
                        </label>
                        <input type="date" name="maintenance_date" id="maintenance_date" required
                            class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                    </div>
                    <div>
                        <label for="hours_spent" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Hours Spent <span class="text-red-500">*</span>
                        </label>
                        <input type="number" name="hours_spent" id="hours_spent" step="0.1" min="0" required
                            class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                    </div>
                    <div>
                        <label for="tasks_performed" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Tasks Performed <span class="text-red-500">*</span>
                        </label>
                        <textarea name="tasks_performed" id="tasks_performed" rows="3" required
                            class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"></textarea>
                    </div>
                    <div>
                        <label for="next_maintenance_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Next Maintenance Date
                        </label>
                        <input type="date" name="next_maintenance_date" id="next_maintenance_date"
                            class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                    </div>
                </div>
                <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-right sm:px-6 border-t border-gray-200 dark:border-gray-600">
                    <button type="button" onclick="closeMaintenanceModal()" class="inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 mr-2">
                        Cancel
                    </button>
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Save
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Modal functions
        function openEarningModal() {
            document.getElementById('earningModal').classList.remove('hidden');
            document.getElementById('earning_date').value = new Date().toISOString().split('T')[0];
        }

        function closeEarningModal() {
            document.getElementById('earningModal').classList.add('hidden');
        }

        function openMaintenanceModal() {
            document.getElementById('maintenanceModal').classList.remove('hidden');
            document.getElementById('maintenance_date').value = new Date().toISOString().split('T')[0];
        }

        function closeMaintenanceModal() {
            document.getElementById('maintenanceModal').classList.add('hidden');
        }

        // Initialize earnings chart
        <?php if (!empty($monthlyEarnings)): ?>
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('earningsChart').getContext('2d');

            // Extract data from PHP
            const months = <?= json_encode(array_column($monthlyEarnings, 'month_name')) ?>;
            const earnings = <?= json_encode(array_column($monthlyEarnings, 'total')) ?>;

            const chart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: months,
                    datasets: [{
                        label: 'Monthly Earnings (Rs)',
                        data: earnings,
                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                        borderColor: 'rgba(16, 185, 129, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return 'Rs ' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'Rs ' + context.raw.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        });
        <?php endif; ?>
    </script>
</div>
