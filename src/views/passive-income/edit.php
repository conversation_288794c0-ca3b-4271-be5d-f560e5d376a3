<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/passive-income/view/<?= $stream['id'] ?>" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
                <span class="ml-1">Back to Stream</span>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Edit Passive Income Stream</h1>
        </div>

        <!-- Form Card -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <form action="/momentum/passive-income/update/<?= $stream['id'] ?>" method="post" class="p-6">
                <!-- Basic Information -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="name" id="name" value="<?= $stream['name'] ?? '' ?>" required
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            <?php if (isset($errors['name'])): ?>
                                <p class="mt-1 text-sm text-red-600 dark:text-red-500"><?= $errors['name'] ?></p>
                            <?php endif; ?>
                        </div>

                        <!-- Category -->
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Category <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="category" id="category" value="<?= $stream['category'] ?? '' ?>" required
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                                placeholder="e.g., Digital Products, Investments, Content Creation">
                            <?php if (isset($errors['category'])): ?>
                                <p class="mt-1 text-sm text-red-600 dark:text-red-500"><?= $errors['category'] ?></p>
                            <?php endif; ?>
                        </div>

                        <!-- Platform -->
                        <div>
                            <label for="platform" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Platform
                            </label>
                            <input type="text" name="platform" id="platform" value="<?= $stream['platform'] ?? '' ?>"
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                                placeholder="e.g., Etsy, YouTube, Stock Market">
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Status <span class="text-red-500">*</span>
                            </label>
                            <select name="status" id="status" required
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                <option value="setup" <?= $stream['status'] === 'setup' ? 'selected' : '' ?>>Setup</option>
                                <option value="growing" <?= $stream['status'] === 'growing' ? 'selected' : '' ?>>Growing</option>
                                <option value="stable" <?= $stream['status'] === 'stable' ? 'selected' : '' ?>>Stable</option>
                                <option value="declining" <?= $stream['status'] === 'declining' ? 'selected' : '' ?>>Declining</option>
                                <option value="inactive" <?= $stream['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                            <?php if (isset($errors['status'])): ?>
                                <p class="mt-1 text-sm text-red-600 dark:text-red-500"><?= $errors['status'] ?></p>
                            <?php endif; ?>
                        </div>

                        <!-- Setup Date -->
                        <div>
                            <label for="setup_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Setup Date
                            </label>
                            <input type="date" name="setup_date" id="setup_date" value="<?= $stream['setup_date'] ?? '' ?>"
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>

                        <!-- Related Opportunity -->
                        <div>
                            <label for="opportunity_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Related Opportunity
                            </label>
                            <select name="opportunity_id" id="opportunity_id"
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                <option value="">None</option>
                                <?php if (!empty($opportunities)): ?>
                                    <?php foreach ($opportunities as $opportunity): ?>
                                        <option value="<?= $opportunity['id'] ?>" <?= $stream['opportunity_id'] == $opportunity['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($opportunity['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Financial Details -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Financial Details</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Initial Investment -->
                        <div>
                            <label for="initial_investment" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Initial Investment (Rs)
                            </label>
                            <input type="number" name="initial_investment" id="initial_investment" value="<?= $stream['initial_investment'] ?? '' ?>" step="0.01" min="0"
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>

                        <!-- Maintenance Hours Per Month -->
                        <div>
                            <label for="maintenance_hours_per_month" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Maintenance Hours Per Month
                            </label>
                            <input type="number" name="maintenance_hours_per_month" id="maintenance_hours_per_month" value="<?= $stream['maintenance_hours_per_month'] ?? '' ?>" step="0.1" min="0"
                                class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Description
                    </label>
                    <textarea name="description" id="description" rows="3"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"><?= $stream['description'] ?? '' ?></textarea>
                </div>

                <!-- Notes -->
                <div class="mb-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Notes
                    </label>
                    <textarea name="notes" id="notes" rows="3"
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"><?= $stream['notes'] ?? '' ?></textarea>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-between">
                    <div>
                        <button type="button" onclick="confirmDelete()" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                            <i class="fas fa-trash mr-2"></i> Delete Stream
                        </button>
                    </div>
                    <div>
                        <a href="/momentum/passive-income/view/<?= $stream['id'] ?>" class="mr-3 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            Cancel
                        </a>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-save mr-2"></i> Save Changes
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Confirm Deletion</h3>
            </div>
            <div class="p-6">
                <p class="text-gray-700 dark:text-gray-300 mb-4">Are you sure you want to delete this passive income stream? This action cannot be undone.</p>
                <div class="flex justify-end">
                    <button type="button" onclick="closeDeleteModal()" class="inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 mr-2">
                        Cancel
                    </button>
                    <form action="/momentum/passive-income/delete/<?= $stream['id'] ?>" method="post">
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            Delete
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function confirmDelete() {
            document.getElementById('delete-modal').classList.remove('hidden');
        }

        function closeDeleteModal() {
            document.getElementById('delete-modal').classList.add('hidden');
        }
    </script>
</div>
