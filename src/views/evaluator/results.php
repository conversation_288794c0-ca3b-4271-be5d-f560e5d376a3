<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/income-evaluator" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
                <span class="ml-1">Back to Evaluator</span>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Results: <?= htmlspecialchars($comparison['name']) ?></h1>
            <div class="ml-auto">
                <a href="/momentum/income-evaluator/compare/<?= $comparison['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-edit mr-2"></i> Edit Evaluation
                </a>
            </div>
        </div>

        <?php if (empty($scores)): ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 text-center">
                <p class="text-gray-500 dark:text-gray-400 mb-4">No evaluation scores found. Please complete the evaluation first.</p>
                <a href="/momentum/income-evaluator/compare/<?= $comparison['id'] ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-star-half-alt mr-2"></i> Start Evaluation
                </a>
            </div>
        <?php else: ?>
            <!-- Results Summary -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Evaluation Summary</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                        Opportunities ranked by weighted score
                    </p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Rank</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Opportunity</th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Weighted Score</th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Raw Score</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php
                                // Sort opportunities by rank
                                usort($comparison['opportunities'], function($a, $b) {
                                    return ($a['ranking'] ?? 999) - ($b['ranking'] ?? 999);
                                });

                                $rank = 1;
                                foreach ($comparison['opportunities'] as $opportunity):
                                    $opportunityId = $opportunity['opportunity_id'];
                                    if (!isset($scores[$opportunityId])) continue;
                            ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-lg font-bold
                                            <?php if ($rank === 1): ?>
                                                text-yellow-500
                                            <?php elseif ($rank === 2): ?>
                                                text-gray-400
                                            <?php elseif ($rank === 3): ?>
                                                text-amber-600
                                            <?php else: ?>
                                                text-gray-700 dark:text-gray-300
                                            <?php endif; ?>
                                        ">
                                            <?= $rank ?>
                                            <?php if ($rank === 1): ?>
                                                <i class="fas fa-trophy ml-1"></i>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($opportunity['name']) ?></div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            <?= ucfirst($opportunity['income_type']) ?> •
                                            <?= ucfirst($opportunity['status']) ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-center">
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white">
                                            <?= number_format($scores[$opportunityId]['weighted_score'], 1) ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-center">
                                        <div class="text-sm text-gray-700 dark:text-gray-300">
                                            <?= number_format($scores[$opportunityId]['total_score'], 1) ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="/momentum/income-opportunities/view/<?= $opportunityId ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300">
                                            View Details
                                        </a>
                                    </td>
                                </tr>
                            <?php
                                $rank++;
                                endforeach;
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Detailed Scores -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Detailed Scores</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <?php foreach ($criteriaByType as $type => $typeCriteria): ?>
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                    <h4 class="font-medium text-gray-900 dark:text-white"><?= ucfirst($type) ?> Criteria</h4>
                                </div>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                        <thead class="bg-gray-50 dark:bg-gray-700">
                                            <tr>
                                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Criteria</th>
                                                <?php
                                                    // Sort opportunities by rank for consistent display
                                                    usort($comparison['opportunities'], function($a, $b) {
                                                        return ($a['ranking'] ?? 999) - ($b['ranking'] ?? 999);
                                                    });

                                                    foreach ($comparison['opportunities'] as $opportunity):
                                                        $opportunityId = $opportunity['opportunity_id'];
                                                        if (!isset($scores[$opportunityId])) continue;
                                                ?>
                                                    <th scope="col" class="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                        <?= htmlspecialchars($opportunity['name']) ?>
                                                    </th>
                                                <?php endforeach; ?>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                            <?php foreach ($typeCriteria as $criteria): ?>
                                                <tr>
                                                    <td class="px-4 py-3 whitespace-nowrap">
                                                        <div class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($criteria['name']) ?></div>
                                                        <div class="text-xs text-gray-500 dark:text-gray-400">Weight: <?= $criteria['weight'] ?></div>
                                                    </td>

                                                    <?php foreach ($comparison['opportunities'] as $opportunity):
                                                        $opportunityId = $opportunity['opportunity_id'];
                                                        if (!isset($scores[$opportunityId])) continue;

                                                        $criteriaId = $criteria['id'];
                                                        $score = isset($scores[$opportunityId]['criteria_scores'][$criteriaId])
                                                            ? $scores[$opportunityId]['criteria_scores'][$criteriaId]['score']
                                                            : '-';
                                                    ?>
                                                        <td class="px-4 py-3 text-center">
                                                            <div class="text-sm font-medium
                                                                <?php if ($score >= 9): ?>
                                                                    text-green-600 dark:text-green-400
                                                                <?php elseif ($score >= 7): ?>
                                                                    text-blue-600 dark:text-blue-400
                                                                <?php elseif ($score >= 4): ?>
                                                                    text-yellow-600 dark:text-yellow-400
                                                                <?php elseif ($score > 0): ?>
                                                                    text-red-600 dark:text-red-400
                                                                <?php else: ?>
                                                                    text-gray-500 dark:text-gray-400
                                                                <?php endif; ?>
                                                            ">
                                                                <?= $score ?>
                                                            </div>
                                                        </td>
                                                    <?php endforeach; ?>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Recommendations -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Recommendations</h3>
                </div>
                <div class="p-6">
                    <?php
                        // Get top opportunity
                        usort($comparison['opportunities'], function($a, $b) {
                            return ($a['ranking'] ?? 999) - ($b['ranking'] ?? 999);
                        });

                        $topOpportunity = null;
                        foreach ($comparison['opportunities'] as $opportunity) {
                            $opportunityId = $opportunity['opportunity_id'];
                            if (isset($scores[$opportunityId])) {
                                $topOpportunity = $opportunity;
                                break;
                            }
                        }

                        if ($topOpportunity):
                    ?>
                        <div class="mb-6">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Top Recommendation</h4>
                            <div class="bg-green-50 dark:bg-green-900 dark:bg-opacity-20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-trophy text-yellow-500 text-xl"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                            <?= htmlspecialchars($topOpportunity['name']) ?>
                                        </h3>
                                        <div class="mt-2 text-sm text-gray-700 dark:text-gray-300">
                                            <p>Based on your evaluation, <strong><?= htmlspecialchars($topOpportunity['name']) ?></strong> is your best income opportunity with a weighted score of <strong><?= number_format($scores[$topOpportunity['opportunity_id']]['weighted_score'], 1) ?></strong>.</p>
                                            <p class="mt-2">This <?= strtolower($topOpportunity['income_type']) ?> income opportunity scored particularly well in:</p>
                                            <ul class="list-disc pl-5 mt-1 space-y-1">
                                                <?php
                                                    // Find top 3 criteria scores
                                                    $criteriaScores = [];
                                                    foreach ($scores[$topOpportunity['opportunity_id']]['criteria_scores'] as $criteriaId => $criteriaScore) {
                                                        $criteriaScores[] = [
                                                            'name' => $criteriaScore['criteria_name'],
                                                            'score' => $criteriaScore['score'],
                                                            'type' => $criteriaScore['criteria_type']
                                                        ];
                                                    }

                                                    usort($criteriaScores, function($a, $b) {
                                                        return $b['score'] - $a['score'];
                                                    });

                                                    $topCriteria = array_slice($criteriaScores, 0, 3);

                                                    foreach ($topCriteria as $criteria):
                                                ?>
                                                    <li><strong><?= htmlspecialchars($criteria['name']) ?></strong> (<?= $criteria['score'] ?>/10)</li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div>
                        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Next Steps</h4>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                                </div>
                                <p class="ml-2 text-gray-700 dark:text-gray-300">
                                    <strong>Review the detailed scores</strong> to understand the strengths and weaknesses of each opportunity.
                                </p>
                            </li>
                            <li class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                                </div>
                                <p class="ml-2 text-gray-700 dark:text-gray-300">
                                    <strong>Create an action plan</strong> for your top-ranked opportunity, focusing on the next steps to implement or grow it.
                                </p>
                            </li>
                            <li class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                                </div>
                                <p class="ml-2 text-gray-700 dark:text-gray-300">
                                    <strong>Consider a portfolio approach</strong> by implementing multiple opportunities that complement each other, especially if they scored similarly.
                                </p>
                            </li>
                            <li class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                                </div>
                                <p class="ml-2 text-gray-700 dark:text-gray-300">
                                    <strong>Re-evaluate periodically</strong> as your skills, available time, and financial situation change.
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Add any JavaScript for charts or interactive elements here
</script>
