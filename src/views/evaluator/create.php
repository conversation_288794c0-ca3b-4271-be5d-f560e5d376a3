<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/income-evaluator" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
                <span class="ml-1">Back to Evaluator</span>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Create New Comparison</h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Comparison Details</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                    Select opportunities to compare and evaluate
                </p>
            </div>
            <div class="p-6">
                <form action="/momentum/income-evaluator/store" method="post">
                    <div class="space-y-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Comparison Name</label>
                            <input type="text" name="name" id="name" value="<?= isset($data['name']) ? htmlspecialchars($data['name']) : '' ?>" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="e.g., Freelance vs Passive Income">
                            <?php if (isset($errors['name'])): ?>
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?= $errors['name'] ?></p>
                            <?php endif; ?>
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description (Optional)</label>
                            <textarea name="description" id="description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="Brief description of what you're comparing..."><?= isset($data['description']) ? htmlspecialchars($data['description']) : '' ?></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select Opportunities to Compare</label>
                            
                            <?php if (empty($opportunities)): ?>
                                <div class="text-center py-4 border border-dashed border-gray-300 dark:border-gray-600 rounded-md">
                                    <p class="text-gray-500 dark:text-gray-400 mb-2">No income opportunities found</p>
                                    <a href="/momentum/income-opportunities/create" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                        <i class="fas fa-plus mr-1.5"></i> Add Opportunity
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <?php foreach ($opportunities as $opportunity): ?>
                                        <div class="relative flex items-start">
                                            <div class="flex items-center h-5">
                                                <input id="opportunity_<?= $opportunity['id'] ?>" name="opportunity_ids[]" value="<?= $opportunity['id'] ?>" type="checkbox" class="h-4 w-4 text-primary-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded focus:ring-primary-500"
                                                    <?php if (isset($data['opportunity_ids']) && (is_array($data['opportunity_ids']) && in_array($opportunity['id'], $data['opportunity_ids']))): ?>
                                                        checked
                                                    <?php endif; ?>>
                                            </div>
                                            <div class="ml-3 text-sm">
                                                <label for="opportunity_<?= $opportunity['id'] ?>" class="font-medium text-gray-700 dark:text-gray-300"><?= htmlspecialchars($opportunity['name']) ?></label>
                                                <p class="text-gray-500 dark:text-gray-400">
                                                    <?= ucfirst($opportunity['income_type']) ?> • 
                                                    <?= ucfirst($opportunity['status']) ?>
                                                </p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php if (isset($errors['opportunity_ids'])): ?>
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?= $errors['opportunity_ids'] ?></p>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>

                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Evaluation Criteria</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                The following criteria will be used to evaluate your opportunities:
                            </p>
                            
                            <div class="space-y-4">
                                <?php foreach ($criteriaByType as $type => $typeCriteria): ?>
                                    <div>
                                        <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"><?= ucfirst($type) ?> Criteria</h5>
                                        <ul class="list-disc pl-5 text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                            <?php foreach ($typeCriteria as $criteria): ?>
                                                <li><?= htmlspecialchars($criteria['name']) ?> (Weight: <?= $criteria['weight'] ?>)</li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3">
                            <a href="/momentum/income-evaluator" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                Cancel
                            </a>
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <i class="fas fa-balance-scale mr-2"></i> Create Comparison
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
