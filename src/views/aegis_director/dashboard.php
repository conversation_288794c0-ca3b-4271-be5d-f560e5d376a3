<?php
/**
 * Aegis Director Dashboard View
 */
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aegis Director - Executive Functioning Partner</title>
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="/momentum/public/js/aegis-director.js" defer></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center">
                <div class="bg-indigo-600 text-white p-3 rounded-full mr-4">
                    <i class="fas fa-shield-alt text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Aegis Director</h1>
                    <p class="text-gray-600">Your Executive Functioning Partner</p>
                </div>
            </div>
            <div class="flex items-center">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 mr-2">
                    <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Active
                </span>
                <button id="open-project-modal" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md shadow-sm text-sm font-medium mr-2">
                    <i class="fas fa-plus mr-1"></i> New Project
                </button>
                <a href="/momentum/ai-agents/view/<?= $agent['id'] ?>" class="text-indigo-600 hover:text-indigo-800 ml-2">
                    <i class="fas fa-cog"></i> Agent Settings
                </a>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if (isset($_SESSION['flash_message'])): ?>
            <div class="bg-<?= $_SESSION['flash_type'] ?? 'green' ?>-100 border-l-4 border-<?= $_SESSION['flash_type'] ?? 'green' ?>-500 text-<?= $_SESSION['flash_type'] ?? 'green' ?>-700 p-4 mb-6" role="alert">
                <p><?= $_SESSION['flash_message'] ?></p>
            </div>
            <?php unset($_SESSION['flash_message'], $_SESSION['flash_type']); ?>
        <?php endif; ?>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Column: Projects and Tasks -->
            <div class="lg:col-span-1">
                <!-- Projects Section -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">Managed Projects</h2>
                        <div>
                            <button id="open-project-modal" class="text-indigo-600 hover:text-indigo-800 mr-2">
                                <i class="fas fa-plus"></i> New
                            </button>
                            <a href="/momentum/brigades/templates" class="text-indigo-600 hover:text-indigo-800 mr-2">
                                <i class="fas fa-layer-group"></i> Brigades
                            </a>
                            <a href="/momentum/checklists/templates" class="text-indigo-600 hover:text-indigo-800">
                                <i class="fas fa-check-square"></i> Checklists
                            </a>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <?php if (empty($projects)): ?>
                            <p class="text-gray-500 italic">No projects yet. Create one to get started.</p>
                        <?php else: ?>
                            <?php foreach ($projects as $project): ?>
                                <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex justify-between items-start">
                                        <h3 class="font-medium text-gray-900"><?= htmlspecialchars($project['project_name']) ?></h3>
                                        <span class="text-xs font-medium uppercase
                                            <?php
                                            switch ($project['project_status']) {
                                                case 'completed': echo 'text-green-600'; break;
                                                case 'in_progress': echo 'text-blue-600'; break;
                                                case 'planning': echo 'text-purple-600'; break;
                                                default: echo 'text-gray-600';
                                            }
                                            ?>">
                                            <?= ucfirst($project['project_status']) ?>
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1"><?= htmlspecialchars(substr($project['project_description'], 0, 100)) ?><?= strlen($project['project_description']) > 100 ? '...' : '' ?></p>
                                    <div class="mt-3 flex justify-between items-center">
                                        <div class="text-xs text-gray-500">
                                            <span class="inline-block mr-2">
                                                <i class="fas fa-chart-pie mr-1"></i> <?= $project['project_progress'] ?>%
                                            </span>
                                            <?php if ($project['brigade_type']): ?>
                                                <span class="inline-block">
                                                    <i class="fas fa-users mr-1"></i> <?= ucfirst(str_replace('_', ' ', $project['brigade_type'])) ?> Brigade
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <a href="/momentum/projects/view/<?= $project['project_id'] ?>" class="text-xs text-indigo-600 hover:text-indigo-800">View</a>
                                            <a href="/momentum/aegis-director/report/<?= $project['project_id'] ?>" class="text-xs text-indigo-600 hover:text-indigo-800 ml-2">Report</a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Tasks Section -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">Current Tasks</h2>
                        <a href="/momentum/ai-agents/tasks/create?agent_id=<?= $agent['id'] ?>" class="text-indigo-600 hover:text-indigo-800">
                            <i class="fas fa-plus"></i> New Task
                        </a>
                    </div>

                    <div class="space-y-4">
                        <?php if (empty($tasks)): ?>
                            <p class="text-gray-500 italic">No tasks assigned yet.</p>
                        <?php else: ?>
                            <?php foreach ($tasks as $task): ?>
                                <div class="task-card bg-white border rounded-lg shadow-sm p-4 priority-<?= $task['priority'] ?> status-<?= $task['status'] ?>">
                                    <div class="flex justify-between items-start">
                                        <h3 class="font-medium text-gray-900"><?= htmlspecialchars($task['title']) ?></h3>
                                        <span class="text-xs font-medium uppercase
                                            <?php
                                            switch ($task['status']) {
                                                case 'pending': echo 'text-gray-600'; break;
                                                case 'in_progress': echo 'text-blue-600'; break;
                                                case 'completed': echo 'text-green-600'; break;
                                                case 'failed': echo 'text-red-600'; break;
                                                default: echo 'text-gray-600';
                                            }
                                            ?>">
                                            <?= str_replace('_', ' ', $task['status']) ?>
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1"><?= htmlspecialchars(substr($task['description'], 0, 100)) ?><?= strlen($task['description']) > 100 ? '...' : '' ?></p>
                                    <div class="flex justify-between items-center mt-3">
                                        <span class="text-xs text-gray-500">
                                            <?php if ($task['due_date']): ?>
                                                <i class="far fa-calendar-alt mr-1"></i> Due: <?= date('M j, Y', strtotime($task['due_date'])) ?>
                                            <?php endif; ?>
                                        </span>
                                        <div>
                                            <a href="/momentum/ai-agents/tasks/view/<?= $task['id'] ?>" class="text-xs text-indigo-600 hover:text-indigo-800">View</a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Right Column: Chat Interface -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Interact with Aegis Director</h2>

                    <!-- Chat Container -->
                    <div class="chat-container border rounded-lg p-4 mb-4" id="chat-container" style="height: 60vh; overflow-y: auto;">
                        <?php if (empty($interactions)): ?>
                            <div class="system-message p-3 mb-4 text-sm bg-yellow-50 rounded-md">
                                <p>Welcome to Aegis Director. I am your executive functioning partner designed to help you achieve rapid, tangible results on your projects and goals.</p>
                                <p class="mt-2">I can help you with:</p>
                                <ul class="list-disc list-inside mt-1 ml-2">
                                    <li>Creating and managing projects</li>
                                    <li>Implementing the 24-hour rapid implementation plan</li>
                                    <li>Building your AI Agent Army with specialized brigades</li>
                                    <li>Breaking down complex goals into actionable tasks</li>
                                    <li>Maintaining focus and accountability</li>
                                </ul>
                                <p class="mt-2">To begin, please tell me what you'd like to work on.</p>
                            </div>
                        <?php else: ?>
                            <?php foreach (array_reverse($interactions) as $interaction): ?>
                                <?php if ($interaction['interaction_type'] !== 'system'): ?>
                                    <!-- User Message -->
                                    <div class="user-message p-3 mb-4 ml-8 bg-blue-50 rounded-lg">
                                        <div class="flex items-start">
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-gray-900">You</p>
                                                <p class="text-sm text-gray-800"><?= nl2br(htmlspecialchars($interaction['content'])) ?></p>
                                                <p class="text-xs text-gray-500 mt-1"><?= date('M j, g:i a', strtotime($interaction['created_at'])) ?></p>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if (!empty($interaction['response'])): ?>
                                        <!-- Agent Response -->
                                        <div class="agent-message p-3 mb-4 mr-8 bg-gray-50 rounded-lg">
                                            <div class="flex items-start">
                                                <div class="flex-1">
                                                    <p class="text-sm font-medium text-gray-900">Aegis Director</p>
                                                    <p class="text-sm text-gray-800"><?= nl2br(htmlspecialchars($interaction['response'])) ?></p>
                                                    <p class="text-xs text-gray-500 mt-1"><?= date('M j, g:i a', strtotime($interaction['created_at'])) ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <!-- System Message -->
                                    <div class="system-message p-3 mb-4 bg-yellow-50 rounded-md">
                                        <p class="text-sm text-gray-800"><?= nl2br(htmlspecialchars($interaction['content'])) ?></p>
                                        <?php if (!empty($interaction['response'])): ?>
                                            <div class="mt-2 p-2 bg-white rounded">
                                                <p class="text-sm text-gray-800"><?= nl2br(htmlspecialchars($interaction['response'])) ?></p>
                                            </div>
                                        <?php endif; ?>
                                        <p class="text-xs text-gray-500 mt-1"><?= date('M j, g:i a', strtotime($interaction['created_at'])) ?></p>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                    <!-- Input Form -->
                    <form action="/momentum/aegis-director/interact" method="POST" class="mt-4" id="chat-form">
                        <div class="flex items-center">
                            <select name="interaction_type" class="mr-2 rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="command">Command</option>
                                <option value="query">Question</option>
                                <option value="feedback">Feedback</option>
                            </select>
                            <input type="text" name="interaction_content" placeholder="Type your message here..." required
                                class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <button type="submit" class="ml-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Send <i class="fas fa-paper-plane ml-2"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- AI Agent Army Section -->
                <div class="bg-white rounded-lg shadow-md p-6 mt-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">AI Agent Army</h2>
                        <div>
                            <a href="/momentum/brigades/templates" class="text-indigo-600 hover:text-indigo-800 mr-2">
                                <i class="fas fa-layer-group"></i> Manage Brigades
                            </a>
                            <a href="/momentum/checklists/templates?category=implementation" class="text-indigo-600 hover:text-indigo-800 mr-2">
                                <i class="fas fa-check-square"></i> Implementation Checklists
                            </a>
                            <?php if (class_exists('YouTubeAgentController')): ?>
                            <a href="/momentum/youtube-agent" class="text-indigo-600 hover:text-indigo-800">
                                <i class="fab fa-youtube"></i> YouTube Agent
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <a href="/momentum/brigades/create-project?type=content_creation" class="border rounded-lg p-4 hover:bg-gray-50 flex items-center">
                            <div class="bg-blue-100 text-blue-800 p-3 rounded-full mr-3">
                                <i class="fas fa-pen-fancy"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Content Creation Brigade</h3>
                                <p class="text-sm text-gray-500">Generate high-quality content at scale</p>
                            </div>
                        </a>

                        <a href="/momentum/brigades/create-project?type=lead_generation" class="border rounded-lg p-4 hover:bg-gray-50 flex items-center">
                            <div class="bg-green-100 text-green-800 p-3 rounded-full mr-3">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Lead Generation Brigade</h3>
                                <p class="text-sm text-gray-500">Identify and engage potential clients</p>
                            </div>
                        </a>

                        <a href="/momentum/brigades/create-project?type=customer_support" class="border rounded-lg p-4 hover:bg-gray-50 flex items-center">
                            <div class="bg-purple-100 text-purple-800 p-3 rounded-full mr-3">
                                <i class="fas fa-headset"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Customer Support Brigade</h3>
                                <p class="text-sm text-gray-500">Provide automated customer support</p>
                            </div>
                        </a>

                        <a href="/momentum/brigades/create-project?type=data_analysis" class="border rounded-lg p-4 hover:bg-gray-50 flex items-center">
                            <div class="bg-yellow-100 text-yellow-800 p-3 rounded-full mr-3">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Data Analysis Brigade</h3>
                                <p class="text-sm text-gray-500">Transform data into actionable insights</p>
                            </div>
                        </a>
                    </div>

                    <!-- Implementation Checklists Section -->
                    <div class="mt-6 border-t pt-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-3">Implementation Checklists</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <a href="/momentum/checklists/create?template_id=1" class="border rounded-lg p-3 hover:bg-gray-50 flex items-center">
                                <div class="bg-indigo-100 text-indigo-800 p-2 rounded-full mr-3">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">Master Implementation Checklist</h4>
                                    <p class="text-sm text-gray-500">Complete guide for implementing your AI Agent Army</p>
                                </div>
                            </a>

                            <a href="/momentum/checklists/create?template_id=2" class="border rounded-lg p-3 hover:bg-gray-50 flex items-center">
                                <div class="bg-green-100 text-green-800 p-2 rounded-full mr-3">
                                    <i class="fas fa-check-double"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">Daily Operation Checklist</h4>
                                    <p class="text-sm text-gray-500">Day-to-day management of your AI Agent Army</p>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- YouTube Agent Section -->
                    <?php if (class_exists('YouTubeAgentController')): ?>
                    <div class="mt-6 border-t pt-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-3">YouTube Agent</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <a href="/momentum/youtube-agent/search" class="border rounded-lg p-3 hover:bg-gray-50 flex items-center">
                                <div class="bg-red-100 text-red-800 p-2 rounded-full mr-3">
                                    <i class="fas fa-search"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">Search YouTube</h4>
                                    <p class="text-sm text-gray-500">Find videos with money-making opportunities</p>
                                </div>
                            </a>

                            <a href="/momentum/youtube-agent/money-making-strategies" class="border rounded-lg p-3 hover:bg-gray-50 flex items-center">
                                <div class="bg-green-100 text-green-800 p-2 rounded-full mr-3">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">Money-Making Strategies</h4>
                                    <p class="text-sm text-gray-500">View analyzed strategies ready for implementation</p>
                                </div>
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Project Creation Modal -->
    <div id="project-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Create New Project</h3>
                <button id="close-project-modal" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="create-project-form" action="/momentum/aegis-director/create-project" method="POST">
                <div class="mb-4">
                    <label for="project-name" class="block text-sm font-medium text-gray-700 mb-1">Project Name</label>
                    <input type="text" id="project-name" name="project_name" required
                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                </div>

                <div class="mb-4">
                    <label for="project-description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea id="project-description" name="project_description" rows="3"
                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"></textarea>
                </div>

                <div class="mb-4">
                    <label for="deadline" class="block text-sm font-medium text-gray-700 mb-1">Deadline</label>
                    <input type="date" id="deadline" name="deadline" required
                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        value="<?= date('Y-m-d', strtotime('+1 week')) ?>">
                </div>

                <div class="mb-4">
                    <label for="project-type" class="block text-sm font-medium text-gray-700 mb-1">Project Type</label>
                    <select id="project-type" name="project_type" required
                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <option value="standard">Standard Project</option>
                        <option value="rapid">24-Hour Rapid Implementation</option>
                        <option value="agent_army">AI Agent Army Brigade</option>
                    </select>
                </div>

                <div id="brigade-type-container" class="mb-4 hidden">
                    <label for="brigade-type" class="block text-sm font-medium text-gray-700 mb-1">Brigade Type</label>
                    <select id="brigade-type" name="brigade_type"
                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <option value="content_creation">Content Creation Brigade</option>
                        <option value="lead_generation">Lead Generation Brigade</option>
                        <option value="customer_support">Customer Support Brigade</option>
                        <option value="data_analysis">Data Analysis Brigade</option>
                    </select>
                </div>

                <div class="flex justify-end mt-6">
                    <button type="button" id="close-project-modal-btn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md shadow-sm text-sm font-medium mr-2">
                        Cancel
                    </button>
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md shadow-sm text-sm font-medium">
                        Create Project
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
