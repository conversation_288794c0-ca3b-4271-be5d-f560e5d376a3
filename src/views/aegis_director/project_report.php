<?php
/**
 * Aegis Director Project Report View
 */
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Report: <?= htmlspecialchars($project['name']) ?> - Aegis Director</title>
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center">
                <a href="/momentum/aegis-director" class="text-indigo-600 hover:text-indigo-800 mr-4">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="bg-indigo-600 text-white p-3 rounded-full mr-4">
                    <i class="fas fa-chart-line text-xl"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Project Report</h1>
                    <p class="text-gray-600"><?= htmlspecialchars($project['name']) ?></p>
                </div>
            </div>
            <div>
                <a href="/momentum/projects/view/<?= $project['id'] ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-external-link-alt mr-2"></i> View Project
                </a>
            </div>
        </div>
        
        <!-- Report Content -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="prose max-w-none">
                <?php
                // Convert markdown to HTML
                $reportHtml = nl2br(htmlspecialchars($report));
                
                // Convert headers
                $reportHtml = preg_replace('/^# (.*?)$/m', '<h1 class="text-2xl font-bold mb-4">$1</h1>', $reportHtml);
                $reportHtml = preg_replace('/^## (.*?)$/m', '<h2 class="text-xl font-semibold mt-6 mb-3">$1</h2>', $reportHtml);
                $reportHtml = preg_replace('/^### (.*?)$/m', '<h3 class="text-lg font-medium mt-4 mb-2">$1</h3>', $reportHtml);
                
                // Convert lists
                $reportHtml = preg_replace('/^- \*\*(.*?):\*\* (.*?)$/m', '<div class="flex items-baseline mb-2"><span class="font-semibold w-32">$1:</span><span>$2</span></div>', $reportHtml);
                $reportHtml = preg_replace('/^- (.*?)$/m', '<li class="ml-6 mb-1">$1</li>', $reportHtml);
                $reportHtml = preg_replace('/^(\d+)\. \*\*(.*?)\*\* \((.*?)\)$/m', '<div class="flex items-baseline mb-2"><span class="mr-2">$1.</span><span class="font-semibold">$2</span><span class="ml-2 text-sm text-gray-600">($3)</span></div>', $reportHtml);
                
                // Convert bold and italic
                $reportHtml = preg_replace('/\*\*(.*?)\*\*/m', '<strong>$1</strong>', $reportHtml);
                $reportHtml = preg_replace('/\*(.*?)\*/m', '<em>$1</em>', $reportHtml);
                
                echo $reportHtml;
                ?>
            </div>
            
            <div class="mt-8 border-t pt-6">
                <h3 class="text-lg font-medium mb-4">Actions</h3>
                
                <div class="flex space-x-4">
                    <a href="/momentum/projects/view/<?= $project['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-tasks mr-2"></i> View Tasks
                    </a>
                    
                    <?php if ($project['status'] !== 'completed'): ?>
                        <form action="/momentum/aegis-director/create-plan/<?= $project['id'] ?>" method="POST" id="create-plan-form">
                            <button type="submit" id="create-plan-button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <i class="fas fa-calendar-alt mr-2"></i> Create 24-Hour Plan
                            </button>
                        </form>
                    <?php endif; ?>
                    
                    <a href="/momentum/aegis-director" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-comment-alt mr-2"></i> Discuss with Aegis
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
