<?php
/**
 * Aegis Director Create Project View
 */
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Project - Aegis Director</title>
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="/momentum/public/js/aegis-director.js" defer></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center mb-8">
            <a href="/momentum/aegis-director" class="text-indigo-600 hover:text-indigo-800 mr-4">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div class="bg-indigo-600 text-white p-3 rounded-full mr-4">
                <i class="fas fa-plus text-xl"></i>
            </div>
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Create New Project</h1>
                <p class="text-gray-600">Let Aegis Director help you manage your project</p>
            </div>
        </div>
        
        <!-- Flash Messages -->
        <?php if (isset($_SESSION['flash_message'])): ?>
            <div class="bg-<?= $_SESSION['flash_type'] ?? 'green' ?>-100 border-l-4 border-<?= $_SESSION['flash_type'] ?? 'green' ?>-500 text-<?= $_SESSION['flash_type'] ?? 'green' ?>-700 p-4 mb-6" role="alert">
                <p><?= $_SESSION['flash_message'] ?></p>
            </div>
            <?php unset($_SESSION['flash_message'], $_SESSION['flash_type']); ?>
        <?php endif; ?>
        
        <!-- Project Creation Form -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <form id="create-project-form" action="/momentum/aegis-director/create-project" method="POST">
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <label for="project-name" class="block text-sm font-medium text-gray-700 mb-1">Project Name</label>
                        <input type="text" id="project-name" name="project_name" required
                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                    </div>
                    
                    <div>
                        <label for="project-description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea id="project-description" name="project_description" rows="4"
                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"></textarea>
                    </div>
                    
                    <div>
                        <label for="deadline" class="block text-sm font-medium text-gray-700 mb-1">Deadline</label>
                        <input type="date" id="deadline" name="deadline" required
                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            value="<?= date('Y-m-d', strtotime('+1 week')) ?>">
                    </div>
                    
                    <div>
                        <label for="project-type" class="block text-sm font-medium text-gray-700 mb-1">Project Type</label>
                        <select id="project-type" name="project_type" required
                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <option value="standard">Standard Project</option>
                            <option value="rapid">24-Hour Rapid Implementation</option>
                            <option value="agent_army">AI Agent Army Brigade</option>
                        </select>
                        <p class="mt-1 text-sm text-gray-500">
                            <span class="project-type-description" data-type="standard">A regular project with standard task management and tracking.</span>
                            <span class="project-type-description hidden" data-type="rapid">A focused project designed to be completed in 24 hours with hourly milestones.</span>
                            <span class="project-type-description hidden" data-type="agent_army">An AI Agent Army brigade implementation project with specialized agent roles.</span>
                        </p>
                    </div>
                    
                    <div id="brigade-type-container" class="hidden">
                        <label for="brigade-type" class="block text-sm font-medium text-gray-700 mb-1">Brigade Type</label>
                        <select id="brigade-type" name="brigade_type"
                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <?php foreach ($brigadeTypes as $value => $label): ?>
                                <option value="<?= $value ?>"><?= $label ?></option>
                            <?php endforeach; ?>
                        </select>
                        <p class="mt-1 text-sm text-gray-500">
                            <span class="brigade-type-description" data-type="content_creation">Generate high-quality, SEO-optimized content at scale for businesses.</span>
                            <span class="brigade-type-description hidden" data-type="lead_generation">Identify and engage potential clients through personalized outreach.</span>
                            <span class="brigade-type-description hidden" data-type="customer_support">Provide 24/7 automated customer support across multiple channels.</span>
                            <span class="brigade-type-description hidden" data-type="data_analysis">Transform raw data into actionable business insights.</span>
                        </p>
                    </div>
                </div>
                
                <div class="mt-8 flex justify-end">
                    <a href="/momentum/aegis-director" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-4">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Create Project
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Project Type Information -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <div class="bg-blue-100 text-blue-600 p-2 rounded-full mr-3">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900">Standard Project</h3>
                </div>
                <p class="text-gray-600 mb-4">A traditional project with flexible timeline and standard task management features.</p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                        <span>Flexible timeline and scope</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                        <span>Standard task management</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                        <span>Progress tracking and reporting</span>
                    </li>
                </ul>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <div class="bg-green-100 text-green-600 p-2 rounded-full mr-3">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900">24-Hour Rapid Implementation</h3>
                </div>
                <p class="text-gray-600 mb-4">A focused project designed to be completed in 24 hours with hourly milestones.</p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                        <span>Hour-by-hour implementation plan</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                        <span>Focus on minimum viable deliverables</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                        <span>Intensive accountability and monitoring</span>
                    </li>
                </ul>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <div class="bg-purple-100 text-purple-600 p-2 rounded-full mr-3">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900">AI Agent Army Brigade</h3>
                </div>
                <p class="text-gray-600 mb-4">Implement a specialized AI Agent brigade for specific business functions.</p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                        <span>Specialized agent roles and assignments</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                        <span>Brigade-specific implementation tasks</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                        <span>Integration with existing systems</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Project type description toggling
            const projectTypeSelect = document.getElementById('project-type');
            const projectTypeDescriptions = document.querySelectorAll('.project-type-description');
            
            if (projectTypeSelect) {
                projectTypeSelect.addEventListener('change', function() {
                    const selectedType = this.value;
                    
                    // Hide all descriptions
                    projectTypeDescriptions.forEach(desc => {
                        desc.classList.add('hidden');
                    });
                    
                    // Show selected description
                    const selectedDesc = document.querySelector(`.project-type-description[data-type="${selectedType}"]`);
                    if (selectedDesc) {
                        selectedDesc.classList.remove('hidden');
                    }
                });
            }
            
            // Brigade type description toggling
            const brigadeTypeSelect = document.getElementById('brigade-type');
            const brigadeTypeDescriptions = document.querySelectorAll('.brigade-type-description');
            
            if (brigadeTypeSelect) {
                brigadeTypeSelect.addEventListener('change', function() {
                    const selectedType = this.value;
                    
                    // Hide all descriptions
                    brigadeTypeDescriptions.forEach(desc => {
                        desc.classList.add('hidden');
                    });
                    
                    // Show selected description
                    const selectedDesc = document.querySelector(`.brigade-type-description[data-type="${selectedType}"]`);
                    if (selectedDesc) {
                        selectedDesc.classList.remove('hidden');
                    }
                });
            }
        });
    </script>
</body>
</html>
