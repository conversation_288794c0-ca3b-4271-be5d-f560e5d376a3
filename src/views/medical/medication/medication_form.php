<?php
/**
 * Medical Medication Form View
 */
?>

<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-pills text-primary-600 dark:text-primary-400 mr-2"></i> Add New Medication
            </h1>
            <a href="/momentum/medical/medication" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-1"></i> Back
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    Medication Information
                </h2>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Enter the details of your medication below or <a href="/momentum/adhd/medication/reference" class="text-primary-600 dark:text-primary-400 hover:underline">browse our medication reference</a> for common medications.
                </p>
            </div>
            <form action="/momentum/medical/medication/save" method="POST" class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 gap-6">
                    <!-- Medication Search -->
                    <div>
                        <label for="medication_search" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Search Medications
                        </label>
                        <div class="mt-1 relative rounded-md shadow-sm">
                            <input type="text" id="medication_search" placeholder="Type to search common medications..."
                                class="focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            <div id="medication_suggestions" class="absolute z-10 w-full bg-white dark:bg-gray-700 mt-1 rounded-md shadow-lg hidden"></div>
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Search by generic name or brand name to auto-fill medication details
                        </p>
                    </div>

                    <!-- Medication Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Medication Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" required
                            class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                    </div>

                    <!-- Dosage -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="dosage" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Dosage <span class="text-red-500">*</span>
                            </label>
                            <input type="number" name="dosage" id="dosage" step="0.01" required
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                        <div>
                            <label for="dosage_unit" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Unit <span class="text-red-500">*</span>
                            </label>
                            <select name="dosage_unit" id="dosage_unit" required
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                <option value="mg">mg (milligrams)</option>
                                <option value="mcg">mcg (micrograms)</option>
                                <option value="g">g (grams)</option>
                                <option value="ml">ml (milliliters)</option>
                                <option value="tablet">tablet(s)</option>
                                <option value="capsule">capsule(s)</option>
                                <option value="patch">patch(es)</option>
                                <option value="spray">spray(s)</option>
                                <option value="drop">drop(s)</option>
                                <option value="unit">unit(s)</option>
                            </select>
                        </div>
                    </div>

                    <!-- Frequency -->
                    <div>
                        <label for="frequency" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Frequency <span class="text-red-500">*</span>
                        </label>
                        <select name="frequency" id="frequency" required
                            class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            <option value="daily">Daily</option>
                            <option value="twice_daily">Twice Daily</option>
                            <option value="three_times_daily">Three Times Daily</option>
                            <option value="four_times_daily">Four Times Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="as_needed">As Needed</option>
                            <option value="custom">Custom</option>
                        </select>
                    </div>

                    <!-- Instructions -->
                    <div>
                        <label for="instructions" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Instructions
                        </label>
                        <textarea name="instructions" id="instructions" rows="3"
                            class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                            placeholder="E.g., Take with food, Take before bedtime"></textarea>
                    </div>

                    <!-- Prescriber and Pharmacy -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="prescriber" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Prescriber
                            </label>
                            <input type="text" name="prescriber" id="prescriber"
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                        <div>
                            <label for="pharmacy" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Pharmacy
                            </label>
                            <input type="text" name="pharmacy" id="pharmacy"
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                    </div>

                    <!-- Start and End Dates -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Start Date
                            </label>
                            <input type="date" name="start_date" id="start_date" value="<?= date('Y-m-d') ?>"
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                End Date (if applicable)
                            </label>
                            <input type="date" name="end_date" id="end_date"
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Notes
                        </label>
                        <textarea name="notes" id="notes" rows="3"
                            class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                            placeholder="Any additional notes about this medication"></textarea>
                    </div>
                </div>

                <div class="mt-6 flex justify-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i> Save Medication
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('medication_search');
    const suggestionsContainer = document.getElementById('medication_suggestions');
    const nameInput = document.getElementById('name');
    const dosageInput = document.getElementById('dosage');
    const dosageUnitSelect = document.getElementById('dosage_unit');
    const frequencySelect = document.getElementById('frequency');
    const instructionsTextarea = document.getElementById('instructions');

    let selectedMedication = null;
    let debounceTimer;

    // Handle search input
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.trim();

        // Clear previous timer
        clearTimeout(debounceTimer);

        if (searchTerm.length < 2) {
            suggestionsContainer.innerHTML = '';
            suggestionsContainer.classList.add('hidden');
            return;
        }

        // Debounce the API call
        debounceTimer = setTimeout(function() {
            // Fetch medication suggestions
            fetch(`/momentum/adhd/medication/suggestions?term=${encodeURIComponent(searchTerm)}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => response.json())
                .then(data => {
                    suggestionsContainer.innerHTML = '';

                    if (data.length === 0) {
                        suggestionsContainer.classList.add('hidden');
                        return;
                    }

                    data.forEach(medication => {
                        const div = document.createElement('div');
                        div.className = 'p-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer';
                        div.innerHTML = `
                            <div class="font-medium">${medication.generic_name}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                ${medication.brand_names}
                            </div>
                        `;

                        div.addEventListener('click', function() {
                            selectMedication(medication);
                        });

                        suggestionsContainer.appendChild(div);
                    });

                    suggestionsContainer.classList.remove('hidden');
                })
                .catch(error => {
                    console.error('Error fetching medication suggestions:', error);
                });
        }, 300);
    });

    // Handle clicking outside the suggestions
    document.addEventListener('click', function(event) {
        if (!searchInput.contains(event.target) && !suggestionsContainer.contains(event.target)) {
            suggestionsContainer.classList.add('hidden');
        }
    });

    // Function to select a medication and fill the form
    function selectMedication(medication) {
        selectedMedication = medication;

        // Fill the form fields
        nameInput.value = medication.generic_name;

        // Parse typical dosages to extract a default value
        const dosageMatch = medication.typical_dosages.match(/(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?) mg/);
        if (dosageMatch && dosageMatch[1]) {
            dosageInput.value = dosageMatch[1];
            dosageUnitSelect.value = 'mg';
        }

        // Set frequency based on common patterns in the description
        if (medication.typical_dosages.toLowerCase().includes('daily')) {
            frequencySelect.value = 'daily';
        } else if (medication.typical_dosages.toLowerCase().includes('as needed')) {
            frequencySelect.value = 'as_needed';
        }

        // Add instructions based on medication information
        let instructions = `${medication.generic_name} (${medication.medication_class})`;

        if (medication.common_side_effects) {
            instructions += `\n\nPossible side effects: ${medication.common_side_effects}`;
        }

        instructionsTextarea.value = instructions;

        // Hide suggestions
        suggestionsContainer.classList.add('hidden');
        searchInput.value = `${medication.generic_name} (${medication.brand_names})`;
    }

    // Check for reference_id in URL and pre-fill form if present
    const urlParams = new URLSearchParams(window.location.search);
    const referenceId = urlParams.get('reference_id');

    if (referenceId) {
        fetch(`/momentum/adhd/medication/reference/view/${referenceId}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Medication reference not found');
                }
                return response.json();
            })
            .then(medication => {
                selectMedication(medication);
            })
            .catch(error => {
                console.error('Error loading medication reference:', error);
            });
    }
});
</script>
