<?php
/**
 * Medical Medication Reports View
 *
 * Displays reports and analytics for medications
 */
?>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Breadcrumb navigation -->
        <nav class="mb-6">
            <ol class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <li>
                    <a href="/momentum/dashboard" class="hover:text-gray-900 dark:hover:text-gray-200">Dashboard</a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-xs mx-2"></i>
                    <a href="/momentum/medical" class="hover:text-gray-900 dark:hover:text-gray-200">Medical</a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-xs mx-2"></i>
                    <a href="/momentum/medical/medication" class="hover:text-gray-900 dark:hover:text-gray-200">Medications</a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-xs mx-2"></i>
                    <span class="text-gray-900 dark:text-white">Reports</span>
                </li>
            </ol>
        </nav>

        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">
                <i class="fas fa-chart-line text-primary-600 dark:text-primary-400 mr-2"></i> Medication Reports
            </h1>
            <div class="flex flex-wrap gap-2">
                <a href="/momentum/medical/medication" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Medications
                </a>
            </div>
        </div>

        <!-- Date Range Filter -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                        <i class="fas fa-filter text-primary-600 dark:text-primary-400 mr-2"></i> Filter Reports
                    </h2>
                    <?php if (!empty($filters) && (isset($filters['start_date']) || isset($filters['end_date']) || isset($filters['medication_id']))): ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-800 dark:text-primary-100">
                            <i class="fas fa-check-circle mr-1"></i> Filters Active
                        </span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <form action="/momentum/medical/medication/reports" method="GET" class="space-y-4 sm:space-y-0 sm:flex sm:items-end sm:space-x-4">
                    <div class="w-full sm:w-auto">
                        <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                        <div class="mt-1">
                            <input type="date" name="start_date" id="start_date" value="<?= $filters['start_date'] ?? date('Y-m-d', strtotime('-30 days')) ?>" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                    </div>

                    <div class="w-full sm:w-auto">
                        <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">End Date</label>
                        <div class="mt-1">
                            <input type="date" name="end_date" id="end_date" value="<?= $filters['end_date'] ?? date('Y-m-d') ?>" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                    </div>

                    <div class="w-full sm:w-auto">
                        <label for="medication_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Medication</label>
                        <div class="mt-1">
                            <select name="medication_id" id="medication_id" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                <option value="">All Medications</option>
                                <?php foreach ($allMedications as $med): ?>
                                    <option value="<?= $med['id'] ?>" <?= isset($filters['medication_id']) && $filters['medication_id'] == $med['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($med['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row gap-2">
                        <button type="submit" id="apply-filters" class="w-full sm:w-auto inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-search mr-1"></i> <span>Apply Filters</span>
                        </button>

                        <button type="button" id="clear-filters" class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-times mr-1"></i> Clear Filters
                        </button>
                    </div>
                </form>

                <?php if (!empty($filters) && (isset($filters['start_date']) || isset($filters['end_date']) || isset($filters['medication_id']))): ?>
                <div class="mt-4 flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <i class="fas fa-filter mr-2"></i>
                    <span>
                        Filtered by:
                        <?php if (isset($filters['start_date'])): ?>
                            <span class="px-2 py-1 mr-1 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                                From: <?= date('M j, Y', strtotime($filters['start_date'])) ?>
                            </span>
                        <?php endif; ?>

                        <?php if (isset($filters['end_date'])): ?>
                            <span class="px-2 py-1 mr-1 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                                To: <?= date('M j, Y', strtotime($filters['end_date'])) ?>
                            </span>
                        <?php endif; ?>

                        <?php if (isset($filters['medication_id']) && !empty($filters['medication_id'])):
                            $filteredMedName = '';
                            foreach ($allMedications as $med) {
                                if ($med['id'] == $filters['medication_id']) {
                                    $filteredMedName = $med['name'];
                                    break;
                                }
                            }
                        ?>
                            <span class="px-2 py-1 mr-1 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                                Medication: <?= htmlspecialchars($filteredMedName) ?>
                            </span>
                        <?php endif; ?>
                    </span>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <?php if (empty($medications)): ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 text-center">
                <div class="text-gray-500 dark:text-gray-400 mb-4">
                    <i class="fas fa-pills text-5xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">No medications found</h3>
                    <p class="mt-1">Add medications to see reports and analytics.</p>
                </div>
                <a href="/momentum/medical/medication/new" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-1"></i> Add Medication
                </a>
            </div>
        <?php else: ?>
            <!-- Adherence Overview -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                <div class="px-4 py-5 sm:px-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                        <i class="fas fa-check-circle text-primary-600 dark:text-primary-400 mr-2"></i> Medication Adherence Overview
                    </h2>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Medication</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Dosage</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Frequency</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Adherence Rate</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Avg. Effectiveness</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($medications as $medication):
                                    $medicationId = $medication['id'];
                                    $adherence = $medicationData[$medicationId]['adherence'] ?? 0;
                                    $effectiveness = $medicationData[$medicationId]['effectiveness'] ?? 0;
                                ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                            <a href="/momentum/medical/medication/view/<?= $medicationId ?>" class="hover:text-primary-600 dark:hover:text-primary-400">
                                                <?= htmlspecialchars($medication['name']) ?>
                                            </a>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= htmlspecialchars($medication['dosage']) ?> <?= htmlspecialchars($medication['dosage_unit']) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= htmlspecialchars($medication['frequency']) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <div class="flex items-center">
                                                <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: <?= $adherence ?>%"></div>
                                                </div>
                                                <span class="ml-2"><?= round($adherence) ?>%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?php
                                            $effectivenessValue = isset($medicationData[$medicationId]['effectiveness']['avg_effectiveness'])
                                                ? floatval($medicationData[$medicationId]['effectiveness']['avg_effectiveness'])
                                                : 0;

                                            if ($effectivenessValue > 0):
                                            ?>
                                                <div class="flex items-center">
                                                    <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                                        <div class="bg-green-600 h-2.5 rounded-full" style="width: <?= ($effectivenessValue * 10) ?>%"></div>
                                                    </div>
                                                    <span class="ml-2"><?= number_format($effectivenessValue, 1) ?>/10</span>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-gray-400 dark:text-gray-500">No data</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Medication Effectiveness Chart -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                <div class="px-4 py-5 sm:px-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                        <i class="fas fa-chart-bar text-primary-600 dark:text-primary-400 mr-2"></i> Medication Effectiveness
                    </h2>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                    <div id="effectiveness-chart" class="h-80 relative">
                        <div id="chart-loading" class="absolute inset-0 flex items-center justify-center bg-white dark:bg-gray-800 bg-opacity-80 dark:bg-opacity-80">
                            <div class="text-center">
                                <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600 dark:border-primary-400"></div>
                                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">Loading chart...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Medication Reports -->
            <?php foreach ($medications as $medication):
                $medicationId = $medication['id'];
                $logs = $medicationData[$medicationId]['logs'] ?? [];
            ?>
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                    <div class="px-4 py-5 sm:px-6">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                            <i class="fas fa-pills text-primary-600 dark:text-primary-400 mr-2"></i> <?= htmlspecialchars($medication['name']) ?> Details
                        </h2>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                        <?php if (empty($logs)): ?>
                            <p class="text-gray-500 dark:text-gray-400 text-center py-4">No medication logs found for this period.</p>
                        <?php else: ?>
                            <div class="mb-6">
                                <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">Log Summary</h3>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                                        <div class="text-blue-500 dark:text-blue-400 text-sm font-medium">Total Doses</div>
                                        <div class="text-2xl font-bold text-blue-700 dark:text-blue-300"><?= count($logs) ?></div>
                                    </div>
                                    <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                                        <div class="text-green-500 dark:text-green-400 text-sm font-medium">Doses Taken</div>
                                        <div class="text-2xl font-bold text-green-700 dark:text-green-300">
                                            <?= count(array_filter($logs, function($log) { return $log['taken'] == 1; })) ?>
                                        </div>
                                    </div>
                                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                                        <div class="text-red-500 dark:text-red-400 text-sm font-medium">Doses Missed</div>
                                        <div class="text-2xl font-bold text-red-700 dark:text-red-300">
                                            <?= count(array_filter($logs, function($log) { return $log['taken'] == 0; })) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-6">
                                <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">Detailed Logs</h3>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                        <thead class="bg-gray-50 dark:bg-gray-700">
                                            <tr>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Time</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Effectiveness</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Side Effects</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                            <?php foreach ($logs as $log): ?>
                                                <tr>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                        <?= date('M j, Y', strtotime($log['log_date'])) ?>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                        <?= $log['log_time'] ? date('g:i A', strtotime($log['log_time'])) : 'N/A' ?>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                        <?php if ($log['taken'] == 1): ?>
                                                            <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                                                <i class="fas fa-check mr-1"></i> Taken
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                                                                <i class="fas fa-times mr-1"></i> Missed
                                                            </span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                        <?= $log['effectiveness_rating'] ? $log['effectiveness_rating'] . '/10' : 'N/A' ?>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                        <?php if (!empty($log['side_effects'])): ?>
                                                            <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100">
                                                                <?= htmlspecialchars($log['side_effects']) ?>
                                                                <?= !empty($log['side_effects_severity']) ? ' (' . $log['side_effects_severity'] . '/10)' : '' ?>
                                                            </span>
                                                        <?php else: ?>
                                                            None reported
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>

            <!-- Back to top button and navigation -->
            <div class="mt-8 flex flex-col sm:flex-row sm:justify-between items-center">
                <div class="mb-4 sm:mb-0">
                    <a href="/momentum/medical/medication" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-1"></i> Back to Medications
                    </a>
                </div>
                <button id="back-to-top" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-up mr-1"></i> Back to Top
                </button>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Back to top functionality
document.getElementById('back-to-top').addEventListener('click', function() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});

// Add event listeners for date inputs to ensure end_date is not before start_date
document.addEventListener('DOMContentLoaded', function() {
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const medicationSelect = document.getElementById('medication_id');
    const clearFiltersBtn = document.getElementById('clear-filters');
    const applyFiltersBtn = document.getElementById('apply-filters');
    const filterForm = document.querySelector('form[action="/momentum/medical/medication/reports"]');

    // Date validation
    if (startDateInput && endDateInput) {
        startDateInput.addEventListener('change', function() {
            if (endDateInput.value && startDateInput.value > endDateInput.value) {
                endDateInput.value = startDateInput.value;
            }
        });

        endDateInput.addEventListener('change', function() {
            if (startDateInput.value && endDateInput.value < startDateInput.value) {
                startDateInput.value = endDateInput.value;
            }
        });
    }

    // Apply filters functionality with loading indicator
    if (applyFiltersBtn && filterForm) {
        filterForm.addEventListener('submit', function(e) {
            // Show loading state on the button
            const buttonText = applyFiltersBtn.querySelector('span');
            const originalText = buttonText.textContent;

            buttonText.textContent = 'Applying...';
            applyFiltersBtn.disabled = true;
            applyFiltersBtn.classList.add('opacity-75');

            // Add a small timeout to ensure the form submits
            setTimeout(() => {
                // The form will submit naturally
            }, 100);
        });
    }

    // Clear filters functionality
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', function() {
            // Show loading state
            clearFiltersBtn.disabled = true;
            clearFiltersBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Clearing...';
            clearFiltersBtn.classList.add('opacity-75');

            // Reset form fields to default values
            if (startDateInput) {
                startDateInput.value = getDefaultStartDate();
            }

            if (endDateInput) {
                endDateInput.value = getCurrentDate();
            }

            if (medicationSelect) {
                medicationSelect.value = '';
            }

            // Submit the form with reset values
            setTimeout(() => {
                clearFiltersBtn.closest('form').submit();
            }, 100);
        });
    }

    // Helper functions for default dates
    function getCurrentDate() {
        const today = new Date();
        return formatDate(today);
    }

    function getDefaultStartDate() {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return formatDate(thirtyDaysAgo);
    }

    function formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
});
</script>

<?php if (!empty($medications)): ?>
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.35.0/dist/apexcharts.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if ApexCharts is loaded
    if (typeof ApexCharts === 'undefined') {
        console.error('ApexCharts library not loaded');
        document.getElementById('effectiveness-chart').innerHTML = '<div class="text-center py-10 text-gray-500">Chart library failed to load. Please refresh the page.</div>';
        return;
    }

    // Medication Effectiveness Chart
    const medications = <?= json_encode(array_column($medications, 'name')) ?>;
    const effectiveness = <?= json_encode(array_map(function($med) use ($medicationData) {
        $eff = isset($medicationData[$med['id']]['effectiveness']) && isset($medicationData[$med['id']]['effectiveness']['avg_effectiveness'])
            ? $medicationData[$med['id']]['effectiveness']['avg_effectiveness']
            : 0;
        return floatval($eff);
    }, $medications)) ?>;

    // Check if we have data to display
    if (medications.length === 0 || effectiveness.every(val => val === 0)) {
        document.getElementById('effectiveness-chart').innerHTML = '<div class="text-center py-10 text-gray-500">No effectiveness data available for the selected period.</div>';
        return;
    }

    const isDarkMode = document.documentElement.classList.contains('dark');
    const textColor = isDarkMode ? '#D1D5DB' : '#6B7280';
    const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

    const effectivenessOptions = {
        series: [{
            name: 'Effectiveness',
            data: effectiveness
        }],
        chart: {
            type: 'bar',
            height: 350,
            fontFamily: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
            toolbar: {
                show: false
            },
            background: 'transparent',
            foreColor: textColor
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '55%',
                borderRadius: 4,
                distributed: false
            },
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            show: true,
            width: 2,
            colors: ['transparent']
        },
        grid: {
            borderColor: gridColor,
            strokeDashArray: 4,
            yaxis: {
                lines: {
                    show: true
                }
            }
        },
        xaxis: {
            categories: medications,
            labels: {
                style: {
                    colors: textColor
                },
                rotate: -45,
                rotateAlways: medications.length > 3
            },
            axisBorder: {
                show: false
            },
            axisTicks: {
                show: false
            }
        },
        yaxis: {
            title: {
                text: 'Effectiveness (1-10)',
                style: {
                    color: textColor
                }
            },
            min: 0,
            max: 10,
            tickAmount: 5,
            labels: {
                style: {
                    colors: textColor
                }
            }
        },
        fill: {
            opacity: 1,
            type: 'solid'
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val.toFixed(1) + "/10";
                }
            },
            theme: isDarkMode ? 'dark' : 'light'
        },
        legend: {
            show: false
        },
        theme: {
            mode: isDarkMode ? 'dark' : 'light'
        },
        colors: ['#3B82F6']
    };

    try {
        // Hide loading indicator when chart is ready
        const hideLoading = () => {
            const loadingElement = document.getElementById('chart-loading');
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
        };

        const effectivenessChart = new ApexCharts(document.querySelector("#effectiveness-chart"), effectivenessOptions);
        effectivenessChart.render().then(hideLoading);

        // Fallback in case the promise doesn't resolve
        setTimeout(hideLoading, 2000);
    } catch (error) {
        console.error('Error rendering chart:', error);
        document.getElementById('effectiveness-chart').innerHTML = '<div class="text-center py-10 text-gray-500">Error rendering chart. Please refresh the page.</div>';

        // Hide loading indicator on error
        const loadingElement = document.getElementById('chart-loading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }
});
</script>
<?php endif; ?>