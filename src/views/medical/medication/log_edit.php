<?php
/**
 * Medical Medication Log Edit Form
 */
?>

<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-clipboard-check text-primary-600 dark:text-primary-400 mr-2"></i> Edit Medication Log
            </h1>
            <a href="/momentum/medical/medication/view/<?= $medication['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-1"></i> Back
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    Edit Log for <?= htmlspecialchars($medication['name']) ?>
                </h2>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Update your medication log entry.
                </p>
            </div>
            <form action="/momentum/medical/medication/log/update/<?= $log['id'] ?>" method="POST" class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 gap-6">
                    <!-- Medication Selection -->
                    <div>
                        <label for="medication_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Medication <span class="text-red-500">*</span>
                        </label>
                        <select name="medication_id" id="medication_id" required
                            class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            <?php foreach ($medications as $med): ?>
                                <option value="<?= $med['id'] ?>" <?= $med['id'] == $log['medication_id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($med['name']) ?> (<?= $med['dosage'] ?> <?= $med['dosage_unit'] ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Date and Time -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="log_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Date <span class="text-red-500">*</span>
                            </label>
                            <input type="date" name="log_date" id="log_date" required value="<?= $log['log_date'] ?>"
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                        <div>
                            <label for="log_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Time <span class="text-red-500">*</span>
                            </label>
                            <input type="time" name="log_time" id="log_time" required value="<?= $log['log_time'] ?>"
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                    </div>

                    <!-- Taken Status -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Status <span class="text-red-500">*</span>
                        </label>
                        <div class="flex items-center space-x-6">
                            <div class="flex items-center">
                                <input type="radio" name="taken" id="taken_yes" value="1" <?= $log['taken'] == 1 ? 'checked' : '' ?> required
                                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700">
                                <label for="taken_yes" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Taken
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" name="taken" id="taken_no" value="0" <?= $log['taken'] == 0 ? 'checked' : '' ?> required
                                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700">
                                <label for="taken_no" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Missed
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Actual Dosage -->
                    <div class="taken-dependent">
                        <label for="actual_dosage" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Actual Dosage Taken
                        </label>
                        <div class="mt-1 flex rounded-md shadow-sm">
                            <input type="number" name="actual_dosage" id="actual_dosage" step="0.01" value="<?= $log['actual_dosage'] ?>"
                                class="focus:ring-primary-500 focus:border-primary-500 flex-1 block w-full rounded-none rounded-l-md sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                            <span class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-600 text-gray-500 dark:text-gray-300 sm:text-sm">
                                <?= $medication['dosage_unit'] ?>
                            </span>
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Leave blank if you took the standard dosage.
                        </p>
                    </div>

                    <!-- Effectiveness Rating -->
                    <div class="taken-dependent">
                        <label for="effectiveness_rating" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Effectiveness Rating (1-10)
                        </label>
                        <div class="mt-1 flex items-center">
                            <input type="range" name="effectiveness_rating" id="effectiveness_rating" min="1" max="10" value="<?= $log['effectiveness_rating'] ?? 5 ?>"
                                class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                            <span id="effectiveness_value" class="ml-2 text-sm text-gray-700 dark:text-gray-300 w-8 text-center"><?= $log['effectiveness_rating'] ?? 5 ?></span>
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            1 = Not effective, 10 = Extremely effective
                        </p>
                    </div>

                    <!-- Side Effects -->
                    <div class="taken-dependent">
                        <label for="side_effects" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Side Effects
                        </label>
                        <textarea name="side_effects" id="side_effects" rows="2"
                            class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                            placeholder="Describe any side effects you experienced"><?= htmlspecialchars($log['side_effects'] ?? '') ?></textarea>
                    </div>

                    <!-- Side Effects Severity -->
                    <div class="taken-dependent">
                        <label for="side_effects_severity" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Side Effects Severity (1-10)
                        </label>
                        <div class="mt-1 flex items-center">
                            <input type="range" name="side_effects_severity" id="side_effects_severity" min="0" max="10" value="<?= $log['side_effects_severity'] ?? 0 ?>"
                                class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                            <span id="severity_value" class="ml-2 text-sm text-gray-700 dark:text-gray-300 w-8 text-center"><?= $log['side_effects_severity'] ?? 0 ?></span>
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            0 = None, 10 = Severe
                        </p>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Notes
                        </label>
                        <textarea name="notes" id="notes" rows="3"
                            class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                            placeholder="Any additional notes about this medication intake"><?= htmlspecialchars($log['notes'] ?? '') ?></textarea>
                    </div>
                </div>

                <div class="mt-6 flex justify-end">
                    <a href="/momentum/medical/medication/view/<?= $medication['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 mr-3">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i> Update Log
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Update effectiveness value display
    const effectivenessSlider = document.getElementById('effectiveness_rating');
    const effectivenessValue = document.getElementById('effectiveness_value');
    effectivenessSlider.addEventListener('input', function() {
        effectivenessValue.textContent = this.value;
    });

    // Update side effects severity value display
    const severitySlider = document.getElementById('side_effects_severity');
    const severityValue = document.getElementById('severity_value');
    severitySlider.addEventListener('input', function() {
        severityValue.textContent = this.value;
    });

    // Show/hide taken-dependent fields
    const takenRadios = document.querySelectorAll('input[name="taken"]');
    const takenDependentFields = document.querySelectorAll('.taken-dependent');

    function updateTakenDependentFields() {
        const isTaken = document.getElementById('taken_yes').checked;
        takenDependentFields.forEach(field => {
            field.style.display = isTaken ? 'block' : 'none';
        });
    }

    takenRadios.forEach(radio => {
        radio.addEventListener('change', updateTakenDependentFields);
    });

    // Initial update
    updateTakenDependentFields();
</script>
