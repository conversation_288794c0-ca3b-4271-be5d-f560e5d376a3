<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Freelance Project Manager</h1>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Manage your clients, projects, and payments</p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/freelance/clients/create" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-user-plus mr-2"></i> Add Client
                </a>
                <a href="/momentum/freelance/projects/create" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i> New Project
                </a>
                <a href="/momentum/freelance/invoices/create" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                    <i class="fas fa-file-invoice-dollar mr-2"></i> Create Invoice
                </a>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <!-- Clients Card -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-md p-3">
                        <i class="fas fa-users text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Clients</h2>
                        <div class="flex items-baseline">
                            <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $clientSummary['total_clients'] ?? 0 ?></p>
                            <p class="ml-2 text-xs text-gray-500 dark:text-gray-400"><?= $clientSummary['active_clients'] ?? 0 ?> active</p>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="/momentum/freelance/clients" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">View all clients <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>

            <!-- Projects Card -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-indigo-100 dark:bg-indigo-900 rounded-md p-3">
                        <i class="fas fa-project-diagram text-indigo-600 dark:text-indigo-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Projects</h2>
                        <div class="flex items-baseline">
                            <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $projectSummary['total_projects'] ?? 0 ?></p>
                            <p class="ml-2 text-xs text-gray-500 dark:text-gray-400"><?= $projectSummary['active_projects'] ?? 0 ?> active</p>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="/momentum/freelance/projects" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">View all projects <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>

            <!-- Invoices Card -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-yellow-100 dark:bg-yellow-900 rounded-md p-3">
                        <i class="fas fa-file-invoice text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Invoices</h2>
                        <div class="flex items-baseline">
                            <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $invoiceSummary['total_invoices'] ?? 0 ?></p>
                            <p class="ml-2 text-xs text-gray-500 dark:text-gray-400"><?= $invoiceSummary['overdue_invoices'] ?? 0 ?> overdue</p>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="/momentum/freelance/invoices" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">View all invoices <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>

            <!-- Payments Card -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-3">
                        <i class="fas fa-money-bill-wave text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Payments</h2>
                        <div class="flex items-baseline">
                            <p class="text-lg font-semibold text-gray-900 dark:text-white">Rs <?= number_format($paymentSummary['total_amount'] ?? 0, 0) ?></p>
                            <p class="ml-2 text-xs text-gray-500 dark:text-gray-400"><?= $paymentSummary['total_payments'] ?? 0 ?> total</p>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="/momentum/freelance/payments" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">View all payments <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Active Projects -->
            <div class="lg:col-span-2 bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Active Projects</h3>
                </div>
                
                <?php if (empty($activeProjects)): ?>
                    <div class="p-6 text-center">
                        <p class="text-gray-500 dark:text-gray-400">No active projects found.</p>
                        <a href="/momentum/freelance/projects/create" class="inline-flex items-center justify-center px-4 py-2 mt-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i> Create Project
                        </a>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Project</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Client</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Deadline</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($activeProjects as $project): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($project['name']) ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($project['client_name']) ?></div>
                                            <?php if (!empty($project['client_company'])): ?>
                                                <div class="text-xs text-gray-400 dark:text-gray-500"><?= htmlspecialchars($project['client_company']) ?></div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if (!empty($project['deadline'])): ?>
                                                <div class="text-sm <?= $project['days_remaining'] < 0 ? 'text-red-600 dark:text-red-400' : ($project['days_remaining'] < 3 ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-500 dark:text-gray-400') ?>">
                                                    <?= date('M j, Y', strtotime($project['deadline'])) ?>
                                                    <?php if ($project['days_remaining'] < 0): ?>
                                                        <span class="text-xs font-medium">(<?= abs($project['days_remaining']) ?> days overdue)</span>
                                                    <?php elseif ($project['days_remaining'] == 0): ?>
                                                        <span class="text-xs font-medium">(Today)</span>
                                                    <?php else: ?>
                                                        <span class="text-xs font-medium">(<?= $project['days_remaining'] ?> days left)</span>
                                                    <?php endif; ?>
                                                </div>
                                            <?php else: ?>
                                                <div class="text-sm text-gray-400 dark:text-gray-500">No deadline</div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <a href="/momentum/freelance/projects/view/<?= $project['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300 mr-3">View</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                        <a href="/momentum/freelance/projects" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">View all projects <i class="fas fa-arrow-right ml-1"></i></a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Payments -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Recent Payments</h3>
                </div>
                
                <?php if (empty($recentPayments)): ?>
                    <div class="p-6 text-center">
                        <p class="text-gray-500 dark:text-gray-400">No payments recorded yet.</p>
                        <a href="/momentum/freelance/payments/create" class="inline-flex items-center justify-center px-4 py-2 mt-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i> Record Payment
                        </a>
                    </div>
                <?php else: ?>
                    <div class="divide-y divide-gray-200 dark:divide-gray-700">
                        <?php foreach ($recentPayments as $payment): ?>
                            <div class="p-4">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            <?= htmlspecialchars($payment['client_name']) ?>
                                            <?php if (!empty($payment['client_company'])): ?>
                                                <span class="text-xs text-gray-500 dark:text-gray-400">(<?= htmlspecialchars($payment['client_company']) ?>)</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            <?= htmlspecialchars($payment['project_name']) ?>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-medium text-green-600 dark:text-green-400">
                                            Rs <?= number_format($payment['amount'], 0) ?>
                                        </div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            <?= date('M j, Y', strtotime($payment['payment_date'])) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                        <a href="/momentum/freelance/payments" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">View all payments <i class="fas fa-arrow-right ml-1"></i></a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Monthly Income Chart -->
        <div class="mt-6 bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Monthly Income</h3>
            </div>
            <div class="p-4 h-64">
                <canvas id="monthlyIncomeChart"></canvas>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Monthly Income Chart
    const monthlyIncomeCtx = document.getElementById('monthlyIncomeChart').getContext('2d');
    
    // Prepare data for chart
    const monthlyPaymentsData = <?= json_encode($monthlyPayments) ?>;
    const labels = [];
    const data = [];
    
    // Format month labels and extract data
    monthlyPaymentsData.forEach(item => {
        const date = new Date(item.month + '-01');
        labels.push(date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }));
        data.push(parseFloat(item.total));
    });
    
    // Create chart
    new Chart(monthlyIncomeCtx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Monthly Income (Rs)',
                data: data,
                backgroundColor: 'rgba(34, 197, 94, 0.2)',
                borderColor: 'rgba(34, 197, 94, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'Rs ' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Rs ' + context.raw.toLocaleString();
                        }
                    }
                }
            }
        }
    });
});
</script>
