<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/freelance/projects" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
                <span class="ml-1">Back to Projects</span>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Create New Project</h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Project Information</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                    Enter the details of your new project
                </p>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <form action="/momentum/freelance/projects/store" method="POST">
                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                        <div class="sm:col-span-4">
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Project Name *</label>
                            <div class="mt-1">
                                <input type="text" name="name" id="name" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required value="<?= isset($data['name']) ? htmlspecialchars($data['name']) : '' ?>">
                            </div>
                        </div>

                        <div class="sm:col-span-2">
                            <label for="client_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Client *</label>
                            <div class="mt-1">
                                <select name="client_id" id="client_id" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required>
                                    <option value="">Select Client</option>
                                    <?php if (isset($clients) && is_array($clients)): ?>
                                        <?php foreach ($clients as $client): ?>
                                            <option value="<?= $client['id'] ?>" <?= (isset($data['client_id']) && $data['client_id'] == $client['id']) || (isset($_GET['client_id']) && $_GET['client_id'] == $client['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($client['name']) ?> <?= !empty($client['company']) ? '(' . htmlspecialchars($client['company']) . ')' : '' ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="mt-2 text-sm">
                                <a href="/momentum/freelance/clients/create" class="text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300">
                                    <i class="fas fa-plus-circle mr-1"></i> Add New Client
                                </a>
                            </div>
                        </div>

                        <div class="sm:col-span-6">
                            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                            <div class="mt-1">
                                <textarea name="description" id="description" rows="4" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"><?= isset($data['description']) ? htmlspecialchars($data['description']) : '' ?></textarea>
                            </div>
                        </div>

                        <div class="sm:col-span-2">
                            <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                            <div class="mt-1">
                                <input type="date" name="start_date" id="start_date" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= isset($data['start_date']) ? htmlspecialchars($data['start_date']) : date('Y-m-d') ?>">
                            </div>
                        </div>

                        <div class="sm:col-span-2">
                            <label for="deadline" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Deadline</label>
                            <div class="mt-1">
                                <input type="date" name="deadline" id="deadline" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= isset($data['deadline']) ? htmlspecialchars($data['deadline']) : '' ?>">
                            </div>
                        </div>

                        <div class="sm:col-span-2">
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                            <div class="mt-1">
                                <select name="status" id="status" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                    <option value="not_started" <?= (isset($data['status']) && $data['status'] === 'not_started') ? 'selected' : '' ?>>Not Started</option>
                                    <option value="in_progress" <?= (isset($data['status']) && $data['status'] === 'in_progress') ? 'selected' : 'selected' ?>>In Progress</option>
                                    <option value="on_hold" <?= (isset($data['status']) && $data['status'] === 'on_hold') ? 'selected' : '' ?>>On Hold</option>
                                    <option value="completed" <?= (isset($data['status']) && $data['status'] === 'completed') ? 'selected' : '' ?>>Completed</option>
                                    <option value="cancelled" <?= (isset($data['status']) && $data['status'] === 'cancelled') ? 'selected' : '' ?>>Cancelled</option>
                                </select>
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <label for="value" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Project Value (Rs)</label>
                            <div class="mt-1">
                                <input type="number" name="value" id="value" step="0.01" min="0" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= isset($data['value']) ? htmlspecialchars($data['value']) : '' ?>">
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <label for="billing_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Billing Type</label>
                            <div class="mt-1">
                                <select name="billing_type" id="billing_type" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                    <option value="fixed" <?= (isset($data['billing_type']) && $data['billing_type'] === 'fixed') ? 'selected' : 'selected' ?>>Fixed Price</option>
                                    <option value="hourly" <?= (isset($data['billing_type']) && $data['billing_type'] === 'hourly') ? 'selected' : '' ?>>Hourly Rate</option>
                                    <option value="milestone" <?= (isset($data['billing_type']) && $data['billing_type'] === 'milestone') ? 'selected' : '' ?>>Milestone Based</option>
                                </select>
                            </div>
                        </div>

                        <div class="sm:col-span-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                            <div class="mt-1">
                                <textarea name="notes" id="notes" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"><?= isset($data['notes']) ? htmlspecialchars($data['notes']) : '' ?></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <a href="/momentum/freelance/projects" class="bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Cancel
                        </a>
                        <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Create Project
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
