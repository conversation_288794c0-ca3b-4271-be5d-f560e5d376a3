<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Freelance Dashboard</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Manage your freelance business</p>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-md p-3">
                        <i class="fas fa-users text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Clients</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $summary['active_clients'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-3">
                        <i class="fas fa-tasks text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Projects</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $summary['active_projects'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-yellow-100 dark:bg-yellow-900 rounded-md p-3">
                        <i class="fas fa-file-invoice-dollar text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Outstanding</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">Rs <?= number_format($summary['outstanding_amount'] ?? 0, 0) ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-purple-100 dark:bg-purple-900 rounded-md p-3">
                        <i class="fas fa-money-bill-wave text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">This Month</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">Rs <?= number_format($summary['current_month_income'] ?? 0, 0) ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Quick Actions</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="/momentum/freelance/clients/create" class="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200">
                    <div class="w-12 h-12 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mb-3">
                        <i class="fas fa-user-plus text-blue-600 dark:text-blue-400 text-xl"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">New Client</span>
                </a>
                <a href="/momentum/freelance/projects/create" class="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200">
                    <div class="w-12 h-12 flex items-center justify-center bg-green-100 dark:bg-green-900 rounded-full mb-3">
                        <i class="fas fa-project-diagram text-green-600 dark:text-green-400 text-xl"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">New Project</span>
                </a>
                <a href="/momentum/freelance/invoices/create" class="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200">
                    <div class="w-12 h-12 flex items-center justify-center bg-yellow-100 dark:bg-yellow-900 rounded-full mb-3">
                        <i class="fas fa-file-invoice text-yellow-600 dark:text-yellow-400 text-xl"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">New Invoice</span>
                </a>
                <a href="/momentum/freelance/payments/create" class="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200">
                    <div class="w-12 h-12 flex items-center justify-center bg-purple-100 dark:bg-purple-900 rounded-full mb-3">
                        <i class="fas fa-hand-holding-usd text-purple-600 dark:text-purple-400 text-xl"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Record Payment</span>
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Active Projects -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Active Projects</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                            Your ongoing projects
                        </p>
                    </div>
                    <a href="/momentum/freelance/projects" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        View All <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <?php if (empty($activeProjects)): ?>
                        <p class="text-gray-500 dark:text-gray-400 text-center py-4">No active projects found.</p>
                    <?php else: ?>
                        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($activeProjects as $project): ?>
                                <li class="py-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($project['name']) ?></h4>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                <?= htmlspecialchars($project['client_name']) ?>
                                                <?php if (!empty($project['deadline'])): ?>
                                                    <span class="mx-1">•</span>
                                                    Due: <?= date('M d, Y', strtotime($project['deadline'])) ?>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                        <a href="/momentum/freelance/projects/view/<?= $project['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 text-sm">
                                            View
                                        </a>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Payments -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Recent Payments</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                            Latest payments received
                        </p>
                    </div>
                    <a href="/momentum/freelance/payments" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        View All <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <?php if (empty($recentPayments)): ?>
                        <p class="text-gray-500 dark:text-gray-400 text-center py-4">No recent payments found.</p>
                    <?php else: ?>
                        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($recentPayments as $payment): ?>
                                <li class="py-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Rs <?= number_format($payment['amount'], 0) ?></h4>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                <?= htmlspecialchars($payment['client_name']) ?>
                                                <span class="mx-1">•</span>
                                                <?= date('M d, Y', strtotime($payment['payment_date'])) ?>
                                            </p>
                                        </div>
                                        <a href="/momentum/freelance/payments/view/<?= $payment['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 text-sm">
                                            View
                                        </a>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Overdue Invoices -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Overdue Invoices</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                            Invoices past their due date
                        </p>
                    </div>
                    <a href="/momentum/freelance/invoices?status=overdue" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        View All <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <?php if (empty($overdueInvoices)): ?>
                        <p class="text-gray-500 dark:text-gray-400 text-center py-4">No overdue invoices found.</p>
                    <?php else: ?>
                        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($overdueInvoices as $invoice): ?>
                                <li class="py-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($invoice['invoice_number']) ?> - Rs <?= number_format($invoice['total_amount'], 0) ?></h4>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                <?= htmlspecialchars($invoice['client_name']) ?>
                                                <span class="mx-1">•</span>
                                                Due: <?= date('M d, Y', strtotime($invoice['due_date'])) ?>
                                                <?php 
                                                    $dueDate = new DateTime($invoice['due_date']);
                                                    $today = new DateTime();
                                                    $diff = $today->diff($dueDate);
                                                    $daysOverdue = $diff->days;
                                                ?>
                                                <span class="text-red-600 dark:text-red-400 ml-1">(<?= $daysOverdue ?> days overdue)</span>
                                            </p>
                                        </div>
                                        <a href="/momentum/freelance/invoices/view/<?= $invoice['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 text-sm">
                                            View
                                        </a>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Active Clients -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Active Clients</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                            Your current clients
                        </p>
                    </div>
                    <a href="/momentum/freelance/clients" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        View All <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <?php if (empty($activeClients)): ?>
                        <p class="text-gray-500 dark:text-gray-400 text-center py-4">No active clients found.</p>
                    <?php else: ?>
                        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($activeClients as $client): ?>
                                <li class="py-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($client['name']) ?></h4>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                <?= !empty($client['company']) ? htmlspecialchars($client['company']) : 'Individual' ?>
                                                <?php if ($client['active_projects'] > 0): ?>
                                                    <span class="mx-1">•</span>
                                                    <?= $client['active_projects'] ?> active project<?= $client['active_projects'] > 1 ? 's' : '' ?>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                        <a href="/momentum/freelance/clients/view/<?= $client['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 text-sm">
                                            View
                                        </a>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
