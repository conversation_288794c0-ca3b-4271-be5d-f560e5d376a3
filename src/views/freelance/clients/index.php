<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Clients</h1>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Manage your freelance clients</p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="/momentum/freelance/clients/create" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-user-plus mr-2"></i> Add Client
                </a>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-md p-3">
                        <i class="fas fa-users text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Clients</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $clientSummary['total_clients'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-3">
                        <i class="fas fa-user-check text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Clients</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $clientSummary['active_clients'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-purple-100 dark:bg-purple-900 rounded-md p-3">
                        <i class="fas fa-user-plus text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Potential Clients</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $clientSummary['potential_clients'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <form action="/momentum/freelance/clients" method="get" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Search</label>
                        <input type="text" name="search" id="search" value="<?= isset($filters['search']) ? htmlspecialchars($filters['search']) : '' ?>" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                        <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Statuses</option>
                            <option value="active" <?= isset($filters['status']) && $filters['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= isset($filters['status']) && $filters['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            <option value="potential" <?= isset($filters['status']) && $filters['status'] === 'potential' ? 'selected' : '' ?>>Potential</option>
                            <option value="archived" <?= isset($filters['status']) && $filters['status'] === 'archived' ? 'selected' : '' ?>>Archived</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-filter mr-2"></i> Apply Filters
                        </button>
                        <a href="/momentum/freelance/clients" class="ml-2 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-times mr-2"></i> Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Clients List -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Client List</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                    <?= count($clients) ?> clients found
                </p>
            </div>
            
            <?php if (empty($clients)): ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500 dark:text-gray-400">No clients found. Add your first client to get started!</p>
                    <a href="/momentum/freelance/clients/create" class="inline-flex items-center justify-center px-4 py-2 mt-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-user-plus mr-2"></i> Add Client
                    </a>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Client</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Contact</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Projects</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Financials</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($clients as $client): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                    <?= htmlspecialchars($client['name']) ?>
                                                </div>
                                                <?php if (!empty($client['company'])): ?>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                                        <?= htmlspecialchars($client['company']) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if (!empty($client['email'])): ?>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                <i class="fas fa-envelope mr-1 text-xs"></i> <?= htmlspecialchars($client['email']) ?>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($client['phone'])): ?>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                <i class="fas fa-phone mr-1 text-xs"></i> <?= htmlspecialchars($client['phone']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            <?php if ($client['status'] === 'active'): ?>
                                                bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
                                            <?php elseif ($client['status'] === 'potential'): ?>
                                                bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200
                                            <?php elseif ($client['status'] === 'inactive'): ?>
                                                bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200
                                            <?php else: ?>
                                                bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300
                                            <?php endif; ?>">
                                            <?= ucfirst(htmlspecialchars($client['status'])) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 dark:text-white"><?= $client['total_projects'] ?> total</div>
                                        <?php if ($client['active_projects'] > 0): ?>
                                            <div class="text-xs text-green-600 dark:text-green-400"><?= $client['active_projects'] ?> active</div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($client['total_paid'] > 0): ?>
                                            <div class="text-sm text-gray-900 dark:text-white">Rs <?= number_format($client['total_paid'], 0) ?> paid</div>
                                        <?php endif; ?>
                                        <?php if ($client['total_outstanding'] > 0): ?>
                                            <div class="text-xs text-yellow-600 dark:text-yellow-400">Rs <?= number_format($client['total_outstanding'], 0) ?> outstanding</div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="/momentum/freelance/clients/view/<?= $client['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300 mr-3">View</a>
                                        <a href="/momentum/freelance/clients/edit/<?= $client['id'] ?>" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-3">Edit</a>
                                        <a href="/momentum/freelance/projects/create?client_id=<?= $client['id'] ?>" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300">New Project</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
