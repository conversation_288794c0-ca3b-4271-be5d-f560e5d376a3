<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/freelance/clients/view/<?= isset($client['id']) ? $client['id'] : '0' ?>" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
                <span class="ml-1">Back to Client</span>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Edit Client: <?= isset($client['name']) ? htmlspecialchars($client['name']) : 'Client' ?>
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Client Information</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                    Update the client's details
                </p>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <form action="/momentum/freelance/clients/update/<?= isset($client['id']) ? $client['id'] : '0' ?>" method="POST">
                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                        <div class="sm:col-span-3">
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name *</label>
                            <div class="mt-1">
                                <input type="text" name="name" id="name" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required value="<?= isset($client['name']) ? htmlspecialchars($client['name']) : '' ?>">
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <label for="company" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Company</label>
                            <div class="mt-1">
                                <input type="text" name="company" id="company" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= isset($client['company']) ? htmlspecialchars($client['company']) : '' ?>">
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                            <div class="mt-1">
                                <input type="email" name="email" id="email" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= isset($client['email']) ? htmlspecialchars($client['email']) : '' ?>">
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone</label>
                            <div class="mt-1">
                                <input type="text" name="phone" id="phone" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= isset($client['phone']) ? htmlspecialchars($client['phone']) : '' ?>">
                            </div>
                        </div>

                        <div class="sm:col-span-6">
                            <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Address</label>
                            <div class="mt-1">
                                <textarea name="address" id="address" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"><?= isset($client['address']) ? htmlspecialchars($client['address']) : '' ?></textarea>
                            </div>
                        </div>

                        <div class="sm:col-span-6">
                            <label for="website" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Website</label>
                            <div class="mt-1">
                                <input type="url" name="website" id="website" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= isset($client['website']) ? htmlspecialchars($client['website']) : '' ?>">
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                            <div class="mt-1">
                                <select name="status" id="status" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                    <option value="active" <?= (isset($client['status']) && $client['status'] === 'active') ? 'selected' : '' ?>>Active</option>
                                    <option value="potential" <?= (isset($client['status']) && $client['status'] === 'potential') ? 'selected' : '' ?>>Potential</option>
                                    <option value="inactive" <?= (isset($client['status']) && $client['status'] === 'inactive') ? 'selected' : '' ?>>Inactive</option>
                                </select>
                            </div>
                        </div>

                        <div class="sm:col-span-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                            <div class="mt-1">
                                <textarea name="notes" id="notes" rows="4" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"><?= isset($client['notes']) ? htmlspecialchars($client['notes']) : '' ?></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <a href="/momentum/freelance/clients/view/<?= isset($client['id']) ? $client['id'] : '0' ?>" class="bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Cancel
                        </a>
                        <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Update Client
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
