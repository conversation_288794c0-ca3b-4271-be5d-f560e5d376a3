<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #<?= isset($invoice['invoice_number']) ? htmlspecialchars($invoice['invoice_number']) : 'Invoice' ?></title>
    <style>
        @media print {
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
                color: #333;
                background: white;
            }
            
            .print-container {
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
            }
            
            .print-header {
                display: flex;
                justify-content: space-between;
                margin-bottom: 40px;
            }
            
            .company-info {
                flex: 1;
            }
            
            .company-info h1 {
                color: #4f46e5;
                margin: 0 0 5px 0;
                font-size: 28px;
            }
            
            .invoice-details {
                text-align: right;
            }
            
            .invoice-details h2 {
                color: #4f46e5;
                margin: 0 0 15px 0;
                font-size: 24px;
            }
            
            .invoice-details-table {
                border-collapse: collapse;
                margin-left: auto;
            }
            
            .invoice-details-table td {
                padding: 3px 0;
            }
            
            .invoice-details-table td:first-child {
                font-weight: bold;
                padding-right: 20px;
            }
            
            .client-info {
                margin-bottom: 40px;
            }
            
            .client-info h3 {
                color: #4f46e5;
                margin: 0 0 10px 0;
                font-size: 18px;
            }
            
            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 30px;
            }
            
            .items-table th {
                background-color: #f3f4f6;
                padding: 10px;
                text-align: left;
                border-bottom: 2px solid #e5e7eb;
            }
            
            .items-table td {
                padding: 10px;
                border-bottom: 1px solid #e5e7eb;
            }
            
            .items-table tr:last-child td {
                border-bottom: none;
            }
            
            .items-table .text-right {
                text-align: right;
            }
            
            .totals-table {
                width: 300px;
                margin-left: auto;
                border-collapse: collapse;
            }
            
            .totals-table td {
                padding: 5px 0;
            }
            
            .totals-table td:first-child {
                font-weight: bold;
            }
            
            .totals-table td:last-child {
                text-align: right;
            }
            
            .totals-table .total-row td {
                border-top: 2px solid #e5e7eb;
                font-size: 18px;
                padding-top: 10px;
            }
            
            .notes {
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #e5e7eb;
            }
            
            .notes h3 {
                color: #4f46e5;
                margin: 0 0 10px 0;
                font-size: 18px;
            }
            
            .footer {
                margin-top: 40px;
                text-align: center;
                color: #6b7280;
                font-size: 14px;
            }
            
            .no-print {
                display: none;
            }
        }
        
        /* Non-print styles */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background: #f9fafb;
        }
        
        .print-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 40px;
            background: white;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            border-radius: 8px;
        }
        
        .print-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .company-info {
            flex: 1;
        }
        
        .company-info h1 {
            color: #4f46e5;
            margin: 0 0 5px 0;
            font-size: 28px;
        }
        
        .invoice-details {
            text-align: right;
        }
        
        .invoice-details h2 {
            color: #4f46e5;
            margin: 0 0 15px 0;
            font-size: 24px;
        }
        
        .invoice-details-table {
            border-collapse: collapse;
            margin-left: auto;
        }
        
        .invoice-details-table td {
            padding: 3px 0;
        }
        
        .invoice-details-table td:first-child {
            font-weight: bold;
            padding-right: 20px;
        }
        
        .client-info {
            margin-bottom: 40px;
        }
        
        .client-info h3 {
            color: #4f46e5;
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .items-table th {
            background-color: #f3f4f6;
            padding: 10px;
            text-align: left;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .items-table td {
            padding: 10px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .items-table tr:last-child td {
            border-bottom: none;
        }
        
        .items-table .text-right {
            text-align: right;
        }
        
        .totals-table {
            width: 300px;
            margin-left: auto;
            border-collapse: collapse;
        }
        
        .totals-table td {
            padding: 5px 0;
        }
        
        .totals-table td:first-child {
            font-weight: bold;
        }
        
        .totals-table td:last-child {
            text-align: right;
        }
        
        .totals-table .total-row td {
            border-top: 2px solid #e5e7eb;
            font-size: 18px;
            padding-top: 10px;
        }
        
        .notes {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        
        .notes h3 {
            color: #4f46e5;
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
        }
        
        .no-print {
            text-align: center;
            margin-top: 20px;
        }
        
        .no-print button {
            background-color: #4f46e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        
        .no-print a {
            display: inline-block;
            background-color: #6b7280;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="print-header">
            <div class="company-info">
                <h1>Your Company Name</h1>
                <p>123 Business Street<br>
                City, State 12345<br>
                Phone: (*************<br>
                Email: <EMAIL></p>
            </div>
            <div class="invoice-details">
                <h2>INVOICE</h2>
                <table class="invoice-details-table">
                    <tr>
                        <td>Invoice Number:</td>
                        <td><?= isset($invoice['invoice_number']) ? htmlspecialchars($invoice['invoice_number']) : 'N/A' ?></td>
                    </tr>
                    <tr>
                        <td>Invoice Date:</td>
                        <td><?= isset($invoice['invoice_date']) ? date('F d, Y', strtotime($invoice['invoice_date'])) : 'N/A' ?></td>
                    </tr>
                    <tr>
                        <td>Due Date:</td>
                        <td><?= isset($invoice['due_date']) ? date('F d, Y', strtotime($invoice['due_date'])) : 'N/A' ?></td>
                    </tr>
                    <tr>
                        <td>Status:</td>
                        <td><?= isset($invoice['status']) ? ucfirst(htmlspecialchars($invoice['status'])) : 'N/A' ?></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="client-info">
            <h3>Bill To:</h3>
            <p>
                <strong><?= isset($invoice['client_name']) ? htmlspecialchars($invoice['client_name']) : 'N/A' ?></strong><br>
                <?= isset($invoice['client_company']) && !empty($invoice['client_company']) ? htmlspecialchars($invoice['client_company']) . '<br>' : '' ?>
                <?= isset($invoice['client_address']) && !empty($invoice['client_address']) ? nl2br(htmlspecialchars($invoice['client_address'])) . '<br>' : '' ?>
                <?= isset($invoice['client_email']) && !empty($invoice['client_email']) ? 'Email: ' . htmlspecialchars($invoice['client_email']) : '' ?>
            </p>
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Unit Price</th>
                    <th class="text-right">Total</th>
                </tr>
            </thead>
            <tbody>
                <?php if (isset($invoice['items']) && !empty($invoice['items'])): ?>
                    <?php foreach ($invoice['items'] as $item): ?>
                        <tr>
                            <td><?= htmlspecialchars($item['description']) ?></td>
                            <td><?= htmlspecialchars($item['quantity']) ?></td>
                            <td>Rs <?= number_format($item['unit_price'], 2) ?></td>
                            <td class="text-right">Rs <?= number_format($item['total'], 2) ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="4">No items found</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
        
        <table class="totals-table">
            <tr>
                <td>Subtotal:</td>
                <td>Rs <?= isset($invoice['subtotal']) ? number_format($invoice['subtotal'], 2) : '0.00' ?></td>
            </tr>
            <?php if (isset($invoice['tax_rate']) && $invoice['tax_rate'] > 0): ?>
                <tr>
                    <td>Tax (<?= number_format($invoice['tax_rate'], 2) ?>%):</td>
                    <td>Rs <?= isset($invoice['tax_amount']) ? number_format($invoice['tax_amount'], 2) : '0.00' ?></td>
                </tr>
            <?php endif; ?>
            <tr class="total-row">
                <td>Total:</td>
                <td>Rs <?= isset($invoice['total_amount']) ? number_format($invoice['total_amount'], 2) : '0.00' ?></td>
            </tr>
            <?php if (isset($invoice['amount_paid']) && $invoice['amount_paid'] > 0): ?>
                <tr>
                    <td>Amount Paid:</td>
                    <td>Rs <?= number_format($invoice['amount_paid'], 2) ?></td>
                </tr>
                <tr>
                    <td>Balance Due:</td>
                    <td>Rs <?= number_format($invoice['amount_due'], 2) ?></td>
                </tr>
            <?php endif; ?>
        </table>
        
        <?php if (isset($invoice['notes']) && !empty($invoice['notes'])): ?>
            <div class="notes">
                <h3>Notes:</h3>
                <p><?= nl2br(htmlspecialchars($invoice['notes'])) ?></p>
            </div>
        <?php endif; ?>
        
        <div class="footer">
            <p>Thank you for your business!</p>
        </div>
    </div>
    
    <div class="no-print">
        <button onclick="window.print()">Print Invoice</button>
        <a href="/momentum/freelance/invoices/view/<?= isset($invoice['id']) ? $invoice['id'] : '0' ?>">Back to Invoice</a>
    </div>
</body>
</html>
