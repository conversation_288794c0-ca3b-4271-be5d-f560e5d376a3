<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Invoices</h1>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Manage your freelance invoices</p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="/momentum/freelance/invoices/create" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i> New Invoice
                </a>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-md p-3">
                        <i class="fas fa-file-invoice text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Invoices</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $invoiceSummary['total_invoices'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-yellow-100 dark:bg-yellow-900 rounded-md p-3">
                        <i class="fas fa-paper-plane text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Sent Invoices</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $invoiceSummary['sent_invoices'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-red-100 dark:bg-red-900 rounded-md p-3">
                        <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Overdue Invoices</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $invoiceSummary['overdue_invoices'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-3">
                        <i class="fas fa-money-bill-wave text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Outstanding</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">Rs <?= number_format($invoiceSummary['total_outstanding'] ?? 0, 0) ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <form action="/momentum/freelance/invoices" method="get" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Search</label>
                        <input type="text" name="search" id="search" value="<?= isset($filters['search']) ? htmlspecialchars($filters['search']) : '' ?>" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                        <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Statuses</option>
                            <option value="draft" <?= isset($filters['status']) && $filters['status'] === 'draft' ? 'selected' : '' ?>>Draft</option>
                            <option value="sent" <?= isset($filters['status']) && $filters['status'] === 'sent' ? 'selected' : '' ?>>Sent</option>
                            <option value="paid" <?= isset($filters['status']) && $filters['status'] === 'paid' ? 'selected' : '' ?>>Paid</option>
                            <option value="overdue" <?= isset($filters['status']) && $filters['status'] === 'overdue' ? 'selected' : '' ?>>Overdue</option>
                            <option value="cancelled" <?= isset($filters['status']) && $filters['status'] === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                        </select>
                    </div>
                    <div>
                        <label for="client_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Client</label>
                        <select name="client_id" id="client_id" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Clients</option>
                            <?php if (isset($clients) && is_array($clients)): ?>
                                <?php foreach ($clients as $client): ?>
                                    <option value="<?= $client['id'] ?>" <?= isset($filters['client_id']) && $filters['client_id'] == $client['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($client['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div>
                        <label for="project_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Project</label>
                        <select name="project_id" id="project_id" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Projects</option>
                            <?php if (isset($projects) && is_array($projects)): ?>
                                <?php foreach ($projects as $project): ?>
                                    <option value="<?= $project['id'] ?>" <?= isset($filters['project_id']) && $filters['project_id'] == $project['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($project['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-filter mr-2"></i> Apply Filters
                    </button>
                    <a href="/momentum/freelance/invoices" class="ml-2 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i> Clear
                    </a>
                </div>
            </form>
        </div>

        <!-- Invoices List -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Invoice List</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                    <?= count($invoices ?? []) ?> invoices found
                </p>
            </div>
            
            <?php if (empty($invoices)): ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500 dark:text-gray-400">No invoices found. Create your first invoice to get started!</p>
                    <a href="/momentum/freelance/invoices/create" class="inline-flex items-center justify-center px-4 py-2 mt-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i> Create Invoice
                    </a>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Invoice #</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Client</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Due Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Amount</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($invoices as $invoice): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            <?= htmlspecialchars($invoice['invoice_number']) ?>
                                        </div>
                                        <?php if (!empty($invoice['project_name'])): ?>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                <?= htmlspecialchars($invoice['project_name']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 dark:text-white">
                                            <?= htmlspecialchars($invoice['client_name']) ?>
                                        </div>
                                        <?php if (!empty($invoice['client_company'])): ?>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                <?= htmlspecialchars($invoice['client_company']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <?= date('M d, Y', strtotime($invoice['invoice_date'])) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <?= date('M d, Y', strtotime($invoice['due_date'])) ?>
                                        <?php 
                                            $dueDate = new DateTime($invoice['due_date']);
                                            $today = new DateTime();
                                            $diff = $today->diff($dueDate);
                                            $daysRemaining = $dueDate > $today ? $diff->days : -$diff->days;
                                            
                                            if ($invoice['status'] !== 'paid' && $invoice['status'] !== 'cancelled'):
                                                if ($daysRemaining < 0): 
                                        ?>
                                            <span class="text-xs text-red-600 dark:text-red-400 ml-1">
                                                <?= abs($daysRemaining) ?> days overdue
                                            </span>
                                        <?php elseif ($daysRemaining <= 7): ?>
                                            <span class="text-xs text-yellow-600 dark:text-yellow-400 ml-1">
                                                <?= $daysRemaining ?> days left
                                            </span>
                                        <?php endif; endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        Rs <?= number_format($invoice['total_amount'], 0) ?>
                                        <?php if (isset($invoice['amount_paid']) && $invoice['amount_paid'] > 0 && $invoice['amount_paid'] < $invoice['total_amount']): ?>
                                            <div class="text-xs text-green-600 dark:text-green-400">
                                                Rs <?= number_format($invoice['amount_paid'], 0) ?> paid
                                            </div>
                                            <div class="text-xs text-yellow-600 dark:text-yellow-400">
                                                Rs <?= number_format($invoice['amount_due'], 0) ?> due
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            <?php if ($invoice['status'] === 'paid'): ?>
                                                bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
                                            <?php elseif ($invoice['status'] === 'sent'): ?>
                                                bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200
                                            <?php elseif ($invoice['status'] === 'overdue'): ?>
                                                bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200
                                            <?php elseif ($invoice['status'] === 'draft'): ?>
                                                bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300
                                            <?php else: ?>
                                                bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200
                                            <?php endif; ?>">
                                            <?= ucfirst(htmlspecialchars($invoice['status'])) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="/momentum/freelance/invoices/view/<?= $invoice['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300 mr-2">View</a>
                                        <a href="/momentum/freelance/invoices/edit/<?= $invoice['id'] ?>" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-2">Edit</a>
                                        <a href="/momentum/freelance/invoices/print/<?= $invoice['id'] ?>" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300">Print</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
