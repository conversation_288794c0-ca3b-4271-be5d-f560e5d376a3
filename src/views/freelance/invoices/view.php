<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/freelance/invoices" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
                <span class="ml-1">Back to Invoices</span>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Invoice #<?= isset($invoice['invoice_number']) ? htmlspecialchars($invoice['invoice_number']) : 'Details' ?>
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Invoice Information</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                        Invoice details and status
                    </p>
                </div>
                <div class="flex space-x-2">
                    <a href="/momentum/freelance/invoices/edit/<?= isset($invoice['id']) ? $invoice['id'] : '0' ?>" class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-edit mr-2"></i> Edit
                    </a>
                    <a href="/momentum/freelance/invoices/print/<?= isset($invoice['id']) ? $invoice['id'] : '0' ?>" class="inline-flex items-center px-3 py-2 border border-blue-300 dark:border-blue-700 rounded-md shadow-sm text-sm font-medium text-blue-700 dark:text-blue-300 bg-white dark:bg-gray-700 hover:bg-blue-50 dark:hover:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-print mr-2"></i> Print
                    </a>
                    <a href="/momentum/freelance/invoices/delete/<?= isset($invoice['id']) ? $invoice['id'] : '0' ?>" class="inline-flex items-center px-3 py-2 border border-red-300 dark:border-red-700 rounded-md shadow-sm text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200" onclick="return confirm('Are you sure you want to delete this invoice?');">
                        <i class="fas fa-trash-alt mr-2"></i> Delete
                    </a>
                </div>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <p class="text-gray-500 dark:text-gray-400 italic">Invoice view template - This is a placeholder. The full implementation will be added later.</p>
            </div>
        </div>
    </div>
</div>
