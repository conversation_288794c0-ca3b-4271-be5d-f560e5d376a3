<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/freelance" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
                <span class="ml-1">Back to Dashboard</span>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Income Report</h1>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <form action="/momentum/freelance/reports/income" method="get" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="period" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Period</label>
                        <select name="period" id="period" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="this_month" <?= isset($filters['period']) && $filters['period'] === 'this_month' ? 'selected' : '' ?>>This Month</option>
                            <option value="last_month" <?= isset($filters['period']) && $filters['period'] === 'last_month' ? 'selected' : '' ?>>Last Month</option>
                            <option value="this_quarter" <?= isset($filters['period']) && $filters['period'] === 'this_quarter' ? 'selected' : '' ?>>This Quarter</option>
                            <option value="last_quarter" <?= isset($filters['period']) && $filters['period'] === 'last_quarter' ? 'selected' : '' ?>>Last Quarter</option>
                            <option value="this_year" <?= isset($filters['period']) && $filters['period'] === 'this_year' ? 'selected' : '' ?>>This Year</option>
                            <option value="last_year" <?= isset($filters['period']) && $filters['period'] === 'last_year' ? 'selected' : '' ?>>Last Year</option>
                            <option value="custom" <?= isset($filters['period']) && $filters['period'] === 'custom' ? 'selected' : '' ?>>Custom Range</option>
                        </select>
                    </div>
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                        <input type="date" name="start_date" id="start_date" value="<?= isset($filters['start_date']) ? htmlspecialchars($filters['start_date']) : '' ?>" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                    </div>
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">End Date</label>
                        <input type="date" name="end_date" id="end_date" value="<?= isset($filters['end_date']) ? htmlspecialchars($filters['end_date']) : '' ?>" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                    </div>
                    <div>
                        <label for="client_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Client</label>
                        <select name="client_id" id="client_id" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Clients</option>
                            <?php if (isset($clients) && is_array($clients)): ?>
                                <?php foreach ($clients as $client): ?>
                                    <option value="<?= $client['id'] ?>" <?= isset($filters['client_id']) && $filters['client_id'] == $client['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($client['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-filter mr-2"></i> Apply Filters
                    </button>
                    <a href="/momentum/freelance/reports/income" class="ml-2 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i> Clear
                    </a>
                </div>
            </form>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-3">
                        <i class="fas fa-money-bill-wave text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Income</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">Rs <?= number_format($summary['total_income'] ?? 0, 0) ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-md p-3">
                        <i class="fas fa-file-invoice-dollar text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Invoiced</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">Rs <?= number_format($summary['invoiced_amount'] ?? 0, 0) ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-yellow-100 dark:bg-yellow-900 rounded-md p-3">
                        <i class="fas fa-exclamation-circle text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Outstanding</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">Rs <?= number_format($summary['outstanding_amount'] ?? 0, 0) ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-purple-100 dark:bg-purple-900 rounded-md p-3">
                        <i class="fas fa-project-diagram text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Projects</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $summary['project_count'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Income Chart -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Income Over Time</h2>
            <div class="h-64">
                <canvas id="incomeChart"></canvas>
            </div>
        </div>

        <!-- Income by Client -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Income by Client</h3>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($clientIncome)): ?>
                    <p class="text-gray-500 dark:text-gray-400 text-center py-4">No income data available for the selected period.</p>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Client</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Projects</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Invoices</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Income</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Outstanding</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">% of Total</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($clientIncome as $client): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                <?= htmlspecialchars($client['name']) ?>
                                            </div>
                                            <?php if (!empty($client['company'])): ?>
                                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                                    <?= htmlspecialchars($client['company']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= $client['project_count'] ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= $client['invoice_count'] ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            Rs <?= number_format($client['income'], 0) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            Rs <?= number_format($client['outstanding'], 0) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-900 dark:text-white mr-2"><?= number_format($client['percentage'], 1) ?>%</span>
                                                <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                                    <div class="bg-primary-600 h-2.5 rounded-full" style="width: <?= $client['percentage'] ?>%"></div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Income by Project -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Income by Project</h3>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($projectIncome)): ?>
                    <p class="text-gray-500 dark:text-gray-400 text-center py-4">No project income data available for the selected period.</p>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Project</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Client</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Income</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Outstanding</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">% of Total</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($projectIncome as $project): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                <?= htmlspecialchars($project['name']) ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= htmlspecialchars($project['client_name']) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                <?php if ($project['status'] === 'completed'): ?>
                                                    bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
                                                <?php elseif ($project['status'] === 'in_progress'): ?>
                                                    bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200
                                                <?php elseif ($project['status'] === 'on_hold'): ?>
                                                    bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200
                                                <?php else: ?>
                                                    bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300
                                                <?php endif; ?>">
                                                <?= ucfirst(str_replace('_', ' ', htmlspecialchars($project['status']))) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            Rs <?= number_format($project['income'], 0) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            Rs <?= number_format($project['outstanding'], 0) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-900 dark:text-white mr-2"><?= number_format($project['percentage'], 1) ?>%</span>
                                                <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                                    <div class="bg-primary-600 h-2.5 rounded-full" style="width: <?= $project['percentage'] ?>%"></div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Income Chart
        const ctx = document.getElementById('incomeChart').getContext('2d');
        
        // Sample data - this would be replaced with actual data from PHP
        const incomeData = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Income',
                data: [5000, 7500, 10000, 8000, 12000, 9000, 11000, 13000, 7000, 9500, 11500, 15000],
                backgroundColor: 'rgba(79, 70, 229, 0.2)',
                borderColor: 'rgba(79, 70, 229, 1)',
                borderWidth: 2,
                tension: 0.3
            }]
        };
        
        new Chart(ctx, {
            type: 'line',
            data: incomeData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rs ' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Income: Rs ' + context.raw.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
        
        // Show/hide date fields based on period selection
        const periodSelect = document.getElementById('period');
        const startDateField = document.getElementById('start_date');
        const endDateField = document.getElementById('end_date');
        
        function toggleDateFields() {
            const isCustom = periodSelect.value === 'custom';
            startDateField.parentElement.style.display = isCustom ? 'block' : 'none';
            endDateField.parentElement.style.display = isCustom ? 'block' : 'none';
        }
        
        periodSelect.addEventListener('change', toggleDateFields);
        toggleDateFields(); // Initial state
    });
</script>
