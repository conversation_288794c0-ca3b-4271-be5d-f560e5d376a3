<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/freelance/payments/view/<?= isset($payment['id']) ? $payment['id'] : '0' ?>" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
                <span class="ml-1">Back to Payment</span>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Edit Payment
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Payment Information</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                    Update the payment details
                </p>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <form action="/momentum/freelance/payments/update/<?= isset($payment['id']) ? $payment['id'] : '0' ?>" method="POST">
                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                        <div class="sm:col-span-3">
                            <label for="client_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Client *</label>
                            <div class="mt-1">
                                <select name="client_id" id="client_id" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required>
                                    <option value="">Select Client</option>
                                    <?php if (isset($clients) && is_array($clients)): ?>
                                        <?php foreach ($clients as $client): ?>
                                            <option value="<?= $client['id'] ?>" <?= (isset($payment['client_id']) && $payment['client_id'] == $client['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($client['name']) ?> <?= !empty($client['company']) ? '(' . htmlspecialchars($client['company']) . ')' : '' ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <label for="invoice_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Invoice</label>
                            <div class="mt-1">
                                <select name="invoice_id" id="invoice_id" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                    <option value="">Select Invoice (Optional)</option>
                                    <?php if (isset($invoices) && is_array($invoices)): ?>
                                        <?php foreach ($invoices as $invoice): ?>
                                            <option value="<?= $invoice['id'] ?>" data-client="<?= $invoice['client_id'] ?>" <?= (isset($payment['invoice_id']) && $payment['invoice_id'] == $invoice['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($invoice['invoice_number']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <label for="project_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Project</label>
                            <div class="mt-1">
                                <select name="project_id" id="project_id" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                    <option value="">Select Project (Optional)</option>
                                    <?php if (isset($projects) && is_array($projects)): ?>
                                        <?php foreach ($projects as $project): ?>
                                            <option value="<?= $project['id'] ?>" data-client="<?= $project['client_id'] ?>" <?= (isset($payment['project_id']) && $payment['project_id'] == $project['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($project['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Amount (Rs) *</label>
                            <div class="mt-1">
                                <input type="number" name="amount" id="amount" step="0.01" min="0" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required value="<?= isset($payment['amount']) ? htmlspecialchars($payment['amount']) : '' ?>">
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <label for="payment_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Payment Date *</label>
                            <div class="mt-1">
                                <input type="date" name="payment_date" id="payment_date" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required value="<?= isset($payment['payment_date']) ? htmlspecialchars($payment['payment_date']) : '' ?>">
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <label for="payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Payment Method *</label>
                            <div class="mt-1">
                                <select name="payment_method" id="payment_method" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required>
                                    <option value="">Select Payment Method</option>
                                    <option value="bank_transfer" <?= (isset($payment['payment_method']) && $payment['payment_method'] === 'bank_transfer') ? 'selected' : '' ?>>Bank Transfer</option>
                                    <option value="cash" <?= (isset($payment['payment_method']) && $payment['payment_method'] === 'cash') ? 'selected' : '' ?>>Cash</option>
                                    <option value="credit_card" <?= (isset($payment['payment_method']) && $payment['payment_method'] === 'credit_card') ? 'selected' : '' ?>>Credit Card</option>
                                    <option value="paypal" <?= (isset($payment['payment_method']) && $payment['payment_method'] === 'paypal') ? 'selected' : '' ?>>PayPal</option>
                                    <option value="check" <?= (isset($payment['payment_method']) && $payment['payment_method'] === 'check') ? 'selected' : '' ?>>Check</option>
                                    <option value="other" <?= (isset($payment['payment_method']) && $payment['payment_method'] === 'other') ? 'selected' : '' ?>>Other</option>
                                </select>
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <label for="transaction_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Transaction ID</label>
                            <div class="mt-1">
                                <input type="text" name="transaction_id" id="transaction_id" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= isset($payment['transaction_id']) ? htmlspecialchars($payment['transaction_id']) : '' ?>">
                            </div>
                        </div>



                        <div class="sm:col-span-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                            <div class="mt-1">
                                <textarea name="notes" id="notes" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"><?= isset($payment['notes']) ? htmlspecialchars($payment['notes']) : '' ?></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <a href="/momentum/freelance/payments/view/<?= isset($payment['id']) ? $payment['id'] : '0' ?>" class="bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Cancel
                        </a>
                        <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Update Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Link invoice selection to client
        document.getElementById('invoice_id').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                const clientId = selectedOption.getAttribute('data-client');
                if (clientId) {
                    document.getElementById('client_id').value = clientId;
                }
            }
        });

        // Link project selection to client
        document.getElementById('project_id').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                const clientId = selectedOption.getAttribute('data-client');
                if (clientId) {
                    document.getElementById('client_id').value = clientId;
                }
            }
        });

        // Link client selection to filter invoices and projects
        document.getElementById('client_id').addEventListener('change', function() {
            const clientId = this.value;

            // Filter invoices
            const invoiceSelect = document.getElementById('invoice_id');
            invoiceSelect.value = '';

            Array.from(invoiceSelect.options).forEach(option => {
                if (option.value === '') return; // Skip the placeholder option

                const invoiceClientId = option.getAttribute('data-client');
                if (clientId === '' || invoiceClientId === clientId) {
                    option.style.display = '';
                } else {
                    option.style.display = 'none';
                }
            });

            // Filter projects
            const projectSelect = document.getElementById('project_id');
            projectSelect.value = '';

            Array.from(projectSelect.options).forEach(option => {
                if (option.value === '') return; // Skip the placeholder option

                const projectClientId = option.getAttribute('data-client');
                if (clientId === '' || projectClientId === clientId) {
                    option.style.display = '';
                } else {
                    option.style.display = 'none';
                }
            });
        });
    });
</script>
