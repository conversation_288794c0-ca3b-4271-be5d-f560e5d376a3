<?php
/**
 * AI Prompt Library View
 */
?>

<link rel="stylesheet" href="/momentum/public/css/tools.css">
<link rel="stylesheet" href="/momentum/public/css/gallery-enhanced.css">

<div class="tools-container">
    <!-- Header -->
    <div class="gallery-header">
        <div class="flex items-center">
            <a href="/momentum/ai-prompts" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 mr-4 text-xl">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="gallery-title">Prompt Library</h1>
                <p class="gallery-subtitle">Browse, search, and manage your collection of AI prompts</p>
            </div>
        </div>

        <div class="flex space-x-3">
            <a href="/momentum/ai-prompts/create" class="inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-plus mr-2"></i>
                Create New Prompt
            </a>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="filter-container">
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2 flex items-center justify-center">
                <i class="fas fa-search mr-3 text-purple-500"></i>
                Find Your Perfect Prompt
            </h2>
            <p class="text-gray-600 dark:text-gray-400">Use filters and search to discover exactly what you need</p>
        </div>

            <form method="GET" action="/momentum/ai-prompts/library" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    <!-- Search -->
                    <div class="lg:col-span-2">
                        <label for="search" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                            Search Prompts
                        </label>
                        <div class="relative">
                            <input type="text"
                                   id="search"
                                   name="search"
                                   value="<?= htmlspecialchars($filters['search'] ?? '') ?>"
                                   class="w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-lg"
                                   placeholder="Search by title, description, or tags...">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400 text-lg"></i>
                            </div>
                        </div>
                    </div>

                <!-- Category Filter -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Category
                    </label>
                    <select id="category"
                            name="category"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option value="">All Categories</option>
                        <?php if (!empty($categories)): ?>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>"
                                        <?= (isset($filters['category']) && $filters['category'] == $category['id']) ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($category['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>

                <!-- Sort -->
                <div>
                    <label for="sort" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Sort By
                    </label>
                    <select id="sort"
                            name="sort"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option value="created_desc" <?= (isset($filters['sort']) && $filters['sort'] == 'created_desc') ? 'selected' : '' ?>>Newest First</option>
                        <option value="created_asc" <?= (isset($filters['sort']) && $filters['sort'] == 'created_asc') ? 'selected' : '' ?>>Oldest First</option>
                        <option value="title_asc" <?= (isset($filters['sort']) && $filters['sort'] == 'title_asc') ? 'selected' : '' ?>>Title A-Z</option>
                        <option value="title_desc" <?= (isset($filters['sort']) && $filters['sort'] == 'title_desc') ? 'selected' : '' ?>>Title Z-A</option>
                        <option value="usage_desc" <?= (isset($filters['sort']) && $filters['sort'] == 'usage_desc') ? 'selected' : '' ?>>Most Used</option>
                        <option value="rating_desc" <?= (isset($filters['sort']) && $filters['sort'] == 'rating_desc') ? 'selected' : '' ?>>Highest Rated</option>
                    </select>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <label class="flex items-center">
                        <input type="checkbox"
                               name="favorites_only"
                               value="1"
                               <?= (isset($filters['favorites_only']) && $filters['favorites_only']) ? 'checked' : '' ?>
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Favorites Only</span>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox"
                               name="templates_only"
                               value="1"
                               <?= (isset($filters['templates_only']) && $filters['templates_only']) ? 'checked' : '' ?>
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Templates Only</span>
                    </label>
                </div>

                <div class="flex space-x-2">
                    <button type="submit" class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-filter mr-2"></i>
                        Apply Filters
                    </button>

                    <a href="/momentum/ai-prompts/library" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Prompts Grid -->
    <?php if (!empty($prompts)): ?>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php foreach ($prompts as $prompt): ?>
            <div class="prompt-card bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
                <div class="p-6">
                    <!-- Header -->
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                                <?= htmlspecialchars($prompt['title']) ?>
                            </h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                                <?php if ($prompt['category_name']): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                          style="background-color: <?= $prompt['category_color'] ?? '#6366F1' ?>20; color: <?= $prompt['category_color'] ?? '#6366F1' ?>;">
                                        <i class="<?= $prompt['category_icon'] ?? 'fa-folder' ?> mr-1"></i>
                                        <?= htmlspecialchars($prompt['category_name']) ?>
                                    </span>
                                <?php endif; ?>

                                <?php if ($prompt['is_template']): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                        <i class="fas fa-bookmark mr-1"></i>
                                        Template
                                    </span>
                                <?php endif; ?>

                                <?php if ($prompt['is_favorite']): ?>
                                    <i class="fas fa-star text-yellow-500"></i>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="flex items-center space-x-1">
                            <button class="prompt-action-btn" onclick="toggleFavorite(<?= $prompt['id'] ?>)" title="Toggle Favorite">
                                <i class="fas fa-star <?= $prompt['is_favorite'] ? 'text-yellow-500' : 'text-gray-400' ?>"></i>
                            </button>

                            <div class="relative">
                                <button class="prompt-action-btn" onclick="toggleDropdown(<?= $prompt['id'] ?>)" title="More Actions">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>

                                <div id="dropdown-<?= $prompt['id'] ?>" class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-700 rounded-md shadow-lg z-10 border border-gray-200 dark:border-gray-600">
                                    <div class="py-1">
                                        <a href="/momentum/ai-prompts/view/<?= $prompt['id'] ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600">
                                            <i class="fas fa-eye mr-2"></i>View Details
                                        </a>
                                        <a href="/momentum/ai-prompts/execute/<?= $prompt['id'] ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600">
                                            <i class="fas fa-play mr-2"></i>Execute
                                        </a>
                                        <a href="/momentum/ai-prompts/edit/<?= $prompt['id'] ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600">
                                            <i class="fas fa-edit mr-2"></i>Edit
                                        </a>
                                        <a href="/momentum/ai-prompts/duplicate/<?= $prompt['id'] ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600">
                                            <i class="fas fa-copy mr-2"></i>Duplicate
                                        </a>
                                        <div class="border-t border-gray-200 dark:border-gray-600"></div>
                                        <button onclick="deletePrompt(<?= $prompt['id'] ?>)" class="block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-600">
                                            <i class="fas fa-trash mr-2"></i>Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <?php if ($prompt['description']): ?>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">
                        <?= htmlspecialchars($prompt['description']) ?>
                    </p>
                    <?php endif; ?>

                    <!-- Prompt Preview -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-3">
                        <p class="text-gray-700 dark:text-gray-300 text-sm font-mono line-clamp-3">
                            <?= htmlspecialchars(substr($prompt['prompt_text'], 0, 150)) ?><?= strlen($prompt['prompt_text']) > 150 ? '...' : '' ?>
                        </p>
                    </div>

                    <!-- Stats -->
                    <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-3">
                        <div class="flex items-center space-x-3">
                            <span>
                                <i class="fas fa-play mr-1"></i>
                                <?= $prompt['usage_count'] ?? 0 ?> uses
                            </span>

                            <?php if ($prompt['effectiveness_rating']): ?>
                            <span>
                                <i class="fas fa-star mr-1"></i>
                                <?= number_format($prompt['effectiveness_rating'], 1) ?>
                            </span>
                            <?php endif; ?>
                        </div>

                        <span><?= date('M j, Y', strtotime($prompt['created_at'])) ?></span>
                    </div>

                    <!-- Actions -->
                    <div class="flex space-x-2">
                        <a href="/momentum/ai-prompts/execute/<?= $prompt['id'] ?>" class="flex-1 inline-flex justify-center items-center px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-play mr-1"></i>
                            Execute
                        </a>

                        <a href="/momentum/ai-prompts/view/<?= $prompt['id'] ?>" class="flex-1 inline-flex justify-center items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-eye mr-1"></i>
                            View
                        </a>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <?php else: ?>
    <!-- Empty State -->
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="fas fa-brain"></i>
        </div>
        <h3 class="empty-state-title">No prompts found</h3>
        <p class="empty-state-description">
            <?php if (!empty($filters['search']) || !empty($filters['category'])): ?>
                Try adjusting your filters or search terms to find what you're looking for.
            <?php else: ?>
                Get started by creating your first AI prompt to build your library.
            <?php endif; ?>
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/momentum/ai-prompts/create" class="inline-flex items-center px-8 py-4 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-plus mr-3"></i>
                Create Your First Prompt
            </a>

            <?php if (!empty($filters['search']) || !empty($filters['category'])): ?>
            <a href="/momentum/ai-prompts/library" class="inline-flex items-center px-8 py-4 bg-gray-600 hover:bg-gray-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-times mr-3"></i>
                Clear Filters
            </a>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
    </div>
</div>

<script>
function toggleFavorite(promptId) {
    fetch(`/momentum/ai-prompts/toggle-favorite/${promptId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the star icon
            const starIcon = document.querySelector(`button[onclick="toggleFavorite(${promptId})"] i`);
            if (data.is_favorite) {
                starIcon.classList.remove('text-gray-400');
                starIcon.classList.add('text-yellow-500');
            } else {
                starIcon.classList.remove('text-yellow-500');
                starIcon.classList.add('text-gray-400');
            }
        }
    })
    .catch(error => {
        console.error('Error toggling favorite:', error);
    });
}

function toggleDropdown(promptId) {
    const dropdown = document.getElementById(`dropdown-${promptId}`);
    const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');

    // Close all other dropdowns
    allDropdowns.forEach(d => {
        if (d.id !== `dropdown-${promptId}`) {
            d.classList.add('hidden');
        }
    });

    // Toggle current dropdown
    dropdown.classList.toggle('hidden');
}

function deletePrompt(promptId) {
    if (confirm('Are you sure you want to delete this prompt? This action cannot be undone.')) {
        fetch(`/momentum/ai-prompts/delete/${promptId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the prompt card from the page
                const promptCard = document.querySelector(`button[onclick="deletePrompt(${promptId})"]`).closest('.prompt-card');
                promptCard.remove();

                // Show success message
                showNotification('Prompt deleted successfully', 'success');
            } else {
                showNotification('Error deleting prompt', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting prompt:', error);
            showNotification('Error deleting prompt', 'error');
        });
    }
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    } text-white`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('[onclick^="toggleDropdown"]') && !event.target.closest('[id^="dropdown-"]')) {
        const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
        allDropdowns.forEach(dropdown => {
            dropdown.classList.add('hidden');
        });
    }
});
</script>
