<?php
/**
 * Create AI Prompt View
 */
?>

<div class="ai-prompt-container">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
            <a href="/momentum/ai-prompts" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 mr-4">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Create New Prompt</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Build a custom AI prompt with variables and templates</p>
            </div>
        </div>
        <div class="flex space-x-3">
            <a href="/momentum/ai-prompts/templates" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200">
                <i class="fas fa-book mr-2"></i>
                Browse Templates
            </a>
        </div>
    </div>

    <!-- Error Messages -->
    <?php if (!empty($errors)): ?>
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-circle text-red-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                    Please correct the following errors:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                    <ul class="list-disc list-inside space-y-1">
                        <?php foreach ($errors as $error): ?>
                            <li><?= htmlspecialchars($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Create Form -->
    <form action="/momentum/ai-prompts/store" method="POST" class="space-y-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Basic Information</h2>

                    <div class="space-y-4">
                        <!-- Title -->
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Title <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="title"
                                   name="title"
                                   value="<?= htmlspecialchars($data['title'] ?? '') ?>"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                   placeholder="Enter a descriptive title for your prompt"
                                   required>
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Description
                            </label>
                            <textarea id="description"
                                      name="description"
                                      rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                      placeholder="Describe what this prompt does and when to use it"><?= htmlspecialchars($data['description'] ?? '') ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Prompt Content -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Prompt Content</h2>
                        <div class="flex space-x-2">
                            <button type="button" id="addVariableBtn" class="inline-flex items-center px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 text-sm font-medium rounded-md hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors duration-200">
                                <i class="fas fa-plus mr-1"></i>
                                Add Variable
                            </button>
                            <button type="button" id="previewBtn" class="inline-flex items-center px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm font-medium rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                                <i class="fas fa-eye mr-1"></i>
                                Preview
                            </button>
                        </div>
                    </div>

                    <!-- Prompt Text -->
                    <div class="mb-4">
                        <label for="prompt_text" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Prompt Text <span class="text-red-500">*</span>
                        </label>
                        <textarea id="prompt_text"
                                  name="prompt_text"
                                  rows="8"
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm"
                                  placeholder="Enter your prompt text here. Use {{variable_name}} for dynamic variables."
                                  required><?= htmlspecialchars($data['prompt_text'] ?? '') ?></textarea>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Use double curly braces {{variable_name}} to create dynamic variables in your prompt.
                        </p>
                    </div>

                    <!-- Variables Section -->
                    <div id="variablesSection" class="space-y-3">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">Variables</h3>
                        <div id="variablesList" class="space-y-2">
                            <!-- Variables will be added here dynamically -->
                        </div>
                    </div>

                    <!-- Hidden field for variables JSON -->
                    <input type="hidden" id="variables_json" name="variables_json" value="">
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Category & Settings -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Settings</h2>

                    <div class="space-y-4">
                        <!-- Category -->
                        <div>
                            <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Category
                            </label>
                            <select id="category_id"
                                    name="category_id"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="">Select a category</option>
                                <?php if (!empty($categories)): ?>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?= $category['id'] ?>"
                                                <?= (isset($data['category_id']) && $data['category_id'] == $category['id']) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($category['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>

                        <!-- Tags -->
                        <div>
                            <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Tags
                            </label>
                            <input type="text"
                                   id="tags"
                                   name="tags"
                                   value="<?= htmlspecialchars($data['tags'] ?? '') ?>"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                   placeholder="tag1, tag2, tag3">
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                Separate tags with commas
                            </p>
                        </div>

                        <!-- Options -->
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <input type="checkbox"
                                       id="is_template"
                                       name="is_template"
                                       value="1"
                                       <?= (isset($data['is_template']) && $data['is_template']) ? 'checked' : '' ?>
                                       class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 dark:border-gray-600 rounded">
                                <label for="is_template" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Save as template
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox"
                                       id="is_favorite"
                                       name="is_favorite"
                                       value="1"
                                       <?= (isset($data['is_favorite']) && $data['is_favorite']) ? 'checked' : '' ?>
                                       class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 dark:border-gray-600 rounded">
                                <label for="is_favorite" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Add to favorites
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox"
                                       id="is_public"
                                       name="is_public"
                                       value="1"
                                       <?= (isset($data['is_public']) && $data['is_public']) ? 'checked' : '' ?>
                                       class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 dark:border-gray-600 rounded">
                                <label for="is_public" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Make public
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="space-y-3">
                        <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-save mr-2"></i>
                            Create Prompt
                        </button>

                        <button type="button" id="saveAndExecuteBtn" class="w-full inline-flex justify-center items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-play mr-2"></i>
                            Save & Execute
                        </button>

                        <a href="/momentum/ai-prompts" class="w-full inline-flex justify-center items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-times mr-2"></i>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Preview Modal -->
<div id="previewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Prompt Preview</h3>
                <button type="button" id="closePreviewBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="previewContent" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 font-mono text-sm text-gray-900 dark:text-white whitespace-pre-wrap">
                <!-- Preview content will be inserted here -->
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let variableCounter = 0;
    const variablesList = document.getElementById('variablesList');
    const variablesJson = document.getElementById('variables_json');
    const promptText = document.getElementById('prompt_text');

    // Add variable functionality
    document.getElementById('addVariableBtn').addEventListener('click', function() {
        addVariable();
    });

    // Preview functionality
    document.getElementById('previewBtn').addEventListener('click', function() {
        showPreview();
    });

    document.getElementById('closePreviewBtn').addEventListener('click', function() {
        document.getElementById('previewModal').classList.add('hidden');
    });

    // Auto-detect variables in prompt text
    promptText.addEventListener('input', function() {
        autoDetectVariables();
    });

    function addVariable(name = '', description = '', type = 'text', defaultValue = '') {
        variableCounter++;
        const variableId = `variable_${variableCounter}`;

        const variableHtml = `
            <div class="variable-item bg-gray-50 dark:bg-gray-700 rounded-lg p-3" data-variable-id="${variableId}">
                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Variable Name</label>
                        <input type="text" class="variable-name w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white" value="${name}" placeholder="variable_name">
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Type</label>
                        <select class="variable-type w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
                            <option value="text" ${type === 'text' ? 'selected' : ''}>Text</option>
                            <option value="textarea" ${type === 'textarea' ? 'selected' : ''}>Long Text</option>
                            <option value="number" ${type === 'number' ? 'selected' : ''}>Number</option>
                            <option value="select" ${type === 'select' ? 'selected' : ''}>Dropdown</option>
                        </select>
                    </div>
                </div>
                <div class="mt-2">
                    <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
                    <input type="text" class="variable-description w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white" value="${description}" placeholder="Describe this variable">
                </div>
                <div class="mt-2">
                    <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Default Value</label>
                    <input type="text" class="variable-default w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white" value="${defaultValue}" placeholder="Optional default value">
                </div>
                <div class="mt-2 flex justify-end">
                    <button type="button" class="remove-variable text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-sm">
                        <i class="fas fa-trash mr-1"></i>Remove
                    </button>
                </div>
            </div>
        `;

        variablesList.insertAdjacentHTML('beforeend', variableHtml);

        // Add event listeners
        const variableItem = variablesList.lastElementChild;
        variableItem.querySelector('.remove-variable').addEventListener('click', function() {
            variableItem.remove();
            updateVariablesJson();
        });

        variableItem.querySelectorAll('input, select').forEach(input => {
            input.addEventListener('input', updateVariablesJson);
        });

        updateVariablesJson();
    }

    function autoDetectVariables() {
        const text = promptText.value;
        const variablePattern = /\{\{([^}]+)\}\}/g;
        const foundVariables = [];
        let match;

        while ((match = variablePattern.exec(text)) !== null) {
            const variableName = match[1].trim();
            if (variableName && !foundVariables.includes(variableName)) {
                foundVariables.push(variableName);
            }
        }

        // Check if we need to add any new variables
        const existingVariables = Array.from(variablesList.querySelectorAll('.variable-name')).map(input => input.value);

        foundVariables.forEach(varName => {
            if (!existingVariables.includes(varName)) {
                addVariable(varName, '', 'text', '');
            }
        });
    }

    function updateVariablesJson() {
        const variables = [];
        const variableItems = variablesList.querySelectorAll('.variable-item');

        variableItems.forEach(item => {
            const name = item.querySelector('.variable-name').value;
            const type = item.querySelector('.variable-type').value;
            const description = item.querySelector('.variable-description').value;
            const defaultValue = item.querySelector('.variable-default').value;

            if (name) {
                variables.push({
                    name: name,
                    type: type,
                    description: description,
                    default_value: defaultValue
                });
            }
        });

        variablesJson.value = JSON.stringify(variables);
    }

    function showPreview() {
        const title = document.getElementById('title').value;
        const description = document.getElementById('description').value;
        const text = promptText.value;

        let previewContent = '';

        if (title) {
            previewContent += `Title: ${title}\n\n`;
        }

        if (description) {
            previewContent += `Description: ${description}\n\n`;
        }

        previewContent += `Prompt:\n${text}`;

        // Highlight variables
        previewContent = previewContent.replace(/\{\{([^}]+)\}\}/g, '<span class="bg-purple-200 dark:bg-purple-800 px-1 rounded">{{$1}}</span>');

        document.getElementById('previewContent').innerHTML = previewContent;
        document.getElementById('previewModal').classList.remove('hidden');
    }

    // Save and execute functionality
    document.getElementById('saveAndExecuteBtn').addEventListener('click', function() {
        // Add a hidden field to indicate save and execute
        const form = document.querySelector('form');
        const hiddenField = document.createElement('input');
        hiddenField.type = 'hidden';
        hiddenField.name = 'save_and_execute';
        hiddenField.value = '1';
        form.appendChild(hiddenField);

        form.submit();
    });
});
</script>
