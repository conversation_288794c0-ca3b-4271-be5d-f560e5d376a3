<?php
/**
 * AI Prompts Dashboard View
 */
?>

<link rel="stylesheet" href="/momentum/public/css/tools.css">
<link rel="stylesheet" href="/momentum/public/css/gallery-enhanced.css">

<div class="tools-container">
    <!-- Header -->
    <div class="gallery-header">
        <div class="flex items-center">
            <a href="/momentum/tools" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 mr-4 text-xl">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="gallery-title">AI Prompts</h1>
                <p class="gallery-subtitle">Create, manage, and execute powerful AI prompts to boost your productivity</p>
            </div>
        </div>

        <div class="flex space-x-3">
            <a href="/momentum/ai-prompts/create" class="inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-plus mr-2"></i>
                Create New Prompt
            </a>
            <a href="/momentum/ai-prompts/library" class="inline-flex items-center px-6 py-3 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 font-semibold rounded-lg border border-gray-200 dark:border-gray-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-book mr-2"></i>
                Browse Library
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="capture-card bg-white dark:bg-gray-800 rounded-xl shadow-soft hover:shadow-lg transition-all duration-300 p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">Total Prompts</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= $stats['overview']['total_prompts'] ?? 0 ?></p>
                </div>
                <div class="bg-gradient-to-r from-purple-500 to-purple-600 p-3 rounded-xl shadow-lg">
                    <i class="fas fa-brain text-white text-xl"></i>
                </div>
            </div>
        </div>

        <div class="capture-card bg-white dark:bg-gray-800 rounded-xl shadow-soft hover:shadow-lg transition-all duration-300 p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">Favorites</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= $stats['overview']['favorite_prompts'] ?? 0 ?></p>
                </div>
                <div class="bg-gradient-to-r from-yellow-500 to-orange-500 p-3 rounded-xl shadow-lg">
                    <i class="fas fa-star text-white text-xl"></i>
                </div>
            </div>
        </div>

        <div class="capture-card bg-white dark:bg-gray-800 rounded-xl shadow-soft hover:shadow-lg transition-all duration-300 p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">Templates</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= $stats['overview']['template_prompts'] ?? 0 ?></p>
                </div>
                <div class="bg-gradient-to-r from-blue-500 to-indigo-600 p-3 rounded-xl shadow-lg">
                    <i class="fas fa-layer-group text-white text-xl"></i>
                </div>
            </div>
        </div>

        <div class="capture-card bg-white dark:bg-gray-800 rounded-xl shadow-soft hover:shadow-lg transition-all duration-300 p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">Total Usage</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= $stats['overview']['total_usage'] ?? 0 ?></p>
                </div>
                <div class="bg-gradient-to-r from-green-500 to-emerald-600 p-3 rounded-xl shadow-lg">
                    <i class="fas fa-chart-line text-white text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-8 mb-8">
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2 flex items-center justify-center">
                <i class="fas fa-bolt mr-3 text-purple-500"></i>
                Quick Actions
            </h2>
            <p class="text-gray-600 dark:text-gray-400">Get started with these common tasks</p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <a href="/momentum/ai-prompts/create" class="capture-card group flex flex-col items-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 hover:from-purple-100 hover:to-purple-200 dark:hover:from-purple-800/30 dark:hover:to-purple-700/30 rounded-xl border border-purple-200 dark:border-purple-700 transition-all duration-300 transform hover:-translate-y-2 hover:shadow-lg">
                <div class="bg-gradient-to-r from-purple-500 to-purple-600 p-4 rounded-full mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <i class="fas fa-plus text-white text-xl"></i>
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Create Prompt</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 text-center">Build a new AI prompt from scratch</p>
            </a>

            <a href="/momentum/ai-prompts/library" class="capture-card group flex flex-col items-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-800/30 dark:hover:to-blue-700/30 rounded-xl border border-blue-200 dark:border-blue-700 transition-all duration-300 transform hover:-translate-y-2 hover:shadow-lg">
                <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-4 rounded-full mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <i class="fas fa-layer-group text-white text-xl"></i>
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Browse Templates</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 text-center">Explore pre-built prompt templates</p>
            </a>

            <a href="/momentum/ai-prompts/categories" class="capture-card group flex flex-col items-center p-6 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 hover:from-green-100 hover:to-green-200 dark:hover:from-green-800/30 dark:hover:to-green-700/30 rounded-xl border border-green-200 dark:border-green-700 transition-all duration-300 transform hover:-translate-y-2 hover:shadow-lg">
                <div class="bg-gradient-to-r from-green-500 to-green-600 p-4 rounded-full mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <i class="fas fa-tags text-white text-xl"></i>
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Manage Categories</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 text-center">Organize your prompts by category</p>
            </a>

            <a href="/momentum/ai-prompts/analytics" class="capture-card group flex flex-col items-center p-6 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 hover:from-orange-100 hover:to-orange-200 dark:hover:from-orange-800/30 dark:hover:to-orange-700/30 rounded-xl border border-orange-200 dark:border-orange-700 transition-all duration-300 transform hover:-translate-y-2 hover:shadow-lg">
                <div class="bg-gradient-to-r from-orange-500 to-orange-600 p-4 rounded-full mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <i class="fas fa-chart-bar text-white text-xl"></i>
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">View Analytics</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 text-center">Track usage and performance</p>
            </a>
        </div>
    </div>

    <!-- Recent Prompts -->
    <?php if (!empty($recentPrompts)): ?>
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-8 mb-8">
        <div class="flex justify-between items-center mb-8">
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2 flex items-center">
                    <i class="fas fa-clock mr-3 text-blue-500"></i>
                    Recent Prompts
                </h2>
                <p class="text-gray-600 dark:text-gray-400">Your latest AI prompt creations</p>
            </div>
            <a href="/momentum/ai-prompts/library" class="inline-flex items-center px-4 py-2 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 hover:bg-purple-200 dark:hover:bg-purple-900/50 rounded-lg font-medium transition-colors duration-200">
                View All
                <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <?php foreach ($recentPrompts as $prompt): ?>
                <div class="capture-card bg-gray-50 dark:bg-gray-700/50 rounded-xl p-6 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 border border-gray-200 dark:border-gray-600 hover:shadow-md">
                    <div class="flex justify-between items-start mb-4">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2"><?= htmlspecialchars($prompt['title']) ?></h3>
                            <div class="flex items-center gap-3 text-sm text-gray-500 dark:text-gray-400">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                      style="background-color: <?= $prompt['category_color'] ?? '#6366F1' ?>20; color: <?= $prompt['category_color'] ?? '#6366F1' ?>;">
                                    <i class="<?= $prompt['category_icon'] ?? 'fa-folder' ?> mr-1"></i>
                                    <?= $prompt['category_name'] ?? 'Uncategorized' ?>
                                </span>
                                <span><?= date('M j, Y', strtotime($prompt['created_at'])) ?></span>
                            </div>
                        </div>
                    </div>

                    <?php if ($prompt['description']): ?>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2"><?= htmlspecialchars($prompt['description']) ?></p>
                    <?php endif; ?>

                    <div class="flex justify-between items-center">
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            Used <?= $prompt['usage_count'] ?> times
                            <?php if ($prompt['effectiveness_rating'] > 0): ?>
                                • Rating: <?= number_format($prompt['effectiveness_rating'], 1) ?>/10
                            <?php endif; ?>
                        </div>

                        <div class="capture-actions flex items-center gap-2">
                            <a href="/momentum/ai-prompts/execute/<?= $prompt['id'] ?>" class="capture-action-btn inline-flex items-center px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-lg transition-colors duration-200" title="Execute Prompt">
                                <i class="fas fa-play mr-1"></i>
                                Execute
                            </a>
                            <button onclick="toggleFavorite(<?= $prompt['id'] ?>)" class="capture-action-btn p-2 text-gray-400 hover:text-yellow-500 transition-colors duration-200 <?= $prompt['is_favorite'] ? 'text-yellow-500' : '' ?>" title="Toggle Favorite">
                                <i class="fas fa-star"></i>
                            </button>
                            <a href="/momentum/ai-prompts/view/<?= $prompt['id'] ?>" class="capture-action-btn p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Empty State -->
    <?php if (empty($recentPrompts) && empty($favoritePrompts) && empty($popularPrompts)): ?>
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="fas fa-brain"></i>
        </div>
        <h3 class="empty-state-title">No prompts yet</h3>
        <p class="empty-state-description">
            Get started by creating your first AI prompt or browsing our template library to find inspiration.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/momentum/ai-prompts/create" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200">
                <i class="fas fa-plus mr-3"></i>
                Create Your First Prompt
            </a>
            <a href="/momentum/ai-prompts/library" class="inline-flex items-center px-8 py-4 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 font-semibold rounded-lg shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 transform hover:-translate-y-1 transition-all duration-200">
                <i class="fas fa-layer-group mr-3"></i>
                Browse Templates
            </a>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function toggleFavorite(promptId) {
    fetch(`/momentum/ai-prompts/toggle-favorite/${promptId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload page to update UI
            location.reload();
        } else {
            alert('Failed to toggle favorite: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to toggle favorite');
    });
}
</script>
