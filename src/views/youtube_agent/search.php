<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">YouTube Search</h1>
        <a href="/momentum/youtube-agent" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
        </a>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Search for YouTube Videos</h2>
        
        <form action="/momentum/youtube-agent/execute-search" method="POST">
            <div class="mb-4">
                <label for="search_query" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search Query</label>
                <input type="text" id="search_query" name="search_query" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Enter keywords, topics, or specific video ideas" required>
            </div>
            
            <div class="mb-6">
                <label for="search_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search Type</label>
                <select id="search_type" name="search_type" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                    <option value="keyword">Keyword Search</option>
                    <option value="channel">Channel Search</option>
                    <option value="trending">Trending Videos</option>
                </select>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    <span id="search_type_help">Search by keywords, specific channel, or get trending videos.</span>
                </p>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-search mr-2"></i> Search
                </button>
            </div>
        </form>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Search Tips</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Finding Money-Making Opportunities</h3>
                <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                        <span>Search for "<strong>passive income ideas 2023</strong>" to find current trends</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                        <span>Try "<strong>make money online for beginners</strong>" for entry-level opportunities</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                        <span>Look for "<strong>side hustle no investment</strong>" for low-cost ideas</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                        <span>Search "<strong>affiliate marketing tutorial</strong>" for specific strategies</span>
                    </li>
                </ul>
            </div>
            
            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Effective Search Strategies</h3>
                <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <li class="flex items-start">
                        <i class="fas fa-lightbulb text-yellow-500 mt-1 mr-2"></i>
                        <span>Use specific niche keywords (e.g., "<strong>digital product ideas for coaches</strong>")</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-lightbulb text-yellow-500 mt-1 mr-2"></i>
                        <span>Search for successful channels in your target niche</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-lightbulb text-yellow-500 mt-1 mr-2"></i>
                        <span>Look for "<strong>how to</strong>" videos to find problems people need solutions for</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-lightbulb text-yellow-500 mt-1 mr-2"></i>
                        <span>Check trending videos to identify current opportunities</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchTypeSelect = document.getElementById('search_type');
        const searchTypeHelp = document.getElementById('search_type_help');
        const searchQueryInput = document.getElementById('search_query');
        
        function updateHelpText() {
            const searchType = searchTypeSelect.value;
            
            switch (searchType) {
                case 'keyword':
                    searchTypeHelp.textContent = 'Search for videos by keywords or phrases.';
                    searchQueryInput.placeholder = 'Enter keywords, topics, or specific video ideas';
                    break;
                case 'channel':
                    searchTypeHelp.textContent = 'Search for videos from a specific channel. Enter channel name or ID.';
                    searchQueryInput.placeholder = 'Enter channel name or ID (e.g., MrBeast, UCX6OQ3DkcsbYNE6H8uQQuVA)';
                    break;
                case 'trending':
                    searchTypeHelp.textContent = 'Get currently trending videos. No search query needed.';
                    searchQueryInput.placeholder = 'Optional: Enter region code (default: US)';
                    break;
            }
        }
        
        searchTypeSelect.addEventListener('change', updateHelpText);
        updateHelpText();
    });
</script>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
