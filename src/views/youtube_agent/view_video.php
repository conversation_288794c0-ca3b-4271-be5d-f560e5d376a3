<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white line-clamp-1"><?= htmlspecialchars($video['title']) ?></h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">
                <a href="https://www.youtube.com/channel/<?= $video['channel_id'] ?>" target="_blank" class="hover:text-primary-600 dark:hover:text-primary-400">
                    <?= htmlspecialchars($video['channel_title']) ?>
                </a>
            </p>
        </div>
        <div class="flex space-x-2">
            <a href="javascript:history.back()" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-arrow-left mr-2"></i> Back
            </a>
            <a href="/momentum/youtube-agent" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-home mr-2"></i> Dashboard
            </a>
        </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-2">
            <!-- Video Player -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
                <div class="aspect-w-16 aspect-h-9">
                    <iframe 
                        src="https://www.youtube.com/embed/<?= $video['youtube_id'] ?>" 
                        frameborder="0" 
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                        allowfullscreen
                        class="w-full h-full"
                    ></iframe>
                </div>
                <div class="p-4">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white"><?= htmlspecialchars($video['title']) ?></h2>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                <?= number_format($video['view_count']) ?> views • 
                                <?= date('M j, Y', strtotime($video['published_at'])) ?>
                            </p>
                        </div>
                        <div class="flex space-x-2">
                            <a href="https://www.youtube.com/watch?v=<?= $video['youtube_id'] ?>" target="_blank" class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <i class="fab fa-youtube mr-1"></i> Watch on YouTube
                            </a>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="flex items-center">
                            <i class="fas fa-thumbs-up text-gray-600 dark:text-gray-400 mr-1"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400"><?= number_format($video['like_count']) ?></span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-comment text-gray-600 dark:text-gray-400 mr-1"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400"><?= number_format($video['comment_count']) ?></span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-clock text-gray-600 dark:text-gray-400 mr-1"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400"><?= $video['duration'] ?></span>
                        </div>
                    </div>
                    
                    <?php if (!empty($video['tags'])): ?>
                        <div class="mb-4">
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags:</h3>
                            <div class="flex flex-wrap gap-1">
                                <?php foreach ($video['tags'] as $tag): ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                        <?= htmlspecialchars($tag) ?>
                                    </span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description:</h3>
                        <div class="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line">
                            <?= nl2br(htmlspecialchars($video['description'])) ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Analyses Section -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Analyses</h2>
                </div>
                <div class="p-4">
                    <?php if (empty($analyses)): ?>
                        <div class="text-center py-4">
                            <p class="text-gray-500 dark:text-gray-400 mb-4">No analyses yet. Analyze this video to discover money-making opportunities.</p>
                            <form action="/momentum/youtube-agent/create-analysis/<?= $video['id'] ?>" method="POST" class="inline-block">
                                <input type="hidden" name="analysis_type" value="money_making">
                                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                    <i class="fas fa-chart-line mr-2"></i> Analyze Money-Making Opportunities
                                </button>
                            </form>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($analyses as $analysis): ?>
                                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <h3 class="text-md font-medium text-gray-900 dark:text-white">
                                            <?= ucfirst(str_replace('_', ' ', $analysis['analysis_type'])) ?> Analysis
                                        </h3>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= getStatusBadgeClass($analysis['status']) ?>">
                                            <?= ucfirst($analysis['status']) ?>
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                        Created: <?= date('M j, Y g:i A', strtotime($analysis['created_at'])) ?>
                                    </p>
                                    <?php if ($analysis['status'] === 'completed'): ?>
                                        <div class="mt-2">
                                            <a href="/momentum/youtube-agent/view-analysis/<?= $analysis['id'] ?>" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900 dark:text-primary-300 dark:hover:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                                <i class="fas fa-eye mr-1"></i> View Analysis
                                            </a>
                                        </div>
                                    <?php elseif ($analysis['status'] === 'failed'): ?>
                                        <div class="mt-2">
                                            <form action="/momentum/youtube-agent/create-analysis/<?= $video['id'] ?>" method="POST" class="inline-block">
                                                <input type="hidden" name="analysis_type" value="<?= $analysis['analysis_type'] ?>">
                                                <button type="submit" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900 dark:text-primary-300 dark:hover:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                                    <i class="fas fa-redo mr-1"></i> Retry Analysis
                                                </button>
                                            </form>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                            
                            <!-- New Analysis Button -->
                            <div class="mt-4">
                                <form action="/momentum/youtube-agent/create-analysis/<?= $video['id'] ?>" method="POST">
                                    <div class="flex items-center space-x-2">
                                        <select name="analysis_type" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                            <option value="money_making">Money Making Opportunities</option>
                                            <option value="content_ideas">Content Ideas</option>
                                        </select>
                                        <button type="submit" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                            <i class="fas fa-plus mr-1"></i> New Analysis
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="lg:col-span-1">
            <!-- Save Video Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Save Video</h2>
                </div>
                <div class="p-4">
                    <?php if ($isSaved): ?>
                        <div class="text-center py-2">
                            <p class="text-green-600 dark:text-green-400 mb-2">
                                <i class="fas fa-check-circle mr-1"></i> This video is saved
                            </p>
                            <a href="/momentum/youtube-agent/saved-videos" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <i class="fas fa-bookmark mr-2"></i> View Saved Videos
                            </a>
                        </div>
                    <?php else: ?>
                        <form action="/momentum/youtube-agent/save-video/<?= $video['id'] ?>" method="POST">
                            <div class="mb-4">
                                <label for="folder" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Folder (Optional)</label>
                                <select id="folder" name="folder" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                    <option value="">No Folder</option>
                                    <?php foreach ($folders as $folder): ?>
                                        <option value="<?= htmlspecialchars($folder) ?>"><?= htmlspecialchars($folder) ?></option>
                                    <?php endforeach; ?>
                                    <option value="__new__">+ Create New Folder</option>
                                </select>
                            </div>
                            
                            <div id="new-folder-input" class="mb-4 hidden">
                                <label for="new_folder" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Folder Name</label>
                                <input type="text" id="new_folder" name="new_folder" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            </div>
                            
                            <div class="mb-4">
                                <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notes (Optional)</label>
                                <textarea id="notes" name="notes" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"></textarea>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                    <i class="fas fa-bookmark mr-2"></i> Save Video
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Quick Actions Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Quick Actions</h2>
                </div>
                <div class="p-4">
                    <div class="grid grid-cols-2 gap-3">
                        <a href="https://www.youtube.com/watch?v=<?= $video['youtube_id'] ?>" target="_blank" class="flex flex-col items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600">
                            <i class="fab fa-youtube text-2xl text-red-600 mb-2"></i>
                            <span class="text-sm text-gray-700 dark:text-gray-300">Watch on YouTube</span>
                        </a>
                        <a href="#" onclick="copyToClipboard('https://www.youtube.com/watch?v=<?= $video['youtube_id'] ?>'); return false;" class="flex flex-col items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600">
                            <i class="fas fa-link text-2xl text-primary-600 mb-2"></i>
                            <span class="text-sm text-gray-700 dark:text-gray-300">Copy Link</span>
                        </a>
                        <a href="#" onclick="shareVideo('<?= $video['title'] ?>', '<?= $video['youtube_id'] ?>'); return false;" class="flex flex-col items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600">
                            <i class="fas fa-share-alt text-2xl text-primary-600 mb-2"></i>
                            <span class="text-sm text-gray-700 dark:text-gray-300">Share</span>
                        </a>
                        <form action="/momentum/youtube-agent/create-analysis/<?= $video['id'] ?>" method="POST" class="contents">
                            <input type="hidden" name="analysis_type" value="money_making">
                            <button type="submit" class="flex flex-col items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600">
                                <i class="fas fa-chart-line text-2xl text-primary-600 mb-2"></i>
                                <span class="text-sm text-gray-700 dark:text-gray-300">Analyze</span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Channel Info Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Channel</h2>
                </div>
                <div class="p-4">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 mr-3">
                            <div class="w-12 h-12 flex items-center justify-center bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 rounded-full">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-md font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($video['channel_title']) ?></h3>
                            <a href="https://www.youtube.com/channel/<?= $video['channel_id'] ?>" target="_blank" class="text-sm text-primary-600 hover:text-primary-800 dark:text-primary-400">
                                View Channel
                            </a>
                        </div>
                    </div>
                    
                    <div class="flex justify-center">
                        <a href="/momentum/youtube-agent/search" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-search mr-2"></i> Search This Channel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const folderSelect = document.getElementById('folder');
        const newFolderInput = document.getElementById('new-folder-input');
        
        if (folderSelect && newFolderInput) {
            folderSelect.addEventListener('change', function() {
                if (this.value === '__new__') {
                    newFolderInput.classList.remove('hidden');
                    // Create a hidden input to store the actual folder value
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'folder';
                    hiddenInput.id = 'hidden_folder';
                    this.parentNode.appendChild(hiddenInput);
                    
                    // Update the hidden input when the new folder name changes
                    document.getElementById('new_folder').addEventListener('input', function() {
                        document.getElementById('hidden_folder').value = this.value;
                    });
                } else {
                    newFolderInput.classList.add('hidden');
                    const hiddenInput = document.getElementById('hidden_folder');
                    if (hiddenInput) {
                        hiddenInput.remove();
                    }
                }
            });
        }
    });
    
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            alert('Link copied to clipboard!');
        }, function(err) {
            console.error('Could not copy text: ', err);
        });
    }
    
    function shareVideo(title, videoId) {
        if (navigator.share) {
            navigator.share({
                title: title,
                url: 'https://www.youtube.com/watch?v=' + videoId
            }).then(() => {
                console.log('Thanks for sharing!');
            }).catch(console.error);
        } else {
            copyToClipboard('https://www.youtube.com/watch?v=' + videoId);
        }
    }
</script>

<?php
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'pending':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
        case 'in_progress':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
        case 'completed':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
        case 'failed':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
}
?>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
