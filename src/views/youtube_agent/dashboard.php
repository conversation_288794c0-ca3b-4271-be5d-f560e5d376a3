<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">YouTube Agent</h1>
        <div class="flex space-x-2">
            <a href="/momentum/youtube-agent/search" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-search mr-2"></i> New Search
            </a>
            <a href="/momentum/youtube-agent/settings" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-cog mr-2"></i> Settings
            </a>
        </div>
    </div>
    
    <!-- API Quota Card -->
    <?php if ($quotaUsage): ?>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <div class="flex items-center justify-between mb-2">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">YouTube API Quota</h2>
            <a href="/momentum/youtube-agent/settings" class="text-sm text-primary-600 hover:text-primary-800 dark:text-primary-400">Manage</a>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-2">
            <?php $percentage = min(100, ($quotaUsage['quota_used'] / $quotaUsage['quota_limit']) * 100); ?>
            <div class="bg-primary-600 h-2.5 rounded-full" style="width: <?= $percentage ?>%"></div>
        </div>
        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400">
            <span>Used: <?= number_format($quotaUsage['quota_used']) ?></span>
            <span>Limit: <?= number_format($quotaUsage['quota_limit']) ?></span>
            <span>Resets: <?= date('M j, Y', strtotime($quotaUsage['reset_date'])) ?></span>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Quick Actions Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Quick Actions</h2>
            <div class="grid grid-cols-2 gap-3">
                <a href="/momentum/youtube-agent/search" class="flex flex-col items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600">
                    <i class="fas fa-search text-2xl text-primary-600 mb-2"></i>
                    <span class="text-sm text-gray-700 dark:text-gray-300">Search Videos</span>
                </a>
                <a href="/momentum/youtube-agent/saved-videos" class="flex flex-col items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600">
                    <i class="fas fa-bookmark text-2xl text-primary-600 mb-2"></i>
                    <span class="text-sm text-gray-700 dark:text-gray-300">Saved Videos</span>
                </a>
                <a href="/momentum/youtube-agent/analyses" class="flex flex-col items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600">
                    <i class="fas fa-chart-line text-2xl text-primary-600 mb-2"></i>
                    <span class="text-sm text-gray-700 dark:text-gray-300">Analyses</span>
                </a>
                <a href="/momentum/youtube-agent/money-making-strategies" class="flex flex-col items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600">
                    <i class="fas fa-dollar-sign text-2xl text-primary-600 mb-2"></i>
                    <span class="text-sm text-gray-700 dark:text-gray-300">Strategies</span>
                </a>
            </div>
        </div>
        
        <!-- Recent Searches Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Recent Searches</h2>
                <a href="/momentum/youtube-agent/searches" class="text-sm text-primary-600 hover:text-primary-800 dark:text-primary-400">View All</a>
            </div>
            <?php if (empty($recentSearches)): ?>
                <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                    <p>No searches yet</p>
                    <a href="/momentum/youtube-agent/search" class="inline-block mt-2 text-primary-600 hover:text-primary-800 dark:text-primary-400">Start a new search</a>
                </div>
            <?php else: ?>
                <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                    <?php foreach ($recentSearches as $search): ?>
                        <li class="py-2">
                            <a href="/momentum/youtube-agent/search-results/<?= $search['id'] ?>" class="flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 p-2 rounded">
                                <div class="flex-shrink-0 mr-3">
                                    <div class="w-10 h-10 flex items-center justify-center bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 rounded-full">
                                        <i class="fas fa-search"></i>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate"><?= htmlspecialchars($search['search_query']) ?></p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        <?= ucfirst($search['search_type']) ?> • 
                                        <?= $search['results_count'] ? $search['results_count'] . ' results' : 'No results' ?> • 
                                        <?= date('M j, Y', strtotime($search['created_at'])) ?>
                                    </p>
                                </div>
                                <div class="flex-shrink-0 ml-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= getStatusBadgeClass($search['status']) ?>">
                                        <?= ucfirst($search['status']) ?>
                                    </span>
                                </div>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
        
        <!-- Recent Analyses Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Recent Analyses</h2>
                <a href="/momentum/youtube-agent/analyses" class="text-sm text-primary-600 hover:text-primary-800 dark:text-primary-400">View All</a>
            </div>
            <?php if (empty($recentAnalyses)): ?>
                <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                    <p>No analyses yet</p>
                    <a href="/momentum/youtube-agent/search" class="inline-block mt-2 text-primary-600 hover:text-primary-800 dark:text-primary-400">Search for videos to analyze</a>
                </div>
            <?php else: ?>
                <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                    <?php foreach ($recentAnalyses as $analysis): ?>
                        <li class="py-2">
                            <a href="/momentum/youtube-agent/view-analysis/<?= $analysis['id'] ?>" class="flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 p-2 rounded">
                                <div class="flex-shrink-0 mr-3">
                                    <?php if (!empty($analysis['thumbnail_url'])): ?>
                                        <img src="<?= $analysis['thumbnail_url'] ?>" alt="Thumbnail" class="w-10 h-10 object-cover rounded">
                                    <?php else: ?>
                                        <div class="w-10 h-10 flex items-center justify-center bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 rounded-full">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate"><?= htmlspecialchars($analysis['video_title']) ?></p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        <?= ucfirst(str_replace('_', ' ', $analysis['analysis_type'])) ?> • 
                                        <?= date('M j, Y', strtotime($analysis['created_at'])) ?>
                                    </p>
                                </div>
                                <div class="flex-shrink-0 ml-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= getStatusBadgeClass($analysis['status']) ?>">
                                        <?= ucfirst($analysis['status']) ?>
                                    </span>
                                </div>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Saved Videos Section -->
    <div class="mt-8">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">Saved Videos</h2>
            <a href="/momentum/youtube-agent/saved-videos" class="text-sm text-primary-600 hover:text-primary-800 dark:text-primary-400">View All</a>
        </div>
        
        <?php if (empty($savedVideos)): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
                <div class="text-gray-500 dark:text-gray-400 mb-4">
                    <i class="fas fa-bookmark text-4xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No saved videos yet</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                    Save videos to access them quickly and organize your research.
                </p>
                <a href="/momentum/youtube-agent/search" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-search mr-2"></i> Search for Videos
                </a>
            </div>
        <?php else: ?>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                <?php foreach ($savedVideos as $saved): ?>
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                        <a href="/momentum/youtube-agent/view-video/<?= $saved['video_id'] ?>" class="block">
                            <img src="<?= $saved['thumbnail_url'] ?>" alt="<?= htmlspecialchars($saved['title']) ?>" class="w-full h-40 object-cover">
                            <div class="p-4">
                                <h3 class="text-sm font-medium text-gray-900 dark:text-white line-clamp-2 mb-1"><?= htmlspecialchars($saved['title']) ?></h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mb-2">
                                    <?= htmlspecialchars($saved['channel_title']) ?>
                                </p>
                                <?php if (!empty($saved['folder'])): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                        <i class="fas fa-folder mr-1"></i> <?= htmlspecialchars($saved['folder']) ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'pending':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
        case 'in_progress':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
        case 'completed':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
        case 'failed':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
}
?>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
