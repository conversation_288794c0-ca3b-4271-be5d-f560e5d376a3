<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Analysis Results</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">
                <?= htmlspecialchars($analysis['video_title']) ?>
            </p>
        </div>
        <div class="flex space-x-2">
            <a href="/momentum/youtube-agent/view-video/<?= $analysis['video_id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-video mr-2"></i> Back to Video
            </a>
            <a href="/momentum/youtube-agent" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-home mr-2"></i> Dashboard
            </a>
        </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-2">
            <!-- Analysis Summary Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                        <?= ucfirst(str_replace('_', ' ', $analysis['analysis_type'])) ?> Analysis
                    </h2>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= getStatusBadgeClass($analysis['status']) ?>">
                        <?= ucfirst($analysis['status']) ?>
                    </span>
                </div>
                <div class="p-4">
                    <?php if ($analysis['status'] !== 'completed'): ?>
                        <div class="text-center py-4">
                            <div class="text-gray-500 dark:text-gray-400 mb-4">
                                <i class="fas <?= $analysis['status'] === 'in_progress' ? 'fa-spinner fa-spin' : 'fa-exclamation-triangle' ?> text-4xl"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                Analysis <?= $analysis['status'] === 'in_progress' ? 'in progress' : 'failed' ?>
                            </h3>
                            <p class="text-gray-500 dark:text-gray-400 mb-4">
                                <?= $analysis['status'] === 'in_progress' ? 'Please wait while we analyze this video.' : 'There was an error analyzing this video. Please try again.' ?>
                            </p>
                            <?php if ($analysis['status'] === 'failed'): ?>
                                <form action="/momentum/youtube-agent/create-analysis/<?= $analysis['video_id'] ?>" method="POST" class="inline-block">
                                    <input type="hidden" name="analysis_type" value="<?= $analysis['analysis_type'] ?>">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                        <i class="fas fa-redo mr-2"></i> Retry Analysis
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Summary</h3>
                            <p class="text-gray-600 dark:text-gray-400">
                                <?= htmlspecialchars($analysis['summary']) ?>
                            </p>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <h3 class="text-md font-medium text-gray-900 dark:text-white mb-2">Estimated ROI</h3>
                                <p class="text-gray-600 dark:text-gray-400">
                                    <?= htmlspecialchars($analysis['estimated_roi']) ?>
                                </p>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <h3 class="text-md font-medium text-gray-900 dark:text-white mb-2">Difficulty Level</h3>
                                <div class="flex items-center">
                                    <?php 
                                    $difficultyLevel = $analysis['difficulty_level'];
                                    $difficultyMap = [
                                        'low' => ['text' => 'Low', 'color' => 'text-green-500', 'stars' => 1],
                                        'medium' => ['text' => 'Medium', 'color' => 'text-yellow-500', 'stars' => 2],
                                        'high' => ['text' => 'High', 'color' => 'text-orange-500', 'stars' => 3],
                                        'very_high' => ['text' => 'Very High', 'color' => 'text-red-500', 'stars' => 4]
                                    ];
                                    $difficulty = $difficultyMap[$difficultyLevel] ?? $difficultyMap['medium'];
                                    ?>
                                    <span class="<?= $difficulty['color'] ?> font-medium mr-2"><?= $difficulty['text'] ?></span>
                                    <div class="flex">
                                        <?php for ($i = 0; $i < 4; $i++): ?>
                                            <i class="fas fa-star <?= $i < $difficulty['stars'] ? $difficulty['color'] : 'text-gray-300 dark:text-gray-600' ?> mr-1"></i>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Key Points</h3>
                            <ul class="list-disc pl-5 space-y-1 text-gray-600 dark:text-gray-400">
                                <?php 
                                $keyPoints = json_decode($analysis['key_points'], true);
                                if (is_array($keyPoints)):
                                    foreach ($keyPoints as $point):
                                ?>
                                    <li><?= htmlspecialchars($point) ?></li>
                                <?php 
                                    endforeach;
                                endif;
                                ?>
                            </ul>
                        </div>
                        
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Opportunities</h3>
                            <ul class="list-disc pl-5 space-y-1 text-gray-600 dark:text-gray-400">
                                <?php 
                                $opportunities = json_decode($analysis['opportunities'], true);
                                if (is_array($opportunities)):
                                    foreach ($opportunities as $opportunity):
                                ?>
                                    <li><?= htmlspecialchars($opportunity) ?></li>
                                <?php 
                                    endforeach;
                                endif;
                                ?>
                            </ul>
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Implementation Steps</h3>
                            <ol class="list-decimal pl-5 space-y-1 text-gray-600 dark:text-gray-400">
                                <?php 
                                $steps = json_decode($analysis['implementation_steps'], true);
                                if (is_array($steps)):
                                    foreach ($steps as $step):
                                ?>
                                    <li><?= htmlspecialchars($step) ?></li>
                                <?php 
                                    endforeach;
                                endif;
                                ?>
                            </ol>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Money Making Strategies -->
            <?php if ($analysis['status'] === 'completed' && !empty($analysis['strategies'])): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white">Money Making Strategies</h2>
                    </div>
                    <div class="p-4">
                        <div class="space-y-6">
                            <?php foreach ($analysis['strategies'] as $index => $strategy): ?>
                                <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <h3 class="text-md font-medium text-gray-900 dark:text-white">
                                            Strategy <?= $index + 1 ?>: <?= htmlspecialchars($strategy['strategy_name']) ?>
                                        </h3>
                                    </div>
                                    <div class="p-4">
                                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                                            <?= htmlspecialchars($strategy['description']) ?>
                                        </p>
                                        
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                            <div>
                                                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Potential Revenue</h4>
                                                <p class="text-gray-600 dark:text-gray-400">
                                                    <?= htmlspecialchars($strategy['potential_revenue']) ?>
                                                </p>
                                            </div>
                                            <div>
                                                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Time Investment</h4>
                                                <p class="text-gray-600 dark:text-gray-400">
                                                    <?= htmlspecialchars($strategy['time_investment']) ?>
                                                </p>
                                            </div>
                                            <div>
                                                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Required Skills</h4>
                                                <p class="text-gray-600 dark:text-gray-400">
                                                    <?= htmlspecialchars($strategy['required_skills']) ?>
                                                </p>
                                            </div>
                                            <div>
                                                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Required Resources</h4>
                                                <p class="text-gray-600 dark:text-gray-400">
                                                    <?= htmlspecialchars($strategy['required_resources']) ?>
                                                </p>
                                            </div>
                                        </div>
                                        
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">Difficulty:</span>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= getDifficultyBadgeClass($strategy['implementation_difficulty']) ?>">
                                                    <?= ucfirst(str_replace('_', ' ', $strategy['implementation_difficulty'])) ?>
                                                </span>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">Suitable Brigade:</span>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= getBrigadeBadgeClass($strategy['suitable_brigade']) ?>">
                                                    <?= ucfirst(str_replace('_', ' ', $strategy['suitable_brigade'])) ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="lg:col-span-1">
            <!-- Video Info Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
                <div class="aspect-w-16 aspect-h-9">
                    <img src="<?= $analysis['thumbnail_url'] ?>" alt="<?= htmlspecialchars($analysis['video_title']) ?>" class="w-full object-cover">
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2"><?= htmlspecialchars($analysis['video_title']) ?></h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        <?= htmlspecialchars($analysis['channel_title']) ?>
                    </p>
                    <div class="flex space-x-2">
                        <a href="/momentum/youtube-agent/view-video/<?= $analysis['video_id'] ?>" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900 dark:text-primary-300 dark:hover:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-video mr-1"></i> View Video
                        </a>
                        <a href="https://www.youtube.com/watch?v=<?= $analysis['youtube_id'] ?>" target="_blank" class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fab fa-youtube mr-1"></i> Watch on YouTube
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Analysis Info Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Analysis Info</h2>
                </div>
                <div class="p-4">
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <div class="flex-shrink-0 mt-0.5">
                                <i class="fas fa-calendar-alt text-gray-500 dark:text-gray-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">Created</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    <?= date('M j, Y g:i A', strtotime($analysis['created_at'])) ?>
                                </p>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <div class="flex-shrink-0 mt-0.5">
                                <i class="fas fa-chart-line text-gray-500 dark:text-gray-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">Analysis Type</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    <?= ucfirst(str_replace('_', ' ', $analysis['analysis_type'])) ?>
                                </p>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <div class="flex-shrink-0 mt-0.5">
                                <i class="fas fa-tasks text-gray-500 dark:text-gray-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">Status</p>
                                <p class="text-sm">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= getStatusBadgeClass($analysis['status']) ?>">
                                        <?= ucfirst($analysis['status']) ?>
                                    </span>
                                </p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Actions Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Actions</h2>
                </div>
                <div class="p-4">
                    <div class="space-y-3">
                        <a href="/momentum/youtube-agent/analyses" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 w-full justify-center">
                            <i class="fas fa-list mr-2"></i> View All Analyses
                        </a>
                        
                        <?php if ($analysis['status'] === 'completed'): ?>
                            <a href="/momentum/youtube-agent/money-making-strategies" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 w-full justify-center">
                                <i class="fas fa-dollar-sign mr-2"></i> View All Strategies
                            </a>
                        <?php endif; ?>
                        
                        <a href="/momentum/youtube-agent/delete-analysis/<?= $analysis['id'] ?>" class="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 w-full justify-center" onclick="return confirm('Are you sure you want to delete this analysis?')">
                            <i class="fas fa-trash mr-2"></i> Delete Analysis
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'pending':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
        case 'in_progress':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
        case 'completed':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
        case 'failed':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
}

function getDifficultyBadgeClass($difficulty) {
    switch ($difficulty) {
        case 'low':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
        case 'medium':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
        case 'high':
            return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
        case 'very_high':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
}

function getBrigadeBadgeClass($brigade) {
    switch ($brigade) {
        case 'content_creation':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
        case 'lead_generation':
            return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
        case 'customer_support':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
        case 'data_analysis':
            return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
}
?>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
