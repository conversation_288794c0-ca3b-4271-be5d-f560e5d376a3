<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><?= htmlspecialchars($businessVenture['name']) ?></h1>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    <?= htmlspecialchars($businessVenture['description']) ?>
                </p>
            </div>

            <div class="flex flex-wrap items-center gap-3">
                <a href="/momentum/online-business" class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-1.5"></i> Back to Dashboard
                </a>
                <a href="/momentum/online-business/edit/<?= $businessVenture['id'] ?>" class="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-edit mr-1.5"></i> Edit Venture
                </a>
            </div>
        </div>

        <!-- Venture Details -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg mb-6">
            <div class="px-5 py-5">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-store text-cyan-500 mr-2"></i> Venture Details
                    </h2>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        <?php if ($businessVenture['status'] === 'active'): ?>
                            bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                        <?php elseif ($businessVenture['status'] === 'planning'): ?>
                            bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                        <?php else: ?>
                            bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                        <?php endif; ?>
                    ">
                        <?= ucfirst(htmlspecialchars($businessVenture['status'])) ?>
                    </span>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <dl class="space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Venture Name</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($businessVenture['name']) ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($businessVenture['description']) ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?= ucfirst(htmlspecialchars($businessVenture['status'])) ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created Date</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?= date('F j, Y', strtotime($businessVenture['created_at'])) ?></dd>
                            </div>
                        </dl>
                    </div>
                    <div>
                        <div class="bg-cyan-50 dark:bg-cyan-900/30 p-4 rounded-lg">
                            <h3 class="font-medium text-cyan-800 dark:text-cyan-300 mb-2">Quick Actions</h3>
                            <div class="space-y-2">
                                <a href="/momentum/online-business/edit/<?= $businessVenture['id'] ?>" class="block text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                    <i class="fas fa-edit mr-1.5"></i> Edit venture details
                                </a>
                                <a href="/momentum/online-business/metrics/<?= $businessVenture['id'] ?>" class="block text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                    <i class="fas fa-chart-line mr-1.5"></i> Update metrics
                                </a>
                                <a href="/momentum/online-business/tasks/<?= $businessVenture['id'] ?>" class="block text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                    <i class="fas fa-tasks mr-1.5"></i> Manage related tasks
                                </a>
                                <a href="/momentum/online-business/notes/<?= $businessVenture['id'] ?>" class="block text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                    <i class="fas fa-sticky-note mr-1.5"></i> Add notes
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg mb-6">
            <div class="px-5 py-5">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-chart-line text-cyan-500 mr-2"></i> Performance Metrics
                    </h2>
                    <a href="/momentum/online-business/metrics/<?= $businessVenture['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                        <i class="fas fa-edit mr-1"></i> Update Metrics
                    </a>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Revenue Metric -->
                    <div class="bg-white dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Revenue</p>
                                <h3 class="text-2xl font-bold text-gray-900 dark:text-white">$<?= number_format($metrics['revenue']['current_month'], 2) ?></h3>
                            </div>
                            <div class="<?= $metrics['revenue']['growth'] >= 0 ? 'text-green-500' : 'text-red-500' ?> flex items-center">
                                <?php if ($metrics['revenue']['growth'] >= 0): ?>
                                    <i class="fas fa-arrow-up mr-1"></i>
                                <?php else: ?>
                                    <i class="fas fa-arrow-down mr-1"></i>
                                <?php endif; ?>
                                <span><?= abs(number_format($metrics['revenue']['growth'], 1)) ?>%</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <p class="text-xs text-gray-500 dark:text-gray-400">vs. previous month: $<?= number_format($metrics['revenue']['previous_month'], 2) ?></p>
                        </div>
                    </div>

                    <!-- Traffic Metric -->
                    <div class="bg-white dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Traffic</p>
                                <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($metrics['traffic']['current_month']) ?></h3>
                            </div>
                            <div class="<?= $metrics['traffic']['growth'] >= 0 ? 'text-green-500' : 'text-red-500' ?> flex items-center">
                                <?php if ($metrics['traffic']['growth'] >= 0): ?>
                                    <i class="fas fa-arrow-up mr-1"></i>
                                <?php else: ?>
                                    <i class="fas fa-arrow-down mr-1"></i>
                                <?php endif; ?>
                                <span><?= abs(number_format($metrics['traffic']['growth'], 1)) ?>%</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <p class="text-xs text-gray-500 dark:text-gray-400">vs. previous month: <?= number_format($metrics['traffic']['previous_month']) ?></p>
                        </div>
                    </div>

                    <!-- Conversion Rate Metric -->
                    <div class="bg-white dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Conversion Rate</p>
                                <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($metrics['conversion_rate']['current_month'], 1) ?>%</h3>
                            </div>
                            <div class="<?= $metrics['conversion_rate']['growth'] >= 0 ? 'text-green-500' : 'text-red-500' ?> flex items-center">
                                <?php if ($metrics['conversion_rate']['growth'] >= 0): ?>
                                    <i class="fas fa-arrow-up mr-1"></i>
                                <?php else: ?>
                                    <i class="fas fa-arrow-down mr-1"></i>
                                <?php endif; ?>
                                <span><?= abs(number_format($metrics['conversion_rate']['growth'], 1)) ?>%</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <p class="text-xs text-gray-500 dark:text-gray-400">vs. previous month: <?= number_format($metrics['conversion_rate']['previous_month'], 1) ?>%</p>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <a href="/momentum/online-business/metrics/<?= $businessVenture['id'] ?>/history" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        View historical metrics <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Related Tasks -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="px-5 py-5">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-tasks text-cyan-500 mr-2"></i> Related Tasks
                    </h2>
                    <a href="/momentum/tasks/create?venture_id=<?= $businessVenture['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                        <i class="fas fa-plus mr-1"></i> Add Task
                    </a>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                    <p class="text-gray-500 dark:text-gray-400">No tasks associated with this venture yet.</p>
                    <a href="/momentum/tasks/create?venture_id=<?= $businessVenture['id'] ?>" class="inline-flex items-center mt-2 px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-plus mr-1.5"></i> Create Task
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
