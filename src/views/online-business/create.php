<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Create Business Venture</h1>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Add a new online business venture to your dashboard
                </p>
            </div>

            <div class="flex flex-wrap items-center gap-3">
                <a href="/momentum/online-business" class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-1.5"></i> Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Create Form -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="px-5 py-5">
                <form action="/momentum/online-business/create" method="POST" class="space-y-6">
                    <!-- Venture Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Venture Name</label>
                        <div class="mt-1">
                            <input type="text" name="name" id="name" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="E.g., E-commerce Store" required>
                        </div>
                    </div>

                    <!-- Venture Type -->
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Venture Type</label>
                        <div class="mt-1">
                            <select name="type" id="type" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                <option value="ecommerce">E-commerce Store</option>
                                <option value="content">Content Website</option>
                                <option value="saas">SaaS Product</option>
                                <option value="digital_products">Digital Products</option>
                                <option value="youtube">YouTube Channel</option>
                                <option value="podcast">Podcast</option>
                                <option value="membership">Membership Site</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                        <div class="mt-1">
                            <textarea name="description" id="description" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Describe your business venture"></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Brief description of your business venture and its goals.</p>
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                        <div class="mt-1">
                            <select name="status" id="status" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                <option value="planning">Planning</option>
                                <option value="development">Development</option>
                                <option value="active">Active</option>
                                <option value="paused">Paused</option>
                                <option value="closed">Closed</option>
                            </select>
                        </div>
                    </div>

                    <!-- Website URL -->
                    <div>
                        <label for="website_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Website URL</label>
                        <div class="mt-1">
                            <input type="url" name="website_url" id="website_url" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="https://example.com">
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Optional. The URL of your business website.</p>
                    </div>

                    <!-- Launch Date -->
                    <div>
                        <label for="launch_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Launch Date</label>
                        <div class="mt-1">
                            <input type="date" name="launch_date" id="launch_date" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Actual or planned launch date.</p>
                    </div>

                    <!-- Initial Investment -->
                    <div>
                        <label for="initial_investment" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Initial Investment (Rs)</label>
                        <div class="mt-1 relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 dark:text-gray-400 sm:text-sm">Rs</span>
                            </div>
                            <input type="number" name="initial_investment" id="initial_investment" class="pl-10 focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="0.00" step="0.01" min="0">
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Optional. The initial investment amount.</p>
                    </div>

                    <!-- Revenue Goal -->
                    <div>
                        <label for="revenue_goal" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Monthly Revenue Goal (Rs)</label>
                        <div class="mt-1 relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 dark:text-gray-400 sm:text-sm">Rs</span>
                            </div>
                            <input type="number" name="revenue_goal" id="revenue_goal" class="pl-10 focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="0.00" step="0.01" min="0">
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Optional. Your monthly revenue target.</p>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                        <div class="mt-1">
                            <textarea name="notes" id="notes" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Additional notes about your business venture"></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Optional. Any additional notes or information.</p>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-save mr-1.5"></i> Create Venture
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
