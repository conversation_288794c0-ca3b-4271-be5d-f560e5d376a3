<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Update Metrics</h1>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    <?= htmlspecialchars($businessVenture['name']) ?>
                </p>
            </div>

            <div class="flex flex-wrap items-center gap-3">
                <a href="/momentum/online-business/dashboard/<?= $businessVenture['id'] ?>" class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-1.5"></i> Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Metrics Form -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="px-5 py-5">
                <form action="/momentum/online-business/save-metrics/<?= $businessVenture['id'] ?>" method="POST" class="space-y-6">
                    <!-- Metric Date -->
                    <div>
                        <label for="metric_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Date</label>
                        <div class="mt-1">
                            <input type="date" name="metric_date" id="metric_date" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= date('Y-m-d') ?>" required>
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">The date these metrics apply to.</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Revenue -->
                        <div>
                            <label for="revenue" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Revenue (Rs)</label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 dark:text-gray-400 sm:text-sm">Rs</span>
                                </div>
                                <input type="number" name="revenue" id="revenue" class="pl-10 focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="0.00" step="0.01" min="0" value="<?= isset($data['revenue']) ? $data['revenue'] : (isset($latestMetrics['revenue']) ? $latestMetrics['revenue'] : '') ?>">
                            </div>
                        </div>

                        <!-- Expenses -->
                        <div>
                            <label for="expenses" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Expenses (Rs)</label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 dark:text-gray-400 sm:text-sm">Rs</span>
                                </div>
                                <input type="number" name="expenses" id="expenses" class="pl-10 focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="0.00" step="0.01" min="0" value="<?= isset($data['expenses']) ? $data['expenses'] : (isset($latestMetrics['expenses']) ? $latestMetrics['expenses'] : '') ?>">
                            </div>
                        </div>

                        <!-- Sales Count -->
                        <div>
                            <label for="sales_count" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Sales Count</label>
                            <div class="mt-1">
                                <input type="number" name="sales_count" id="sales_count" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="0" min="0" value="<?= isset($data['sales_count']) ? $data['sales_count'] : (isset($latestMetrics['sales_count']) ? $latestMetrics['sales_count'] : '') ?>">
                            </div>
                        </div>

                        <!-- New Customers -->
                        <div>
                            <label for="new_customers" class="block text-sm font-medium text-gray-700 dark:text-gray-300">New Customers</label>
                            <div class="mt-1">
                                <input type="number" name="new_customers" id="new_customers" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="0" min="0" value="<?= isset($data['new_customers']) ? $data['new_customers'] : (isset($latestMetrics['new_customers']) ? $latestMetrics['new_customers'] : '') ?>">
                            </div>
                        </div>

                        <!-- Website Visits -->
                        <div>
                            <label for="website_visits" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Website Visits</label>
                            <div class="mt-1">
                                <input type="number" name="website_visits" id="website_visits" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="0" min="0" value="<?= isset($data['website_visits']) ? $data['website_visits'] : (isset($latestMetrics['website_visits']) ? $latestMetrics['website_visits'] : '') ?>">
                            </div>
                        </div>

                        <!-- Conversion Rate -->
                        <div>
                            <label for="conversion_rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Conversion Rate (%)</label>
                            <div class="mt-1">
                                <input type="number" name="conversion_rate" id="conversion_rate" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="0.00" step="0.01" min="0" max="100" value="<?= isset($data['conversion_rate']) ? $data['conversion_rate'] : (isset($latestMetrics['conversion_rate']) ? $latestMetrics['conversion_rate'] : '') ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                        <div class="mt-1">
                            <textarea name="notes" id="notes" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Any additional notes about these metrics"><?= isset($data['notes']) ? $data['notes'] : (isset($latestMetrics['notes']) ? $latestMetrics['notes'] : '') ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Optional. Any additional notes or context about these metrics.</p>
                    </div>

                    <!-- Calculated Fields -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Calculated Metrics</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Profit -->
                            <div>
                                <label for="profit" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Profit (Rs)</label>
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 dark:text-gray-400 sm:text-sm">Rs</span>
                                    </div>
                                    <input type="number" name="profit" id="profit" class="pl-10 bg-gray-100 dark:bg-gray-600 focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:text-white rounded-md" placeholder="0.00" step="0.01" readonly>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Calculated as Revenue - Expenses</p>
                            </div>
                            
                            <!-- Average Order Value -->
                            <div>
                                <label for="average_order_value" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Average Order Value (Rs)</label>
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 dark:text-gray-400 sm:text-sm">Rs</span>
                                    </div>
                                    <input type="number" name="average_order_value" id="average_order_value" class="pl-10 bg-gray-100 dark:bg-gray-600 focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:text-white rounded-md" placeholder="0.00" step="0.01" readonly>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Calculated as Revenue / Sales Count</p>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-save mr-1.5"></i> Save Metrics
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Calculate profit and average order value
    document.addEventListener('DOMContentLoaded', function() {
        const revenueInput = document.getElementById('revenue');
        const expensesInput = document.getElementById('expenses');
        const salesCountInput = document.getElementById('sales_count');
        const profitInput = document.getElementById('profit');
        const aovInput = document.getElementById('average_order_value');
        
        function calculateMetrics() {
            // Calculate profit
            const revenue = parseFloat(revenueInput.value) || 0;
            const expenses = parseFloat(expensesInput.value) || 0;
            const profit = revenue - expenses;
            profitInput.value = profit.toFixed(2);
            
            // Calculate average order value
            const salesCount = parseInt(salesCountInput.value) || 0;
            const aov = salesCount > 0 ? revenue / salesCount : 0;
            aovInput.value = aov.toFixed(2);
        }
        
        // Calculate on page load
        calculateMetrics();
        
        // Calculate on input change
        revenueInput.addEventListener('input', calculateMetrics);
        expensesInput.addEventListener('input', calculateMetrics);
        salesCountInput.addEventListener('input', calculateMetrics);
    });
</script>
