<?php
/**
 * Voice Recording Form
 */
?>

<div class="quick-capture-container">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="flex items-center mb-6">
            <a href="/momentum/quick-capture" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 mr-4">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Voice Recording</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Record audio notes with transcription</p>
            </div>
        </div>

        <!-- Voice Recorder -->
        <div class="voice-recorder mb-6">
            <div class="text-center mb-4">
                <button id="recordButton" class="record-button" onclick="toggleRecording()">
                    <i class="fas fa-microphone"></i>
                </button>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">Click to start recording</p>
            </div>

            <div id="recordingStatus" class="text-center mb-4 hidden">
                <div class="recording-timer">00:00</div>
                <p class="text-sm text-red-600 dark:text-red-400">Recording in progress...</p>
            </div>

            <div id="waveformContainer" class="waveform-visualization mb-4">
                <p class="text-sm text-gray-500">Audio visualization will appear here</p>
            </div>

            <div id="playbackControls" class="text-center mb-4 hidden">
                <button onclick="playRecording()" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md mr-2">
                    <i class="fas fa-play mr-2"></i>
                    Play
                </button>
                <button onclick="stopPlayback()" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md mr-2">
                    <i class="fas fa-stop mr-2"></i>
                    Stop
                </button>
                <button onclick="resetRecording()" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md">
                    <i class="fas fa-trash mr-2"></i>
                    Reset
                </button>
            </div>
        </div>

        <!-- Recording Form -->
        <div id="recordingForm" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hidden">
            <form id="voiceForm" method="POST" action="/momentum/quick-capture/voice/upload">
                <div class="mb-4">
                    <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Title (Optional)
                    </label>
                    <input type="text" id="title" name="title" 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 dark:bg-gray-700 dark:text-white"
                           placeholder="Enter a title for your voice note...">
                </div>

                <div class="mb-4">
                    <label for="transcription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Transcription (Auto-generated)
                    </label>
                    <textarea id="transcription" name="transcription" rows="6"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 dark:bg-gray-700 dark:text-white resize-none"
                              placeholder="Transcription will appear here automatically..."></textarea>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">You can edit the transcription if needed</p>
                </div>

                <div class="mb-4">
                    <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Tags (Optional)
                    </label>
                    <input type="text" id="tags" name="tags" 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 dark:bg-gray-700 dark:text-white"
                           placeholder="Enter tags separated by commas...">
                </div>

                <div class="mb-4">
                    <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Category (Optional)
                    </label>
                    <input type="text" id="category" name="category" 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 dark:bg-gray-700 dark:text-white"
                           placeholder="Enter a category...">
                </div>

                <div class="mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" id="is_pinned" name="is_pinned" value="1" 
                               class="rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-300 focus:ring focus:ring-orange-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Pin this voice note</span>
                    </label>
                </div>

                <div class="flex justify-end space-x-3">
                    <a href="/momentum/quick-capture" 
                       class="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i>
                        Save Voice Note
                    </button>
                </div>
            </form>
        </div>

        <!-- Browser Support Warning -->
        <div id="browserWarning" class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4 hidden">
            <div class="flex">
                <i class="fas fa-exclamation-triangle text-yellow-400 mr-3 mt-1"></i>
                <div>
                    <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-300">Browser Not Supported</h3>
                    <p class="text-sm text-yellow-700 dark:text-yellow-400 mt-1">
                        Your browser doesn't support audio recording. Please use a modern browser like Chrome, Firefox, or Safari.
                    </p>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="mt-6 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-4">
            <h3 class="text-sm font-medium text-orange-800 dark:text-orange-300 mb-2">
                <i class="fas fa-info-circle mr-1"></i>
                How to Use
            </h3>
            <ul class="text-xs text-orange-700 dark:text-orange-400 space-y-1">
                <li>• Click the microphone button to start recording</li>
                <li>• Speak clearly for better transcription accuracy</li>
                <li>• Click stop when finished, then review and edit the transcription</li>
                <li>• Add tags and categories to organize your voice notes</li>
                <li>• Your browser will ask for microphone permission</li>
            </ul>
        </div>
    </div>
</div>

<script>
let mediaRecorder;
let audioChunks = [];
let isRecording = false;
let recordingTimer;
let startTime;
let audioBlob;

// Check browser support
if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    document.getElementById('browserWarning').classList.remove('hidden');
}

async function toggleRecording() {
    if (!isRecording) {
        await startRecording();
    } else {
        stopRecording();
    }
}

async function startRecording() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        
        mediaRecorder = new MediaRecorder(stream);
        audioChunks = [];
        
        mediaRecorder.ondataavailable = event => {
            audioChunks.push(event.data);
        };
        
        mediaRecorder.onstop = () => {
            audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
            showPlaybackControls();
            showRecordingForm();
            
            // Stop all tracks to release microphone
            stream.getTracks().forEach(track => track.stop());
        };
        
        mediaRecorder.start();
        isRecording = true;
        
        // Update UI
        const recordButton = document.getElementById('recordButton');
        recordButton.classList.add('recording');
        recordButton.innerHTML = '<i class="fas fa-stop"></i>';
        
        document.getElementById('recordingStatus').classList.remove('hidden');
        
        // Start timer
        startTime = Date.now();
        recordingTimer = setInterval(updateTimer, 1000);
        
    } catch (error) {
        console.error('Error starting recording:', error);
        alert('Failed to start recording. Please check your microphone permissions.');
    }
}

function stopRecording() {
    if (mediaRecorder && isRecording) {
        mediaRecorder.stop();
        isRecording = false;
        
        // Update UI
        const recordButton = document.getElementById('recordButton');
        recordButton.classList.remove('recording');
        recordButton.innerHTML = '<i class="fas fa-microphone"></i>';
        
        document.getElementById('recordingStatus').classList.add('hidden');
        
        // Stop timer
        clearInterval(recordingTimer);
    }
}

function updateTimer() {
    const elapsed = Math.floor((Date.now() - startTime) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    
    const timerElement = document.querySelector('.recording-timer');
    timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

function showPlaybackControls() {
    document.getElementById('playbackControls').classList.remove('hidden');
}

function showRecordingForm() {
    document.getElementById('recordingForm').classList.remove('hidden');
    
    // Simulate transcription (in a real app, you'd send the audio to a transcription service)
    setTimeout(() => {
        document.getElementById('transcription').value = 'Transcription will be available when connected to a speech-to-text service.';
    }, 1000);
}

function playRecording() {
    if (audioBlob) {
        const audio = new Audio(URL.createObjectURL(audioBlob));
        audio.play();
    }
}

function stopPlayback() {
    // Stop any currently playing audio
    const audios = document.querySelectorAll('audio');
    audios.forEach(audio => {
        audio.pause();
        audio.currentTime = 0;
    });
}

function resetRecording() {
    if (confirm('Are you sure you want to reset the recording?')) {
        audioBlob = null;
        audioChunks = [];
        
        document.getElementById('playbackControls').classList.add('hidden');
        document.getElementById('recordingForm').classList.add('hidden');
        
        // Reset form
        document.getElementById('voiceForm').reset();
    }
}

// Handle form submission
document.getElementById('voiceForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!audioBlob) {
        alert('Please record audio first');
        return;
    }
    
    const formData = new FormData();
    formData.append('audio', audioBlob, 'voice_note.wav');
    formData.append('title', document.getElementById('title').value);
    formData.append('transcription', document.getElementById('transcription').value);
    formData.append('tags', document.getElementById('tags').value);
    formData.append('category', document.getElementById('category').value);
    formData.append('is_pinned', document.getElementById('is_pinned').checked ? '1' : '0');
    
    try {
        const response = await fetch('/momentum/quick-capture/voice/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Voice note saved successfully!');
            window.location.href = '/momentum/quick-capture/view/' + result.capture_id;
        } else {
            alert('Failed to save voice note: ' + (result.error || 'Unknown error'));
        }
        
    } catch (error) {
        console.error('Error saving voice note:', error);
        alert('Failed to save voice note');
    }
});
</script>
