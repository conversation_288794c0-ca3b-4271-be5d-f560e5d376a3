<?php
/**
 * Quick Capture Dashboard View
 */
?>

<link rel="stylesheet" href="/momentum/public/css/tools.css">
<link rel="stylesheet" href="/momentum/public/css/gallery-enhanced.css">

<div class="tools-container">
    <!-- Header -->
    <div class="gallery-header">
        <div class="flex items-center">
            <a href="/momentum/tools" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 mr-4 text-xl">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="gallery-title">Quick Capture</h1>
                <p class="gallery-subtitle">Capture screenshots, notes, and voice recordings instantly</p>
            </div>
        </div>

        <div class="flex space-x-3">
            <button onclick="openQuickCapture()" class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-camera mr-2"></i>
                Quick Capture
            </button>
            <a href="<?= strpos($_SERVER['REQUEST_URI'] ?? '', '/tools/') !== false ? '/momentum/tools/gallery' : '/momentum/quick-capture/gallery' ?>" class="inline-flex items-center px-6 py-3 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 font-semibold rounded-lg border border-gray-200 dark:border-gray-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-th-large mr-2"></i>
                View Gallery
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="capture-card bg-white dark:bg-gray-800 rounded-xl shadow-soft hover:shadow-lg transition-all duration-300 p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">Screenshots</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= $stats['overview']['screenshots'] ?? 0 ?></p>
                </div>
                <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-3 rounded-xl shadow-lg">
                    <i class="fas fa-camera text-white text-xl"></i>
                </div>
            </div>
        </div>

        <div class="capture-card bg-white dark:bg-gray-800 rounded-xl shadow-soft hover:shadow-lg transition-all duration-300 p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">Notes</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= $stats['overview']['notes'] ?? 0 ?></p>
                </div>
                <div class="bg-gradient-to-r from-green-500 to-green-600 p-3 rounded-xl shadow-lg">
                    <i class="fas fa-sticky-note text-white text-xl"></i>
                </div>
            </div>
        </div>

        <div class="capture-card bg-white dark:bg-gray-800 rounded-xl shadow-soft hover:shadow-lg transition-all duration-300 p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">Voice Notes</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= $stats['overview']['voice_notes'] ?? 0 ?></p>
                </div>
                <div class="bg-gradient-to-r from-orange-500 to-orange-600 p-3 rounded-xl shadow-lg">
                    <i class="fas fa-microphone text-white text-xl"></i>
                </div>
            </div>
        </div>

        <div class="capture-card bg-white dark:bg-gray-800 rounded-xl shadow-soft hover:shadow-lg transition-all duration-300 p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">Pinned</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= $stats['overview']['pinned_captures'] ?? 0 ?></p>
                </div>
                <div class="bg-gradient-to-r from-purple-500 to-purple-600 p-3 rounded-xl shadow-lg">
                    <i class="fas fa-thumbtack text-white text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-8 mb-8">
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2 flex items-center justify-center">
                <i class="fas fa-bolt mr-3 text-blue-500"></i>
                Quick Actions
            </h2>
            <p class="text-gray-600 dark:text-gray-400">Choose your capture method</p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <button onclick="startScreenshot()" class="capture-card group flex flex-col items-center p-8 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-800/30 dark:hover:to-blue-700/30 rounded-xl border border-blue-200 dark:border-blue-700 transition-all duration-300 transform hover:-translate-y-2 hover:shadow-lg">
                <div class="w-16 h-16 bg-blue-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <i class="fas fa-camera text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-blue-700 dark:text-blue-300 mb-2">Take Screenshot</h3>
                <p class="text-blue-600 dark:text-blue-400 text-center leading-relaxed">Capture and annotate your screen with powerful editing tools</p>
            </button>

            <button onclick="createQuickNote()" class="capture-card group flex flex-col items-center p-8 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 hover:from-green-100 hover:to-green-200 dark:hover:from-green-800/30 dark:hover:to-green-700/30 rounded-xl border border-green-200 dark:border-green-700 transition-all duration-300 transform hover:-translate-y-2 hover:shadow-lg">
                <div class="w-16 h-16 bg-green-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <i class="fas fa-sticky-note text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-green-700 dark:text-green-300 mb-2">Quick Note</h3>
                <p class="text-green-600 dark:text-green-400 text-center leading-relaxed">Create a text note instantly with tags and categories</p>
            </button>

            <button onclick="startVoiceNote()" class="capture-card group flex flex-col items-center p-8 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 hover:from-orange-100 hover:to-orange-200 dark:hover:from-orange-800/30 dark:hover:to-orange-700/30 rounded-xl border border-orange-200 dark:border-orange-700 transition-all duration-300 transform hover:-translate-y-2 hover:shadow-lg">
                <div class="w-16 h-16 bg-orange-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <i class="fas fa-microphone text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-orange-700 dark:text-orange-300 mb-2">Voice Note</h3>
                <p class="text-orange-600 dark:text-orange-400 text-center leading-relaxed">Record audio with automatic transcription</p>
            </button>
        </div>
    </div>

    <!-- Pinned Captures -->
    <?php if (!empty($pinnedCaptures)): ?>
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-8 mb-8">
        <div class="flex justify-between items-center mb-8">
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2 flex items-center">
                    <i class="fas fa-thumbtack mr-3 text-yellow-500"></i>
                    Pinned Captures
                </h2>
                <p class="text-gray-600 dark:text-gray-400">Your most important captures</p>
            </div>
            <a href="<?= strpos($_SERVER['REQUEST_URI'] ?? '', '/tools/') !== false ? '/momentum/tools/gallery?filter=pinned' : '/momentum/quick-capture/gallery?filter=pinned' ?>" class="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300 font-medium rounded-lg transition-colors duration-200">
                <i class="fas fa-external-link-alt mr-2"></i>
                View All
            </a>
        </div>

        <div class="capture-gallery">
            <?php foreach ($pinnedCaptures as $capture): ?>
                <div class="capture-card pinned">
                    <div class="capture-card-header">
                        <h3 class="capture-card-title"><?= htmlspecialchars($capture['title'] ?: ucfirst($capture['type']) . ' capture') ?></h3>
                        <div class="capture-card-meta">
                            <span class="capture-type-badge <?= $capture['type'] ?>"><?= ucfirst($capture['type']) ?></span>
                            <span><?= date('M j, Y', strtotime($capture['created_at'])) ?></span>
                        </div>
                        <i class="fas fa-thumbtack pin-indicator"></i>
                    </div>

                    <?php if ($capture['type'] === 'screenshot' && $capture['thumbnail_path']): ?>
                    <div class="capture-card-body">
                        <img src="<?= htmlspecialchars($capture['thumbnail_path']) ?>" alt="Screenshot" class="capture-preview">
                    </div>
                    <?php elseif ($capture['content']): ?>
                    <div class="capture-card-body">
                        <div class="capture-content"><?= htmlspecialchars(substr($capture['content'], 0, 150)) ?><?= strlen($capture['content']) > 150 ? '...' : '' ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if ($capture['tags']): ?>
                    <div class="capture-card-body">
                        <div class="capture-tags">
                            <?php foreach (explode(',', $capture['tags']) as $tag): ?>
                                <span class="capture-tag"><?= htmlspecialchars(trim($tag)) ?></span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="capture-card-footer">
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            <?= $capture['category'] ? htmlspecialchars($capture['category']) : 'Uncategorized' ?>
                        </div>

                        <div class="capture-actions">
                            <button onclick="togglePin(<?= $capture['id'] ?>)" class="capture-action-btn pin" title="Unpin">
                                <i class="fas fa-thumbtack"></i>
                            </button>
                            <a href="/momentum/quick-capture/view/<?= $capture['id'] ?>" class="capture-action-btn" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
            </div>
            <?php endif; ?>

    <!-- Recent Captures -->
    <?php if (!empty($recentCaptures)): ?>
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-8 mb-8">
        <div class="flex justify-between items-center mb-8">
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2 flex items-center">
                    <i class="fas fa-clock mr-3 text-green-500"></i>
                    Recent Captures
                </h2>
                <p class="text-gray-600 dark:text-gray-400">Your latest captures</p>
            </div>
            <a href="<?= strpos($_SERVER['REQUEST_URI'] ?? '', '/tools/') !== false ? '/momentum/tools/gallery' : '/momentum/quick-capture/gallery' ?>" class="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300 font-medium rounded-lg transition-colors duration-200">
                <i class="fas fa-external-link-alt mr-2"></i>
                View All
            </a>
        </div>

        <div class="capture-gallery">
            <?php foreach (array_slice($recentCaptures, 0, 6) as $capture): ?>
                <div class="capture-card <?= $capture['type'] ?>">
                    <div class="capture-card-header">
                        <h3 class="capture-card-title"><?= htmlspecialchars($capture['title'] ?: ucfirst($capture['type']) . ' capture') ?></h3>
                        <div class="capture-card-meta">
                            <span class="capture-type-badge <?= $capture['type'] ?>"><?= ucfirst($capture['type']) ?></span>
                            <span><?= date('M j, g:i A', strtotime($capture['created_at'])) ?></span>
                        </div>
                        <?php if ($capture['is_pinned']): ?>
                            <i class="fas fa-thumbtack pin-indicator"></i>
                        <?php endif; ?>
                    </div>

                    <?php if ($capture['type'] === 'screenshot' && $capture['thumbnail_path']): ?>
                    <div class="capture-card-body">
                        <img src="<?= htmlspecialchars($capture['thumbnail_path']) ?>" alt="Screenshot" class="capture-preview">
                    </div>
                    <?php elseif ($capture['content']): ?>
                    <div class="capture-card-body">
                        <div class="capture-content"><?= htmlspecialchars(substr($capture['content'], 0, 100)) ?><?= strlen($capture['content']) > 100 ? '...' : '' ?></div>
                    </div>
                    <?php endif; ?>

                    <div class="capture-card-footer">
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            <?= $capture['category'] ? htmlspecialchars($capture['category']) : 'Uncategorized' ?>
                        </div>

                        <div class="capture-actions">
                            <button onclick="togglePin(<?= $capture['id'] ?>)" class="capture-action-btn <?= $capture['is_pinned'] ? 'pin' : '' ?>" title="<?= $capture['is_pinned'] ? 'Unpin' : 'Pin' ?>">
                                <i class="fas fa-thumbtack"></i>
                            </button>
                            <a href="/momentum/quick-capture/view/<?= $capture['id'] ?>" class="capture-action-btn" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                            <button onclick="deleteCapture(<?= $capture['id'] ?>)" class="capture-action-btn delete" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
            </div>
            <?php endif; ?>

    <!-- Categories -->
    <?php if (!empty($categories)): ?>
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-8 mb-8">
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2 flex items-center">
                <i class="fas fa-folder mr-3 text-purple-500"></i>
                Categories
            </h2>
            <p class="text-gray-600 dark:text-gray-400">Browse captures by category</p>
        </div>
        <div class="flex flex-wrap gap-3">
        <?php foreach ($categories as $category): ?>
                <?php $galleryUrl = strpos($_SERVER['REQUEST_URI'] ?? '', '/tools/') !== false ? '/momentum/tools/gallery' : '/momentum/quick-capture/gallery'; ?>
                <a href="<?= $galleryUrl ?>?category=<?= urlencode($category['category']) ?>" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 hover:from-gray-200 hover:to-gray-300 dark:hover:from-gray-600 dark:hover:to-gray-500 text-gray-700 dark:text-gray-300 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md">
                    <i class="fas fa-tag mr-2 text-purple-500"></i>
                    <?= htmlspecialchars($category['category']) ?>
                    <span class="ml-2 px-2 py-1 bg-white dark:bg-gray-800 text-xs text-gray-500 dark:text-gray-400 rounded-full"><?= $category['count'] ?></span>
                </a>
        <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Empty State -->
    <?php if (empty($recentCaptures) && empty($pinnedCaptures)): ?>
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="fas fa-camera"></i>
        </div>
        <h3 class="empty-state-title">No captures yet</h3>
        <p class="empty-state-description">Start capturing screenshots, notes, and voice recordings to organize your ideas and boost your productivity.</p>
        <button onclick="openQuickCapture()" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg transition-all duration-200 transform hover:-translate-y-1 shadow-lg hover:shadow-xl">
            <i class="fas fa-camera mr-3"></i>
            Start Capturing
        </button>
    </div>
    <?php endif; ?>
</div>

<!-- Quick Capture Modal -->
<div id="quickCaptureModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Quick Capture</h3>
                <button onclick="closeQuickCapture()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="space-y-3">
                <button onclick="startScreenshot()" class="w-full flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40 rounded-lg border border-blue-200 dark:border-blue-700 transition-colors duration-200">
                    <i class="fas fa-camera text-blue-600 dark:text-blue-400 mr-3"></i>
                    <div class="text-left">
                        <p class="font-medium text-blue-700 dark:text-blue-300">Take Screenshot</p>
                        <p class="text-xs text-blue-600 dark:text-blue-400">Capture and annotate screen content</p>
                    </div>
                </button>

                <button onclick="createQuickNote()" class="w-full flex items-center p-3 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/40 rounded-lg border border-green-200 dark:border-green-700 transition-colors duration-200">
                    <i class="fas fa-sticky-note text-green-600 dark:text-green-400 mr-3"></i>
                    <div class="text-left">
                        <p class="font-medium text-green-700 dark:text-green-300">Quick Note</p>
                        <p class="text-xs text-green-600 dark:text-green-400">Create a text note instantly</p>
                    </div>
                </button>

                <button onclick="startVoiceNote()" class="w-full flex items-center p-3 bg-orange-50 dark:bg-orange-900/20 hover:bg-orange-100 dark:hover:bg-orange-900/40 rounded-lg border border-orange-200 dark:border-orange-700 transition-colors duration-200">
                    <i class="fas fa-microphone text-orange-600 dark:text-orange-400 mr-3"></i>
                    <div class="text-left">
                        <p class="font-medium text-orange-700 dark:text-orange-300">Voice Note</p>
                        <p class="text-xs text-orange-600 dark:text-orange-400">Record audio with transcription</p>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Include the AI Assistant widget JavaScript functions
function openQuickCapture() {
    document.getElementById('quickCaptureModal').classList.remove('hidden');
}

function closeQuickCapture() {
    document.getElementById('quickCaptureModal').classList.add('hidden');
}

function startScreenshot() {
    closeQuickCapture();
    // Check if we're in tools context
    const isToolsContext = window.location.pathname.includes('/tools/');
    const screenshotUrl = isToolsContext ? '/momentum/tools/screenshot' : '/momentum/quick-capture/screenshot';
    window.open(screenshotUrl, '_blank', 'width=1200,height=800');
}

function createQuickNote() {
    closeQuickCapture();
    // Check if we're in tools context
    const isToolsContext = window.location.pathname.includes('/tools/');
    const noteUrl = isToolsContext ? '/momentum/tools/note' : '/momentum/quick-capture/note';
    window.location.href = noteUrl;
}

function startVoiceNote() {
    closeQuickCapture();
    // Check if we're in tools context
    const isToolsContext = window.location.pathname.includes('/tools/');
    const voiceUrl = isToolsContext ? '/momentum/tools/voice' : '/momentum/quick-capture/voice';
    window.location.href = voiceUrl;
}

function togglePin(captureId) {
    fetch(`/momentum/quick-capture/toggle-pin/${captureId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to toggle pin: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to toggle pin');
    });
}

function deleteCapture(captureId) {
    if (confirm('Are you sure you want to delete this capture?')) {
        fetch(`/momentum/quick-capture/delete/${captureId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to delete capture: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to delete capture');
        });
    }
}

// Close modal when clicking outside
document.getElementById('quickCaptureModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeQuickCapture();
    }
});
</script>
