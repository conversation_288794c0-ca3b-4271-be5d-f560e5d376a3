<?php
/**
 * Quick Capture Gallery
 */
?>

<link rel="stylesheet" href="/momentum/public/css/tools.css">
<link rel="stylesheet" href="/momentum/public/css/quick-capture.css">
<link rel="stylesheet" href="/momentum/public/css/gallery-enhanced.css">

<div class="tools-container">
    <!-- Header -->
    <div class="gallery-header">
        <div class="flex items-center">
            <a href="/momentum/quick-capture" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 mr-4 text-xl">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="gallery-title">Capture Gallery</h1>
                <p class="gallery-subtitle">Browse your screenshots, notes, and voice recordings</p>
            </div>
        </div>

        <div class="flex space-x-3">
            <button onclick="openQuickCapture()" class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-camera mr-2"></i>
                Quick Capture
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-container">
        <div class="filter-row">
            <div class="filter-group">
                <label class="filter-label">Type:</label>
                <select id="typeFilter" class="filter-select">
                    <option value="">All Types</option>
                    <option value="screenshot">Screenshots</option>
                    <option value="note">Notes</option>
                    <option value="voice">Voice Notes</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Category:</label>
                <select id="categoryFilter" class="filter-select">
                    <option value="">All Categories</option>
                    <?php if (!empty($categories)): ?>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?= htmlspecialchars($category['category']) ?>"><?= htmlspecialchars($category['category']) ?></option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Sort:</label>
                <select id="sortFilter" class="filter-select">
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="title">Title A-Z</option>
                    <option value="type">Type</option>
                </select>
            </div>

            <div class="filter-group">
                <input type="text" id="searchInput" placeholder="Search captures..." class="filter-input">
                <button onclick="applyFilters()" class="filter-button">
                    <i class="fas fa-search mr-2"></i>
                    Search
                </button>
            </div>
        </div>
    </div>

    <!-- Gallery Grid -->
    <div id="captureGrid" class="capture-gallery">
        <?php if (!empty($captures)): ?>
            <?php foreach ($captures as $capture): ?>
                <div class="capture-card <?= $capture['type'] ?>" data-type="<?= $capture['type'] ?>" data-category="<?= htmlspecialchars($capture['category'] ?? '') ?>">
                    <div class="capture-card-header">
                        <h3 class="capture-card-title"><?= htmlspecialchars($capture['title'] ?: ucfirst($capture['type']) . ' capture') ?></h3>
                        <div class="capture-card-meta">
                            <span class="capture-type-badge <?= $capture['type'] ?>"><?= ucfirst($capture['type']) ?></span>
                            <span><?= date('M j, Y', strtotime($capture['created_at'])) ?></span>
                        </div>
                        <?php if ($capture['is_pinned']): ?>
                            <i class="fas fa-thumbtack pin-indicator"></i>
                        <?php endif; ?>
                    </div>

                    <?php if ($capture['type'] === 'screenshot' && $capture['thumbnail_path']): ?>
                    <div class="capture-card-body">
                        <img src="<?= htmlspecialchars($capture['thumbnail_path']) ?>" alt="Screenshot" class="capture-preview">
                    </div>
                    <?php elseif ($capture['content']): ?>
                    <div class="capture-card-body">
                        <div class="capture-content"><?= htmlspecialchars(substr($capture['content'], 0, 150)) ?><?= strlen($capture['content']) > 150 ? '...' : '' ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if ($capture['tags']): ?>
                    <div class="capture-card-body">
                        <div class="capture-tags">
                            <?php foreach (explode(',', $capture['tags']) as $tag): ?>
                                <span class="capture-tag"><?= htmlspecialchars(trim($tag)) ?></span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="capture-card-footer">
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            <?= $capture['category'] ? htmlspecialchars($capture['category']) : 'Uncategorized' ?>
                        </div>

                        <div class="capture-actions">
                            <button onclick="togglePin(<?= $capture['id'] ?>)" class="capture-action-btn <?= $capture['is_pinned'] ? 'pin' : '' ?>" title="<?= $capture['is_pinned'] ? 'Unpin' : 'Pin' ?>">
                                <i class="fas fa-thumbtack"></i>
                            </button>
                            <a href="/momentum/quick-capture/view/<?= $capture['id'] ?>" class="capture-action-btn" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                            <button onclick="deleteCapture(<?= $capture['id'] ?>)" class="capture-action-btn delete" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-camera"></i>
                </div>
                <h3 class="empty-state-title">No captures yet</h3>
                <p class="empty-state-description">Start capturing screenshots, notes, and voice recordings to build your collection.</p>
                <button onclick="openQuickCapture()" class="inline-flex items-center px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    <i class="fas fa-camera mr-3"></i>
                    Start Capturing
                </button>
            </div>
        <?php endif; ?>
    </div>

    <!-- Load More Button -->
    <?php if (!empty($captures) && count($captures) >= 20): ?>
        <div class="load-more-container">
            <button onclick="loadMore()" class="load-more-btn">
                <i class="fas fa-plus mr-2"></i>
                Load More Captures
            </button>
        </div>
    <?php endif; ?>
</div>

<!-- Quick Capture Modal -->
<div id="quickCaptureModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Quick Capture</h3>
                <button onclick="closeQuickCapture()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="space-y-3">
                <button onclick="startScreenshot()" class="w-full flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40 rounded-lg border border-blue-200 dark:border-blue-700 transition-colors duration-200">
                    <i class="fas fa-camera text-blue-600 dark:text-blue-400 mr-3"></i>
                    <div class="text-left">
                        <p class="font-medium text-blue-700 dark:text-blue-300">Take Screenshot</p>
                        <p class="text-xs text-blue-600 dark:text-blue-400">Capture and annotate screen content</p>
                    </div>
                </button>

                <button onclick="createQuickNote()" class="w-full flex items-center p-3 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/40 rounded-lg border border-green-200 dark:border-green-700 transition-colors duration-200">
                    <i class="fas fa-sticky-note text-green-600 dark:text-green-400 mr-3"></i>
                    <div class="text-left">
                        <p class="font-medium text-green-700 dark:text-green-300">Quick Note</p>
                        <p class="text-xs text-green-600 dark:text-green-400">Create a text note instantly</p>
                    </div>
                </button>

                <button onclick="startVoiceNote()" class="w-full flex items-center p-3 bg-orange-50 dark:bg-orange-900/20 hover:bg-orange-100 dark:hover:bg-orange-900/40 rounded-lg border border-orange-200 dark:border-orange-700 transition-colors duration-200">
                    <i class="fas fa-microphone text-orange-600 dark:text-orange-400 mr-3"></i>
                    <div class="text-left">
                        <p class="font-medium text-orange-700 dark:text-orange-300">Voice Note</p>
                        <p class="text-xs text-orange-600 dark:text-orange-400">Record audio with transcription</p>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Quick Capture Modal Functions
function openQuickCapture() {
    document.getElementById('quickCaptureModal').classList.remove('hidden');
}

function closeQuickCapture() {
    document.getElementById('quickCaptureModal').classList.add('hidden');
}

function startScreenshot() {
    closeQuickCapture();
    window.open('/momentum/quick-capture/screenshot', '_blank', 'width=1200,height=800');
}

function createQuickNote() {
    closeQuickCapture();
    window.location.href = '/momentum/quick-capture/note';
}

function startVoiceNote() {
    closeQuickCapture();
    window.location.href = '/momentum/quick-capture/voice';
}

// Filter Functions
function applyFilters() {
    const typeFilter = document.getElementById('typeFilter').value;
    const categoryFilter = document.getElementById('categoryFilter').value;
    const sortFilter = document.getElementById('sortFilter').value;
    const searchQuery = document.getElementById('searchInput').value.toLowerCase();

    const cards = document.querySelectorAll('.capture-card');
    let visibleCards = [];

    cards.forEach(card => {
        const cardType = card.dataset.type;
        const cardCategory = card.dataset.category;
        const cardTitle = card.querySelector('.capture-card-title').textContent.toLowerCase();
        const cardContent = card.querySelector('.capture-content')?.textContent.toLowerCase() || '';

        let show = true;

        // Type filter
        if (typeFilter && cardType !== typeFilter) {
            show = false;
        }

        // Category filter
        if (categoryFilter && cardCategory !== categoryFilter) {
            show = false;
        }

        // Search filter
        if (searchQuery && !cardTitle.includes(searchQuery) && !cardContent.includes(searchQuery)) {
            show = false;
        }

        if (show) {
            card.style.display = 'block';
            visibleCards.push(card);
        } else {
            card.style.display = 'none';
        }
    });

    // Sort visible cards
    if (sortFilter && visibleCards.length > 0) {
        const parent = visibleCards[0].parentNode;
        visibleCards.sort((a, b) => {
            switch (sortFilter) {
                case 'title':
                    return a.querySelector('.capture-card-title').textContent.localeCompare(
                        b.querySelector('.capture-card-title').textContent
                    );
                case 'type':
                    return a.dataset.type.localeCompare(b.dataset.type);
                case 'oldest':
                    // Would need timestamp data for proper sorting
                    return 0;
                case 'newest':
                default:
                    return 0;
            }
        });

        visibleCards.forEach(card => parent.appendChild(card));
    }
}

// Capture Actions
function togglePin(captureId) {
    fetch(`/momentum/quick-capture/toggle-pin/${captureId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to toggle pin: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to toggle pin');
    });
}

function deleteCapture(captureId) {
    if (confirm('Are you sure you want to delete this capture?')) {
        fetch(`/momentum/quick-capture/delete/${captureId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to delete capture: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to delete capture');
        });
    }
}

function loadMore() {
    // Implement pagination loading
    alert('Load more functionality would be implemented here');
}

// Auto-apply filters on change
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('typeFilter').addEventListener('change', applyFilters);
    document.getElementById('categoryFilter').addEventListener('change', applyFilters);
    document.getElementById('sortFilter').addEventListener('change', applyFilters);
    document.getElementById('searchInput').addEventListener('input', applyFilters);

    // Close modal when clicking outside
    document.getElementById('quickCaptureModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeQuickCapture();
        }
    });
});
</script>
