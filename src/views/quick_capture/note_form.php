<?php
/**
 * Quick Note Creation Form
 */
?>

<div class="quick-capture-container">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="flex items-center mb-6">
            <a href="/momentum/quick-capture" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 mr-4">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Create Quick Note</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Capture your thoughts instantly</p>
            </div>
        </div>

        <!-- Note Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <form id="noteForm" method="POST" action="/momentum/quick-capture/note/create">
                <div class="mb-4">
                    <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Title (Optional)
                    </label>
                    <input type="text" id="title" name="title" 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                           placeholder="Enter a title for your note...">
                </div>

                <div class="mb-4">
                    <label for="content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Content <span class="text-red-500">*</span>
                    </label>
                    <textarea id="content" name="content" rows="10" required
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white resize-none"
                              placeholder="Write your note here..."></textarea>
                </div>

                <div class="mb-4">
                    <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Tags (Optional)
                    </label>
                    <input type="text" id="tags" name="tags" 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                           placeholder="Enter tags separated by commas...">
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Example: meeting, ideas, todo</p>
                </div>

                <div class="mb-4">
                    <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Category (Optional)
                    </label>
                    <input type="text" id="category" name="category" 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                           placeholder="Enter a category...">
                </div>

                <div class="mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" id="is_pinned" name="is_pinned" value="1" 
                               class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Pin this note</span>
                    </label>
                </div>

                <div class="flex justify-end space-x-3">
                    <a href="/momentum/quick-capture" 
                       class="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i>
                        Save Note
                    </button>
                </div>
            </form>
        </div>

        <!-- Quick Tips -->
        <div class="mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
            <h3 class="text-sm font-medium text-green-800 dark:text-green-300 mb-2">
                <i class="fas fa-lightbulb mr-1"></i>
                Quick Tips
            </h3>
            <ul class="text-xs text-green-700 dark:text-green-400 space-y-1">
                <li>• Use tags to organize your notes for easy searching</li>
                <li>• Pin important notes to keep them at the top</li>
                <li>• Categories help group related notes together</li>
                <li>• You can link notes to prompts and tasks later</li>
            </ul>
        </div>
    </div>
</div>

<script>
document.getElementById('noteForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    // Validate content
    if (!data.content || !data.content.trim()) {
        alert('Note content is required');
        return;
    }
    
    try {
        const response = await fetch('/momentum/quick-capture/note/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Show success message
            showNotification('Note created successfully!', 'success');
            
            // Redirect after a short delay
            setTimeout(() => {
                window.location.href = '/momentum/quick-capture/view/' + result.capture_id;
            }, 1000);
        } else {
            showNotification('Failed to create note: ' + (result.error || 'Unknown error'), 'error');
        }
        
    } catch (error) {
        console.error('Error creating note:', error);
        showNotification('Failed to create note', 'error');
    }
});

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${
                type === 'success' ? 'fa-check-circle' :
                type === 'error' ? 'fa-exclamation-circle' :
                'fa-info-circle'
            } mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Auto-focus content textarea
document.getElementById('content').focus();
</script>
