<?php
/**
 * Edit Capture Form
 */
?>

<div class="quick-capture-container">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="flex items-center mb-6">
            <a href="/momentum/quick-capture/view/<?= $capture['id'] ?>" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 mr-4">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Capture</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Update your <?= htmlspecialchars($capture['type']) ?> capture</p>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <form action="/momentum/quick-capture/update/<?= $capture['id'] ?>" method="POST" id="editCaptureForm">
                <!-- Title -->
                <div class="mb-6">
                    <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Title
                    </label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           value="<?= htmlspecialchars($capture['title'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                           placeholder="Enter a title for this capture">
                </div>

                <!-- Content (for notes and voice captures) -->
                <?php if ($capture['type'] === 'note' || $capture['type'] === 'voice'): ?>
                <div class="mb-6">
                    <label for="content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Content
                        <?php if ($capture['type'] === 'voice'): ?>
                            <span class="text-sm text-gray-500">(Transcription or notes)</span>
                        <?php endif; ?>
                    </label>
                    <textarea id="content" 
                              name="content" 
                              rows="8"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                              placeholder="Enter content..."><?= htmlspecialchars($capture['content'] ?? '') ?></textarea>
                </div>
                <?php endif; ?>

                <!-- OCR Text (for screenshots) -->
                <?php if ($capture['type'] === 'screenshot' && $capture['ocr_text']): ?>
                <div class="mb-6">
                    <label for="ocr_text" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Extracted Text (OCR)
                        <span class="text-sm text-gray-500">(Read-only)</span>
                    </label>
                    <textarea id="ocr_text" 
                              rows="4"
                              readonly
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-gray-600 text-gray-700 dark:text-gray-300"><?= htmlspecialchars($capture['ocr_text']) ?></textarea>
                </div>
                <?php endif; ?>

                <!-- Category -->
                <div class="mb-6">
                    <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Category
                    </label>
                    <select id="category" 
                            name="category"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="">Select a category</option>
                        <?php if (!empty($categories)): ?>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?= htmlspecialchars($cat['category']) ?>" 
                                        <?= ($capture['category'] === $cat['category']) ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($cat['category']) ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>

                <!-- Tags -->
                <div class="mb-6">
                    <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Tags
                        <span class="text-sm text-gray-500">(Separate with commas)</span>
                    </label>
                    <input type="text" 
                           id="tags" 
                           name="tags" 
                           value="<?= htmlspecialchars($capture['tags'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                           placeholder="tag1, tag2, tag3">
                </div>

                <!-- Pin Status -->
                <div class="mb-6">
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="is_pinned" 
                               name="is_pinned" 
                               value="1"
                               <?= $capture['is_pinned'] ? 'checked' : '' ?>
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="is_pinned" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                            Pin this capture
                        </label>
                    </div>
                </div>

                <!-- Linking Options -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Link to Other Items</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- Link to Prompt -->
                        <div>
                            <label for="linked_prompt_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                AI Prompt
                            </label>
                            <select id="linked_prompt_id" 
                                    name="linked_prompt_id"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                <option value="">None</option>
                                <!-- Prompts would be loaded here -->
                            </select>
                        </div>

                        <!-- Link to Task -->
                        <div>
                            <label for="linked_task_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Task
                            </label>
                            <select id="linked_task_id" 
                                    name="linked_task_id"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                <option value="">None</option>
                                <!-- Tasks would be loaded here -->
                            </select>
                        </div>

                        <!-- Link to Project -->
                        <div>
                            <label for="linked_project_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Project
                            </label>
                            <select id="linked_project_id" 
                                    name="linked_project_id"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                <option value="">None</option>
                                <!-- Projects would be loaded here -->
                            </select>
                        </div>
                    </div>
                </div>

                <!-- File Information (if applicable) -->
                <?php if ($capture['file_path']): ?>
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">File Information</h3>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-gray-700 dark:text-gray-300">File Type:</span>
                                <span class="text-gray-600 dark:text-gray-400"><?= htmlspecialchars($capture['file_type'] ?? 'Unknown') ?></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700 dark:text-gray-300">File Size:</span>
                                <span class="text-gray-600 dark:text-gray-400"><?= $capture['file_size'] ? number_format($capture['file_size'] / 1024, 1) . ' KB' : 'Unknown' ?></span>
                            </div>
                        </div>
                        <?php if ($capture['type'] === 'screenshot' && $capture['thumbnail_path']): ?>
                        <div class="mt-4">
                            <img src="<?= htmlspecialchars($capture['thumbnail_path']) ?>" 
                                 alt="Screenshot preview" 
                                 class="max-w-xs rounded-lg border border-gray-200 dark:border-gray-600">
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-600">
                    <div class="flex space-x-3">
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-save mr-2"></i>
                            Save Changes
                        </button>
                        
                        <a href="/momentum/quick-capture/view/<?= $capture['id'] ?>" 
                           class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-times mr-2"></i>
                            Cancel
                        </a>
                    </div>
                    
                    <button type="button" 
                            onclick="deleteCapture(<?= $capture['id'] ?>)"
                            class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-trash mr-2"></i>
                        Delete
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function deleteCapture(captureId) {
    if (confirm('Are you sure you want to delete this capture? This action cannot be undone.')) {
        fetch(`/momentum/quick-capture/delete/${captureId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '/momentum/quick-capture';
            } else {
                alert('Failed to delete capture: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to delete capture');
        });
    }
}

// Auto-save functionality (optional)
let autoSaveTimeout;
function autoSave() {
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(() => {
        // Could implement auto-save here
        console.log('Auto-save would trigger here');
    }, 5000);
}

// Add auto-save listeners to form fields
document.getElementById('title').addEventListener('input', autoSave);
document.getElementById('content')?.addEventListener('input', autoSave);
document.getElementById('tags').addEventListener('input', autoSave);
</script>
