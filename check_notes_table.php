<?php
require_once 'src/utils/Database.php';

try {
    $db = Database::getInstance();
    echo "Checking notes table structure...\n\n";
    
    $result = $db->fetchAll('DESCRIBE notes');
    
    echo "Current columns in notes table:\n";
    echo str_pad('Field', 20) . str_pad('Type', 25) . str_pad('Null', 8) . str_pad('Key', 8) . "Default\n";
    echo str_repeat('-', 80) . "\n";
    
    foreach ($result as $column) {
        echo str_pad($column['Field'], 20) . 
             str_pad($column['Type'], 25) . 
             str_pad($column['Null'], 8) . 
             str_pad($column['Key'], 8) . 
             ($column['Default'] ?? 'NULL') . "\n";
    }
    
    echo "\nChecking for new enhanced columns:\n";
    $enhancedColumns = [
        'is_favorite', 'priority_level', 'last_accessed', 'auto_saved', 
        'word_count', 'reading_time', 'color_code', 'template_id', 
        'parent_note_id', 'note_type'
    ];
    
    $existingColumns = array_column($result, 'Field');
    
    foreach ($enhancedColumns as $column) {
        $exists = in_array($column, $existingColumns);
        echo ($exists ? '✓' : '✗') . " $column" . ($exists ? '' : ' (missing)') . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
