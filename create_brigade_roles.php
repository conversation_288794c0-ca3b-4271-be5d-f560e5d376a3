<?php
/**
 * Create Default Brigade Roles
 * 
 * This script creates the default roles for each brigade type.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/AgentBrigadeRole.php';

// Initialize models
$db = Database::getInstance();
$brigadeRoleModel = new AgentBrigadeRole();

// Check database connection
if (!$db) {
    die("Database connection failed");
}

// Create default brigade roles
$success = $brigadeRoleModel->createDefaultRoles();

if ($success) {
    echo "Default brigade roles created successfully!";
} else {
    echo "Failed to create some default brigade roles.";
}
