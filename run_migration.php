<?php
/**
 * Migration Runner for Enhanced Notes
 */

require_once 'src/utils/Database.php';

try {
    echo "Starting notes enhancement migration...\n";

    $db = Database::getInstance();
    $migration = file_get_contents('database/migrations/enhance_notes_table.sql');

    // Split by semicolon and execute each statement
    $statements = explode(';', $migration);
    $executed = 0;
    $errors = 0;

    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $db->query($statement);
                echo "✓ Executed: " . substr($statement, 0, 50) . "...\n";
                $executed++;
            } catch (Exception $e) {
                echo "✗ Error: " . $e->getMessage() . "\n";
                echo "  Statement: " . substr($statement, 0, 100) . "...\n";
                $errors++;
            }
        }
    }

    echo "\nMigration completed!\n";
    echo "Executed: $executed statements\n";
    echo "Errors: $errors\n";

    if ($errors === 0) {
        echo "\n🎉 All migrations executed successfully!\n";
        echo "Enhanced notes features are now available:\n";
        echo "- Auto-save functionality\n";
        echo "- Priority levels and favorites\n";
        echo "- Note templates\n";
        echo "- Enhanced search and analytics\n";
        echo "- ADHD-friendly features\n";
    }

} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
