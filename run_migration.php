<?php
require_once 'src/utils/Database.php';

// Create database connection using singleton pattern
$db = Database::getInstance();

// Load SQL file
$sql = file_get_contents('database/migrations/pet_reminders_table.sql');

// Execute SQL
try {
    $db->query($sql);
    echo "Migration successful: pet_reminders table created or updated.\n";
} catch (Exception $e) {
    echo "Error running migration: " . $e->getMessage() . "\n";
}
