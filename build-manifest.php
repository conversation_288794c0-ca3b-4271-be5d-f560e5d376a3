<?php
/**
 * Build Asset Manifest
 *
 * This script generates a manifest file for asset versioning.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Function to scan directory recursively
function scanDirectory($dir, $baseDir = '') {
    $result = [];
    $files = scandir($dir);
    
    foreach ($files as $file) {
        if ($file === '.' || $file === '..') {
            continue;
        }
        
        $path = $dir . '/' . $file;
        $relativePath = $baseDir ? $baseDir . '/' . $file : $file;
        
        if (is_dir($path)) {
            $result = array_merge($result, scanDirectory($path, $relativePath));
        } else {
            $result[] = $relativePath;
        }
    }
    
    return $result;
}

// Scan dist directory
$distDir = BASE_PATH . '/public/dist';
$files = scanDirectory($distDir, 'dist');

// Build manifest
$manifest = [];

foreach ($files as $file) {
    // Skip manifest file
    if ($file === 'dist/manifest.json') {
        continue;
    }
    
    // Get file extension
    $extension = pathinfo($file, PATHINFO_EXTENSION);
    
    // Only include CSS and JS files
    if ($extension === 'css' || $extension === 'js') {
        // Extract original file name (without hash)
        $originalName = preg_replace('/\.[a-f0-9]{8,}\./', '.', $file);
        
        // Add to manifest
        $manifest['/' . $originalName] = '/' . $file;
    }
}

// Save manifest
$manifestPath = $distDir . '/manifest.json';
$manifestJson = json_encode($manifest, JSON_PRETTY_PRINT);
file_put_contents($manifestPath, $manifestJson);

echo "Manifest file generated successfully.\n";
echo "Total assets: " . count($manifest) . "\n";
