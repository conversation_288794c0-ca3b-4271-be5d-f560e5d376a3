<?php
/**
 * Test Pinterest Integration
 * 
 * This script tests the Pinterest integration with different API types.
 */

// Include required files
require_once 'src/utils/Environment.php';
require_once 'src/api/PinterestAPIFactory.php';
require_once 'src/data/pinterest_data_real.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load environment variables
Environment::load();

// Output HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pinterest Integration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1, h2, h3 { color: #333; }
        .test-section { margin-bottom: 30px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .pin-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 20px; margin-top: 20px; }
        .pin-card { border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
        .pin-image { width: 100%; height: 200px; object-fit: cover; }
        .pin-content { padding: 10px; }
        .pin-title { font-weight: bold; margin-bottom: 5px; }
        .pin-description { font-size: 0.9em; color: #666; margin-bottom: 10px; }
        .pin-meta { font-size: 0.8em; color: #999; }
        .pin-actions { display: flex; gap: 10px; margin-top: 10px; }
        .pin-button { display: inline-block; padding: 5px 10px; background: #e60023; color: white; text-decoration: none; border-radius: 4px; font-size: 0.8em; }
        .pin-button:hover { background: #d50c22; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; max-height: 300px; }
        .config-section { background: #f8f9fa; padding: 15px; border-left: 4px solid #17a2b8; margin-bottom: 20px; }
        .api-type { display: inline-block; padding: 3px 8px; border-radius: 3px; background: #e9ecef; margin-right: 5px; }
        .api-type.active { background: #28a745; color: white; }
    </style>
</head>
<body>
    <h1>Test Pinterest Integration</h1>';

// Display current configuration
$apiType = Environment::get('PINTEREST_API_TYPE', 'unofficial');
$disableApi = Environment::get('DISABLE_PINTEREST_API') === 'true';

echo '<div class="config-section">
    <h2>Current Configuration</h2>
    <p>
        <strong>API Type:</strong> 
        <span class="api-type' . ($apiType === 'unofficial' ? ' active' : '') . '">Unofficial (py3-pinterest)</span>
        <span class="api-type' . ($apiType === 'official' ? ' active' : '') . '">Official (API Key)</span>
        <span class="api-type' . ($apiType === 'fallback' ? ' active' : '') . '">Fallback (No API)</span>
    </p>
    <p><strong>API Disabled:</strong> ' . ($disableApi ? '<span class="error">Yes</span>' : '<span class="success">No</span>') . '</p>
    <p><strong>Email:</strong> ' . htmlspecialchars(Environment::get('PINTEREST_EMAIL', '')) . '</p>
    <p><strong>Username:</strong> ' . htmlspecialchars(Environment::get('PINTEREST_USERNAME', '')) . '</p>
    <p><strong>API Key:</strong> ' . (Environment::get('PINTEREST_API_KEY') ? 'Set' : 'Not Set') . '</p>
    <p><strong>Access Token:</strong> ' . (Environment::get('PINTEREST_ACCESS_TOKEN') ? 'Set' : 'Not Set') . '</p>
</div>';

// Test 1: Get Pinterest API
echo '<div class="test-section">
    <h2>Test 1: Get Pinterest API</h2>';

$api = PinterestAPIFactory::getAPI();

if ($api) {
    echo '<p class="success">Successfully got Pinterest API instance: ' . get_class($api) . '</p>';
} else {
    echo '<p class="error">Failed to get Pinterest API instance. Using fallback data.</p>';
}

echo '</div>';

// Test 2: Search for pins
echo '<div class="test-section">
    <h2>Test 2: Search for Pins</h2>';

$searchTerm = 'digital marketing';
$pins = PinterestAPIFactory::search($searchTerm, 10);

if (!empty($pins)) {
    echo '<p class="success">Found ' . count($pins) . ' pins for search term "' . $searchTerm . '"</p>';
    
    // Display the pins
    echo '<div class="pin-grid">';
    foreach (array_slice($pins, 0, 4) as $pin) {
        echo '<div class="pin-card">
            <img src="' . htmlspecialchars($pin['image_url'] ?? 'https://via.placeholder.com/600x800/f8f9fa/dc3545?text=No+Image') . '" alt="' . htmlspecialchars($pin['title'] ?? 'Pinterest Pin') . '" class="pin-image">
            <div class="pin-content">
                <div class="pin-title">' . htmlspecialchars($pin['title'] ?? 'Pinterest Pin') . '</div>
                <div class="pin-description">' . htmlspecialchars(substr($pin['description'] ?? '', 0, 100)) . '...</div>
                <div class="pin-meta">
                    <div>Board: ' . htmlspecialchars($pin['board_name'] ?? 'Pinterest Board') . '</div>
                    <div>Saves: ' . number_format($pin['save_count'] ?? 0) . '</div>
                </div>
                <div class="pin-actions">
                    <a href="' . htmlspecialchars($pin['pin_url'] ?? '') . '" target="_blank" class="pin-button">View on Pinterest</a>
                </div>
            </div>
        </div>';
    }
    echo '</div>';
    
    // Show the raw data for the first pin
    echo '<h3>Raw data for first pin:</h3>';
    echo '<pre>' . htmlspecialchars(print_r($pins[0], true)) . '</pre>';
} else {
    echo '<p class="error">No pins found for search term "' . $searchTerm . '"</p>';
}

echo '</div>';

// Test 3: Generate Pinterest data
echo '<div class="test-section">
    <h2>Test 3: Generate Pinterest Data</h2>';

$pinterestData = getPinterestData($searchTerm, 5);

if (!empty($pinterestData)) {
    echo '<p class="success">Successfully generated Pinterest data for search term "' . $searchTerm . '"</p>';
    
    // Display the pins
    echo '<div class="pin-grid">';
    foreach ($pinterestData as $pin) {
        echo '<div class="pin-card">
            <img src="' . htmlspecialchars($pin['image_url'] ?? 'https://via.placeholder.com/600x800/f8f9fa/dc3545?text=No+Image') . '" alt="' . htmlspecialchars($pin['title'] ?? 'Pinterest Pin') . '" class="pin-image">
            <div class="pin-content">
                <div class="pin-title">' . htmlspecialchars($pin['title'] ?? 'Pinterest Pin') . '</div>
                <div class="pin-description">' . htmlspecialchars(substr($pin['description'] ?? '', 0, 100)) . '...</div>
                <div class="pin-meta">
                    <div>Board: ' . htmlspecialchars($pin['board_name'] ?? 'Pinterest Board') . '</div>
                    <div>Saves: ' . number_format($pin['save_count'] ?? 0) . '</div>
                </div>
                <div class="pin-actions">
                    <a href="' . htmlspecialchars($pin['pin_url'] ?? '') . '" target="_blank" class="pin-button">View on Pinterest</a>
                </div>
            </div>
        </div>';
    }
    echo '</div>';
    
    // Show the raw data for the first pin
    echo '<h3>Raw data for first pin:</h3>';
    echo '<pre>' . htmlspecialchars(print_r($pinterestData[0], true)) . '</pre>';
} else {
    echo '<p class="error">Failed to generate Pinterest data for search term "' . $searchTerm . '"</p>';
}

echo '</div>';

// Test 4: Download an image
echo '<div class="test-section">
    <h2>Test 4: Download Image</h2>';

$imageUrl = '';
$pinId = '';

if (!empty($pins)) {
    // Use the first pin from the search results
    $imageUrl = $pins[0]['image_url'] ?? '';
    $pinId = $pins[0]['pin_id'] ?? '';
} else if (!empty($pinterestData)) {
    // Use the first pin from the generated data
    $imageUrl = $pinterestData[0]['image_url'] ?? '';
    $pinId = $pinterestData[0]['pin_id'] ?? '';
} else {
    // Use a fallback image URL
    $imageUrl = 'https://via.placeholder.com/600x800/f8f9fa/dc3545?text=Pinterest+Image';
    $pinId = 'placeholder';
}

// Create the output directory if it doesn't exist
$outputDir = 'public/uploads/pinterest';
if (!file_exists($outputDir)) {
    mkdir($outputDir, 0755, true);
}

$outputPath = $outputDir . '/test_' . $pinId . '.jpg';

$downloadResult = PinterestAPIFactory::downloadImage($imageUrl, $outputPath);

if ($downloadResult) {
    echo '<p class="success">Successfully downloaded image to: ' . $outputPath . '</p>';
    echo '<img src="' . $outputPath . '" alt="Downloaded image" style="max-width: 300px; display: block; margin: 20px auto; border: 1px solid #ddd;">';
} else {
    echo '<p class="error">Failed to download image</p>';
}

echo '</div>';

// Server information
echo '<div class="test-section">
    <h2>Server Information</h2>
    <pre>';
echo 'PHP Version: ' . phpversion() . "\n";
echo 'Server Software: ' . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo 'User Agent: ' . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "\n";
echo 'Document Root: ' . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "\n";
echo 'Python Version: ' . trim(shell_exec('python --version 2>&1') ?? 'Unknown') . "\n";
echo 'Chrome Installed: ' . (file_exists('C:/Program Files/Google/Chrome/Application/chrome.exe') ? 'Yes' : 'No') . "\n";
echo '</pre>
</div>';

echo '</body>
</html>';
