<?php
require_once 'src/utils/Database.php';
require_once 'src/models/IncomeSource.php';

// Create a new instance of the IncomeSource model
$incomeSourceModel = new IncomeSource();

// Set user ID (replace with an actual user ID from your database)
$userId = 1; // Assuming user ID 1 exists

// Set date range
$startDate = date('Y-m-01'); // First day of current month
$endDate = date('Y-m-t'); // Last day of current month

// Get income sources with filtering
$incomeSources = $incomeSourceModel->getSourcesWithTotalIncome($userId, $startDate, $endDate);

// Get all income sources without filtering
$db = Database::getInstance();
$allSources = $db->fetchAll("SELECT * FROM income_sources");

// Output results
echo "User ID: $userId\n";
echo "Start Date: $startDate\n";
echo "End Date: $endDate\n";
echo "Number of income sources found with filtering: " . count($incomeSources) . "\n\n";

if (count($incomeSources) > 0) {
    echo "Income Sources (with filtering):\n";
    foreach ($incomeSources as $source) {
        echo "- ID: " . $source['id'] . "\n";
        echo "  Name: " . $source['name'] . "\n";
        echo "  Active: " . ($source['is_active'] ? 'Yes' : 'No') . "\n";
        echo "  Recurring: " . ($source['is_recurring'] ? 'Yes' : 'No') . "\n";
        echo "  Category: " . ($source['category'] ?? 'N/A') . "\n";
        echo "  Transaction Count: " . ($source['transaction_count'] ?? 0) . "\n";
        echo "  Total Income: " . ($source['total_income'] ?? 0) . "\n\n";
    }
} else {
    echo "No income sources found with filtering.\n";
}

echo "\nNumber of all income sources in database: " . count($allSources) . "\n\n";

if (count($allSources) > 0) {
    echo "All Income Sources in Database:\n";
    foreach ($allSources as $source) {
        echo "- ID: " . $source['id'] . "\n";
        echo "  User ID: " . $source['user_id'] . "\n";
        echo "  Name: " . $source['name'] . "\n";
        echo "  Active: " . ($source['is_active'] ? 'Yes' : 'No') . "\n";
        echo "  Recurring: " . ($source['is_recurring'] ? 'Yes' : 'No') . "\n";
        echo "  Category: " . ($source['category'] ?? 'N/A') . "\n\n";
    }
} else {
    echo "No income sources found in database.\n";
}

// Check if the view file exists
$viewPath = 'src/views/finances/income_sources/index.php';
echo "View file exists: " . (file_exists($viewPath) ? 'Yes' : 'No') . "\n";

// Check if the controller file exists
$controllerPath = 'src/controllers/IncomeSourceController.php';
echo "Controller file exists: " . (file_exists($controllerPath) ? 'Yes' : 'No') . "\n";

// Check if the model file exists
$modelPath = 'src/models/IncomeSource.php';
echo "Model file exists: " . (file_exists($modelPath) ? 'Yes' : 'No') . "\n";
