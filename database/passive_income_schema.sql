-- Passive Income Portfolio Schema

-- Create passive_income_streams table
CREATE TABLE passive_income_streams (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  opportunity_id INT,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  platform VARCHAR(255),
  category VARCHAR(100) NOT NULL,
  setup_date DATE,
  initial_investment DECIMAL(10,2),
  maintenance_hours_per_month DECIMAL(5,2),
  status ENUM('setup', 'growing', 'stable', 'declining', 'inactive') NOT NULL DEFAULT 'setup',
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (opportunity_id) REFERENCES income_opportunities(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create passive_income_earnings table
CREATE TABLE passive_income_earnings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  stream_id INT NOT NULL,
  earning_date DATE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (stream_id) REFERENCES passive_income_streams(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create passive_income_maintenance table
CREATE TABLE passive_income_maintenance (
  id INT AUTO_INCREMENT PRIMARY KEY,
  stream_id INT NOT NULL,
  maintenance_date DATE NOT NULL,
  hours_spent DECIMAL(5,2) NOT NULL,
  tasks_performed TEXT,
  next_maintenance_date DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (stream_id) REFERENCES passive_income_streams(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create indexes for better performance
ALTER TABLE passive_income_streams ADD INDEX idx_user_id (user_id);
ALTER TABLE passive_income_streams ADD INDEX idx_opportunity_id (opportunity_id);
ALTER TABLE passive_income_streams ADD INDEX idx_status (status);
ALTER TABLE passive_income_streams ADD INDEX idx_category (category);

ALTER TABLE passive_income_earnings ADD INDEX idx_stream_id (stream_id);
ALTER TABLE passive_income_earnings ADD INDEX idx_earning_date (earning_date);

ALTER TABLE passive_income_maintenance ADD INDEX idx_stream_id (stream_id);
ALTER TABLE passive_income_maintenance ADD INDEX idx_maintenance_date (maintenance_date);
ALTER TABLE passive_income_maintenance ADD INDEX idx_next_maintenance_date (next_maintenance_date);
