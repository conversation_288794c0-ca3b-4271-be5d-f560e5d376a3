-- Online Business Dashboard Schema

-- Create business_ventures table
CREATE TABLE business_ventures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    business_type VARCHAR(100) NOT NULL,
    website VARCHAR(255),
    start_date DATE,
    status ENUM('planning', 'startup', 'operational', 'growing', 'declining', 'inactive') DEFAULT 'planning',
    notes TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create business_products table
CREATE TABLE business_products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    venture_id INT NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    product_type ENUM('physical', 'digital', 'service', 'subscription') NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    cost DECIMAL(10, 2),
    inventory INT,
    sku VARCHAR(100),
    status ENUM('active', 'inactive', 'draft', 'archived') DEFAULT 'active',
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (venture_id) REFERENCES business_ventures(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create business_sales table
CREATE TABLE business_sales (
    id INT AUTO_INCREMENT PRIMARY KEY,
    venture_id INT NOT NULL,
    product_id INT,
    sale_date DATE NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    unit_price DECIMAL(10, 2) NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    customer_id INT,
    platform VARCHAR(100),
    transaction_id VARCHAR(100),
    notes TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (venture_id) REFERENCES business_ventures(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES business_products(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create business_expenses table
CREATE TABLE business_expenses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    venture_id INT NOT NULL,
    expense_date DATE NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    category VARCHAR(100) NOT NULL,
    description TEXT,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_period VARCHAR(50),
    next_due_date DATE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (venture_id) REFERENCES business_ventures(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create business_customers table
CREATE TABLE business_customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    venture_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    customer_since DATE,
    lifetime_value DECIMAL(10, 2) DEFAULT 0.00,
    notes TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (venture_id) REFERENCES business_ventures(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create business_marketing table
CREATE TABLE business_marketing (
    id INT AUTO_INCREMENT PRIMARY KEY,
    venture_id INT NOT NULL,
    campaign_name VARCHAR(255) NOT NULL,
    platform VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    budget DECIMAL(10, 2),
    spent DECIMAL(10, 2) DEFAULT 0.00,
    status ENUM('planned', 'active', 'completed', 'cancelled') DEFAULT 'planned',
    results TEXT,
    notes TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (venture_id) REFERENCES business_ventures(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create business_metrics table for storing key performance indicators
CREATE TABLE business_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    venture_id INT NOT NULL,
    metric_date DATE NOT NULL,
    revenue DECIMAL(10, 2) DEFAULT 0.00,
    expenses DECIMAL(10, 2) DEFAULT 0.00,
    profit DECIMAL(10, 2) DEFAULT 0.00,
    sales_count INT DEFAULT 0,
    new_customers INT DEFAULT 0,
    website_visits INT,
    conversion_rate DECIMAL(5, 2),
    average_order_value DECIMAL(10, 2),
    notes TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (venture_id) REFERENCES business_ventures(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
