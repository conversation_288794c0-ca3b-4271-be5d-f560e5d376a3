-- Seed data for executive function exercises
INSERT INTO `executive_function_exercises`
(`name`, `description`, `instructions`, `category`, `difficulty_level`, `estimated_time`, `created_at`, `updated_at`)
VALUES
-- Working Memory Exercises
('Number Sequence Recall', 'Practice remembering and recalling sequences of numbers to improve working memory.', 'You will be shown a sequence of numbers for 5 seconds. After they disappear, try to recall and write down the complete sequence in the correct order. Start with 5 digits and gradually increase as you improve.', 'working_memory', 1, 10, NOW(), NOW()),

('Dual N-Back', 'A challenging working memory exercise that trains your brain to hold and manipulate multiple pieces of information.', 'You will be presented with a sequence of visual and auditory stimuli. Your task is to indicate when the current stimulus matches the one from N steps earlier in the sequence. Start with 1-back and gradually increase to 2-back and 3-back as you improve.', 'working_memory', 3, 15, NOW(), NOW()),

('Reverse Word Recall', 'Improve working memory by recalling words in reverse order.', 'Read a list of 5-10 words at a normal pace. When finished, try to recall and write down the words in reverse order, starting from the last word and working backward to the first.', 'working_memory', 2, 10, NOW(), NOW()),

-- Task Initiation Exercises
('5-Minute Start', 'Overcome task initiation difficulties by committing to just 5 minutes of work.', 'Choose a task you have been avoiding. Set a timer for just 5 minutes and commit to working on the task until the timer rings. After 5 minutes, decide whether to continue or stop. Record your experience and feelings before and after.', 'task_initiation', 1, 10, NOW(), NOW()),

('Task Breakdown Practice', 'Practice breaking down overwhelming tasks into smaller, manageable steps.', 'Select a complex task or project you need to complete. Break it down into the smallest possible steps, with each step taking no more than 15-30 minutes to complete. Identify the very first physical action needed to begin. Take that action immediately after completing this exercise.', 'task_initiation', 2, 20, NOW(), NOW()),

('Implementation Intentions', 'Create specific if-then plans to overcome task initiation barriers.', 'Identify a task you struggle to start. Create 3-5 implementation intentions in the format "If [situation/barrier], then I will [specific action]." For example: "If I feel overwhelmed by my inbox, then I will just open and respond to the oldest unread email." Practice implementing these plans over the next week.', 'task_initiation', 2, 15, NOW(), NOW()),

-- Planning Exercises
('Backward Planning', 'Improve planning skills by working backward from the end goal.', 'Choose a goal or project with a specific deadline. Start by visualizing the completed project, then work backward to identify all the steps needed to reach that end point. Create a timeline with specific milestones, working backward from the deadline to today.', 'planning', 2, 25, NOW(), NOW()),

('Daily Planning Routine', 'Establish a consistent daily planning routine to improve executive function.', 'Create a 10-minute planning routine for either the evening (to plan the next day) or morning. Include: 1) Reviewing your calendar, 2) Identifying your top 3 priorities, 3) Estimating time needed for each task, 4) Scheduling specific times for important tasks, 5) Identifying potential obstacles and solutions.', 'planning', 1, 15, NOW(), NOW()),

('Plan B Exercise', 'Strengthen flexible planning by developing contingency plans.', 'Select an upcoming project or event. Identify 3-5 things that could go wrong or obstacles that might arise. For each potential problem, develop a specific "Plan B" response. Consider what resources, support, or alternative approaches you might need.', 'planning', 2, 20, NOW(), NOW()),

-- Organization Exercises
('Space Organization', 'Practice organizing physical spaces using systematic approaches.', 'Select a small, disorganized space (desk drawer, bathroom cabinet, etc.). Remove everything from the space. Sort items into categories. Discard or donate unneeded items. Create a logical organization system for remaining items. Return items to the space according to your system.', 'organization', 1, 30, NOW(), NOW()),

('Information Organization', 'Develop skills for organizing digital or paper information.', 'Choose a collection of information that needs organization (digital files, papers, emails, etc.). Create a logical categorization system. Sort the information according to your system. Create a method for easily retrieving information when needed. Test your system by trying to quickly locate 5 specific items.', 'organization', 2, 25, NOW(), NOW()),

('Priority Matrix', 'Learn to organize tasks based on importance and urgency.', 'Create a 2x2 matrix with axes labeled "Important" and "Urgent". Place all your current tasks and projects into the appropriate quadrant: 1) Important and Urgent, 2) Important but Not Urgent, 3) Urgent but Not Important, 4) Neither Urgent nor Important. Reflect on how you should allocate your time based on this organization.', 'organization', 2, 20, NOW(), NOW()),

-- Time Management Exercises
('Time Estimation Training', 'Improve your ability to accurately estimate how long tasks will take.', 'Select 5-10 routine tasks for the day. Before starting each task, write down your estimate of how long it will take. Time yourself as you complete each task. Record the actual time taken. Calculate the difference between your estimate and reality. Reflect on patterns in your estimation accuracy.', 'time_management', 1, 15, NOW(), NOW()),

('Pomodoro Technique Practice', 'Train yourself to work in focused intervals with scheduled breaks.', 'Choose a task that requires concentration. Set a timer for 25 minutes and work on the task without interruptions. When the timer rings, take a 5-minute break. Repeat this cycle 4 times, then take a longer 15-30 minute break. Record your focus level and productivity during each work interval.', 'time_management', 1, 30, NOW(), NOW()),

('Time Blocking Implementation', 'Practice allocating specific time blocks for different types of activities.', 'Create a schedule for tomorrow with specific time blocks allocated for different types of activities (focused work, meetings, email, breaks, etc.). Try to group similar tasks together. Include buffer time between blocks. Follow your time-blocked schedule tomorrow, noting when and why you deviate from it.', 'time_management', 2, 20, NOW(), NOW()),

-- Emotional Regulation Exercises
('Emotion Labeling Practice', 'Improve emotional awareness by practicing precise emotion labeling.', 'Throughout the day, set 3-5 alarms as reminders to pause and identify your current emotional state. Use a nuanced emotion vocabulary (beyond just "good" or "bad"). Note the emotion, its intensity (1-10), physical sensations, thoughts, and any triggers. Practice without judgment.', 'emotional_regulation', 1, 15, NOW(), NOW()),

('Cognitive Reframing', 'Practice reinterpreting situations to manage emotional responses.', 'Identify a recent situation that triggered a strong negative emotion. Write down your initial thoughts about the situation. Then generate at least 3 alternative interpretations or perspectives on the same situation. Consider what evidence supports each perspective. Notice how different interpretations affect your emotional response.', 'emotional_regulation', 2, 20, NOW(), NOW()),

('Mindful STOP Practice', 'Learn to interrupt automatic emotional reactions using mindfulness.', 'Practice the STOP technique when you notice strong emotions arising: S - Stop what you are doing. T - Take a breath. O - Observe your thoughts, emotions, and physical sensations without judgment. P - Proceed with awareness, making a conscious choice about how to respond. Record your experience with this technique in different emotional situations.', 'emotional_regulation', 2, 15, NOW(), NOW());
