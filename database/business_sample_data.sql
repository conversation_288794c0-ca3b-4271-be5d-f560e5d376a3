-- Get a user ID to use for the sample data
SET @user_id = (SELECT id FROM users LIMIT 1);

-- Insert sample business ventures
INSERT INTO `business_ventures` (`user_id`, `name`, `description`, `business_type`, `website`, `start_date`, `status`, `notes`, `created_at`, `updated_at`)
VALUES
(@user_id, 'Tech Blog', 'A blog about technology and programming', 'content', 'https://techblog.example.com', '2023-01-15', 'active', 'Focus on JavaScript and PHP tutorials', NOW(), NOW()),
(@user_id, 'Digital Products Store', 'Online store selling digital products and templates', 'ecommerce', 'https://digitalstore.example.com', '2023-03-10', 'active', 'Currently selling design templates and e-books', NOW(), NOW()),
(@user_id, 'Freelance Services', 'Web development and design services', 'service', 'https://freelance.example.com', '2022-11-05', 'active', 'Offering web development, design, and SEO services', NOW(), NOW()),
(@user_id, 'Mobile App', 'Productivity app for Android and iOS', 'saas', 'https://app.example.com', '2023-05-20', 'planning', 'Currently in development phase, launch planned for Q3', NOW(), NOW()),
(@user_id, 'YouTube Channel', 'Educational content about programming', 'youtube', 'https://youtube.com/example', '2023-02-01', 'active', 'Weekly videos about web development and programming', NOW(), NOW());

-- Get the venture IDs
SET @venture1_id = (SELECT id FROM business_ventures WHERE name = 'Tech Blog' AND user_id = @user_id);
SET @venture2_id = (SELECT id FROM business_ventures WHERE name = 'Digital Products Store' AND user_id = @user_id);
SET @venture3_id = (SELECT id FROM business_ventures WHERE name = 'Freelance Services' AND user_id = @user_id);
SET @venture4_id = (SELECT id FROM business_ventures WHERE name = 'Mobile App' AND user_id = @user_id);
SET @venture5_id = (SELECT id FROM business_ventures WHERE name = 'YouTube Channel' AND user_id = @user_id);

-- Insert sample metrics for Tech Blog (last 3 months)
INSERT INTO `business_metrics` (`venture_id`, `metric_date`, `revenue`, `expenses`, `profit`, `sales_count`, `new_customers`, `website_visits`, `conversion_rate`, `average_order_value`, `notes`, `created_at`, `updated_at`)
VALUES
(@venture1_id, DATE_SUB(CURDATE(), INTERVAL 90 DAY), 350.00, 50.00, 300.00, 5, 3, 1200, 0.42, 70.00, 'Started monetizing with affiliate links', NOW(), NOW()),
(@venture1_id, DATE_SUB(CURDATE(), INTERVAL 60 DAY), 420.00, 55.00, 365.00, 6, 4, 1500, 0.40, 70.00, 'Added new affiliate products', NOW(), NOW()),
(@venture1_id, DATE_SUB(CURDATE(), INTERVAL 30 DAY), 580.00, 60.00, 520.00, 8, 5, 1800, 0.44, 72.50, 'Published viral article', NOW(), NOW()),
(@venture1_id, DATE_SUB(CURDATE(), INTERVAL 0 DAY), 650.00, 65.00, 585.00, 9, 6, 2100, 0.43, 72.22, 'Increased ad revenue', NOW(), NOW());

-- Insert sample metrics for Digital Products Store (last 3 months)
INSERT INTO `business_metrics` (`venture_id`, `metric_date`, `revenue`, `expenses`, `profit`, `sales_count`, `new_customers`, `website_visits`, `conversion_rate`, `average_order_value`, `notes`, `created_at`, `updated_at`)
VALUES
(@venture2_id, DATE_SUB(CURDATE(), INTERVAL 90 DAY), 1200.00, 300.00, 900.00, 15, 12, 800, 1.88, 80.00, 'Launched new template pack', NOW(), NOW()),
(@venture2_id, DATE_SUB(CURDATE(), INTERVAL 60 DAY), 1350.00, 320.00, 1030.00, 18, 14, 950, 1.89, 75.00, 'Added new e-book', NOW(), NOW()),
(@venture2_id, DATE_SUB(CURDATE(), INTERVAL 30 DAY), 1500.00, 350.00, 1150.00, 20, 15, 1100, 1.82, 75.00, 'Ran promotion', NOW(), NOW()),
(@venture2_id, DATE_SUB(CURDATE(), INTERVAL 0 DAY), 1800.00, 380.00, 1420.00, 24, 18, 1300, 1.85, 75.00, 'Holiday season boost', NOW(), NOW());

-- Insert sample metrics for Freelance Services (last 3 months)
INSERT INTO `business_metrics` (`venture_id`, `metric_date`, `revenue`, `expenses`, `profit`, `sales_count`, `new_customers`, `website_visits`, `conversion_rate`, `average_order_value`, `notes`, `created_at`, `updated_at`)
VALUES
(@venture3_id, DATE_SUB(CURDATE(), INTERVAL 90 DAY), 2500.00, 200.00, 2300.00, 3, 2, 300, 1.00, 833.33, 'Completed 3 client projects', NOW(), NOW()),
(@venture3_id, DATE_SUB(CURDATE(), INTERVAL 60 DAY), 3000.00, 250.00, 2750.00, 4, 2, 350, 1.14, 750.00, 'Added SEO services', NOW(), NOW()),
(@venture3_id, DATE_SUB(CURDATE(), INTERVAL 30 DAY), 3500.00, 300.00, 3200.00, 5, 3, 400, 1.25, 700.00, 'Expanded client base', NOW(), NOW()),
(@venture3_id, DATE_SUB(CURDATE(), INTERVAL 0 DAY), 4000.00, 350.00, 3650.00, 6, 3, 450, 1.33, 666.67, 'Increased rates', NOW(), NOW());

-- Insert sample metrics for YouTube Channel (last 3 months)
INSERT INTO `business_metrics` (`venture_id`, `metric_date`, `revenue`, `expenses`, `profit`, `sales_count`, `new_customers`, `website_visits`, `conversion_rate`, `average_order_value`, `notes`, `created_at`, `updated_at`)
VALUES
(@venture5_id, DATE_SUB(CURDATE(), INTERVAL 90 DAY), 100.00, 50.00, 50.00, 0, 0, 5000, 0.00, 0.00, 'Started monetization', NOW(), NOW()),
(@venture5_id, DATE_SUB(CURDATE(), INTERVAL 60 DAY), 150.00, 50.00, 100.00, 0, 0, 7500, 0.00, 0.00, 'Increased video frequency', NOW(), NOW()),
(@venture5_id, DATE_SUB(CURDATE(), INTERVAL 30 DAY), 200.00, 60.00, 140.00, 0, 0, 10000, 0.00, 0.00, 'Viral video', NOW(), NOW()),
(@venture5_id, DATE_SUB(CURDATE(), INTERVAL 0 DAY), 250.00, 70.00, 180.00, 0, 0, 12500, 0.00, 0.00, 'Added sponsorship', NOW(), NOW());

-- Insert sample customers
INSERT INTO `business_customers` (`venture_id`, `name`, `email`, `phone`, `customer_since`, `lifetime_value`, `notes`, `created_at`, `updated_at`)
VALUES
(@venture2_id, 'John Smith', '<EMAIL>', '+1234567890', DATE_SUB(CURDATE(), INTERVAL 90 DAY), 240.00, 'Purchased multiple templates', NOW(), NOW()),
(@venture2_id, 'Jane Doe', '<EMAIL>', '+0987654321', DATE_SUB(CURDATE(), INTERVAL 60 DAY), 150.00, 'Interested in e-books', NOW(), NOW()),
(@venture2_id, 'Bob Johnson', '<EMAIL>', '+1122334455', DATE_SUB(CURDATE(), INTERVAL 30 DAY), 75.00, 'First-time customer', NOW(), NOW()),
(@venture3_id, 'Acme Corp', '<EMAIL>', '+5544332211', DATE_SUB(CURDATE(), INTERVAL 90 DAY), 2500.00, 'Ongoing web development project', NOW(), NOW()),
(@venture3_id, 'Tech Startup', '<EMAIL>', '+6677889900', DATE_SUB(CURDATE(), INTERVAL 30 DAY), 1500.00, 'Redesign project', NOW(), NOW());

-- Insert sample sales
INSERT INTO `business_sales` (`venture_id`, `customer_id`, `sale_date`, `product_name`, `quantity`, `unit_price`, `total_amount`, `payment_method`, `notes`, `created_at`, `updated_at`)
VALUES
(@venture2_id, 1, DATE_SUB(CURDATE(), INTERVAL 90 DAY), 'Website Template Pack', 1, 80.00, 80.00, 'PayPal', 'First purchase', NOW(), NOW()),
(@venture2_id, 1, DATE_SUB(CURDATE(), INTERVAL 60 DAY), 'E-commerce Template', 1, 95.00, 95.00, 'PayPal', 'Repeat customer', NOW(), NOW()),
(@venture2_id, 1, DATE_SUB(CURDATE(), INTERVAL 30 DAY), 'SEO E-book', 1, 65.00, 65.00, 'Credit Card', 'Added e-book', NOW(), NOW()),
(@venture2_id, 2, DATE_SUB(CURDATE(), INTERVAL 60 DAY), 'Portfolio Template', 1, 75.00, 75.00, 'PayPal', 'New customer', NOW(), NOW()),
(@venture2_id, 2, DATE_SUB(CURDATE(), INTERVAL 30 DAY), 'Marketing E-book', 1, 75.00, 75.00, 'PayPal', 'E-book purchase', NOW(), NOW()),
(@venture2_id, 3, DATE_SUB(CURDATE(), INTERVAL 30 DAY), 'Blog Template', 1, 75.00, 75.00, 'Credit Card', 'First purchase', NOW(), NOW());

-- Insert sample expenses
INSERT INTO `business_expenses` (`venture_id`, `expense_date`, `category`, `description`, `amount`, `recurring`, `notes`, `created_at`, `updated_at`)
VALUES
(@venture1_id, DATE_SUB(CURDATE(), INTERVAL 90 DAY), 'Hosting', 'Web hosting fees', 20.00, 1, 'Monthly hosting', NOW(), NOW()),
(@venture1_id, DATE_SUB(CURDATE(), INTERVAL 60 DAY), 'Hosting', 'Web hosting fees', 20.00, 1, 'Monthly hosting', NOW(), NOW()),
(@venture1_id, DATE_SUB(CURDATE(), INTERVAL 30 DAY), 'Hosting', 'Web hosting fees', 20.00, 1, 'Monthly hosting', NOW(), NOW()),
(@venture1_id, DATE_SUB(CURDATE(), INTERVAL 0 DAY), 'Hosting', 'Web hosting fees', 20.00, 1, 'Monthly hosting', NOW(), NOW()),
(@venture1_id, DATE_SUB(CURDATE(), INTERVAL 90 DAY), 'Software', 'Premium plugins', 30.00, 0, 'One-time purchase', NOW(), NOW()),
(@venture1_id, DATE_SUB(CURDATE(), INTERVAL 30 DAY), 'Marketing', 'Social media promotion', 40.00, 0, 'Promoted post', NOW(), NOW()),
(@venture2_id, DATE_SUB(CURDATE(), INTERVAL 90 DAY), 'Hosting', 'E-commerce hosting', 50.00, 1, 'Monthly hosting', NOW(), NOW()),
(@venture2_id, DATE_SUB(CURDATE(), INTERVAL 60 DAY), 'Hosting', 'E-commerce hosting', 50.00, 1, 'Monthly hosting', NOW(), NOW()),
(@venture2_id, DATE_SUB(CURDATE(), INTERVAL 30 DAY), 'Hosting', 'E-commerce hosting', 50.00, 1, 'Monthly hosting', NOW(), NOW()),
(@venture2_id, DATE_SUB(CURDATE(), INTERVAL 0 DAY), 'Hosting', 'E-commerce hosting', 50.00, 1, 'Monthly hosting', NOW(), NOW()),
(@venture2_id, DATE_SUB(CURDATE(), INTERVAL 90 DAY), 'Software', 'Design software', 250.00, 0, 'Annual subscription', NOW(), NOW()),
(@venture2_id, DATE_SUB(CURDATE(), INTERVAL 30 DAY), 'Marketing', 'Google Ads', 300.00, 0, 'Ad campaign', NOW(), NOW());

-- Insert sample goals
INSERT INTO `business_goals` (`venture_id`, `name`, `description`, `target_value`, `current_value`, `metric_type`, `start_date`, `target_date`, `status`, `created_at`, `updated_at`)
VALUES
(@venture1_id, 'Monthly Revenue', 'Reach Rs 1,000 monthly revenue', 1000.00, 650.00, 'revenue', DATE_SUB(CURDATE(), INTERVAL 90 DAY), DATE_ADD(CURDATE(), INTERVAL 90 DAY), 'active', NOW(), NOW()),
(@venture1_id, 'Traffic Growth', 'Reach 5,000 monthly visitors', 5000.00, 2100.00, 'traffic', DATE_SUB(CURDATE(), INTERVAL 90 DAY), DATE_ADD(CURDATE(), INTERVAL 180 DAY), 'active', NOW(), NOW()),
(@venture2_id, 'Monthly Sales', 'Reach 50 sales per month', 50.00, 24.00, 'sales', DATE_SUB(CURDATE(), INTERVAL 60 DAY), DATE_ADD(CURDATE(), INTERVAL 120 DAY), 'active', NOW(), NOW()),
(@venture2_id, 'Revenue Target', 'Reach Rs 3,000 monthly revenue', 3000.00, 1800.00, 'revenue', DATE_SUB(CURDATE(), INTERVAL 60 DAY), DATE_ADD(CURDATE(), INTERVAL 180 DAY), 'active', NOW(), NOW()),
(@venture3_id, 'Client Acquisition', 'Reach 10 active clients', 10.00, 6.00, 'clients', DATE_SUB(CURDATE(), INTERVAL 30 DAY), DATE_ADD(CURDATE(), INTERVAL 150 DAY), 'active', NOW(), NOW()),
(@venture5_id, 'Subscriber Goal', 'Reach 10,000 subscribers', 10000.00, 2500.00, 'subscribers', DATE_SUB(CURDATE(), INTERVAL 90 DAY), DATE_ADD(CURDATE(), INTERVAL 270 DAY), 'active', NOW(), NOW()),
(@venture5_id, 'Monthly Revenue', 'Reach Rs 500 monthly revenue', 500.00, 250.00, 'revenue', DATE_SUB(CURDATE(), INTERVAL 90 DAY), DATE_ADD(CURDATE(), INTERVAL 180 DAY), 'active', NOW(), NOW());
