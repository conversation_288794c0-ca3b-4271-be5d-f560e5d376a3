-- Income Opportunities Management Schema

-- Create income_opportunities table
CREATE TABLE income_opportunities (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(100) NOT NULL,
  income_type ENUM('active', 'passive', 'semi-passive') NOT NULL,
  skill_level ENUM('beginner', 'intermediate', 'advanced', 'mixed') NOT NULL,
  startup_cost ENUM('none', 'low', 'medium', 'high') NOT NULL,
  time_commitment ENUM('minimal', 'low', 'medium', 'high') NOT NULL,
  estimated_income_min DECIMAL(10,2),
  estimated_income_max DECIMAL(10,2),
  income_frequency ENUM('hourly', 'daily', 'weekly', 'monthly', 'per-project', 'variable') NOT NULL,
  time_to_first_income VARCHAR(100),
  status ENUM('considering', 'researching', 'implementing', 'active', 'paused', 'abandoned') NOT NULL DEFAULT 'considering',
  priority INT NOT NULL DEFAULT 0,
  notes TEXT,
  resources TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create income_opportunity_logs table
CREATE TABLE income_opportunity_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  opportunity_id INT NOT NULL,
  log_date DATE NOT NULL,
  hours_spent DECIMAL(5,2),
  amount_earned DECIMAL(10,2),
  activities TEXT,
  challenges TEXT,
  wins TEXT,
  next_steps TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (opportunity_id) REFERENCES income_opportunities(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create income_opportunity_milestones table
CREATE TABLE income_opportunity_milestones (
  id INT AUTO_INCREMENT PRIMARY KEY,
  opportunity_id INT NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  target_date DATE,
  completion_date DATE,
  status ENUM('pending', 'in-progress', 'completed', 'delayed') NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (opportunity_id) REFERENCES income_opportunities(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
