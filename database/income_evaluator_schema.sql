-- Income Stream Evaluator Schema

-- Create evaluation_criteria table
CREATE TABLE evaluation_criteria (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  weight DECIMAL(3,2) DEFAULT 1.00,
  criteria_type ENUM('financial', 'time', 'skill', 'risk', 'growth', 'satisfaction') NOT NULL,
  is_default BOOLEAN DEFAULT FALSE,
  user_id INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create opportunity_comparisons table
CREATE TABLE opportunity_comparisons (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  name VA<PERSON>HAR(255) NOT NULL,
  description TEXT,
  date_created TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
  last_updated TIM<PERSON><PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create comparison_opportunities table (junction table)
CREATE TABLE comparison_opportunities (
  id INT AUTO_INCREMENT PRIMARY KEY,
  comparison_id INT NOT NULL,
  opportunity_id INT NOT NULL,
  ranking INT,
  notes TEXT,
  FOREIGN KEY (comparison_id) REFERENCES opportunity_comparisons(id) ON DELETE CASCADE,
  FOREIGN KEY (opportunity_id) REFERENCES income_opportunities(id) ON DELETE CASCADE,
  UNIQUE KEY unique_comparison_opportunity (comparison_id, opportunity_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create comparison_criteria_scores table
CREATE TABLE comparison_criteria_scores (
  id INT AUTO_INCREMENT PRIMARY KEY,
  comparison_id INT NOT NULL,
  opportunity_id INT NOT NULL,
  criteria_id INT NOT NULL,
  score DECIMAL(4,2) NOT NULL,
  notes TEXT,
  FOREIGN KEY (comparison_id) REFERENCES opportunity_comparisons(id) ON DELETE CASCADE,
  FOREIGN KEY (opportunity_id) REFERENCES income_opportunities(id) ON DELETE CASCADE,
  FOREIGN KEY (criteria_id) REFERENCES evaluation_criteria(id) ON DELETE CASCADE,
  UNIQUE KEY unique_comparison_criteria_opportunity (comparison_id, opportunity_id, criteria_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create user_skills table
CREATE TABLE user_skills (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  skill_name VARCHAR(100) NOT NULL,
  proficiency_level ENUM('beginner', 'intermediate', 'advanced', 'expert') NOT NULL,
  years_experience DECIMAL(4,1),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_skill (user_id, skill_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create opportunity_skill_requirements table
CREATE TABLE opportunity_skill_requirements (
  id INT AUTO_INCREMENT PRIMARY KEY,
  opportunity_id INT NOT NULL,
  skill_name VARCHAR(100) NOT NULL,
  required_level ENUM('beginner', 'intermediate', 'advanced', 'expert') NOT NULL,
  importance ENUM('nice-to-have', 'important', 'critical') NOT NULL DEFAULT 'important',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (opportunity_id) REFERENCES income_opportunities(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default evaluation criteria
INSERT INTO evaluation_criteria (name, description, weight, criteria_type, is_default) VALUES
('Income Potential', 'Maximum potential income that can be earned', 1.00, 'financial', TRUE),
('Startup Cost', 'Initial investment required to start', 0.90, 'financial', TRUE),
('Time to First Income', 'How quickly you can start earning', 0.85, 'financial', TRUE),
('Time Commitment', 'Hours per week required to maintain', 0.90, 'time', TRUE),
('Flexibility', 'Ability to work on your own schedule', 0.80, 'time', TRUE),
('Skill Match', 'How well your current skills match requirements', 0.95, 'skill', TRUE),
('Learning Curve', 'How difficult it is to learn required skills', 0.75, 'skill', TRUE),
('Risk Level', 'Potential for loss or failure', 0.85, 'risk', TRUE),
('Market Saturation', 'Level of competition in the market', 0.70, 'risk', TRUE),
('Growth Potential', 'Opportunity for income growth over time', 0.80, 'growth', TRUE),
('Scalability', 'Ability to increase income without proportional time increase', 0.85, 'growth', TRUE),
('Personal Interest', 'How interesting or enjoyable the work is', 0.75, 'satisfaction', TRUE),
('Work-Life Balance', 'Impact on overall lifestyle and wellbeing', 0.80, 'satisfaction', TRUE);

-- Create indexes for better performance
ALTER TABLE evaluation_criteria ADD INDEX idx_criteria_type (criteria_type);
ALTER TABLE evaluation_criteria ADD INDEX idx_user_id (user_id);
ALTER TABLE opportunity_comparisons ADD INDEX idx_user_id (user_id);
ALTER TABLE comparison_opportunities ADD INDEX idx_comparison_id (comparison_id);
ALTER TABLE comparison_opportunities ADD INDEX idx_opportunity_id (opportunity_id);
ALTER TABLE comparison_criteria_scores ADD INDEX idx_comparison_id (comparison_id);
ALTER TABLE comparison_criteria_scores ADD INDEX idx_opportunity_id (opportunity_id);
ALTER TABLE comparison_criteria_scores ADD INDEX idx_criteria_id (criteria_id);
ALTER TABLE user_skills ADD INDEX idx_user_id (user_id);
ALTER TABLE user_skills ADD INDEX idx_skill_name (skill_name);
ALTER TABLE opportunity_skill_requirements ADD INDEX idx_opportunity_id (opportunity_id);
ALTER TABLE opportunity_skill_requirements ADD INDEX idx_skill_name (skill_name);
