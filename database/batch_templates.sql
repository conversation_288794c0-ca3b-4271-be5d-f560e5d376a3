-- Batch Templates table
CREATE TABLE IF NOT EXISTS `batch_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `energy_level` enum('high', 'medium', 'low') NOT NULL DEFAULT 'medium',
  `estimated_time` int(11) DEFAULT NULL COMMENT 'Estimated time in minutes',
  `is_recurring` tinyint(1) NOT NULL DEFAULT 0,
  `recurrence_pattern` enum('daily', 'weekdays', 'weekly', 'monthly') DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `energy_level` (`energy_level`),
  KEY `is_recurring` (`is_recurring`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Batch Template Items table (for task types in a template)
CREATE TABLE IF NOT EXISTS `batch_template_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_id` int(11) NOT NULL,
  `task_type` varchar(255) NOT NULL,
  `description` text,
  `estimated_time` int(11) DEFAULT NULL COMMENT 'Estimated time in minutes',
  `position` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `template_id` (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add foreign key constraints
ALTER TABLE `batch_templates`
  ADD CONSTRAINT `batch_templates_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `batch_template_items`
  ADD CONSTRAINT `batch_template_items_ibfk_1` FOREIGN KEY (`template_id`) REFERENCES `batch_templates` (`id`) ON DELETE CASCADE;

-- Add template-related columns to task_batches table
ALTER TABLE `task_batches`
  ADD COLUMN `template_id` int(11) DEFAULT NULL AFTER `user_id`,
  ADD COLUMN `is_recurring` tinyint(1) NOT NULL DEFAULT 0 AFTER `status`,
  ADD COLUMN `recurrence_pattern` enum('daily', 'weekdays', 'weekly', 'monthly') DEFAULT NULL AFTER `is_recurring`,
  ADD COLUMN `last_generated` date DEFAULT NULL AFTER `recurrence_pattern`;

-- Add indexes and foreign keys
ALTER TABLE `task_batches`
  ADD KEY `template_id` (`template_id`),
  ADD KEY `is_recurring` (`is_recurring`);

-- Add foreign key constraint
ALTER TABLE `task_batches`
  ADD CONSTRAINT `task_batches_template_fk` FOREIGN KEY (`template_id`) REFERENCES `batch_templates` (`id`) ON DELETE SET NULL;
