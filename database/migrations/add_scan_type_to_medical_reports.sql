-- Add scan_type column to medical_test_reports table
ALTER TABLE `medical_test_reports` 
ADD COLUMN `scan_type` VARCHAR(50) DEFAULT NULL AFTER `report_type`,
ADD INDEX `scan_type` (`scan_type`);

-- Update existing scan reports with default scan types
UPDATE `medical_test_reports` 
SET `scan_type` = 'other' 
WHERE `report_type` = 'scan' AND (`scan_type` IS NULL OR `scan_type` = '');

-- Insert common scan parameters
INSERT INTO `common_test_parameters` 
(`test_type`, `parameter_name`, `display_name`, `unit`, `reference_range_min`, `reference_range_max`, `description`, `category`, `created_at`, `updated_at`) 
VALUES
-- X-Ray Parameters
('scan', 'bone_density', 'Bone Density', 'g/cm²', '1.0', '1.5', 'Measure of bone mineral density', 'X-Ray', NOW(), NOW()),
('scan', 'joint_space', 'Joint Space', 'mm', '2', '4', 'Space between joints', 'X-Ray', NOW(), NOW()),
('scan', 'fracture_presence', 'Fracture Presence', '', 'Negative', 'Negative', 'Presence of bone fractures', 'X-Ray', NOW(), NOW()),

-- CT Scan Parameters
('scan', 'hounsfield_units', 'Hounsfield Units', 'HU', '-1000', '3000', 'Measure of radiodensity', 'CT Scan', NOW(), NOW()),
('scan', 'contrast_enhancement', 'Contrast Enhancement', '', 'Normal', 'Normal', 'Pattern of contrast enhancement', 'CT Scan', NOW(), NOW()),
('scan', 'lesion_size', 'Lesion Size', 'mm', '0', '0', 'Size of any lesions detected', 'CT Scan', NOW(), NOW()),

-- MRI Parameters
('scan', 't1_signal', 'T1 Signal Intensity', '', 'Normal', 'Normal', 'Signal intensity on T1-weighted images', 'MRI', NOW(), NOW()),
('scan', 't2_signal', 'T2 Signal Intensity', '', 'Normal', 'Normal', 'Signal intensity on T2-weighted images', 'MRI', NOW(), NOW()),
('scan', 'diffusion_restriction', 'Diffusion Restriction', '', 'Negative', 'Negative', 'Presence of diffusion restriction', 'MRI', NOW(), NOW()),

-- Ultrasound Parameters
('scan', 'echogenicity', 'Echogenicity', '', 'Normal', 'Normal', 'Ability of tissue to reflect ultrasound waves', 'Ultrasound', NOW(), NOW()),
('scan', 'doppler_flow', 'Doppler Flow', 'cm/s', '20', '100', 'Blood flow velocity', 'Ultrasound', NOW(), NOW()),
('scan', 'structure_size', 'Structure Size', 'mm', '', '', 'Size of structures visualized', 'Ultrasound', NOW(), NOW());
