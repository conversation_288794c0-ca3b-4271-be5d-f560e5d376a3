-- Pet Growth Metrics table
CREATE TABLE IF NOT EXISTS `pet_growth_metrics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `record_date` date NOT NULL,
  `height` decimal(10,2) DEFAULT NULL,
  `height_unit` varchar(10) DEFAULT 'cm',
  `length` decimal(10,2) DEFAULT NULL,
  `length_unit` varchar(10) DEFAULT 'cm',
  `chest_girth` decimal(10,2) DEFAULT NULL,
  `chest_girth_unit` varchar(10) DEFAULT 'cm',
  `neck_size` decimal(10,2) DEFAULT NULL,
  `neck_size_unit` varchar(10) DEFAULT 'cm',
  `weight` decimal(10,2) DEFAULT NULL,
  `weight_unit` varchar(10) DEFAULT 'kg',
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON>Y `pet_id` (`pet_id`),
  KEY `record_date` (`record_date`),
  CONSTRAINT `pet_growth_metrics_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Growth Milestones table
CREATE TABLE IF NOT EXISTS `pet_growth_milestones` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `milestone_date` date NOT NULL,
  `milestone_type` enum('weight','height','length','teeth','other') NOT NULL,
  `milestone_name` varchar(255) NOT NULL,
  `milestone_value` decimal(10,2) DEFAULT NULL,
  `milestone_unit` varchar(10) DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  KEY `milestone_date` (`milestone_date`),
  CONSTRAINT `pet_growth_milestones_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Growth Standards table (for reference data)
CREATE TABLE IF NOT EXISTS `pet_growth_standards` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `species` varchar(100) NOT NULL,
  `breed` varchar(100) DEFAULT NULL,
  `gender` enum('male','female','both') NOT NULL DEFAULT 'both',
  `age_months` int(11) NOT NULL,
  `min_weight` decimal(10,2) DEFAULT NULL,
  `max_weight` decimal(10,2) DEFAULT NULL,
  `weight_unit` varchar(10) DEFAULT 'kg',
  `min_height` decimal(10,2) DEFAULT NULL,
  `max_height` decimal(10,2) DEFAULT NULL,
  `height_unit` varchar(10) DEFAULT 'cm',
  `min_length` decimal(10,2) DEFAULT NULL,
  `max_length` decimal(10,2) DEFAULT NULL,
  `length_unit` varchar(10) DEFAULT 'cm',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `species_breed_gender` (`species`, `breed`, `gender`),
  KEY `age_months` (`age_months`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
