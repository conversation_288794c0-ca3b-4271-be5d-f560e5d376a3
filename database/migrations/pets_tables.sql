-- Pets Tables

-- Pets table
CREATE TABLE IF NOT EXISTS `pets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `species` varchar(100) NOT NULL,
  `breed` varchar(100) DEFAULT NULL,
  `gender` enum('male','female','unknown') NOT NULL DEFAULT 'unknown',
  `birth_date` date DEFAULT NULL,
  `weight` decimal(10,2) DEFAULT NULL,
  `weight_unit` varchar(10) DEFAULT 'kg',
  `color` varchar(100) DEFAULT NULL,
  `microchip_id` varchar(100) DEFAULT NULL,
  `adoption_date` date DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `pets_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Medications table
CREATE TABLE IF NOT EXISTS `pet_medications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `dosage` decimal(10,2) NOT NULL,
  `dosage_unit` varchar(50) NOT NULL,
  `frequency` enum('daily','as_needed','custom') NOT NULL DEFAULT 'daily',
  `instructions` text,
  `prescriber` varchar(255) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  CONSTRAINT `pet_medications_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Medication Logs table
CREATE TABLE IF NOT EXISTS `pet_medication_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `medication_id` int(11) NOT NULL,
  `log_date` date NOT NULL,
  `log_time` time NOT NULL,
  `given` tinyint(1) NOT NULL DEFAULT '1',
  `actual_dosage` decimal(10,2) DEFAULT NULL,
  `effectiveness_rating` int(11) DEFAULT NULL,
  `side_effects` text,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `medication_id` (`medication_id`),
  KEY `log_date` (`log_date`),
  CONSTRAINT `pet_medication_logs_ibfk_1` FOREIGN KEY (`medication_id`) REFERENCES `pet_medications` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Treatments table
CREATE TABLE IF NOT EXISTS `pet_treatments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `treatment_type` enum('vaccination','deworming','grooming','checkup','surgery','dental','other') NOT NULL,
  `treatment_name` varchar(255) NOT NULL,
  `treatment_date` date NOT NULL,
  `provider` varchar(255) DEFAULT NULL,
  `cost` decimal(10,2) DEFAULT NULL,
  `next_due_date` date DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  CONSTRAINT `pet_treatments_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Training Logs table
CREATE TABLE IF NOT EXISTS `pet_training_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `training_date` date NOT NULL,
  `skill_name` varchar(255) NOT NULL,
  `duration` int(11) DEFAULT NULL,
  `progress_rating` int(11) DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  CONSTRAINT `pet_training_logs_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
