<?php
/**
 * Migration script to create productivity-related tables
 */

require_once __DIR__ . '/../../src/utils/Database.php';

// Create a database connection using singleton pattern
$db = Database::getInstance();

// Function to check if a table exists
function tableExists($db, $tableName) {
    $result = $db->fetchOne("SHOW TABLES LIKE '$tableName'");
    return !empty($result);
}

// Create time_blocks table if it doesn't exist
if (!tableExists($db, 'time_blocks')) {
    $sql = "CREATE TABLE time_blocks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        start_time DATETIME NOT NULL,
        end_time DATETIME NOT NULL,
        category VARCHAR(50),
        color VARCHAR(20) DEFAULT '#4F46E5',
        task_id INT,
        is_recurring BOOLEAN DEFAULT FALSE,
        recurrence_pattern VARCHAR(50),
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($db->query($sql)) {
        echo "Table 'time_blocks' created successfully\n";
    } else {
        echo "Error creating table 'time_blocks'\n";
    }
} else {
    echo "Table 'time_blocks' already exists\n";
}

// Create energy_levels table if it doesn't exist
if (!tableExists($db, 'energy_levels')) {
    $sql = "CREATE TABLE energy_levels (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        level TINYINT NOT NULL COMMENT 'Energy level from 1-10',
        recorded_at DATETIME NOT NULL,
        notes TEXT,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($db->query($sql)) {
        echo "Table 'energy_levels' created successfully\n";
    } else {
        echo "Error creating table 'energy_levels'\n";
    }
} else {
    echo "Table 'energy_levels' already exists\n";
}

// Create task_batches table if it doesn't exist
if (!tableExists($db, 'task_batches')) {
    $sql = "CREATE TABLE task_batches (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        category VARCHAR(50),
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($db->query($sql)) {
        echo "Table 'task_batches' created successfully\n";
    } else {
        echo "Error creating table 'task_batches'\n";
    }
} else {
    echo "Table 'task_batches' already exists\n";
}

// Create task_batch_items table if it doesn't exist
if (!tableExists($db, 'task_batch_items')) {
    $sql = "CREATE TABLE task_batch_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        batch_id INT NOT NULL,
        task_id INT NOT NULL,
        position INT NOT NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (batch_id) REFERENCES task_batches(id) ON DELETE CASCADE,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($db->query($sql)) {
        echo "Table 'task_batch_items' created successfully\n";
    } else {
        echo "Error creating table 'task_batch_items'\n";
    }
} else {
    echo "Table 'task_batch_items' already exists\n";
}

// Create distractions table if it doesn't exist
if (!tableExists($db, 'distractions')) {
    $sql = "CREATE TABLE distractions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        type VARCHAR(50) NOT NULL,
        description TEXT,
        occurred_at DATETIME NOT NULL,
        duration INT COMMENT 'Duration in minutes',
        context TEXT COMMENT 'What was happening when distraction occurred',
        impact ENUM('low', 'medium', 'high') DEFAULT 'medium',
        resolution TEXT COMMENT 'How the distraction was handled',
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($db->query($sql)) {
        echo "Table 'distractions' created successfully\n";
    } else {
        echo "Error creating table 'distractions'\n";
    }
} else {
    echo "Table 'distractions' already exists\n";
}

echo "Migration completed successfully\n";
