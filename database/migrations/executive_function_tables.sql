-- Executive function exercises table
CREATE TABLE IF NOT EXISTS `executive_function_exercises` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `instructions` text NOT NULL,
  `category` enum('working_memory','task_initiation','planning','organization','time_management','emotional_regulation') NOT NULL,
  `difficulty_level` int(11) NOT NULL DEFAULT '1',
  `estimated_time` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `category` (`category`),
  KEY `difficulty_level` (`difficulty_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Exercise results table
CREATE TABLE IF NOT EXISTS `exercise_results` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `exercise_id` int(11) NOT NULL,
  `completion_date` date NOT NULL,
  `completion_time` time NOT NULL,
  `score` int(11) DEFAULT NULL,
  `time_taken` int(11) DEFAULT NULL,
  `difficulty_rating` int(11) DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `exercise_id` (`exercise_id`),
  KEY `completion_date` (`completion_date`),
  CONSTRAINT `exercise_results_ibfk_1` FOREIGN KEY (`exercise_id`) REFERENCES `executive_function_exercises` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Executive function progress table
CREATE TABLE IF NOT EXISTS `executive_function_progress` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `category` enum('working_memory','task_initiation','planning','organization','time_management','emotional_regulation') NOT NULL,
  `date` date NOT NULL,
  `score` int(11) NOT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_category_date` (`user_id`,`category`,`date`),
  KEY `category` (`category`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
