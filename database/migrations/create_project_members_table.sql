-- Create project_members table
CREATE TABLE IF NOT EXISTS `project_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `project_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `role` enum('owner', 'admin', 'member') NOT NULL DEFAULT 'member',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `project_user_unique` (`project_id`, `user_id`),
  KEY `project_id` (`project_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `project_members_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `project_members_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add project owner as a member with owner role for each existing project
INSERT INTO `project_members` (`project_id`, `user_id`, `role`, `created_at`, `updated_at`)
SELECT `id`, `user_id`, 'owner', `created_at`, `updated_at` FROM `projects`
ON DUPLICATE KEY UPDATE `role` = 'owner', `updated_at` = VALUES(`updated_at`);
