-- Pet Enhancements Tables

-- Pet Vitals table
CREATE TABLE IF NOT EXISTS `pet_vitals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `record_date` date NOT NULL,
  `weight` decimal(10,2) DEFAULT NULL,
  `temperature` decimal(5,2) DEFAULT NULL,
  `heart_rate` int(11) DEFAULT NULL,
  `respiratory_rate` int(11) DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  KEY `record_date` (`record_date`),
  CONSTRAINT `pet_vitals_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Health Assessments table
CREATE TABLE IF NOT EXISTS `pet_health_assessments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `assessment_date` date NOT NULL,
  `coat_condition` int(11) DEFAULT NULL,
  `skin_condition` int(11) DEFAULT NULL,
  `eye_condition` int(11) DEFAULT NULL,
  `ear_condition` int(11) DEFAULT NULL,
  `dental_condition` int(11) DEFAULT NULL,
  `mobility` int(11) DEFAULT NULL,
  `appetite` int(11) DEFAULT NULL,
  `hydration` int(11) DEFAULT NULL,
  `overall_health` int(11) DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  KEY `assessment_date` (`assessment_date`),
  CONSTRAINT `pet_health_assessments_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Diet Plans table
CREATE TABLE IF NOT EXISTS `pet_diet_plans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  CONSTRAINT `pet_diet_plans_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Meals table
CREATE TABLE IF NOT EXISTS `pet_meals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `diet_plan_id` int(11) DEFAULT NULL,
  `meal_date` date NOT NULL,
  `meal_time` time NOT NULL,
  `food_type` varchar(255) NOT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `amount_unit` varchar(50) DEFAULT NULL,
  `calories` int(11) DEFAULT NULL,
  `was_eaten` tinyint(1) NOT NULL DEFAULT '1',
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  KEY `diet_plan_id` (`diet_plan_id`),
  KEY `meal_date` (`meal_date`),
  CONSTRAINT `pet_meals_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `pet_meals_ibfk_2` FOREIGN KEY (`diet_plan_id`) REFERENCES `pet_diet_plans` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Food Allergies table
CREATE TABLE IF NOT EXISTS `pet_food_allergies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `food_name` varchar(255) NOT NULL,
  `reaction` text,
  `severity` enum('mild','moderate','severe') NOT NULL DEFAULT 'moderate',
  `diagnosed_date` date DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  CONSTRAINT `pet_food_allergies_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Activities table
CREATE TABLE IF NOT EXISTS `pet_activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `activity_date` date NOT NULL,
  `activity_time` time NOT NULL,
  `activity_type` varchar(100) NOT NULL,
  `duration` int(11) DEFAULT NULL,
  `intensity` enum('low','moderate','high') DEFAULT 'moderate',
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  KEY `activity_date` (`activity_date`),
  CONSTRAINT `pet_activities_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Behavior Logs table
CREATE TABLE IF NOT EXISTS `pet_behavior_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `log_date` date NOT NULL,
  `log_time` time NOT NULL,
  `behavior_type` varchar(100) NOT NULL,
  `description` text,
  `trigger` varchar(255) DEFAULT NULL,
  `severity` int(11) DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  KEY `log_date` (`log_date`),
  CONSTRAINT `pet_behavior_logs_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Vet Visits table
CREATE TABLE IF NOT EXISTS `pet_vet_visits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `visit_date` date NOT NULL,
  `visit_time` time DEFAULT NULL,
  `vet_name` varchar(255) DEFAULT NULL,
  `clinic_name` varchar(255) DEFAULT NULL,
  `visit_reason` varchar(255) NOT NULL,
  `diagnosis` text,
  `treatment` text,
  `follow_up_date` date DEFAULT NULL,
  `cost` decimal(10,2) DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  KEY `visit_date` (`visit_date`),
  CONSTRAINT `pet_vet_visits_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Expenses table
CREATE TABLE IF NOT EXISTS `pet_expenses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `expense_date` date NOT NULL,
  `category` varchar(100) NOT NULL,
  `description` varchar(255) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `vendor` varchar(255) DEFAULT NULL,
  `receipt_image` varchar(255) DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  KEY `expense_date` (`expense_date`),
  KEY `category` (`category`),
  CONSTRAINT `pet_expenses_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Breeding Records table
CREATE TABLE IF NOT EXISTS `pet_breeding_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `partner_name` varchar(255) DEFAULT NULL,
  `partner_breed` varchar(255) DEFAULT NULL,
  `heat_start_date` date DEFAULT NULL,
  `breeding_date` date DEFAULT NULL,
  `due_date` date DEFAULT NULL,
  `delivery_date` date DEFAULT NULL,
  `litter_size` int(11) DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  CONSTRAINT `pet_breeding_records_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Offspring table
CREATE TABLE IF NOT EXISTS `pet_offspring` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `breeding_record_id` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `gender` enum('male','female','unknown') NOT NULL DEFAULT 'unknown',
  `color` varchar(100) DEFAULT NULL,
  `identifying_marks` text,
  `birth_weight` decimal(10,2) DEFAULT NULL,
  `current_owner` varchar(255) DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `breeding_record_id` (`breeding_record_id`),
  CONSTRAINT `pet_offspring_ibfk_1` FOREIGN KEY (`breeding_record_id`) REFERENCES `pet_breeding_records` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Documents table
CREATE TABLE IF NOT EXISTS `pet_documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `document_type` varchar(100) NOT NULL,
  `title` varchar(255) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_size` int(11) DEFAULT NULL,
  `file_type` varchar(100) DEFAULT NULL,
  `issue_date` date DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  KEY `document_type` (`document_type`),
  CONSTRAINT `pet_documents_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Contacts table
CREATE TABLE IF NOT EXISTS `pet_contacts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `role` varchar(100) NOT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  CONSTRAINT `pet_contacts_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Pet Reminders table
CREATE TABLE IF NOT EXISTS `pet_reminders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `reminder_type` varchar(100) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text,
  `due_date` date NOT NULL,
  `is_recurring` tinyint(1) NOT NULL DEFAULT '0',
  `recurrence_pattern` varchar(50) DEFAULT NULL,
  `is_completed` tinyint(1) NOT NULL DEFAULT '0',
  `completed_date` date DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  KEY `due_date` (`due_date`),
  KEY `reminder_type` (`reminder_type`),
  CONSTRAINT `pet_reminders_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
