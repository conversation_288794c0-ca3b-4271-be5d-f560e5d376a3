-- Pet Health Assessments table
CREATE TABLE IF NOT EXISTS `pet_health_assessments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `assessment_date` date NOT NULL,
  `overall_health` enum('excellent','good','fair','poor','critical') NOT NULL,
  `coat_condition` enum('excellent','good','fair','poor','n/a') DEFAULT NULL,
  `skin_condition` enum('excellent','good','fair','poor') DEFAULT NULL,
  `eye_condition` enum('excellent','good','fair','poor') DEFAULT NULL,
  `ear_condition` enum('excellent','good','fair','poor') DEFAULT NULL,
  `dental_health` enum('excellent','good','fair','poor') DEFAULT NULL,
  `nail_condition` enum('excellent','good','fair','poor','n/a') DEFAULT NULL,
  `energy_level` enum('very_high','high','normal','low','very_low') DEFAULT NULL,
  `appetite` enum('excellent','good','fair','poor','none') DEFAULT NULL,
  `hydration` enum('excellent','good','fair','poor') DEFAULT NULL,
  `mobility` enum('excellent','good','fair','poor','very_poor') DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  KEY `assessment_date` (`assessment_date`),
  CONSTRAINT `pet_health_assessments_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
