-- Check if the current_focus_task_id column exists in the users table
SELECT COUNT(*) AS column_exists
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'users'
  AND COLUMN_NAME = 'current_focus_task_id';

-- If the column doesn't exist, add it
IF (SELECT column_exists FROM (SELECT COUNT(*) AS column_exists
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = 'users'
      AND COLUMN_NAME = 'current_focus_task_id') AS subquery) = 0 THEN
    ALTER TABLE users ADD COLUMN current_focus_task_id INT DEFAULT NULL;
END IF;
