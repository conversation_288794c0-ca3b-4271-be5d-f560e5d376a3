-- ADHD Triggers table
CREATE TABLE IF NOT EXISTS `adhd_triggers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `category` enum('environmental','emotional','physical','social','task_related','time_related','other') NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Trigger occurrences table
CREATE TABLE IF NOT EXISTS `trigger_occurrences` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trigger_id` int(11) NOT NULL,
  `occurrence_date` date NOT NULL,
  `occurrence_time` time DEFAULT NULL,
  `impact_rating` int(11) NOT NULL,
  `symptoms_experienced` text,
  `context` text,
  `coping_strategy_used` text,
  `coping_effectiveness` int(11) DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `trigger_id` (`trigger_id`),
  KEY `occurrence_date` (`occurrence_date`),
  CONSTRAINT `trigger_occurrences_ibfk_1` FOREIGN KEY (`trigger_id`) REFERENCES `adhd_triggers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Trigger coping strategies table
CREATE TABLE IF NOT EXISTS `trigger_coping_strategies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trigger_id` int(11) NOT NULL,
  `strategy_name` varchar(255) NOT NULL,
  `description` text,
  `effectiveness_rating` int(11) DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `trigger_id` (`trigger_id`),
  CONSTRAINT `trigger_coping_strategies_ibfk_1` FOREIGN KEY (`trigger_id`) REFERENCES `adhd_triggers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
