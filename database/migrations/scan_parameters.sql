-- Add common scan parameters
INSERT INTO `common_test_parameters` 
(`test_type`, `parameter_name`, `display_name`, `unit`, `reference_range_min`, `reference_range_max`, `description`, `category`, `created_at`, `updated_at`) 
VALUES
-- X-Ray Parameters
('scan', 'bone_density', 'Bone Density', 'g/cm²', '0.97', '1.28', 'Measurement of bone mineral density', 'X-Ray', NOW(), NOW()),
('scan', 'joint_space', 'Joint Space', 'mm', '2', '4', 'Space between joints', 'X-Ray', NOW(), NOW()),
('scan', 'fracture_healing', 'Fracture Healing', '%', '0', '100', 'Percentage of fracture healing', 'X-Ray', NOW(), NOW()),

-- CT Scan Parameters
('scan', 'hounsfield_units_liver', 'Liver Density (HU)', 'HU', '50', '70', 'Hounsfield units measurement for liver density', 'CT Scan', NOW(), NOW()),
('scan', 'hounsfield_units_spleen', 'Spleen Density (HU)', 'HU', '40', '60', 'Hounsfield units measurement for spleen density', 'CT Scan', NOW(), NOW()),
('scan', 'coronary_calcium_score', 'Coronary Calcium Score', '', '0', '100', 'Measurement of calcium in coronary arteries', 'CT Scan', NOW(), NOW()),
('scan', 'nodule_size', 'Nodule Size', 'mm', '0', '4', 'Size of nodules found in scan', 'CT Scan', NOW(), NOW()),

-- MRI Parameters
('scan', 'disc_height', 'Disc Height', 'mm', '7', '10', 'Height of intervertebral discs', 'MRI', NOW(), NOW()),
('scan', 'cartilage_thickness', 'Cartilage Thickness', 'mm', '2', '4', 'Thickness of cartilage', 'MRI', NOW(), NOW()),
('scan', 't2_signal_intensity', 'T2 Signal Intensity', '', 'Normal', 'Normal', 'Signal intensity on T2-weighted images', 'MRI', NOW(), NOW()),
('scan', 'brain_volume', 'Brain Volume', 'cm³', '1100', '1300', 'Total brain volume', 'MRI', NOW(), NOW()),

-- Ultrasound Parameters
('scan', 'carotid_intima_media_thickness', 'Carotid Intima-Media Thickness', 'mm', '0.5', '0.9', 'Thickness of carotid artery walls', 'Ultrasound', NOW(), NOW()),
('scan', 'ejection_fraction', 'Ejection Fraction', '%', '55', '70', 'Percentage of blood pumped out of ventricles', 'Ultrasound', NOW(), NOW()),
('scan', 'kidney_length', 'Kidney Length', 'cm', '10', '12', 'Length of kidney', 'Ultrasound', NOW(), NOW()),
('scan', 'liver_size', 'Liver Size', 'cm', '13', '15', 'Size of liver', 'Ultrasound', NOW(), NOW()),
('scan', 'gallbladder_wall_thickness', 'Gallbladder Wall Thickness', 'mm', '2', '3', 'Thickness of gallbladder wall', 'Ultrasound', NOW(), NOW()),

-- DEXA Scan Parameters
('scan', 'bmd_spine', 'Bone Mineral Density (Spine)', 'g/cm²', '0.9', '1.2', 'Bone mineral density of spine', 'DEXA Scan', NOW(), NOW()),
('scan', 'bmd_hip', 'Bone Mineral Density (Hip)', 'g/cm²', '0.8', '1.1', 'Bone mineral density of hip', 'DEXA Scan', NOW(), NOW()),
('scan', 't_score', 'T-Score', '', '-1.0', '1.0', 'Comparison to young adult bone density', 'DEXA Scan', NOW(), NOW()),
('scan', 'z_score', 'Z-Score', '', '-1.0', '1.0', 'Comparison to age-matched bone density', 'DEXA Scan', NOW(), NOW()),
('scan', 'body_fat_percentage', 'Body Fat Percentage', '%', '15', '30', 'Percentage of body fat', 'DEXA Scan', NOW(), NOW()),

-- PET Scan Parameters
('scan', 'suv_max', 'SUV Max', '', '0', '2.5', 'Maximum standardized uptake value', 'PET Scan', NOW(), NOW()),
('scan', 'metabolic_tumor_volume', 'Metabolic Tumor Volume', 'cm³', '0', '0', 'Volume of metabolically active tumor', 'PET Scan', NOW(), NOW()),
('scan', 'total_lesion_glycolysis', 'Total Lesion Glycolysis', '', '0', '0', 'Product of SUV mean and metabolic tumor volume', 'PET Scan', NOW(), NOW()),

-- Mammogram Parameters
('scan', 'breast_density', 'Breast Density', 'BI-RADS', '1', '4', 'Breast density category (BI-RADS)', 'Mammogram', NOW(), NOW()),
('scan', 'microcalcification_count', 'Microcalcification Count', '', '0', '0', 'Number of microcalcifications', 'Mammogram', NOW(), NOW()),
('scan', 'mass_size', 'Mass Size', 'mm', '0', '0', 'Size of any masses detected', 'Mammogram', NOW(), NOW());
