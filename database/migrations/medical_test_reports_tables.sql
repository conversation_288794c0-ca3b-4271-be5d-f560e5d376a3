-- Medical Test Reports Tables

-- Medical Test Reports table
CREATE TABLE IF NOT EXISTS `medical_test_reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `report_type` enum('blood', 'urine', 'scan', 'other') NOT NULL DEFAULT 'blood',
  `report_date` date NOT NULL,
  `lab_name` varchar(255) DEFAULT NULL,
  `doctor_name` varchar(255) DEFAULT NULL,
  `report_title` varchar(255) NOT NULL,
  `notes` text,
  `file_path` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `report_date` (`report_date`),
  KEY `report_type` (`report_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Medical Test Parameters table
CREATE TABLE IF NOT EXISTS `medical_test_parameters` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `report_id` int(11) NOT NULL,
  `parameter_name` varchar(255) NOT NULL,
  `parameter_value` varchar(50) NOT NULL,
  `unit` varchar(50) DEFAULT NULL,
  `reference_range_min` varchar(50) DEFAULT NULL,
  `reference_range_max` varchar(50) DEFAULT NULL,
  `is_abnormal` tinyint(1) NOT NULL DEFAULT '0',
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `report_id` (`report_id`),
  KEY `parameter_name` (`parameter_name`),
  CONSTRAINT `medical_test_parameters_ibfk_1` FOREIGN KEY (`report_id`) REFERENCES `medical_test_reports` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Common Blood Test Parameters
CREATE TABLE IF NOT EXISTS `common_test_parameters` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `test_type` enum('blood', 'urine', 'scan', 'other') NOT NULL,
  `parameter_name` varchar(255) NOT NULL,
  `display_name` varchar(255) NOT NULL,
  `unit` varchar(50) DEFAULT NULL,
  `reference_range_min` varchar(50) DEFAULT NULL,
  `reference_range_max` varchar(50) DEFAULT NULL,
  `description` text,
  `category` varchar(100) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `test_type_parameter_name` (`test_type`, `parameter_name`),
  KEY `test_type` (`test_type`),
  KEY `category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert common blood test parameters
INSERT INTO `common_test_parameters` 
(`test_type`, `parameter_name`, `display_name`, `unit`, `reference_range_min`, `reference_range_max`, `description`, `category`, `created_at`, `updated_at`) 
VALUES
-- Complete Blood Count (CBC)
('blood', 'hemoglobin', 'Hemoglobin', 'g/dL', '13.5', '17.5', 'Protein in red blood cells that carries oxygen', 'CBC', NOW(), NOW()),
('blood', 'hematocrit', 'Hematocrit', '%', '38.8', '50.0', 'Percentage of blood volume that is occupied by red blood cells', 'CBC', NOW(), NOW()),
('blood', 'rbc', 'Red Blood Cells', 'million/µL', '4.5', '5.9', 'Cells that carry oxygen throughout the body', 'CBC', NOW(), NOW()),
('blood', 'wbc', 'White Blood Cells', 'thousand/µL', '4.5', '11.0', 'Cells that fight infection', 'CBC', NOW(), NOW()),
('blood', 'platelets', 'Platelets', 'thousand/µL', '150', '450', 'Cell fragments that help blood clot', 'CBC', NOW(), NOW()),
('blood', 'mcv', 'Mean Corpuscular Volume', 'fL', '80', '100', 'Average size of red blood cells', 'CBC', NOW(), NOW()),
('blood', 'mch', 'Mean Corpuscular Hemoglobin', 'pg', '27', '31', 'Average amount of hemoglobin in red blood cells', 'CBC', NOW(), NOW()),
('blood', 'mchc', 'Mean Corpuscular Hemoglobin Concentration', 'g/dL', '32', '36', 'Average concentration of hemoglobin in red blood cells', 'CBC', NOW(), NOW()),
('blood', 'rdw', 'Red Cell Distribution Width', '%', '11.5', '14.5', 'Measure of variation in red blood cell size', 'CBC', NOW(), NOW()),

-- Lipid Panel
('blood', 'total_cholesterol', 'Total Cholesterol', 'mg/dL', '125', '200', 'Measure of all cholesterol in blood', 'Lipid Panel', NOW(), NOW()),
('blood', 'hdl', 'HDL Cholesterol', 'mg/dL', '40', '60', 'High-density lipoprotein ("good" cholesterol)', 'Lipid Panel', NOW(), NOW()),
('blood', 'ldl', 'LDL Cholesterol', 'mg/dL', '0', '100', 'Low-density lipoprotein ("bad" cholesterol)', 'Lipid Panel', NOW(), NOW()),
('blood', 'triglycerides', 'Triglycerides', 'mg/dL', '0', '150', 'Type of fat found in blood', 'Lipid Panel', NOW(), NOW()),

-- Liver Function Tests
('blood', 'alt', 'Alanine Aminotransferase (ALT)', 'U/L', '7', '56', 'Enzyme found primarily in the liver', 'Liver Function', NOW(), NOW()),
('blood', 'ast', 'Aspartate Aminotransferase (AST)', 'U/L', '10', '40', 'Enzyme found in the liver, heart, and muscles', 'Liver Function', NOW(), NOW()),
('blood', 'alp', 'Alkaline Phosphatase (ALP)', 'U/L', '44', '147', 'Enzyme found in liver and bone', 'Liver Function', NOW(), NOW()),
('blood', 'ggt', 'Gamma-Glutamyl Transferase (GGT)', 'U/L', '0', '51', 'Enzyme found in liver, bile ducts, and pancreas', 'Liver Function', NOW(), NOW()),
('blood', 'total_bilirubin', 'Total Bilirubin', 'mg/dL', '0.1', '1.2', 'Waste product from breakdown of red blood cells', 'Liver Function', NOW(), NOW()),
('blood', 'direct_bilirubin', 'Direct Bilirubin', 'mg/dL', '0', '0.3', 'Form of bilirubin that is water-soluble', 'Liver Function', NOW(), NOW()),
('blood', 'albumin', 'Albumin', 'g/dL', '3.4', '5.4', 'Protein made by the liver', 'Liver Function', NOW(), NOW()),
('blood', 'total_protein', 'Total Protein', 'g/dL', '6.0', '8.3', 'Measure of all proteins in blood', 'Liver Function', NOW(), NOW()),

-- Kidney Function Tests
('blood', 'bun', 'Blood Urea Nitrogen (BUN)', 'mg/dL', '7', '20', 'Waste product filtered by kidneys', 'Kidney Function', NOW(), NOW()),
('blood', 'creatinine', 'Creatinine', 'mg/dL', '0.6', '1.2', 'Waste product from muscle metabolism', 'Kidney Function', NOW(), NOW()),
('blood', 'egfr', 'Estimated Glomerular Filtration Rate (eGFR)', 'mL/min/1.73m²', '90', '120', 'Measure of kidney function', 'Kidney Function', NOW(), NOW()),
('blood', 'uric_acid', 'Uric Acid', 'mg/dL', '3.5', '7.2', 'Waste product from breakdown of purines', 'Kidney Function', NOW(), NOW()),

-- Electrolytes
('blood', 'sodium', 'Sodium', 'mmol/L', '135', '145', 'Electrolyte that helps maintain fluid balance', 'Electrolytes', NOW(), NOW()),
('blood', 'potassium', 'Potassium', 'mmol/L', '3.5', '5.0', 'Electrolyte important for heart and muscle function', 'Electrolytes', NOW(), NOW()),
('blood', 'chloride', 'Chloride', 'mmol/L', '98', '107', 'Electrolyte that helps maintain fluid balance', 'Electrolytes', NOW(), NOW()),
('blood', 'bicarbonate', 'Bicarbonate', 'mmol/L', '22', '29', 'Electrolyte that helps maintain acid-base balance', 'Electrolytes', NOW(), NOW()),
('blood', 'calcium', 'Calcium', 'mg/dL', '8.5', '10.5', 'Mineral important for bone health and muscle function', 'Electrolytes', NOW(), NOW()),
('blood', 'phosphorus', 'Phosphorus', 'mg/dL', '2.5', '4.5', 'Mineral important for bone health and energy production', 'Electrolytes', NOW(), NOW()),
('blood', 'magnesium', 'Magnesium', 'mg/dL', '1.7', '2.2', 'Mineral important for muscle and nerve function', 'Electrolytes', NOW(), NOW()),

-- Glucose Tests
('blood', 'glucose_fasting', 'Fasting Glucose', 'mg/dL', '70', '99', 'Blood sugar level after fasting', 'Glucose', NOW(), NOW()),
('blood', 'glucose_random', 'Random Glucose', 'mg/dL', '70', '140', 'Blood sugar level at any time', 'Glucose', NOW(), NOW()),
('blood', 'hba1c', 'Hemoglobin A1c', '%', '4.0', '5.6', 'Average blood sugar level over past 2-3 months', 'Glucose', NOW(), NOW()),

-- Thyroid Function Tests
('blood', 'tsh', 'Thyroid Stimulating Hormone (TSH)', 'mIU/L', '0.4', '4.0', 'Hormone that regulates thyroid function', 'Thyroid', NOW(), NOW()),
('blood', 't4_free', 'Free T4', 'ng/dL', '0.8', '1.8', 'Active form of thyroid hormone thyroxine', 'Thyroid', NOW(), NOW()),
('blood', 't3_free', 'Free T3', 'pg/mL', '2.3', '4.2', 'Active form of thyroid hormone triiodothyronine', 'Thyroid', NOW(), NOW());

-- Insert common urine test parameters
INSERT INTO `common_test_parameters` 
(`test_type`, `parameter_name`, `display_name`, `unit`, `reference_range_min`, `reference_range_max`, `description`, `category`, `created_at`, `updated_at`) 
VALUES
-- Urinalysis
('urine', 'color', 'Color', '', 'Pale Yellow', 'Yellow', 'Visual appearance of urine', 'Physical Properties', NOW(), NOW()),
('urine', 'clarity', 'Clarity', '', 'Clear', 'Clear', 'Transparency of urine', 'Physical Properties', NOW(), NOW()),
('urine', 'specific_gravity', 'Specific Gravity', '', '1.005', '1.030', 'Concentration of particles in urine', 'Physical Properties', NOW(), NOW()),
('urine', 'ph', 'pH', '', '4.5', '8.0', 'Acidity or alkalinity of urine', 'Physical Properties', NOW(), NOW()),
('urine', 'protein', 'Protein', '', 'Negative', 'Negative', 'Protein in urine can indicate kidney problems', 'Chemical Properties', NOW(), NOW()),
('urine', 'glucose', 'Glucose', '', 'Negative', 'Negative', 'Sugar in urine can indicate diabetes', 'Chemical Properties', NOW(), NOW()),
('urine', 'ketones', 'Ketones', '', 'Negative', 'Negative', 'Breakdown products of fat metabolism', 'Chemical Properties', NOW(), NOW()),
('urine', 'blood', 'Blood', '', 'Negative', 'Negative', 'Blood in urine can indicate various conditions', 'Chemical Properties', NOW(), NOW()),
('urine', 'bilirubin', 'Bilirubin', '', 'Negative', 'Negative', 'Breakdown product of red blood cells', 'Chemical Properties', NOW(), NOW()),
('urine', 'urobilinogen', 'Urobilinogen', '', 'Normal', 'Normal', 'Breakdown product of bilirubin', 'Chemical Properties', NOW(), NOW()),
('urine', 'nitrite', 'Nitrite', '', 'Negative', 'Negative', 'Can indicate bacterial infection', 'Chemical Properties', NOW(), NOW()),
('urine', 'leukocyte_esterase', 'Leukocyte Esterase', '', 'Negative', 'Negative', 'Can indicate white blood cells in urine', 'Chemical Properties', NOW(), NOW()),
('urine', 'wbc', 'White Blood Cells', '/HPF', '0', '5', 'White blood cells in urine can indicate infection', 'Microscopic Examination', NOW(), NOW()),
('urine', 'rbc', 'Red Blood Cells', '/HPF', '0', '2', 'Red blood cells in urine can indicate various conditions', 'Microscopic Examination', NOW(), NOW()),
('urine', 'epithelial_cells', 'Epithelial Cells', '', 'Few', 'Few', 'Cells that line the urinary tract', 'Microscopic Examination', NOW(), NOW()),
('urine', 'bacteria', 'Bacteria', '', 'Negative', 'Negative', 'Can indicate infection', 'Microscopic Examination', NOW(), NOW()),
('urine', 'crystals', 'Crystals', '', 'Negative', 'Negative', 'Can indicate various metabolic conditions', 'Microscopic Examination', NOW(), NOW()),
('urine', 'casts', 'Casts', '', 'Negative', 'Negative', 'Cylindrical structures that can indicate kidney disease', 'Microscopic Examination', NOW(), NOW());
