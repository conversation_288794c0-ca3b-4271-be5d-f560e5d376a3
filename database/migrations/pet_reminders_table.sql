-- Pet Reminders table
CREATE TABLE IF NOT EXISTS `pet_reminders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `reminder_type` enum('medication','vaccination','vet_visit','grooming','training','feeding','exercise','other') NOT NULL,
  `priority` enum('high','normal','low') NOT NULL DEFAULT 'normal',
  `due_date` date NOT NULL,
  `due_time` time DEFAULT NULL,
  `recurrence_type` enum('none','daily','weekly','monthly','yearly','custom') NOT NULL DEFAULT 'none',
  `recurrence_interval` int(11) DEFAULT NULL,
  `recurrence_end_date` date DEFAULT NULL,
  `notification_type` enum('app','email','both','none') NOT NULL DEFAULT 'app',
  `notification_time` int(11) DEFAULT 0,
  `is_completed` tinyint(1) NOT NULL DEFAULT 0,
  `completed_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  KEY `due_date` (`due_date`),
  KEY `is_completed` (`is_completed`),
  CONSTRAINT `pet_reminders_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
