-- Freelance Project Management Schema

-- Create freelance_clients table
CREATE TABLE freelance_clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    company VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    website VARCHAR(255),
    notes TEXT,
    status ENUM('active', 'inactive', 'potential', 'archived') DEFAULT 'active',
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create freelance_projects table
CREATE TABLE freelance_projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    client_id INT NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    start_date DATE,
    deadline DATE,
    status ENUM('proposal', 'negotiation', 'in_progress', 'review', 'completed', 'cancelled') DEFAULT 'proposal',
    payment_type ENUM('hourly', 'fixed', 'milestone') NOT NULL,
    hourly_rate DECIMAL(10, 2),
    fixed_price DECIMAL(10, 2),
    currency VARCHAR(10) DEFAULT 'Rs',
    estimated_hours DECIMAL(10, 2),
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    notes TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES freelance_clients(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create freelance_project_milestones table
CREATE TABLE freelance_project_milestones (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    due_date DATE,
    amount DECIMAL(10, 2),
    status ENUM('pending', 'in_progress', 'completed', 'paid') DEFAULT 'pending',
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (project_id) REFERENCES freelance_projects(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create freelance_invoices table
CREATE TABLE freelance_invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    project_id INT NOT NULL,
    client_id INT NOT NULL,
    invoice_number VARCHAR(50) NOT NULL,
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    subtotal DECIMAL(10, 2) NOT NULL,
    tax_rate DECIMAL(5, 2) DEFAULT 0,
    tax_amount DECIMAL(10, 2) DEFAULT 0,
    discount_amount DECIMAL(10, 2) DEFAULT 0,
    total_amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'Rs',
    notes TEXT,
    terms TEXT,
    status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (project_id) REFERENCES freelance_projects(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES freelance_clients(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create freelance_invoice_items table
CREATE TABLE freelance_invoice_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    description TEXT NOT NULL,
    quantity DECIMAL(10, 2) NOT NULL DEFAULT 1,
    unit_price DECIMAL(10, 2) NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (invoice_id) REFERENCES freelance_invoices(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create freelance_payments table
CREATE TABLE freelance_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    project_id INT NOT NULL,
    client_id INT NOT NULL,
    invoice_id INT,
    amount DECIMAL(10, 2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method ENUM('bank_transfer', 'cash', 'paypal', 'credit_card', 'other') NOT NULL,
    transaction_id VARCHAR(255),
    notes TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (project_id) REFERENCES freelance_projects(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES freelance_clients(id) ON DELETE CASCADE,
    FOREIGN KEY (invoice_id) REFERENCES freelance_invoices(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create freelance_time_entries table
CREATE TABLE freelance_time_entries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    project_id INT NOT NULL,
    description TEXT,
    start_time DATETIME NOT NULL,
    end_time DATETIME,
    duration INT, -- in minutes
    billable BOOLEAN DEFAULT TRUE,
    billed BOOLEAN DEFAULT FALSE,
    invoice_id INT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (project_id) REFERENCES freelance_projects(id) ON DELETE CASCADE,
    FOREIGN KEY (invoice_id) REFERENCES freelance_invoices(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create indexes for better performance
ALTER TABLE freelance_clients ADD INDEX idx_user_id (user_id);
ALTER TABLE freelance_clients ADD INDEX idx_status (status);

ALTER TABLE freelance_projects ADD INDEX idx_user_id (user_id);
ALTER TABLE freelance_projects ADD INDEX idx_client_id (client_id);
ALTER TABLE freelance_projects ADD INDEX idx_status (status);

ALTER TABLE freelance_invoices ADD INDEX idx_user_id (user_id);
ALTER TABLE freelance_invoices ADD INDEX idx_project_id (project_id);
ALTER TABLE freelance_invoices ADD INDEX idx_client_id (client_id);
ALTER TABLE freelance_invoices ADD INDEX idx_status (status);

ALTER TABLE freelance_payments ADD INDEX idx_user_id (user_id);
ALTER TABLE freelance_payments ADD INDEX idx_project_id (project_id);
ALTER TABLE freelance_payments ADD INDEX idx_client_id (client_id);
ALTER TABLE freelance_payments ADD INDEX idx_invoice_id (invoice_id);

ALTER TABLE freelance_time_entries ADD INDEX idx_user_id (user_id);
ALTER TABLE freelance_time_entries ADD INDEX idx_project_id (project_id);
ALTER TABLE freelance_time_entries ADD INDEX idx_invoice_id (invoice_id);
