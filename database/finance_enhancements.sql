-- Finance System Enhancements

-- Alter finances table to add new fields
ALTER TABLE finances 
ADD COLUMN payment_method ENUM('cash', 'credit_card', 'debit_card', 'bank_transfer', 'other') DEFAULT 'cash' AFTER description,
ADD COLUMN transaction_type ENUM('monetary', 'non_monetary') DEFAULT 'monetary' AFTER payment_method,
ADD COLUMN fair_market_value DECIMAL(10, 2) NULL AFTER transaction_type,
ADD COLUMN goods_services_description TEXT NULL AFTER fair_market_value;

-- Create debts table
CREATE TABLE debts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('given', 'received') NOT NULL COMMENT 'given = money lent to others, received = money borrowed from others',
    person_entity VARCHAR(100) NOT NULL COMMENT 'Person or entity involved in the debt',
    amount DECIMAL(10, 2) NOT NULL COMMENT 'Total debt amount',
    amount_paid DECIMAL(10, 2) DEFAULT 0 COMMENT 'Amount already paid',
    due_date DATE NOT NULL COMMENT 'When the debt is due',
    status ENUM('unpaid', 'partially_paid', 'paid') DEFAULT 'unpaid',
    notes TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
