-- Income Sources Specialized Tracking Schema Update

-- Add new fields to income_sources table for specialized tracking
ALTER TABLE income_sources 
ADD COLUMN source_type ENUM('employment', 'business', 'investment', 'rental', 'sales', 'other') DEFAULT 'other' AFTER category,
ADD COLUMN payment_method VARCHAR(50) AFTER source_type,
ADD COLUMN location VARCHAR(100) AFTER payment_method,
ADD COLUMN contact_person VARCHAR(100) AFTER location,
ADD COLUMN contact_info VARCHAR(100) AFTER contact_person,
ADD COLUMN notes TEXT AFTER contact_info,
ADD COLUMN tags VARCHAR(255) AFTER notes,
ADD COLUMN last_payment_date DATE AFTER tags,
ADD COLUMN next_expected_date DATE AFTER last_payment_date;

-- Add index for faster searching
ALTER TABLE income_sources ADD INDEX idx_source_type (source_type);
ALTER TABLE income_sources ADD INDEX idx_tags (tags);
