-- Business Ventures Table
CREATE TABLE IF NOT EXISTS `business_ventures` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `business_type` varchar(50) NOT NULL,
  `website` varchar(255) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'planning',
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  CONSTRAINT `business_ventures_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Business Metrics Table
CREATE TABLE IF NOT EXISTS `business_metrics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `venture_id` int(11) NOT NULL,
  `metric_date` date NOT NULL,
  `revenue` decimal(10,2) NOT NULL DEFAULT 0.00,
  `expenses` decimal(10,2) NOT NULL DEFAULT 0.00,
  `profit` decimal(10,2) NOT NULL DEFAULT 0.00,
  `sales_count` int(11) NOT NULL DEFAULT 0,
  `new_customers` int(11) NOT NULL DEFAULT 0,
  `website_visits` int(11) DEFAULT NULL,
  `conversion_rate` decimal(5,2) DEFAULT NULL,
  `average_order_value` decimal(10,2) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `venture_id_metric_date` (`venture_id`, `metric_date`),
  KEY `metric_date` (`metric_date`),
  CONSTRAINT `business_metrics_ibfk_1` FOREIGN KEY (`venture_id`) REFERENCES `business_ventures` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Business Customers Table
CREATE TABLE IF NOT EXISTS `business_customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `venture_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `customer_since` date NOT NULL,
  `lifetime_value` decimal(10,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `venture_id` (`venture_id`),
  CONSTRAINT `business_customers_ibfk_1` FOREIGN KEY (`venture_id`) REFERENCES `business_ventures` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Business Sales Table
CREATE TABLE IF NOT EXISTS `business_sales` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `venture_id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `sale_date` date NOT NULL,
  `product_name` varchar(255) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10,2) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `venture_id` (`venture_id`),
  KEY `customer_id` (`customer_id`),
  KEY `sale_date` (`sale_date`),
  CONSTRAINT `business_sales_ibfk_1` FOREIGN KEY (`venture_id`) REFERENCES `business_ventures` (`id`) ON DELETE CASCADE,
  CONSTRAINT `business_sales_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `business_customers` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Business Expenses Table
CREATE TABLE IF NOT EXISTS `business_expenses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `venture_id` int(11) NOT NULL,
  `expense_date` date NOT NULL,
  `category` varchar(50) NOT NULL,
  `description` varchar(255) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `recurring` tinyint(1) NOT NULL DEFAULT 0,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `venture_id` (`venture_id`),
  KEY `expense_date` (`expense_date`),
  KEY `category` (`category`),
  CONSTRAINT `business_expenses_ibfk_1` FOREIGN KEY (`venture_id`) REFERENCES `business_ventures` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Business Goals Table
CREATE TABLE IF NOT EXISTS `business_goals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `venture_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `target_value` decimal(10,2) DEFAULT NULL,
  `current_value` decimal(10,2) DEFAULT NULL,
  `metric_type` varchar(50) NOT NULL,
  `start_date` date NOT NULL,
  `target_date` date NOT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'active',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `venture_id` (`venture_id`),
  KEY `status` (`status`),
  CONSTRAINT `business_goals_ibfk_1` FOREIGN KEY (`venture_id`) REFERENCES `business_ventures` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
