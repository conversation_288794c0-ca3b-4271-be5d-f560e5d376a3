<?php
/**
 * Debug Pinterest API
 *
 * This script helps debug the PinterestAPI class.
 */

// Include required files
require_once 'src/utils/Environment.php';
require_once 'src/api/PinterestAPI.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load environment variables
Environment::load();

// Get Pinterest credentials from environment variables
$email = Environment::get('PINTEREST_EMAIL', '');
$password = Environment::get('PINTEREST_PASSWORD', '');
$username = Environment::get('PINTEREST_USERNAME', '');
$chromeProfile = Environment::get('CHROME_PROFILE_PATH', '');

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Pinterest API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #e60023;
        }
        .section {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Debug Pinterest API</h1>
    <p>This page helps debug the PinterestAPI class.</p>';

// Display current configuration
echo '<div class="section">
    <h2>Current Configuration</h2>
    <p><strong>Email:</strong> ' . htmlspecialchars($email) . '</p>
    <p><strong>Username:</strong> ' . htmlspecialchars($username) . '</p>
    <p><strong>Password:</strong> ' . ($password ? '********' : '<span class="error">Not set</span>') . '</p>
    <p><strong>Chrome Profile:</strong> ' . htmlspecialchars($chromeProfile) . '</p>
</div>';

// Test PinterestAPI initialization
echo '<div class="section">
    <h2>PinterestAPI Initialization</h2>';

try {
    // Initialize the API
    $api = PinterestAPI::getInstance($email, $password, $username, null, null, $chromeProfile);

    echo '<p class="success">PinterestAPI initialized successfully!</p>';

    // Get API properties
    $reflection = new ReflectionClass($api);

    // Get email property
    $emailProp = $reflection->getProperty('email');
    $emailProp->setAccessible(true);
    $emailValue = $emailProp->getValue($api);

    // Get username property
    $usernameProp = $reflection->getProperty('username');
    $usernameProp->setAccessible(true);
    $usernameValue = $usernameProp->getValue($api);

    // Get credRoot property
    $credRootProp = $reflection->getProperty('credRoot');
    $credRootProp->setAccessible(true);
    $credRootValue = $credRootProp->getValue($api);

    // Get chromeProfile property
    $chromeProfileProp = $reflection->getProperty('chromeProfile');
    $chromeProfileProp->setAccessible(true);
    $chromeProfileValue = $chromeProfileProp->getValue($api);

    echo '<p><strong>API Email:</strong> ' . htmlspecialchars($emailValue) . '</p>';
    echo '<p><strong>API Username:</strong> ' . htmlspecialchars($usernameValue) . '</p>';
    echo '<p><strong>API Credentials Directory:</strong> ' . htmlspecialchars($credRootValue) . '</p>';
    echo '<p><strong>API Chrome Profile:</strong> ' . htmlspecialchars($chromeProfileValue) . '</p>';

    // Check if credentials directory exists
    if (file_exists($credRootValue)) {
        echo '<p class="success">Credentials directory exists!</p>';

        // Check if cookies file exists
        $cookiesFile = $credRootValue . '/' . $usernameValue . '.cookies';
        if (file_exists($cookiesFile)) {
            echo '<p class="success">Cookies file exists: ' . htmlspecialchars($cookiesFile) . '</p>';
            echo '<p>File size: ' . filesize($cookiesFile) . ' bytes</p>';
            echo '<p>Last modified: ' . date('Y-m-d H:i:s', filemtime($cookiesFile)) . '</p>';
        } else {
            echo '<p class="error">Cookies file does not exist: ' . htmlspecialchars($cookiesFile) . '</p>';
        }
    } else {
        echo '<p class="error">Credentials directory does not exist: ' . htmlspecialchars($credRootValue) . '</p>';
    }
} catch (Exception $e) {
    echo '<p class="error">Error initializing PinterestAPI: ' . htmlspecialchars($e->getMessage()) . '</p>';
}

echo '</div>';

// Test login
echo '<div class="section">
    <h2>Login Test</h2>';

try {
    // Initialize the API
    $api = PinterestAPI::getInstance($email, $password, $username, null, null, $chromeProfile);

    // Test login
    $loginResult = $api->login();

    if ($loginResult) {
        echo '<p class="success">Login successful!</p>';
    } else {
        echo '<p class="error">Login failed. Please check your credentials and try again.</p>';
    }
} catch (Exception $e) {
    echo '<p class="error">Error during login: ' . htmlspecialchars($e->getMessage()) . '</p>';
}

echo '</div>';

// Test board listing
echo '<div class="section">
    <h2>Board Listing Test</h2>';

try {
    // Initialize the API
    $api = PinterestAPI::getInstance($email, $password, $username, null, null, $chromeProfile);

    // List boards
    $result = $api->listBoards();

    if ($result) {
        echo '<p class="success">' . htmlspecialchars($result['message']) . '</p>';

        if (!empty($result['boards'])) {
            echo '<h3>Your Pinterest Boards</h3>';
            echo '<ul>';
            foreach ($result['boards'] as $board) {
                echo '<li>';
                echo '<strong>' . htmlspecialchars($board['name']) . '</strong> ';
                echo '(ID: ' . htmlspecialchars($board['id']) . ') - ';
                echo htmlspecialchars($board['pin_count']) . ' pins';
                echo '</li>';
            }
            echo '</ul>';
        } else {
            echo '<p>No boards found.</p>';
        }
    } else {
        echo '<p class="error">Failed to list boards.</p>';

        // Try to get the error message from the error log
        $errorOutput = shell_exec('python scripts/pinterest/list_boards_selenium.py ' .
                      escapeshellarg($email) . ' ' .
                      escapeshellarg($password) . ' ' .
                      escapeshellarg($username) . ' ' .
                      escapeshellarg(__DIR__ . '/pinterest_creds') . ' ' .
                      escapeshellarg($chromeProfile) . ' 2>&1');

        echo '<h3>Debug Output:</h3>';
        echo '<pre>' . htmlspecialchars($errorOutput) . '</pre>';
    }
} catch (Exception $e) {
    echo '<p class="error">Error listing boards: ' . htmlspecialchars($e->getMessage()) . '</p>';
}

echo '</div>';

echo '</body>
</html>';
