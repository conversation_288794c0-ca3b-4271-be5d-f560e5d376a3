-- SQL script to clean up duplicate entries in the productivity_strategies table

-- Use the momentum database
USE momentum;

-- Create a temporary table to store the IDs we want to keep
CREATE TEMPORARY TABLE keep_ids (
    name VARCHAR(100) NOT NULL,
    keep_id INT NOT NULL
);

-- Insert the minimum ID for each strategy name into the temporary table
INSERT INTO keep_ids (name, keep_id)
SELECT name, MIN(id) as keep_id
FROM productivity_strategies
GROUP BY name;

-- Update user_strategies to point to the IDs we're keeping
UPDATE user_strategies us
JOIN productivity_strategies ps ON us.strategy_id = ps.id
JOIN keep_ids ki ON ps.name = ki.name AND ps.id != ki.keep_id
SET us.strategy_id = ki.keep_id;

-- Delete the duplicate entries
DELETE ps FROM productivity_strategies ps
LEFT JOIN keep_ids ki ON ps.id = ki.keep_id
WHERE ki.keep_id IS NULL OR ps.id != ki.keep_id;

-- Verify no more duplicates exist
SELECT name, COUNT(*) as count
FROM productivity_strategies
GROUP BY name
HAVING COUNT(*) > 1;

-- Show the remaining strategies
SELECT id, name FROM productivity_strategies ORDER BY name;
