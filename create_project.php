<?php
// <PERSON><PERSON><PERSON> to create a new project programmatically

// Include necessary files
require_once __DIR__ . '/src/config/config.php';
require_once __DIR__ . '/src/helpers/Session.php';
require_once __DIR__ . '/src/models/Project.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!Session::isLoggedIn()) {
    echo "Error: User not logged in. Please log in first.";
    exit;
}

// Get user ID from session
$user = Session::getUser();
$userId = $user['id'];

// Read project description from file
$description = file_get_contents(__DIR__ . '/project_description.txt');

// Prepare project data
$projectData = [
    'user_id' => $userId,
    'name' => 'ADHD-Friendly Project Planning Guide',
    'description' => $description,
    'start_date' => date('Y-m-d'),
    'end_date' => date('Y-m-d', strtotime('+3 months')),
    'status' => 'planning',
    'is_template' => 1,
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
];

// Create project model instance
$projectModel = new Project();

// Create project
$projectId = $projectModel->create($projectData);

if ($projectId) {
    echo "Project created successfully with ID: " . $projectId;
    echo "<br><a href='/momentum/projects/view/" . $projectId . "'>View Project</a>";
} else {
    echo "Failed to create project";
}
