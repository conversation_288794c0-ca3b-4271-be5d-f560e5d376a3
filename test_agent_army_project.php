<?php
/**
 * Test Agent Army Project Creation
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/Project.php';
require_once 'src/models/Task.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';
require_once 'src/models/ProjectAgentAssignment.php';
require_once 'src/models/TaskDependency.php';

// Initialize database
$db = Database::getInstance();

// Check database connection
if ($db) {
    echo "Database connection successful\n";
    
    // Create a test project directly
    $projectData = [
        'user_id' => 1,
        'name' => 'Test Agent Army Project',
        'description' => 'This is a test project for the Content Creation Brigade.',
        'start_date' => date('Y-m-d'),
        'end_date' => date('Y-m-d', strtotime('+7 days')),
        'status' => 'planning',
        'progress' => 0,
        'brigade_type' => 'content_creation',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    echo "Project data to be created:\n";
    print_r($projectData);
    
    try {
        // Enable error reporting for PDO
        $db->getConnection()->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Insert the project
        $projectId = $db->insert('projects', $projectData);
        
        if ($projectId) {
            echo "Project created successfully with ID: {$projectId}\n";
            
            // Get the Aegis Director agent
            $agentModel = new AIAgent();
            $agents = $agentModel->getUserAgents(1);
            $aegisDirectorId = null;
            
            foreach ($agents as $agent) {
                if ($agent['name'] === 'Aegis Director') {
                    $aegisDirectorId = $agent['id'];
                    break;
                }
            }
            
            if ($aegisDirectorId) {
                echo "Found Aegis Director agent with ID: {$aegisDirectorId}\n";
                
                // Assign Aegis Director to the project
                $assignmentModel = new ProjectAgentAssignment();
                $assignmentResult = $assignmentModel->assignAgentToProject($projectId, $aegisDirectorId, 'Brigade Commander');
                
                if ($assignmentResult) {
                    echo "Assigned Aegis Director to the project successfully\n";
                    
                    // Create tasks for the project
                    $taskModel = new Task();
                    
                    // Create a few sample tasks
                    $tasks = [
                        [
                            'title' => '1. Define content strategy and goals',
                            'description' => 'Establish clear objectives, target audience, and key performance indicators for the content.',
                            'priority' => 'high',
                            'status' => 'todo',
                            'user_id' => 1,
                            'project_id' => $projectId,
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ],
                        [
                            'title' => '2. Conduct content audit and gap analysis',
                            'description' => 'Analyze existing content and identify opportunities for new content.',
                            'priority' => 'high',
                            'status' => 'todo',
                            'user_id' => 1,
                            'project_id' => $projectId,
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ]
                    ];
                    
                    $taskIds = [];
                    foreach ($tasks as $taskData) {
                        $taskId = $taskModel->create($taskData);
                        if ($taskId) {
                            $taskIds[] = $taskId;
                            echo "Created task: {$taskData['title']} with ID: {$taskId}\n";
                        } else {
                            echo "Failed to create task: {$taskData['title']}\n";
                        }
                    }
                    
                    // Create dependencies between tasks
                    if (count($taskIds) > 1) {
                        $dependencyResult = $taskModel->addDependency($taskIds[1], $taskIds[0]);
                        if ($dependencyResult) {
                            echo "Created dependency: Task {$taskIds[1]} depends on Task {$taskIds[0]}\n";
                        } else {
                            echo "Failed to create dependency\n";
                        }
                    }
                    
                    // Create agent tasks for Aegis Director
                    $agentTaskModel = new AIAgentTask();
                    foreach ($tasks as $index => $taskData) {
                        $agentTaskData = [
                            'agent_id' => $aegisDirectorId,
                            'user_id' => 1,
                            'title' => "Monitor: " . $taskData['title'],
                            'description' => "Ensure completion of task: " . $taskData['description'],
                            'priority' => $taskData['priority'],
                            'status' => 'pending',
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ];
                        
                        $agentTaskId = $agentTaskModel->createTask($agentTaskData);
                        if ($agentTaskId) {
                            echo "Created agent task: {$agentTaskData['title']} with ID: {$agentTaskId}\n";
                        } else {
                            echo "Failed to create agent task: {$agentTaskData['title']}\n";
                        }
                    }
                    
                    // Create a system interaction
                    $interactionModel = new AIAgentInteraction();
                    $interactionData = [
                        'agent_id' => $aegisDirectorId,
                        'user_id' => 1,
                        'interaction_type' => 'system',
                        'content' => "Created tasks for Content Creation Brigade project: {$projectData['name']}",
                        'response' => "I've created tasks for your Content Creation Brigade. Each task has been assigned a priority. I'll monitor your progress and help keep you on track to complete this project by the deadline.",
                        'success' => true,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    
                    $interactionId = $interactionModel->createInteraction($interactionData);
                    if ($interactionId) {
                        echo "Created system interaction with ID: {$interactionId}\n";
                    } else {
                        echo "Failed to create system interaction\n";
                    }
                } else {
                    echo "Failed to assign Aegis Director to the project\n";
                }
            } else {
                echo "Aegis Director agent not found\n";
            }
        } else {
            echo "Failed to create project. Error info:\n";
            print_r($db->getConnection()->errorInfo());
        }
    } catch (Exception $e) {
        echo "Exception caught: " . $e->getMessage() . "\n";
        echo "Stack trace: " . $e->getTraceAsString() . "\n";
    }
} else {
    echo "Database connection failed\n";
}
