<?php
/**
 * Test Server Configuration
 */

// Output basic information
echo "PHP Version: " . phpversion() . "<br>";
echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script Filename: " . $_SERVER['SCRIPT_FILENAME'] . "<br>";
echo "Request URI: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "Server Name: " . $_SERVER['SERVER_NAME'] . "<br>";
echo "Server Port: " . $_SERVER['SERVER_PORT'] . "<br>";

// Check if mod_rewrite is enabled
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    $mod_rewrite = in_array('mod_rewrite', $modules);
    echo "mod_rewrite enabled: " . ($mod_rewrite ? 'Yes' : 'No') . "<br>";
} else {
    echo "mod_rewrite status: Unknown (not running under Apache or unable to check)<br>";
}

// Check file permissions
echo "<h3>File Permissions</h3>";
$files = [
    'aegis-director-interface.php',
    'public/css/tailwind.css',
    'public/js/aegis-director.js',
    'src/models/AgentBrigadeRole.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $perms_string = sprintf('%o', $perms);
        echo "{$file}: Exists (Permissions: {$perms_string})<br>";
    } else {
        echo "{$file}: Does not exist<br>";
    }
}

// Check database connection
echo "<h3>Database Connection</h3>";
if (file_exists('src/utils/Database.php')) {
    require_once 'src/utils/Database.php';
    $db = Database::getInstance();
    if ($db) {
        echo "Database connection successful<br>";
        
        // Check if required tables exist
        $tables = ['projects', 'tasks', 'ai_agents', 'agent_brigade_roles'];
        
        foreach ($tables as $table) {
            $result = $db->query("SHOW TABLES LIKE '{$table}'");
            $exists = $result && $result->rowCount() > 0;
            echo "- {$table}: " . ($exists ? "EXISTS" : "MISSING") . "<br>";
        }
    } else {
        echo "Database connection failed<br>";
    }
} else {
    echo "Database.php file not found<br>";
}

// Check URL rewriting
echo "<h3>URL Rewriting Test</h3>";
echo "Current URL: http://{$_SERVER['HTTP_HOST']}{$_SERVER['REQUEST_URI']}<br>";
echo "Try accessing: <a href='/momentum/test-rewrite'>Test Rewrite</a><br>";

// Output phpinfo
echo "<h3>PHP Info</h3>";
echo "<a href='phpinfo.php'>View PHP Info</a>";
