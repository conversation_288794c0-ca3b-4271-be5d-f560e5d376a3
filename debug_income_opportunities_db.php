<?php
/**
 * Debug script for Income Opportunities Database
 * 
 * This script helps diagnose issues with the income opportunities database.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Include required files
require_once BASE_PATH . '/src/utils/Database.php';

echo "Debug Income Opportunities Database\n";
echo "==================================\n\n";

// Get database instance directly
$db = Database::getInstance();

// Check if income_opportunities table exists
$tableCheck = $db->fetchOne("SHOW TABLES LIKE 'income_opportunities'");
echo "Income Opportunities table exists: " . ($tableCheck ? "Yes" : "No") . "\n\n";

if (!$tableCheck) {
    echo "The income_opportunities table does not exist. Please run the schema setup script.\n";
    exit;
}

// Get table structure
echo "Table Structure:\n";
echo "---------------\n";
$tableStructure = $db->fetchAll("DESCRIBE income_opportunities");
if ($tableStructure) {
    foreach ($tableStructure as $column) {
        echo $column['Field'] . " - " . $column['Type'] . " - " . ($column['Null'] === 'YES' ? 'NULL' : 'NOT NULL') . 
             ($column['Key'] ? " - " . $column['Key'] : "") . 
             ($column['Default'] ? " - Default: " . $column['Default'] : "") . "\n";
    }
} else {
    echo "Could not retrieve table structure.\n";
}
echo "\n";

// Get all opportunities in the database
$allOpportunities = $db->fetchAll("SELECT * FROM income_opportunities");
echo "Total opportunities in database: " . ($allOpportunities ? count($allOpportunities) : 0) . "\n\n";

// Display all opportunities in the database
if ($allOpportunities && count($allOpportunities) > 0) {
    echo "All opportunities in database:\n";
    echo "-----------------------------\n";
    foreach ($allOpportunities as $opportunity) {
        echo "ID: " . $opportunity['id'] . "\n";
        echo "User ID: " . $opportunity['user_id'] . "\n";
        echo "Name: " . $opportunity['name'] . "\n";
        echo "Category: " . $opportunity['category'] . "\n";
        echo "Status: " . $opportunity['status'] . "\n";
        echo "Created: " . $opportunity['created_at'] . "\n";
        echo "Updated: " . $opportunity['updated_at'] . "\n";
        echo "-----------------------------\n";
    }
} else {
    echo "No opportunities found in the database.\n";
}

// Test inserting a record directly
echo "\nTesting direct database insertion:\n";
echo "--------------------------------\n";

$testData = [
    'user_id' => 1, // Assuming user ID 1 exists
    'name' => 'Test Opportunity ' . date('Y-m-d H:i:s'),
    'description' => 'This is a test opportunity created by the debug script',
    'category' => 'Testing',
    'income_type' => 'active',
    'skill_level' => 'beginner',
    'startup_cost' => 'none',
    'time_commitment' => 'minimal',
    'income_frequency' => 'monthly',
    'status' => 'considering',
    'priority' => 5,
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
];

echo "Inserting test record...\n";
$newId = $db->insert('income_opportunities', $testData);

if ($newId) {
    echo "Success! New opportunity created with ID: $newId\n";
    
    // Verify the opportunity was created
    $newOpportunity = $db->fetchOne("SELECT * FROM income_opportunities WHERE id = ?", [$newId]);
    if ($newOpportunity) {
        echo "Verified: Opportunity exists in database.\n";
    } else {
        echo "Error: Could not find the newly created opportunity.\n";
    }
} else {
    echo "Error: Failed to create test opportunity.\n";
}

// Check database configuration
echo "\nDatabase Configuration:\n";
echo "---------------------\n";
$config = require_once BASE_PATH . '/src/config/database.php';
echo "Host: " . $config['host'] . "\n";
echo "Database: " . $config['database'] . "\n";
echo "Username: " . $config['username'] . "\n";
echo "Charset: " . $config['charset'] . "\n";

// Check for any database errors in the error log
echo "\nChecking for recent database errors in PHP error log...\n";
$errorLog = error_get_last();
if ($errorLog) {
    echo "Last error: " . print_r($errorLog, true) . "\n";
} else {
    echo "No recent PHP errors found.\n";
}

echo "\nDebug complete.\n";
