<?php
// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Connect to database directly
$host = 'localhost';
$dbname = 'momentum';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n";
    
    // Check if brigade_type column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM projects LIKE 'brigade_type'");
    $brigadeTypeExists = $stmt->rowCount() > 0;
    
    if ($brigadeTypeExists) {
        echo "The brigade_type column already exists.\n";
    } else {
        echo "Adding brigade_type column...\n";
        $pdo->exec("ALTER TABLE projects ADD COLUMN brigade_type VARCHAR(50) NULL");
        echo "Added brigade_type column successfully.\n";
    }
    
    // Check if is_brigade_template column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM projects LIKE 'is_brigade_template'");
    $isBrigadeTemplateExists = $stmt->rowCount() > 0;
    
    if ($isBrigadeTemplateExists) {
        echo "The is_brigade_template column already exists.\n";
    } else {
        echo "Adding is_brigade_template column...\n";
        $pdo->exec("ALTER TABLE projects ADD COLUMN is_brigade_template BOOLEAN DEFAULT FALSE");
        echo "Added is_brigade_template column successfully.\n";
    }
    
    // Check if parent_brigade_id column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM projects LIKE 'parent_brigade_id'");
    $parentBrigadeIdExists = $stmt->rowCount() > 0;
    
    if ($parentBrigadeIdExists) {
        echo "The parent_brigade_id column already exists.\n";
    } else {
        echo "Adding parent_brigade_id column...\n";
        $pdo->exec("ALTER TABLE projects ADD COLUMN parent_brigade_id INT NULL");
        echo "Added parent_brigade_id column successfully.\n";
    }
    
    // Check if project_agent_assignments table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'project_agent_assignments'");
    $projectAgentAssignmentsExists = $stmt->rowCount() > 0;
    
    if ($projectAgentAssignmentsExists) {
        echo "The project_agent_assignments table already exists.\n";
    } else {
        echo "Creating project_agent_assignments table...\n";
        $pdo->exec("CREATE TABLE project_agent_assignments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            project_id INT NOT NULL,
            agent_id INT NOT NULL,
            role VARCHAR(100) NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
            FOREIGN KEY (agent_id) REFERENCES ai_agents(id) ON DELETE CASCADE,
            UNIQUE KEY unique_project_agent (project_id, agent_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "Created project_agent_assignments table successfully.\n";
    }
    
    // Check if brigade_coordination table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'brigade_coordination'");
    $brigadeCoordinationExists = $stmt->rowCount() > 0;
    
    if ($brigadeCoordinationExists) {
        echo "The brigade_coordination table already exists.\n";
    } else {
        echo "Creating brigade_coordination table...\n";
        $pdo->exec("CREATE TABLE brigade_coordination (
            id INT AUTO_INCREMENT PRIMARY KEY,
            source_project_id INT NOT NULL,
            target_project_id INT NOT NULL,
            coordination_type ENUM('dependency', 'collaboration', 'information_sharing', 'resource_sharing', 'sequential', 'parallel') NOT NULL,
            description TEXT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            FOREIGN KEY (source_project_id) REFERENCES projects(id) ON DELETE CASCADE,
            FOREIGN KEY (target_project_id) REFERENCES projects(id) ON DELETE CASCADE,
            UNIQUE KEY unique_coordination (source_project_id, target_project_id, coordination_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "Created brigade_coordination table successfully.\n";
    }
    
    // Check if agent_brigade_roles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'agent_brigade_roles'");
    $agentBrigadeRolesExists = $stmt->rowCount() > 0;
    
    if ($agentBrigadeRolesExists) {
        echo "The agent_brigade_roles table already exists.\n";
    } else {
        echo "Creating agent_brigade_roles table...\n";
        $pdo->exec("CREATE TABLE agent_brigade_roles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            brigade_type VARCHAR(50) NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT NOT NULL,
            required_skills VARCHAR(255) NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            UNIQUE KEY unique_brigade_role (brigade_type, name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "Created agent_brigade_roles table successfully.\n";
    }
    
    echo "\nDatabase structure update completed successfully.\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
