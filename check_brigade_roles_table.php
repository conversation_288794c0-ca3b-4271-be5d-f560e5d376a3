<?php
/**
 * Check if the agent_brigade_roles table exists
 */

// Include required files
require_once 'src/utils/Database.php';

// Initialize database
$db = Database::getInstance();

// Check database connection
if ($db) {
    echo "Database connection successful\n";
    
    // Check if the agent_brigade_roles table exists
    $result = $db->query("SHOW TABLES LIKE 'agent_brigade_roles'");
    $exists = $result && $result->rowCount() > 0;
    
    if ($exists) {
        echo "agent_brigade_roles table exists\n";
        
        // Get the table structure
        $tableStructure = $db->query("DESCRIBE agent_brigade_roles");
        if ($tableStructure) {
            echo "Table structure:\n";
            while ($column = $tableStructure->fetch()) {
                echo "- {$column['Field']}: {$column['Type']} " . 
                     ($column['Null'] === 'NO' ? 'NOT NULL' : 'NULL') . 
                     ($column['Key'] === 'PRI' ? ' PRIMARY KEY' : '') . 
                     ($column['Default'] ? " DEFAULT '{$column['Default']}'" : '') . "\n";
            }
        }
        
        // Count the number of roles
        $countResult = $db->query("SELECT COUNT(*) as count FROM agent_brigade_roles");
        if ($countResult) {
            $count = $countResult->fetch();
            echo "Number of roles: {$count['count']}\n";
        }
    } else {
        echo "agent_brigade_roles table does not exist\n";
        
        // Create the table
        echo "Creating agent_brigade_roles table...\n";
        
        $sql = "CREATE TABLE agent_brigade_roles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            brigade_type VARCHAR(50) NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            required_skills VARCHAR(255),
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            INDEX (brigade_type)
        )";
        
        $result = $db->query($sql);
        
        if ($result) {
            echo "Table created successfully\n";
            
            // Create default roles
            require_once 'src/models/AgentBrigadeRole.php';
            $brigadeRoleModel = new AgentBrigadeRole();
            $success = $brigadeRoleModel->createDefaultRoles();
            
            if ($success) {
                echo "Default roles created successfully\n";
            } else {
                echo "Failed to create default roles\n";
            }
        } else {
            echo "Failed to create table\n";
        }
    }
} else {
    echo "Database connection failed\n";
}
