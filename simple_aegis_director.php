<?php
/**
 * Simple Aegis Director Interface
 */

// Include minimal required files
require_once 'src/utils/Database.php';

// Initialize database
$db = Database::getInstance();

// Check database connection
$dbConnected = $db ? true : false;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Aegis Director Interface</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        .status {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .button {
            display: inline-block;
            background-color: #4a6fdc;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Aegis Director Interface</h1>
        
        <h2>System Status</h2>
        <div class="status <?php echo $dbConnected ? 'success' : 'error'; ?>">
            Database Connection: <?php echo $dbConnected ? 'Connected' : 'Failed'; ?>
        </div>
        
        <h2>Available Actions</h2>
        <ul>
            <li><a href="aegis-director-interface.php" class="button">Full Aegis Director Interface</a></li>
            <li><a href="create_aegis_director_agent.php" class="button">Create Aegis Director Agent</a></li>
            <li><a href="test_aegis_director_implementation.php" class="button">Test Implementation</a></li>
        </ul>
        
        <h2>Troubleshooting</h2>
        <p>If you're having trouble accessing the full interface, try the following:</p>
        <ol>
            <li>Check that all required files exist</li>
            <li>Verify database connection and tables</li>
            <li>Check file permissions</li>
            <li>Look for PHP errors in the server logs</li>
        </ol>
        
        <h2>File Paths</h2>
        <p>Current script: <?php echo $_SERVER['SCRIPT_FILENAME']; ?></p>
        <p>Document root: <?php echo $_SERVER['DOCUMENT_ROOT']; ?></p>
    </div>
</body>
</html>
