<?php
// Simple test to check if P<PERSON> is working and project data is accessible
echo "<PERSON><PERSON> is working!<br>";

// Define base path
define('BASE_PATH', __DIR__);

// Test database connection
require_once 'src/utils/Database.php';
require_once 'src/models/BaseModel.php';
require_once 'src/models/Project.php';

try {
    $projectModel = new Project();
    $project = $projectModel->find(34);

    if ($project) {
        echo "Project found: " . htmlspecialchars($project['name']) . "<br>";
        echo "Project ID: " . $project['id'] . "<br>";
        echo "User ID: " . $project['user_id'] . "<br>";
    } else {
        echo "Project not found<br>";
    }

    // Test View class
    require_once 'src/utils/View.php';
    echo "View class loaded successfully<br>";
    echo "View::escape test: " . View::escape('<script>alert("test")</script>') . "<br>";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}

// Test if view file exists
$viewFile = 'src/views/projects/view.php';
if (file_exists($viewFile)) {
    echo "View file exists<br>";
    echo "File size: " . filesize($viewFile) . " bytes<br>";
} else {
    echo "View file does not exist<br>";
}
?>
