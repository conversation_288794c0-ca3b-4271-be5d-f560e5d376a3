<?php
require_once 'src/utils/Database.php';

try {
    $db = Database::getInstance();
    $users = $db->fetchAll('SELECT id, name, email FROM users LIMIT 5');
    
    if (empty($users)) {
        echo "No users found. You need to register a user first.\n";
        echo "Visit: http://localhost/momentum/register\n";
    } else {
        echo "Existing users:\n";
        foreach ($users as $user) {
            echo "- ID: {$user['id']}, Name: {$user['name']}, Email: {$user['email']}\n";
        }
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
