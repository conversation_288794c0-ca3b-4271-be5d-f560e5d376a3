<?php
/**
 * Test Pinterest Links
 * 
 * This script tests the Pinterest Clone links to ensure they're working correctly.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/PinterestClone.php';

try {
    // Create a new PinterestClone instance
    $pinterestClone = new PinterestClone();
    
    // Get recent scrapes
    $recentScrapes = $pinterestClone->getRecentScrapes(1);
    
    // Output HTML
    echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pinterest Links</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { display: inline-block; padding: 5px 10px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px; }
        .btn:hover { background-color: #45a049; }
    </style>
</head>
<body>
    <h1>Test Pinterest Links</h1>
    
    <h2>Recent Scrapes</h2>
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Search Term</th>
                <th>Date</th>
                <th>Pins</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>';
    
    foreach ($recentScrapes as $scrape) {
        echo '<tr>
            <td>' . $scrape['id'] . '</td>
            <td>' . htmlspecialchars($scrape['search_term']) . '</td>
            <td>' . date('M j, Y g:i A', strtotime($scrape['created_at'])) . '</td>
            <td>' . $scrape['pin_count'] . '</td>
            <td>' . $scrape['status'] . '</td>
            <td>
                <a href="/momentum/clone/pinterest/view-scrape/' . $scrape['id'] . '" class="btn" target="_blank">View (Relative)</a>
                <a href="http://' . $_SERVER['HTTP_HOST'] . '/momentum/clone/pinterest/view-scrape/' . $scrape['id'] . '" class="btn" target="_blank">View (Absolute)</a>
                <a href="#" class="btn view-link" data-id="' . $scrape['id'] . '">View (JS)</a>
            </td>
        </tr>';
    }
    
    echo '</tbody>
    </table>
    
    <h2>Link Information</h2>
    <div id="link-info">
        <p><strong>Current URL:</strong> ' . $_SERVER['REQUEST_URI'] . '</p>
        <p><strong>Base Path:</strong> /momentum</p>
        <p><strong>HTTP Host:</strong> ' . $_SERVER['HTTP_HOST'] . '</p>
    </div>
    
    <script>
        // Add event listeners to the JS view links
        document.querySelectorAll(".view-link").forEach(function(link) {
            link.addEventListener("click", function(e) {
                e.preventDefault();
                var id = this.getAttribute("data-id");
                var url = "/momentum/clone/pinterest/view-scrape/" + id;
                console.log("Navigating to:", url);
                window.open(url, "_blank");
            });
        });
    </script>
</body>
</html>';
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
