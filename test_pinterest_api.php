<?php
/**
 * Test Pinterest API Integration
 * 
 * This script tests the Pinterest API integration to ensure it works correctly.
 */

// Include the Pinterest API class
require_once 'src/api/PinterestAPI.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Output HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pinterest API Integration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1, h2 { color: #333; }
        .test-section { margin-bottom: 30px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .pin-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 20px; margin-top: 20px; }
        .pin-card { border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
        .pin-image { width: 100%; height: 200px; object-fit: cover; }
        .pin-content { padding: 10px; }
        .pin-title { font-weight: bold; margin-bottom: 5px; }
        .pin-description { font-size: 0.9em; color: #666; margin-bottom: 10px; }
        .pin-meta { font-size: 0.8em; color: #999; }
        .pin-actions { display: flex; gap: 10px; margin-top: 10px; }
        .pin-button { display: inline-block; padding: 5px 10px; background: #e60023; color: white; text-decoration: none; border-radius: 4px; font-size: 0.8em; }
        .pin-button:hover { background: #d50c22; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; max-height: 300px; }
    </style>
</head>
<body>
    <h1>Test Pinterest API Integration</h1>';

// Test 1: Initialize the Pinterest API
echo '<div class="test-section">
    <h2>Test 1: Initialize Pinterest API</h2>';

try {
    // Replace with your Pinterest credentials
    $email = '<EMAIL>'; // Replace with your Pinterest email
    $password = 'your_password_here'; // Replace with your Pinterest password
    $username = 'asankaperera9'; // Replace with your Pinterest username
    
    // Initialize the API
    $api = PinterestAPI::getInstance($email, $password, $username);
    
    echo '<p class="success">API initialized successfully</p>';
} catch (Exception $e) {
    echo '<p class="error">Error initializing API: ' . $e->getMessage() . '</p>';
}

echo '</div>';

// Test 2: Login to Pinterest
echo '<div class="test-section">
    <h2>Test 2: Login to Pinterest</h2>';

try {
    $loginResult = $api->login();
    
    if ($loginResult) {
        echo '<p class="success">Login successful</p>';
    } else {
        echo '<p class="error">Login failed</p>';
    }
} catch (Exception $e) {
    echo '<p class="error">Error during login: ' . $e->getMessage() . '</p>';
}

echo '</div>';

// Test 3: Search for pins
echo '<div class="test-section">
    <h2>Test 3: Search for Pins</h2>';

try {
    $searchTerm = 'digital marketing';
    $pins = $api->search($searchTerm, 'pins', 10);
    
    if (!empty($pins)) {
        echo '<p class="success">Found ' . count($pins) . ' pins for search term "' . $searchTerm . '"</p>';
        
        // Display the pins
        echo '<div class="pin-grid">';
        foreach (array_slice($pins, 0, 4) as $pin) {
            echo '<div class="pin-card">
                <img src="' . htmlspecialchars($pin['image_url']) . '" alt="' . htmlspecialchars($pin['title']) . '" class="pin-image">
                <div class="pin-content">
                    <div class="pin-title">' . htmlspecialchars($pin['title']) . '</div>
                    <div class="pin-description">' . htmlspecialchars(substr($pin['description'], 0, 100)) . '...</div>
                    <div class="pin-meta">
                        <div>Board: ' . htmlspecialchars($pin['board_name']) . '</div>
                        <div>Saves: ' . number_format($pin['save_count']) . '</div>
                    </div>
                    <div class="pin-actions">
                        <a href="' . htmlspecialchars($pin['pin_url']) . '" target="_blank" class="pin-button">View on Pinterest</a>
                    </div>
                </div>
            </div>';
        }
        echo '</div>';
        
        // Show the raw data for the first pin
        echo '<h3>Raw data for first pin:</h3>';
        echo '<pre>' . htmlspecialchars(print_r($pins[0], true)) . '</pre>';
    } else {
        echo '<p class="error">No pins found for search term "' . $searchTerm . '"</p>';
    }
} catch (Exception $e) {
    echo '<p class="error">Error searching for pins: ' . $e->getMessage() . '</p>';
}

echo '</div>';

// Test 4: Get pin details
echo '<div class="test-section">
    <h2>Test 4: Get Pin Details</h2>';

try {
    // Use a known pin ID
    $pinId = '573786808744734357';
    
    if (!empty($pins)) {
        // Use the first pin from the search results
        $pinId = $pins[0]['pin_id'];
    }
    
    $pinDetails = $api->getPinDetails($pinId);
    
    if ($pinDetails) {
        echo '<p class="success">Successfully retrieved details for pin ID: ' . $pinId . '</p>';
        
        // Display the pin details
        echo '<div class="pin-card" style="max-width: 400px; margin: 0 auto;">
            <img src="' . htmlspecialchars($pinDetails['image_url']) . '" alt="' . htmlspecialchars($pinDetails['title']) . '" class="pin-image">
            <div class="pin-content">
                <div class="pin-title">' . htmlspecialchars($pinDetails['title']) . '</div>
                <div class="pin-description">' . htmlspecialchars($pinDetails['description']) . '</div>
                <div class="pin-meta">
                    <div>Board: ' . htmlspecialchars($pinDetails['board_name']) . '</div>
                    <div>Saves: ' . number_format($pinDetails['save_count']) . '</div>
                    <div>Comments: ' . number_format($pinDetails['comment_count']) . '</div>
                </div>
                <div class="pin-actions">
                    <a href="' . htmlspecialchars($pinDetails['pin_url']) . '" target="_blank" class="pin-button">View on Pinterest</a>
                </div>
            </div>
        </div>';
        
        // Show the raw data
        echo '<h3>Raw pin details:</h3>';
        echo '<pre>' . htmlspecialchars(print_r($pinDetails, true)) . '</pre>';
    } else {
        echo '<p class="error">Failed to retrieve details for pin ID: ' . $pinId . '</p>';
    }
} catch (Exception $e) {
    echo '<p class="error">Error getting pin details: ' . $e->getMessage() . '</p>';
}

echo '</div>';

// Test 5: Download an image
echo '<div class="test-section">
    <h2>Test 5: Download Image</h2>';

try {
    $imageUrl = '';
    $pinId = '';
    
    if (!empty($pins)) {
        // Use the first pin from the search results
        $imageUrl = $pins[0]['image_url'];
        $pinId = $pins[0]['pin_id'];
    } else if ($pinDetails) {
        // Use the pin details
        $imageUrl = $pinDetails['image_url'];
        $pinId = $pinDetails['pin_id'];
    } else {
        // Use a fallback image URL
        $imageUrl = 'https://via.placeholder.com/600x800/f8f9fa/dc3545?text=Pinterest+Image';
        $pinId = 'placeholder';
    }
    
    // Create the output directory if it doesn't exist
    $outputDir = 'public/uploads/pinterest';
    if (!file_exists($outputDir)) {
        mkdir($outputDir, 0755, true);
    }
    
    $outputPath = $outputDir . '/test_' . $pinId . '.jpg';
    
    $downloadResult = $api->downloadImage($imageUrl, $outputPath);
    
    if ($downloadResult) {
        echo '<p class="success">Successfully downloaded image to: ' . $outputPath . '</p>';
        echo '<img src="' . $outputPath . '" alt="Downloaded image" style="max-width: 300px; display: block; margin: 20px auto; border: 1px solid #ddd;">';
    } else {
        echo '<p class="error">Failed to download image</p>';
    }
} catch (Exception $e) {
    echo '<p class="error">Error downloading image: ' . $e->getMessage() . '</p>';
}

echo '</div>';

echo '</body>
</html>';
