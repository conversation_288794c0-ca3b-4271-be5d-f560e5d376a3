<?php
/**
 * Apply Income Sources Specialized Schema
 *
 * This script applies the specialized schema updates to the income_sources table.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Include the Database class
require_once BASE_PATH . '/src/utils/Database.php';

try {
    // Get database instance
    echo "Getting database instance...\n";
    $db = Database::getInstance();
    echo "Database instance created successfully.\n";

    echo "Applying Income Sources Specialized Schema...\n";

    // Check if the income_sources table exists
    echo "Checking if income_sources table exists...\n";
    $tableResult = $db->query("SHOW TABLES LIKE 'income_sources'");
    $tableExists = $tableResult && $tableResult->rowCount() > 0;
    echo "Table exists: " . ($tableExists ? "Yes" : "No") . "\n";

    if (!$tableExists) {
        echo "Error: income_sources table does not exist. Please run apply_income_sources_schema.php first.\n";
        exit(1);
    }

    // Check if the source_type column already exists
    echo "Checking if source_type column exists...\n";
    $result = $db->query("SHOW COLUMNS FROM income_sources LIKE 'source_type'");
    $columnExists = $result && $result->rowCount() > 0;
    echo "Column exists: " . ($columnExists ? "Yes" : "No") . "\n";

    if ($columnExists) {
        echo "Specialized schema already applied.\n";
    } else {
        echo "Applying specialized schema...\n";

        // Read the schema file
        $schemaFile = BASE_PATH . '/database/income_sources_specialized_schema.sql';
        echo "Reading schema file: {$schemaFile}\n";
        $schema = file_get_contents($schemaFile);

        // Split the schema into individual statements
        $statements = explode(';', $schema);

        // Execute each statement
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                echo "Executing: " . substr($statement, 0, 50) . "...\n";
                $result = $db->query($statement);
                if ($result) {
                    echo "Success!\n";
                } else {
                    echo "Failed!\n";
                }
            }
        }

        echo "Specialized schema applied successfully!\n";
    }

    // Check if the new columns exist
    $columns = [
        'source_type',
        'payment_method',
        'location',
        'contact_person',
        'contact_info',
        'notes',
        'tags',
        'last_payment_date',
        'next_expected_date'
    ];

    echo "\nVerifying new columns:\n";
    foreach ($columns as $column) {
        $result = $db->query("SHOW COLUMNS FROM income_sources LIKE '{$column}'");
        $exists = $result && $result->rowCount() > 0;
        echo "- {$column}: " . ($exists ? "OK" : "Missing") . "\n";
    }

    echo "\nDone!\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
