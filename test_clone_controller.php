<?php
/**
 * Test Clone Controller
 * 
 * This script tests the CloneController class to ensure it works correctly.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/PinterestClone.php';
require_once 'src/controllers/BaseController.php';
require_once 'src/controllers/CloneController.php';

try {
    // Create a new CloneController instance
    echo "Creating CloneController instance...\n";
    $cloneController = new CloneController();
    echo "CloneController instance created successfully.\n";
    
    // Test that the controller has a working PinterestClone model
    echo "Controller created with PinterestClone model.\n";
    
    echo "Test completed successfully.\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
