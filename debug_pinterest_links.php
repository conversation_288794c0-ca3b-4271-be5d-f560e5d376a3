<?php
/**
 * Debug Pinterest Links
 * 
 * This script helps debug issues with Pinterest pin links.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/PinterestClone.php';

// Initialize database
$db = Database::getInstance();

// Initialize Pinterest model
$pinterestModel = PinterestClone::getInstance();

// Get a sample of pins
$pins = [];
$scrapes = $pinterestModel->getAllScrapes();

if (!empty($scrapes)) {
    // Get pins from the first scrape
    $scrapeId = $scrapes[0]['id'];
    $pins = $pinterestModel->getPinsFromScrape($scrapeId);
}

// Output HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Pinterest Links</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1, h2 { color: #333; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .error { color: red; }
        .success { color: green; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
        .btn { display: inline-block; padding: 5px 10px; background: #4CAF50; color: white; text-decoration: none; border-radius: 4px; margin-right: 5px; }
        .btn:hover { background: #45a049; }
    </style>
</head>
<body>
    <h1>Debug Pinterest Links</h1>';

// Test direct Pinterest URL access
echo '<h2>Testing Direct Pinterest URL Access</h2>';
echo '<p>This section tests direct access to Pinterest URLs to verify they are valid.</p>';

// Function to test URL
function testUrl($url) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'final_url' => $finalUrl,
        'error' => $error,
        'success' => ($httpCode >= 200 && $httpCode < 300)
    ];
}

// Test a few known good Pinterest URLs
$testUrls = [
    'https://www.pinterest.com/pin/573786808744734357/',
    'https://www.pinterest.com/pin/1055599864844775/',
    'https://www.pinterest.com/pin/4433299612528918/',
    'https://www.pinterest.com/pin/6755468166998975/'
];

echo '<table>
    <thead>
        <tr>
            <th>URL</th>
            <th>HTTP Code</th>
            <th>Final URL</th>
            <th>Status</th>
            <th>Error</th>
        </tr>
    </thead>
    <tbody>';

foreach ($testUrls as $url) {
    $result = testUrl($url);
    echo '<tr>
        <td>' . htmlspecialchars($url) . '</td>
        <td>' . $result['http_code'] . '</td>
        <td>' . htmlspecialchars($result['final_url']) . '</td>
        <td>' . ($result['success'] ? '<span class="success">Success</span>' : '<span class="error">Failed</span>') . '</td>
        <td>' . ($result['error'] ? '<span class="error">' . htmlspecialchars($result['error']) . '</span>' : '') . '</td>
    </tr>';
}

echo '</tbody>
</table>';

// Display pins from the database
if (!empty($pins)) {
    echo '<h2>Pins from Database</h2>';
    echo '<p>This section shows pins from your database and tests their URLs.</p>';
    
    echo '<table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Title</th>
                <th>Pin URL</th>
                <th>Pin ID</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>';
    
    foreach (array_slice($pins, 0, 5) as $pin) {
        $pinUrl = $pin['pin_url'];
        if (is_array($pinUrl)) {
            $pinUrl = $pinUrl['url'] ?? '';
        }
        
        $pinId = $pin['pin_id'];
        if (is_array($pin['pin_url']) && isset($pin['pin_url']['pin_id'])) {
            $pinId = $pin['pin_url']['pin_id'];
        }
        
        // Create a direct Pinterest URL
        $directUrl = "https://www.pinterest.com/pin/{$pinId}/";
        
        // Test the URL
        $result = testUrl($directUrl);
        
        echo '<tr>
            <td>' . $pin['id'] . '</td>
            <td>' . htmlspecialchars(substr($pin['title'], 0, 50)) . '...</td>
            <td>' . htmlspecialchars($pinUrl) . '</td>
            <td>' . htmlspecialchars($pinId) . '</td>
            <td>' . ($result['success'] ? '<span class="success">Valid</span>' : '<span class="error">Invalid</span>') . '</td>
            <td>
                <a href="' . htmlspecialchars($directUrl) . '" target="_blank" class="btn">Open Direct</a>
                <a href="' . htmlspecialchars($pinUrl) . '" target="_blank" class="btn">Open Original</a>
            </td>
        </tr>';
    }
    
    echo '</tbody>
    </table>';
} else {
    echo '<p class="error">No pins found in the database.</p>';
}

// Server information
echo '<h2>Server Information</h2>';
echo '<pre>';
echo 'PHP Version: ' . phpversion() . "\n";
echo 'Server Software: ' . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo 'User Agent: ' . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "\n";
echo 'Request URI: ' . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "\n";
echo 'Base URL: ' . (isset($_SERVER['HTTP_HOST']) ? 'http://' . $_SERVER['HTTP_HOST'] : 'Unknown') . "\n";
echo '</pre>';

echo '</body>
</html>';
