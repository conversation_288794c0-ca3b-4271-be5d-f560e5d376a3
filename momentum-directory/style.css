/*
Theme Name: Momentum Directory
Theme URI: https://momentum-directory.com
Author: Momentum Team
Author URI: https://momentum-directory.com
Description: A professional WordPress directory theme for business listings.
Version: 1.0.0
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: momentum-directory
Tags: directory, business-directory, listings, responsive, modern
*/

/* 
 * This is the main stylesheet for the Momentum Directory theme.
 * Most styles are loaded from separate CSS files for better organization.
 */

/* Base Styles */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --accent-color: #e74c3c;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --text-color: #333333;
    --background-color: #ffffff;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-color);
    background-color: var(--background-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--dark-color);
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: var(--dark-color);
    margin-top: 0;
}

h1 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

h2 {
    font-size: 2rem;
    margin-bottom: 1.25rem;
}

h3 {
    font-size: 1.75rem;
    margin-bottom: 1rem;
}

h4 {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
}

img {
    max-width: 100%;
    height: auto;
}

/* Container */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    text-align: center;
    transition: var(--transition);
}

.btn:hover {
    background-color: var(--dark-color);
    color: white;
}

.btn-secondary {
    background-color: var(--secondary-color);
}

.btn-accent {
    background-color: var(--accent-color);
}

.btn-outline {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Cards */
.card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    transition: var(--transition);
    height: 100%;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-content {
    padding: 1.5rem;
}

.card-title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.card-text {
    color: #666;
    margin-bottom: 1rem;
}

/* Grid System */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    padding: 0 15px;
    margin-bottom: 30px;
}

.col-1 { width: 8.33%; }
.col-2 { width: 16.66%; }
.col-3 { width: 25%; }
.col-4 { width: 33.33%; }
.col-5 { width: 41.66%; }
.col-6 { width: 50%; }
.col-7 { width: 58.33%; }
.col-8 { width: 66.66%; }
.col-9 { width: 75%; }
.col-10 { width: 83.33%; }
.col-11 { width: 91.66%; }
.col-12 { width: 100%; }

/* Responsive */
@media (max-width: 992px) {
    .col-md-1 { width: 8.33%; }
    .col-md-2 { width: 16.66%; }
    .col-md-3 { width: 25%; }
    .col-md-4 { width: 33.33%; }
    .col-md-5 { width: 41.66%; }
    .col-md-6 { width: 50%; }
    .col-md-7 { width: 58.33%; }
    .col-md-8 { width: 66.66%; }
    .col-md-9 { width: 75%; }
    .col-md-10 { width: 83.33%; }
    .col-md-11 { width: 91.66%; }
    .col-md-12 { width: 100%; }
}

@media (max-width: 768px) {
    .col-sm-1 { width: 8.33%; }
    .col-sm-2 { width: 16.66%; }
    .col-sm-3 { width: 25%; }
    .col-sm-4 { width: 33.33%; }
    .col-sm-5 { width: 41.66%; }
    .col-sm-6 { width: 50%; }
    .col-sm-7 { width: 58.33%; }
    .col-sm-8 { width: 66.66%; }
    .col-sm-9 { width: 75%; }
    .col-sm-10 { width: 83.33%; }
    .col-sm-11 { width: 91.66%; }
    .col-sm-12 { width: 100%; }
}

@media (max-width: 576px) {
    .col-xs-12 { width: 100%; }
}
