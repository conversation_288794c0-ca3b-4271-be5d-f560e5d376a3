/**
 * Main JavaScript file for Momentum Directory theme
 */

(function($) {
    'use strict';
    
    /**
     * Mobile menu toggle
     */
    $('.menu-toggle').on('click', function() {
        $(this).toggleClass('active');
        $('.main-navigation').toggleClass('toggled');
    });
    
    /**
     * User dropdown toggle
     */
    $('.user-menu-toggle').on('click', function(e) {
        e.preventDefault();
        $('.user-dropdown').toggleClass('active');
    });
    
    /**
     * Close user dropdown when clicking outside
     */
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.user-menu').length) {
            $('.user-dropdown').removeClass('active');
        }
    });
    
    /**
     * Smooth scroll to anchor links
     */
    $('a[href*="#"]:not([href="#"])').on('click', function() {
        if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && location.hostname === this.hostname) {
            var target = $(this.hash);
            target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 1000);
                return false;
            }
        }
    });
    
    /**
     * Initialize rating stars
     */
    function initRatingStars() {
        $('.rating-input').each(function() {
            var $this = $(this);
            var $stars = $this.find('.star');
            var $input = $this.find('input');
            var value = parseFloat($input.val()) || 0;
            
            // Set initial stars
            updateStars($stars, value);
            
            // Handle star click
            $stars.on('click', function() {
                var index = $(this).index();
                var newValue = index + 1;
                
                $input.val(newValue);
                updateStars($stars, newValue);
            });
        });
    }
    
    /**
     * Update rating stars
     */
    function updateStars($stars, value) {
        $stars.each(function(index) {
            if (index < value) {
                $(this).addClass('active');
            } else {
                $(this).removeClass('active');
            }
        });
    }
    
    /**
     * Initialize image gallery
     */
    function initImageGallery() {
        $('.listing-gallery').each(function() {
            var $gallery = $(this);
            var $mainImage = $gallery.find('.main-image');
            var $thumbnails = $gallery.find('.thumbnail');
            
            $thumbnails.on('click', function() {
                var src = $(this).attr('data-src');
                $mainImage.attr('src', src);
                $thumbnails.removeClass('active');
                $(this).addClass('active');
            });
        });
    }
    
    /**
     * Initialize tabs
     */
    function initTabs() {
        $('.tabs').each(function() {
            var $tabs = $(this);
            var $tabLinks = $tabs.find('.tab-link');
            var $tabContents = $tabs.find('.tab-content');
            
            $tabLinks.on('click', function(e) {
                e.preventDefault();
                
                var target = $(this).attr('href');
                
                $tabLinks.removeClass('active');
                $(this).addClass('active');
                
                $tabContents.removeClass('active');
                $(target).addClass('active');
            });
        });
    }
    
    /**
     * Initialize accordion
     */
    function initAccordion() {
        $('.accordion').each(function() {
            var $accordion = $(this);
            var $items = $accordion.find('.accordion-item');
            
            $items.each(function() {
                var $item = $(this);
                var $header = $item.find('.accordion-header');
                var $content = $item.find('.accordion-content');
                
                $header.on('click', function() {
                    $item.toggleClass('active');
                    $content.slideToggle(300);
                });
            });
        });
    }
    
    /**
     * Initialize form validation
     */
    function initFormValidation() {
        $('form.validate').each(function() {
            var $form = $(this);
            
            $form.on('submit', function(e) {
                var isValid = true;
                
                $form.find('.required').each(function() {
                    var $field = $(this);
                    var value = $field.val();
                    
                    if (!value || value.trim() === '') {
                        isValid = false;
                        $field.addClass('error');
                    } else {
                        $field.removeClass('error');
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    $form.find('.error-message').show();
                }
            });
        });
    }
    
    /**
     * Document ready
     */
    $(document).ready(function() {
        initRatingStars();
        initImageGallery();
        initTabs();
        initAccordion();
        initFormValidation();
    });
    
})(jQuery);
