/**
 * Custom styles for Momentum Directory theme
 */

/* Header Styles */
.top-bar {
    background-color: var(--dark-color);
    color: white;
    padding: 10px 0;
    font-size: 0.9rem;
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info span {
    margin-right: 20px;
}

.contact-info i {
    margin-right: 5px;
}

.social-links a {
    color: white;
    margin-left: 15px;
    transition: var(--transition);
}

.social-links a:hover {
    color: var(--primary-color);
}

.main-header {
    background-color: white;
    padding: 20px 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.main-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-branding {
    display: flex;
    align-items: center;
}

.site-logo {
    max-height: 60px;
    width: auto;
}

.site-title {
    margin: 0;
    font-size: 1.8rem;
}

.site-title a {
    color: var(--dark-color);
    text-decoration: none;
}

.main-navigation {
    display: flex;
    align-items: center;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--dark-color);
    cursor: pointer;
}

.main-navigation ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-navigation li {
    margin: 0 15px;
    position: relative;
}

.main-navigation a {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 500;
    padding: 10px 0;
    display: block;
    transition: var(--transition);
}

.main-navigation a:hover {
    color: var(--primary-color);
}

.header-actions {
    display: flex;
    align-items: center;
}

.add-listing-btn {
    margin-right: 15px;
}

.login-link {
    color: var(--dark-color);
    font-weight: 500;
    transition: var(--transition);
}

.login-link:hover {
    color: var(--primary-color);
}

.user-menu {
    position: relative;
}

.user-menu-toggle {
    display: flex;
    align-items: center;
    color: var(--dark-color);
    text-decoration: none;
    padding: 5px 10px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.user-menu-toggle:hover {
    background-color: var(--light-color);
}

.user-menu-toggle img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 10px;
}

.user-menu-toggle i {
    margin-left: 5px;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    min-width: 200px;
    z-index: 100;
    display: none;
}

.user-dropdown.active {
    display: block;
}

.user-dropdown li {
    list-style: none;
}

.user-dropdown a {
    display: block;
    padding: 10px 15px;
    color: var(--dark-color);
    text-decoration: none;
    transition: var(--transition);
}

.user-dropdown a:hover {
    background-color: var(--light-color);
}

.user-dropdown i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.page-header {
    background-color: var(--light-color);
    padding: 30px 0;
    margin-bottom: 40px;
}

.page-title {
    margin: 0 0 10px;
    font-size: 2.2rem;
    color: var(--dark-color);
}

/* Footer Styles */
.site-footer {
    background-color: var(--dark-color);
    color: white;
    margin-top: 60px;
}

.footer-widgets {
    padding: 60px 0 30px;
}

.footer-widget {
    margin-bottom: 30px;
}

.footer-logo {
    max-height: 60px;
    margin-bottom: 20px;
}

.footer-logo-text {
    font-size: 1.8rem;
    margin-bottom: 20px;
}

.footer-widget p {
    margin-bottom: 20px;
    color: #ccc;
}

.footer-widget .social-links {
    margin-top: 20px;
}

.footer-widget .social-links a {
    display: inline-block;
    width: 36px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-right: 10px;
    color: white;
    transition: var(--transition);
}

.footer-widget .social-links a:hover {
    background-color: var(--primary-color);
}

.widget-title {
    font-size: 1.4rem;
    margin-bottom: 20px;
    color: white;
    position: relative;
    padding-bottom: 10px;
}

.widget-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--primary-color);
}

.footer-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-menu li {
    margin-bottom: 10px;
}

.footer-menu a {
    color: #ccc;
    text-decoration: none;
    transition: var(--transition);
}

.footer-menu a:hover {
    color: var(--primary-color);
}

.contact-info {
    list-style: none;
    padding: 0;
    margin: 0;
}

.contact-info li {
    margin-bottom: 15px;
    display: flex;
}

.contact-info i {
    margin-right: 10px;
    color: var(--primary-color);
    width: 20px;
    text-align: center;
}

.footer-bottom {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 20px 0;
    text-align: center;
}

.footer-bottom .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.copyright {
    color: #ccc;
}

.footer-bottom-links a {
    color: #ccc;
    margin-left: 20px;
    text-decoration: none;
    transition: var(--transition);
}

.footer-bottom-links a:hover {
    color: var(--primary-color);
}

/* Breadcrumbs */
.breadcrumbs {
    margin-bottom: 30px;
}

.breadcrumbs-list {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    color: #777;
}

.breadcrumb-item:not(:last-child):after {
    content: '/';
    margin: 0 10px;
    color: #ccc;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #777;
}

/* Hero Section */
.hero-section {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 100px 0;
    position: relative;
    color: white;
    text-align: center;
    margin-bottom: 60px;
}

.hero-section:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
}

.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 20px;
    color: white;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.search-form {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
    margin-top: 30px;
}

.search-input-group {
    display: flex;
    flex-wrap: wrap;
}

.search-input {
    flex: 1;
    position: relative;
    margin-right: 10px;
}

.search-input i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #777;
}

.search-input input {
    width: 100%;
    padding: 12px 15px 12px 40px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
}

.search-form button {
    padding: 12px 30px;
}

/* Section Styles */
.section-padding {
    padding: 60px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-header h3 {
    font-size: 2rem;
    margin-bottom: 10px;
}

.section-header p {
    color: #777;
    max-width: 600px;
    margin: 0 auto;
}

.bg-light {
    background-color: var(--light-color);
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
}

.btn-block {
    width: 100%;
    display: block;
}

/* Card Styles */
.location-card {
    text-align: center;
    transition: var(--transition);
}

.card-img-placeholder {
    height: 200px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #ccc;
}

.listing-card {
    position: relative;
}

.status-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    z-index: 1;
}

.status-badge.open {
    background-color: var(--secondary-color);
    color: white;
}

.status-badge.closed {
    background-color: var(--accent-color);
    color: white;
}

.rating {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.rating i {
    color: #ffc107;
    margin-right: 2px;
}

.rating-value {
    margin-left: 5px;
    font-weight: 500;
}

.address, .work-hours, .website {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;
    color: #777;
}

.address i, .work-hours i, .website i {
    margin-right: 10px;
    min-width: 16px;
    color: var(--primary-color);
}

/* CTA Section */
.cta-section {
    background-color: var(--primary-color);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: white;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-content .btn {
    background-color: white;
    color: var(--primary-color);
}

.cta-content .btn:hover {
    background-color: var(--light-color);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .main-header .container {
        flex-wrap: wrap;
    }
    
    .site-branding {
        margin-bottom: 20px;
    }
    
    .menu-toggle {
        display: block;
    }
    
    .main-navigation ul {
        display: none;
    }
    
    .main-navigation.toggled ul {
        display: block;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: white;
        box-shadow: var(--box-shadow);
        z-index: 100;
        padding: 10px 0;
    }
    
    .main-navigation.toggled li {
        margin: 0;
    }
    
    .main-navigation.toggled a {
        padding: 10px 20px;
    }
    
    .search-input {
        flex: 0 0 100%;
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .search-form button {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .footer-bottom .container {
        flex-direction: column;
    }
    
    .copyright {
        margin-bottom: 10px;
    }
    
    .footer-bottom-links a {
        margin: 0 10px;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
}
