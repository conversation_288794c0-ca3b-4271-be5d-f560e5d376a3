<?php
/**
 * Template part for displaying posts
 *
 * @package Momentum_Directory
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}
?>

<article id="post-<?php the_ID(); ?>" <?php post_class('blog-post'); ?>>
    <header class="entry-header">
        <?php
        if (is_singular()) :
            the_title('<h1 class="entry-title">', '</h1>');
        else :
            the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>');
        endif;

        if ('post' === get_post_type()) :
            ?>
            <div class="entry-meta">
                <span class="posted-on">
                    <i class="fas fa-calendar-alt"></i>
                    <?php echo esc_html(get_the_date()); ?>
                </span>
                <span class="byline">
                    <i class="fas fa-user"></i>
                    <?php echo esc_html(get_the_author()); ?>
                </span>
                <?php if (has_category()) : ?>
                    <span class="cat-links">
                        <i class="fas fa-folder"></i>
                        <?php the_category(', '); ?>
                    </span>
                <?php endif; ?>
                <?php if (has_tag()) : ?>
                    <span class="tags-links">
                        <i class="fas fa-tags"></i>
                        <?php the_tags('', ', '); ?>
                    </span>
                <?php endif; ?>
                <span class="comments-link">
                    <i class="fas fa-comments"></i>
                    <?php comments_popup_link(
                        __('Leave a comment', 'momentum-directory'),
                        __('1 Comment', 'momentum-directory'),
                        __('% Comments', 'momentum-directory')
                    ); ?>
                </span>
            </div><!-- .entry-meta -->
        <?php endif; ?>
    </header><!-- .entry-header -->

    <?php if (has_post_thumbnail() && !is_singular()) : ?>
        <div class="post-thumbnail">
            <a href="<?php the_permalink(); ?>">
                <?php the_post_thumbnail('large', array('class' => 'img-fluid')); ?>
            </a>
        </div>
    <?php endif; ?>

    <div class="entry-content">
        <?php
        if (is_singular()) :
            the_content(
                sprintf(
                    wp_kses(
                        /* translators: %s: Name of current post. Only visible to screen readers */
                        __('Continue reading<span class="screen-reader-text"> "%s"</span>', 'momentum-directory'),
                        array(
                            'span' => array(
                                'class' => array(),
                            ),
                        )
                    ),
                    wp_kses_post(get_the_title())
                )
            );

            wp_link_pages(
                array(
                    'before' => '<div class="page-links">' . esc_html__('Pages:', 'momentum-directory'),
                    'after'  => '</div>',
                )
            );
        else :
            the_excerpt();
            ?>
            <a href="<?php the_permalink(); ?>" class="btn btn-primary read-more">
                <?php esc_html_e('Read More', 'momentum-directory'); ?>
                <i class="fas fa-arrow-right"></i>
            </a>
        <?php endif; ?>
    </div><!-- .entry-content -->

    <?php if (is_singular() && 'post' === get_post_type()) : ?>
        <footer class="entry-footer">
            <?php
            // Author bio
            if (get_the_author_meta('description')) :
                ?>
                <div class="author-bio">
                    <div class="author-avatar">
                        <?php echo get_avatar(get_the_author_meta('ID'), 100); ?>
                    </div>
                    <div class="author-info">
                        <h3 class="author-name">
                            <?php echo esc_html(get_the_author_meta('display_name')); ?>
                        </h3>
                        <p class="author-description">
                            <?php echo wp_kses_post(get_the_author_meta('description')); ?>
                        </p>
                        <div class="author-links">
                            <?php if (get_the_author_meta('url')) : ?>
                                <a href="<?php echo esc_url(get_the_author_meta('url')); ?>" target="_blank" rel="noopener noreferrer">
                                    <i class="fas fa-globe"></i>
                                </a>
                            <?php endif; ?>
                            <?php if (get_the_author_meta('twitter')) : ?>
                                <a href="<?php echo esc_url('https://twitter.com/' . get_the_author_meta('twitter')); ?>" target="_blank" rel="noopener noreferrer">
                                    <i class="fab fa-twitter"></i>
                                </a>
                            <?php endif; ?>
                            <?php if (get_the_author_meta('facebook')) : ?>
                                <a href="<?php echo esc_url(get_the_author_meta('facebook')); ?>" target="_blank" rel="noopener noreferrer">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                            <?php endif; ?>
                            <?php if (get_the_author_meta('instagram')) : ?>
                                <a href="<?php echo esc_url(get_the_author_meta('instagram')); ?>" target="_blank" rel="noopener noreferrer">
                                    <i class="fab fa-instagram"></i>
                                </a>
                            <?php endif; ?>
                            <?php if (get_the_author_meta('linkedin')) : ?>
                                <a href="<?php echo esc_url(get_the_author_meta('linkedin')); ?>" target="_blank" rel="noopener noreferrer">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php
            // Post navigation
            the_post_navigation(
                array(
                    'prev_text' => '<i class="fas fa-arrow-left"></i> <span class="nav-title">%title</span>',
                    'next_text' => '<span class="nav-title">%title</span> <i class="fas fa-arrow-right"></i>',
                )
            );
            ?>
        </footer><!-- .entry-footer -->
    <?php endif; ?>
</article><!-- #post-<?php the_ID(); ?> -->
