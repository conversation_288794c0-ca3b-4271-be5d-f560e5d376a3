<?php
/**
 * ACF Fields for the Momentum Directory theme
 *
 * @package Momentum_Directory
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register ACF fields
 */
function momentum_directory_register_acf_fields() {
    global $customFields, $customPostKey;
    
    if (!function_exists('acf_add_local_field_group')) {
        return;
    }
    
    // Create fields array
    $fields = array();
    
    // Add each custom field
    foreach ($customFields as $index => $field_name) {
        $fields[] = array(
            'key' => 'field_' . md5($field_name),
            'label' => ucwords(str_replace('_', ' ', $field_name)),
            'name' => $field_name,
            'type' => 'text',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'default_value' => '',
            'placeholder' => '',
            'prepend' => '',
            'append' => '',
            'maxlength' => '',
        );
    }
    
    // Register the field group
    acf_add_local_field_group(array(
        'key' => 'group_business_listing_fields',
        'title' => 'Business Listing Fields',
        'fields' => $fields,
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => $customPostKey,
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ));
}
add_action('acf/init', 'momentum_directory_register_acf_fields');

/**
 * Create ACF JSON sync directory
 */
function momentum_directory_acf_json_save_point($path) {
    // Update path
    $path = get_template_directory() . '/acf-json';
    
    // Create directory if it doesn't exist
    if (!file_exists($path)) {
        mkdir($path, 0755, true);
    }
    
    // Return path
    return $path;
}
add_filter('acf/settings/save_json', 'momentum_directory_acf_json_save_point');

/**
 * Register ACF JSON sync load point
 */
function momentum_directory_acf_json_load_point($paths) {
    // Remove original path
    unset($paths[0]);
    
    // Append our path
    $paths[] = get_template_directory() . '/acf-json';
    
    // Return paths
    return $paths;
}
add_filter('acf/settings/load_json', 'momentum_directory_acf_json_load_point');

/**
 * Generate ACF JSON file
 */
function momentum_directory_generate_acf_json() {
    global $customFields, $customPostKey;
    
    // Create fields array
    $fields = array();
    
    // Add each custom field
    foreach ($customFields as $index => $field_name) {
        $fields[] = array(
            'key' => 'field_' . md5($field_name),
            'label' => ucwords(str_replace('_', ' ', $field_name)),
            'name' => $field_name,
            'type' => 'text',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'default_value' => '',
            'placeholder' => '',
            'prepend' => '',
            'append' => '',
            'maxlength' => '',
        );
    }
    
    // Create field group
    $field_group = array(
        'key' => 'group_business_listing_fields',
        'title' => 'Business Listing Fields',
        'fields' => $fields,
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => $customPostKey,
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    );
    
    // Create directory if it doesn't exist
    $directory = get_template_directory() . '/acf-json';
    if (!file_exists($directory)) {
        mkdir($directory, 0755, true);
    }
    
    // Generate JSON file
    $file_path = $directory . '/group_business_listing_fields.json';
    file_put_contents($file_path, json_encode($field_group, JSON_PRETTY_PRINT));
}

// Generate ACF JSON file on theme activation
function momentum_directory_theme_activation() {
    momentum_directory_generate_acf_json();
}
add_action('after_switch_theme', 'momentum_directory_theme_activation');
