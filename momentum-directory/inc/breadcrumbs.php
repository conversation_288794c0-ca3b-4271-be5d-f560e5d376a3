<?php
/**
 * Breadcrumbs functionality
 *
 * @package Momentum_Directory
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display breadcrumbs
 */
function momentum_directory_breadcrumbs() {
    global $post, $customPostKey, $customTaxonomyKey;
    
    // Get the home URL and label
    $home_url = home_url('/');
    $home_label = __('Home', 'momentum-directory');
    
    // Start the breadcrumbs
    echo '<div class="breadcrumbs">';
    echo '<ul class="breadcrumbs-list">';
    
    // Home link
    echo '<li class="breadcrumb-item"><a href="' . esc_url($home_url) . '">' . esc_html($home_label) . '</a></li>';
    
    // Handle different page types
    if (is_front_page()) {
        // Don't show breadcrumbs on the front page
    } elseif (is_home()) {
        // Blog page
        echo '<li class="breadcrumb-item active">' . esc_html__('Blog', 'momentum-directory') . '</li>';
    } elseif (is_category()) {
        // Category archive
        $category = get_queried_object();
        echo '<li class="breadcrumb-item"><a href="' . esc_url(get_permalink(get_option('page_for_posts'))) . '">' . esc_html__('Blog', 'momentum-directory') . '</a></li>';
        echo '<li class="breadcrumb-item active">' . esc_html($category->name) . '</li>';
    } elseif (is_tag()) {
        // Tag archive
        $tag = get_queried_object();
        echo '<li class="breadcrumb-item"><a href="' . esc_url(get_permalink(get_option('page_for_posts'))) . '">' . esc_html__('Blog', 'momentum-directory') . '</a></li>';
        echo '<li class="breadcrumb-item active">' . esc_html($tag->name) . '</li>';
    } elseif (is_author()) {
        // Author archive
        $author = get_queried_object();
        echo '<li class="breadcrumb-item"><a href="' . esc_url(get_permalink(get_option('page_for_posts'))) . '">' . esc_html__('Blog', 'momentum-directory') . '</a></li>';
        echo '<li class="breadcrumb-item active">' . esc_html($author->display_name) . '</li>';
    } elseif (is_date()) {
        // Date archive
        echo '<li class="breadcrumb-item"><a href="' . esc_url(get_permalink(get_option('page_for_posts'))) . '">' . esc_html__('Blog', 'momentum-directory') . '</a></li>';
        
        if (is_day()) {
            echo '<li class="breadcrumb-item active">' . esc_html(get_the_date()) . '</li>';
        } elseif (is_month()) {
            echo '<li class="breadcrumb-item active">' . esc_html(get_the_date('F Y')) . '</li>';
        } elseif (is_year()) {
            echo '<li class="breadcrumb-item active">' . esc_html(get_the_date('Y')) . '</li>';
        }
    } elseif (is_tax($customTaxonomyKey)) {
        // Business location taxonomy
        $term = get_queried_object();
        
        // Check if it's a child term
        if ($term->parent !== 0) {
            $parent_term = get_term($term->parent, $term->taxonomy);
            $parent_term_link = get_term_link($parent_term);
            
            // Add the parent term
            echo '<li class="breadcrumb-item"><a href="' . esc_url($parent_term_link) . '">' . esc_html($parent_term->name) . '</a></li>';
        }
        
        // Add the current term
        echo '<li class="breadcrumb-item active">' . esc_html($term->name) . '</li>';
    } elseif (is_singular($customPostKey)) {
        // Business listing
        $terms = get_the_terms($post->ID, $customTaxonomyKey);
        
        if ($terms && !is_wp_error($terms)) {
            // Find the deepest term (child term)
            $child_term = null;
            $parent_term = null;
            
            foreach ($terms as $term) {
                if ($term->parent !== 0) {
                    $child_term = $term;
                    $parent_term = get_term($term->parent, $term->taxonomy);
                    break;
                }
            }
            
            // If we found a child term, add the parent and child to breadcrumbs
            if ($child_term && $parent_term) {
                $parent_term_link = get_term_link($parent_term);
                $child_term_link = get_term_link($child_term);
                
                echo '<li class="breadcrumb-item"><a href="' . esc_url($parent_term_link) . '">' . esc_html($parent_term->name) . '</a></li>';
                echo '<li class="breadcrumb-item"><a href="' . esc_url($child_term_link) . '">' . esc_html($child_term->name) . '</a></li>';
            } elseif (!empty($terms)) {
                // If no child term, just use the first term
                $term = reset($terms);
                $term_link = get_term_link($term);
                
                echo '<li class="breadcrumb-item"><a href="' . esc_url($term_link) . '">' . esc_html($term->name) . '</a></li>';
            }
        }
        
        // Add the current post
        echo '<li class="breadcrumb-item active">' . esc_html(get_the_title()) . '</li>';
    } elseif (is_page()) {
        // Regular page
        if ($post->post_parent) {
            // If the page has a parent, add the parent to the breadcrumbs
            $parent_id = $post->post_parent;
            $parents = array();
            
            while ($parent_id) {
                $parent = get_post($parent_id);
                $parents[] = array(
                    'id' => $parent_id,
                    'title' => get_the_title($parent_id),
                    'url' => get_permalink($parent_id),
                );
                $parent_id = $parent->post_parent;
            }
            
            // Add parents in reverse order (from highest to lowest in hierarchy)
            $parents = array_reverse($parents);
            
            foreach ($parents as $parent) {
                echo '<li class="breadcrumb-item"><a href="' . esc_url($parent['url']) . '">' . esc_html($parent['title']) . '</a></li>';
            }
        }
        
        // Add the current page
        echo '<li class="breadcrumb-item active">' . esc_html(get_the_title()) . '</li>';
    } elseif (is_search()) {
        // Search results
        echo '<li class="breadcrumb-item active">' . esc_html__('Search Results for: ', 'momentum-directory') . esc_html(get_search_query()) . '</li>';
    } elseif (is_404()) {
        // 404 page
        echo '<li class="breadcrumb-item active">' . esc_html__('Page Not Found', 'momentum-directory') . '</li>';
    } else {
        // Fallback for other page types
        echo '<li class="breadcrumb-item active">' . esc_html(get_the_title()) . '</li>';
    }
    
    echo '</ul>';
    echo '</div>';
}
