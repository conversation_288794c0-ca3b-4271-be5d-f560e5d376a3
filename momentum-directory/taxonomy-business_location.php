<?php
/**
 * The template for displaying business location taxonomy archives
 *
 * @package Momentum_Directory
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

global $directoryGoogleCat, $customPostKey;

get_header();

// Get the current term
$term = get_queried_object();
$parent_term = null;

// Check if it's a child term
$is_child = $term->parent !== 0;
if ($is_child) {
    $parent_term = get_term($term->parent, $term->taxonomy);
}
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <div class="container">
            <?php if (function_exists('momentum_directory_breadcrumbs')) : ?>
                <div class="taxonomy-breadcrumbs">
                    <?php momentum_directory_breadcrumbs(); ?>
                </div>
            <?php endif; ?>
            
            <header class="taxonomy-header">
                <h1 class="taxonomy-title">
                    <?php
                    if ($is_child) {
                        // Child taxonomy title format
                        printf(
                            esc_html__('%1$s near %2$s, %3$s', 'momentum-directory'),
                            esc_html($directoryGoogleCat),
                            esc_html($term->name),
                            esc_html($parent_term->name)
                        );
                    } else {
                        // Parent taxonomy title format
                        printf(
                            esc_html__('%1$s in %2$s', 'momentum-directory'),
                            esc_html($directoryGoogleCat),
                            esc_html($term->name)
                        );
                    }
                    ?>
                </h1>
                
                <?php if (!empty($term->description)) : ?>
                    <div class="taxonomy-description">
                        <?php echo wp_kses_post($term->description); ?>
                    </div>
                <?php endif; ?>
            </header>
            
            <?php if (!$is_child) : ?>
                <!-- Display child taxonomies for parent term -->
                <section class="child-taxonomies-section">
                    <div class="row">
                        <?php
                        $child_terms = get_terms(array(
                            'taxonomy' => $term->taxonomy,
                            'hide_empty' => false,
                            'parent' => $term->term_id,
                        ));
                        
                        if (!empty($child_terms) && !is_wp_error($child_terms)) {
                            foreach ($child_terms as $child_term) {
                                $child_term_link = get_term_link($child_term);
                                $post_count = momentum_get_term_post_count($child_term->term_id, $child_term->taxonomy, $customPostKey);
                                ?>
                                <div class="col col-md-3 col-sm-6">
                                    <div class="card taxonomy-card">
                                        <?php
                                        $term_image = get_term_meta($child_term->term_id, 'taxonomy_image', true);
                                        if ($term_image) {
                                            echo '<img src="' . esc_url($term_image) . '" alt="' . esc_attr($child_term->name) . '" class="card-img">';
                                        } else {
                                            echo '<div class="card-img-placeholder"><i class="fas fa-map-marker-alt"></i></div>';
                                        }
                                        ?>
                                        <div class="card-content">
                                            <h3 class="card-title"><a href="<?php echo esc_url($child_term_link); ?>"><?php echo esc_html($child_term->name); ?></a></h3>
                                            <span class="listing-count"><?php echo esc_html($post_count) . ' ' . esc_html(_n('Listing', 'Listings', $post_count, 'momentum-directory')); ?></span>
                                        </div>
                                    </div>
                                </div>
                                <?php
                            }
                        } else {
                            echo '<div class="col col-12"><p class="no-results">' . esc_html__('No cities found in this state.', 'momentum-directory') . '</p></div>';
                        }
                        ?>
                    </div>
                </section>
                
                <div class="back-link">
                    <a href="<?php echo esc_url(home_url('/')); ?>"><i class="fas fa-arrow-left"></i> <?php esc_html_e('Back to Homepage', 'momentum-directory'); ?></a>
                </div>
                
            <?php else : ?>
                <!-- Display listings for child term -->
                <section class="listings-section">
                    <div class="row">
                        <?php
                        $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
                        $listings = new WP_Query(array(
                            'post_type' => $customPostKey,
                            'tax_query' => array(
                                array(
                                    'taxonomy' => $term->taxonomy,
                                    'field' => 'term_id',
                                    'terms' => $term->term_id,
                                ),
                            ),
                            'posts_per_page' => 12,
                            'paged' => $paged,
                        ));
                        
                        if ($listings->have_posts()) {
                            while ($listings->have_posts()) {
                                $listings->the_post();
                                
                                // Get ACF fields
                                $work_time_json = get_field('work_time');
                                $address = get_field('address');
                                $url = get_field('url');
                                $rating_json = get_field('rating');
                                
                                // Parse work time
                                $current_status = '';
                                if ($work_time_json) {
                                    $work_time = json_decode($work_time_json, true);
                                    if (isset($work_time['work_hours']['current_status'])) {
                                        $current_status = $work_time['work_hours']['current_status'];
                                    }
                                }
                                
                                // Parse rating
                                $rating = null;
                                if ($rating_json) {
                                    $rating_data = json_decode($rating_json, true);
                                    if (isset($rating_data['value'])) {
                                        $rating = $rating_data['value'];
                                    }
                                }
                                ?>
                                <div class="col col-md-4 col-sm-6">
                                    <div class="card listing-card">
                                        <a href="<?php the_permalink(); ?>" class="card-img-link">
                                            <?php if (has_post_thumbnail()) : ?>
                                                <?php the_post_thumbnail('medium', array('class' => 'card-img')); ?>
                                            <?php else : ?>
                                                <img src="https://placehold.co/600x400?text=<?php echo esc_attr(get_bloginfo('name')); ?>" alt="<?php the_title_attribute(); ?>" class="card-img">
                                            <?php endif; ?>
                                            
                                            <?php if ($current_status) : ?>
                                                <span class="status-badge <?php echo esc_attr(strtolower($current_status)); ?>">
                                                    <?php echo esc_html(ucfirst($current_status)); ?>
                                                </span>
                                            <?php endif; ?>
                                        </a>
                                        
                                        <div class="card-content">
                                            <h3 class="card-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
                                            
                                            <?php if ($rating) : ?>
                                                <div class="rating">
                                                    <?php
                                                    for ($i = 1; $i <= 5; $i++) {
                                                        if ($i <= $rating) {
                                                            echo '<i class="fas fa-star"></i>';
                                                        } elseif ($i - 0.5 <= $rating) {
                                                            echo '<i class="fas fa-star-half-alt"></i>';
                                                        } else {
                                                            echo '<i class="far fa-star"></i>';
                                                        }
                                                    }
                                                    ?>
                                                    <span class="rating-value"><?php echo esc_html(number_format($rating, 1)); ?></span>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($address) : ?>
                                                <div class="address">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    <span><?php echo esc_html($address); ?></span>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($work_time_json) : ?>
                                                <div class="work-hours">
                                                    <i class="fas fa-clock"></i>
                                                    <?php
                                                    if ($work_time && isset($work_time['work_hours']['timetable'])) {
                                                        $today = strtolower(date('l'));
                                                        $today_hours = $work_time['work_hours']['timetable'][$today][0] ?? null;
                                                        
                                                        if ($today_hours && isset($today_hours['open']['hour']) && isset($today_hours['close']['hour'])) {
                                                            $open_hour = sprintf('%02d:%02d', $today_hours['open']['hour'], $today_hours['open']['minute']);
                                                            $close_hour = sprintf('%02d:%02d', $today_hours['close']['hour'], $today_hours['close']['minute']);
                                                            
                                                            if ($open_hour === '00:00' && $close_hour === '00:00') {
                                                                echo '<span>' . esc_html__('Closed today', 'momentum-directory') . '</span>';
                                                            } else {
                                                                echo '<span>' . esc_html__('Today:', 'momentum-directory') . ' ' . esc_html($open_hour) . ' - ' . esc_html($close_hour) . '</span>';
                                                            }
                                                        } else {
                                                            echo '<span>' . esc_html__('Hours not available', 'momentum-directory') . '</span>';
                                                        }
                                                    } else {
                                                        echo '<span>' . esc_html__('Hours not available', 'momentum-directory') . '</span>';
                                                    }
                                                    ?>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($url) : ?>
                                                <div class="website">
                                                    <i class="fas fa-globe"></i>
                                                    <a href="<?php echo esc_url($url); ?>" target="_blank" rel="noopener noreferrer"><?php esc_html_e('Visit Website', 'momentum-directory'); ?></a>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <a href="<?php the_permalink(); ?>" class="btn btn-outline btn-sm"><?php esc_html_e('View Details', 'momentum-directory'); ?></a>
                                        </div>
                                    </div>
                                </div>
                                <?php
                            }
                            wp_reset_postdata();
                            
                            // Pagination
                            echo '<div class="col col-12">';
                            echo '<div class="pagination">';
                            echo paginate_links(array(
                                'base' => str_replace(999999999, '%#%', esc_url(get_pagenum_link(999999999))),
                                'format' => '?paged=%#%',
                                'current' => max(1, get_query_var('paged')),
                                'total' => $listings->max_num_pages,
                                'prev_text' => '<i class="fas fa-chevron-left"></i>',
                                'next_text' => '<i class="fas fa-chevron-right"></i>',
                            ));
                            echo '</div>';
                            echo '</div>';
                            
                        } else {
                            echo '<div class="col col-12"><p class="no-results">' . esc_html__('No listings found in this location.', 'momentum-directory') . '</p></div>';
                        }
                        ?>
                    </div>
                </section>
                
                <div class="back-link">
                    <a href="<?php echo esc_url(get_term_link($parent_term)); ?>"><i class="fas fa-arrow-left"></i> <?php echo esc_html(sprintf(__('Back to %s', 'momentum-directory'), $parent_term->name)); ?></a>
                </div>
            <?php endif; ?>
        </div>
    </main><!-- #main -->
</div><!-- #primary -->

<?php
get_footer();
