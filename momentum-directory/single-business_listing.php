<?php
/**
 * The template for displaying single business listings
 *
 * @package Momentum_Directory
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

global $directoryGoogleCat, $customTaxonomyKey, $customPostKey;

get_header();

// Get ACF fields
$address = get_field('address');
$address_info_city = get_field('address_info_city');
$address_info_region = get_field('address_info_region');
$work_time_json = get_field('work_time');
$contact_info_json = get_field('contact_info');
$check_url = get_field('check_url');
$url = get_field('url');
$latitude = get_field('latitude');
$longitude = get_field('longitude');

// Get the terms
$terms = get_the_terms(get_the_ID(), $customTaxonomyKey);
$child_term = null;
$parent_term = null;

if ($terms && !is_wp_error($terms)) {
    foreach ($terms as $term) {
        if ($term->parent !== 0) {
            $child_term = $term;
            $parent_term = get_term($term->parent, $customTaxonomyKey);
            break;
        }
    }
}
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <div class="container">
            <?php if (function_exists('momentum_directory_breadcrumbs')) : ?>
                <div class="listing-breadcrumbs">
                    <?php momentum_directory_breadcrumbs(); ?>
                </div>
            <?php endif; ?>
            
            <?php
            while (have_posts()) :
                the_post();
                ?>
                
                <article id="post-<?php the_ID(); ?>" <?php post_class('listing-single'); ?>>
                    <header class="listing-header">
                        <h1 class="listing-title">
                            <?php
                            if ($address_info_city && $address_info_region) {
                                printf(
                                    esc_html__('%1$s in %2$s, %3$s', 'momentum-directory'),
                                    get_the_title(),
                                    esc_html($address_info_city),
                                    esc_html($address_info_region)
                                );
                            } else {
                                the_title();
                            }
                            ?>
                        </h1>
                        
                        <?php if ($child_term && $parent_term) : ?>
                            <div class="listing-category">
                                <i class="fas fa-tag"></i>
                                <a href="<?php echo esc_url(get_term_link($child_term)); ?>"><?php echo esc_html($child_term->name); ?></a>
                                <span class="separator">|</span>
                                <a href="<?php echo esc_url(get_term_link($parent_term)); ?>"><?php echo esc_html($parent_term->name); ?></a>
                            </div>
                        <?php endif; ?>
                    </header>
                    
                    <div class="listing-content">
                        <div class="row">
                            <div class="col col-md-8">
                                <div class="listing-gallery">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <?php the_post_thumbnail('large', array('class' => 'listing-featured-image')); ?>
                                    <?php else : ?>
                                        <img src="https://placehold.co/600x400?text=<?php echo esc_attr(get_bloginfo('name')); ?>" alt="<?php the_title_attribute(); ?>" class="listing-featured-image">
                                    <?php endif; ?>
                                </div>
                                
                                <div class="listing-details">
                                    <h2><?php esc_html_e('About', 'momentum-directory'); ?></h2>
                                    
                                    <?php
                                    // Get the raw content and parse JSON attributes
                                    $raw_content = get_the_content();
                                    $attributes = array();
                                    
                                    if (!empty($raw_content)) {
                                        try {
                                            $content_data = json_decode($raw_content, true);
                                            
                                            if (isset($content_data['available_attributes'])) {
                                                foreach ($content_data['available_attributes'] as $category => $items) {
                                                    if (!isset($attributes[$category])) {
                                                        $attributes[$category] = array(
                                                            'available' => array(),
                                                            'unavailable' => array(),
                                                        );
                                                    }
                                                    
                                                    foreach ($items as $item) {
                                                        $attributes[$category]['available'][] = $item;
                                                    }
                                                }
                                            }
                                            
                                            if (isset($content_data['unavailable_attributes'])) {
                                                foreach ($content_data['unavailable_attributes'] as $category => $items) {
                                                    if (!isset($attributes[$category])) {
                                                        $attributes[$category] = array(
                                                            'available' => array(),
                                                            'unavailable' => array(),
                                                        );
                                                    }
                                                    
                                                    foreach ($items as $item) {
                                                        $attributes[$category]['unavailable'][] = $item;
                                                    }
                                                }
                                            }
                                        } catch (Exception $e) {
                                            // If JSON parsing fails, just display the raw content
                                            echo '<div class="listing-description">';
                                            echo wp_kses_post($raw_content);
                                            echo '</div>';
                                        }
                                    }
                                    
                                    // Display attributes in a human-readable format
                                    if (!empty($attributes)) {
                                        echo '<div class="listing-attributes">';
                                        
                                        foreach ($attributes as $category => $items) {
                                            $category_label = ucwords(str_replace('_', ' ', $category));
                                            
                                            echo '<div class="attribute-category">';
                                            echo '<h3>' . esc_html($category_label) . '</h3>';
                                            
                                            if (!empty($items['available'])) {
                                                echo '<ul class="attributes-available">';
                                                foreach ($items['available'] as $item) {
                                                    $item_label = ucwords(str_replace(array('has_', '_'), array('', ' '), $item));
                                                    echo '<li><i class="fas fa-check"></i> ' . esc_html($item_label) . '</li>';
                                                }
                                                echo '</ul>';
                                            }
                                            
                                            if (!empty($items['unavailable'])) {
                                                echo '<ul class="attributes-unavailable">';
                                                foreach ($items['unavailable'] as $item) {
                                                    $item_label = ucwords(str_replace(array('has_', '_'), array('', ' '), $item));
                                                    echo '<li><i class="fas fa-times"></i> ' . esc_html($item_label) . '</li>';
                                                }
                                                echo '</ul>';
                                            }
                                            
                                            echo '</div>';
                                        }
                                        
                                        echo '</div>';
                                    } else {
                                        echo '<div class="listing-description">';
                                        the_content();
                                        echo '</div>';
                                    }
                                    ?>
                                </div>
                            </div>
                            
                            <div class="col col-md-4">
                                <div class="listing-sidebar">
                                    <div class="listing-card">
                                        <div class="listing-card-header">
                                            <h3><?php esc_html_e('Business Information', 'momentum-directory'); ?></h3>
                                        </div>
                                        
                                        <div class="listing-card-content">
                                            <?php if ($address) : ?>
                                                <div class="info-item">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    <div>
                                                        <h4><?php esc_html_e('Address', 'momentum-directory'); ?></h4>
                                                        <p><?php echo esc_html($address); ?></p>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($contact_info_json) : ?>
                                                <?php
                                                $contact_info = json_decode($contact_info_json, true);
                                                if (is_array($contact_info)) {
                                                    foreach ($contact_info as $contact) {
                                                        if (isset($contact['type']) && isset($contact['value'])) {
                                                            $icon = 'fa-info-circle';
                                                            $label = ucfirst($contact['type']);
                                                            
                                                            if ($contact['type'] === 'telephone') {
                                                                $icon = 'fa-phone';
                                                                $label = 'Phone';
                                                            } elseif ($contact['type'] === 'email') {
                                                                $icon = 'fa-envelope';
                                                                $label = 'Email';
                                                            } elseif ($contact['type'] === 'website') {
                                                                $icon = 'fa-globe';
                                                                $label = 'Website';
                                                            }
                                                            ?>
                                                            <div class="info-item">
                                                                <i class="fas <?php echo esc_attr($icon); ?>"></i>
                                                                <div>
                                                                    <h4><?php echo esc_html($label); ?></h4>
                                                                    <?php if ($contact['type'] === 'telephone') : ?>
                                                                        <p><a href="tel:<?php echo esc_attr($contact['value']); ?>"><?php echo esc_html($contact['value']); ?></a></p>
                                                                    <?php elseif ($contact['type'] === 'email') : ?>
                                                                        <p><a href="mailto:<?php echo esc_attr($contact['value']); ?>"><?php echo esc_html($contact['value']); ?></a></p>
                                                                    <?php elseif ($contact['type'] === 'website') : ?>
                                                                        <p><a href="<?php echo esc_url($contact['value']); ?>" target="_blank" rel="noopener noreferrer"><?php echo esc_html($contact['value']); ?></a></p>
                                                                    <?php else : ?>
                                                                        <p><?php echo esc_html($contact['value']); ?></p>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                            <?php
                                                        }
                                                    }
                                                }
                                                ?>
                                            <?php endif; ?>
                                            
                                            <?php if ($work_time_json) : ?>
                                                <div class="info-item">
                                                    <i class="fas fa-clock"></i>
                                                    <div>
                                                        <h4><?php esc_html_e('Business Hours', 'momentum-directory'); ?></h4>
                                                        <?php
                                                        $work_time = json_decode($work_time_json, true);
                                                        if (isset($work_time['work_hours']['timetable'])) {
                                                            echo '<div class="business-hours">';
                                                            
                                                            $days = array(
                                                                'monday' => __('Monday', 'momentum-directory'),
                                                                'tuesday' => __('Tuesday', 'momentum-directory'),
                                                                'wednesday' => __('Wednesday', 'momentum-directory'),
                                                                'thursday' => __('Thursday', 'momentum-directory'),
                                                                'friday' => __('Friday', 'momentum-directory'),
                                                                'saturday' => __('Saturday', 'momentum-directory'),
                                                                'sunday' => __('Sunday', 'momentum-directory'),
                                                            );
                                                            
                                                            $today = strtolower(date('l'));
                                                            
                                                            foreach ($days as $day_key => $day_label) {
                                                                $is_today = ($day_key === $today);
                                                                $day_hours = $work_time['work_hours']['timetable'][$day_key][0] ?? null;
                                                                
                                                                echo '<div class="hours-row' . ($is_today ? ' today' : '') . '">';
                                                                echo '<span class="day">' . esc_html($day_label) . '</span>';
                                                                
                                                                if ($day_hours && isset($day_hours['open']['hour']) && isset($day_hours['close']['hour'])) {
                                                                    $open_hour = sprintf('%02d:%02d', $day_hours['open']['hour'], $day_hours['open']['minute']);
                                                                    $close_hour = sprintf('%02d:%02d', $day_hours['close']['hour'], $day_hours['close']['minute']);
                                                                    
                                                                    if ($open_hour === '00:00' && $close_hour === '00:00') {
                                                                        echo '<span class="hours closed">' . esc_html__('Closed', 'momentum-directory') . '</span>';
                                                                    } else {
                                                                        echo '<span class="hours">' . esc_html($open_hour) . ' - ' . esc_html($close_hour) . '</span>';
                                                                    }
                                                                } else {
                                                                    echo '<span class="hours not-available">' . esc_html__('Not available', 'momentum-directory') . '</span>';
                                                                }
                                                                
                                                                echo '</div>';
                                                            }
                                                            
                                                            echo '</div>';
                                                        } else {
                                                            echo '<p>' . esc_html__('Hours not available', 'momentum-directory') . '</p>';
                                                        }
                                                        ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($url) : ?>
                                                <div class="info-item">
                                                    <i class="fas fa-globe"></i>
                                                    <div>
                                                        <h4><?php esc_html_e('Website', 'momentum-directory'); ?></h4>
                                                        <p><a href="<?php echo esc_url($url); ?>" target="_blank" rel="noopener noreferrer"><?php echo esc_html(preg_replace('#^https?://#', '', $url)); ?></a></p>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($check_url) : ?>
                                                <div class="info-item">
                                                    <i class="fas fa-map"></i>
                                                    <div>
                                                        <h4><?php esc_html_e('Map', 'momentum-directory'); ?></h4>
                                                        <p><a href="<?php echo esc_url($check_url); ?>" target="_blank" rel="noopener noreferrer"><?php esc_html_e('View on Google Maps', 'momentum-directory'); ?></a></p>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="listing-card-footer">
                                            <?php if ($url) : ?>
                                                <a href="<?php echo esc_url($url); ?>" class="btn btn-primary btn-block" target="_blank" rel="noopener noreferrer">
                                                    <i class="fas fa-external-link-alt"></i> <?php esc_html_e('Visit Website', 'momentum-directory'); ?>
                                                </a>
                                            <?php endif; ?>
                                            
                                            <?php if ($check_url) : ?>
                                                <a href="<?php echo esc_url($check_url); ?>" class="btn btn-outline btn-block" target="_blank" rel="noopener noreferrer">
                                                    <i class="fas fa-directions"></i> <?php esc_html_e('Get Directions', 'momentum-directory'); ?>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <?php if ($latitude && $longitude) : ?>
                                        <div class="listing-map">
                                            <div id="listing-map" style="height: 300px;"></div>
                                            <script>
                                                function initMap() {
                                                    var location = {lat: <?php echo esc_js((float) $latitude); ?>, lng: <?php echo esc_js((float) $longitude); ?>};
                                                    var map = new google.maps.Map(document.getElementById('listing-map'), {
                                                        zoom: 15,
                                                        center: location
                                                    });
                                                    var marker = new google.maps.Marker({
                                                        position: location,
                                                        map: map,
                                                        title: '<?php echo esc_js(get_the_title()); ?>'
                                                    });
                                                }
                                            </script>
                                            <script async defer src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap"></script>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </article>
                
                <?php
            endwhile; // End of the loop.
            ?>
            
            <!-- Nearby Businesses Section -->
            <section class="nearby-businesses-section">
                <h2>
                    <?php
                    printf(
                        esc_html__('Nearby %1$s in %2$s, %3$s', 'momentum-directory'),
                        esc_html($directoryGoogleCat),
                        esc_html($address_info_city),
                        esc_html($address_info_region)
                    );
                    ?>
                </h2>
                
                <div class="row">
                    <?php
                    // Get nearby listings in the same child taxonomy
                    if ($child_term) {
                        $nearby_listings = new WP_Query(array(
                            'post_type' => $customPostKey,
                            'posts_per_page' => 6,
                            'post__not_in' => array(get_the_ID()),
                            'tax_query' => array(
                                array(
                                    'taxonomy' => $customTaxonomyKey,
                                    'field' => 'term_id',
                                    'terms' => $child_term->term_id,
                                ),
                            ),
                        ));
                        
                        if ($nearby_listings->have_posts()) {
                            while ($nearby_listings->have_posts()) {
                                $nearby_listings->the_post();
                                ?>
                                <div class="col col-md-6">
                                    <div class="card nearby-card">
                                        <a href="<?php the_permalink(); ?>" class="card-img-link">
                                            <?php if (has_post_thumbnail()) : ?>
                                                <?php the_post_thumbnail('medium', array('class' => 'card-img')); ?>
                                            <?php else : ?>
                                                <img src="https://placehold.co/600x400?text=<?php echo esc_attr(get_bloginfo('name')); ?>" alt="<?php the_title_attribute(); ?>" class="card-img">
                                            <?php endif; ?>
                                        </a>
                                        <div class="card-content">
                                            <h3 class="card-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
                                            <?php
                                            $nearby_address = get_field('address');
                                            if ($nearby_address) {
                                                echo '<div class="address"><i class="fas fa-map-marker-alt"></i> ' . esc_html($nearby_address) . '</div>';
                                            }
                                            ?>
                                            <a href="<?php the_permalink(); ?>" class="btn btn-outline btn-sm"><?php esc_html_e('View Details', 'momentum-directory'); ?></a>
                                        </div>
                                    </div>
                                </div>
                                <?php
                            }
                            wp_reset_postdata();
                        } else {
                            echo '<div class="col col-12"><p class="no-results">' . esc_html__('No nearby businesses found.', 'momentum-directory') . '</p></div>';
                        }
                    } else {
                        echo '<div class="col col-12"><p class="no-results">' . esc_html__('No nearby businesses found.', 'momentum-directory') . '</p></div>';
                    }
                    ?>
                </div>
            </section>
        </div>
    </main><!-- #main -->
</div><!-- #primary -->

<?php
get_footer();
