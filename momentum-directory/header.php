<?php
/**
 * The header for our theme
 *
 * @package Momentum_Directory
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

global $siteTitle;
?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <?php if (file_exists(get_template_directory() . '/assets/images/favicon.png')) : ?>
        <link rel="icon" href="<?php echo esc_url(get_template_directory_uri() . '/assets/images/favicon.png'); ?>" type="image/png">
    <?php endif; ?>
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <a class="skip-link screen-reader-text" href="#primary"><?php esc_html_e('Skip to content', 'momentum-directory'); ?></a>

    <header id="masthead" class="site-header">
        <div class="top-bar">
            <div class="container">
                <div class="top-bar-content">
                    <div class="contact-info">
                        <span><i class="fas fa-envelope"></i> info@<?php echo esc_html(str_replace('https://', '', home_url('/'))); ?></span>
                        <span><i class="fas fa-phone"></i> +****************</span>
                    </div>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-header">
            <div class="container">
                <div class="site-branding">
                    <?php if (file_exists(get_template_directory() . '/assets/images/logo.png')) : ?>
                        <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                            <img src="<?php echo esc_url(get_template_directory_uri() . '/assets/images/logo.png'); ?>" alt="<?php echo esc_attr($siteTitle); ?>" class="site-logo">
                        </a>
                    <?php else : ?>
                        <?php the_custom_logo(); ?>
                        <h1 class="site-title"><a href="<?php echo esc_url(home_url('/')); ?>" rel="home"><?php bloginfo('name'); ?></a></h1>
                    <?php endif; ?>
                </div><!-- .site-branding -->

                <nav id="site-navigation" class="main-navigation">
                    <button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                        <i class="fas fa-bars"></i>
                    </button>
                    <?php
                    wp_nav_menu(array(
                        'theme_location' => 'primary',
                        'menu_id'        => 'primary-menu',
                        'container'      => false,
                        'fallback_cb'    => function() {
                            echo '<ul id="primary-menu" class="menu">';
                            echo '<li><a href="' . esc_url(home_url('/')) . '">' . esc_html__('Home', 'momentum-directory') . '</a></li>';
                            echo '<li><a href="#">' . esc_html__('About', 'momentum-directory') . '</a></li>';
                            echo '<li><a href="#">' . esc_html__('Listings', 'momentum-directory') . '</a></li>';
                            echo '<li><a href="#">' . esc_html__('Contact', 'momentum-directory') . '</a></li>';
                            echo '</ul>';
                        },
                    ));
                    ?>
                </nav><!-- #site-navigation -->

                <div class="header-actions">
                    <a href="#" class="btn btn-primary add-listing-btn"><i class="fas fa-plus"></i> Add Listing</a>
                    <?php if (!is_user_logged_in()) : ?>
                        <a href="<?php echo esc_url(wp_login_url()); ?>" class="login-link"><i class="fas fa-user"></i> Login</a>
                    <?php else : ?>
                        <div class="user-menu">
                            <a href="#" class="user-menu-toggle">
                                <?php 
                                $current_user = wp_get_current_user();
                                echo get_avatar($current_user->ID, 32);
                                ?>
                                <span><?php echo esc_html($current_user->display_name); ?></span>
                                <i class="fas fa-chevron-down"></i>
                            </a>
                            <ul class="user-dropdown">
                                <li><a href="<?php echo esc_url(admin_url('profile.php')); ?>"><i class="fas fa-user-circle"></i> Profile</a></li>
                                <li><a href="<?php echo esc_url(admin_url('edit.php?post_type=business_listing')); ?>"><i class="fas fa-store"></i> My Listings</a></li>
                                <li><a href="<?php echo esc_url(wp_logout_url(home_url())); ?>"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </header><!-- #masthead -->

    <?php if (!is_front_page()) : ?>
        <div class="page-header">
            <div class="container">
                <h1 class="page-title">
                    <?php
                    if (is_archive()) {
                        the_archive_title();
                    } elseif (is_search()) {
                        printf(esc_html__('Search Results for: %s', 'momentum-directory'), '<span>' . get_search_query() . '</span>');
                    } elseif (is_404()) {
                        esc_html_e('Page Not Found', 'momentum-directory');
                    } else {
                        the_title();
                    }
                    ?>
                </h1>
                <?php 
                if (function_exists('momentum_directory_breadcrumbs')) {
                    momentum_directory_breadcrumbs();
                }
                ?>
            </div>
        </div>
    <?php endif; ?>
