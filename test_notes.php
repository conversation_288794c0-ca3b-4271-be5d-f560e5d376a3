<?php
/**
 * Test script for enhanced notes functionality
 */

require_once 'src/utils/Database.php';
require_once 'src/models/Note.php';

try {
    echo "Testing Enhanced Notes Functionality...\n\n";
    
    // Test database connection
    $db = Database::getInstance();
    echo "✓ Database connection successful\n";
    
    // Test Note model
    $noteModel = new Note();
    echo "✓ Note model instantiated\n";
    
    // Test getting user notes (using user ID 1 for testing)
    $userId = 1;
    
    // Test getUserNotes method
    $notes = $noteModel->getUserNotes($userId, [], 10, 0);
    echo "✓ getUserNotes method works - Found " . count($notes) . " notes\n";
    
    // Test getUserNoteCategories method
    $categories = $noteModel->getUserNoteCategories($userId);
    echo "✓ getUserNoteCategories method works - Found " . count($categories) . " categories\n";
    
    // Test getPopularTags method
    $tags = $noteModel->getPopularTags($userId, 10);
    echo "✓ getPopularTags method works - Found " . count($tags) . " tags\n";
    
    // Test getNoteStats method
    $stats = $noteModel->getNoteStats($userId);
    echo "✓ getNoteStats method works\n";
    echo "  - Total notes: " . ($stats['total_notes'] ?? 0) . "\n";
    echo "  - Pinned notes: " . ($stats['pinned_notes'] ?? 0) . "\n";
    echo "  - High priority: " . ($stats['high_priority'] ?? 0) . "\n";
    echo "  - Today's notes: " . ($stats['today_notes'] ?? 0) . "\n";
    
    // Test search functionality
    if (count($notes) > 0) {
        $searchResults = $noteModel->searchNotes($userId, 'test', []);
        echo "✓ searchNotes method works - Found " . count($searchResults) . " results for 'test'\n";
    }
    
    // Test creating a test note with enhanced fields
    $testNoteData = [
        'user_id' => $userId,
        'title' => 'Enhanced Test Note',
        'content' => 'This is a test note with enhanced ADHD-friendly features. It includes priority levels, favorites, and auto-save capabilities.',
        'category' => 'Testing',
        'tags' => 'test,enhanced,adhd-friendly',
        'is_pinned' => 0,
        'is_favorite' => 1,
        'priority_level' => 'high',
        'color_code' => '#ff6b6b',
        'note_type' => 'note',
        'word_count' => 20,
        'reading_time' => 1,
        'auto_saved' => 0,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    $newNoteId = $noteModel->create($testNoteData);
    if ($newNoteId) {
        echo "✓ Created test note with ID: $newNoteId\n";
        
        // Test updating the note
        $updateResult = $noteModel->update($newNoteId, [
            'title' => 'Updated Enhanced Test Note',
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($updateResult) {
            echo "✓ Updated test note successfully\n";
        }
        
        // Test auto-save functionality
        $autoSaveResult = $noteModel->autoSave($userId, $newNoteId, [
            'title' => 'Auto-saved Test Note',
            'content' => 'This content was auto-saved',
            'category' => 'Testing',
            'tags' => 'auto-save,test'
        ]);
        
        if ($autoSaveResult) {
            echo "✓ Auto-save functionality works\n";
        }
        
        // Clean up - delete the test note
        $deleteResult = $noteModel->delete($newNoteId);
        if ($deleteResult) {
            echo "✓ Deleted test note successfully\n";
        }
    }
    
    // Test template functionality
    $templatesQuery = "SELECT COUNT(*) as count FROM note_templates WHERE is_public = 1";
    $templateCount = $db->fetchOne($templatesQuery);
    echo "✓ Found " . ($templateCount['count'] ?? 0) . " public templates\n";
    
    echo "\n🎉 All enhanced notes functionality tests passed!\n";
    echo "\nEnhanced features available:\n";
    echo "- ✅ Auto-save functionality\n";
    echo "- ✅ Priority levels (high, medium, low)\n";
    echo "- ✅ Favorites system\n";
    echo "- ✅ Enhanced search with filters\n";
    echo "- ✅ Note statistics and analytics\n";
    echo "- ✅ Popular tags system\n";
    echo "- ✅ Note templates\n";
    echo "- ✅ Word count and reading time\n";
    echo "- ✅ Color coding support\n";
    echo "- ✅ ADHD-friendly organization\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
