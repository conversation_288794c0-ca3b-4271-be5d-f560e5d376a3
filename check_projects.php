<?php
// Database connection
$dbConfig = require_once __DIR__ . '/src/config/database.php';
$pdo = new PDO(
    "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}",
    $dbConfig['username'],
    $dbConfig['password'],
    [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
);

// Check projects table
echo "=== PROJECTS TABLE ===\n";
$stmt = $pdo->query("SELECT * FROM projects ORDER BY id DESC LIMIT 10");
$projects = $stmt->fetchAll();

if (empty($projects)) {
    echo "No projects found in the database.\n";
} else {
    echo "Found " . count($projects) . " projects:\n";
    foreach ($projects as $project) {
        echo "ID: {$project['id']}, Name: {$project['name']}, User ID: {$project['user_id']}, Status: {$project['status']}\n";
    }
}

// Check tasks table for the latest project
echo "\n=== TASKS FOR LATEST PROJECT ===\n";
if (!empty($projects)) {
    $latestProject = $projects[0];
    $stmt = $pdo->prepare("SELECT * FROM tasks WHERE project_id = ?");
    $stmt->execute([$latestProject['id']]);
    $tasks = $stmt->fetchAll();
    
    if (empty($tasks)) {
        echo "No tasks found for project ID {$latestProject['id']}.\n";
    } else {
        echo "Found " . count($tasks) . " tasks for project ID {$latestProject['id']}:\n";
        foreach ($tasks as $task) {
            echo "ID: {$task['id']}, Title: {$task['title']}, Status: {$task['status']}\n";
        }
    }
}

// Check users table
echo "\n=== USERS TABLE ===\n";
$stmt = $pdo->query("SELECT id, name, email FROM users LIMIT 5");
$users = $stmt->fetchAll();

if (empty($users)) {
    echo "No users found in the database.\n";
} else {
    echo "Found " . count($users) . " users:\n";
    foreach ($users as $user) {
        echo "ID: {$user['id']}, Name: {$user['name']}, Email: {$user['email']}\n";
    }
}
