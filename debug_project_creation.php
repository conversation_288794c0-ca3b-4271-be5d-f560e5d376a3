<?php
/**
 * Debug Project Creation
 * 
 * This script debugs the creation of an AI Agent Army project.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/Project.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/ProjectAgentAssignment.php';

// Initialize models
$db = Database::getInstance();
$projectModel = new Project();
$agentModel = new AIAgent();
$assignmentModel = new ProjectAgentAssignment();

// Check database connection
if ($db) {
    echo "Database connection successful\n";
} else {
    echo "Database connection failed\n";
    exit;
}

// Create a test project directly
$projectData = [
    'user_id' => 1,
    'name' => 'Test Direct Project Creation',
    'description' => 'This is a test project created directly.',
    'start_date' => date('Y-m-d'),
    'end_date' => date('Y-m-d', strtotime('+7 days')),
    'status' => 'planning',
    'progress' => 0,
    'brigade_type' => 'content_creation',
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
];

echo "Creating project with data:\n";
print_r($projectData);

$projectId = $projectModel->create($projectData);

if ($projectId) {
    echo "Project created successfully with ID: {$projectId}\n";
    
    // Get the Aegis Director agent
    $agents = $agentModel->getUserAgents(1);
    $aegisDirectorId = null;
    
    foreach ($agents as $agent) {
        if ($agent['name'] === 'Aegis Director') {
            $aegisDirectorId = $agent['id'];
            break;
        }
    }
    
    if ($aegisDirectorId) {
        echo "Found Aegis Director agent with ID: {$aegisDirectorId}\n";
        
        // Assign Aegis Director to the project
        $assignmentResult = $assignmentModel->assignAgentToProject($projectId, $aegisDirectorId, 'Brigade Commander');
        
        if ($assignmentResult) {
            echo "Assigned Aegis Director to the project successfully\n";
        } else {
            echo "Failed to assign Aegis Director to the project\n";
        }
    } else {
        echo "Aegis Director agent not found\n";
    }
} else {
    echo "Failed to create project\n";
    
    // Check if there's an error in the database
    $error = $db->getConnection()->errorInfo();
    echo "Database error: " . print_r($error, true) . "\n";
}
