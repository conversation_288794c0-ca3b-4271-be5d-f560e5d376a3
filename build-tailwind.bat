@echo off
echo Building Tailwind CSS for Momentum...
echo.

REM Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: Node.js is not installed or not in PATH.
    echo Please install Node.js from https://nodejs.org/
    exit /b 1
)

REM Check if npm is installed
where npm >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: npm is not installed or not in PATH.
    echo Please install Node.js from https://nodejs.org/
    exit /b 1
)

REM Check if package.json exists
if not exist package.json (
    echo Error: package.json not found.
    echo Please run this script from the root directory of the Momentum project.
    exit /b 1
)

REM Install dependencies if node_modules doesn't exist
if not exist node_modules (
    echo Installing dependencies...
    npm install
    if %ERRORLEVEL% neq 0 (
        echo Error: Failed to install dependencies.
        exit /b 1
    )
)

REM Build Tailwind CSS
echo Building Tailwind CSS...
npx tailwindcss -i ./src/input.css -o ./public/css/tailwind.css --minify
if %ERRORLEVEL% neq 0 (
    echo Error: Failed to build Tailwind CSS.
    exit /b 1
)

echo.
echo Tailwind CSS built successfully!
echo Output file: public/css/tailwind.css
echo.

exit /b 0
