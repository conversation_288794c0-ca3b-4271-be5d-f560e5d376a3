<?php
/**
 * Medication Notifications API Endpoint
 * 
 * Handles real-time medication reminder notifications
 */

require_once '../src/utils/Database.php';
require_once '../src/utils/Session.php';
require_once '../src/utils/NotificationSystem.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Check if user is logged in
    Session::start();
    $userId = Session::get('user_id');
    
    if (!$userId) {
        http_response_code(401);
        echo json_encode(['error' => 'Not authenticated']);
        exit;
    }
    
    $notificationSystem = new NotificationSystem();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get notifications for the user
        $notifications = $notificationSystem->getNotificationData($userId);
        $stats = $notificationSystem->getNotificationStats($userId);
        
        echo json_encode([
            'success' => true,
            'notifications' => $notifications,
            'stats' => $stats,
            'timestamp' => time()
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle notification actions
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['action'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request data']);
            exit;
        }
        
        $result = $notificationSystem->handleAction($userId, $input['action'], $input);
        
        if ($result['success']) {
            echo json_encode($result);
        } else {
            http_response_code(400);
            echo json_encode($result);
        }
        
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log('Medication Notifications API Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
