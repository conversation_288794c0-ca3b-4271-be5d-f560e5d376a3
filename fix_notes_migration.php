<?php
require_once 'src/utils/Database.php';

try {
    $db = Database::getInstance();
    echo "Adding missing columns to notes table...\n\n";
    
    // Add columns one by one to avoid issues
    $alterStatements = [
        "ALTER TABLE notes ADD COLUMN is_favorite TINYINT(1) DEFAULT 0",
        "ALTER TABLE notes ADD COLUMN priority_level ENUM('low', 'medium', 'high') DEFAULT 'medium'",
        "ALTER TABLE notes ADD COLUMN last_accessed DATETIME NULL",
        "ALTER TABLE notes ADD COLUMN auto_saved TINYINT(1) DEFAULT 0",
        "ALTER TABLE notes ADD COLUMN word_count INT DEFAULT 0",
        "ALTER TABLE notes ADD COLUMN reading_time INT DEFAULT 0",
        "ALTER TABLE notes ADD COLUMN color_code VARCHAR(7) NULL",
        "ALTER TABLE notes ADD COLUMN template_id INT NULL",
        "ALTER TABLE notes ADD COLUMN parent_note_id INT NULL",
        "ALTER TABLE notes ADD COLUMN note_type ENUM('note', 'template', 'checklist', 'journal') DEFAULT 'note'"
    ];
    
    foreach ($alterStatements as $sql) {
        try {
            $db->query($sql);
            echo "✓ " . substr($sql, 0, 50) . "...\n";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "- Column already exists: " . substr($sql, 0, 50) . "...\n";
            } else {
                echo "✗ Error: " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Add indexes
    $indexStatements = [
        "CREATE INDEX idx_notes_user_priority ON notes(user_id, priority_level)",
        "CREATE INDEX idx_notes_user_category ON notes(user_id, category)",
        "CREATE INDEX idx_notes_user_updated ON notes(user_id, updated_at)",
        "CREATE INDEX idx_notes_user_pinned ON notes(user_id, is_pinned)",
        "CREATE INDEX idx_notes_user_favorite ON notes(user_id, is_favorite)",
        "CREATE INDEX idx_notes_last_accessed ON notes(last_accessed)",
        "CREATE INDEX idx_notes_auto_saved ON notes(auto_saved)",
        "CREATE INDEX idx_notes_template ON notes(template_id)",
        "CREATE INDEX idx_notes_parent ON notes(parent_note_id)"
    ];
    
    echo "\nAdding performance indexes...\n";
    foreach ($indexStatements as $sql) {
        try {
            $db->query($sql);
            echo "✓ " . substr($sql, 0, 50) . "...\n";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "- Index already exists: " . substr($sql, 0, 50) . "...\n";
            } else {
                echo "✗ Error: " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Create note templates table
    echo "\nCreating note templates table...\n";
    $templateTableSql = "CREATE TABLE IF NOT EXISTS note_templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        template_content TEXT,
        category VARCHAR(100),
        default_tags VARCHAR(500),
        is_public TINYINT(1) DEFAULT 0,
        usage_count INT DEFAULT 0,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        INDEX idx_templates_user (user_id),
        INDEX idx_templates_public (is_public),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    try {
        $db->query($templateTableSql);
        echo "✓ Note templates table created\n";
    } catch (Exception $e) {
        echo "✗ Error creating templates table: " . $e->getMessage() . "\n";
    }
    
    // Insert default templates
    echo "\nInserting default templates...\n";
    $defaultTemplates = [
        [
            'name' => 'Daily Journal',
            'description' => 'ADHD-friendly daily reflection template',
            'template_content' => "# Daily Journal - {{date}}\n\n## 🎯 Today's Focus\n- [ ] Priority 1:\n- [ ] Priority 2:\n- [ ] Priority 3:\n\n## 💭 Thoughts & Feelings\n\n\n## ✅ Accomplishments\n\n\n## 🔄 Tomorrow's Prep\n\n\n## 📝 Random Notes\n",
            'category' => 'Personal',
            'default_tags' => 'journal,daily,reflection'
        ],
        [
            'name' => 'Meeting Notes',
            'description' => 'Structured meeting notes template',
            'template_content' => "# Meeting: {{title}}\n\n**Date:** {{date}}\n**Attendees:** \n**Duration:** \n\n## 📋 Agenda\n- \n\n## 🗣️ Discussion Points\n\n\n## ✅ Action Items\n- [ ] \n\n## 📝 Follow-up\n\n",
            'category' => 'Work',
            'default_tags' => 'meeting,work,action-items'
        ],
        [
            'name' => 'Quick Capture',
            'description' => 'Fast note template for ADHD brain dumps',
            'template_content' => "# Quick Capture - {{time}}\n\n## 💡 Main Idea\n\n\n## 🔗 Related To\n\n\n## ⏰ Follow-up Needed?\n- [ ] Yes - When: \n- [ ] No\n\n## 🏷️ Tags\n",
            'category' => 'Ideas',
            'default_tags' => 'quick,capture,ideas'
        ]
    ];
    
    foreach ($defaultTemplates as $template) {
        $checkSql = "SELECT COUNT(*) as count FROM note_templates WHERE name = ? AND user_id = 0";
        $exists = $db->fetchOne($checkSql, [$template['name']]);
        
        if ($exists['count'] == 0) {
            $insertSql = "INSERT INTO note_templates (user_id, name, description, template_content, category, default_tags, is_public, created_at, updated_at) VALUES (0, ?, ?, ?, ?, ?, 1, NOW(), NOW())";
            try {
                $db->query($insertSql, [
                    $template['name'],
                    $template['description'],
                    $template['template_content'],
                    $template['category'],
                    $template['default_tags']
                ]);
                echo "✓ Added template: " . $template['name'] . "\n";
            } catch (Exception $e) {
                echo "✗ Error adding template " . $template['name'] . ": " . $e->getMessage() . "\n";
            }
        } else {
            echo "- Template already exists: " . $template['name'] . "\n";
        }
    }
    
    // Update existing notes with default values
    echo "\nUpdating existing notes with default values...\n";
    $updateSql = "UPDATE notes SET 
        priority_level = COALESCE(priority_level, 'medium'),
        is_favorite = COALESCE(is_favorite, 0),
        auto_saved = COALESCE(auto_saved, 0),
        note_type = COALESCE(note_type, 'note'),
        word_count = CASE 
            WHEN word_count IS NULL OR word_count = 0 THEN 
                GREATEST(1, LENGTH(content) - LENGTH(REPLACE(content, ' ', '')) + 1)
            ELSE word_count 
        END,
        reading_time = CASE 
            WHEN reading_time IS NULL OR reading_time = 0 THEN 
                GREATEST(1, ROUND((LENGTH(content) - LENGTH(REPLACE(content, ' ', '')) + 1) / 200))
            ELSE reading_time 
        END
        WHERE content IS NOT NULL";
    
    try {
        $db->query($updateSql);
        echo "✓ Updated existing notes with default values\n";
    } catch (Exception $e) {
        echo "✗ Error updating existing notes: " . $e->getMessage() . "\n";
    }
    
    echo "\n🎉 Notes enhancement migration completed!\n";
    
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
}
?>
