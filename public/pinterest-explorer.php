<?php
/**
 * Pinterest Explorer
 *
 * A production-ready Pinterest-like interface for the Enhanced Pinterest Scraper.
 */

require_once __DIR__ . '/../src/utils/View.php';
require_once __DIR__ . '/../src/utils/AssetManager.php';
require_once __DIR__ . '/../src/utils/Session.php';
require_once __DIR__ . '/../src/utils/Environment.php';
require_once __DIR__ . '/../src/api/EnhancedPinterestAPI.php';

// Start the session
Session::start();

// Check if the user is logged in
if (!Session::isLoggedIn()) {
    // Set flash message
    Session::setFlash('error', 'Please log in to access the Pinterest Explorer');

    // Redirect to login page
    header('Location: /momentum/login');
    exit;
}

// Load environment variables
Environment::load();

// Get credentials from environment variables
$email = Environment::get('PINTEREST_EMAIL');
$password = Environment::get('PINTEREST_PASSWORD');
$username = Environment::get('PINTEREST_USERNAME');
$chromeProfile = Environment::get('CHROME_PROFILE_PATH');
$pythonPath = Environment::get('PYTHON_PATH');

// Initialize the API
$api = EnhancedPinterestAPI::getInstance($email, $password, $username, $pythonPath, $chromeProfile);

// Handle form submission
$searchResults = null;
$searchQuery = '';
$message = null;
$messageType = 'info';
$collections = [];
$savedPins = [];

// Load saved collections from session
if (isset($_SESSION['pinterest_collections'])) {
    $collections = $_SESSION['pinterest_collections'];
}

// Load saved pins from session
if (isset($_SESSION['pinterest_saved_pins'])) {
    $savedPins = $_SESSION['pinterest_saved_pins'];
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'search':
                // Get search parameters
                $searchQuery = isset($_POST['query']) ? $_POST['query'] : '';
                $limit = isset($_POST['limit']) ? (int)$_POST['limit'] : 20;

                if (empty($searchQuery)) {
                    $message = 'Please enter a search query';
                    $messageType = 'error';
                    break;
                }

                // Perform the search
                $searchResults = $api->searchPins($searchQuery, 'pins', $limit, false);

                if (!is_array($searchResults) || empty($searchResults)) {
                    $message = 'No results found. Please try a different search query.';
                    $messageType = 'error';
                }
                break;

            case 'save_pin':
                // Get pin data
                $pinId = isset($_POST['pin_id']) ? $_POST['pin_id'] : '';
                $pinUrl = isset($_POST['pin_url']) ? $_POST['pin_url'] : '';
                $imageUrl = isset($_POST['image_url']) ? $_POST['image_url'] : '';
                $title = isset($_POST['title']) ? $_POST['title'] : '';
                $description = isset($_POST['description']) ? $_POST['description'] : '';

                if (empty($pinId) || empty($imageUrl)) {
                    $message = 'Invalid pin data';
                    $messageType = 'error';
                    break;
                }

                // Add to saved pins
                $savedPins[$pinId] = [
                    'pin_id' => $pinId,
                    'pin_url' => $pinUrl,
                    'image_url' => $imageUrl,
                    'title' => $title,
                    'description' => $description,
                    'saved_at' => date('Y-m-d H:i:s')
                ];

                // Save to session
                $_SESSION['pinterest_saved_pins'] = $savedPins;

                $message = 'Pin saved successfully';
                $messageType = 'success';
                break;

            case 'remove_pin':
                // Get pin ID
                $pinId = isset($_POST['pin_id']) ? $_POST['pin_id'] : '';

                if (empty($pinId)) {
                    $message = 'Invalid pin ID';
                    $messageType = 'error';
                    break;
                }

                // Remove from saved pins
                if (isset($savedPins[$pinId])) {
                    unset($savedPins[$pinId]);

                    // Save to session
                    $_SESSION['pinterest_saved_pins'] = $savedPins;

                    $message = 'Pin removed successfully';
                    $messageType = 'success';
                } else {
                    $message = 'Pin not found';
                    $messageType = 'error';
                }
                break;

            case 'create_collection':
                // Get collection name
                $collectionName = isset($_POST['collection_name']) ? $_POST['collection_name'] : '';

                if (empty($collectionName)) {
                    $message = 'Please enter a collection name';
                    $messageType = 'error';
                    break;
                }

                // Create collection
                $collectionId = 'collection_' . time();
                $collections[$collectionId] = [
                    'id' => $collectionId,
                    'name' => $collectionName,
                    'pins' => [],
                    'created_at' => date('Y-m-d H:i:s')
                ];

                // Save to session
                $_SESSION['pinterest_collections'] = $collections;

                $message = 'Collection created successfully';
                $messageType = 'success';
                break;

            case 'add_to_collection':
                // Get pin ID and collection ID
                $pinId = isset($_POST['pin_id']) ? $_POST['pin_id'] : '';
                $collectionId = isset($_POST['collection_id']) ? $_POST['collection_id'] : '';

                if (empty($pinId) || empty($collectionId)) {
                    $message = 'Invalid pin or collection';
                    $messageType = 'error';
                    break;
                }

                // Check if pin exists
                if (!isset($savedPins[$pinId])) {
                    $message = 'Pin not found';
                    $messageType = 'error';
                    break;
                }

                // Check if collection exists
                if (!isset($collections[$collectionId])) {
                    $message = 'Collection not found';
                    $messageType = 'error';
                    break;
                }

                // Add pin to collection
                if (!in_array($pinId, $collections[$collectionId]['pins'])) {
                    $collections[$collectionId]['pins'][] = $pinId;

                    // Save to session
                    $_SESSION['pinterest_collections'] = $collections;

                    $message = 'Pin added to collection successfully';
                    $messageType = 'success';
                } else {
                    $message = 'Pin already in collection';
                    $messageType = 'info';
                }
                break;
        }
    }
}

// Page title
$pageTitle = 'Pinterest Explorer';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    <link rel="stylesheet" href="/momentum/css/fontawesome.css">
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Masonry layout for Pinterest-like grid -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/masonry/4.2.2/masonry.pkgd.min.js" integrity="sha512-JRlcvSZAXT8+5SQQAvklXGJuxXTouyq8oIMaYERZQasB8SBDHZaUbeASsJWpk0UUrf89DP3/aefPPrlMR1h1yQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.imagesloaded/5.0.0/imagesloaded.pkgd.min.js" integrity="sha512-kfs3Dt9u9YAkuVjurfVrJxs2IjW5TZO6+ayOIZJ3FOJUQTfC0qbX2K7kHxPnhRZFGpN6yAm/3UaZXjZM5JKLKA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <style>
        /* Pinterest-like styles */
        .pin-grid {
            margin: 0 auto;
        }

        .pin-item {
            width: 236px;
            margin-bottom: 16px;
            break-inside: avoid;
            transition: transform 0.2s ease-in-out;
        }

        .pin-item:hover {
            transform: scale(1.02);
        }

        .pin-image {
            border-radius: 16px;
            width: 100%;
            height: auto;
            object-fit: cover;
        }

        .pin-modal {
            transition: opacity 0.3s ease-in-out;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* Loading animation */
        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left-color: #e60023;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
    <script>
        // Initialize Alpine.js data before the component loads
        document.addEventListener('alpine:init', () => {
            Alpine.store('pinterest', {
                isLoading: false
            });
        });
    </script>

    <div x-data="{
        showPinModal: false,
        currentPin: null,
        showCollectionModal: false,
        showSavedPinsModal: false,
        isLoading: false
    }"
    x-init="$watch('isLoading', value => console.log('Loading state changed:', value))">
        <!-- Navigation Bar -->
        <nav class="bg-white dark:bg-gray-800 shadow-md py-4 px-6 sticky top-0 z-50">
            <div class="container mx-auto flex justify-between items-center">
                <div class="flex items-center">
                    <a href="/momentum/clone/pinterest" class="text-red-600 dark:text-red-400 font-bold text-2xl mr-6">
                        <i class="fab fa-pinterest mr-2"></i> Pinterest Explorer
                    </a>
                </div>

                <div class="flex-1 mx-4">
                    <form method="post" class="w-full" @submit="isLoading = true">
                        <input type="hidden" name="action" value="search">
                        <div class="relative">
                            <input type="text" name="query" value="<?= htmlspecialchars($searchQuery) ?>" placeholder="Search for ideas..." class="w-full px-4 py-2 rounded-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                            <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <div class="flex items-center space-x-4">
                    <button @click="showSavedPinsModal = true" class="text-gray-700 dark:text-gray-300 hover:text-red-500 dark:hover:text-red-400">
                        <i class="fas fa-bookmark mr-1"></i> Saved
                        <span class="bg-red-500 text-white rounded-full px-2 py-1 text-xs"><?= count($savedPins) ?></span>
                    </button>

                    <a href="/momentum/pinterest-research-guide.php" class="text-gray-700 dark:text-gray-300 hover:text-red-500 dark:hover:text-red-400">
                        <i class="fas fa-book mr-1"></i> Guide
                    </a>

                    <a href="/momentum/clone/pinterest" class="text-gray-700 dark:text-gray-300 hover:text-red-500 dark:hover:text-red-400">
                        <i class="fas fa-home mr-1"></i> Dashboard
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container mx-auto px-4 py-6">
            <!-- Message Display -->
            <?php if ($message): ?>
                <div class="mb-6 p-4 rounded-lg <?php
                    if ($messageType === 'success') echo 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200';
                    else if ($messageType === 'error') echo 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200';
                    else echo 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200';
                ?>">
                    <p><?= htmlspecialchars($message) ?></p>
                </div>
            <?php endif; ?>

            <!-- Loading Indicator -->
            <div x-show="isLoading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg text-center">
                    <div class="loading-spinner mx-auto mb-4"></div>
                    <p class="text-lg">Loading pins...</p>
                </div>
            </div>

            <!-- Debug Info (Remove in production) -->
            <div class="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 p-4 rounded mb-4">
                <h3 class="font-bold">Debug Information</h3>
                <p>This is a development version of Pinterest Explorer. If you encounter any issues, please check the browser console for error messages.</p>
            </div>

            <!-- Search Results -->
            <?php if (is_array($searchResults) && !empty($searchResults)): ?>
                <div class="mb-6">
                    <h2 class="text-2xl font-bold mb-4">Results for "<?= htmlspecialchars($searchQuery) ?>"</h2>

                    <!-- Pinterest-like Grid -->
                    <div class="pin-grid" id="pin-masonry-grid">
                        <?php foreach ($searchResults as $pin): ?>
                            <?php if (isset($pin['image_url']) && !empty($pin['image_url'])): ?>
                                <div class="pin-item">
                                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                                        <!-- Pin Image -->
                                        <div class="relative group">
                                            <img
                                                src="<?= htmlspecialchars($pin['image_url']) ?>"
                                                alt="<?= htmlspecialchars($pin['title'] ?? 'Pinterest Pin') ?>"
                                                class="pin-image w-full cursor-zoom-in"
                                                @click="currentPin = <?= htmlspecialchars(json_encode($pin)) ?>; showPinModal = true"
                                                onerror="this.onerror=null; this.src='/momentum/img/placeholder-pin.jpg';"
                                            >

                                            <!-- Hover Actions -->
                                            <div class="absolute inset-0 bg-black bg-opacity-20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-end justify-between p-3">
                                                <div class="flex space-x-2">
                                                    <button type="button"
                                                        class="save-pin-button bg-red-500 hover:bg-red-600 text-white rounded-full px-3 py-1 text-sm font-medium"
                                                        data-pin-id="<?= htmlspecialchars($pin['pin_id']) ?>"
                                                        data-pin-url="<?= htmlspecialchars($pin['pin_url']) ?>"
                                                        data-image-url="<?= htmlspecialchars($pin['image_url']) ?>"
                                                        data-title="<?= htmlspecialchars($pin['title'] ?? '') ?>"
                                                        data-description="<?= htmlspecialchars($pin['description'] ?? '') ?>"
                                                    >
                                                        <i class="fas fa-bookmark mr-1"></i> Save
                                                    </button>

                                                    <button type="button"
                                                        class="download-image-button bg-green-500 hover:bg-green-600 text-white rounded-full px-3 py-1 text-sm font-medium"
                                                        data-image-url="<?= htmlspecialchars($pin['image_url']) ?>"
                                                    >
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                </div>

                                                <a href="<?= htmlspecialchars($pin['pin_url']) ?>" target="_blank" class="bg-gray-800 bg-opacity-70 hover:bg-opacity-90 text-white rounded-full p-2">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                            </div>
                                        </div>

                                        <!-- Pin Info -->
                                        <div class="p-3">
                                            <h3 class="font-medium text-sm line-clamp-2">
                                                <?= htmlspecialchars($pin['title'] ?? 'Untitled Pin') ?>
                                            </h3>
                                            <?php if (isset($pin['save_count']) && $pin['save_count'] > 0): ?>
                                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                    <i class="fas fa-thumbtack mr-1"></i> <?= number_format($pin['save_count']) ?> saves
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php elseif ($searchQuery): ?>
                <div class="text-center py-12">
                    <div class="text-5xl mb-4"><i class="fas fa-search"></i></div>
                    <h2 class="text-2xl font-bold mb-2">No pins found</h2>
                    <p class="text-gray-600 dark:text-gray-400">Try a different search term or check your connection.</p>
                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <div class="text-5xl mb-4"><i class="fas fa-search"></i></div>
                    <h2 class="text-2xl font-bold mb-2">Discover ideas on Pinterest</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">Search for inspiration in home decor, fashion, recipes, and more.</p>

                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-3xl mx-auto">
                        <?php
                        $popularSearches = [
                            'home decor ideas' => 'fas fa-home',
                            'healthy recipes' => 'fas fa-utensils',
                            'fashion outfits' => 'fas fa-tshirt',
                            'diy projects' => 'fas fa-tools',
                            'travel destinations' => 'fas fa-plane',
                            'fitness tips' => 'fas fa-dumbbell',
                            'wedding inspiration' => 'fas fa-ring',
                            'garden ideas' => 'fas fa-leaf'
                        ];

                        foreach ($popularSearches as $search => $icon):
                        ?>
                            <form method="post" class="w-full">
                                <input type="hidden" name="action" value="search">
                                <input type="hidden" name="query" value="<?= htmlspecialchars($search) ?>">
                                <button type="submit" class="w-full bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 p-4 rounded-lg shadow-sm transition-colors">
                                    <i class="<?= $icon ?> text-2xl mb-2 text-red-500"></i>
                                    <p class="text-sm"><?= htmlspecialchars($search) ?></p>
                                </button>
                            </form>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Pin Detail Modal -->
        <div x-show="showPinModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 pin-modal" x-cloak>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden" @click.away="showPinModal = false">
                <div class="flex flex-col md:flex-row h-full">
                    <!-- Pin Image -->
                    <div class="md:w-2/3 bg-gray-100 dark:bg-gray-900 flex items-center justify-center p-4">
                        <img
                            x-bind:src="currentPin?.image_url"
                            x-bind:alt="currentPin?.title || 'Pinterest Pin'"
                            class="max-w-full max-h-[70vh] object-contain rounded-lg"
                            onerror="this.onerror=null; this.src='/momentum/img/placeholder-pin.jpg';"
                        >
                    </div>

                    <!-- Pin Info -->
                    <div class="md:w-1/3 p-6 overflow-y-auto max-h-[70vh]">
                        <div class="flex justify-between items-start mb-4">
                            <h2 class="text-xl font-bold" x-text="currentPin?.title || 'Untitled Pin'"></h2>
                            <button @click="showPinModal = false" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <p class="text-gray-600 dark:text-gray-400 mb-4" x-text="currentPin?.description || 'No description available.'"></p>

                        <div class="flex items-center mb-4">
                            <span class="text-gray-600 dark:text-gray-400 mr-4">
                                <i class="fas fa-thumbtack mr-1"></i> <span x-text="currentPin?.save_count ? currentPin.save_count.toLocaleString() : '0'"></span> saves
                            </span>
                            <span class="text-gray-600 dark:text-gray-400">
                                <i class="fas fa-comment mr-1"></i> <span x-text="currentPin?.comment_count ? currentPin.comment_count.toLocaleString() : '0'"></span> comments
                            </span>
                        </div>

                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mb-4">
                            <h3 class="font-bold mb-2">Board</h3>
                            <p class="text-gray-600 dark:text-gray-400" x-text="currentPin?.board_name || 'Unknown Board'"></p>
                        </div>

                        <div class="flex space-x-2">
                            <button type="button"
                                class="save-pin-button w-full bg-red-500 hover:bg-red-600 text-white rounded-full py-2 px-4 font-medium"
                                x-bind:data-pin-id="currentPin?.pin_id"
                                x-bind:data-pin-url="currentPin?.pin_url"
                                x-bind:data-image-url="currentPin?.image_url"
                                x-bind:data-title="currentPin?.title"
                                x-bind:data-description="currentPin?.description"
                            >
                                <i class="fas fa-bookmark mr-1"></i> Save
                            </button>

                            <button type="button"
                                class="download-image-button bg-green-500 hover:bg-green-600 text-white rounded-full py-2 px-4 font-medium"
                                x-bind:data-image-url="currentPin?.image_url"
                            >
                                <i class="fas fa-download"></i>
                            </button>

                            <a x-bind:href="currentPin?.pin_url" target="_blank" class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-full p-2">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Saved Pins Modal -->
        <div x-show="showSavedPinsModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 pin-modal" x-cloak>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden" @click.away="showSavedPinsModal = false">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold">Saved Pins</h2>
                        <button @click="showSavedPinsModal = false" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <?php if (empty($savedPins)): ?>
                        <div class="text-center py-8">
                            <div class="text-5xl mb-4 text-gray-300 dark:text-gray-600"><i class="fas fa-bookmark"></i></div>
                            <h3 class="text-xl font-bold mb-2">No saved pins yet</h3>
                            <p class="text-gray-600 dark:text-gray-400">Search for pins and save them to see them here.</p>
                        </div>
                    <?php else: ?>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-4 overflow-y-auto max-h-[60vh]">
                            <?php foreach ($savedPins as $pin): ?>
                                <div class="bg-white dark:bg-gray-700 rounded-lg shadow-sm overflow-hidden">
                                    <div class="relative group">
                                        <img
                                            src="<?= htmlspecialchars($pin['image_url']) ?>"
                                            alt="<?= htmlspecialchars($pin['title'] ?? 'Saved Pin') ?>"
                                            class="w-full h-40 object-cover rounded-t-lg cursor-zoom-in"
                                            @click="currentPin = <?= htmlspecialchars(json_encode($pin)) ?>; showSavedPinsModal = false; showPinModal = true"
                                            onerror="this.onerror=null; this.src='/momentum/img/placeholder-pin.jpg';"
                                        >

                                        <div class="absolute inset-0 bg-black bg-opacity-20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-end justify-between p-3">
                                            <form method="post">
                                                <input type="hidden" name="action" value="remove_pin">
                                                <input type="hidden" name="pin_id" value="<?= htmlspecialchars($pin['pin_id']) ?>">

                                                <button type="submit" class="bg-red-500 hover:bg-red-600 text-white rounded-full px-3 py-1 text-sm font-medium">
                                                    <i class="fas fa-trash-alt mr-1"></i> Remove
                                                </button>
                                            </form>

                                            <a href="<?= htmlspecialchars($pin['pin_url']) ?>" target="_blank" class="bg-gray-800 bg-opacity-70 hover:bg-opacity-90 text-white rounded-full p-2">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        </div>
                                    </div>

                                    <div class="p-3">
                                        <h3 class="font-medium text-sm line-clamp-1">
                                            <?= htmlspecialchars($pin['title'] ?? 'Untitled Pin') ?>
                                        </h3>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            Saved on <?= date('M j, Y', strtotime($pin['saved_at'])) ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Create Collection Modal -->
        <div x-show="showCollectionModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 pin-modal" style="display: none;">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full" @click.away="showCollectionModal = false">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold">Create Collection</h2>
                        <button @click="showCollectionModal = false" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form method="post" id="create-collection-form">
                        <input type="hidden" name="action" value="create_collection">

                        <div class="mb-4">
                            <label for="collection_name" class="block mb-2 font-medium">Collection Name:</label>
                            <input type="text" id="collection_name" name="collection_name" class="w-full px-4 py-2 border rounded dark:bg-gray-700 dark:border-gray-600" required>
                        </div>

                        <button type="submit" class="w-full bg-red-500 hover:bg-red-600 text-white rounded-full py-2 px-4 font-medium">
                            <i class="fas fa-plus mr-1"></i> Create Collection
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Create Collection Button -->
        <div class="fixed bottom-6 right-6">
            <button
                @click="showCollectionModal = true"
                class="bg-red-500 hover:bg-red-600 text-white rounded-full p-4 shadow-lg"
            >
                <i class="fas fa-plus text-xl"></i>
            </button>
        </div>
    </div>

    <!-- JavaScript for Pinterest Explorer -->
    <script src="/momentum/js/pinterest-explorer.js"></script>
</body>
</html>