<?php
/**
 * Public Test File
 * 
 * This file is used to test if the server can access files in the public directory.
 */

// Output basic information
echo "<h1>Public Test File</h1>";
echo "<p>This file is accessible at: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>Server Name: " . $_SERVER['SERVER_NAME'] . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Script Filename: " . $_SERVER['SCRIPT_FILENAME'] . "</p>";

// Provide links to test
echo "<h2>Test Links:</h2>";
echo "<ul>";
echo "<li><a href='/momentum/'>Root Directory</a></li>";
echo "<li><a href='/momentum/test_access.php'>Test Access File</a></li>";
echo "<li><a href='/momentum/aegis-director-interface.php'>Aegis Director Interface (Root)</a></li>";
echo "<li><a href='/momentum/public/aegis-director-interface.php'>Aegis Director Interface (Public)</a></li>";
echo "<li><a href='/momentum/create_aegis_director_agent.php'>Create Aegis Director Agent</a></li>";
echo "<li><a href='/momentum/public/aegis-director.php'>Aegis Director Redirect</a></li>";
echo "</ul>";
