<?php
/**
 * Pinterest Image API
 *
 * This API endpoint handles downloading and serving Pinterest images.
 */

require_once __DIR__ . '/../../src/utils/Environment.php';
require_once __DIR__ . '/../../src/utils/Session.php';

// Set content type to JSON
header('Content-Type: application/json');

// Start the session
Session::start();

// Check if the user is logged in
if (!Session::isLoggedIn()) {
    echo json_encode([
        'success' => false,
        'message' => 'You must be logged in to access this API'
    ]);
    exit;
}

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
    exit;
}

// Check if the image URL is provided
if (!isset($_POST['image_url']) || empty($_POST['image_url'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Image URL is required'
    ]);
    exit;
}

// Get the image URL
$imageUrl = $_POST['image_url'];

// Function to convert thumbnail URLs to full-size image URLs
function convertToFullSizeUrl($imageUrl) {
    if (!$imageUrl) {
        return null;
    }
    
    // Already a full-size URL
    if (strpos($imageUrl, '/originals/') !== false) {
        return $imageUrl;
    }
    
    // Handle 75x75_RS format
    if (strpos($imageUrl, '75x75_RS') !== false) {
        // Extract the image ID from the URL
        $parts = explode('/', $imageUrl);
        $filename = end($parts);
        
        // Construct a 736x URL (large image)
        return "https://i.pinimg.com/736x/" . substr($filename, 0, 2) . "/" . substr($filename, 2, 2) . "/" . substr($filename, 4, 2) . "/" . $filename;
    }
    
    // Handle other thumbnail formats (236x, 474x)
    foreach (['75x75', '150x150', '236x', '474x'] as $size) {
        if (strpos($imageUrl, '/' . $size . '/') !== false) {
            return str_replace('/' . $size . '/', '/736x/', $imageUrl);
        }
    }
    
    // If it's already a 736x image, try to get the original
    if (strpos($imageUrl, '/736x/') !== false) {
        try {
            // Sometimes we can get the original by replacing 736x with originals
            $originalUrl = str_replace('/736x/', '/originals/', $imageUrl);
            $headers = get_headers($originalUrl);
            if ($headers && strpos($headers[0], '200') !== false) {
                return $originalUrl;
            }
        } catch (Exception $e) {
            // If that fails, stick with the 736x version
        }
    }
    
    // Default to returning the original URL if we couldn't convert it
    return $imageUrl;
}

// Convert to full-size URL
$fullSizeUrl = convertToFullSizeUrl($imageUrl);

// Create the uploads directory if it doesn't exist
$uploadsDir = __DIR__ . '/../uploads/pinterest';
if (!file_exists($uploadsDir)) {
    mkdir($uploadsDir, 0777, true);
}

// Generate a unique filename
$filename = 'pinterest_' . time() . '_' . md5($fullSizeUrl) . '.jpg';
$outputPath = $uploadsDir . '/' . $filename;

// Try to download the image
$imageData = @file_get_contents($fullSizeUrl);

if ($imageData !== false) {
    // Save the image
    if (file_put_contents($outputPath, $imageData)) {
        // Return success response with the download URL
        echo json_encode([
            'success' => true,
            'message' => 'Image downloaded successfully',
            'download_url' => '/momentum/uploads/pinterest/' . $filename,
            'file_path' => $outputPath
        ]);
        exit;
    }
}

// If direct download fails, try using cURL
$ch = curl_init($fullSizeUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_HEADER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
$imageData = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200 && $imageData) {
    // Save the image
    if (file_put_contents($outputPath, $imageData)) {
        // Return success response with the download URL
        echo json_encode([
            'success' => true,
            'message' => 'Image downloaded successfully (cURL method)',
            'download_url' => '/momentum/uploads/pinterest/' . $filename,
            'file_path' => $outputPath
        ]);
        exit;
    }
}

// If all methods fail, return error
echo json_encode([
    'success' => false,
    'message' => 'Failed to download image after trying all methods',
    'original_url' => $imageUrl,
    'full_size_url' => $fullSizeUrl
]);
