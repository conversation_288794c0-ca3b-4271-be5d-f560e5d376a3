/**
 * AI Prompt Executor JavaScript
 * 
 * Handles prompt execution, variable filling, and AI response management
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('AI Prompt Executor loaded');
    
    // Initialize executor functionality
    initPromptExecutor();
    initVariableForms();
    initExecutionHistory();
    initRatingSystem();
});

/**
 * Initialize the prompt executor interface
 */
function initPromptExecutor() {
    const executeBtn = document.getElementById('executePromptBtn');
    const promptForm = document.getElementById('promptExecutionForm');
    
    if (executeBtn) {
        executeBtn.addEventListener('click', executePrompt);
    }
    
    if (promptForm) {
        promptForm.addEventListener('submit', function(e) {
            e.preventDefault();
            executePrompt();
        });
    }
    
    // Check if we should show execution interface on page load
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('execute') === '1') {
        showExecutionInterface();
    }
}

/**
 * Initialize variable input forms
 */
function initVariableForms() {
    const variableInputs = document.querySelectorAll('.variable-input');
    
    variableInputs.forEach(input => {
        input.addEventListener('input', function() {
            updatePromptPreview();
            validateVariableInput(this);
        });
        
        input.addEventListener('blur', function() {
            saveVariableValue(this);
        });
    });
    
    // Load saved variable values
    loadSavedVariableValues();
}

/**
 * Initialize execution history
 */
function initExecutionHistory() {
    const historyContainer = document.getElementById('executionHistory');
    if (historyContainer) {
        loadExecutionHistory();
    }
    
    // Setup history item interactions
    document.addEventListener('click', function(e) {
        if (e.target.matches('.history-item-toggle')) {
            toggleHistoryItem(e.target);
        }
        
        if (e.target.matches('.copy-response-btn')) {
            copyResponseToClipboard(e.target);
        }
        
        if (e.target.matches('.rerun-prompt-btn')) {
            rerunPromptFromHistory(e.target);
        }
    });
}

/**
 * Initialize rating system
 */
function initRatingSystem() {
    document.addEventListener('click', function(e) {
        if (e.target.matches('.rating-star')) {
            handleRatingClick(e.target);
        }
    });
}

/**
 * Show the execution interface
 */
function showExecutionInterface() {
    const executionPanel = document.getElementById('executionPanel');
    if (executionPanel) {
        executionPanel.classList.remove('hidden');
        executionPanel.scrollIntoView({ behavior: 'smooth' });
    }
}

/**
 * Execute the prompt with current variable values
 */
async function executePrompt() {
    const promptId = getPromptId();
    const variables = collectVariableValues();
    const executeBtn = document.getElementById('executePromptBtn');
    const resultContainer = document.getElementById('executionResult');
    
    if (!promptId) {
        showNotification('Prompt ID not found', 'error');
        return;
    }
    
    // Show loading state
    if (executeBtn) {
        executeBtn.disabled = true;
        executeBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Executing...';
    }
    
    if (resultContainer) {
        resultContainer.innerHTML = `
            <div class="flex items-center justify-center p-8">
                <div class="text-center">
                    <i class="fas fa-brain fa-2x text-purple-500 mb-4 animate-pulse"></i>
                    <p class="text-gray-600 dark:text-gray-400">AI is thinking...</p>
                </div>
            </div>
        `;
        resultContainer.classList.remove('hidden');
    }
    
    try {
        // For now, simulate AI execution since we don't have real AI integration
        const response = await simulateAIExecution(promptId, variables);
        
        displayExecutionResult(response);
        saveExecutionToHistory(response);
        
        showNotification('Prompt executed successfully!', 'success');
        
    } catch (error) {
        console.error('Execution error:', error);
        showNotification('Failed to execute prompt: ' + error.message, 'error');
        
        if (resultContainer) {
            resultContainer.innerHTML = `
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <div class="flex">
                        <i class="fas fa-exclamation-triangle text-red-500 mr-3 mt-1"></i>
                        <div>
                            <h3 class="text-red-800 dark:text-red-200 font-medium">Execution Failed</h3>
                            <p class="text-red-700 dark:text-red-300 text-sm mt-1">${error.message}</p>
                        </div>
                    </div>
                </div>
            `;
        }
    } finally {
        // Reset button state
        if (executeBtn) {
            executeBtn.disabled = false;
            executeBtn.innerHTML = '<i class="fas fa-play mr-2"></i>Execute Prompt';
        }
    }
}

/**
 * Simulate AI execution (placeholder for real AI integration)
 */
async function simulateAIExecution(promptId, variables) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));
    
    // Get the prompt text and fill variables
    const promptText = getFilledPromptText(variables);
    
    // Simulate different types of responses
    const responses = [
        "This is a simulated AI response. In a real implementation, this would be the actual AI-generated content based on your prompt.",
        "Here's a comprehensive response to your prompt:\n\n1. First point of analysis\n2. Second important consideration\n3. Final recommendations\n\nThis demonstrates how the AI would structure its response.",
        "Based on your input, here are some key insights:\n\n• Insight one with detailed explanation\n• Another important point to consider\n• Final thoughts and next steps\n\nWould you like me to elaborate on any of these points?",
    ];
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    return {
        id: Date.now(),
        prompt_id: promptId,
        executed_prompt: promptText,
        response_text: randomResponse,
        ai_provider: 'simulated',
        model_name: 'gpt-4-simulated',
        execution_time_ms: Math.floor(1000 + Math.random() * 4000),
        success: true,
        created_at: new Date().toISOString(),
        tokens_used: Math.floor(100 + Math.random() * 500),
        cost_estimate: (Math.random() * 0.05).toFixed(4)
    };
}

/**
 * Get the current prompt ID
 */
function getPromptId() {
    const urlParts = window.location.pathname.split('/');
    const viewIndex = urlParts.indexOf('view');
    if (viewIndex !== -1 && urlParts[viewIndex + 1]) {
        return urlParts[viewIndex + 1];
    }
    
    const executeIndex = urlParts.indexOf('execute');
    if (executeIndex !== -1 && urlParts[executeIndex + 1]) {
        return urlParts[executeIndex + 1];
    }
    
    return null;
}

/**
 * Collect current variable values
 */
function collectVariableValues() {
    const variables = {};
    const variableInputs = document.querySelectorAll('.variable-input');
    
    variableInputs.forEach(input => {
        const name = input.dataset.variableName;
        const value = input.value || input.dataset.defaultValue || '';
        if (name) {
            variables[name] = value;
        }
    });
    
    return variables;
}

/**
 * Get prompt text with variables filled in
 */
function getFilledPromptText(variables) {
    const promptElement = document.getElementById('promptContent');
    if (!promptElement) return '';
    
    let promptText = promptElement.textContent;
    
    // Replace variables
    Object.keys(variables).forEach(varName => {
        const pattern = new RegExp(`\\{\\{\\s*${varName}\\s*\\}\\}`, 'g');
        promptText = promptText.replace(pattern, variables[varName]);
    });
    
    return promptText;
}

/**
 * Display execution result
 */
function displayExecutionResult(response) {
    const resultContainer = document.getElementById('executionResult');
    if (!resultContainer) return;
    
    const resultHtml = `
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div class="bg-gradient-to-r from-purple-500 to-purple-600 px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-white font-semibold">AI Response</h3>
                    <div class="flex items-center space-x-2 text-purple-100 text-sm">
                        <span><i class="fas fa-clock mr-1"></i>${response.execution_time_ms}ms</span>
                        <span><i class="fas fa-coins mr-1"></i>$${response.cost_estimate}</span>
                    </div>
                </div>
            </div>
            
            <div class="p-6">
                <div class="prose dark:prose-invert max-w-none mb-6">
                    ${formatResponseText(response.response_text)}
                </div>
                
                <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center space-x-4">
                        <button onclick="copyResponseToClipboard(this)" data-response="${escapeHtml(response.response_text)}" class="inline-flex items-center px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                            <i class="fas fa-copy mr-1"></i>Copy
                        </button>
                        
                        <button onclick="shareResponse(this)" data-response="${escapeHtml(response.response_text)}" class="inline-flex items-center px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                            <i class="fas fa-share mr-1"></i>Share
                        </button>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Rate this response:</span>
                        <div class="flex space-x-1">
                            ${[1,2,3,4,5].map(rating => `
                                <button class="rating-star text-gray-300 hover:text-yellow-500 transition-colors" data-rating="${rating}" data-response-id="${response.id}">
                                    <i class="fas fa-star"></i>
                                </button>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    resultContainer.innerHTML = resultHtml;
    resultContainer.classList.remove('hidden');
    
    // Scroll to result
    resultContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

/**
 * Format response text for display
 */
function formatResponseText(text) {
    // Convert line breaks to paragraphs
    const paragraphs = text.split('\n\n').filter(p => p.trim());
    return paragraphs.map(p => `<p>${escapeHtml(p).replace(/\n/g, '<br>')}</p>`).join('');
}

/**
 * Save execution to history
 */
function saveExecutionToHistory(response) {
    const history = getExecutionHistory();
    history.unshift(response);
    
    // Keep only last 50 executions
    if (history.length > 50) {
        history.splice(50);
    }
    
    localStorage.setItem('ai_prompt_execution_history', JSON.stringify(history));
    
    // Update history display if visible
    const historyContainer = document.getElementById('executionHistory');
    if (historyContainer && !historyContainer.classList.contains('hidden')) {
        loadExecutionHistory();
    }
}

/**
 * Get execution history from localStorage
 */
function getExecutionHistory() {
    try {
        const history = localStorage.getItem('ai_prompt_execution_history');
        return history ? JSON.parse(history) : [];
    } catch (e) {
        console.error('Error loading execution history:', e);
        return [];
    }
}

/**
 * Load and display execution history
 */
function loadExecutionHistory() {
    const historyContainer = document.getElementById('executionHistory');
    if (!historyContainer) return;
    
    const history = getExecutionHistory();
    
    if (history.length === 0) {
        historyContainer.innerHTML = `
            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                <i class="fas fa-history text-2xl mb-2"></i>
                <p>No execution history yet</p>
            </div>
        `;
        return;
    }
    
    const historyHtml = history.map(item => `
        <div class="history-item border border-gray-200 dark:border-gray-700 rounded-lg mb-3">
            <div class="p-4 cursor-pointer history-item-toggle" data-item-id="${item.id}">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-clock text-gray-400"></i>
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                            ${new Date(item.created_at).toLocaleString()}
                        </span>
                        <span class="text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                            ${item.execution_time_ms}ms
                        </span>
                    </div>
                    <i class="fas fa-chevron-down text-gray-400 transform transition-transform"></i>
                </div>
            </div>
            
            <div class="history-item-content hidden border-t border-gray-200 dark:border-gray-700 p-4">
                <div class="space-y-3">
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Prompt:</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-2 rounded">${escapeHtml(item.executed_prompt.substring(0, 200))}${item.executed_prompt.length > 200 ? '...' : ''}</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Response:</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">${escapeHtml(item.response_text.substring(0, 300))}${item.response_text.length > 300 ? '...' : ''}</p>
                    </div>
                    
                    <div class="flex space-x-2">
                        <button class="copy-response-btn text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600" data-response="${escapeHtml(item.response_text)}">
                            <i class="fas fa-copy mr-1"></i>Copy
                        </button>
                        <button class="rerun-prompt-btn text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-1 rounded hover:bg-purple-200 dark:hover:bg-purple-900/50" data-prompt="${escapeHtml(item.executed_prompt)}">
                            <i class="fas fa-redo mr-1"></i>Rerun
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    historyContainer.innerHTML = historyHtml;
}

/**
 * Toggle history item expansion
 */
function toggleHistoryItem(button) {
    const historyItem = button.closest('.history-item');
    const content = historyItem.querySelector('.history-item-content');
    const chevron = button.querySelector('.fa-chevron-down');
    
    content.classList.toggle('hidden');
    chevron.classList.toggle('rotate-180');
}

/**
 * Copy response to clipboard
 */
function copyResponseToClipboard(button) {
    const response = button.dataset.response;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(response).then(() => {
            showNotification('Response copied to clipboard!', 'success');
        }).catch(err => {
            console.error('Failed to copy: ', err);
            fallbackCopyTextToClipboard(response);
        });
    } else {
        fallbackCopyTextToClipboard(response);
    }
}

/**
 * Fallback copy method for older browsers
 */
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showNotification('Response copied to clipboard!', 'success');
        } else {
            showNotification('Failed to copy response', 'error');
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        showNotification('Failed to copy response', 'error');
    }
    
    document.body.removeChild(textArea);
}

/**
 * Handle rating click
 */
function handleRatingClick(star) {
    const rating = parseInt(star.dataset.rating);
    const responseId = star.dataset.responseId;
    const stars = star.parentNode.querySelectorAll('.rating-star');
    
    // Update visual state
    stars.forEach((s, index) => {
        if (index < rating) {
            s.classList.remove('text-gray-300');
            s.classList.add('text-yellow-500');
        } else {
            s.classList.remove('text-yellow-500');
            s.classList.add('text-gray-300');
        }
    });
    
    // Save rating (in a real app, this would go to the server)
    console.log(`Rated response ${responseId} with ${rating} stars`);
    showNotification(`Rated ${rating} star${rating !== 1 ? 's' : ''}!`, 'success');
}

/**
 * Update prompt preview with current variable values
 */
function updatePromptPreview() {
    const previewElement = document.getElementById('promptPreview');
    if (!previewElement) return;
    
    const variables = collectVariableValues();
    const filledPrompt = getFilledPromptText(variables);
    
    previewElement.textContent = filledPrompt;
}

/**
 * Validate variable input
 */
function validateVariableInput(input) {
    const type = input.dataset.variableType;
    const value = input.value;
    
    // Remove previous validation classes
    input.classList.remove('border-red-500', 'border-green-500');
    
    if (type === 'number' && value && isNaN(value)) {
        input.classList.add('border-red-500');
        return false;
    }
    
    if (value) {
        input.classList.add('border-green-500');
    }
    
    return true;
}

/**
 * Save variable value to localStorage
 */
function saveVariableValue(input) {
    const name = input.dataset.variableName;
    const value = input.value;
    
    if (name) {
        localStorage.setItem(`ai_prompt_var_${name}`, value);
    }
}

/**
 * Load saved variable values from localStorage
 */
function loadSavedVariableValues() {
    const variableInputs = document.querySelectorAll('.variable-input');
    
    variableInputs.forEach(input => {
        const name = input.dataset.variableName;
        if (name) {
            const savedValue = localStorage.getItem(`ai_prompt_var_${name}`);
            if (savedValue) {
                input.value = savedValue;
            }
        }
    });
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white max-w-sm ${
        type === 'error' ? 'bg-red-500' : 
        type === 'success' ? 'bg-green-500' : 
        'bg-blue-500'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
