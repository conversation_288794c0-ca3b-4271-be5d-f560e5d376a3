/**
 * Task Manager Module
 *
 * A consolidated module that handles all task-related functionality.
 * This replaces multiple redundant scripts:
 * - task-list-manager.js
 * - direct-task-fix.js
 * - task-checkbox-fix.js
 * - task-list-enhancements.js
 */

const TaskManager = (function() {
    // Private variables
    let todayTasksWidget = null;
    let overdueTasksWidget = null;
    let todayTasksList = null;
    let overdueTasksList = null;
    let todayTasksContainer = null;
    let overdueTasksContainer = null;
    let initialized = false;

    // Cache for task data
    let cachedTaskRows = [];
    let cachedGroups = {};
    let lastGroupBy = '';

    /**
     * Initialize the module
     */
    function init() {
        if (initialized) return;

        console.log('Initializing Task Manager Module');

        // Get task widgets and lists
        todayTasksWidget = document.querySelector('[data-widget="today-tasks"]');
        overdueTasksWidget = document.querySelector('[data-widget="overdue-tasks"]');
        todayTasksList = document.getElementById('today-tasks-list');
        overdueTasksList = document.getElementById('overdue-tasks-list');
        todayTasksContainer = document.getElementById('today-tasks-container');
        overdueTasksContainer = document.getElementById('overdue-tasks-container');

        // Check if elements exist
        if (!todayTasksWidget && !overdueTasksWidget) {
            console.warn('Task widgets not found, may not be on dashboard page');
            return;
        }

        // Apply direct styling
        applyDirectTaskStyling();

        // Add expand buttons
        setTimeout(addExpandButtons, 300);

        // Check for overflow
        if (todayTasksList && todayTasksContainer) {
            checkOverflow(todayTasksList, todayTasksContainer);
        }

        if (overdueTasksList && overdueTasksContainer) {
            checkOverflow(overdueTasksList, overdueTasksContainer);
        }

        // Add scroll hints
        setTimeout(addScrollHints, 500);

        // Setup keyboard navigation
        if (todayTasksList) {
            setupKeyboardNavigation(todayTasksList);
        }

        if (overdueTasksList) {
            setupKeyboardNavigation(overdueTasksList);
        }

        // Apply ADHD-friendly focus highlighting
        applyTaskItemHighlighting();

        // Setup event listeners
        setupEventListeners();

        // Cache task rows if on task list page
        if (document.querySelector('.task-table')) {
            cacheTaskRows();
        }

        initialized = true;
    }

    /**
     * Setup event listeners
     */
    function setupEventListeners() {
        // Check for overflow when window is resized
        window.addEventListener('resize', function() {
            if (todayTasksList && todayTasksContainer) {
                checkOverflow(todayTasksList, todayTasksContainer);
            }

            if (overdueTasksList && overdueTasksContainer) {
                checkOverflow(overdueTasksList, overdueTasksContainer);
            }
        });

        // Listen for layout changes
        document.addEventListener('layout-applied', function(e) {
            console.log('Layout change detected, updating task widgets');
            applyDirectTaskStyling();

            // Re-check for overflow
            if (todayTasksList && todayTasksContainer) {
                checkOverflow(todayTasksList, todayTasksContainer);
            }

            if (overdueTasksList && overdueTasksContainer) {
                checkOverflow(overdueTasksList, overdueTasksContainer);
            }
        });
    }

    /**
     * Apply direct styling to task widgets
     */
    function applyDirectTaskStyling() {
        console.log('Applying direct task styling');

        // Today's Tasks Widget
        if (todayTasksWidget) {
            todayTasksWidget.style.height = '400px';
            todayTasksWidget.style.maxHeight = '400px';
            todayTasksWidget.style.overflow = 'hidden';
            todayTasksWidget.style.display = 'flex';
            todayTasksWidget.style.flexDirection = 'column';
        }

        if (todayTasksList) {
            todayTasksList.style.maxHeight = '300px';
            todayTasksList.style.overflowY = 'auto';
            todayTasksList.style.scrollbarWidth = 'thin';
        }

        // Overdue Tasks Widget
        if (overdueTasksWidget) {
            overdueTasksWidget.style.height = '400px';
            overdueTasksWidget.style.maxHeight = '400px';
            overdueTasksWidget.style.overflow = 'hidden';
            overdueTasksWidget.style.display = 'flex';
            overdueTasksWidget.style.flexDirection = 'column';
        }

        if (overdueTasksList) {
            overdueTasksList.style.maxHeight = '300px';
            overdueTasksList.style.overflowY = 'auto';
            overdueTasksList.style.scrollbarWidth = 'thin';
        }

        // Apply styles to task list containers
        document.querySelectorAll('.task-list-container').forEach(container => {
            container.style.flex = '1';
            container.style.display = 'flex';
            container.style.flexDirection = 'column';
            container.style.overflow = 'hidden';
            container.style.position = 'relative';
        });
    }

    /**
     * Add expand buttons to task lists
     */
    function addExpandButtons() {
        console.log('Adding expand buttons to task lists');

        // Today's Tasks
        if (todayTasksWidget && todayTasksList) {
            // Check if list has enough content to need scrolling
            if (todayTasksList.scrollHeight > todayTasksList.clientHeight) {
                // Create expand button if it doesn't exist
                if (!document.querySelector('.task-list-expand-button[data-target="today-tasks"]')) {
                    const expandButton = document.createElement('button');
                    expandButton.type = 'button';
                    expandButton.className = 'task-list-expand-button';
                    expandButton.setAttribute('data-target', 'today-tasks');
                    expandButton.innerHTML = '<i class="fas fa-chevron-down mr-1"></i> Show more';

                    // Add click handler
                    expandButton.onclick = function() {
                        toggleTaskWidgetExpansion('today-tasks');
                    };

                    // Add button to widget
                    const container = todayTasksList.parentNode;
                    container.appendChild(expandButton);
                }
            }
        }

        // Overdue Tasks
        if (overdueTasksWidget && overdueTasksList) {
            // Check if list has enough content to need scrolling
            if (overdueTasksList.scrollHeight > overdueTasksList.clientHeight) {
                // Create expand button if it doesn't exist
                if (!document.querySelector('.task-list-expand-button[data-target="overdue-tasks"]')) {
                    const expandButton = document.createElement('button');
                    expandButton.type = 'button';
                    expandButton.className = 'task-list-expand-button';
                    expandButton.setAttribute('data-target', 'overdue-tasks');
                    expandButton.innerHTML = '<i class="fas fa-chevron-down mr-1"></i> Show more';

                    // Add click handler
                    expandButton.onclick = function() {
                        toggleTaskWidgetExpansion('overdue-tasks');
                    };

                    // Add button to widget
                    const container = overdueTasksList.parentNode;
                    container.appendChild(expandButton);
                }
            }
        }
    }

    /**
     * Toggle task widget expansion
     */
    function toggleTaskWidgetExpansion(widgetType) {
        console.log('Toggling expansion for', widgetType);

        const widget = document.querySelector(`[data-widget="${widgetType}"]`);
        const expandButton = document.querySelector(`.task-list-expand-button[data-target="${widgetType}"]`);

        if (!widget || !expandButton) return;

        // Toggle expanded class
        if (widget.classList.contains('expanded')) {
            // Collapse
            widget.classList.remove('expanded');
            expandButton.innerHTML = '<i class="fas fa-chevron-down mr-1"></i> Show more';

            // Scroll to top of widget with smooth animation
            widget.scrollIntoView({ behavior: 'smooth', block: 'start' });
        } else {
            // Expand
            widget.classList.add('expanded');
            expandButton.innerHTML = '<i class="fas fa-chevron-up mr-1"></i> Show less';
        }
    }

    /**
     * Check if a list has overflow
     */
    function checkOverflow(list, container) {
        if (!list || !container) return false;

        // Check if the list's scroll height is greater than its client height
        const hasOverflow = list.scrollHeight > list.clientHeight;

        // Add or remove the has-overflow class
        if (hasOverflow) {
            container.classList.add('has-overflow');
        } else {
            container.classList.remove('has-overflow');
        }

        return hasOverflow;
    }

    /**
     * Add visual cues for scrollable content
     */
    function addScrollHints() {
        // Today's tasks scroll hint
        if (todayTasksList && todayTasksList.scrollHeight > todayTasksList.clientHeight) {
            // Add subtle animation to indicate scrollability
            todayTasksContainer.classList.add('has-overflow');

            // Add scroll event listener to show/hide gradient
            todayTasksList.addEventListener('scroll', function() {
                const isAtBottom = this.scrollHeight - this.scrollTop - this.clientHeight < 10;

                if (isAtBottom) {
                    todayTasksContainer.classList.remove('has-more-content');
                } else {
                    todayTasksContainer.classList.add('has-more-content');
                }
            });

            // Trigger scroll event to initialize state
            todayTasksList.dispatchEvent(new Event('scroll'));
        }

        // Overdue tasks scroll hint
        if (overdueTasksList && overdueTasksList.scrollHeight > overdueTasksList.clientHeight) {
            // Add subtle animation to indicate scrollability
            overdueTasksContainer.classList.add('has-overflow');

            // Add scroll event listener to show/hide gradient
            overdueTasksList.addEventListener('scroll', function() {
                const isAtBottom = this.scrollHeight - this.scrollTop - this.clientHeight < 10;

                if (isAtBottom) {
                    overdueTasksContainer.classList.remove('has-more-content');
                } else {
                    overdueTasksContainer.classList.add('has-more-content');
                }
            });

            // Trigger scroll event to initialize state
            overdueTasksList.dispatchEvent(new Event('scroll'));
        }
    }

    /**
     * Apply ADHD-friendly focus highlighting for task items
     */
    function applyTaskItemHighlighting() {
        document.querySelectorAll('.task-item').forEach(item => {
            // Remove any existing blue highlight classes
            item.classList.remove('bg-primary-50', 'dark:bg-primary-900', 'border-l-4', 'border-primary-500');

            // Add hover effect
            item.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(243, 244, 246, 0.2)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });

            // Add focus effect for keyboard navigation
            item.addEventListener('focus', function() {
                this.style.outline = '2px solid rgba(59, 130, 246, 0.5)';
                this.style.backgroundColor = 'rgba(243, 244, 246, 0.2)';
            });

            item.addEventListener('blur', function() {
                this.style.outline = '';
                this.style.backgroundColor = '';
            });
        });
    }

    /**
     * Add keyboard navigation for task lists
     */
    function setupKeyboardNavigation(list) {
        if (!list) return;

        list.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                e.preventDefault();

                const items = Array.from(list.querySelectorAll('.task-item'));
                const currentIndex = items.findIndex(item => item === document.activeElement);

                if (currentIndex >= 0) {
                    let nextIndex;

                    if (e.key === 'ArrowDown') {
                        nextIndex = Math.min(currentIndex + 1, items.length - 1);
                    } else {
                        nextIndex = Math.max(currentIndex - 1, 0);
                    }

                    items[nextIndex].focus();

                    // Ensure the focused item is visible
                    items[nextIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }
            }
        });
    }

    /**
     * Cache task rows for better performance
     */
    function cacheTaskRows() {
        const taskTable = document.querySelector('.task-table');
        if (!taskTable) return;

        const rows = Array.from(taskTable.querySelectorAll('tbody tr.task-row'));
        if (rows.length === 0) return;

        cachedTaskRows = rows.map(row => ({
            element: row,
            status: row.getAttribute('data-status') || 'No Status',
            priority: row.getAttribute('data-priority') || 'No Priority',
            category: row.getAttribute('data-category') || 'No Category',
            categoryColor: row.getAttribute('data-category-color') || '#6b7280',
            dueDate: row.getAttribute('data-due-date') || '',
            taskId: row.getAttribute('data-task-id') || ''
        }));
    }

    // Return public API
    return {
        init: init,
        toggleTaskWidgetExpansion: toggleTaskWidgetExpansion
    };
})();

// Initialize on DOM content loaded
document.addEventListener('DOMContentLoaded', TaskManager.init);

// Make available globally
window.TaskManager = TaskManager;
