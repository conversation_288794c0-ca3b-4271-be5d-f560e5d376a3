// Extremely simple dropdown fix
window.onload = function() {
    // ADHD dropdown
    var adhdButton = document.getElementById('adhd-dropdown-button');
    var adhdMenu = document.getElementById('adhd-dropdown-menu');

    if (adhdButton && adhdMenu) {
        // Hide the menu initially
        adhdMenu.style.display = 'none';

        // Add click handler
        adhdButton.onclick = function(e) {
            e.preventDefault();

            // Toggle menu visibility
            if (adhdMenu.style.display === 'block') {
                adhdMenu.style.display = 'none';
            } else {
                // Hide all other menus first
                hideAllMenus();

                // Show this menu
                adhdMenu.style.display = 'block';
            }

            return false;
        };
    }

    // Productivity dropdown
    var prodButton = document.getElementById('productivity-dropdown-button');
    var prodMenu = document.getElementById('productivity-dropdown-menu');

    if (prodButton && prodMenu) {
        // Hide the menu initially
        prodMenu.style.display = 'none';

        // Add click handler
        prodButton.onclick = function(e) {
            e.preventDefault();

            // Toggle menu visibility
            if (prodMenu.style.display === 'block') {
                prodMenu.style.display = 'none';
            } else {
                // Hide all other menus first
                hideAllMenus();

                // Show this menu
                prodMenu.style.display = 'block';
            }

            return false;
        };
    }

    // Tools dropdown
    var toolsButton = document.getElementById('tools-dropdown-button');
    var toolsMenu = document.getElementById('tools-dropdown-menu');

    if (toolsButton && toolsMenu) {
        // Hide the menu initially
        toolsMenu.style.display = 'none';

        // Add click handler
        toolsButton.onclick = function(e) {
            e.preventDefault();

            // Toggle menu visibility
            if (toolsMenu.style.display === 'block') {
                toolsMenu.style.display = 'none';
            } else {
                // Hide all other menus first
                hideAllMenus();

                // Show this menu
                toolsMenu.style.display = 'block';
            }

            return false;
        };
    }

    // User dropdown
    var userButton = document.getElementById('user-menu-button');
    var userMenu = document.getElementById('user-dropdown-menu');

    if (userButton && userMenu) {
        // Hide the menu initially
        userMenu.style.display = 'none';

        // Add click handler
        userButton.onclick = function(e) {
            e.preventDefault();

            // Toggle menu visibility
            if (userMenu.style.display === 'block') {
                userMenu.style.display = 'none';
            } else {
                // Hide all other menus first
                hideAllMenus();

                // Show this menu
                userMenu.style.display = 'block';
            }

            return false;
        };
    }

    // Function to hide all menus
    function hideAllMenus() {
        if (adhdMenu) adhdMenu.style.display = 'none';
        if (prodMenu) prodMenu.style.display = 'none';
        if (toolsMenu) toolsMenu.style.display = 'none';
        if (userMenu) userMenu.style.display = 'none';
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        // If click is not on a dropdown button or menu, hide all menus
        if (e.target !== adhdButton && e.target !== prodButton && e.target !== toolsButton && e.target !== userButton &&
            (!adhdMenu || !adhdMenu.contains(e.target)) &&
            (!prodMenu || !prodMenu.contains(e.target)) &&
            (!toolsMenu || !toolsMenu.contains(e.target)) &&
            (!userMenu || !userMenu.contains(e.target))) {
            hideAllMenus();
        }
    });

    // Close dropdowns when pressing Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideAllMenus();
        }
    });
};
