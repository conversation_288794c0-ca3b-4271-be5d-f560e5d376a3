/**
 * Pinterest Pin Redirect Fix
 *
 * This script fixes the issue with Pinterest pin redirects by ensuring
 * direct navigation to Pinterest pins without going through any intermediary services.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Pinterest Pin Redirect Fix loaded');

    // Fix all Pinterest pin links
    fixPinterestPinLinks();

    // Add a mutation observer to fix any dynamically added links
    setupMutationObserver();

    /**
     * Fix all Pinterest pin links on the page
     */
    function fixPinterestPinLinks() {
        // Find all links that point to Pinterest pins
        const pinterestLinks = document.querySelectorAll('a[href*="pinterest.com/pin/"]');
        
        console.log('Found Pinterest pin links:', pinterestLinks.length);
        
        pinterestLinks.forEach(link => {
            // Skip already fixed links
            if (link.getAttribute('data-redirect-fixed') === 'true') {
                return;
            }
            
            // Get the original href
            const originalHref = link.getAttribute('href');
            console.log('Original Pinterest link:', originalHref);
            
            // Extract the pin ID from the URL
            const pinIdMatch = originalHref.match(/\/pin\/(\d+)/);
            if (pinIdMatch && pinIdMatch[1]) {
                const pinId = pinIdMatch[1];
                
                // Create a direct Pinterest URL
                const directUrl = `https://www.pinterest.com/pin/${pinId}/`;
                console.log('Direct Pinterest URL:', directUrl);
                
                // Update the href attribute
                link.setAttribute('href', directUrl);
                
                // Clone the link to remove any existing event listeners
                const newLink = link.cloneNode(true);
                link.parentNode.replaceChild(newLink, link);
                
                // Mark as fixed
                newLink.setAttribute('data-redirect-fixed', 'true');
                
                // Add a direct click handler
                newLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    console.log('Pinterest pin link clicked, opening:', directUrl);
                    
                    // Open in a new tab
                    window.open(directUrl, '_blank');
                    
                    return false;
                });
            }
        });
    }

    /**
     * Set up a mutation observer to fix dynamically added links
     */
    function setupMutationObserver() {
        // Create a mutation observer to watch for dynamically added links
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    // Check if we need to fix any Pinterest links
                    setTimeout(fixPinterestPinLinks, 100);
                }
            });
        });
        
        // Start observing the document with the configured parameters
        observer.observe(document.body, { childList: true, subtree: true });
        console.log('Mutation observer set up for Pinterest links');
    }
});
