/**
 * Aegis Director <PERSON>
 *
 * Provides client-side functionality for the Aegis Director agent interface
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Aegis Director JS loaded');

    // Initialize components
    initChatInterface();
    initTaskManagement();
    initFocusTimer();
    initProgressTracking();
    initProjectManagement();

    // Set up event listeners
    setupEventListeners();
});

/**
 * Initialize the chat interface
 */
function initChatInterface() {
    // Scroll chat to bottom
    const chatContainer = document.getElementById('chat-container');
    if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    // Add quick response buttons if they exist
    const quickResponseButtons = document.querySelectorAll('.quick-response-btn');
    quickResponseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const responseText = this.getAttribute('data-response');
            const inputField = document.querySelector('input[name="interaction_content"]');

            if (inputField) {
                inputField.value = responseText;
                // Focus the input field
                inputField.focus();
            }
        });
    });
}

/**
 * Initialize task management functionality
 */
function initTaskManagement() {
    // Task status toggle
    const taskStatusToggles = document.querySelectorAll('.task-status-toggle');
    taskStatusToggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const taskId = this.getAttribute('data-task-id');
            const newStatus = this.value;

            // Update task status via AJAX
            updateTaskStatus(taskId, newStatus);
        });
    });

    // Task priority indicators
    const taskCards = document.querySelectorAll('.task-card');
    taskCards.forEach(card => {
        const priority = card.getAttribute('data-priority');
        if (priority) {
            let borderColor = '#10b981'; // Default: low (green)

            if (priority === 'high') {
                borderColor = '#ef4444'; // Red
            } else if (priority === 'medium') {
                borderColor = '#f59e0b'; // Amber
            } else if (priority === 'urgent') {
                borderColor = '#7c3aed'; // Purple
            }

            card.style.borderLeftColor = borderColor;
            card.style.borderLeftWidth = '4px';
        }
    });
}

/**
 * Update task status via AJAX
 */
function updateTaskStatus(taskId, newStatus) {
    fetch('/momentum/ai-agents/tasks/update-status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            task_id: taskId,
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI to reflect the new status
            const taskCard = document.querySelector(`.task-card[data-task-id="${taskId}"]`);
            if (taskCard) {
                // Remove all status classes
                taskCard.classList.remove('status-pending', 'status-in_progress', 'status-completed', 'status-failed');
                // Add the new status class
                taskCard.classList.add(`status-${newStatus}`);

                // Update the status text
                const statusText = taskCard.querySelector('.task-status-text');
                if (statusText) {
                    statusText.textContent = newStatus.replace('_', ' ');

                    // Update text color based on status
                    statusText.classList.remove('text-gray-600', 'text-blue-600', 'text-green-600', 'text-red-600');

                    switch (newStatus) {
                        case 'pending':
                            statusText.classList.add('text-gray-600');
                            break;
                        case 'in_progress':
                            statusText.classList.add('text-blue-600');
                            break;
                        case 'completed':
                            statusText.classList.add('text-green-600');
                            break;
                        case 'failed':
                            statusText.classList.add('text-red-600');
                            break;
                    }
                }
            }

            // Show success message
            showNotification('Task status updated successfully', 'success');
        } else {
            // Show error message
            showNotification('Failed to update task status', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating task status:', error);
        showNotification('An error occurred while updating task status', 'error');
    });
}

/**
 * Initialize focus timer functionality
 */
function initFocusTimer() {
    const timerContainer = document.getElementById('focus-timer-container');
    if (!timerContainer) return;

    const startTimerBtn = document.getElementById('start-timer-btn');
    const pauseTimerBtn = document.getElementById('pause-timer-btn');
    const resetTimerBtn = document.getElementById('reset-timer-btn');
    const timerDisplay = document.getElementById('timer-display');

    let timerInterval;
    let timerRunning = false;
    let timeLeft = 25 * 60; // 25 minutes in seconds (Pomodoro)

    // Update timer display
    function updateTimerDisplay() {
        const minutes = Math.floor(timeLeft / 60);
        const seconds = timeLeft % 60;
        timerDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    // Start timer
    if (startTimerBtn) {
        startTimerBtn.addEventListener('click', function() {
            if (!timerRunning) {
                timerRunning = true;
                timerInterval = setInterval(function() {
                    timeLeft--;
                    updateTimerDisplay();

                    if (timeLeft <= 0) {
                        clearInterval(timerInterval);
                        timerRunning = false;
                        showNotification('Focus session completed!', 'success');

                        // Play sound if available
                        const timerSound = document.getElementById('timer-sound');
                        if (timerSound) {
                            timerSound.play();
                        }
                    }
                }, 1000);

                // Update button states
                startTimerBtn.disabled = true;
                if (pauseTimerBtn) pauseTimerBtn.disabled = false;
                if (resetTimerBtn) resetTimerBtn.disabled = false;
            }
        });
    }

    // Pause timer
    if (pauseTimerBtn) {
        pauseTimerBtn.addEventListener('click', function() {
            if (timerRunning) {
                clearInterval(timerInterval);
                timerRunning = false;

                // Update button states
                startTimerBtn.disabled = false;
                pauseTimerBtn.disabled = true;
            }
        });
    }

    // Reset timer
    if (resetTimerBtn) {
        resetTimerBtn.addEventListener('click', function() {
            clearInterval(timerInterval);
            timerRunning = false;
            timeLeft = 25 * 60;
            updateTimerDisplay();

            // Update button states
            startTimerBtn.disabled = false;
            if (pauseTimerBtn) pauseTimerBtn.disabled = true;
            if (resetTimerBtn) resetTimerBtn.disabled = true;
        });
    }

    // Initialize timer display
    updateTimerDisplay();
}

/**
 * Initialize progress tracking
 */
function initProgressTracking() {
    const progressCharts = document.querySelectorAll('.progress-chart');

    progressCharts.forEach(chart => {
        // This would typically use a charting library like Chart.js
        // For now, we'll just update the progress bars
        const progressBar = chart.querySelector('.progress-bar');
        const progressValue = chart.getAttribute('data-progress');

        if (progressBar && progressValue) {
            progressBar.style.width = `${progressValue}%`;
        }
    });
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
    // Form submission
    const interactionForm = document.querySelector('form');
    if (interactionForm) {
        interactionForm.addEventListener('submit', function() {
            // Disable the submit button to prevent double submission
            const submitButton = this.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
            }
        });
    }

    // Strategic pause protocol
    const strategicPauseBtn = document.getElementById('strategic-pause-btn');
    if (strategicPauseBtn) {
        strategicPauseBtn.addEventListener('click', function() {
            // Show the strategic pause modal
            const strategicPauseModal = document.getElementById('strategic-pause-modal');
            if (strategicPauseModal) {
                strategicPauseModal.classList.remove('hidden');
            }
        });
    }

    // Close modal buttons
    const closeModalBtns = document.querySelectorAll('.close-modal-btn');
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const modalId = this.getAttribute('data-modal-id');
            const modal = document.getElementById(modalId);

            if (modal) {
                modal.classList.add('hidden');
            }
        });
    });
}

/**
 * Show a notification
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'fixed bottom-4 right-4 px-6 py-3 rounded-lg shadow-lg transform transition-all duration-500 ease-in-out';

    // Set background color based on type
    switch (type) {
        case 'success':
            notification.classList.add('bg-green-500', 'text-white');
            break;
        case 'error':
            notification.classList.add('bg-red-500', 'text-white');
            break;
        case 'warning':
            notification.classList.add('bg-yellow-500', 'text-white');
            break;
        default:
            notification.classList.add('bg-blue-500', 'text-white');
    }

    // Set content
    notification.textContent = message;

    // Add to document
    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('opacity-0', 'translate-y-2');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 500);
    }, 3000);
}

/**
 * Initialize project management functionality
 */
function initProjectManagement() {
    // Project creation form handling
    const createProjectForm = document.getElementById('create-project-form');
    if (createProjectForm) {
        createProjectForm.addEventListener('submit', function(e) {
            const projectName = document.getElementById('project-name');
            const projectDescription = document.getElementById('project-description');

            if (!projectName || !projectName.value.trim()) {
                e.preventDefault();
                showNotification('Please enter a project name', 'error');
                return false;
            }

            // Disable submit button to prevent double submission
            const submitButton = this.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Creating...';
            }
        });
    }

    // Project type selection handling
    const projectTypeSelect = document.getElementById('project-type');
    const brigadeTypeContainer = document.getElementById('brigade-type-container');

    if (projectTypeSelect && brigadeTypeContainer) {
        projectTypeSelect.addEventListener('change', function() {
            if (this.value === 'agent_army') {
                brigadeTypeContainer.classList.remove('hidden');
            } else {
                brigadeTypeContainer.classList.add('hidden');
            }
        });
    }

    // 24-hour plan creation button
    const createPlanButton = document.getElementById('create-plan-button');
    if (createPlanButton) {
        createPlanButton.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Creating Plan...';

            // Submit the form
            const planForm = document.getElementById('create-plan-form');
            if (planForm) {
                planForm.submit();
            }
        });
    }

    // Project modal handling
    const openProjectModalButton = document.getElementById('open-project-modal');
    const closeProjectModalButton = document.getElementById('close-project-modal');
    const projectModal = document.getElementById('project-modal');

    if (openProjectModalButton && projectModal) {
        openProjectModalButton.addEventListener('click', function() {
            projectModal.classList.remove('hidden');
        });
    }

    if (closeProjectModalButton && projectModal) {
        closeProjectModalButton.addEventListener('click', function() {
            projectModal.classList.add('hidden');
        });
    }
}
