/**
 * All Dropdowns Fix
 *
 * This script handles all dropdown menus in the application in a simple, direct way.
 * It works with ADHD, Productivity, Tools, and User dropdowns.
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('All Dropdowns Fix script loaded');

    // Force a small delay to ensure all elements are properly loaded
    setTimeout(function() {
    // Get all dropdown buttons and menus
    const dropdowns = [
        {
            button: document.getElementById('adhd-dropdown-button'),
            menu: document.getElementById('adhd-dropdown-menu')
        },
        {
            button: document.getElementById('productivity-dropdown-button'),
            menu: document.getElementById('productivity-dropdown-menu')
        },
        {
            button: document.getElementById('tools-dropdown-button'),
            menu: document.getElementById('tools-dropdown-menu')
        },
        {
            button: document.getElementById('user-menu-button'),
            menu: document.getElementById('user-dropdown-menu')
        }
    ];

    // Track the currently active dropdown
    let activeDropdown = null;

    // Create a single overlay for all dropdowns
    const overlay = document.createElement('div');
    overlay.id = 'global-dropdown-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'transparent';
    overlay.style.zIndex = '99998';
    overlay.style.display = 'none';
    document.body.appendChild(overlay);

    // Function to position a dropdown menu
    function positionMenu(button, menu) {
        const rect = button.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Set basic styles
        menu.style.position = 'fixed';
        menu.style.zIndex = '99999';
        menu.style.backgroundColor = document.documentElement.classList.contains('dark') ? '#374151' : 'white';
        menu.style.borderRadius = '0.375rem';
        menu.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
        menu.style.minWidth = '14rem';
        menu.style.maxHeight = '400px';
        menu.style.overflowY = 'auto';
        menu.style.padding = '0.5rem 0';

        // Position the menu
        menu.style.top = (rect.bottom + 5) + 'px';
        menu.style.left = rect.left + 'px';

        // Handle horizontal positioning
        const menuRect = menu.getBoundingClientRect();
        if (menuRect.right > viewportWidth) {
            menu.style.left = 'auto';
            menu.style.right = (viewportWidth - rect.right) + 'px';
        }

        // Handle vertical positioning
        if (menuRect.bottom > viewportHeight) {
            const spaceBelow = viewportHeight - rect.bottom - 10;
            menu.style.maxHeight = spaceBelow + 'px';
        }
    }

    // Function to show a dropdown menu
    function showMenu(dropdown) {
        // Hide any active dropdown first
        if (activeDropdown) {
            hideMenu(activeDropdown);
        }

        // Position the menu
        positionMenu(dropdown.button, dropdown.menu);

        // Show the menu
        dropdown.menu.style.display = 'block';
        dropdown.menu.style.visibility = 'visible';
        dropdown.menu.style.opacity = '1';

        // Show the overlay
        overlay.style.display = 'block';

        // Update ARIA attributes
        dropdown.button.setAttribute('aria-expanded', 'true');

        // Set as active dropdown
        activeDropdown = dropdown;
    }

    // Function to hide a dropdown menu
    function hideMenu(dropdown) {
        dropdown.menu.style.display = 'none';
        dropdown.menu.style.visibility = 'hidden';
        dropdown.menu.style.opacity = '0';

        // Update ARIA attributes
        dropdown.button.setAttribute('aria-expanded', 'false');

        // Clear active dropdown if this is the active one
        if (activeDropdown === dropdown) {
            activeDropdown = null;

            // Hide the overlay
            overlay.style.display = 'none';
        }
    }

    // Function to hide all dropdown menus
    function hideAllMenus() {
        dropdowns.forEach(function(dropdown) {
            if (dropdown.button && dropdown.menu) {
                hideMenu(dropdown);
            }
        });

        // Hide the overlay
        overlay.style.display = 'none';

        // Clear active dropdown
        activeDropdown = null;
    }

    // Set up each dropdown
    dropdowns.forEach(function(dropdown) {
        if (dropdown.button && dropdown.menu) {
            // Set initial state
            dropdown.menu.style.display = 'none';
            dropdown.menu.style.visibility = 'hidden';
            dropdown.menu.style.opacity = '0';
            dropdown.button.setAttribute('aria-expanded', 'false');

            // Style menu items
            const menuItems = dropdown.menu.querySelectorAll('a');
            menuItems.forEach(function(item) {
                item.style.display = 'block';
                item.style.padding = '0.75rem 1rem';
                item.style.color = document.documentElement.classList.contains('dark') ? '#E5E7EB' : '#4B5563';
                item.style.fontSize = '0.875rem';
                item.style.textDecoration = 'none';
                item.style.whiteSpace = 'nowrap';
                item.style.width = '100%';
                item.style.textOverflow = 'ellipsis';
                item.style.overflow = 'hidden';
                item.style.cursor = 'pointer';
                item.style.borderBottom = document.documentElement.classList.contains('dark') ?
                    '1px solid rgba(255, 255, 255, 0.05)' : '1px solid rgba(0, 0, 0, 0.05)';

                // Add hover effect
                item.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = document.documentElement.classList.contains('dark') ?
                        '#4B5563' : '#F3F4F6';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'transparent';
                });
            });

            // Remove border from last item
            if (menuItems.length > 0) {
                menuItems[menuItems.length - 1].style.borderBottom = 'none';
            }

            // Toggle menu on button click
            dropdown.button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                if (activeDropdown === dropdown) {
                    hideMenu(dropdown);
                } else {
                    showMenu(dropdown);
                }
            });
        }
    });

    // Hide menus when clicking outside
    overlay.addEventListener('click', hideAllMenus);

    // Hide menus when pressing Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && activeDropdown) {
            hideAllMenus();
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (activeDropdown) {
            positionMenu(activeDropdown.button, activeDropdown.menu);
        }
    });

    // Log that initialization is complete
    console.log('All dropdowns initialized');
    }, 100); // End of setTimeout
});
