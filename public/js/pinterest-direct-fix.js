/**
 * Pinterest Direct Fix
 * 
 * This script directly fixes the Pinterest Clone navigation and form submission issues
 * without relying on the existing pinterest-clone-fix.js file.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Pinterest Direct Fix loaded');
    
    // Fix for the New Scrape button on the dashboard
    const newScrapeButtons = document.querySelectorAll('a[href*="/momentum/clone/pinterest/scraper"]');
    
    if (newScrapeButtons.length > 0) {
        console.log('Found New Scrape buttons:', newScrapeButtons.length);
        
        newScrapeButtons.forEach(button => {
            // Remove existing event listeners by cloning
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            
            // Add our direct click handler
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                console.log('New Scrape button clicked, redirecting to:', this.href);
                window.location.href = this.href;
                
                return false;
            });
        });
    }
    
    // Fix for the scraper form
    const scraperForm = document.getElementById('scraper-form');
    
    if (scraperForm) {
        console.log('Found scraper form, adding direct handler');
        
        // Remove existing event listeners by cloning
        const newForm = scraperForm.cloneNode(true);
        scraperForm.parentNode.replaceChild(newForm, scraperForm);
        
        // Add our direct submit handler
        newForm.addEventListener('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('Scraper form submitted');
            
            // Get form data
            const formData = new FormData(newForm);
            
            // Show progress indicator
            const progressElement = document.getElementById('scraping-progress');
            if (progressElement) {
                progressElement.classList.remove('hidden');
                
                // Update progress bar to show activity
                const progressBar = document.getElementById('progress-bar');
                const progressStatus = document.getElementById('progress-status');
                const progressPercentage = document.getElementById('progress-percentage');
                
                if (progressBar) progressBar.style.width = '10%';
                if (progressStatus) progressStatus.textContent = 'Processing...';
                if (progressPercentage) progressPercentage.textContent = '10%';
            }
            
            // Disable submit button
            const submitButton = newForm.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.classList.add('opacity-50', 'cursor-not-allowed');
            }
            
            // Submit the form via fetch API
            fetch('/momentum/clone/pinterest/process-scrape', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                
                if (data.success && data.scrape_id) {
                    // Show success message
                    if (progressElement) {
                        const progressStatus = document.getElementById('progress-status');
                        if (progressStatus) {
                            progressStatus.textContent = 'Completed';
                            progressStatus.classList.remove('text-blue-600', 'bg-blue-200');
                            progressStatus.classList.add('text-green-600', 'bg-green-200');
                        }
                        
                        const progressBar = document.getElementById('progress-bar');
                        if (progressBar) progressBar.style.width = '100%';
                        
                        const progressPercentage = document.getElementById('progress-percentage');
                        if (progressPercentage) progressPercentage.textContent = '100%';
                    }
                    
                    // Redirect to the results page
                    console.log('Redirecting to view-scrape/' + data.scrape_id);
                    setTimeout(() => {
                        window.location.href = '/momentum/clone/pinterest/view-scrape/' + data.scrape_id;
                    }, 1000);
                } else {
                    // Show error
                    alert(data.message || 'An error occurred while processing the scrape request.');
                    
                    // Re-enable submit button
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                
                // Show error
                alert('An error occurred while processing the scrape request.');
                
                // Re-enable submit button
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                }
            });
            
            return false;
        });
    }
});
