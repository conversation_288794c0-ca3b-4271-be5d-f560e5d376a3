/**
 * Direct Click Handler
 *
 * This script directly attaches click handlers to the layout options in the dropdown menu.
 * It uses a more aggressive approach to ensure the options are clickable.
 */

// Execute immediately
(function() {
    console.log('Direct Click Handler executing immediately');

    // Function to apply layout directly
    function applyLayoutDirectly(layoutName) {
        console.log('Applying layout directly from direct-click-handler.js:', layoutName);

        // Update the layout name in the UI
        const viewModeText = document.getElementById('view-mode-text');
        if (viewModeText) {
            viewModeText.textContent = layoutName.charAt(0).toUpperCase() + layoutName.slice(1) + ' View';
        }

        // Save the preference
        localStorage.setItem('dashboard_view_mode', layoutName);

        // Get the dashboard widgets container
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) {
            console.error('Dashboard widgets container not found');
            return;
        }

        // Update data attributes
        dashboardWidgets.setAttribute('data-view-mode', layoutName);
        dashboardWidgets.setAttribute('data-arrangement', layoutName);

        // Apply task widget styles
        const todayTasksWidget = document.querySelector('[data-widget="today-tasks"]');
        const overdueTasksWidget = document.querySelector('[data-widget="overdue-tasks"]');
        const todayTasksList = document.getElementById('today-tasks-list');
        const overdueTasksList = document.getElementById('overdue-tasks-list');

        // Reset task widget styles
        if (todayTasksWidget) {
            todayTasksWidget.style.height = '400px';
            todayTasksWidget.style.display = 'flex';
            todayTasksWidget.style.flexDirection = 'column';
        }

        if (overdueTasksWidget) {
            overdueTasksWidget.style.height = '400px';
            overdueTasksWidget.style.display = 'flex';
            overdueTasksWidget.style.flexDirection = 'column';
        }

        if (todayTasksList) {
            todayTasksList.style.maxHeight = '300px';
            todayTasksList.style.overflowY = 'auto';
            todayTasksList.style.scrollbarWidth = 'thin';
        }

        if (overdueTasksList) {
            overdueTasksList.style.maxHeight = '300px';
            overdueTasksList.style.overflowY = 'auto';
            overdueTasksList.style.scrollbarWidth = 'thin';
        }

        // Apply dramatic visual changes based on layout
        switch(layoutName) {
            case 'adhd-optimized':
                // Make current focus widget full width with blue border
                const currentFocusWidget = document.getElementById('current-focus-widget');
                if (currentFocusWidget) {
                    currentFocusWidget.style.gridColumn = '1 / -1';
                    currentFocusWidget.style.borderLeft = '6px solid #0ea5e9';
                    currentFocusWidget.style.transform = 'scale(1.02)';
                    currentFocusWidget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
                }

                // Show all widgets
                document.querySelectorAll('[data-widget]').forEach(widget => {
                    widget.style.display = 'flex';
                    widget.style.flexDirection = 'column';
                });

                // Hide widget controls
                const widgetControls = document.getElementById('widget-controls');
                if (widgetControls) {
                    widgetControls.style.display = 'none';
                }

                // Apply container styles
                dashboardWidgets.style.backgroundColor = 'rgba(14, 165, 233, 0.08)';
                dashboardWidgets.style.padding = '1.5rem';
                dashboardWidgets.style.borderRadius = '0.75rem';

                // Apply task widget styles for ADHD layout
                if (todayTasksWidget) {
                    todayTasksWidget.style.height = '420px';
                    todayTasksWidget.style.borderLeft = '4px solid rgba(59, 130, 246, 0.8)';
                }

                if (overdueTasksWidget) {
                    overdueTasksWidget.style.height = '420px';
                    overdueTasksWidget.style.borderLeft = '4px solid rgba(239, 68, 68, 0.8)';
                }

                if (todayTasksList) {
                    todayTasksList.style.maxHeight = '320px';
                }

                if (overdueTasksList) {
                    overdueTasksList.style.maxHeight = '320px';
                }
                break;

            case 'focus':
                // Hide all widgets except essential ones
                document.querySelectorAll('[data-widget]').forEach(widget => {
                    const widgetType = widget.getAttribute('data-widget');
                    if (widgetType === 'current-focus-widget' || widgetType === 'today-tasks' || widgetType === 'keyboard-shortcuts') {
                        widget.style.display = 'flex';
                        widget.style.flexDirection = 'column';
                    } else {
                        widget.style.display = 'none';
                    }
                });

                // Apply container styles
                dashboardWidgets.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
                dashboardWidgets.style.padding = '1.5rem';
                dashboardWidgets.style.borderRadius = '0.75rem';

                // Apply task widget styles for Focus layout
                if (todayTasksWidget) {
                    todayTasksWidget.style.height = '450px';
                    todayTasksWidget.style.borderLeft = '5px solid rgba(59, 130, 246, 0.8)';
                }

                if (todayTasksList) {
                    todayTasksList.style.maxHeight = '350px';
                }
                break;

            case 'standard':
                // Show all widgets
                document.querySelectorAll('[data-widget]').forEach(widget => {
                    widget.style.display = 'flex';
                    widget.style.flexDirection = 'column';
                    widget.style.border = '1px solid rgba(0, 0, 0, 0.05)';
                    widget.style.borderRadius = '0.5rem';
                });

                // Hide widget controls
                const widgetControlsStd = document.getElementById('widget-controls');
                if (widgetControlsStd) {
                    widgetControlsStd.style.display = 'none';
                }

                // Apply container styles
                dashboardWidgets.style.backgroundColor = 'rgba(0, 0, 0, 0.02)';
                dashboardWidgets.style.padding = '1.25rem';
                dashboardWidgets.style.borderRadius = '0.75rem';

                // Apply task widget styles for Standard layout
                if (todayTasksWidget) {
                    todayTasksWidget.style.height = '380px';
                }

                if (overdueTasksWidget) {
                    overdueTasksWidget.style.height = '380px';
                }

                if (todayTasksList) {
                    todayTasksList.style.maxHeight = '280px';
                }

                if (overdueTasksList) {
                    overdueTasksList.style.maxHeight = '280px';
                }
                break;

            case 'custom':
                // Show all widgets
                document.querySelectorAll('[data-widget]').forEach(widget => {
                    widget.style.display = 'flex';
                    widget.style.flexDirection = 'column';
                    widget.style.border = '2px dotted rgba(139, 92, 246, 0.3)';
                    widget.style.position = 'relative';
                    widget.style.cursor = 'move';
                });

                // Show widget controls
                const widgetControlsCustom = document.getElementById('widget-controls');
                if (widgetControlsCustom) {
                    widgetControlsCustom.style.display = 'block';
                }

                // Apply container styles
                dashboardWidgets.style.backgroundColor = 'rgba(139, 92, 246, 0.05)';
                dashboardWidgets.style.padding = '1.5rem';
                dashboardWidgets.style.borderRadius = '0.75rem';
                dashboardWidgets.style.border = '2px dashed rgba(139, 92, 246, 0.3)';

                // Apply task widget styles for Custom layout
                if (todayTasksWidget) {
                    todayTasksWidget.style.height = '400px';
                    todayTasksWidget.style.resize = 'vertical';
                    todayTasksWidget.style.overflow = 'hidden';
                }

                if (overdueTasksWidget) {
                    overdueTasksWidget.style.height = '400px';
                    overdueTasksWidget.style.resize = 'vertical';
                    overdueTasksWidget.style.overflow = 'hidden';
                }
                break;
        }

        // Hide dropdown
        const layoutDropdown = document.getElementById('layout-dropdown');
        if (layoutDropdown) {
            layoutDropdown.classList.add('hidden');
            layoutDropdown.style.display = 'none';
        }
    }

    // Function to attach click handlers
    function attachClickHandlers() {
        console.log('Attaching direct click handlers to layout options');

        // Get all layout options
        const layoutOptions = document.querySelectorAll('[data-view]');
        console.log('Found layout options:', layoutOptions.length);

        if (layoutOptions.length === 0) {
            // Try again later
            setTimeout(attachClickHandlers, 500);
            return;
        }

        // Attach click handlers
        layoutOptions.forEach(option => {
            const layoutName = option.getAttribute('data-view');
            console.log('Attaching direct click handler to:', layoutName);

            // Remove existing click handlers
            option.onclick = null;

            // Add new click handler
            option.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Layout option clicked directly:', layoutName);
                applyLayoutDirectly(layoutName);
                return false;
            };

            // Also add event listener
            option.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Layout option clicked via event listener:', layoutName);
                applyLayoutDirectly(layoutName);
                return false;
            });
        });
    }

    // Try to attach click handlers immediately
    attachClickHandlers();

    // Also try after DOM is loaded
    document.addEventListener('DOMContentLoaded', attachClickHandlers);

    // And try again after a delay
    setTimeout(attachClickHandlers, 1000);
    setTimeout(attachClickHandlers, 2000);
})();
