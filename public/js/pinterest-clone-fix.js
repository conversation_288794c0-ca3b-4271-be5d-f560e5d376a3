/**
 * Pinterest Clone Fix
 *
 * This script fixes issues with the Pinterest Clone Research Tool.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Pinterest Clone Fix loaded');

    // Fix View buttons in the Pinterest Clone
    fixPinterestViewButtons();

    // Also fix other Pinterest Clone links
    fixPinterestLinks();

    // Fix the scraper form submission
    fixScraperForm();
});

/**
 * Fix View buttons in the Pinterest Clone
 */
function fixPinterestViewButtons() {
    // Find all View buttons in the Pinterest Clone
    const viewButtons = document.querySelectorAll('a[href*="/momentum/clone/pinterest/view-scrape/"]');

    console.log('Found Pinterest View buttons:', viewButtons.length);

    // Add direct click handlers to each button
    viewButtons.forEach(function(button) {
        // Clone the button to remove any existing event listeners
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        // Add a direct click handler
        newButton.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default to ensure our handler runs
            e.stopPropagation(); // Stop event bubbling

            console.log('Pinterest View button clicked:', this.href);

            // Force navigation to the href
            window.location.href = this.href;

            return false; // Ensure no other handlers run
        });
    });
}

/**
 * Fix all Pinterest Clone links
 */
function fixPinterestLinks() {
    // Find all links in the Pinterest Clone section
    const pinterestLinks = document.querySelectorAll('a[href*="/momentum/clone/pinterest/"]');

    console.log('Found Pinterest links:', pinterestLinks.length);

    // Add direct click handlers to each link
    pinterestLinks.forEach(function(link) {
        // Skip links that already have the fix applied
        if (link.getAttribute('data-fixed') === 'true') {
            return;
        }

        // Skip "New Scrape" buttons
        if (link.textContent.trim().includes('New Scrape')) {
            console.log('Skipping New Scrape button:', link.href);
            return;
        }

        // Skip links to the scraper page
        if (link.href.includes('/momentum/clone/pinterest/scraper')) {
            console.log('Skipping scraper link:', link.href);
            return;
        }

        // Clone the link to remove any existing event listeners
        const newLink = link.cloneNode(true);
        link.parentNode.replaceChild(newLink, link);

        // Mark as fixed
        newLink.setAttribute('data-fixed', 'true');

        // Add a direct click handler
        newLink.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default to ensure our handler runs
            e.stopPropagation(); // Stop event bubbling

            console.log('Pinterest link clicked:', this.href);

            // Force navigation to the href
            window.location.href = this.href;

            return false; // Ensure no other handlers run
        });
    });
}

/**
 * Fix the scraper form submission
 */
function fixScraperForm() {
    // Find the scraper form
    const scraperForm = document.getElementById('scraper-form');

    if (scraperForm) {
        console.log('Found scraper form, adding fix');

        // Clone the form to remove any existing event listeners
        const newForm = scraperForm.cloneNode(true);
        scraperForm.parentNode.replaceChild(newForm, scraperForm);

        // Add a direct submit handler
        newForm.addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent default form submission
            e.stopPropagation(); // Stop event bubbling

            console.log('Scraper form submitted');

            // Get form data
            const formData = new FormData(newForm);
            const searchTerm = formData.get('search_term');

            if (!searchTerm) {
                alert('Please enter a search term');
                return false;
            }

            // Show progress indicator if it exists
            const progressElement = document.getElementById('scraping-progress');
            if (progressElement) {
                progressElement.classList.remove('hidden');
            }

            // Disable submit button if it exists
            const submitButton = newForm.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.classList.add('opacity-50', 'cursor-not-allowed');
            }

            // Submit the form via AJAX with explicit headers
            fetch('/momentum/clone/pinterest/process-scrape', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                console.log('Scrape processed:', data);

                if (data.success) {
                    // Show success message
                    if (progressElement) {
                        const progressStatus = document.getElementById('progress-status');
                        const progressMessage = document.getElementById('progress-message');

                        if (progressStatus) {
                            progressStatus.textContent = 'Completed';
                            progressStatus.classList.remove('text-blue-600', 'bg-blue-200');
                            progressStatus.classList.add('text-green-600', 'bg-green-200');
                        }

                        if (progressMessage) {
                            progressMessage.textContent = 'Scrape completed successfully! Redirecting to results...';
                        }
                    }

                    // Redirect to the scrape view page after a short delay
                    setTimeout(() => {
                        console.log('Redirecting to view-scrape/' + data.scrape_id);
                        window.location.href = '/momentum/clone/pinterest/view-scrape/' + data.scrape_id;
                    }, 1500);
                } else {
                    // Show error
                    alert(data.message || 'An error occurred while processing the scrape request.');

                    // Re-enable submit button
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);

                // Show error
                alert('An error occurred while processing the scrape request.');

                // Re-enable submit button
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                }
            });

            return false;
        });
    }
}
