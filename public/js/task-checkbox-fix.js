/**
 * Task Checkbox Fix
 *
 * This script fixes the task checkbox functionality by using the correct endpoint.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Task Checkbox Fix loaded');

    // Fix task checkboxes
    fixTaskCheckboxes();

    /**
     * Fix task checkboxes
     */
    function fixTaskCheckboxes() {
        const taskCheckboxes = document.querySelectorAll('.task-checkbox');

        if (taskCheckboxes.length === 0) {
            console.log('No task checkboxes found');
            return;
        }

        console.log('Fixing task checkboxes:', taskCheckboxes.length);

        taskCheckboxes.forEach((checkbox, index) => {
            // Remove any existing event listeners
            const newCheckbox = checkbox.cloneNode(true);
            checkbox.parentNode.replaceChild(newCheckbox, checkbox);

            // Add direct change handler
            newCheckbox.addEventListener('change', function() {
                const taskId = this.getAttribute('data-task-id');
                const isChecked = this.checked;

                console.log(`Task checkbox ${index + 1} changed (fixed):`, taskId, isChecked);

                if (isChecked) {
                    // Use the correct endpoint for task completion
                    fetch(`/momentum/tasks/complete/${taskId}`, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        console.log('Complete task response status:', response.status);
                        return response.json();
                    })
                    .then(data => {
                        console.log('Task completed:', data);

                        if (data.success) {
                            // Update UI
                            const taskItem = document.querySelector(`.task-item[data-task-id="${taskId}"]`);
                            if (taskItem) {
                                const taskTitle = taskItem.querySelector('a');

                                taskTitle.classList.add('line-through', 'text-gray-500', 'dark:text-gray-400');
                                showSuccessMessage('Task marked as complete!');

                                // Always update the UI without reloading
                                setTimeout(() => {
                                    // Remove any blue highlight classes
                                    taskItem.classList.remove('bg-primary-50', 'dark:bg-primary-900', 'border-l-4', 'border-primary-500');
                                }, 100);
                            }
                        } else {
                            console.error('Failed to complete task:', data.message);
                            this.checked = false; // Revert checkbox state
                            showErrorMessage('Failed to mark task as complete. Please try again.');
                        }
                    })
                    .catch(error => {
                        console.error('Error completing task:', error);
                        this.checked = false; // Revert checkbox state
                        showErrorMessage('An error occurred. Please try again.');
                    });
                } else {
                    // Unchecking is not supported in the current API
                    // Revert the checkbox state
                    this.checked = true;
                    showErrorMessage('Unchecking completed tasks is not supported.');
                }
            });
        });
    }

    /**
     * Show success message
     */
    function showSuccessMessage(message) {
        showMessage(message, 'success');
    }

    /**
     * Show error message
     */
    function showErrorMessage(message) {
        showMessage(message, 'error');
    }

    /**
     * Show message
     */
    function showMessage(message, type = 'success') {
        console.log(`Showing ${type} message:`, message);

        // Create message element if it doesn't exist
        let messageElement = document.getElementById(`${type}-message`);

        if (!messageElement) {
            messageElement = document.createElement('div');
            messageElement.id = `${type}-message`;

            const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';

            messageElement.className = `fixed top-4 right-4 ${bgColor} text-white px-4 py-2 rounded shadow-lg z-50 transform transition-all duration-300 opacity-0 translate-y-[-20px]`;
            document.body.appendChild(messageElement);
        }

        // Set message text
        messageElement.textContent = message;

        // Show message with animation
        setTimeout(() => {
            messageElement.classList.remove('opacity-0', 'translate-y-[-20px]');
            messageElement.classList.add('opacity-100', 'translate-y-0');
        }, 10);

        // Hide message after delay
        setTimeout(() => {
            messageElement.classList.remove('opacity-100', 'translate-y-0');
            messageElement.classList.add('opacity-0', 'translate-y-[-20px]');
        }, 3000);
    }
});
