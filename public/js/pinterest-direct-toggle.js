/**
 * Pinterest Direct Toggle
 *
 * This script directly handles the toggle functionality for the Pinterest scraper.
 */

// Wait for the DOM to be fully loaded
window.addEventListener('DOMContentLoaded', function() {
    console.log('Pinterest Direct Toggle loaded');

    // Function to toggle advanced options
    function setupAdvancedToggle() {
        const toggleBtn = document.getElementById('toggle-advanced');
        const advancedOptions = document.getElementById('advanced-options');
        const showText = document.getElementById('advanced-show');
        const hideText = document.getElementById('advanced-hide');

        console.log('Toggle button:', toggleBtn);
        console.log('Advanced options:', advancedOptions);

        if (toggleBtn && advancedOptions) {
            // Make sure advanced options are hidden initially
            advancedOptions.style.display = 'none';

            if (showText) showText.style.display = 'inline';
            if (hideText) hideText.style.display = 'none';

            // Add click event
            toggleBtn.onclick = function(e) {
                e.preventDefault();
                console.log('Toggle button clicked');

                if (advancedOptions.style.display === 'none') {
                    advancedOptions.style.display = 'block';
                    if (showText) showText.style.display = 'none';
                    if (hideText) hideText.style.display = 'inline';
                } else {
                    advancedOptions.style.display = 'none';
                    if (showText) showText.style.display = 'inline';
                    if (hideText) hideText.style.display = 'none';
                }

                return false;
            };

            console.log('Advanced toggle setup complete');
        } else {
            console.error('Could not find toggle button or advanced options');
        }
    }

    // Function to toggle Pinterest credentials
    function setupCredentialsToggle() {
        const checkbox = document.getElementById('use_real_scraping');
        const credentials = document.getElementById('pinterest-credentials');

        console.log('Checkbox:', checkbox);
        console.log('Credentials:', credentials);

        if (checkbox && credentials) {
            // Make sure credentials are hidden initially
            credentials.style.display = 'none';

            // Add change event
            checkbox.onchange = function() {
                console.log('Checkbox changed:', this.checked);

                if (this.checked) {
                    credentials.style.display = 'block';
                } else {
                    credentials.style.display = 'none';
                }
            };

            console.log('Credentials toggle setup complete');
        } else {
            console.error('Could not find checkbox or credentials');
        }
    }

    // Function to handle form submission
    function setupFormSubmission() {
        const form = document.getElementById('scraper-form');
        const progress = document.getElementById('scraping-progress');

        console.log('Form:', form);
        console.log('Progress:', progress);

        if (form && progress) {
            // Make sure progress is hidden initially
            progress.style.display = 'none';

            // Add submit event
            form.onsubmit = function(e) {
                e.preventDefault();
                console.log('Form submitted');

                // Show progress
                progress.style.display = 'block';

                // Get form data
                const formData = new FormData(form);

                // Update progress bar
                const progressBar = document.getElementById('progress-bar');
                const progressStatus = document.getElementById('progress-status');
                const progressPercentage = document.getElementById('progress-percentage');
                const progressMessage = document.getElementById('progress-message');

                if (progressBar) progressBar.style.width = '10%';
                if (progressPercentage) progressPercentage.innerText = '10%';
                if (progressStatus) progressStatus.innerText = 'Processing...';
                if (progressMessage) progressMessage.innerText = 'Connecting to Pinterest servers...';

                // Log form data for debugging
                console.log('Form data:');
                for (let pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }

                // Submit form via fetch
                fetch('/momentum/clone/pinterest/process-scrape', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);
                    return response.json();
                })
                .then(data => {
                    console.log('Response:', data);

                    if (data.success && data.scrape_id) {
                        // Show success message
                        if (progressBar) progressBar.style.width = '100%';
                        if (progressPercentage) progressPercentage.innerText = '100%';
                        if (progressStatus) {
                            progressStatus.innerText = 'Completed';
                            progressStatus.classList.remove('text-blue-600', 'bg-blue-200');
                            progressStatus.classList.add('text-green-600', 'bg-green-200');
                        }
                        if (progressMessage) progressMessage.innerText = 'Scrape completed successfully! Redirecting to results...';

                        // Redirect to results page after a short delay
                        const redirectUrl = '/momentum/clone/pinterest/view-scrape/' + data.scrape_id;
                        console.log('Redirecting to:', redirectUrl);

                        setTimeout(function() {
                            console.log('Executing redirect now...');
                            window.location.href = redirectUrl;
                        }, 1500);
                    } else {
                        // Show error
                        if (progressStatus) {
                            progressStatus.innerText = 'Error';
                            progressStatus.classList.remove('text-blue-600', 'bg-blue-200');
                            progressStatus.classList.add('text-red-600', 'bg-red-200');
                        }
                        if (progressMessage) progressMessage.innerText = data.message || 'An error occurred while processing the scrape request.';

                        // Alert the error
                        alert(data.message || 'An error occurred');

                        // Hide progress after a short delay
                        setTimeout(function() {
                            progress.style.display = 'none';
                        }, 1500);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Show error in progress bar
                    if (progressStatus) {
                        progressStatus.innerText = 'Error';
                        progressStatus.classList.remove('text-blue-600', 'bg-blue-200');
                        progressStatus.classList.add('text-red-600', 'bg-red-200');
                    }
                    if (progressMessage) progressMessage.innerText = 'An error occurred while processing the scrape request.';

                    // Alert the error
                    alert('An error occurred while processing the scrape request.');

                    // Hide progress after a short delay
                    setTimeout(function() {
                        progress.style.display = 'none';
                    }, 1500);
                });

                return false;
            };

            console.log('Form submission setup complete');
        } else {
            console.error('Could not find form or progress');
        }
    }

    // Run setup functions
    setupAdvancedToggle();
    setupCredentialsToggle();
    setupFormSubmission();
});
