/**
 * ADHD-Friendly Dashboard Enhancements
 *
 * This script provides ADHD-optimized functionality for the Momentum dashboard.
 * It focuses on reducing cognitive load, improving focus, enhancing usability,
 * and supporting executive function challenges.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('ADHD Dashboard Enhancements loaded');

    // ===== Core Elements =====
    const dashboardWidgets = document.getElementById('dashboard-widgets');
    const currentFocusWidget = document.getElementById('current-focus-widget');
    const viewModeText = document.getElementById('view-mode-text');
    const focusModeToggle = document.getElementById('focus-mode-toggle');
    const clearFocusBtn = document.getElementById('clear-focus-task');
    const markCompleteBtn = document.getElementById('mark-complete-btn');
    const taskCheckboxes = document.querySelectorAll('.task-checkbox');
    const setFocusBtns = document.querySelectorAll('.set-focus-btn');

    // Apply saved view mode on page load
    if (dashboardWidgets) {
        const savedViewMode = localStorage.getItem('dashboard_view_mode') || 'adhd-optimized';
        console.log('Applying saved view mode on page load:', savedViewMode);

        // Force a small delay to ensure the DOM is fully loaded
        setTimeout(() => {
            // Update data attributes
            dashboardWidgets.setAttribute('data-view-mode', savedViewMode);
            dashboardWidgets.setAttribute('data-arrangement', savedViewMode);

            // Update view mode text
            if (viewModeText) {
                viewModeText.textContent = savedViewMode.charAt(0).toUpperCase() + savedViewMode.slice(1) + ' View';
            }

            // Apply specific layout based on view mode
            switch(savedViewMode) {
                case 'adhd-optimized':
                    applyADHDOptimizedArrangement();
                    break;
                case 'focus':
                    enterFocusMode();
                    break;
                case 'standard':
                    // Reset to standard layout
                    document.body.classList.remove('focus-mode');
                    break;
                case 'custom':
                    // Show widget controls for custom layout
                    const widgetControls = document.getElementById('widget-controls');
                    if (widgetControls) {
                        widgetControls.style.display = 'block';
                    }
                    break;
            }

            console.log('View mode applied:', savedViewMode);
        }, 100);
    }

    // ===== Keyboard Shortcuts =====

    // Initialize keyboard shortcuts
    initKeyboardShortcuts();

    function initKeyboardShortcuts() {
        // Global keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Backspace to go back (unless in an input field)
            if (e.key === 'Backspace' && !isInputFocused()) {
                e.preventDefault();
                window.history.back();
            }

            // Alt + key shortcuts for quick navigation
            if (e.altKey) {
                switch(e.key.toLowerCase()) {
                    case 't': // Tasks
                        e.preventDefault();
                        window.location.href = '/momentum/tasks';
                        break;
                    case 'a': // ADHD
                        e.preventDefault();
                        window.location.href = '/momentum/adhd';
                        break;
                    case 'p': // Projects
                        e.preventDefault();
                        window.location.href = '/momentum/projects';
                        break;
                    case 'f': // Finances
                        e.preventDefault();
                        window.location.href = '/momentum/finances';
                        break;
                    case 'i': // Ideas
                        e.preventDefault();
                        window.location.href = '/momentum/ideas';
                        break;
                    case 'h': // Help
                        e.preventDefault();
                        window.location.href = '/momentum/help';
                        break;
                    case 'k': // Keyboard shortcuts
                        e.preventDefault();
                        toggleShortcutsDetails();
                        break;
                }
            }

            // Escape to exit focus mode
            if (e.key === 'Escape' && document.body.classList.contains('focus-mode')) {
                e.preventDefault();
                exitFocusMode();
            }
        });
    }

    function isInputFocused() {
        const activeElement = document.activeElement;
        return activeElement.tagName === 'INPUT' ||
               activeElement.tagName === 'TEXTAREA' ||
               activeElement.isContentEditable;
    }

    function toggleShortcutsDetails() {
        const shortcutsDetails = document.getElementById('shortcuts-details');
        const toggleButton = document.getElementById('toggle-shortcuts-details');

        if (shortcutsDetails) {
            const isHidden = shortcutsDetails.classList.contains('hidden');
            shortcutsDetails.classList.toggle('hidden');

            if (toggleButton) {
                toggleButton.textContent = isHidden ? 'Hide Details' : 'Show Details';
            }
        }
    }

    // ===== View Mode Management =====

    // Switch to a different view mode
    function switchViewMode(mode) {
        console.log('Switching view mode to:', mode);

        if (!dashboardWidgets) {
            console.error('Dashboard widgets container not found');
            return;
        }

        // Update data attribute
        dashboardWidgets.setAttribute('data-view-mode', mode);

        // Update view mode indicator
        if (viewModeText) {
            viewModeText.textContent = mode.charAt(0).toUpperCase() + mode.slice(1) + ' View';
        }

        // Apply specific view mode settings
        switch(mode) {
            case 'focus':
                enterFocusMode();
                break;

            case 'adhd-optimized':
                exitFocusMode();
                applyADHDOptimizedArrangement();
                break;

            case 'standard':
                exitFocusMode();
                // Reset to standard layout
                break;

            case 'custom':
                exitFocusMode();
                // Apply custom layout
                break;
        }

        // Save the current view mode preference
        localStorage.setItem('dashboard_view_mode', mode);
    }

    // ===== Focus Mode =====

    // Initialize focus mode toggle
    if (focusModeToggle) {
        focusModeToggle.addEventListener('click', function() {
            const isFocusModeActive = document.body.classList.contains('focus-mode');

            if (isFocusModeActive) {
                exitFocusMode();
            } else {
                enterFocusMode();
            }
        });
    }

    function enterFocusMode() {
        console.log('Entering focus mode');

        // Add focus mode class to body
        document.body.classList.add('focus-mode');

        // Update button text
        if (focusModeToggle) {
            focusModeToggle.innerHTML = '<i class="fas fa-compress mr-1.5"></i> Exit Focus Mode';
            focusModeToggle.classList.add('bg-primary-100', 'dark:bg-primary-800', 'text-primary-700', 'dark:text-primary-300');
            focusModeToggle.classList.remove('bg-white', 'dark:bg-gray-700');
        }

        // Set view mode
        if (dashboardWidgets) {
            dashboardWidgets.setAttribute('data-view-mode', 'focus');
            dashboardWidgets.setAttribute('data-arrangement', 'focus');
        }

        // Show success message
        showSuccessMessage('Focus Mode Activated');
    }

    function exitFocusMode() {
        console.log('Exiting focus mode');

        // Remove focus mode class from body
        document.body.classList.remove('focus-mode');

        // Update button text
        if (focusModeToggle) {
            focusModeToggle.innerHTML = '<i class="fas fa-expand mr-1.5"></i> Enter Focus Mode';
            focusModeToggle.classList.remove('bg-primary-100', 'dark:bg-primary-800', 'text-primary-700', 'dark:text-primary-300');
            focusModeToggle.classList.add('bg-white', 'dark:bg-gray-700');
        }

        // Restore previous view mode
        const savedViewMode = localStorage.getItem('dashboard_view_mode') || 'adhd-optimized';
        if (dashboardWidgets) {
            dashboardWidgets.setAttribute('data-view-mode', savedViewMode);
            dashboardWidgets.setAttribute('data-arrangement', savedViewMode);
        }
    }

    // ===== ADHD-Optimized Arrangement =====

    // Apply ADHD-optimized arrangement
    function applyADHDOptimizedArrangement() {
        if (!dashboardWidgets) return;

        console.log('Applying ADHD-optimized arrangement');

        // Define the optimal order for ADHD productivity
        const optimalOrder = [
            'current-focus-widget',  // Most important - what to focus on now
            'today-tasks',           // Time-sensitive - what needs to be done today
            'overdue-tasks',         // Urgent - what's already late
            'keyboard-shortcuts',    // Quick access to features - reduces cognitive load
            'upcoming-tasks',        // Forward planning - what's coming up
            'projects-summary',      // Project overview - big picture context
            'recent-ideas',          // Capture and inspiration - creative outlet
            'adhd-guide',            // Support and strategies - ADHD-specific help
            'financial-summary',     // Financial awareness - practical life management
            'help-center'            // Help and resources - when stuck
        ];

        // Apply special styling to important widgets
        if (currentFocusWidget) {
            // Make Current Focus widget more prominent
            currentFocusWidget.classList.add('md:col-span-2', 'lg:col-span-3');
        }

        // Make Today's Tasks and Overdue Tasks more prominent
        const todayTasksWidget = document.querySelector('[data-widget="today-tasks"]');
        const overdueTasksWidget = document.querySelector('[data-widget="overdue-tasks"]');

        if (todayTasksWidget) {
            todayTasksWidget.classList.add('md:col-span-1', 'lg:col-span-2');
        }

        if (overdueTasksWidget) {
            overdueTasksWidget.classList.add('md:col-span-1', 'lg:col-span-1');
        }
    }

    // ===== Utility Functions =====

    // Show a success message
    function showSuccessMessage(message) {
        // Create message element if it doesn't exist
        let messageElement = document.getElementById('success-message');

        if (!messageElement) {
            messageElement = document.createElement('div');
            messageElement.id = 'success-message';
            messageElement.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50 transform transition-all duration-300 opacity-0 translate-y-[-20px]';
            document.body.appendChild(messageElement);
        }

        // Set message text
        messageElement.textContent = message;

        // Show message with animation
        setTimeout(() => {
            messageElement.classList.remove('opacity-0', 'translate-y-[-20px]');
            messageElement.classList.add('opacity-100', 'translate-y-0');
        }, 10);

        // Hide message after delay
        setTimeout(() => {
            messageElement.classList.remove('opacity-100', 'translate-y-0');
            messageElement.classList.add('opacity-0', 'translate-y-[-20px]');
        }, 3000);
    }
});


