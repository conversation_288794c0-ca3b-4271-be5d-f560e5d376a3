/**
 * Dropdown Menu Fix
 *
 * This script provides a simple, direct implementation for dropdown menus
 * to ensure they open when clicked.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get all dropdown buttons
    const adhdButton = document.getElementById('adhd-dropdown-button');
    const productivityButton = document.getElementById('productivity-dropdown-button');
    const toolsButton = document.getElementById('tools-dropdown-button');
    const userButton = document.getElementById('user-menu-button');

    // Get all dropdown menus
    const adhdMenu = document.getElementById('adhd-dropdown-menu');
    const productivityMenu = document.getElementById('productivity-dropdown-menu');
    const toolsMenu = document.getElementById('tools-dropdown-menu');
    const userMenu = document.getElementById('user-dropdown-menu');

    // Get the overlay
    const overlay = document.getElementById('dropdown-overlay');

    // Track active menu
    let activeMenu = null;

    // Function to show a menu
    function showMenu(menu, button) {
        // Hide any active menu first
        hideAllMenus();

        if (!menu) return;

        // Position the menu
        const buttonRect = button.getBoundingClientRect();

        // Set initial styles for positioning
        menu.style.position = 'fixed';
        menu.style.top = (buttonRect.bottom + 5) + 'px';
        menu.style.left = buttonRect.left + 'px';
        menu.style.display = 'block';
        menu.style.visibility = 'visible';
        menu.style.opacity = '1';
        menu.style.zIndex = '9999';
        menu.classList.add('active');

        // Check if menu would go off the bottom of the screen
        const menuRect = menu.getBoundingClientRect();
        const viewportHeight = window.innerHeight;

        if (menuRect.bottom > viewportHeight) {
            // Position above the button if it would go off the bottom
            menu.style.top = 'auto';
            menu.style.bottom = (viewportHeight - buttonRect.top + 5) + 'px';
        }

        // Check if menu would go off the right of the screen
        const viewportWidth = window.innerWidth;

        if (menuRect.right > viewportWidth) {
            // Align right edge of menu with right edge of button
            menu.style.left = 'auto';
            menu.style.right = (viewportWidth - buttonRect.right) + 'px';
        }

        // Show the overlay
        if (overlay) {
            overlay.style.display = 'block';
            overlay.classList.add('active');
        }

        // Update button state
        button.setAttribute('aria-expanded', 'true');

        // Set as active menu
        activeMenu = menu;
    }

    // Function to hide all menus
    function hideAllMenus() {
        const menus = [productivityMenu, toolsMenu, userMenu];
        const buttons = [productivityButton, toolsButton, userButton];

        menus.forEach(function(menu, index) {
            if (menu) {
                menu.style.display = 'none';
                menu.style.visibility = 'hidden';
                menu.style.opacity = '0';
                menu.classList.remove('active');
            }

            if (buttons[index]) {
                buttons[index].setAttribute('aria-expanded', 'false');
            }
        });

        // Hide the overlay
        if (overlay) {
            overlay.style.display = 'none';
            overlay.classList.remove('active');
        }

        // Also hide the ADHD menu (which is now managed separately)
        const adhdMenu = document.getElementById('adhd-dropdown-menu');
        if (adhdMenu) {
            adhdMenu.style.display = 'none';
            adhdMenu.style.visibility = 'hidden';
            adhdMenu.style.opacity = '0';

            const adhdButton = document.getElementById('adhd-dropdown-button');
            if (adhdButton) {
                adhdButton.setAttribute('aria-expanded', 'false');
            }

            // Also hide the ADHD overlay
            const adhdOverlay = document.getElementById('adhd-dropdown-overlay');
            if (adhdOverlay) {
                adhdOverlay.style.display = 'none';
            }
        }

        activeMenu = null;
    }

    // Setup ADHD dropdown
    if (adhdButton && adhdMenu) {
        // Special handling for ADHD menu due to its length
        function positionADHDMenu() {
            const buttonRect = adhdButton.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // Set basic styles for the menu
            adhdMenu.style.position = 'fixed';
            adhdMenu.style.maxHeight = '400px';
            adhdMenu.style.overflowY = 'auto';
            adhdMenu.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)';
            adhdMenu.style.borderRadius = '0.375rem';
            adhdMenu.style.backgroundColor = 'white';
            adhdMenu.style.width = 'auto';
            adhdMenu.style.minWidth = '200px';

            // Position the menu below the button
            adhdMenu.style.top = (buttonRect.bottom + 5) + 'px';
            adhdMenu.style.left = buttonRect.left + 'px';

            // Force a reflow to get accurate dimensions
            adhdMenu.offsetHeight;

            // Check if menu would go off the screen
            const menuRect = adhdMenu.getBoundingClientRect();

            // Handle horizontal positioning
            if (menuRect.right > viewportWidth) {
                // Align right edge of menu with right edge of button
                adhdMenu.style.left = 'auto';
                adhdMenu.style.right = (viewportWidth - buttonRect.right) + 'px';
            }

            // Handle vertical positioning
            if (menuRect.bottom > viewportHeight) {
                // If menu is too tall for the viewport, adjust its position and max height
                const spaceBelow = viewportHeight - buttonRect.bottom - 10;
                adhdMenu.style.maxHeight = spaceBelow + 'px';
            }

            // Apply dark mode styles if needed
            if (document.documentElement.classList.contains('dark')) {
                adhdMenu.style.backgroundColor = '#374151';
                adhdMenu.style.color = 'white';
            }
        }

        adhdButton.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('ADHD button clicked');

            if (activeMenu === adhdMenu) {
                hideAllMenus();
            } else {
                hideAllMenus();

                // Show the menu
                adhdMenu.style.display = 'block';
                adhdMenu.style.visibility = 'visible';
                adhdMenu.style.opacity = '1';
                adhdMenu.style.zIndex = '99999'; // Much higher z-index to ensure it's above everything
                adhdMenu.classList.add('active');

                // Force the menu to be on top of everything
                document.body.appendChild(adhdMenu);

                // Position it
                positionADHDMenu();

                // Show the overlay
                if (overlay) {
                    overlay.style.display = 'block';
                    overlay.classList.add('active');
                }

                // Update button state
                adhdButton.setAttribute('aria-expanded', 'true');

                // Set as active menu
                activeMenu = adhdMenu;
            }

            return false;
        };
    }

    // Setup Productivity dropdown
    if (productivityButton && productivityMenu) {
        productivityButton.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Productivity button clicked');

            if (activeMenu === productivityMenu) {
                hideAllMenus();
            } else {
                // Hide ADHD menu if it's open
                const adhdMenu = document.getElementById('adhd-dropdown-menu');
                if (adhdMenu && adhdMenu.style.display === 'block') {
                    adhdMenu.style.display = 'none';
                    adhdMenu.style.visibility = 'hidden';
                    adhdMenu.style.opacity = '0';

                    const adhdButton = document.getElementById('adhd-dropdown-button');
                    if (adhdButton) {
                        adhdButton.setAttribute('aria-expanded', 'false');
                    }
                }

                showMenu(productivityMenu, productivityButton);
            }

            return false;
        };

        // Also handle hover
        productivityButton.addEventListener('mouseenter', function() {
            // Hide ADHD menu if it's open
            const adhdMenu = document.getElementById('adhd-dropdown-menu');
            if (adhdMenu && adhdMenu.style.display === 'block') {
                adhdMenu.style.display = 'none';
                adhdMenu.style.visibility = 'hidden';
                adhdMenu.style.opacity = '0';

                const adhdButton = document.getElementById('adhd-dropdown-button');
                if (adhdButton) {
                    adhdButton.setAttribute('aria-expanded', 'false');
                }
            }
        });
    }

    // Setup Tools dropdown
    if (toolsButton && toolsMenu) {
        toolsButton.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Tools button clicked');

            if (activeMenu === toolsMenu) {
                hideAllMenus();
            } else {
                // Hide ADHD menu if it's open
                const adhdMenu = document.getElementById('adhd-dropdown-menu');
                if (adhdMenu && adhdMenu.style.display === 'block') {
                    adhdMenu.style.display = 'none';
                    adhdMenu.style.visibility = 'hidden';
                    adhdMenu.style.opacity = '0';

                    const adhdButton = document.getElementById('adhd-dropdown-button');
                    if (adhdButton) {
                        adhdButton.setAttribute('aria-expanded', 'false');
                    }
                }

                showMenu(toolsMenu, toolsButton);
            }

            return false;
        };

        // Also handle hover
        toolsButton.addEventListener('mouseenter', function() {
            // Hide ADHD menu if it's open
            const adhdMenu = document.getElementById('adhd-dropdown-menu');
            if (adhdMenu && adhdMenu.style.display === 'block') {
                adhdMenu.style.display = 'none';
                adhdMenu.style.visibility = 'hidden';
                adhdMenu.style.opacity = '0';

                const adhdButton = document.getElementById('adhd-dropdown-button');
                if (adhdButton) {
                    adhdButton.setAttribute('aria-expanded', 'false');
                }
            }
        });
    }

    // Setup User dropdown
    if (userButton && userMenu) {
        userButton.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('User button clicked');

            if (activeMenu === userMenu) {
                hideAllMenus();
            } else {
                // Hide ADHD menu if it's open
                const adhdMenu = document.getElementById('adhd-dropdown-menu');
                if (adhdMenu && adhdMenu.style.display === 'block') {
                    adhdMenu.style.display = 'none';
                    adhdMenu.style.visibility = 'hidden';
                    adhdMenu.style.opacity = '0';

                    const adhdButton = document.getElementById('adhd-dropdown-button');
                    if (adhdButton) {
                        adhdButton.setAttribute('aria-expanded', 'false');
                    }
                }

                showMenu(userMenu, userButton);
            }

            return false;
        };

        // Also handle hover
        userButton.addEventListener('mouseenter', function() {
            // Hide ADHD menu if it's open
            const adhdMenu = document.getElementById('adhd-dropdown-menu');
            if (adhdMenu && adhdMenu.style.display === 'block') {
                adhdMenu.style.display = 'none';
                adhdMenu.style.visibility = 'hidden';
                adhdMenu.style.opacity = '0';

                const adhdButton = document.getElementById('adhd-dropdown-button');
                if (adhdButton) {
                    adhdButton.setAttribute('aria-expanded', 'false');
                }
            }
        });
    }

    // Hide menus when clicking outside
    document.addEventListener('click', function(e) {
        if (activeMenu && !activeMenu.contains(e.target)) {
            hideAllMenus();
        }
    });

    // Hide menus when pressing Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && activeMenu) {
            hideAllMenus();
        }
    });
});
