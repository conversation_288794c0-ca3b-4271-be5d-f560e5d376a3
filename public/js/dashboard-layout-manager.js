/**
 * Dashboard Layout Manager
 *
 * A consolidated script that handles all dashboard layout functionality.
 * This replaces multiple redundant scripts:
 * - layout-dropdown-fix.js
 * - direct-layout-switcher.js
 * - layout-button-fix.js
 * - universal-layout-handler.js
 * - inline scripts in redesign.php
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard Layout Manager loaded');

    // Core elements
    const layoutButton = document.getElementById('layout-selector-button');
    const layoutDropdown = document.getElementById('layout-dropdown');
    const dashboardWidgets = document.getElementById('dashboard-widgets');
    const viewModeText = document.getElementById('view-mode-text');
    const layoutOptions = document.querySelectorAll('[data-view]');
    const saveLayoutBtn = document.getElementById('save-layout-btn');
    const resetLayoutBtn = document.getElementById('reset-layout-btn');

    // Initialize
    initializeLayoutManager();

    /**
     * Initialize the layout manager
     */
    function initializeLayoutManager() {
        console.log('Initializing Layout Manager');

        // Apply saved layout on page load
        const savedLayout = localStorage.getItem('dashboard_view_mode') || 'adhd-optimized';
        console.log('Applying saved layout on page load:', savedLayout);

        // Apply with a slight delay to ensure DOM is ready
        setTimeout(() => {
            applyLayout(savedLayout);
        }, 200);

        // Setup layout button click handler
        if (layoutButton && layoutDropdown) {
            console.log('Setting up layout button click handler');

            // Remove any existing click handlers
            layoutButton.onclick = null;

            // Add new click handler
            layoutButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Layout button clicked');

                toggleLayoutDropdown(e);
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (layoutDropdown && !layoutDropdown.classList.contains('hidden') &&
                    !layoutButton.contains(e.target) && !layoutDropdown.contains(e.target)) {
                    console.log('Clicking outside, hiding dropdown');
                    hideLayoutDropdown();
                }
            });
        }

        // Setup widget control buttons
        setupWidgetControls();
    }

    /**
     * Setup widget control buttons
     */
    function setupWidgetControls() {
        const saveLayoutBtn = document.getElementById('save-layout-btn');
        const resetLayoutBtn = document.getElementById('reset-layout-btn');

        if (saveLayoutBtn) {
            console.log('Setting up save layout button');

            // Remove existing click handlers
            saveLayoutBtn.onclick = null;

            // Add new click handler
            saveLayoutBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Save layout button clicked');

                // Save the current widget arrangement
                saveWidgetArrangement();
            });
        }

        if (resetLayoutBtn) {
            console.log('Setting up reset layout button');

            // Remove existing click handlers
            resetLayoutBtn.onclick = null;

            // Add new click handler
            resetLayoutBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Reset layout button clicked');

                // Confirm before resetting
                if (confirm('Are you sure you want to reset the layout? This will restore the default arrangement.')) {
                    // Remove saved arrangement
                    localStorage.removeItem('dashboard_widget_arrangement');

                    // Reload the page to apply default layout
                    window.location.reload();
                }
            });
        }
    }

    // Setup layout options click handlers
    if (layoutOptions.length > 0) {
        console.log('Setting up layout options click handlers');

        layoutOptions.forEach(option => {
            // Remove existing click handlers
            option.onclick = null;

            // Add new click handler
            option.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const layoutName = this.getAttribute('data-view');
                console.log('Layout option clicked:', layoutName);

                // Apply layout
                applyLayout(layoutName);

                // Hide dropdown
                hideLayoutDropdown();
            });
        });
    }

    /**
     * Toggle the layout dropdown visibility
     */
    function toggleLayoutDropdown(event) {
        console.log('Toggling layout dropdown');

        if (!layoutDropdown) return;

        // Toggle dropdown visibility
        if (layoutDropdown.classList.contains('hidden')) {
            showLayoutDropdown(event);
        } else {
            hideLayoutDropdown();
        }
    }

    /**
     * Show the layout dropdown
     */
    function showLayoutDropdown(event) {
        console.log('Showing layout dropdown');

        if (!layoutDropdown) return;

        // Show dropdown
        layoutDropdown.classList.remove('hidden');
        layoutDropdown.style.display = 'block';

        // Position dropdown
        const buttonRect = event.currentTarget.getBoundingClientRect();
        layoutDropdown.style.position = 'fixed';
        layoutDropdown.style.top = (buttonRect.bottom + window.scrollY) + 'px';
        layoutDropdown.style.left = (buttonRect.left + window.scrollX) + 'px';
        layoutDropdown.style.zIndex = '9999';

        // Update ARIA attributes
        layoutButton.setAttribute('aria-expanded', 'true');
    }

    /**
     * Hide the layout dropdown
     */
    function hideLayoutDropdown() {
        console.log('Hiding layout dropdown');

        if (!layoutDropdown) return;

        // Hide dropdown
        layoutDropdown.classList.add('hidden');
        layoutDropdown.style.display = 'none';

        // Update ARIA attributes
        if (layoutButton) {
            layoutButton.setAttribute('aria-expanded', 'false');
        }
    }

    /**
     * Apply a layout
     */
    function applyLayout(layoutName) {
        console.log('Applying layout:', layoutName);

        if (!dashboardWidgets) {
            console.error('Dashboard widgets container not found');
            return;
        }

        // Update view mode text
        if (viewModeText) {
            viewModeText.textContent = layoutName.charAt(0).toUpperCase() + layoutName.slice(1) + ' View';
        }

        // Update data attributes
        dashboardWidgets.setAttribute('data-view-mode', layoutName);
        dashboardWidgets.setAttribute('data-arrangement', layoutName);

        // Save preference
        localStorage.setItem('dashboard_view_mode', layoutName);

        // Apply the appropriate layout
        switch (layoutName) {
            case 'adhd-optimized':
                applyADHDOptimizedLayout();
                break;
            case 'focus':
                applyFocusLayout();
                break;
            case 'standard':
                applyStandardLayout();
                break;
            case 'custom':
                applyCustomLayout();
                break;
        }

        // Dispatch a custom event to notify other scripts
        const event = new CustomEvent('layout-changed', {
            detail: {
                layout: layoutName
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * Apply ADHD Optimized Layout
     */
    function applyADHDOptimizedLayout() {
        console.log('Applying ADHD Optimized Layout');

        // Get all widgets
        const widgets = document.querySelectorAll('[data-widget]');
        const currentFocusWidget = document.getElementById('current-focus-widget');
        const widgetControls = document.getElementById('widget-controls');

        // Reset all widgets to visible
        widgets.forEach(widget => {
            widget.style.display = 'block';
            widget.style.order = '';
            widget.style.opacity = '1';
            widget.style.filter = '';
            widget.style.transform = '';
            widget.style.boxShadow = '';
            widget.style.border = '';
            widget.style.borderLeft = '4px solid transparent';
        });

        // Hide widget controls
        if (widgetControls) {
            widgetControls.style.display = 'none';
        }

        // Apply container styles
        dashboardWidgets.style.display = 'grid';
        dashboardWidgets.style.gridTemplateColumns = 'repeat(1, 1fr)';
        dashboardWidgets.style.backgroundColor = 'rgba(14, 165, 233, 0.08)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(14, 165, 233, 0.2)';
        dashboardWidgets.style.position = 'relative';

        // Add a label
        addLayoutLabel('ADHD Optimized Layout', '#0ea5e9');

        // Apply media queries for grid
        if (window.innerWidth >= 768) {
            dashboardWidgets.style.gridTemplateColumns = 'repeat(2, 1fr)';
        }

        // Apply specific widget styles
        if (currentFocusWidget) {
            currentFocusWidget.style.gridColumn = '1 / -1';
            currentFocusWidget.style.borderLeft = '6px solid rgba(14, 165, 233, 0.8)';
            currentFocusWidget.style.transform = 'scale(1.02)';
            currentFocusWidget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
        }

        // Apply colored borders to widgets
        widgets.forEach(widget => {
            const widgetType = widget.getAttribute('data-widget');

            if (widgetType === 'today-tasks') {
                widget.style.borderLeft = '4px solid rgba(59, 130, 246, 0.8)';
            } else if (widgetType === 'overdue-tasks') {
                widget.style.borderLeft = '4px solid rgba(239, 68, 68, 0.8)';
            } else if (widgetType === 'keyboard-shortcuts') {
                widget.style.borderLeft = '4px solid rgba(139, 92, 246, 0.8)';
            } else if (widgetType === 'adhd-guide') {
                widget.style.borderLeft = '4px solid rgba(16, 185, 129, 0.8)';
            } else if (widgetType === 'help-center') {
                widget.style.borderLeft = '4px solid rgba(245, 158, 11, 0.8)';
            }
        });
    }

    /**
     * Apply Focus Layout
     */
    function applyFocusLayout() {
        console.log('Applying Focus Layout');

        // Get all widgets
        const widgets = document.querySelectorAll('[data-widget]');
        const currentFocusWidget = document.getElementById('current-focus-widget');
        const todayTasksWidget = document.querySelector('[data-widget="today-tasks"]');
        const keyboardShortcutsWidget = document.querySelector('[data-widget="keyboard-shortcuts"]');
        const widgetControls = document.getElementById('widget-controls');

        // Hide widget controls
        if (widgetControls) {
            widgetControls.style.display = 'none';
        }

        // Apply container styles
        dashboardWidgets.style.display = 'flex';
        dashboardWidgets.style.flexDirection = 'column';
        dashboardWidgets.style.gap = '1.5rem';
        dashboardWidgets.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(99, 102, 241, 0.2)';
        dashboardWidgets.style.position = 'relative';

        // Add a label
        addLayoutLabel('Focus Mode', '#6366f1');

        // Hide all widgets except the essential ones
        widgets.forEach(widget => {
            const widgetType = widget.getAttribute('data-widget');

            if (widgetType === 'current-focus-widget' || widgetType === 'today-tasks' || widgetType === 'keyboard-shortcuts') {
                widget.style.display = 'block';
                widget.style.opacity = '1';
                widget.style.filter = '';
            } else {
                widget.style.display = 'none';
            }
        });

        // Apply specific widget styles
        if (currentFocusWidget) {
            currentFocusWidget.style.order = '-1';
            currentFocusWidget.style.borderLeft = '8px solid rgba(99, 102, 241, 0.8)';
            currentFocusWidget.style.transform = 'scale(1.03)';
            currentFocusWidget.style.boxShadow = '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
            currentFocusWidget.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
        }

        if (todayTasksWidget) {
            todayTasksWidget.style.order = '0';
            todayTasksWidget.style.borderLeft = '5px solid rgba(59, 130, 246, 0.8)';
            todayTasksWidget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
        }

        if (keyboardShortcutsWidget) {
            keyboardShortcutsWidget.style.order = '1';
            keyboardShortcutsWidget.style.borderLeft = '5px solid rgba(139, 92, 246, 0.8)';
            keyboardShortcutsWidget.style.maxHeight = '200px';
            keyboardShortcutsWidget.style.overflow = 'auto';
        }
    }

    /**
     * Apply Standard Layout
     */
    function applyStandardLayout() {
        console.log('Applying Standard Layout');

        // Get all widgets
        const widgets = document.querySelectorAll('[data-widget]');
        const currentFocusWidget = document.getElementById('current-focus-widget');
        const widgetControls = document.getElementById('widget-controls');

        // Reset all widgets to visible
        widgets.forEach(widget => {
            widget.style.display = 'block';
            widget.style.order = '';
            widget.style.opacity = '1';
            widget.style.filter = '';
            widget.style.transform = '';
            widget.style.boxShadow = '';
            widget.style.border = '1px solid rgba(0, 0, 0, 0.05)';
            widget.style.borderRadius = '0.5rem';
        });

        // Hide widget controls
        if (widgetControls) {
            widgetControls.style.display = 'none';
        }

        // Apply container styles
        dashboardWidgets.style.display = 'grid';
        dashboardWidgets.style.gridTemplateColumns = 'repeat(1, 1fr)';
        dashboardWidgets.style.gap = '1rem';
        dashboardWidgets.style.backgroundColor = 'rgba(0, 0, 0, 0.02)';
        dashboardWidgets.style.padding = '1.25rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(16, 185, 129, 0.2)';
        dashboardWidgets.style.position = 'relative';

        // Add a label
        addLayoutLabel('Standard Layout', '#10b981');

        // Apply media queries for grid
        if (window.innerWidth >= 768) {
            dashboardWidgets.style.gridTemplateColumns = 'repeat(2, 1fr)';
        }

        if (window.innerWidth >= 1024) {
            dashboardWidgets.style.gridTemplateColumns = 'repeat(3, 1fr)';
        }

        // Apply specific widget styles
        if (currentFocusWidget) {
            currentFocusWidget.style.gridColumn = '1 / -1';
            currentFocusWidget.style.borderLeft = '4px solid rgba(16, 185, 129, 0.8)';
        }
    }

    /**
     * Apply Custom Layout
     */
    function applyCustomLayout() {
        console.log('Applying Custom Layout');

        // Get all widgets
        const widgets = document.querySelectorAll('[data-widget]');
        const widgetControls = document.getElementById('widget-controls');

        // Reset all widgets to visible
        widgets.forEach(widget => {
            widget.style.display = 'block';
            widget.style.order = '';
            widget.style.opacity = '1';
            widget.style.filter = '';
            widget.style.transform = '';
            widget.style.boxShadow = '';
            widget.style.border = '2px dotted rgba(139, 92, 246, 0.3)';
            widget.style.position = 'relative';
            widget.style.cursor = 'move';
        });

        // Show widget controls
        if (widgetControls) {
            widgetControls.style.display = 'block';
            widgetControls.style.marginBottom = '1.5rem';
            widgetControls.style.padding = '1rem';
            widgetControls.style.backgroundColor = 'rgba(139, 92, 246, 0.05)';
            widgetControls.style.borderRadius = '0.5rem';
            widgetControls.style.border = '2px dashed rgba(139, 92, 246, 0.3)';
        }

        // Apply container styles
        dashboardWidgets.style.display = 'grid';
        dashboardWidgets.style.gridTemplateColumns = 'repeat(1, 1fr)';
        dashboardWidgets.style.gap = '1rem';
        dashboardWidgets.style.backgroundColor = 'rgba(139, 92, 246, 0.05)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.border = '2px dashed rgba(139, 92, 246, 0.3)';
        dashboardWidgets.style.boxShadow = 'none';
        dashboardWidgets.style.position = 'relative';

        // Add a label
        addLayoutLabel('Custom Layout', '#8b5cf6');

        // Apply media queries for grid
        if (window.innerWidth >= 768) {
            dashboardWidgets.style.gridTemplateColumns = 'repeat(2, 1fr)';
        }

        // Set data-view-mode attribute to trigger SortableJS initialization
        dashboardWidgets.setAttribute('data-view-mode', 'custom');

        // Load saved widget arrangement if available
        loadSavedArrangement();
    }

    /**
     * Helper function to add a layout label
     */
    function addLayoutLabel(labelText, color) {
        // Remove existing label if any
        const existingLabel = document.getElementById('layout-label');
        if (existingLabel) {
            existingLabel.remove();
        }

        // Create new label
        const label = document.createElement('div');
        label.id = 'layout-label';
        label.textContent = labelText;
        label.style.position = 'absolute';
        label.style.top = '-10px';
        label.style.left = '20px';
        label.style.backgroundColor = color;
        label.style.color = 'white';
        label.style.padding = '2px 10px';
        label.style.borderRadius = '4px';
        label.style.fontSize = '0.75rem';
        label.style.fontWeight = '600';
        label.style.zIndex = '10';

        // Add to dashboard widgets container
        if (dashboardWidgets) {
            dashboardWidgets.appendChild(label);
        }
    }

    /**
     * Load saved widget arrangement
     */
    function loadSavedArrangement() {
        console.log('Loading saved widget arrangement');

        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) {
            console.error('Dashboard widgets container not found');
            return;
        }

        const savedArrangement = localStorage.getItem('dashboard_widget_arrangement');
        if (!savedArrangement) {
            console.log('No saved arrangement found');
            return;
        }

        try {
            const arrangement = JSON.parse(savedArrangement);
            console.log('Found saved arrangement:', arrangement);

            // Reorder widgets based on saved arrangement
            arrangement.forEach(widgetId => {
                const widget = document.querySelector(`[data-widget="${widgetId}"]`) ||
                              document.getElementById(widgetId);

                if (widget) {
                    dashboardWidgets.appendChild(widget);
                }
            });

            console.log('Applied saved widget arrangement');
        } catch (error) {
            console.error('Error loading widget arrangement:', error);
        }
    }
});