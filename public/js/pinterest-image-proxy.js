/**
 * Pinterest Image Proxy
 *
 * This script handles downloading Pinterest images by using a client-side approach
 * that avoids the "Access Denied" errors from direct downloads.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Pinterest Image Proxy loaded');

    // Fix all download buttons
    fixDownloadButtons();

    // Set up a mutation observer to catch dynamically added buttons
    setupMutationObserver();

    /**
     * Fix all download buttons on the page
     */
    function fixDownloadButtons() {
        // Find all download buttons
        const downloadButtons = document.querySelectorAll('a[download^="pinterest_"]');
        console.log('Found download buttons:', downloadButtons.length);

        downloadButtons.forEach(function(button) {
            // Skip already fixed buttons
            if (button.getAttribute('data-download-fixed') === 'true') {
                return;
            }

            // Get the original URL and download filename
            const originalUrl = button.getAttribute('href');
            const downloadFilename = button.getAttribute('download');
            
            console.log('Original image URL:', originalUrl);

            // Clone the button to remove any existing event listeners
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            
            // Mark as fixed
            newButton.setAttribute('data-download-fixed', 'true');
            
            // Add a direct click handler
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                console.log('Download button clicked for:', originalUrl);
                
                // Use client-side approach to download the image
                downloadImage(originalUrl, downloadFilename);
                
                return false;
            });
        });
    }

    /**
     * Download an image using a client-side approach
     * 
     * @param {string} url The image URL
     * @param {string} filename The filename to save as
     */
    function downloadImage(url, filename) {
        // Show loading indicator
        showLoadingIndicator();

        // If the URL is from Pinterest's CDN or S3, use a proxy approach
        if (url.includes('i.pinimg.com') || url.includes('amazonaws.com')) {
            console.log('Using proxy approach for:', url);
            
            // Create a canvas to draw the image
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            // Handle CORS issues
            img.crossOrigin = 'Anonymous';
            
            img.onload = function() {
                // Set canvas dimensions to match the image
                canvas.width = img.width;
                canvas.height = img.height;
                
                // Draw the image on the canvas
                ctx.drawImage(img, 0, 0);
                
                try {
                    // Convert canvas to data URL and trigger download
                    const dataUrl = canvas.toDataURL('image/jpeg', 0.8);
                    triggerDownload(dataUrl, filename);
                    hideLoadingIndicator();
                } catch (error) {
                    console.error('Error creating data URL:', error);
                    fallbackToPlaceholder(filename);
                }
            };
            
            img.onerror = function() {
                console.error('Error loading image:', url);
                fallbackToPlaceholder(filename);
            };
            
            // Add a cache-busting parameter to avoid CORS issues
            img.src = url + (url.includes('?') ? '&' : '?') + 'cb=' + new Date().getTime();
        } else {
            // For other URLs, try direct download
            console.log('Using direct download for:', url);
            fetch(url)
                .then(response => response.blob())
                .then(blob => {
                    const objectUrl = URL.createObjectURL(blob);
                    triggerDownload(objectUrl, filename);
                    hideLoadingIndicator();
                })
                .catch(error => {
                    console.error('Error downloading image:', error);
                    fallbackToPlaceholder(filename);
                });
        }
    }

    /**
     * Trigger a download from a data URL or object URL
     * 
     * @param {string} url The data URL or object URL
     * @param {string} filename The filename to save as
     */
    function triggerDownload(url, filename) {
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    /**
     * Fall back to a placeholder image if download fails
     * 
     * @param {string} filename The filename to save as
     */
    function fallbackToPlaceholder(filename) {
        console.log('Falling back to placeholder image');
        
        // Create a placeholder image with text
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Set canvas dimensions
        canvas.width = 400;
        canvas.height = 300;
        
        // Fill background
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Add text
        ctx.fillStyle = '#dc3545';
        ctx.font = 'bold 20px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Image Download Failed', canvas.width / 2, canvas.height / 2 - 15);
        ctx.font = '16px Arial';
        ctx.fillText('Pinterest image could not be accessed', canvas.width / 2, canvas.height / 2 + 15);
        
        // Convert to data URL and trigger download
        const dataUrl = canvas.toDataURL('image/png');
        triggerDownload(dataUrl, filename);
        hideLoadingIndicator();
    }

    /**
     * Show a loading indicator
     */
    function showLoadingIndicator() {
        // Check if loading indicator already exists
        if (document.getElementById('download-loading-indicator')) {
            return;
        }
        
        // Create loading indicator
        const loadingIndicator = document.createElement('div');
        loadingIndicator.id = 'download-loading-indicator';
        loadingIndicator.style.position = 'fixed';
        loadingIndicator.style.top = '50%';
        loadingIndicator.style.left = '50%';
        loadingIndicator.style.transform = 'translate(-50%, -50%)';
        loadingIndicator.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        loadingIndicator.style.color = 'white';
        loadingIndicator.style.padding = '20px';
        loadingIndicator.style.borderRadius = '5px';
        loadingIndicator.style.zIndex = '9999';
        loadingIndicator.innerHTML = 'Downloading image... <span class="spinner"></span>';
        
        // Add spinner style
        const style = document.createElement('style');
        style.textContent = `
            .spinner {
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                border-top-color: white;
                animation: spin 1s ease-in-out infinite;
                vertical-align: middle;
                margin-left: 10px;
            }
            @keyframes spin {
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(loadingIndicator);
    }

    /**
     * Hide the loading indicator
     */
    function hideLoadingIndicator() {
        const loadingIndicator = document.getElementById('download-loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.remove();
        }
    }

    /**
     * Set up a mutation observer to fix dynamically added buttons
     */
    function setupMutationObserver() {
        // Create a mutation observer to watch for dynamically added buttons
        const observer = new MutationObserver(function(mutations) {
            let needsFixing = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    // Check if any of the added nodes contain download buttons
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.nodeType === 1) { // Element node
                            if (node.hasAttribute && node.hasAttribute('download') && 
                                node.getAttribute('download').startsWith('pinterest_')) {
                                needsFixing = true;
                                break;
                            } else if (node.querySelector) {
                                const hasButtons = node.querySelector('a[download^="pinterest_"]');
                                if (hasButtons) {
                                    needsFixing = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            });
            
            if (needsFixing) {
                console.log('Detected new download buttons, fixing...');
                fixDownloadButtons();
            }
        });
        
        // Start observing the document with the configured parameters
        observer.observe(document.body, { 
            childList: true, 
            subtree: true 
        });
        
        console.log('Mutation observer set up for download buttons');
    }
});
