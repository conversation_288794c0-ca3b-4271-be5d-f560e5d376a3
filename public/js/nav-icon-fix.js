/**
 * Navigation Icon Fix
 * 
 * This script ensures that Font Awesome icons load properly in the navigation bar
 * and provides additional enhancements for the navigation experience.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Force Font Awesome icon refresh
    refreshFontAwesomeIcons();
    
    // Add hover effects for better ADHD-friendly visual feedback
    addHoverEffects();
    
    // Ensure proper icon spacing
    fixIconSpacing();
    
    // Add keyboard navigation support
    setupKeyboardNavigation();
});

/**
 * Refreshes Font Awesome icons by temporarily hiding and showing them
 * This can help with rendering issues in some browsers
 */
function refreshFontAwesomeIcons() {
    // Get all Font Awesome icons in the navigation
    const navIcons = document.querySelectorAll('.two-tier-nav .fas, .two-tier-nav .far, .two-tier-nav .fab');
    
    // Force a reflow of each icon
    navIcons.forEach(icon => {
        // Save the original display value
        const originalDisplay = window.getComputedStyle(icon).display;
        
        // Hide and immediately show to force a refresh
        icon.style.display = 'none';
        
        // Force reflow
        void icon.offsetHeight;
        
        // Restore original display
        icon.style.display = originalDisplay;
    });
    
    console.log('Font Awesome icons refreshed:', navIcons.length);
}

/**
 * Adds enhanced hover effects for better visual feedback
 * Especially helpful for users with ADHD
 */
function addHoverEffects() {
    // Primary navigation items
    const primaryItems = document.querySelectorAll('.nav-item-primary');
    primaryItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            // Slightly scale up the icon for visual feedback
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'scale(1.2)';
                icon.style.transition = 'transform 0.15s ease';
            }
        });
        
        item.addEventListener('mouseleave', function() {
            // Reset icon scale
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'scale(1)';
            }
        });
    });
    
    // Secondary navigation items
    const secondaryItems = document.querySelectorAll('.nav-item-secondary');
    secondaryItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            // Slightly scale up the icon for visual feedback
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'scale(1.2)';
                icon.style.transition = 'transform 0.15s ease';
            }
        });
        
        item.addEventListener('mouseleave', function() {
            // Reset icon scale
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'scale(1)';
            }
        });
    });
}

/**
 * Ensures proper spacing between icons and text
 */
function fixIconSpacing() {
    // Get all navigation items with icons
    const navItems = document.querySelectorAll('.nav-item-primary, .nav-item-secondary');
    
    navItems.forEach(item => {
        const icon = item.querySelector('i');
        if (icon) {
            // Ensure consistent margin
            icon.style.marginRight = '0.375rem';
            
            // Ensure consistent width for better alignment
            icon.style.width = '1rem';
            icon.style.textAlign = 'center';
        }
    });
}

/**
 * Sets up keyboard navigation for accessibility
 */
function setupKeyboardNavigation() {
    // Get all interactive elements in the navigation
    const navElements = document.querySelectorAll('.nav-item-primary, .nav-item-secondary, .nav-dropdown button');
    
    navElements.forEach((element, index) => {
        element.addEventListener('keydown', function(e) {
            // Handle arrow key navigation
            switch(e.key) {
                case 'ArrowRight':
                    e.preventDefault();
                    // Focus next element
                    if (index < navElements.length - 1) {
                        navElements[index + 1].focus();
                    }
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    // Focus previous element
                    if (index > 0) {
                        navElements[index - 1].focus();
                    }
                    break;
                case 'ArrowDown':
                    // If this is a dropdown button, open the dropdown
                    if (element.classList.contains('nav-dropdown') || element.closest('.nav-dropdown')) {
                        e.preventDefault();
                        const dropdown = element.closest('.nav-dropdown');
                        const menu = dropdown.querySelector('.nav-dropdown-menu');
                        if (menu) {
                            menu.classList.add('show');
                            // Focus first item in dropdown
                            const firstItem = menu.querySelector('a');
                            if (firstItem) {
                                firstItem.focus();
                            }
                        }
                    }
                    break;
                case 'Escape':
                    // Close any open dropdowns
                    const openDropdowns = document.querySelectorAll('.nav-dropdown-menu.show');
                    openDropdowns.forEach(dropdown => {
                        dropdown.classList.remove('show');
                    });
                    break;
            }
        });
    });
    
    // Handle keyboard navigation within dropdowns
    const dropdownMenus = document.querySelectorAll('.nav-dropdown-menu');
    dropdownMenus.forEach(menu => {
        const items = menu.querySelectorAll('a');
        
        items.forEach((item, itemIndex) => {
            item.addEventListener('keydown', function(e) {
                switch(e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        // Focus next item
                        if (itemIndex < items.length - 1) {
                            items[itemIndex + 1].focus();
                        }
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        // Focus previous item
                        if (itemIndex > 0) {
                            items[itemIndex - 1].focus();
                        } else {
                            // Focus the dropdown button
                            const button = menu.closest('.nav-dropdown').querySelector('button');
                            if (button) {
                                button.focus();
                            }
                        }
                        break;
                    case 'Escape':
                        e.preventDefault();
                        // Close dropdown and focus the button
                        menu.classList.remove('show');
                        const button = menu.closest('.nav-dropdown').querySelector('button');
                        if (button) {
                            button.focus();
                        }
                        break;
                }
            });
        });
    });
}
