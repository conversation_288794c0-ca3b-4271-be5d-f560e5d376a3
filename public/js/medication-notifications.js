/**
 * ADHD Medication Notification System
 * 
 * Handles real-time medication reminders with ADHD-friendly features
 */

class MedicationNotificationSystem {
    constructor() {
        this.notifications = [];
        this.checkInterval = null;
        this.soundEnabled = true;
        this.browserNotificationsEnabled = false;
        this.snoozeTimers = new Map();
        
        this.init();
    }
    
    async init() {
        // Request notification permission
        await this.requestNotificationPermission();
        
        // Create notification container
        this.createNotificationContainer();
        
        // Start checking for notifications
        this.startNotificationCheck();
        
        // Setup event listeners
        this.setupEventListeners();
        
        console.log('Medication Notification System initialized');
    }
    
    async requestNotificationPermission() {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            this.browserNotificationsEnabled = permission === 'granted';
            
            if (this.browserNotificationsEnabled) {
                console.log('Browser notifications enabled');
            }
        }
    }
    
    createNotificationContainer() {
        if (document.getElementById('medication-notifications')) return;
        
        const container = document.createElement('div');
        container.id = 'medication-notifications';
        container.className = 'fixed top-4 right-4 z-50 space-y-2 max-w-sm';
        document.body.appendChild(container);
    }
    
    startNotificationCheck() {
        // Check immediately
        this.checkForNotifications();
        
        // Then check every 30 seconds
        this.checkInterval = setInterval(() => {
            this.checkForNotifications();
        }, 30000);
    }
    
    async checkForNotifications() {
        try {
            const response = await fetch('/momentum/api/medication-notifications', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            });
            
            if (response.ok) {
                const data = await response.json();
                this.updateNotifications(data.notifications || []);
            }
        } catch (error) {
            console.error('Failed to check notifications:', error);
        }
    }
    
    updateNotifications(newNotifications) {
        // Remove notifications that are no longer active
        this.notifications = this.notifications.filter(notification => {
            const stillActive = newNotifications.some(n => n.id === notification.id);
            if (!stillActive) {
                this.removeNotificationElement(notification.id);
            }
            return stillActive;
        });
        
        // Add new notifications
        newNotifications.forEach(notification => {
            const existing = this.notifications.find(n => n.id === notification.id);
            if (!existing) {
                this.notifications.push(notification);
                this.showNotification(notification);
            }
        });
    }
    
    showNotification(notification) {
        // Show browser notification for high priority
        if (this.browserNotificationsEnabled && notification.priority === 'high') {
            this.showBrowserNotification(notification);
        }
        
        // Play sound for urgent notifications
        if (this.soundEnabled && notification.priority === 'urgent') {
            this.playNotificationSound();
        }
        
        // Show in-app notification
        this.showInAppNotification(notification);
    }
    
    showBrowserNotification(notification) {
        const browserNotification = new Notification(notification.title, {
            body: notification.message,
            icon: '/momentum/assets/images/pill-icon.png',
            badge: '/momentum/assets/images/pill-icon.png',
            tag: notification.id,
            requireInteraction: notification.priority === 'urgent',
            actions: notification.actions?.slice(0, 2).map(action => ({
                action: action.action,
                title: action.label
            })) || []
        });
        
        browserNotification.onclick = () => {
            window.focus();
            this.focusNotification(notification.id);
            browserNotification.close();
        };
        
        // Auto-close after 10 seconds for non-urgent notifications
        if (notification.priority !== 'urgent') {
            setTimeout(() => browserNotification.close(), 10000);
        }
    }
    
    showInAppNotification(notification) {
        const container = document.getElementById('medication-notifications');
        if (!container) return;
        
        const notificationElement = this.createNotificationElement(notification);
        container.appendChild(notificationElement);
        
        // Animate in
        setTimeout(() => {
            notificationElement.classList.remove('translate-x-full', 'opacity-0');
        }, 10);
        
        // Auto-remove non-urgent notifications after 30 seconds
        if (notification.priority !== 'urgent') {
            setTimeout(() => {
                this.removeNotificationElement(notification.id);
            }, 30000);
        }
    }
    
    createNotificationElement(notification) {
        const element = document.createElement('div');
        element.id = `notification-${notification.id}`;
        element.className = `transform translate-x-full opacity-0 transition-all duration-300 ${this.getNotificationClasses(notification)}`;
        
        const timeInfo = notification.minutes_until !== undefined 
            ? `in ${notification.minutes_until} minutes`
            : notification.minutes_overdue !== undefined 
                ? `${notification.minutes_overdue} minutes overdue`
                : '';
        
        element.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center ${this.getIconClasses(notification)}">
                        <i class="fas fa-pills text-sm"></i>
                    </div>
                </div>
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">
                            ${notification.title}
                        </p>
                        <button onclick="medicationNotifications.dismissNotification('${notification.id}')" 
                                class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <i class="fas fa-times text-xs"></i>
                        </button>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                        ${notification.message}
                    </p>
                    ${timeInfo ? `<p class="text-xs text-gray-500 dark:text-gray-400 mt-1">${timeInfo}</p>` : ''}
                    <div class="flex space-x-2 mt-3">
                        ${notification.actions?.map(action => `
                            <button onclick="medicationNotifications.handleAction('${notification.id}', '${action.action}')"
                                    class="px-3 py-1 text-xs font-medium rounded-md ${this.getActionButtonClasses(action.action)}">
                                ${action.label}
                            </button>
                        `).join('') || ''}
                    </div>
                </div>
            </div>
        `;
        
        return element;
    }
    
    getNotificationClasses(notification) {
        const baseClasses = 'bg-white dark:bg-gray-800 border rounded-lg shadow-lg p-4 max-w-sm';
        
        switch (notification.priority) {
            case 'urgent':
                return `${baseClasses} border-red-500 bg-red-50 dark:bg-red-900/20`;
            case 'high':
                return `${baseClasses} border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20`;
            default:
                return `${baseClasses} border-blue-500 bg-blue-50 dark:bg-blue-900/20`;
        }
    }
    
    getIconClasses(notification) {
        switch (notification.priority) {
            case 'urgent':
                return 'bg-red-500 text-white';
            case 'high':
                return 'bg-yellow-500 text-white';
            default:
                return 'bg-blue-500 text-white';
        }
    }
    
    getActionButtonClasses(action) {
        switch (action) {
            case 'mark_taken':
                return 'bg-green-600 text-white hover:bg-green-700';
            case 'snooze_15':
                return 'bg-yellow-600 text-white hover:bg-yellow-700';
            case 'skip':
            case 'skip_today':
                return 'bg-gray-600 text-white hover:bg-gray-700';
            default:
                return 'bg-blue-600 text-white hover:bg-blue-700';
        }
    }
    
    async handleAction(notificationId, action) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (!notification) return;
        
        try {
            const response = await fetch('/momentum/api/medication-notifications/action', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    action: action,
                    medication_id: notification.medication_id,
                    reminder_id: notification.reminder_id
                })
            });
            
            if (response.ok) {
                const result = await response.json();
                
                if (result.success) {
                    this.showSuccessMessage(result.message);
                    this.removeNotificationElement(notificationId);
                    
                    // Remove from notifications array
                    this.notifications = this.notifications.filter(n => n.id !== notificationId);
                    
                    // Handle snooze
                    if (action === 'snooze_15') {
                        this.handleSnooze(notification, 15);
                    }
                } else {
                    this.showErrorMessage(result.message);
                }
            }
        } catch (error) {
            console.error('Failed to handle action:', error);
            this.showErrorMessage('Failed to process action');
        }
    }
    
    handleSnooze(notification, minutes) {
        // Set a timer to re-show the notification
        const snoozeTimer = setTimeout(() => {
            this.showNotification({
                ...notification,
                id: notification.id + '_snoozed',
                title: 'Snoozed Reminder',
                message: `Reminder: ${notification.message}`
            });
            this.snoozeTimers.delete(notification.id);
        }, minutes * 60 * 1000);
        
        this.snoozeTimers.set(notification.id, snoozeTimer);
    }
    
    dismissNotification(notificationId) {
        this.removeNotificationElement(notificationId);
        this.notifications = this.notifications.filter(n => n.id !== notificationId);
    }
    
    removeNotificationElement(notificationId) {
        const element = document.getElementById(`notification-${notificationId}`);
        if (element) {
            element.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => {
                if (element.parentNode) {
                    element.parentNode.removeChild(element);
                }
            }, 300);
        }
    }
    
    focusNotification(notificationId) {
        const element = document.getElementById(`notification-${notificationId}`);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
            element.classList.add('ring-2', 'ring-blue-500');
            setTimeout(() => {
                element.classList.remove('ring-2', 'ring-blue-500');
            }, 2000);
        }
    }
    
    playNotificationSound() {
        if (!this.soundEnabled) return;
        
        try {
            const audio = new Audio('/momentum/assets/sounds/medication-reminder.mp3');
            audio.volume = 0.5;
            audio.play().catch(e => console.log('Could not play notification sound:', e));
        } catch (error) {
            console.log('Notification sound not available');
        }
    }
    
    showSuccessMessage(message) {
        this.showToast(message, 'success');
    }
    
    showErrorMessage(message) {
        this.showToast(message, 'error');
    }
    
    showToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `fixed bottom-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white transform transition-transform duration-300 translate-y-full ${
            type === 'success' ? 'bg-green-500' : 'bg-red-500'
        }`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.classList.remove('translate-y-full');
        }, 10);
        
        setTimeout(() => {
            toast.classList.add('translate-y-full');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
    
    setupEventListeners() {
        // Listen for page visibility changes to check notifications when page becomes visible
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.checkForNotifications();
            }
        });
        
        // Listen for focus events
        window.addEventListener('focus', () => {
            this.checkForNotifications();
        });
    }
    
    destroy() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
        }
        
        // Clear snooze timers
        this.snoozeTimers.forEach(timer => clearTimeout(timer));
        this.snoozeTimers.clear();
        
        // Remove notification container
        const container = document.getElementById('medication-notifications');
        if (container) {
            container.remove();
        }
    }
}

// Initialize the notification system when the page loads
let medicationNotifications;

document.addEventListener('DOMContentLoaded', () => {
    medicationNotifications = new MedicationNotificationSystem();
});

// Clean up when page unloads
window.addEventListener('beforeunload', () => {
    if (medicationNotifications) {
        medicationNotifications.destroy();
    }
});
