/**
 * Focus Button Handler
 *
 * This script handles the focus button functionality in the dashboard.
 * It allows users to set a task as the current focus task and clear the current focus task.
 */

// Function to initialize focus button handlers
function initializeFocusButtons() {
    console.log('Initializing focus button handlers');

    // Get elements
    const setFocusBtns = document.querySelectorAll('.set-focus-btn');
    const clearFocusBtn = document.getElementById('clear-focus-task');
    const markCompleteBtn = document.getElementById('mark-complete-btn');
    const focusModeToggle = document.getElementById('focus-mode-toggle');

    console.log('Found focus buttons:', setFocusBtns.length);
    console.log('Clear focus button:', clearFocusBtn ? 'Found' : 'Not found');
    console.log('Mark complete button:', markCompleteBtn ? 'Found' : 'Not found');
    console.log('Focus mode toggle:', focusModeToggle ? 'Found' : 'Not found');

    // Function to show success message
    function showSuccessMessage(message) {
        console.log('Showing success message:', message);

        // Create message element if it doesn't exist
        let messageElement = document.getElementById('success-message');

        if (!messageElement) {
            messageElement = document.createElement('div');
            messageElement.id = 'success-message';
            messageElement.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50 transform transition-all duration-300 opacity-0 translate-y-[-20px]';
            document.body.appendChild(messageElement);
        }

        // Set message text
        messageElement.textContent = message;

        // Show message with animation
        setTimeout(() => {
            messageElement.classList.remove('opacity-0', 'translate-y-[-20px]');
            messageElement.classList.add('opacity-100', 'translate-y-0');
        }, 10);

        // Hide message after delay
        setTimeout(() => {
            messageElement.classList.remove('opacity-100', 'translate-y-0');
            messageElement.classList.add('opacity-0', 'translate-y-[-20px]');
        }, 3000);
    }

    // Function to set focus task
    function setFocusTask(taskId) {
        console.log('Setting focus task with ID:', taskId);

        return fetch('/momentum/dashboard/set-focus-task', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ task_id: taskId })
        })
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('Error in setFocusTask:', error);
            throw error;
        });
    }

    // Function to clear focus task
    function clearFocusTask() {
        console.log('Clearing focus task');

        return fetch('/momentum/dashboard/clear-focus-task', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('Clear focus response status:', response.status);
            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('Error in clearFocusTask:', error);
            throw error;
        });
    }

    // Handle Set Focus buttons
    setFocusBtns.forEach((button, index) => {
        console.log(`Adding click handler to focus button ${index + 1}`);

        // Remove any existing event listeners
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        // Add new event listener
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const taskId = this.getAttribute('data-task-id');
            console.log('Focus button clicked for task ID:', taskId);

            setFocusTask(taskId)
            .then(data => {
                if (data.success) {
                    showSuccessMessage('Focus task set successfully!');
                    // Reload the page to refresh the widgets
                    window.location.reload();
                } else {
                    alert('Failed to set focus task. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            });
        });
    });

    // Handle Clear Focus button
    if (clearFocusBtn) {
        console.log('Adding click handler to clear focus button');

        // Remove any existing event listeners
        const newClearBtn = clearFocusBtn.cloneNode(true);
        clearFocusBtn.parentNode.replaceChild(newClearBtn, clearFocusBtn);

        // Add new event listener
        newClearBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Clear focus button clicked');

            clearFocusTask()
            .then(data => {
                if (data.success) {
                    showSuccessMessage('Focus task cleared successfully!');
                    // Reload the page to refresh the widgets
                    window.location.reload();
                } else {
                    alert('Failed to clear focus task. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            });
        });
    }

    // Handle Focus Mode toggle
    if (focusModeToggle) {
        console.log('Adding click handler to focus mode toggle');

        // Remove any existing event listeners
        const newToggle = focusModeToggle.cloneNode(true);
        focusModeToggle.parentNode.replaceChild(newToggle, focusModeToggle);

        // Add new event listener
        newToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Focus mode toggle clicked');

            const taskId = markCompleteBtn ? markCompleteBtn.getAttribute('data-task-id') : null;

            if (taskId) {
                window.location.href = `/momentum/productivity/focus-mode?task_id=${taskId}`;
            } else {
                alert('Please set a focus task first.');
            }
        });
    }
}

// Initialize on DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Focus Button Handler loaded');
    initializeFocusButtons();
});

// Also initialize after a short delay to ensure all elements are loaded
setTimeout(function() {
    console.log('Delayed initialization of focus buttons');
    initializeFocusButtons();
}, 500);
