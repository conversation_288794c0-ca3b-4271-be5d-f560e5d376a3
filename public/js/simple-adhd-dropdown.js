/**
 * Simple ADHD Dropdown Fix
 * 
 * This script provides a very simple, direct implementation for the ADHD dropdown menu
 * without any dependencies or complex interactions.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get the ADHD dropdown button and menu
    const adhdButton = document.getElementById('adhd-dropdown-button');
    const adhdMenu = document.getElementById('adhd-dropdown-menu');
    
    if (!adhdButton || !adhdMenu) {
        console.error('ADHD dropdown elements not found');
        return;
    }
    
    // Create a simple overlay for capturing clicks outside
    let overlay = document.createElement('div');
    overlay.id = 'adhd-dropdown-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100vw';
    overlay.style.height = '100vh';
    overlay.style.backgroundColor = 'transparent';
    overlay.style.zIndex = '99998';
    overlay.style.display = 'none';
    document.body.appendChild(overlay);
    
    // Function to position the menu
    function positionMenu() {
        const buttonRect = adhdButton.getBoundingClientRect();
        
        // Set the menu position
        adhdMenu.style.position = 'fixed';
        adhdMenu.style.top = (buttonRect.bottom + 5) + 'px';
        adhdMenu.style.left = buttonRect.left + 'px';
        adhdMenu.style.zIndex = '99999';
        adhdMenu.style.minWidth = '200px';
        adhdMenu.style.backgroundColor = document.documentElement.classList.contains('dark') ? '#374151' : 'white';
        adhdMenu.style.borderRadius = '0.375rem';
        adhdMenu.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
        adhdMenu.style.maxHeight = '400px';
        adhdMenu.style.overflowY = 'auto';
        
        // Check if menu would go off screen
        const menuRect = adhdMenu.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // Handle horizontal positioning
        if (menuRect.right > viewportWidth) {
            adhdMenu.style.left = 'auto';
            adhdMenu.style.right = (viewportWidth - buttonRect.right) + 'px';
        }
        
        // Handle vertical positioning
        if (menuRect.bottom > viewportHeight) {
            const spaceBelow = viewportHeight - buttonRect.bottom - 10;
            adhdMenu.style.maxHeight = spaceBelow + 'px';
        }
    }
    
    // Function to show the menu
    function showMenu() {
        // Position the menu first
        positionMenu();
        
        // Show the menu
        adhdMenu.style.display = 'block';
        adhdMenu.style.visibility = 'visible';
        adhdMenu.style.opacity = '1';
        
        // Show the overlay
        overlay.style.display = 'block';
        
        // Update ARIA attributes
        adhdButton.setAttribute('aria-expanded', 'true');
    }
    
    // Function to hide the menu
    function hideMenu() {
        adhdMenu.style.display = 'none';
        adhdMenu.style.visibility = 'hidden';
        adhdMenu.style.opacity = '0';
        
        // Hide the overlay
        overlay.style.display = 'none';
        
        // Update ARIA attributes
        adhdButton.setAttribute('aria-expanded', 'false');
    }
    
    // Toggle menu on button click
    adhdButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        if (adhdMenu.style.display === 'block') {
            hideMenu();
        } else {
            showMenu();
        }
    });
    
    // Hide menu when clicking outside
    overlay.addEventListener('click', hideMenu);
    
    // Hide menu when pressing Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && adhdMenu.style.display === 'block') {
            hideMenu();
        }
    });
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (adhdMenu.style.display === 'block') {
            positionMenu();
        }
    });
    
    // Handle window scroll
    window.addEventListener('scroll', function() {
        if (adhdMenu.style.display === 'block') {
            positionMenu();
        }
    });
    
    // Initialize menu state
    hideMenu();
});
