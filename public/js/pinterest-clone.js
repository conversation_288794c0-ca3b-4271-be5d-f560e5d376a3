/**
 * Pinterest Clone Research Tool
 *
 * JavaScript functionality for the Pinterest clone research tool
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Pinterest Clone Research Tool loaded');

    // Initialize components
    initScraper();
    initTrends();
    initAnalysis();
    initPinViewer();
});

/**
 * Initialize the Pinterest scraper functionality
 */
function initScraper() {
    const scraperForm = document.getElementById('scraper-form');
    
    if (scraperForm) {
        console.log('Initializing Pinterest scraper');
        
        // Toggle advanced options
        const toggleAdvanced = document.getElementById('toggle-advanced');
        const advancedOptions = document.getElementById('advanced-options');
        const advancedShow = document.getElementById('advanced-show');
        const advancedHide = document.getElementById('advanced-hide');
        
        if (toggleAdvanced && advancedOptions && advancedShow && advancedHide) {
            toggleAdvanced.addEventListener('click', function() {
                advancedOptions.classList.toggle('hidden');
                advancedShow.classList.toggle('hidden');
                advancedHide.classList.toggle('hidden');
            });
        }
        
        // Form submission
        scraperForm.addEventListener('submit', function(e) {
            // Add form validation if needed
            const searchTerm = document.getElementById('search_term');
            
            if (!searchTerm || !searchTerm.value.trim()) {
                e.preventDefault();
                alert('Please enter a search term');
                return false;
            }
            
            // Show loading state
            const submitButton = scraperForm.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';
            }
            
            return true;
        });
    }
}

/**
 * Initialize the Pinterest trends visualization
 */
function initTrends() {
    // Check if we're on the trends page
    const categoryTrendsChart = document.getElementById('categoryTrendsChart');
    const engagementMetricsChart = document.getElementById('engagementMetricsChart');
    
    if (categoryTrendsChart || engagementMetricsChart) {
        console.log('Initializing Pinterest trends visualization');
        
        // Handle trend period change
        const trendPeriod = document.getElementById('trend-period');
        if (trendPeriod) {
            trendPeriod.addEventListener('change', function() {
                // In a real implementation, this would fetch new data based on the selected period
                console.log('Trend period changed to:', this.value);
                
                // Show loading state
                if (categoryTrendsChart) {
                    categoryTrendsChart.style.opacity = '0.5';
                }
                if (engagementMetricsChart) {
                    engagementMetricsChart.style.opacity = '0.5';
                }
                
                // Simulate loading delay
                setTimeout(function() {
                    if (categoryTrendsChart) {
                        categoryTrendsChart.style.opacity = '1';
                    }
                    if (engagementMetricsChart) {
                        engagementMetricsChart.style.opacity = '1';
                    }
                    
                    alert('In a real implementation, this would load data for the selected period: ' + trendPeriod.value + ' days');
                }, 500);
            });
        }
        
        // Handle engagement metric buttons
        const engagementButtons = document.querySelectorAll('.engagement-metric-btn');
        if (engagementButtons.length > 0 && engagementMetricsChart) {
            engagementButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const metric = this.getAttribute('data-metric');
                    
                    // Update active button styling
                    engagementButtons.forEach(btn => {
                        btn.classList.remove('bg-red-100', 'text-red-800', 'dark:bg-red-900', 'dark:text-red-200', 'border-2', 'border-red-500');
                        btn.classList.add('bg-gray-100', 'text-gray-800', 'dark:bg-gray-700', 'dark:text-gray-200');
                    });
                    this.classList.remove('bg-gray-100', 'text-gray-800', 'dark:bg-gray-700', 'dark:text-gray-200');
                    this.classList.add('bg-red-100', 'text-red-800', 'dark:bg-red-900', 'dark:text-red-200', 'border-2', 'border-red-500');
                    
                    // In a real implementation, this would update the chart data
                    console.log('Engagement metric changed to:', metric);
                });
            });
        }
    }
}

/**
 * Initialize the Pinterest visual analysis functionality
 */
function initAnalysis() {
    // Check if we're on the analysis page
    const visualCategoryButtons = document.querySelectorAll('.visual-category-btn');
    
    if (visualCategoryButtons.length > 0) {
        console.log('Initializing Pinterest visual analysis');
        
        // Handle visual category buttons
        visualCategoryButtons.forEach(button => {
            button.addEventListener('click', function() {
                const category = this.getAttribute('data-category');
                
                // Update active button styling
                visualCategoryButtons.forEach(btn => {
                    btn.classList.remove('bg-red-100', 'text-red-800', 'dark:bg-red-900', 'dark:text-red-200', 'border-2', 'border-red-500');
                    btn.classList.add('bg-gray-100', 'text-gray-800', 'dark:bg-gray-700', 'dark:text-gray-200');
                });
                this.classList.remove('bg-gray-100', 'text-gray-800', 'dark:bg-gray-700', 'dark:text-gray-200');
                this.classList.add('bg-red-100', 'text-red-800', 'dark:bg-red-900', 'dark:text-red-200', 'border-2', 'border-red-500');
                
                // In a real implementation, this would filter the pins by category
                console.log('Filtering by category:', category);
            });
        });
    }
}

/**
 * Initialize the Pinterest pin viewer functionality
 */
function initPinViewer() {
    // Check if we're on the pin view page
    const pinDetailsButtons = document.querySelectorAll('.pin-details-btn');
    
    if (pinDetailsButtons.length > 0) {
        console.log('Initializing Pinterest pin viewer');
        
        const modal = document.getElementById('pin-details-modal');
        const closeModal = document.getElementById('close-modal');
        const pinDetailsContent = document.getElementById('pin-details-content');
        
        // Open modal when details button is clicked
        pinDetailsButtons.forEach(button => {
            button.addEventListener('click', function() {
                const pinId = this.getAttribute('data-pin-id');
                
                // Show loading state
                if (modal) {
                    modal.classList.remove('hidden');
                }
                
                // In a real implementation, this would fetch pin details from the server
                console.log('Viewing pin details for ID:', pinId);
                
                // Simulate loading delay
                setTimeout(() => {
                    // In a real implementation, this would populate the modal with pin data
                    if (pinDetailsContent) {
                        // This would be replaced with actual data in a real implementation
                        console.log('Pin details loaded');
                    }
                }, 500);
            });
        });
        
        // Close modal when close button is clicked
        if (closeModal && modal) {
            closeModal.addEventListener('click', function() {
                modal.classList.add('hidden');
            });
        }
        
        // Close modal when clicking outside
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });
        }
    }
    
    // Handle pin sorting
    const sortSelect = document.getElementById('sort-pins');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            // In a real implementation, this would sort the pins
            console.log('Sorting pins by:', this.value);
        });
    }
}
