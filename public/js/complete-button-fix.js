/**
 * Complete Button Fix
 * 
 * This script fixes the "Complete" button in the Current Focus widget.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Complete Button Fix loaded');
    
    // Fix the mark complete button
    fixMarkCompleteButton();
    
    /**
     * Fix the mark complete button
     */
    function fixMarkCompleteButton() {
        const markCompleteBtn = document.getElementById('mark-complete-btn');
        
        if (!markCompleteBtn) {
            console.log('Mark complete button not found');
            return;
        }
        
        console.log('Fixing mark complete button');
        
        // Remove any existing event listeners
        const newButton = markCompleteBtn.cloneNode(true);
        markCompleteBtn.parentNode.replaceChild(newButton, markCompleteBtn);
        
        // Add direct click handler
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const taskId = this.getAttribute('data-task-id');
            console.log('Mark complete button clicked (fixed):', taskId);
            
            // Add visual feedback
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1.5"></i> Processing...';
            this.classList.add('opacity-75');
            
            // Send AJAX request to mark task as complete using GET method
            fetch(`/momentum/tasks/complete/${taskId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Mark complete response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Task marked as complete:', data);
                
                if (data.success) {
                    showSuccessMessage('Task completed successfully!');
                    
                    // Reload the page after a short delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    console.error('Failed to complete task:', data.message);
                    
                    // Reset button
                    this.innerHTML = '<i class="fas fa-check mr-1.5"></i> Complete';
                    this.classList.remove('opacity-75');
                    
                    showErrorMessage('Failed to mark task as complete. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error completing task:', error);
                
                // Reset button
                this.innerHTML = '<i class="fas fa-check mr-1.5"></i> Complete';
                this.classList.remove('opacity-75');
                
                showErrorMessage('An error occurred. Please try again.');
            });
        });
    }
    
    /**
     * Show success message
     */
    function showSuccessMessage(message) {
        showMessage(message, 'success');
    }
    
    /**
     * Show error message
     */
    function showErrorMessage(message) {
        showMessage(message, 'error');
    }
    
    /**
     * Show message
     */
    function showMessage(message, type = 'success') {
        console.log(`Showing ${type} message:`, message);
        
        // Create message element if it doesn't exist
        let messageElement = document.getElementById(`${type}-message`);
        
        if (!messageElement) {
            messageElement = document.createElement('div');
            messageElement.id = `${type}-message`;
            
            const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
            
            messageElement.className = `fixed top-4 right-4 ${bgColor} text-white px-4 py-2 rounded shadow-lg z-50 transform transition-all duration-300 opacity-0 translate-y-[-20px]`;
            document.body.appendChild(messageElement);
        }
        
        // Set message text
        messageElement.textContent = message;
        
        // Show message with animation
        setTimeout(() => {
            messageElement.classList.remove('opacity-0', 'translate-y-[-20px]');
            messageElement.classList.add('opacity-100', 'translate-y-0');
        }, 10);
        
        // Hide message after delay
        setTimeout(() => {
            messageElement.classList.remove('opacity-100', 'translate-y-0');
            messageElement.classList.add('opacity-0', 'translate-y-[-20px]');
        }, 3000);
    }
});
