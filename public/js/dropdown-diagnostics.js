/**
 * Dropdown Menu Diagnostics
 * 
 * This script helps diagnose issues with dropdown menus by logging events
 * and state changes. It doesn't modify behavior, just observes and reports.
 */

(function() {
    // Enable verbose logging
    const DEBUG = true;
    
    // Store references to all dropdown elements
    const dropdowns = [
        {
            name: 'ADHD',
            button: document.getElementById('adhd-dropdown-button'),
            menu: document.getElementById('adhd-dropdown-menu'),
            container: document.getElementById('adhd-dropdown')
        },
        {
            name: 'Productivity',
            button: document.getElementById('productivity-dropdown-button'),
            menu: document.getElementById('productivity-dropdown-menu'),
            container: document.getElementById('productivity-dropdown')
        },
        {
            name: 'Tools',
            button: document.getElementById('tools-dropdown-button'),
            menu: document.getElementById('tools-dropdown-menu'),
            container: document.getElementById('tools-dropdown')
        },
        {
            name: 'User',
            button: document.getElementById('user-menu-button'),
            menu: document.getElementById('user-dropdown-menu'),
            container: document.querySelector('.relative:has(#user-menu-button)')
        }
    ];
    
    // Create a visual debug panel
    function createDebugPanel() {
        const panel = document.createElement('div');
        panel.id = 'dropdown-debug-panel';
        panel.style.position = 'fixed';
        panel.style.bottom = '10px';
        panel.style.right = '10px';
        panel.style.width = '300px';
        panel.style.maxHeight = '200px';
        panel.style.overflowY = 'auto';
        panel.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        panel.style.color = 'white';
        panel.style.padding = '10px';
        panel.style.borderRadius = '5px';
        panel.style.fontFamily = 'monospace';
        panel.style.fontSize = '12px';
        panel.style.zIndex = '99999';
        
        const heading = document.createElement('h3');
        heading.textContent = 'Dropdown Debug';
        heading.style.margin = '0 0 10px 0';
        heading.style.fontSize = '14px';
        panel.appendChild(heading);
        
        const log = document.createElement('div');
        log.id = 'dropdown-debug-log';
        panel.appendChild(log);
        
        const clear = document.createElement('button');
        clear.textContent = 'Clear Log';
        clear.style.marginTop = '10px';
        clear.style.padding = '5px';
        clear.style.backgroundColor = '#333';
        clear.style.border = 'none';
        clear.style.color = 'white';
        clear.style.borderRadius = '3px';
        clear.style.cursor = 'pointer';
        clear.onclick = function() {
            document.getElementById('dropdown-debug-log').innerHTML = '';
        };
        panel.appendChild(clear);
        
        document.body.appendChild(panel);
        return log;
    }
    
    // Log a message to the debug panel
    function logDebug(message, type = 'info') {
        if (!DEBUG) return;
        
        console.log(`[Dropdown Debug] ${message}`);
        
        let log = document.getElementById('dropdown-debug-log');
        if (!log) {
            log = createDebugPanel();
        }
        
        const entry = document.createElement('div');
        entry.style.marginBottom = '5px';
        entry.style.borderLeft = '3px solid ' + (type === 'error' ? 'red' : type === 'warning' ? 'orange' : 'green');
        entry.style.paddingLeft = '5px';
        
        const time = new Date().toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit', second: '2-digit', fractionalSecondDigits: 3 });
        entry.innerHTML = `<span style="color: #aaa;">${time}</span> ${message}`;
        
        log.appendChild(entry);
        log.scrollTop = log.scrollHeight;
    }
    
    // Log the current state of all dropdowns
    function logDropdownStates() {
        dropdowns.forEach(dropdown => {
            if (dropdown.button && dropdown.menu) {
                const menuDisplay = window.getComputedStyle(dropdown.menu).display;
                const menuVisibility = window.getComputedStyle(dropdown.menu).visibility;
                const menuOpacity = window.getComputedStyle(dropdown.menu).opacity;
                
                logDebug(`${dropdown.name} Menu - Display: ${menuDisplay}, Visibility: ${menuVisibility}, Opacity: ${menuOpacity}`);
            }
        });
    }
    
    // Monitor all event listeners on an element
    function monitorEvents(element, name) {
        if (!element) return;
        
        const originalAddEventListener = element.addEventListener;
        element.addEventListener = function(type, listener, options) {
            logDebug(`Event listener added to ${name}: ${type}`);
            return originalAddEventListener.call(this, type, listener, options);
        };
        
        // Log initial style
        logDebug(`${name} initial style - Display: ${window.getComputedStyle(element).display}, Visibility: ${window.getComputedStyle(element).visibility}, Opacity: ${window.getComputedStyle(element).opacity}`);
        
        // Monitor style changes
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                if (mutation.attributeName === 'style') {
                    logDebug(`${name} style changed - Display: ${element.style.display}, Visibility: ${element.style.visibility}, Opacity: ${element.style.opacity}`);
                }
            });
        });
        
        observer.observe(element, { attributes: true, attributeFilter: ['style'] });
    }
    
    // Monitor all dropdowns
    function monitorDropdowns() {
        dropdowns.forEach(dropdown => {
            if (dropdown.button) {
                monitorEvents(dropdown.button, `${dropdown.name} Button`);
                
                // Monitor mouse events
                dropdown.button.addEventListener('mouseenter', () => {
                    logDebug(`${dropdown.name} Button mouseenter`);
                });
                
                dropdown.button.addEventListener('mouseleave', () => {
                    logDebug(`${dropdown.name} Button mouseleave`);
                });
                
                dropdown.button.addEventListener('click', () => {
                    logDebug(`${dropdown.name} Button click`);
                    setTimeout(logDropdownStates, 50);
                });
            }
            
            if (dropdown.menu) {
                monitorEvents(dropdown.menu, `${dropdown.name} Menu`);
                
                // Monitor mouse events
                dropdown.menu.addEventListener('mouseenter', () => {
                    logDebug(`${dropdown.name} Menu mouseenter`);
                });
                
                dropdown.menu.addEventListener('mouseleave', () => {
                    logDebug(`${dropdown.name} Menu mouseleave`);
                });
            }
            
            if (dropdown.container) {
                monitorEvents(dropdown.container, `${dropdown.name} Container`);
                
                // Monitor mouse events
                dropdown.container.addEventListener('mouseenter', () => {
                    logDebug(`${dropdown.name} Container mouseenter`);
                });
                
                dropdown.container.addEventListener('mouseleave', () => {
                    logDebug(`${dropdown.name} Container mouseleave`);
                });
            }
        });
    }
    
    // Initialize when the DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        logDebug('Dropdown diagnostics initialized');
        monitorDropdowns();
        
        // Log initial states
        setTimeout(logDropdownStates, 500);
    });
})();
