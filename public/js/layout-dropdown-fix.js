/**
 * Layout Dropdown Fix
 * 
 * This script fixes the issue with the layout dropdown menu where the options aren't clickable.
 * It directly attaches click event handlers to the layout options.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Layout Dropdown Fix script loaded');
    
    // Function to apply layout directly
    function applyLayoutDirectly(layoutName) {
        console.log('Applying layout directly from layout-dropdown-fix.js:', layoutName);
        
        // Update the layout name in the UI
        const viewModeText = document.getElementById('view-mode-text');
        if (viewModeText) {
            viewModeText.textContent = layoutName.charAt(0).toUpperCase() + layoutName.slice(1) + ' View';
        }
        
        // Save the preference
        localStorage.setItem('dashboard_view_mode', layoutName);
        
        // Get the dashboard widgets container
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) {
            console.error('Dashboard widgets container not found');
            return;
        }
        
        // Update data attributes
        dashboardWidgets.setAttribute('data-view-mode', layoutName);
        dashboardWidgets.setAttribute('data-arrangement', layoutName);
        
        // Apply the appropriate layout
        switch (layoutName) {
            case 'adhd-optimized':
                applyADHDOptimizedLayout();
                break;
            case 'focus':
                applyFocusLayout();
                break;
            case 'standard':
                applyStandardLayout();
                break;
            case 'custom':
                applyCustomLayout();
                break;
        }
        
        // Hide dropdown
        const layoutDropdown = document.getElementById('layout-dropdown');
        if (layoutDropdown) {
            layoutDropdown.classList.add('hidden');
            layoutDropdown.style.display = 'none';
        }
    }
    
    // ADHD Optimized Layout
    function applyADHDOptimizedLayout() {
        console.log('Applying ADHD Optimized Layout from layout-dropdown-fix.js');
        
        // Get all widgets
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        const widgets = document.querySelectorAll('[data-widget]');
        const currentFocusWidget = document.getElementById('current-focus-widget');
        const widgetControls = document.getElementById('widget-controls');
        
        // Reset all widgets to visible
        widgets.forEach(widget => {
            widget.style.display = 'block';
            widget.style.order = '';
            widget.style.opacity = '1';
            widget.style.filter = '';
            widget.style.transform = '';
            widget.style.boxShadow = '';
            widget.style.border = '';
            widget.style.borderLeft = '4px solid transparent';
        });
        
        // Hide widget controls
        if (widgetControls) {
            widgetControls.style.display = 'none';
        }
        
        // Apply container styles
        dashboardWidgets.style.display = 'grid';
        dashboardWidgets.style.gridTemplateColumns = 'repeat(1, 1fr)';
        dashboardWidgets.style.backgroundColor = 'rgba(14, 165, 233, 0.08)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(14, 165, 233, 0.2)';
        dashboardWidgets.style.position = 'relative';
        
        // Apply media queries for grid
        if (window.innerWidth >= 768) {
            dashboardWidgets.style.gridTemplateColumns = 'repeat(2, 1fr)';
        }
        
        // Apply specific widget styles
        if (currentFocusWidget) {
            currentFocusWidget.style.gridColumn = '1 / -1';
            currentFocusWidget.style.borderLeft = '6px solid rgba(14, 165, 233, 0.8)';
            currentFocusWidget.style.transform = 'scale(1.02)';
            currentFocusWidget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
        }
        
        // Apply colored borders to widgets
        widgets.forEach(widget => {
            const widgetType = widget.getAttribute('data-widget');
            
            if (widgetType === 'today-tasks') {
                widget.style.borderLeft = '4px solid rgba(59, 130, 246, 0.8)';
            } else if (widgetType === 'overdue-tasks') {
                widget.style.borderLeft = '4px solid rgba(239, 68, 68, 0.8)';
            } else if (widgetType === 'keyboard-shortcuts') {
                widget.style.borderLeft = '4px solid rgba(139, 92, 246, 0.8)';
            } else if (widgetType === 'adhd-guide') {
                widget.style.borderLeft = '4px solid rgba(16, 185, 129, 0.8)';
            } else if (widgetType === 'help-center') {
                widget.style.borderLeft = '4px solid rgba(245, 158, 11, 0.8)';
            }
        });
    }
    
    // Focus Layout
    function applyFocusLayout() {
        console.log('Applying Focus Layout from layout-dropdown-fix.js');
        
        // Get all widgets
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        const widgets = document.querySelectorAll('[data-widget]');
        const currentFocusWidget = document.getElementById('current-focus-widget');
        const todayTasksWidget = document.querySelector('[data-widget="today-tasks"]');
        const keyboardShortcutsWidget = document.querySelector('[data-widget="keyboard-shortcuts"]');
        const widgetControls = document.getElementById('widget-controls');
        
        // Hide widget controls
        if (widgetControls) {
            widgetControls.style.display = 'none';
        }
        
        // Apply container styles
        dashboardWidgets.style.display = 'flex';
        dashboardWidgets.style.flexDirection = 'column';
        dashboardWidgets.style.gap = '1.5rem';
        dashboardWidgets.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(99, 102, 241, 0.2)';
        dashboardWidgets.style.position = 'relative';
        
        // Hide all widgets except the essential ones
        widgets.forEach(widget => {
            const widgetType = widget.getAttribute('data-widget');
            
            if (widgetType === 'current-focus-widget' || widgetType === 'today-tasks' || widgetType === 'keyboard-shortcuts') {
                widget.style.display = 'block';
                widget.style.opacity = '1';
                widget.style.filter = '';
            } else {
                widget.style.display = 'none';
            }
        });
        
        // Apply specific widget styles
        if (currentFocusWidget) {
            currentFocusWidget.style.order = '-1';
            currentFocusWidget.style.borderLeft = '8px solid rgba(99, 102, 241, 0.8)';
            currentFocusWidget.style.transform = 'scale(1.03)';
            currentFocusWidget.style.boxShadow = '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
            currentFocusWidget.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
        }
        
        if (todayTasksWidget) {
            todayTasksWidget.style.order = '0';
            todayTasksWidget.style.borderLeft = '5px solid rgba(59, 130, 246, 0.8)';
            todayTasksWidget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
        }
        
        if (keyboardShortcutsWidget) {
            keyboardShortcutsWidget.style.order = '1';
            keyboardShortcutsWidget.style.borderLeft = '5px solid rgba(139, 92, 246, 0.8)';
            keyboardShortcutsWidget.style.maxHeight = '200px';
            keyboardShortcutsWidget.style.overflow = 'auto';
        }
    }
    
    // Standard Layout
    function applyStandardLayout() {
        console.log('Applying Standard Layout from layout-dropdown-fix.js');
        
        // Get all widgets
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        const widgets = document.querySelectorAll('[data-widget]');
        const currentFocusWidget = document.getElementById('current-focus-widget');
        const widgetControls = document.getElementById('widget-controls');
        
        // Reset all widgets to visible
        widgets.forEach(widget => {
            widget.style.display = 'block';
            widget.style.order = '';
            widget.style.opacity = '1';
            widget.style.filter = '';
            widget.style.transform = '';
            widget.style.boxShadow = '';
            widget.style.border = '1px solid rgba(0, 0, 0, 0.05)';
            widget.style.borderRadius = '0.5rem';
        });
        
        // Hide widget controls
        if (widgetControls) {
            widgetControls.style.display = 'none';
        }
        
        // Apply container styles
        dashboardWidgets.style.display = 'grid';
        dashboardWidgets.style.gridTemplateColumns = 'repeat(1, 1fr)';
        dashboardWidgets.style.gap = '1rem';
        dashboardWidgets.style.backgroundColor = 'rgba(0, 0, 0, 0.02)';
        dashboardWidgets.style.padding = '1.25rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(16, 185, 129, 0.2)';
        dashboardWidgets.style.position = 'relative';
        
        // Apply media queries for grid
        if (window.innerWidth >= 768) {
            dashboardWidgets.style.gridTemplateColumns = 'repeat(2, 1fr)';
        }
        
        if (window.innerWidth >= 1024) {
            dashboardWidgets.style.gridTemplateColumns = 'repeat(3, 1fr)';
        }
        
        // Apply specific widget styles
        if (currentFocusWidget) {
            currentFocusWidget.style.gridColumn = '1 / -1';
            currentFocusWidget.style.borderLeft = '4px solid rgba(16, 185, 129, 0.8)';
        }
    }
    
    // Custom Layout
    function applyCustomLayout() {
        console.log('Applying Custom Layout from layout-dropdown-fix.js');
        
        // Get all widgets
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        const widgets = document.querySelectorAll('[data-widget]');
        const widgetControls = document.getElementById('widget-controls');
        
        // Reset all widgets to visible
        widgets.forEach(widget => {
            widget.style.display = 'block';
            widget.style.order = '';
            widget.style.opacity = '1';
            widget.style.filter = '';
            widget.style.transform = '';
            widget.style.boxShadow = '';
            widget.style.border = '2px dotted rgba(139, 92, 246, 0.3)';
            widget.style.position = 'relative';
            widget.style.cursor = 'move';
        });
        
        // Show widget controls
        if (widgetControls) {
            widgetControls.style.display = 'block';
            widgetControls.style.marginBottom = '1.5rem';
            widgetControls.style.padding = '1rem';
            widgetControls.style.backgroundColor = 'rgba(139, 92, 246, 0.05)';
            widgetControls.style.borderRadius = '0.5rem';
            widgetControls.style.border = '2px dashed rgba(139, 92, 246, 0.3)';
        }
        
        // Apply container styles
        dashboardWidgets.style.display = 'grid';
        dashboardWidgets.style.gridTemplateColumns = 'repeat(1, 1fr)';
        dashboardWidgets.style.gap = '1rem';
        dashboardWidgets.style.backgroundColor = 'rgba(139, 92, 246, 0.05)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.border = '2px dashed rgba(139, 92, 246, 0.3)';
        dashboardWidgets.style.boxShadow = 'none';
        dashboardWidgets.style.position = 'relative';
        
        // Apply media queries for grid
        if (window.innerWidth >= 768) {
            dashboardWidgets.style.gridTemplateColumns = 'repeat(2, 1fr)';
        }
    }
    
    // Directly attach click handlers to layout options
    setTimeout(function() {
        console.log('Attaching click handlers to layout options');
        
        // Get all layout options
        const layoutOptions = document.querySelectorAll('[data-view]');
        console.log('Found layout options:', layoutOptions.length);
        
        // Attach click handlers
        layoutOptions.forEach(option => {
            console.log('Attaching click handler to:', option.textContent.trim());
            
            // Remove existing click handlers
            option.onclick = null;
            
            // Add new click handler
            option.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const layoutName = this.getAttribute('data-view');
                console.log('Layout option clicked:', layoutName);
                
                // Apply layout
                applyLayoutDirectly(layoutName);
            });
        });
    }, 500);
});
