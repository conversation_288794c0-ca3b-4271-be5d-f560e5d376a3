/**
 * Layout Switcher
 * 
 * This script provides a direct approach to switching between different dashboard layouts.
 * It applies dramatic visual changes to make each layout visually distinct.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Layout Switcher loaded');
    
    // Core elements
    const dashboardWidgets = document.getElementById('dashboard-widgets');
    const viewModeText = document.getElementById('view-mode-text');
    const layoutButtons = document.querySelectorAll('[data-view]');
    
    // Apply saved layout on page load
    const savedViewMode = localStorage.getItem('dashboard_view_mode') || 'adhd-optimized';
    console.log('Applying saved layout on page load:', savedViewMode);
    
    // Apply layout with a slight delay to ensure DOM is ready
    setTimeout(() => {
        applyLayout(savedViewMode);
    }, 200);
    
    // Add click handlers to all layout buttons
    layoutButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const viewMode = this.getAttribute('data-view');
            console.log('Layout button clicked:', viewMode);
            
            applyLayout(viewMode);
            
            // Hide dropdown
            const layoutDropdown = document.getElementById('layout-dropdown');
            if (layoutDropdown) {
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
            }
        });
    });
    
    // Main function to apply a layout
    function applyLayout(layoutName) {
        if (!dashboardWidgets) {
            console.error('Dashboard widgets container not found');
            return;
        }
        
        console.log('Applying layout:', layoutName);
        
        // Update view mode text
        if (viewModeText) {
            viewModeText.textContent = layoutName.charAt(0).toUpperCase() + layoutName.slice(1) + ' View';
        }
        
        // Save preference
        localStorage.setItem('dashboard_view_mode', layoutName);
        
        // Reset all layout-specific classes
        document.body.classList.remove('layout-adhd-optimized', 'layout-focus', 'layout-standard', 'layout-custom');
        document.body.classList.remove('focus-mode');
        
        // Set data attributes
        dashboardWidgets.setAttribute('data-view-mode', layoutName);
        dashboardWidgets.setAttribute('data-arrangement', layoutName);
        
        // Apply layout-specific class to body
        document.body.classList.add('layout-' + layoutName);
        
        // Apply dramatic visual changes based on layout
        switch(layoutName) {
            case 'adhd-optimized':
                applyADHDOptimizedLayout();
                break;
                
            case 'focus':
                applyFocusLayout();
                break;
                
            case 'standard':
                applyStandardLayout();
                break;
                
            case 'custom':
                applyCustomLayout();
                break;
        }
    }
    
    // ADHD Optimized Layout
    function applyADHDOptimizedLayout() {
        console.log('Applying ADHD Optimized Layout');
        
        // Add a visual indicator
        addLayoutLabel('ADHD Optimized Layout', '#0ea5e9');
        
        // Apply dramatic styling
        dashboardWidgets.style.backgroundColor = 'rgba(14, 165, 233, 0.08)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(14, 165, 233, 0.2)';
        
        // Arrange widgets
        const widgets = dashboardWidgets.querySelectorAll('[data-widget]');
        widgets.forEach(widget => {
            // Reset styles
            widget.style.display = 'block';
            widget.style.gridColumn = '';
            widget.style.order = '';
            widget.style.borderLeft = '4px solid transparent';
            
            // Apply widget-specific styling
            if (widget.getAttribute('data-widget') === 'current-focus-widget') {
                widget.style.gridColumn = '1 / -1';
                widget.style.borderLeftColor = 'rgba(14, 165, 233, 0.8)';
                widget.style.borderLeftWidth = '6px';
                widget.style.transform = 'scale(1.02)';
                widget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
            } else if (widget.getAttribute('data-widget') === 'today-tasks') {
                widget.style.borderLeftColor = 'rgba(59, 130, 246, 0.8)';
            } else if (widget.getAttribute('data-widget') === 'overdue-tasks') {
                widget.style.borderLeftColor = 'rgba(239, 68, 68, 0.8)';
            } else if (widget.getAttribute('data-widget') === 'keyboard-shortcuts') {
                widget.style.borderLeftColor = 'rgba(139, 92, 246, 0.8)';
            } else if (widget.getAttribute('data-widget') === 'adhd-guide') {
                widget.style.borderLeftColor = 'rgba(16, 185, 129, 0.8)';
            } else if (widget.getAttribute('data-widget') === 'help-center') {
                widget.style.borderLeftColor = 'rgba(245, 158, 11, 0.8)';
            }
        });
    }
    
    // Focus Layout
    function applyFocusLayout() {
        console.log('Applying Focus Layout');
        
        // Add a visual indicator
        addLayoutLabel('Focus Mode', '#6366f1');
        
        // Apply dramatic styling
        dashboardWidgets.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(99, 102, 241, 0.2)';
        
        // Add focus mode class to body
        document.body.classList.add('focus-mode');
        
        // Arrange widgets
        const widgets = dashboardWidgets.querySelectorAll('[data-widget]');
        widgets.forEach(widget => {
            const widgetType = widget.getAttribute('data-widget');
            
            if (widgetType === 'current-focus-widget' || widgetType === 'today-tasks' || widgetType === 'keyboard-shortcuts') {
                // Show essential widgets
                widget.style.display = 'block';
                widget.style.gridColumn = '1 / -1';
                
                // Apply widget-specific styling
                if (widgetType === 'current-focus-widget') {
                    widget.style.order = '-1';
                    widget.style.borderLeft = '8px solid rgba(99, 102, 241, 0.8)';
                    widget.style.transform = 'scale(1.03)';
                    widget.style.boxShadow = '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
                    widget.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
                } else if (widgetType === 'today-tasks') {
                    widget.style.order = '0';
                    widget.style.borderLeft = '5px solid rgba(59, 130, 246, 0.8)';
                } else if (widgetType === 'keyboard-shortcuts') {
                    widget.style.order = '1';
                    widget.style.borderLeft = '5px solid rgba(139, 92, 246, 0.8)';
                    widget.style.maxHeight = '200px';
                    widget.style.overflow = 'auto';
                }
            } else {
                // Hide non-essential widgets
                widget.style.display = 'none';
            }
        });
    }
    
    // Standard Layout
    function applyStandardLayout() {
        console.log('Applying Standard Layout');
        
        // Add a visual indicator
        addLayoutLabel('Standard Layout', '#10b981');
        
        // Apply dramatic styling
        dashboardWidgets.style.backgroundColor = 'rgba(0, 0, 0, 0.02)';
        dashboardWidgets.style.padding = '1.25rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(16, 185, 129, 0.2)';
        
        // Arrange widgets
        const widgets = dashboardWidgets.querySelectorAll('[data-widget]');
        widgets.forEach(widget => {
            // Reset styles
            widget.style.display = 'block';
            widget.style.gridColumn = '';
            widget.style.order = '';
            widget.style.borderLeft = '';
            widget.style.transform = '';
            widget.style.boxShadow = '';
            
            // Apply widget-specific styling
            if (widget.getAttribute('data-widget') === 'current-focus-widget') {
                widget.style.gridColumn = '1 / -1';
                widget.style.borderLeft = '4px solid rgba(16, 185, 129, 0.8)';
            } else {
                widget.style.minHeight = '200px';
                widget.style.border = '1px solid rgba(0, 0, 0, 0.05)';
                widget.style.borderRadius = '0.5rem';
            }
        });
    }
    
    // Custom Layout
    function applyCustomLayout() {
        console.log('Applying Custom Layout');
        
        // Add a visual indicator
        addLayoutLabel('Custom Layout', '#8b5cf6');
        
        // Apply dramatic styling
        dashboardWidgets.style.backgroundColor = 'rgba(139, 92, 246, 0.05)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.border = '2px dashed rgba(139, 92, 246, 0.3)';
        dashboardWidgets.style.boxShadow = 'none';
        
        // Arrange widgets
        const widgets = dashboardWidgets.querySelectorAll('[data-widget]');
        widgets.forEach(widget => {
            // Reset styles
            widget.style.display = 'block';
            widget.style.gridColumn = '';
            widget.style.order = '';
            
            // Apply widget-specific styling
            widget.style.border = '2px dotted rgba(139, 92, 246, 0.3)';
            widget.style.position = 'relative';
            widget.style.cursor = 'move';
            
            // Add drag handle indicator
            const widgetId = widget.getAttribute('data-widget');
            const handleId = 'drag-handle-' + widgetId;
            
            // Remove existing handle if any
            const existingHandle = document.getElementById(handleId);
            if (existingHandle) {
                existingHandle.remove();
            }
            
            // Create new handle
            const handle = document.createElement('div');
            handle.id = handleId;
            handle.innerHTML = '⋮⋮';
            handle.style.position = 'absolute';
            handle.style.top = '10px';
            handle.style.right = '10px';
            handle.style.color = 'rgba(139, 92, 246, 0.5)';
            handle.style.fontSize = '16px';
            handle.style.lineHeight = '1';
            handle.style.cursor = 'move';
            
            widget.appendChild(handle);
        });
        
        // Show widget controls if they exist
        const widgetControls = document.getElementById('widget-controls');
        if (widgetControls) {
            widgetControls.style.display = 'block';
            widgetControls.style.marginBottom = '1.5rem';
            widgetControls.style.padding = '1rem';
            widgetControls.style.backgroundColor = 'rgba(139, 92, 246, 0.05)';
            widgetControls.style.borderRadius = '0.5rem';
            widgetControls.style.border = '2px dashed rgba(139, 92, 246, 0.3)';
        }
    }
    
    // Helper function to add a visual label for the current layout
    function addLayoutLabel(labelText, color) {
        // Remove existing label if any
        const existingLabel = document.getElementById('layout-label');
        if (existingLabel) {
            existingLabel.remove();
        }
        
        // Create new label
        const label = document.createElement('div');
        label.id = 'layout-label';
        label.textContent = labelText;
        label.style.position = 'absolute';
        label.style.top = '-10px';
        label.style.left = '20px';
        label.style.backgroundColor = color;
        label.style.color = 'white';
        label.style.padding = '2px 10px';
        label.style.borderRadius = '4px';
        label.style.fontSize = '0.75rem';
        label.style.fontWeight = '600';
        label.style.zIndex = '10';
        
        // Add to dashboard widgets container
        if (dashboardWidgets) {
            // Make sure container has position relative
            dashboardWidgets.style.position = 'relative';
            dashboardWidgets.style.overflow = 'visible';
            
            dashboardWidgets.appendChild(label);
        }
    }
});
