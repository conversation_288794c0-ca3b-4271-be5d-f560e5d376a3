/**
 * Pinterest Explorer JavaScript
 *
 * This file contains the JavaScript code for the Pinterest Explorer interface.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Masonry layout
    initMasonryLayout();

    // Initialize image lazy loading
    initLazyLoading();

    // Initialize pin actions
    initPinActions();

    // Initialize form submissions
    initFormSubmissions();

    // Initialize infinite scroll (if enabled)
    initInfiniteScroll();
});

/**
 * Initialize Masonry layout for Pinterest-like grid
 */
function initMasonryLayout() {
    console.log('Initializing Masonry layout...');
    const grid = document.querySelector('#pin-masonry-grid');
    if (!grid) {
        console.log('Grid element not found');
        return;
    }

    // Check if imagesLoaded is available
    if (typeof imagesLoaded === 'undefined') {
        console.error('imagesLoaded library not loaded');
        // Fallback to direct initialization
        try {
            const masonry = new Masonry(grid, {
                itemSelector: '.pin-item',
                columnWidth: 236,
                gutter: 16,
                fitWidth: true,
                transitionDuration: '0.2s'
            });
            console.log('Masonry initialized without imagesLoaded');
        } catch (e) {
            console.error('Failed to initialize Masonry:', e);
        }
        return;
    }

    // Wait for images to load before initializing Masonry
    try {
        imagesLoaded(grid, function() {
            console.log('Images loaded, initializing Masonry');
            try {
                const masonry = new Masonry(grid, {
                    itemSelector: '.pin-item',
                    columnWidth: 236,
                    gutter: 16,
                    fitWidth: true,
                    transitionDuration: '0.2s'
                });

                // Hide loading indicator when masonry is initialized
                const loadingIndicator = document.querySelector('[x-show="isLoading"]');
                if (loadingIndicator) {
                    try {
                        if (typeof Alpine !== 'undefined') {
                            if (Alpine.store) {
                                Alpine.store('isLoading', false);
                            } else {
                                console.log('Alpine.store not available');
                            }
                        } else {
                            console.log('Alpine.js not initialized yet');
                        }
                    } catch (e) {
                        console.log('Error updating Alpine state:', e);
                    }
                }

                // Refresh masonry layout after a short delay to ensure all images are properly loaded
                setTimeout(() => {
                    masonry.layout();
                }, 500);
            } catch (e) {
                console.error('Failed to initialize Masonry after images loaded:', e);
            }
        });
    } catch (e) {
        console.error('Error with imagesLoaded:', e);
    }
}

/**
 * Initialize lazy loading for images
 */
function initLazyLoading() {
    // Check if IntersectionObserver is supported
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    const src = img.getAttribute('data-src');

                    if (src) {
                        img.src = src;
                        img.removeAttribute('data-src');
                    }

                    observer.unobserve(img);
                }
            });
        });

        // Get all images with data-src attribute
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => {
            imageObserver.observe(img);
        });
    } else {
        // Fallback for browsers that don't support IntersectionObserver
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => {
            img.src = img.getAttribute('data-src');
            img.removeAttribute('data-src');
        });
    }
}

/**
 * Initialize pin actions (save, download, etc.)
 */
function initPinActions() {
    // Save pin button
    const savePinButtons = document.querySelectorAll('.save-pin-button');
    savePinButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const pinId = this.getAttribute('data-pin-id');
            const pinUrl = this.getAttribute('data-pin-url');
            const imageUrl = this.getAttribute('data-image-url');
            const title = this.getAttribute('data-title');
            const description = this.getAttribute('data-description');

            // Create form data
            const formData = new FormData();
            formData.append('action', 'save_pin');
            formData.append('pin_id', pinId);
            formData.append('pin_url', pinUrl);
            formData.append('image_url', imageUrl);
            formData.append('title', title);
            formData.append('description', description);

            // Submit form data
            fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showMessage('Pin saved successfully', 'success');

                    // Update saved pins count
                    updateSavedPinsCount();
                } else {
                    // Show error message
                    showMessage(data.message || 'Failed to save pin', 'error');
                }
            })
            .catch(error => {
                console.error('Error saving pin:', error);
                showMessage('An error occurred while saving the pin', 'error');
            });
        });
    });

    // Download image button
    const downloadImageButtons = document.querySelectorAll('.download-image-button');
    downloadImageButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const imageUrl = this.getAttribute('data-image-url');

            // Create form data
            const formData = new FormData();
            formData.append('image_url', imageUrl);

            // Show loading indicator
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            this.disabled = true;

            // Submit form data
            fetch('/momentum/api/pinterest-image.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // Reset button
                this.innerHTML = '<i class="fas fa-download"></i>';
                this.disabled = false;

                if (data.success) {
                    // Create a temporary link to download the file
                    const tempLink = document.createElement('a');
                    tempLink.href = data.download_url;
                    tempLink.download = data.download_url.split('/').pop();
                    document.body.appendChild(tempLink);
                    tempLink.click();
                    document.body.removeChild(tempLink);

                    // Show success message
                    showMessage('Image downloaded successfully', 'success');
                } else {
                    // Show error message
                    showMessage(data.message || 'Failed to download image', 'error');
                }
            })
            .catch(error => {
                // Reset button
                this.innerHTML = '<i class="fas fa-download"></i>';
                this.disabled = false;

                console.error('Error downloading image:', error);
                showMessage('An error occurred while downloading the image', 'error');
            });
        });
    });
}

/**
 * Initialize form submissions
 */
function initFormSubmissions() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;

                // Add loading spinner to button
                const originalContent = submitButton.innerHTML;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> ' + submitButton.textContent;

                // Re-enable button after 5 seconds in case of error
                setTimeout(() => {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalContent;
                }, 5000);
            }
        });
    });
}

/**
 * Initialize infinite scroll
 */
function initInfiniteScroll() {
    // Check if infinite scroll is enabled
    const infiniteScrollEnabled = document.querySelector('#infinite-scroll-enabled');
    if (!infiniteScrollEnabled) return;

    // Get the container element
    const container = document.querySelector('#pin-masonry-grid');
    if (!container) return;

    // Get the loading indicator
    const loadingIndicator = document.querySelector('#infinite-scroll-loading');

    // Get the current page and total pages
    let currentPage = parseInt(infiniteScrollEnabled.getAttribute('data-current-page')) || 1;
    const totalPages = parseInt(infiniteScrollEnabled.getAttribute('data-total-pages')) || 1;

    // Check if we've reached the last page
    if (currentPage >= totalPages) return;

    // Create an intersection observer
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && currentPage < totalPages) {
                // Show loading indicator
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'block';
                }

                // Load more pins
                loadMorePins(currentPage + 1);
            }
        });
    });

    // Observe the last pin item
    const lastPinItem = container.querySelector('.pin-item:last-child');
    if (lastPinItem) {
        observer.observe(lastPinItem);
    }
}

/**
 * Show a message to the user
 *
 * @param {string} message - The message to show
 * @param {string} type - The type of message (success, error, info)
 */
function showMessage(message, type = 'info') {
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.className = 'fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-opacity duration-300';

    // Set background color based on message type
    if (type === 'success') {
        messageElement.className += ' bg-green-500 text-white';
    } else if (type === 'error') {
        messageElement.className += ' bg-red-500 text-white';
    } else {
        messageElement.className += ' bg-blue-500 text-white';
    }

    // Set message content
    messageElement.innerHTML = message;

    // Add message to the DOM
    document.body.appendChild(messageElement);

    // Remove message after 3 seconds
    setTimeout(() => {
        messageElement.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(messageElement);
        }, 300);
    }, 3000);
}

/**
 * Update the saved pins count
 */
function updateSavedPinsCount() {
    // Reload the page to update the saved pins count
    window.location.reload();
}
