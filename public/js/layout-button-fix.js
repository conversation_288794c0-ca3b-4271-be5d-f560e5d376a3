/**
 * Layout But<PERSON> Fix
 * 
 * This script provides a direct fix for the layout button dropdown functionality.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Layout Button Fix loaded');
    
    // Direct approach to fix the layout button
    const layoutButton = document.querySelector('#layout-selector-button, .layout-button, [data-action="layout"]');
    const layoutDropdown = document.querySelector('#layout-dropdown');
    
    if (layoutButton && layoutDropdown) {
        console.log('Found layout button and dropdown');
        
        // Add click event directly
        layoutButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Layout button clicked directly');
            
            // Toggle dropdown visibility
            if (layoutDropdown.classList.contains('hidden')) {
                // Show dropdown
                layoutDropdown.classList.remove('hidden');
                layoutDropdown.style.display = 'block';
                
                // Position dropdown
                const buttonRect = layoutButton.getBoundingClientRect();
                layoutDropdown.style.position = 'absolute';
                layoutDropdown.style.top = (buttonRect.bottom + window.scrollY) + 'px';
                layoutDropdown.style.left = (buttonRect.left + window.scrollX) + 'px';
                layoutDropdown.style.zIndex = '9999';
                
                // Set active state
                layoutButton.setAttribute('aria-expanded', 'true');
            } else {
                // Hide dropdown
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
                layoutButton.setAttribute('aria-expanded', 'false');
            }
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (layoutButton && layoutDropdown && !layoutButton.contains(e.target) && !layoutDropdown.contains(e.target)) {
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
                if (layoutButton) {
                    layoutButton.setAttribute('aria-expanded', 'false');
                }
            }
        });
        
        // Handle view options
        const viewOptions = layoutDropdown.querySelectorAll('[data-view]');
        viewOptions.forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const viewMode = this.getAttribute('data-view');
                console.log('View option clicked:', viewMode);
                
                // Update dashboard view mode
                const dashboardWidgets = document.getElementById('dashboard-widgets');
                if (dashboardWidgets) {
                    dashboardWidgets.setAttribute('data-view-mode', viewMode);
                    dashboardWidgets.setAttribute('data-arrangement', viewMode);
                }
                
                // Update view mode text
                const viewModeText = document.getElementById('view-mode-text');
                if (viewModeText) {
                    viewModeText.textContent = viewMode.charAt(0).toUpperCase() + viewMode.slice(1) + ' View';
                }
                
                // Save preference
                localStorage.setItem('dashboard_view_mode', viewMode);
                
                // Hide dropdown
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
                layoutButton.setAttribute('aria-expanded', 'false');
            });
        });
    } else {
        console.error('Layout button or dropdown not found');
        if (!layoutButton) console.error('Layout button missing');
        if (!layoutDropdown) console.error('Layout dropdown missing');
    }
});
