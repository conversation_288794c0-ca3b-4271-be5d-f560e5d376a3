/**
 * Force Layout Change
 * 
 * This script ensures that layout changes are applied correctly by directly manipulating
 * the DOM and applying CSS classes to the body element.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Force Layout Change script loaded');
    
    // Apply the saved layout immediately
    const savedViewMode = localStorage.getItem('dashboard_view_mode') || 'adhd-optimized';
    console.log('Applying saved layout on page load:', savedViewMode);
    
    // Apply layout with a slight delay to ensure DOM is ready
    setTimeout(function() {
        forceLayoutChange(savedViewMode);
        
        // Add click handlers to all layout buttons
        const layoutButtons = document.querySelectorAll('[data-view]');
        layoutButtons.forEach(button => {
            button.addEventListener('click', function() {
                const viewMode = this.getAttribute('data-view');
                console.log('Layout button clicked, forcing layout change to:', viewMode);
                forceLayoutChange(viewMode);
            });
        });
    }, 100);
    
    // Function to force layout change
    function forceLayoutChange(layoutName) {
        console.log('Forcing layout change to:', layoutName);
        
        // Remove all layout classes
        document.body.classList.remove('layout-adhd-optimized', 'layout-focus', 'layout-standard', 'layout-custom');
        
        // Add the appropriate layout class
        document.body.classList.add('layout-' + layoutName);
        
        // Update the dashboard widgets container
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (dashboardWidgets) {
            dashboardWidgets.setAttribute('data-view-mode', layoutName);
            dashboardWidgets.setAttribute('data-arrangement', layoutName);
            
            // Apply specific styles based on layout
            switch(layoutName) {
                case 'adhd-optimized':
                    applyADHDOptimizedStyles();
                    break;
                case 'focus':
                    applyFocusStyles();
                    break;
                case 'standard':
                    applyStandardStyles();
                    break;
                case 'custom':
                    applyCustomStyles();
                    break;
            }
        }
        
        // Update the view mode text
        const viewModeText = document.getElementById('view-mode-text');
        if (viewModeText) {
            viewModeText.textContent = layoutName.charAt(0).toUpperCase() + layoutName.slice(1) + ' View';
        }
        
        // Save the preference
        localStorage.setItem('dashboard_view_mode', layoutName);
    }
    
    // Apply ADHD Optimized styles
    function applyADHDOptimizedStyles() {
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) return;
        
        // Apply container styles
        dashboardWidgets.style.backgroundColor = 'rgba(14, 165, 233, 0.08)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(14, 165, 233, 0.2)';
        
        // Apply widget styles
        const widgets = dashboardWidgets.querySelectorAll('[data-widget]');
        widgets.forEach(widget => {
            // Reset styles
            widget.style.display = 'block';
            
            // Apply widget-specific styles
            if (widget.id === 'current-focus-widget') {
                widget.style.borderLeft = '6px solid rgba(14, 165, 233, 0.8)';
                widget.style.transform = 'scale(1.02)';
                widget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
            } else if (widget.getAttribute('data-widget') === 'today-tasks') {
                widget.style.borderLeft = '4px solid rgba(59, 130, 246, 0.8)';
            } else if (widget.getAttribute('data-widget') === 'overdue-tasks') {
                widget.style.borderLeft = '4px solid rgba(239, 68, 68, 0.8)';
            } else if (widget.getAttribute('data-widget') === 'keyboard-shortcuts') {
                widget.style.borderLeft = '4px solid rgba(139, 92, 246, 0.8)';
            } else if (widget.getAttribute('data-widget') === 'adhd-guide') {
                widget.style.borderLeft = '4px solid rgba(16, 185, 129, 0.8)';
            } else if (widget.getAttribute('data-widget') === 'help-center') {
                widget.style.borderLeft = '4px solid rgba(245, 158, 11, 0.8)';
            }
        });
    }
    
    // Apply Focus styles
    function applyFocusStyles() {
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) return;
        
        // Apply container styles
        dashboardWidgets.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(99, 102, 241, 0.2)';
        
        // Apply widget styles
        const widgets = dashboardWidgets.querySelectorAll('[data-widget]');
        widgets.forEach(widget => {
            const widgetType = widget.getAttribute('data-widget');
            
            if (widgetType === 'current-focus-widget' || widgetType === 'today-tasks' || widgetType === 'keyboard-shortcuts') {
                // Show essential widgets
                widget.style.display = 'block';
                
                // Apply widget-specific styles
                if (widgetType === 'current-focus-widget') {
                    widget.style.borderLeft = '8px solid rgba(99, 102, 241, 0.8)';
                    widget.style.transform = 'scale(1.03)';
                    widget.style.boxShadow = '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
                } else if (widgetType === 'today-tasks') {
                    widget.style.borderLeft = '5px solid rgba(59, 130, 246, 0.8)';
                } else if (widgetType === 'keyboard-shortcuts') {
                    widget.style.borderLeft = '5px solid rgba(139, 92, 246, 0.8)';
                }
            } else {
                // Hide non-essential widgets
                widget.style.display = 'none';
            }
        });
    }
    
    // Apply Standard styles
    function applyStandardStyles() {
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) return;
        
        // Apply container styles
        dashboardWidgets.style.backgroundColor = 'rgba(0, 0, 0, 0.02)';
        dashboardWidgets.style.padding = '1.25rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(16, 185, 129, 0.2)';
        
        // Apply widget styles
        const widgets = dashboardWidgets.querySelectorAll('[data-widget]');
        widgets.forEach(widget => {
            // Reset styles
            widget.style.display = 'block';
            widget.style.transform = '';
            
            // Apply widget-specific styles
            if (widget.id === 'current-focus-widget') {
                widget.style.borderLeft = '4px solid rgba(16, 185, 129, 0.8)';
            } else {
                widget.style.border = '1px solid rgba(0, 0, 0, 0.05)';
            }
        });
    }
    
    // Apply Custom styles
    function applyCustomStyles() {
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) return;
        
        // Apply container styles
        dashboardWidgets.style.backgroundColor = 'rgba(139, 92, 246, 0.05)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.border = '2px dashed rgba(139, 92, 246, 0.3)';
        
        // Apply widget styles
        const widgets = dashboardWidgets.querySelectorAll('[data-widget]');
        widgets.forEach(widget => {
            // Reset styles
            widget.style.display = 'block';
            
            // Apply widget-specific styles
            widget.style.border = '2px dotted rgba(139, 92, 246, 0.3)';
            widget.style.cursor = 'move';
        });
        
        // Show widget controls
        const widgetControls = document.getElementById('widget-controls');
        if (widgetControls) {
            widgetControls.style.display = 'block';
        }
    }
});
