/**
 * Fallback Sortable Implementation
 * 
 * This script provides a fallback for browsers that have issues with the HTML5 Drag and Drop API.
 * It uses jQuery UI Sortable to implement drag-and-drop functionality for the dashboard widgets.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Fallback Sortable script loaded');
    
    // Check if jQuery is available
    if (typeof jQuery === 'undefined') {
        console.error('jQuery is not available for fallback sortable implementation');
        return;
    }
    
    // Check if jQuery UI is available
    if (typeof jQuery.ui === 'undefined' || typeof jQuery.ui.sortable === 'undefined') {
        console.log('jQuery UI Sortable not available, loading it dynamically');
        loadJQueryUI();
        return;
    }
    
    // Initialize sortable
    initSortable();
    
    /**
     * Load jQuery UI dynamically
     */
    function loadJQueryUI() {
        // Load jQuery UI CSS
        const uiCss = document.createElement('link');
        uiCss.rel = 'stylesheet';
        uiCss.href = 'https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css';
        document.head.appendChild(uiCss);
        
        // Load jQuery UI JS
        const uiScript = document.createElement('script');
        uiScript.src = 'https://code.jquery.com/ui/1.13.2/jquery-ui.min.js';
        uiScript.onload = function() {
            console.log('jQuery UI loaded successfully');
            initSortable();
        };
        uiScript.onerror = function() {
            console.error('Failed to load jQuery UI');
        };
        document.head.appendChild(uiScript);
    }
    
    /**
     * Initialize jQuery UI Sortable
     */
    function initSortable() {
        // Wait for jQuery UI to be fully loaded
        if (typeof jQuery.ui === 'undefined' || typeof jQuery.ui.sortable === 'undefined') {
            console.log('jQuery UI not ready yet, waiting...');
            setTimeout(initSortable, 100);
            return;
        }
        
        // Check if we're in custom layout mode
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets || dashboardWidgets.getAttribute('data-view-mode') !== 'custom') {
            console.log('Not in custom layout mode, skipping sortable initialization');
            
            // Set up a mutation observer to watch for layout changes
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'data-view-mode') {
                        const newViewMode = dashboardWidgets.getAttribute('data-view-mode');
                        if (newViewMode === 'custom') {
                            console.log('Switched to custom layout mode, initializing sortable');
                            initSortableNow();
                        }
                    }
                });
            });
            
            observer.observe(dashboardWidgets, { attributes: true });
            return;
        }
        
        // Initialize sortable now
        initSortableNow();
    }
    
    /**
     * Actually initialize the sortable functionality
     */
    function initSortableNow() {
        console.log('Initializing jQuery UI Sortable as fallback');
        
        try {
            // Initialize sortable
            jQuery('#dashboard-widgets').sortable({
                items: '[data-widget]',
                handle: '.widget-drag-handle',
                placeholder: 'sortable-placeholder',
                tolerance: 'pointer',
                opacity: 0.7,
                cursor: 'move',
                revert: 150,
                start: function(event, ui) {
                    ui.placeholder.height(ui.item.height());
                    ui.placeholder.css({
                        'border': '2px dashed rgba(139, 92, 246, 0.5)',
                        'background-color': 'rgba(139, 92, 246, 0.05)',
                        'margin-bottom': '1rem'
                    });
                    
                    // Dispatch custom event
                    const dragStartEvent = new CustomEvent('widget-drag-start-fallback', {
                        detail: { widgetId: ui.item.attr('data-widget') || ui.item.attr('id') }
                    });
                    document.dispatchEvent(dragStartEvent);
                },
                stop: function(event, ui) {
                    // Save the new arrangement
                    saveWidgetArrangement();
                    
                    // Dispatch custom event
                    const dropEvent = new CustomEvent('widget-dropped-fallback', {
                        detail: { widgetId: ui.item.attr('data-widget') || ui.item.attr('id') }
                    });
                    document.dispatchEvent(dropEvent);
                }
            });
            
            // Add a message to indicate sortable is ready
            const message = document.createElement('div');
            message.className = 'sortable-ready-message';
            message.textContent = 'Drag and drop is ready! (Using fallback method)';
            message.style.position = 'fixed';
            message.style.bottom = '20px';
            message.style.right = '20px';
            message.style.backgroundColor = 'rgba(139, 92, 246, 0.9)';
            message.style.color = 'white';
            message.style.padding = '10px 15px';
            message.style.borderRadius = '4px';
            message.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
            message.style.zIndex = '9999';
            message.style.opacity = '0';
            message.style.transform = 'translateY(20px)';
            message.style.transition = 'opacity 0.3s, transform 0.3s';
            
            document.body.appendChild(message);
            
            // Animate in
            setTimeout(() => {
                message.style.opacity = '1';
                message.style.transform = 'translateY(0)';
            }, 10);
            
            // Remove after delay
            setTimeout(() => {
                message.style.opacity = '0';
                message.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    if (message.parentNode) {
                        message.parentNode.removeChild(message);
                    }
                }, 300);
            }, 3000);
            
            console.log('jQuery UI Sortable initialized successfully');
        } catch (error) {
            console.error('Error initializing jQuery UI Sortable:', error);
        }
    }
    
    /**
     * Save the current widget arrangement to localStorage
     */
    function saveWidgetArrangement() {
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) return;
        
        const widgets = dashboardWidgets.querySelectorAll('[data-widget]');
        const arrangement = Array.from(widgets).map(widget => {
            return widget.getAttribute('data-widget') || widget.id;
        });
        
        localStorage.setItem('dashboard_widget_arrangement', JSON.stringify(arrangement));
        console.log('Saved widget arrangement (fallback):', arrangement);
        
        // Show a success message
        showArrangementSavedMessage();
    }
    
    /**
     * Show a message that the arrangement was saved
     */
    function showArrangementSavedMessage() {
        // Create message element
        const message = document.createElement('div');
        message.textContent = 'Layout saved!';
        message.style.position = 'fixed';
        message.style.bottom = '20px';
        message.style.right = '20px';
        message.style.backgroundColor = 'rgba(139, 92, 246, 0.9)';
        message.style.color = 'white';
        message.style.padding = '10px 15px';
        message.style.borderRadius = '4px';
        message.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
        message.style.zIndex = '9999';
        message.style.opacity = '0';
        message.style.transform = 'translateY(20px)';
        message.style.transition = 'opacity 0.3s, transform 0.3s';
        
        document.body.appendChild(message);
        
        // Animate in
        setTimeout(() => {
            message.style.opacity = '1';
            message.style.transform = 'translateY(0)';
        }, 10);
        
        // Remove after delay
        setTimeout(() => {
            message.style.opacity = '0';
            message.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 300);
        }, 3000);
    }
});
