/**
 * AI Prompt Builder JavaScript
 * 
 * Handles dynamic functionality for creating and editing AI prompts
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('AI Prompt Builder loaded');
    
    // Initialize prompt builder functionality
    initPromptBuilder();
    initVariableDetection();
    initPreviewModal();
    initFormValidation();
});

/**
 * Initialize the prompt builder interface
 */
function initPromptBuilder() {
    const promptTextarea = document.getElementById('prompt_text');
    const addVariableBtn = document.getElementById('addVariableBtn');
    const variablesList = document.getElementById('variablesList');
    
    if (!promptTextarea || !addVariableBtn || !variablesList) {
        console.warn('Prompt builder elements not found');
        return;
    }
    
    // Add syntax highlighting for variables
    promptTextarea.addEventListener('input', function() {
        highlightVariables(this);
        autoDetectVariables();
    });
    
    // Add variable button functionality
    addVariableBtn.addEventListener('click', function() {
        addVariableField();
    });
    
    // Initialize existing variables if any
    loadExistingVariables();
}

/**
 * Initialize variable detection
 */
function initVariableDetection() {
    const promptTextarea = document.getElementById('prompt_text');
    if (!promptTextarea) return;
    
    // Debounce variable detection
    let detectionTimeout;
    promptTextarea.addEventListener('input', function() {
        clearTimeout(detectionTimeout);
        detectionTimeout = setTimeout(() => {
            detectAndSuggestVariables();
        }, 500);
    });
}

/**
 * Initialize preview modal functionality
 */
function initPreviewModal() {
    const previewBtn = document.getElementById('previewBtn');
    const previewModal = document.getElementById('previewModal');
    const closePreviewBtn = document.getElementById('closePreviewBtn');
    
    if (previewBtn) {
        previewBtn.addEventListener('click', showPromptPreview);
    }
    
    if (closePreviewBtn) {
        closePreviewBtn.addEventListener('click', function() {
            if (previewModal) {
                previewModal.classList.add('hidden');
            }
        });
    }
    
    // Close modal when clicking outside
    if (previewModal) {
        previewModal.addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });
    }
}

/**
 * Initialize form validation
 */
function initFormValidation() {
    const form = document.querySelector('form');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            showValidationErrors();
        }
    });
}

/**
 * Highlight variables in the prompt text
 */
function highlightVariables(textarea) {
    // This is a simple implementation - in a real app you might use a more sophisticated editor
    const text = textarea.value;
    const variablePattern = /\{\{([^}]+)\}\}/g;
    
    // Store cursor position
    const cursorPos = textarea.selectionStart;
    
    // For now, we'll just add a visual indicator by changing the background temporarily
    textarea.style.backgroundColor = '#f8f9fa';
    setTimeout(() => {
        textarea.style.backgroundColor = '';
    }, 100);
}

/**
 * Auto-detect variables in prompt text and suggest adding them
 */
function detectAndSuggestVariables() {
    const promptText = document.getElementById('prompt_text');
    if (!promptText) return;
    
    const text = promptText.value;
    const variablePattern = /\{\{([^}]+)\}\}/g;
    const foundVariables = [];
    let match;
    
    while ((match = variablePattern.exec(text)) !== null) {
        const variableName = match[1].trim();
        if (variableName && !foundVariables.includes(variableName)) {
            foundVariables.push(variableName);
        }
    }
    
    // Check which variables are not yet defined
    const existingVariables = getExistingVariableNames();
    const newVariables = foundVariables.filter(name => !existingVariables.includes(name));
    
    // Auto-add new variables
    newVariables.forEach(varName => {
        addVariableField(varName, '', 'text', '');
    });
}

/**
 * Get names of existing variables
 */
function getExistingVariableNames() {
    const variableInputs = document.querySelectorAll('.variable-name');
    return Array.from(variableInputs).map(input => input.value).filter(name => name);
}

/**
 * Add a new variable field
 */
function addVariableField(name = '', description = '', type = 'text', defaultValue = '') {
    const variablesList = document.getElementById('variablesList');
    if (!variablesList) return;
    
    const variableId = 'var_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    
    const variableHtml = `
        <div class="variable-item bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-3" data-variable-id="${variableId}">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                    <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Variable Name</label>
                    <input type="text" 
                           class="variable-name w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white" 
                           value="${escapeHtml(name)}" 
                           placeholder="variable_name"
                           required>
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Type</label>
                    <select class="variable-type w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
                        <option value="text" ${type === 'text' ? 'selected' : ''}>Text</option>
                        <option value="textarea" ${type === 'textarea' ? 'selected' : ''}>Long Text</option>
                        <option value="number" ${type === 'number' ? 'selected' : ''}>Number</option>
                        <option value="select" ${type === 'select' ? 'selected' : ''}>Dropdown</option>
                        <option value="checkbox" ${type === 'checkbox' ? 'selected' : ''}>Checkbox</option>
                    </select>
                </div>
            </div>
            <div class="mt-2">
                <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
                <input type="text" 
                       class="variable-description w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white" 
                       value="${escapeHtml(description)}" 
                       placeholder="Describe this variable">
            </div>
            <div class="mt-2">
                <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Default Value</label>
                <input type="text" 
                       class="variable-default w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white" 
                       value="${escapeHtml(defaultValue)}" 
                       placeholder="Optional default value">
            </div>
            <div class="mt-2 flex justify-between items-center">
                <div class="text-xs text-gray-500 dark:text-gray-400">
                    Use: <code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">{{${escapeHtml(name || 'variable_name')}}}</code>
                </div>
                <button type="button" 
                        class="remove-variable text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-sm px-2 py-1 rounded hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors">
                    <i class="fas fa-trash mr-1"></i>Remove
                </button>
            </div>
        </div>
    `;
    
    variablesList.insertAdjacentHTML('beforeend', variableHtml);
    
    // Add event listeners to the new variable
    const newVariable = variablesList.lastElementChild;
    setupVariableEventListeners(newVariable);
    
    // Update the variables JSON
    updateVariablesJson();
    
    // Focus on the name input if it's empty
    if (!name) {
        const nameInput = newVariable.querySelector('.variable-name');
        if (nameInput) nameInput.focus();
    }
}

/**
 * Setup event listeners for a variable element
 */
function setupVariableEventListeners(variableElement) {
    // Remove button
    const removeBtn = variableElement.querySelector('.remove-variable');
    if (removeBtn) {
        removeBtn.addEventListener('click', function() {
            variableElement.remove();
            updateVariablesJson();
        });
    }
    
    // All inputs
    const inputs = variableElement.querySelectorAll('input, select');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            updateVariablesJson();
            updateVariableUsageExample(variableElement);
        });
    });
}

/**
 * Update the usage example for a variable
 */
function updateVariableUsageExample(variableElement) {
    const nameInput = variableElement.querySelector('.variable-name');
    const exampleCode = variableElement.querySelector('code');
    
    if (nameInput && exampleCode) {
        const name = nameInput.value || 'variable_name';
        exampleCode.textContent = `{{${name}}}`;
    }
}

/**
 * Load existing variables from the hidden JSON field
 */
function loadExistingVariables() {
    const variablesJson = document.getElementById('variables_json');
    if (!variablesJson || !variablesJson.value) return;
    
    try {
        const variables = JSON.parse(variablesJson.value);
        variables.forEach(variable => {
            addVariableField(
                variable.name || '',
                variable.description || '',
                variable.type || 'text',
                variable.default_value || ''
            );
        });
    } catch (e) {
        console.error('Error parsing existing variables:', e);
    }
}

/**
 * Update the hidden variables JSON field
 */
function updateVariablesJson() {
    const variablesJson = document.getElementById('variables_json');
    if (!variablesJson) return;
    
    const variables = [];
    const variableItems = document.querySelectorAll('.variable-item');
    
    variableItems.forEach(item => {
        const name = item.querySelector('.variable-name')?.value || '';
        const type = item.querySelector('.variable-type')?.value || 'text';
        const description = item.querySelector('.variable-description')?.value || '';
        const defaultValue = item.querySelector('.variable-default')?.value || '';
        
        if (name.trim()) {
            variables.push({
                name: name.trim(),
                type: type,
                description: description,
                default_value: defaultValue
            });
        }
    });
    
    variablesJson.value = JSON.stringify(variables);
}

/**
 * Show prompt preview
 */
function showPromptPreview() {
    const title = document.getElementById('title')?.value || '';
    const description = document.getElementById('description')?.value || '';
    const promptText = document.getElementById('prompt_text')?.value || '';
    const previewContent = document.getElementById('previewContent');
    const previewModal = document.getElementById('previewModal');
    
    if (!previewContent || !previewModal) return;
    
    let preview = '';
    
    if (title) {
        preview += `<div class="mb-4"><strong>Title:</strong> ${escapeHtml(title)}</div>`;
    }
    
    if (description) {
        preview += `<div class="mb-4"><strong>Description:</strong> ${escapeHtml(description)}</div>`;
    }
    
    preview += `<div class="mb-4"><strong>Prompt:</strong></div>`;
    preview += `<div class="bg-gray-100 dark:bg-gray-600 p-3 rounded font-mono text-sm whitespace-pre-wrap">`;
    
    // Highlight variables in the preview
    const highlightedText = promptText.replace(
        /\{\{([^}]+)\}\}/g, 
        '<span class="bg-purple-200 dark:bg-purple-800 text-purple-900 dark:text-purple-100 px-1 rounded font-semibold">{{$1}}</span>'
    );
    
    preview += escapeHtml(promptText).replace(
        /\{\{([^}]+)\}\}/g, 
        '<span class="bg-purple-200 dark:bg-purple-800 text-purple-900 dark:text-purple-100 px-1 rounded font-semibold">{{$1}}</span>'
    );
    
    preview += `</div>`;
    
    previewContent.innerHTML = preview;
    previewModal.classList.remove('hidden');
}

/**
 * Validate the form before submission
 */
function validateForm() {
    const title = document.getElementById('title')?.value || '';
    const promptText = document.getElementById('prompt_text')?.value || '';
    
    if (!title.trim()) {
        showNotification('Please enter a title for your prompt', 'error');
        return false;
    }
    
    if (!promptText.trim()) {
        showNotification('Please enter the prompt text', 'error');
        return false;
    }
    
    // Validate variables
    const variableItems = document.querySelectorAll('.variable-item');
    for (let item of variableItems) {
        const name = item.querySelector('.variable-name')?.value || '';
        if (!name.trim()) {
            showNotification('Please provide names for all variables or remove empty ones', 'error');
            return false;
        }
    }
    
    return true;
}

/**
 * Show validation errors
 */
function showValidationErrors() {
    // Scroll to the first error field
    const titleInput = document.getElementById('title');
    const promptTextarea = document.getElementById('prompt_text');
    
    if (titleInput && !titleInput.value.trim()) {
        titleInput.focus();
        titleInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else if (promptTextarea && !promptTextarea.value.trim()) {
        promptTextarea.focus();
        promptTextarea.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white max-w-sm ${
        type === 'error' ? 'bg-red-500' : 
        type === 'success' ? 'bg-green-500' : 
        'bg-blue-500'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
