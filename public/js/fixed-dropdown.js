/**
 * Fixed Dropdown Menu Handler
 * 
 * This script provides a consolidated solution for dropdown menus
 * with proper hover behavior and no flickering.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Fixed dropdown handler loaded');
    
    // Get all dropdown buttons
    const adhdButton = document.getElementById('adhd-dropdown-button');
    const productivityButton = document.getElementById('productivity-dropdown-button');
    const toolsButton = document.getElementById('tools-dropdown-button');
    const userButton = document.getElementById('user-menu-button');
    
    // Get all dropdown menus
    const adhdMenu = document.getElementById('adhd-dropdown-menu');
    const productivityMenu = document.getElementById('productivity-dropdown-menu');
    const toolsMenu = document.getElementById('tools-dropdown-menu');
    const userMenu = document.getElementById('user-dropdown-menu');
    
    // Get all dropdown containers
    const adhdContainer = document.getElementById('adhd-dropdown');
    const productivityContainer = document.getElementById('productivity-dropdown');
    const toolsContainer = document.getElementById('tools-dropdown');
    
    // Get the overlay
    const overlay = document.getElementById('dropdown-overlay');
    
    // Track active dropdown
    let activeDropdown = null;
    
    // Track hover state
    let isHovering = false;
    
    // Track hover timeout
    let hoverTimeout = null;
    
    // Function to position a dropdown menu
    function positionDropdown(button, menu) {
        if (!button || !menu) return;
        
        const buttonRect = button.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        
        // Position the menu below the button
        menu.style.position = 'fixed';
        menu.style.top = (buttonRect.bottom + window.scrollY) + 'px';
        menu.style.left = (buttonRect.left + window.scrollX) + 'px';
        menu.style.zIndex = '9999';
        
        // Adjust if menu would go off-screen to the right
        const menuWidth = menu.offsetWidth || 200; // Default width if not yet rendered
        if (buttonRect.left + menuWidth > viewportWidth) {
            menu.style.left = (buttonRect.right - menuWidth + window.scrollX) + 'px';
        }
        
        // Set max height based on viewport
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - buttonRect.bottom - 10;
        menu.style.maxHeight = spaceBelow + 'px';
        menu.style.overflowY = 'auto';
    }
    
    // Function to show a dropdown
    function showDropdown(button, menu) {
        if (!button || !menu) return;
        
        // Hide any active dropdown first
        hideAllDropdowns();
        
        // Position the menu
        positionDropdown(button, menu);
        
        // Show the overlay
        if (overlay) {
            overlay.style.display = 'block';
            overlay.classList.add('active');
        }
        
        // Show the menu with a slight delay to ensure smooth transition
        menu.style.display = 'block';
        menu.style.visibility = 'visible';
        
        // Use setTimeout to ensure the transition works
        setTimeout(() => {
            menu.style.opacity = '1';
        }, 10);
        
        // Set as active dropdown
        activeDropdown = menu;
    }
    
    // Function to hide all dropdowns
    function hideAllDropdowns() {
        if (!activeDropdown) return;
        
        // Hide the active dropdown
        activeDropdown.style.opacity = '0';
        
        // Use setTimeout to ensure the transition completes
        setTimeout(() => {
            activeDropdown.style.display = 'none';
            activeDropdown.style.visibility = 'hidden';
        }, 150); // Match this with the CSS transition duration
        
        // Hide the overlay
        if (overlay) {
            overlay.style.display = 'none';
            overlay.classList.remove('active');
        }
        
        // Clear active dropdown
        activeDropdown = null;
    }
    
    // Function to handle hover state
    function handleHover(isEntering, button, menu, container) {
        if (isEntering) {
            // Clear any existing timeout
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }
            
            // Set hovering state
            isHovering = true;
            
            // Show the dropdown
            showDropdown(button, menu);
        } else {
            // Set a timeout to hide the dropdown
            hoverTimeout = setTimeout(() => {
                // Only hide if we're not hovering over the menu or button
                if (!container.matches(':hover') && !menu.matches(':hover')) {
                    isHovering = false;
                    hideAllDropdowns();
                }
            }, 300); // 300ms delay before hiding
        }
    }
    
    // Setup ADHD dropdown
    if (adhdButton && adhdMenu && adhdContainer) {
        // Handle click
        adhdButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (activeDropdown === adhdMenu) {
                hideAllDropdowns();
            } else {
                showDropdown(adhdButton, adhdMenu);
            }
        });
        
        // Handle hover on container
        adhdContainer.addEventListener('mouseenter', function() {
            handleHover(true, adhdButton, adhdMenu, adhdContainer);
        });
        
        adhdContainer.addEventListener('mouseleave', function() {
            handleHover(false, adhdButton, adhdMenu, adhdContainer);
        });
        
        // Handle hover on menu
        adhdMenu.addEventListener('mouseenter', function() {
            // Clear any existing timeout
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }
            isHovering = true;
        });
        
        adhdMenu.addEventListener('mouseleave', function() {
            handleHover(false, adhdButton, adhdMenu, adhdContainer);
        });
    }
    
    // Setup Productivity dropdown
    if (productivityButton && productivityMenu && productivityContainer) {
        // Handle click
        productivityButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (activeDropdown === productivityMenu) {
                hideAllDropdowns();
            } else {
                showDropdown(productivityButton, productivityMenu);
            }
        });
        
        // Handle hover
        productivityContainer.addEventListener('mouseenter', function() {
            handleHover(true, productivityButton, productivityMenu, productivityContainer);
        });
        
        productivityContainer.addEventListener('mouseleave', function() {
            handleHover(false, productivityButton, productivityMenu, productivityContainer);
        });
        
        // Handle hover on menu
        productivityMenu.addEventListener('mouseenter', function() {
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }
            isHovering = true;
        });
        
        productivityMenu.addEventListener('mouseleave', function() {
            handleHover(false, productivityButton, productivityMenu, productivityContainer);
        });
    }
    
    // Setup Tools dropdown
    if (toolsButton && toolsMenu && toolsContainer) {
        // Handle click
        toolsButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (activeDropdown === toolsMenu) {
                hideAllDropdowns();
            } else {
                showDropdown(toolsButton, toolsMenu);
            }
        });
        
        // Handle hover
        toolsContainer.addEventListener('mouseenter', function() {
            handleHover(true, toolsButton, toolsMenu, toolsContainer);
        });
        
        toolsContainer.addEventListener('mouseleave', function() {
            handleHover(false, toolsButton, toolsMenu, toolsContainer);
        });
        
        // Handle hover on menu
        toolsMenu.addEventListener('mouseenter', function() {
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }
            isHovering = true;
        });
        
        toolsMenu.addEventListener('mouseleave', function() {
            handleHover(false, toolsButton, toolsMenu, toolsContainer);
        });
    }
    
    // Setup User dropdown (no hover, only click)
    if (userButton && userMenu) {
        userButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (activeDropdown === userMenu) {
                hideAllDropdowns();
            } else {
                showDropdown(userButton, userMenu);
            }
        });
    }
    
    // Hide dropdowns when clicking outside
    if (overlay) {
        overlay.addEventListener('click', hideAllDropdowns);
    }
    
    // Hide dropdowns when pressing escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideAllDropdowns();
        }
    });
    
    // Handle window resize and scroll
    window.addEventListener('resize', function() {
        if (activeDropdown) {
            const button = activeDropdown === adhdMenu ? adhdButton :
                          activeDropdown === productivityMenu ? productivityButton :
                          activeDropdown === toolsMenu ? toolsButton : userButton;
            
            positionDropdown(button, activeDropdown);
        }
    });
    
    window.addEventListener('scroll', function() {
        if (activeDropdown) {
            const button = activeDropdown === adhdMenu ? adhdButton :
                          activeDropdown === productivityMenu ? productivityButton :
                          activeDropdown === toolsMenu ? toolsButton : userButton;
            
            positionDropdown(button, activeDropdown);
        }
    });
});
