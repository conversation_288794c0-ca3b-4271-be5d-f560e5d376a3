/**
 * Consolidated Layout Handler
 * 
 * This script provides a unified approach to handle layout switching functionality
 * across all dashboard versions. It replaces all previous layout handlers to avoid conflicts.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Consolidated Layout Handler loaded');
    
    // Core elements
    const dashboardWidgets = document.getElementById('dashboard-widgets');
    const viewModeText = document.getElementById('view-mode-text');
    const layoutButton = document.getElementById('layout-selector-button');
    const layoutDropdown = document.getElementById('layout-dropdown');
    
    console.log('Dashboard widgets:', dashboardWidgets ? 'Found' : 'Not found');
    console.log('View mode text:', viewModeText ? 'Found' : 'Not found');
    console.log('Layout button:', layoutButton ? 'Found' : 'Not found');
    console.log('Layout dropdown:', layoutDropdown ? 'Found' : 'Not found');
    
    // Apply saved layout on page load
    const savedViewMode = localStorage.getItem('dashboard_view_mode') || 'adhd-optimized';
    console.log('Applying saved layout on page load:', savedViewMode);
    
    // Apply layout with a slight delay to ensure DOM is ready
    setTimeout(() => {
        applyLayout(savedViewMode);
    }, 200);
    
    // Setup layout button click handler
    if (layoutButton && layoutDropdown) {
        console.log('Setting up layout button click handler');
        
        // Remove any existing click handlers by cloning and replacing
        const newButton = layoutButton.cloneNode(true);
        layoutButton.parentNode.replaceChild(newButton, layoutButton);
        
        // Add click event listener to toggle dropdown
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Layout button clicked');
            
            // Toggle dropdown visibility
            const isHidden = layoutDropdown.classList.contains('hidden');
            
            if (isHidden) {
                // Show dropdown
                layoutDropdown.classList.remove('hidden');
                
                // Position the dropdown correctly
                const buttonRect = newButton.getBoundingClientRect();
                
                // Calculate position relative to the viewport
                const dropdownTop = buttonRect.bottom + window.scrollY;
                const dropdownLeft = buttonRect.left + window.scrollX;
                
                // Set position and show
                layoutDropdown.style.position = 'absolute';
                layoutDropdown.style.top = dropdownTop + 'px';
                layoutDropdown.style.left = dropdownLeft + 'px';
                layoutDropdown.style.zIndex = '9999';
                layoutDropdown.style.display = 'block';
                
                // Ensure the dropdown is visible within the viewport
                setTimeout(() => {
                    const dropdownRect = layoutDropdown.getBoundingClientRect();
                    const viewportWidth = window.innerWidth;
                    const viewportHeight = window.innerHeight;
                    
                    // Adjust if dropdown would go off-screen to the right
                    if (dropdownRect.right > viewportWidth) {
                        const rightOffset = dropdownRect.right - viewportWidth + 10;
                        layoutDropdown.style.left = (dropdownLeft - rightOffset) + 'px';
                    }
                    
                    // Adjust if dropdown would go off-screen at the bottom
                    if (dropdownRect.bottom > viewportHeight) {
                        const bottomOffset = dropdownRect.bottom - viewportHeight + 10;
                        layoutDropdown.style.top = (dropdownTop - bottomOffset) + 'px';
                    }
                }, 0);
                
                // Set active state on button
                newButton.setAttribute('aria-expanded', 'true');
                newButton.classList.add('bg-gray-100', 'dark:bg-gray-600');
            } else {
                // Hide dropdown
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
                newButton.setAttribute('aria-expanded', 'false');
                newButton.classList.remove('bg-gray-100', 'dark:bg-gray-600');
            }
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (layoutDropdown && !layoutDropdown.classList.contains('hidden') &&
                !newButton.contains(e.target) && !layoutDropdown.contains(e.target)) {
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
                newButton.setAttribute('aria-expanded', 'false');
                newButton.classList.remove('bg-gray-100', 'dark:bg-gray-600');
            }
        });
        
        // Handle view option selection
        const viewOptions = layoutDropdown.querySelectorAll('[data-view]');
        viewOptions.forEach(option => {
            // Remove any existing click listeners by cloning and replacing
            const newOption = option.cloneNode(true);
            option.parentNode.replaceChild(newOption, option);
            
            // Add event listener to the new option
            newOption.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const viewMode = this.getAttribute('data-view');
                console.log('View option clicked:', viewMode);
                
                // Apply the layout
                applyLayout(viewMode);
                
                // Hide dropdown
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
                newButton.setAttribute('aria-expanded', 'false');
                newButton.classList.remove('bg-gray-100', 'dark:bg-gray-600');
            });
        });
    }
    
    // Main function to apply a layout
    function applyLayout(layoutName) {
        if (!dashboardWidgets) {
            console.error('Dashboard widgets container not found');
            return;
        }
        
        console.log('Applying layout:', layoutName);
        
        // Update view mode text
        if (viewModeText) {
            viewModeText.textContent = layoutName.charAt(0).toUpperCase() + layoutName.slice(1) + ' View';
        }
        
        // Save preference
        localStorage.setItem('dashboard_view_mode', layoutName);
        
        // Reset all layout-specific classes
        document.body.classList.remove('layout-adhd-optimized', 'layout-focus', 'layout-standard', 'layout-custom');
        document.body.classList.remove('focus-mode');
        
        // Set data attributes
        dashboardWidgets.setAttribute('data-view-mode', layoutName);
        dashboardWidgets.setAttribute('data-arrangement', layoutName);
        
        // Apply layout-specific class to body
        document.body.classList.add('layout-' + layoutName);
        
        // Apply dramatic visual changes based on layout
        switch(layoutName) {
            case 'adhd-optimized':
                applyADHDOptimizedLayout();
                break;
                
            case 'focus':
                applyFocusLayout();
                break;
                
            case 'standard':
                applyStandardLayout();
                break;
                
            case 'custom':
                applyCustomLayout();
                break;
        }
    }
    
    // Layout implementation functions
    function applyADHDOptimizedLayout() {
        console.log('Applying ADHD Optimized Layout');
        addLayoutLabel('ADHD Optimized Layout', '#0ea5e9');
        
        // Apply CSS classes and styles from layout-visual-styles.css
        // The actual styling is handled by the CSS file
    }
    
    function applyFocusLayout() {
        console.log('Applying Focus Layout');
        addLayoutLabel('Focus Mode', '#6366f1');
        
        // Add focus mode class to body
        document.body.classList.add('focus-mode');
        
        // The actual styling is handled by the CSS file
    }
    
    function applyStandardLayout() {
        console.log('Applying Standard Layout');
        addLayoutLabel('Standard Layout', '#10b981');
        
        // The actual styling is handled by the CSS file
    }
    
    function applyCustomLayout() {
        console.log('Applying Custom Layout');
        addLayoutLabel('Custom Layout', '#8b5cf6');
        
        // Show widget controls if they exist
        const widgetControls = document.getElementById('widget-controls');
        if (widgetControls) {
            widgetControls.classList.remove('hidden');
        }
        
        // The actual styling is handled by the CSS file
    }
    
    // Helper function to add a visual label for the current layout
    function addLayoutLabel(labelText, color) {
        // Remove existing label if any
        const existingLabel = document.getElementById('layout-label');
        if (existingLabel) {
            existingLabel.remove();
        }
        
        // Create new label
        const label = document.createElement('div');
        label.id = 'layout-label';
        label.textContent = labelText;
        label.style.position = 'absolute';
        label.style.top = '-10px';
        label.style.left = '20px';
        label.style.backgroundColor = color;
        label.style.color = 'white';
        label.style.padding = '2px 10px';
        label.style.borderRadius = '4px';
        label.style.fontSize = '0.75rem';
        label.style.fontWeight = '600';
        label.style.zIndex = '10';
        
        // Add to dashboard widgets container
        if (dashboardWidgets) {
            // Make sure container has position relative
            dashboardWidgets.style.position = 'relative';
            dashboardWidgets.style.overflow = 'visible';
            
            dashboardWidgets.appendChild(label);
        }
    }
});
