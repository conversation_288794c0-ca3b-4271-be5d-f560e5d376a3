/**
 * Pinterest Direct Access
 *
 * This script ensures direct access to Pinterest pins without any intermediary services.
 * It completely bypasses any redirects or proxies that might be causing Access Denied errors.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Pinterest Direct Access loaded');

    // Fix all Pinterest pin links immediately
    fixAllPinterestLinks();

    // Set up a mutation observer to catch dynamically added links
    setupMutationObserver();

    /**
     * Fix all Pinterest pin links on the page
     */
    function fixAllPinterestLinks() {
        // Find all Pinterest pin links
        const pinterestLinks = document.querySelectorAll('a[href*="pinterest.com/pin/"], a.pinterest-pin-link');
        console.log('Found Pinterest links:', pinterestLinks.length);

        pinterestLinks.forEach(function(link) {
            // Skip already fixed links
            if (link.getAttribute('data-fixed-direct') === 'true') {
                return;
            }

            // Get the original URL
            const originalUrl = link.getAttribute('href');
            console.log('Original Pinterest URL:', originalUrl);

            // Extract the pin ID
            let pinId = '';
            
            // Try to get pin ID from data attribute first
            if (link.hasAttribute('data-pin-id')) {
                pinId = link.getAttribute('data-pin-id');
                console.log('Pin ID from data attribute:', pinId);
            } 
            // Otherwise extract from URL
            else {
                const pinIdMatch = originalUrl.match(/\/pin\/([0-9]+)/);
                if (pinIdMatch && pinIdMatch[1]) {
                    pinId = pinIdMatch[1];
                    console.log('Pin ID extracted from URL:', pinId);
                }
            }

            if (pinId) {
                // Create a direct Pinterest URL
                const directUrl = `https://www.pinterest.com/pin/${pinId}/`;
                console.log('Direct Pinterest URL:', directUrl);

                // Update the href attribute
                link.setAttribute('href', directUrl);
                
                // Mark as fixed
                link.setAttribute('data-fixed-direct', 'true');
                
                // Clone the link to remove any existing event listeners
                const newLink = link.cloneNode(true);
                link.parentNode.replaceChild(newLink, link);
                
                // Add a direct click handler that completely bypasses any redirects
                newLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    console.log('Pinterest link clicked, opening direct URL:', directUrl);
                    
                    // Open in a new tab using window.open which bypasses most redirects
                    const newWindow = window.open('about:blank', '_blank');
                    if (newWindow) {
                        newWindow.location.href = directUrl;
                    } else {
                        // If popup blocked, try setting location directly
                        window.open(directUrl, '_blank');
                    }
                    
                    return false;
                });
            } else {
                console.error('Could not extract pin ID from URL:', originalUrl);
            }
        });
    }

    /**
     * Set up a mutation observer to fix dynamically added links
     */
    function setupMutationObserver() {
        // Create a mutation observer to watch for dynamically added links
        const observer = new MutationObserver(function(mutations) {
            let needsFixing = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    // Check if any of the added nodes contain Pinterest links
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.nodeType === 1) { // Element node
                            if (node.tagName === 'A' && 
                                (node.href.includes('pinterest.com/pin/') || 
                                 node.classList.contains('pinterest-pin-link'))) {
                                needsFixing = true;
                                break;
                            } else if (node.querySelector) {
                                const hasLinks = node.querySelector('a[href*="pinterest.com/pin/"], a.pinterest-pin-link');
                                if (hasLinks) {
                                    needsFixing = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            });
            
            if (needsFixing) {
                console.log('Detected new Pinterest links, fixing...');
                fixAllPinterestLinks();
            }
        });
        
        // Start observing the document with the configured parameters
        observer.observe(document.body, { 
            childList: true, 
            subtree: true 
        });
        
        console.log('Mutation observer set up for Pinterest links');
    }
});
