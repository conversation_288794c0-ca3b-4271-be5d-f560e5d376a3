/**
 * SortableJS Implementation for Dashboard Widgets
 *
 * This script implements drag-and-drop functionality for dashboard widgets
 * using the SortableJS library.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('SortableJS Implementation loaded');

    // Load SortableJS if not already loaded
    if (typeof Sortable === 'undefined') {
        loadSortableJS();
    } else {
        initSortable();
    }

    // Setup save and reset buttons
    setupButtons();

    /**
     * Load SortableJS library
     */
    function loadSortableJS() {
        console.log('Loading SortableJS...');

        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js';
        script.onload = function() {
            console.log('SortableJS loaded successfully');
            initSortable();
        };
        script.onerror = function() {
            console.error('Failed to load SortableJS');
        };

        document.head.appendChild(script);
    }

    /**
     * Initialize Sortable
     */
    function initSortable() {
        // Wait for the dashboard to be in custom layout mode
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) {
            console.error('Dashboard widgets container not found');
            return;
        }

        // Check if we're in custom layout mode
        if (dashboardWidgets.getAttribute('data-view-mode') !== 'custom') {
            console.log('Not in custom layout mode, setting up observer');

            // Set up a mutation observer to watch for layout changes
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'data-view-mode') {
                        const newViewMode = dashboardWidgets.getAttribute('data-view-mode');
                        if (newViewMode === 'custom') {
                            console.log('Switched to custom layout mode, initializing Sortable');
                            initSortableNow();
                        }
                    }
                });
            });

            observer.observe(dashboardWidgets, { attributes: true });

            // Also check periodically in case the observer misses the change
            const checkInterval = setInterval(function() {
                if (dashboardWidgets.getAttribute('data-view-mode') === 'custom') {
                    console.log('Custom layout mode detected by interval check');
                    clearInterval(checkInterval);
                    initSortableNow();
                }
            }, 1000);

            return;
        }

        // Initialize immediately if already in custom mode
        initSortableNow();
    }

    /**
     * Actually initialize Sortable
     */
    function initSortableNow() {
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) return;

        // Only proceed if in custom layout mode
        if (dashboardWidgets.getAttribute('data-view-mode') !== 'custom') return;

        console.log('Initializing Sortable for dashboard widgets');

        // Add drag handles to widgets if they don't exist
        const widgets = dashboardWidgets.querySelectorAll('[data-widget]');
        widgets.forEach(function(widget) {
            const widgetId = widget.getAttribute('data-widget') || widget.id;
            const handleId = 'drag-handle-' + widgetId;

            if (!document.getElementById(handleId)) {
                const dragHandle = document.createElement('div');
                dragHandle.id = handleId;
                dragHandle.className = 'widget-drag-handle';
                dragHandle.innerHTML = '<i class="fas fa-grip-vertical"></i>';
                dragHandle.style.position = 'absolute';
                dragHandle.style.top = '10px';
                dragHandle.style.right = '10px';
                dragHandle.style.color = 'rgba(139, 92, 246, 0.7)';
                dragHandle.style.cursor = 'move';
                dragHandle.style.padding = '5px';
                dragHandle.style.zIndex = '5';

                widget.appendChild(dragHandle);
            }
        });

        // Check if Sortable is available
        if (typeof Sortable === 'undefined') {
            console.error('SortableJS not loaded');
            return;
        }

        // Destroy existing instance if any
        if (window.dashboardSortable) {
            window.dashboardSortable.destroy();
        }

        // Create new Sortable instance
        try {
            window.dashboardSortable = new Sortable(dashboardWidgets, {
                animation: 150,
                handle: '.widget-drag-handle',
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                forceFallback: true,
                fallbackClass: 'sortable-fallback',
                fallbackOnBody: true,
                swapThreshold: 0.65,
                direction: 'vertical',
                delay: 150,
                delayOnTouchOnly: true,
                touchStartThreshold: 3,
                onStart: function(evt) {
                    console.log('Drag started on widget:', evt.item.getAttribute('data-widget') || evt.item.id);
                    showMessage('Dragging widget...');

                    // Add a class to the body to indicate dragging is in progress
                    document.body.classList.add('sortable-dragging');

                    // Add a class to the container
                    dashboardWidgets.classList.add('receiving-drag');
                },
                onEnd: function(evt) {
                    console.log('Drag ended');

                    // Remove the dragging class
                    document.body.classList.remove('sortable-dragging');

                    // Remove the receiving class
                    dashboardWidgets.classList.remove('receiving-drag');

                    if (evt.oldIndex !== evt.newIndex) {
                        console.log('Widget moved from position', evt.oldIndex, 'to', evt.newIndex);
                        saveWidgetArrangement();
                    }
                },
                onMove: function(evt) {
                    // Add visual feedback during movement
                    return true;
                }
            });

            console.log('Sortable initialized successfully');
            showMessage('Drag and drop is ready! Drag widgets to rearrange them.');
        } catch (error) {
            console.error('Error initializing Sortable:', error);
        }
    }

    /**
     * Setup save and reset buttons
     */
    function setupButtons() {
        const saveLayoutBtn = document.getElementById('save-layout-btn');
        const resetLayoutBtn = document.getElementById('reset-layout-btn');

        if (saveLayoutBtn) {
            saveLayoutBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Save layout button clicked');
                saveWidgetArrangement();
            });
        }

        if (resetLayoutBtn) {
            resetLayoutBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Reset layout button clicked');

                if (confirm('Are you sure you want to reset the layout? This will restore the default arrangement.')) {
                    localStorage.removeItem('dashboard_widget_arrangement');
                    showMessage('Layout reset! Reloading page...');

                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                }
            });
        }
    }

    /**
     * Save the current widget arrangement to localStorage
     */
    function saveWidgetArrangement() {
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) return;

        const widgets = dashboardWidgets.querySelectorAll('[data-widget]');
        const arrangement = Array.from(widgets).map(widget => {
            return widget.getAttribute('data-widget') || widget.id;
        });

        localStorage.setItem('dashboard_widget_arrangement', JSON.stringify(arrangement));
        console.log('Saved widget arrangement:', arrangement);

        showMessage('Layout saved successfully!');
    }

    /**
     * Show a message to the user
     */
    function showMessage(text) {
        // Remove existing message if any
        const existingMessage = document.getElementById('sortable-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // Create message element
        const message = document.createElement('div');
        message.id = 'sortable-message';
        message.textContent = text;
        message.style.position = 'fixed';
        message.style.bottom = '20px';
        message.style.right = '20px';
        message.style.backgroundColor = 'rgba(139, 92, 246, 0.9)';
        message.style.color = 'white';
        message.style.padding = '10px 15px';
        message.style.borderRadius = '4px';
        message.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
        message.style.zIndex = '9999';
        message.style.opacity = '0';
        message.style.transform = 'translateY(20px)';
        message.style.transition = 'opacity 0.3s, transform 0.3s';

        document.body.appendChild(message);

        // Animate in
        setTimeout(() => {
            message.style.opacity = '1';
            message.style.transform = 'translateY(0)';
        }, 10);

        // Remove after delay
        setTimeout(() => {
            message.style.opacity = '0';
            message.style.transform = 'translateY(20px)';

            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 300);
        }, 3000);
    }
});
