/**
 * Layout Dropdown Initializer
 *
 * This script ensures the layout dropdown is properly initialized and functional.
 * It runs immediately and also after the DOM is loaded to ensure it works in all scenarios.
 */

(function() {
    console.log('Layout Dropdown Initializer executing');

    // Function to initialize the layout dropdown
    function initializeLayoutDropdown() {
        console.log('Initializing layout dropdown');

        const layoutButton = document.getElementById('layout-selector-button');
        const layoutDropdown = document.getElementById('layout-dropdown');

        if (!layoutButton || !layoutDropdown) {
            console.log('Layout button or dropdown not found, will try again later');
            return false;
        }

        console.log('Found layout button and dropdown');

        // Ensure dropdown is initially hidden
        layoutDropdown.classList.add('hidden');
        layoutDropdown.style.display = 'none';

        // Remove any existing event listeners from the button
        const newButton = layoutButton.cloneNode(true);
        layoutButton.parentNode.replaceChild(newButton, layoutButton);

        // Add click handler to the button
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Layout button clicked (initializer)');

            // Toggle dropdown visibility
            const isHidden = layoutDropdown.classList.contains('hidden');
            if (isHidden) {
                // Show dropdown
                layoutDropdown.classList.remove('hidden');
                layoutDropdown.style.display = 'block';

                // Position dropdown
                const buttonRect = newButton.getBoundingClientRect();
                layoutDropdown.style.position = 'fixed';
                layoutDropdown.style.top = (buttonRect.bottom + window.scrollY + 5) + 'px';
                layoutDropdown.style.left = (buttonRect.left + window.scrollX) + 'px';
                layoutDropdown.style.zIndex = '9999';

                // Set active state
                newButton.setAttribute('aria-expanded', 'true');
            } else {
                // Hide dropdown
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
                newButton.setAttribute('aria-expanded', 'false');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (layoutDropdown && !layoutDropdown.classList.contains('hidden') &&
                !newButton.contains(e.target) && !layoutDropdown.contains(e.target)) {
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
                newButton.setAttribute('aria-expanded', 'false');
            }
        });

        // Initialize layout options
        initializeLayoutOptions(layoutDropdown, newButton);

        return true;
    }

    // Function to initialize layout options
    function initializeLayoutOptions(layoutDropdown, layoutButton) {
        console.log('Initializing layout options');

        const viewOptions = layoutDropdown.querySelectorAll('[data-view]');
        console.log(`Found ${viewOptions.length} layout options`);

        if (viewOptions.length === 0) {
            console.log('No layout options found, will try again later');
            return false;
        }

        viewOptions.forEach(option => {
            // Remove any existing event listeners
            const newOption = option.cloneNode(true);
            option.parentNode.replaceChild(newOption, option);

            const layoutName = newOption.getAttribute('data-view');
            console.log(`Initializing option: ${layoutName}`);

            // Add click handler
            newOption.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log(`Layout option clicked: ${layoutName}`);

                // Apply layout
                applyLayoutChange(layoutName);

                // Hide dropdown
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
                layoutButton.setAttribute('aria-expanded', 'false');
            });
        });

        return true;
    }

    // Function to apply layout change
    function applyLayoutChange(layoutName) {
        console.log(`Applying layout change: ${layoutName}`);

        // Update view mode text
        const viewModeText = document.getElementById('view-mode-text');
        if (viewModeText) {
            viewModeText.textContent = layoutName.charAt(0).toUpperCase() + layoutName.slice(1) + ' View';
        }

        // Save preference
        localStorage.setItem('dashboard_view_mode', layoutName);

        // Get dashboard widgets container
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) {
            console.error('Dashboard widgets container not found');
            return;
        }

        // Update data attributes
        dashboardWidgets.setAttribute('data-view-mode', layoutName);
        dashboardWidgets.setAttribute('data-arrangement', layoutName);

        // Apply layout-specific class to body
        document.body.classList.remove('layout-adhd-optimized', 'layout-focus', 'layout-standard', 'layout-custom');
        document.body.classList.add('layout-' + layoutName);

        // Show success message if function exists
        if (typeof showSuccessMessage === 'function') {
            showSuccessMessage(`${layoutName.charAt(0).toUpperCase() + layoutName.slice(1)} layout applied!`);
        }

        // Reload the page to apply the layout fully
        window.location.reload();
    }

    // Try to initialize immediately
    if (!initializeLayoutDropdown()) {
        // If initialization fails, try again after a short delay
        setTimeout(initializeLayoutDropdown, 500);
    }

    // Also try after DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        if (!initializeLayoutDropdown()) {
            // If initialization fails, try again after a short delay
            setTimeout(initializeLayoutDropdown, 500);
        }
    });

    // And try one more time after a longer delay
    setTimeout(initializeLayoutDropdown, 1000);
})();
