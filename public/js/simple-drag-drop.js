/**
 * Simple Drag and Drop Implementation
 *
 * A very simple and direct implementation of drag and drop for dashboard widgets
 * without relying on external libraries.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Simple Drag and Drop loaded');

    // Check if we're on the dashboard page by looking for the dashboard-widgets container
    const isDashboardPage = document.getElementById('dashboard-widgets') !== null;

    if (!isDashboardPage) {
        console.log('Not on dashboard page, skipping drag-drop initialization');

        // Create a mock container to prevent errors
        if (document.querySelector('.dashboard-widgets-error-message') === null) {
            const mockMessage = document.createElement('div');
            mockMessage.className = 'dashboard-widgets-error-message';
            mockMessage.style.display = 'none';
            document.body.appendChild(mockMessage);
        }

        return;
    }

    // Initialize when the page loads
    initDragDrop();

    // Also initialize when the layout changes
    document.addEventListener('layout-changed', function(e) {
        console.log('Layout changed to:', e.detail.layout);
        if (e.detail.layout === 'custom') {
            initDragDrop();
        }
    });

    // Setup save and reset buttons
    setupButtons();

    /**
     * Initialize drag and drop functionality
     */
    function initDragDrop() {
        console.log('Initializing simple drag and drop');

        // Get the dashboard widgets container
        const container = document.getElementById('dashboard-widgets');
        if (!container) {
            console.error('Dashboard widgets container not found');
            return;
        }

        // Only initialize if in custom layout mode
        if (container.getAttribute('data-view-mode') !== 'custom') {
            console.log('Not in custom layout mode, setting up observer');

            // Set up a mutation observer to watch for layout changes
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'data-view-mode') {
                        const newViewMode = container.getAttribute('data-view-mode');
                        if (newViewMode === 'custom') {
                            console.log('Switched to custom layout mode, initializing drag and drop');
                            setupDragHandles();
                        }
                    }
                });
            });

            observer.observe(container, { attributes: true });

            return;
        }

        // Setup drag handles
        setupDragHandles();
    }

    /**
     * Setup drag handles for all widgets
     */
    function setupDragHandles() {
        console.log('Setting up drag handles');

        // Get all widgets
        const widgets = document.querySelectorAll('[data-widget]');
        console.log('Found', widgets.length, 'widgets');

        // Add drag handles to each widget
        widgets.forEach(function(widget) {
            const widgetId = widget.getAttribute('data-widget') || widget.id;
            const handleId = 'drag-handle-' + widgetId;

            // Remove existing handle if any
            const existingHandle = document.getElementById(handleId);
            if (existingHandle) {
                existingHandle.remove();
            }

            // Create new drag handle
            const handle = document.createElement('div');
            handle.id = handleId;
            handle.className = 'widget-drag-handle';
            handle.innerHTML = '<i class="fas fa-grip-vertical"></i>';
            handle.style.position = 'absolute';
            handle.style.top = '10px';
            handle.style.right = '10px';
            handle.style.color = 'rgba(139, 92, 246, 0.7)';
            handle.style.cursor = 'move';
            handle.style.padding = '5px';
            handle.style.zIndex = '5';

            // Add the handle to the widget
            widget.appendChild(handle);

            // Make the widget draggable
            widget.setAttribute('draggable', 'true');

            // Add drag event listeners
            widget.addEventListener('dragstart', handleDragStart);
            widget.addEventListener('dragend', handleDragEnd);
        });

        // Add drop event listeners to the container
        const container = document.getElementById('dashboard-widgets');
        if (container) {
            container.addEventListener('dragover', handleDragOver);
            container.addEventListener('drop', handleDrop);
        }

        // Load saved arrangement
        loadSavedArrangement();

        // Show a message
        showMessage('Drag and drop is ready! Drag widgets to rearrange them.');
    }

    /**
     * Handle drag start event
     */
    function handleDragStart(e) {
        console.log('Drag started on widget:', this.getAttribute('data-widget') || this.id);

        // Store the widget ID
        e.dataTransfer.setData('text/plain', this.getAttribute('data-widget') || this.id);

        // Add a class to the dragged element
        this.classList.add('dragging');

        // Add a class to the body
        document.body.classList.add('dragging-active');
    }

    /**
     * Handle drag end event
     */
    function handleDragEnd() {
        console.log('Drag ended');

        // Remove the class from the dragged element
        this.classList.remove('dragging');

        // Remove the class from the body
        document.body.classList.remove('dragging-active');
    }

    /**
     * Handle drag over event
     */
    function handleDragOver(e) {
        // Prevent default to allow drop
        e.preventDefault();
    }

    /**
     * Handle drop event
     */
    function handleDrop(e) {
        // Prevent default behavior
        e.preventDefault();

        // Get the dragged widget ID
        const draggedId = e.dataTransfer.getData('text/plain');
        console.log('Dropped widget:', draggedId);

        // Get the dragged widget
        const draggedWidget = document.querySelector(`[data-widget="${draggedId}"]`) ||
                             document.getElementById(draggedId);

        if (!draggedWidget) {
            console.error('Dragged widget not found:', draggedId);
            return;
        }

        // Get the container
        const container = document.getElementById('dashboard-widgets');
        if (!container) {
            console.error('Container not found');
            return;
        }

        // Get all widgets
        const widgets = Array.from(container.querySelectorAll('[data-widget]'));

        // Get the drop position
        const mouseY = e.clientY;

        // Find the widget to insert before
        let insertBeforeWidget = null;

        for (const widget of widgets) {
            if (widget === draggedWidget) continue;

            const rect = widget.getBoundingClientRect();
            const widgetMiddle = rect.top + rect.height / 2;

            if (mouseY < widgetMiddle) {
                insertBeforeWidget = widget;
                break;
            }
        }

        // Insert the dragged widget
        if (insertBeforeWidget) {
            container.insertBefore(draggedWidget, insertBeforeWidget);
        } else {
            container.appendChild(draggedWidget);
        }

        // Save the arrangement
        saveArrangement();

        // Show a message
        showMessage('Widget moved successfully!');
    }

    /**
     * Save the current arrangement
     */
    function saveArrangement() {
        console.log('Saving arrangement');

        // Get the container
        const container = document.getElementById('dashboard-widgets');
        if (!container) {
            console.error('Container not found');
            return;
        }

        // Get all widgets
        const widgets = container.querySelectorAll('[data-widget]');

        // Create an array of widget IDs
        const arrangement = Array.from(widgets).map(widget => {
            return widget.getAttribute('data-widget') || widget.id;
        });

        // Save to localStorage
        localStorage.setItem('dashboard_widget_arrangement', JSON.stringify(arrangement));
        console.log('Arrangement saved:', arrangement);
    }

    /**
     * Load the saved arrangement
     */
    function loadSavedArrangement() {
        console.log('Loading saved arrangement');

        // Get the saved arrangement
        const savedArrangement = localStorage.getItem('dashboard_widget_arrangement');
        if (!savedArrangement) {
            console.log('No saved arrangement found');
            return;
        }

        try {
            // Parse the saved arrangement
            const arrangement = JSON.parse(savedArrangement);
            console.log('Found saved arrangement:', arrangement);

            // Get the container
            const container = document.getElementById('dashboard-widgets');
            if (!container) {
                console.error('Container not found');
                return;
            }

            // Reorder widgets
            arrangement.forEach(widgetId => {
                const widget = document.querySelector(`[data-widget="${widgetId}"]`) ||
                              document.getElementById(widgetId);

                if (widget) {
                    container.appendChild(widget);
                }
            });

            console.log('Arrangement applied');
        } catch (error) {
            console.error('Error loading arrangement:', error);
        }
    }

    /**
     * Setup save and reset buttons
     */
    function setupButtons() {
        const saveBtn = document.getElementById('save-layout-btn');
        const resetBtn = document.getElementById('reset-layout-btn');

        if (saveBtn) {
            saveBtn.addEventListener('click', function() {
                saveArrangement();
                showMessage('Layout saved!');
            });
        }

        if (resetBtn) {
            resetBtn.addEventListener('click', function() {
                if (confirm('Are you sure you want to reset the layout?')) {
                    localStorage.removeItem('dashboard_widget_arrangement');
                    showMessage('Layout reset! Reloading page...');
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                }
            });
        }
    }

    /**
     * Show a message
     */
    function showMessage(text) {
        // Create message element
        const message = document.createElement('div');
        message.className = 'drag-drop-message';
        message.textContent = text;
        message.style.position = 'fixed';
        message.style.bottom = '20px';
        message.style.right = '20px';
        message.style.backgroundColor = 'rgba(139, 92, 246, 0.9)';
        message.style.color = 'white';
        message.style.padding = '10px 15px';
        message.style.borderRadius = '4px';
        message.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
        message.style.zIndex = '9999';

        // Add to body
        document.body.appendChild(message);

        // Remove after 3 seconds
        setTimeout(function() {
            document.body.removeChild(message);
        }, 3000);
    }
});
