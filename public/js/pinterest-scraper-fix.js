/**
 * Pinterest Scraper Fix
 *
 * This script fixes issues with the Pinterest scraper form submission and UI interactions.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Pinterest Scraper Fix loaded');

    // Fix for advanced options toggle
    const toggleAdvanced = document.getElementById('toggle-advanced');
    const advancedOptions = document.getElementById('advanced-options');
    const advancedShow = document.getElementById('advanced-show');
    const advancedHide = document.getElementById('advanced-hide');

    if (toggleAdvanced && advancedOptions) {
        console.log('Found advanced options elements');

        // Make sure the advanced options are hidden by default
        if (!advancedOptions.classList.contains('hidden')) {
            advancedOptions.classList.add('hidden');
        }

        // Add click handler
        toggleAdvanced.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Toggle advanced clicked');
            advancedOptions.classList.toggle('hidden');

            if (advancedShow && advancedHide) {
                advancedShow.classList.toggle('hidden');
                advancedHide.classList.toggle('hidden');
            }
        });
    }

    // Fix for real scraping toggle
    const useRealScraping = document.getElementById('use_real_scraping');
    const pinterestCredentials = document.getElementById('pinterest-credentials');

    if (useRealScraping && pinterestCredentials) {
        console.log('Found Pinterest credentials elements');

        // Make sure credentials are hidden by default
        if (!pinterestCredentials.classList.contains('hidden')) {
            pinterestCredentials.classList.add('hidden');
        }

        // Add change handler
        useRealScraping.addEventListener('change', function() {
            console.log('Use real scraping changed:', this.checked);
            if (this.checked) {
                pinterestCredentials.classList.remove('hidden');
            } else {
                pinterestCredentials.classList.add('hidden');
            }
        });
    }

    // Make sure scraping progress is hidden by default
    const scrapingProgress = document.getElementById('scraping-progress');
    if (scrapingProgress) {
        console.log('Found scraping progress element');
        if (!scrapingProgress.classList.contains('hidden')) {
            console.log('Hiding scraping progress');
            scrapingProgress.classList.add('hidden');
        }
    }

    // Fix for the scraper form
    const scraperForm = document.getElementById('scraper-form');

    if (scraperForm) {
        console.log('Found scraper form, adding direct handler');

        // Remove existing event listeners by cloning
        const newForm = scraperForm.cloneNode(true);
        scraperForm.parentNode.replaceChild(newForm, scraperForm);

        // Add our direct submit handler
        newForm.addEventListener('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Scraper form submitted');

            // Get form data
            const formData = new FormData(newForm);

            // Validate form
            const searchTerm = document.getElementById('search_term').value.trim();
            if (!searchTerm) {
                alert('Please enter a search term');
                return false;
            }

            // Show progress indicator
            const progressElement = document.getElementById('scraping-progress');
            if (progressElement) {
                progressElement.classList.remove('hidden');

                // Update progress bar to show activity
                const progressBar = document.getElementById('progress-bar');
                const progressStatus = document.getElementById('progress-status');
                const progressPercentage = document.getElementById('progress-percentage');
                const progressMessage = document.getElementById('progress-message');

                if (progressBar) progressBar.style.width = '10%';
                if (progressStatus) progressStatus.textContent = 'Processing...';
                if (progressPercentage) progressPercentage.textContent = '10%';
                if (progressMessage) progressMessage.textContent = 'Connecting to Pinterest servers...';
            }

            // Disable submit button
            const submitButton = newForm.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.classList.add('opacity-50', 'cursor-not-allowed');
            }

            // Submit the form via fetch API
            fetch('/momentum/clone/pinterest/process-scrape', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);

                if (data.success && data.scrape_id) {
                    // Show success message
                    if (progressElement) {
                        if (progressStatus) {
                            progressStatus.textContent = 'Completed';
                            progressStatus.classList.remove('text-blue-600', 'bg-blue-200');
                            progressStatus.classList.add('text-green-600', 'bg-green-200');
                        }

                        if (progressBar) progressBar.style.width = '100%';
                        if (progressPercentage) progressPercentage.textContent = '100%';
                        if (progressMessage) progressMessage.textContent = 'Scrape completed successfully! Redirecting to results...';
                    }

                    // Redirect to the results page
                    console.log('Redirecting to view-scrape/' + data.scrape_id);
                    setTimeout(() => {
                        window.location.href = '/momentum/clone/pinterest/view-scrape/' + data.scrape_id;
                    }, 1500);
                } else {
                    // Show error
                    if (progressStatus) {
                        progressStatus.textContent = 'Error';
                        progressStatus.classList.remove('text-blue-600', 'bg-blue-200');
                        progressStatus.classList.add('text-red-600', 'bg-red-200');
                    }

                    if (progressMessage) {
                        progressMessage.textContent = data.message || 'An error occurred while processing the scrape request.';
                    }

                    // Re-enable submit button
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);

                // Show error
                if (progressStatus) {
                    progressStatus.textContent = 'Error';
                    progressStatus.classList.remove('text-blue-600', 'bg-blue-200');
                    progressStatus.classList.add('text-red-600', 'bg-red-200');
                }

                if (progressMessage) {
                    progressMessage.textContent = 'An error occurred while processing the scrape request.';
                }

                // Re-enable submit button
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                }
            });

            return false;
        });
    }
});
