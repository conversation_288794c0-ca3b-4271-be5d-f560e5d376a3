/**
 * Main JavaScript Entry Point
 * 
 * This file serves as the main entry point for the application's JavaScript.
 * It loads all required modules and initializes them.
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Main JavaScript loaded');
    
    // Initialize modules
    initializeModules();
    
    // Setup global event listeners
    setupGlobalEventListeners();
});

/**
 * Initialize all modules
 */
function initializeModules() {
    // Check if modules are available
    if (window.DashboardLayout) {
        console.log('Initializing DashboardLayout module');
        window.DashboardLayout.init();
    }
    
    if (window.TaskManager) {
        console.log('Initializing TaskManager module');
        window.TaskManager.init();
    }
}

/**
 * Setup global event listeners
 */
function setupGlobalEventListeners() {
    // Listen for keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Escape key to close modals
        if (e.key === 'Escape') {
            closeAllModals();
        }
        
        // Backspace key to go back (only when not in an input field)
        if (e.key === 'Backspace' && !isInputFocused()) {
            e.preventDefault();
            window.history.back();
        }
    });
    
    // Handle dark mode toggle
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            toggleDarkMode();
        });
    }
}

/**
 * Check if an input field is currently focused
 */
function isInputFocused() {
    const activeElement = document.activeElement;
    const inputTags = ['INPUT', 'TEXTAREA', 'SELECT'];
    
    return inputTags.includes(activeElement.tagName) || 
           activeElement.isContentEditable || 
           activeElement.getAttribute('role') === 'textbox';
}

/**
 * Close all open modals
 */
function closeAllModals() {
    document.querySelectorAll('.modal:not(.hidden)').forEach(modal => {
        modal.classList.add('hidden');
    });
}

/**
 * Toggle dark mode
 */
function toggleDarkMode() {
    const html = document.documentElement;
    const isDarkMode = html.classList.contains('dark');
    
    if (isDarkMode) {
        html.classList.remove('dark');
        localStorage.setItem('theme', 'light');
    } else {
        html.classList.add('dark');
        localStorage.setItem('theme', 'dark');
    }
}

// Make utility functions available globally
window.closeAllModals = closeAllModals;
window.toggleDarkMode = toggleDarkMode;
