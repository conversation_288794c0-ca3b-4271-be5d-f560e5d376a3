/**
 * AI Prompts General JavaScript
 * 
 * General functionality for the AI Prompts system
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('AI Prompts system loaded');
    
    // Initialize general functionality
    initPromptCards();
    initSearchAndFilters();
    initKeyboardShortcuts();
    initTooltips();
    initDarkModeSupport();
});

/**
 * Initialize prompt card interactions
 */
function initPromptCards() {
    // Add hover effects and interactions to prompt cards
    const promptCards = document.querySelectorAll('.prompt-card');
    
    promptCards.forEach(card => {
        // Add click handler for card navigation
        card.addEventListener('click', function(e) {
            // Don't navigate if clicking on action buttons
            if (e.target.closest('.prompt-actions') || e.target.closest('button') || e.target.closest('a')) {
                return;
            }
            
            // Find the view link and navigate
            const viewLink = card.querySelector('a[href*="/view/"]');
            if (viewLink) {
                window.location.href = viewLink.href;
            }
        });
        
        // Add keyboard navigation
        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                card.click();
            }
        });
        
        // Make cards focusable
        if (!card.hasAttribute('tabindex')) {
            card.setAttribute('tabindex', '0');
        }
    });
}

/**
 * Initialize search and filter functionality
 */
function initSearchAndFilters() {
    const searchInput = document.getElementById('search');
    const categoryFilter = document.getElementById('category');
    const sortFilter = document.getElementById('sort');
    
    // Debounced search
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch();
            }, 300);
        });
    }
    
    // Filter changes
    if (categoryFilter) {
        categoryFilter.addEventListener('change', performSearch);
    }
    
    if (sortFilter) {
        sortFilter.addEventListener('change', performSearch);
    }
    
    // Clear filters button
    const clearFiltersBtn = document.querySelector('[href*="clear"]');
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', function(e) {
            e.preventDefault();
            clearAllFilters();
        });
    }
}

/**
 * Perform search with current filters
 */
function performSearch() {
    const searchInput = document.getElementById('search');
    const categoryFilter = document.getElementById('category');
    const sortFilter = document.getElementById('sort');
    const favoritesOnly = document.querySelector('input[name="favorites_only"]');
    const templatesOnly = document.querySelector('input[name="templates_only"]');
    
    const params = new URLSearchParams();
    
    if (searchInput && searchInput.value) {
        params.set('search', searchInput.value);
    }
    
    if (categoryFilter && categoryFilter.value) {
        params.set('category', categoryFilter.value);
    }
    
    if (sortFilter && sortFilter.value) {
        params.set('sort', sortFilter.value);
    }
    
    if (favoritesOnly && favoritesOnly.checked) {
        params.set('favorites_only', '1');
    }
    
    if (templatesOnly && templatesOnly.checked) {
        params.set('templates_only', '1');
    }
    
    // Update URL and reload
    const newUrl = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
    window.location.href = newUrl;
}

/**
 * Clear all filters
 */
function clearAllFilters() {
    const searchInput = document.getElementById('search');
    const categoryFilter = document.getElementById('category');
    const sortFilter = document.getElementById('sort');
    const favoritesOnly = document.querySelector('input[name="favorites_only"]');
    const templatesOnly = document.querySelector('input[name="templates_only"]');
    
    if (searchInput) searchInput.value = '';
    if (categoryFilter) categoryFilter.value = '';
    if (sortFilter) sortFilter.value = 'created_desc';
    if (favoritesOnly) favoritesOnly.checked = false;
    if (templatesOnly) templatesOnly.checked = false;
    
    // Navigate to clean URL
    window.location.href = window.location.pathname;
}

/**
 * Initialize keyboard shortcuts
 */
function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Only handle shortcuts when not in input fields
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.isContentEditable) {
            return;
        }
        
        // Ctrl/Cmd + N: New prompt
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            window.location.href = '/momentum/ai-prompts/create';
        }
        
        // Ctrl/Cmd + F: Focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.getElementById('search');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }
        
        // Escape: Clear search
        if (e.key === 'Escape') {
            const searchInput = document.getElementById('search');
            if (searchInput && searchInput.value) {
                searchInput.value = '';
                performSearch();
            }
        }
        
        // L: Go to library
        if (e.key === 'l' || e.key === 'L') {
            window.location.href = '/momentum/ai-prompts/library';
        }
        
        // H: Go to home/dashboard
        if (e.key === 'h' || e.key === 'H') {
            window.location.href = '/momentum/ai-prompts';
        }
    });
}

/**
 * Initialize tooltips
 */
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[title]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

/**
 * Show tooltip
 */
function showTooltip(e) {
    const element = e.target;
    const title = element.getAttribute('title');
    
    if (!title) return;
    
    // Remove title to prevent browser tooltip
    element.setAttribute('data-original-title', title);
    element.removeAttribute('title');
    
    // Create tooltip
    const tooltip = document.createElement('div');
    tooltip.className = 'ai-prompt-tooltip fixed z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg pointer-events-none';
    tooltip.textContent = title;
    tooltip.id = 'ai-prompt-tooltip';
    
    document.body.appendChild(tooltip);
    
    // Position tooltip
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
    
    // Ensure tooltip stays in viewport
    const tooltipRect = tooltip.getBoundingClientRect();
    if (tooltipRect.left < 0) {
        tooltip.style.left = '5px';
    }
    if (tooltipRect.right > window.innerWidth) {
        tooltip.style.left = (window.innerWidth - tooltip.offsetWidth - 5) + 'px';
    }
    if (tooltipRect.top < 0) {
        tooltip.style.top = rect.bottom + 5 + 'px';
    }
}

/**
 * Hide tooltip
 */
function hideTooltip(e) {
    const element = e.target;
    const originalTitle = element.getAttribute('data-original-title');
    
    if (originalTitle) {
        element.setAttribute('title', originalTitle);
        element.removeAttribute('data-original-title');
    }
    
    const tooltip = document.getElementById('ai-prompt-tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

/**
 * Initialize dark mode support
 */
function initDarkModeSupport() {
    // Watch for dark mode changes
    const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    darkModeMediaQuery.addEventListener('change', handleDarkModeChange);
    
    // Check for saved theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        applyTheme(savedTheme);
    }
}

/**
 * Handle dark mode change
 */
function handleDarkModeChange(e) {
    if (!localStorage.getItem('theme')) {
        // Only auto-switch if user hasn't set a preference
        applyTheme(e.matches ? 'dark' : 'light');
    }
}

/**
 * Apply theme
 */
function applyTheme(theme) {
    if (theme === 'dark') {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
    }
}

/**
 * Toggle favorite status
 */
function toggleFavorite(promptId) {
    fetch(`/momentum/ai-prompts/toggle-favorite/${promptId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI
            const favoriteButtons = document.querySelectorAll(`[onclick="toggleFavorite(${promptId})"]`);
            favoriteButtons.forEach(button => {
                const icon = button.querySelector('i');
                if (data.is_favorite) {
                    icon.classList.remove('text-gray-400');
                    icon.classList.add('text-yellow-500');
                    button.setAttribute('title', 'Remove from favorites');
                } else {
                    icon.classList.remove('text-yellow-500');
                    icon.classList.add('text-gray-400');
                    button.setAttribute('title', 'Add to favorites');
                }
            });
            
            showNotification(
                data.is_favorite ? 'Added to favorites!' : 'Removed from favorites!',
                'success'
            );
        } else {
            showNotification('Failed to update favorite status', 'error');
        }
    })
    .catch(error => {
        console.error('Error toggling favorite:', error);
        showNotification('Failed to update favorite status', 'error');
    });
}

/**
 * Delete prompt with confirmation
 */
function deletePrompt(promptId) {
    if (!confirm('Are you sure you want to delete this prompt? This action cannot be undone.')) {
        return;
    }
    
    fetch(`/momentum/ai-prompts/delete/${promptId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the prompt card from the page
            const promptCard = document.querySelector(`[data-prompt-id="${promptId}"]`);
            if (promptCard) {
                promptCard.remove();
            }
            
            showNotification('Prompt deleted successfully', 'success');
            
            // Redirect if we're on the prompt's detail page
            if (window.location.pathname.includes(`/view/${promptId}`) || 
                window.location.pathname.includes(`/edit/${promptId}`)) {
                window.location.href = '/momentum/ai-prompts';
            }
        } else {
            showNotification('Failed to delete prompt', 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting prompt:', error);
        showNotification('Failed to delete prompt', 'error');
    });
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.ai-prompt-notification');
    existingNotifications.forEach(notification => notification.remove());
    
    const notification = document.createElement('div');
    notification.className = `ai-prompt-notification fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white max-w-sm transform transition-all duration-300 ${
        type === 'error' ? 'bg-red-500' : 
        type === 'success' ? 'bg-green-500' : 
        type === 'warning' ? 'bg-yellow-500' :
        'bg-blue-500'
    }`;
    
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${
                type === 'error' ? 'fa-exclamation-circle' :
                type === 'success' ? 'fa-check-circle' :
                type === 'warning' ? 'fa-exclamation-triangle' :
                'fa-info-circle'
            } mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentNode.parentNode.remove()" class="ml-3 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    }, 5000);
}

/**
 * Copy text to clipboard
 */
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('Copied to clipboard!', 'success');
        }).catch(err => {
            console.error('Failed to copy: ', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

/**
 * Fallback copy method for older browsers
 */
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showNotification('Copied to clipboard!', 'success');
        } else {
            showNotification('Failed to copy text', 'error');
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        showNotification('Failed to copy text', 'error');
    }
    
    document.body.removeChild(textArea);
}

// Export functions for global use
window.toggleFavorite = toggleFavorite;
window.deletePrompt = deletePrompt;
window.copyToClipboard = copyToClipboard;
window.showNotification = showNotification;
