/**
 * Dashboard Layout Debug Script
 * 
 * This script helps debug issues with the dashboard layout functionality.
 * It logs information about the dashboard widgets container and layout changes.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard Layout Debug Script loaded');
    
    // Check if dashboard widgets container exists
    const dashboardWidgets = document.getElementById('dashboard-widgets');
    
    if (dashboardWidgets) {
        console.log('Dashboard widgets container found:', {
            id: dashboardWidgets.id,
            viewMode: dashboardWidgets.getAttribute('data-view-mode'),
            arrangement: dashboardWidgets.getAttribute('data-arrangement'),
            childCount: dashboardWidgets.children.length,
            classes: dashboardWidgets.className
        });
        
        // Log all widgets
        const widgets = dashboardWidgets.querySelectorAll('[data-widget]');
        console.log(`Found ${widgets.length} widgets:`);
        widgets.forEach((widget, index) => {
            console.log(`Widget ${index + 1}:`, {
                widgetType: widget.getAttribute('data-widget'),
                id: widget.id,
                classes: widget.className
            });
        });
        
        // Monitor for changes to data-view-mode attribute
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                if (mutation.type === 'attributes' && 
                    (mutation.attributeName === 'data-view-mode' || 
                     mutation.attributeName === 'data-arrangement')) {
                    console.log('Dashboard layout changed:', {
                        viewMode: dashboardWidgets.getAttribute('data-view-mode'),
                        arrangement: dashboardWidgets.getAttribute('data-arrangement'),
                        timestamp: new Date().toISOString()
                    });
                }
            });
        });
        
        observer.observe(dashboardWidgets, { 
            attributes: true,
            attributeFilter: ['data-view-mode', 'data-arrangement']
        });
        
        // Add test buttons for direct layout changes
        const testButtons = document.createElement('div');
        testButtons.className = 'fixed bottom-4 right-4 flex flex-col space-y-2 bg-white dark:bg-gray-800 p-2 rounded shadow-lg z-50';
        testButtons.innerHTML = `
            <div class="text-xs font-bold mb-1 text-gray-500 dark:text-gray-400">Debug Layout</div>
            <button class="px-2 py-1 text-xs bg-blue-500 text-white rounded" onclick="testLayout('adhd-optimized')">ADHD Layout</button>
            <button class="px-2 py-1 text-xs bg-green-500 text-white rounded" onclick="testLayout('focus')">Focus Layout</button>
            <button class="px-2 py-1 text-xs bg-purple-500 text-white rounded" onclick="testLayout('standard')">Standard Layout</button>
            <button class="px-2 py-1 text-xs bg-gray-500 text-white rounded" onclick="testLayout('custom')">Custom Layout</button>
        `;
        
        // Only add in development environment
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            document.body.appendChild(testButtons);
        }
        
        // Global test function
        window.testLayout = function(mode) {
            console.log('Testing layout mode:', mode);
            dashboardWidgets.setAttribute('data-view-mode', mode);
            dashboardWidgets.setAttribute('data-arrangement', mode);
            
            // Update view mode text
            const viewModeText = document.getElementById('view-mode-text');
            if (viewModeText) {
                viewModeText.textContent = mode.charAt(0).toUpperCase() + mode.slice(1) + ' View';
            }
            
            // Save preference
            localStorage.setItem('dashboard_view_mode', mode);
        };
    } else {
        console.error('Dashboard widgets container not found!');
        
        // Log all elements with IDs to help troubleshoot
        const elementsWithId = document.querySelectorAll('[id]');
        console.log(`Found ${elementsWithId.length} elements with IDs:`);
        elementsWithId.forEach((el, index) => {
            console.log(`Element ${index + 1}:`, {
                id: el.id,
                tagName: el.tagName,
                classes: el.className
            });
        });
    }
    
    // Check for layout buttons and dropdowns
    const layoutButtons = document.querySelectorAll('button[id*="layout"], .layout-button, [data-action="layout"]');
    console.log(`Found ${layoutButtons.length} layout buttons`);
    
    const layoutDropdowns = document.querySelectorAll('#layout-dropdown, [id*="layout-dropdown"]');
    console.log(`Found ${layoutDropdowns.length} layout dropdowns`);
    
    // Check for view options
    const viewOptions = document.querySelectorAll('[data-view]');
    console.log(`Found ${viewOptions.length} view options`);
    viewOptions.forEach((option, index) => {
        console.log(`View option ${index + 1}:`, {
            viewMode: option.getAttribute('data-view'),
            text: option.textContent.trim()
        });
    });
});
