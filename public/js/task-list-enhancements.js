/**
 * Task List Enhancements
 * ADHD-friendly enhancements for the task list view
 * Optimized for performance and ADHD users
 */

// Cache DOM elements and frequently used values
let cachedTaskRows = [];
let cachedGroups = {};
let lastGroupBy = '';
let isGroupingInProgress = false;

// Store the last filter applied to handle page refreshes
let lastAppliedFilter = '';

// Initialize on DOM content loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Task List Enhancements loaded');

    // Add page transition effect for smoother experience
    document.body.classList.add('page-loaded');

    // Pre-cache task rows for better performance
    cacheTaskRows();

    // Initialize task grouping
    initTaskGrouping();

    // Initialize quick filters
    initQuickFilters();

    // Initialize compact view toggle
    initCompactViewToggle();

    // Initialize expandable task details
    initExpandableTaskDetails();

    // Initialize keyboard navigation
    initKeyboardNavigation();

    // Handle browser back/forward navigation
    window.addEventListener('popstate', handlePopState);
});

/**
 * Cache task rows for better performance
 */
function cacheTaskRows() {
    const taskTable = document.querySelector('.task-table');
    if (!taskTable) return;

    const rows = Array.from(taskTable.querySelectorAll('tbody tr.task-row'));
    if (rows.length === 0) return;

    cachedTaskRows = rows.map(row => ({
        element: row,
        status: row.getAttribute('data-status') || 'No Status',
        priority: row.getAttribute('data-priority') || 'No Priority',
        category: row.getAttribute('data-category') || 'No Category',
        categoryColor: row.getAttribute('data-category-color') || '#6b7280',
        dueDate: row.getAttribute('data-due-date') || '',
        taskId: row.getAttribute('data-task-id') || ''
    }));
}

/**
 * Initialize task grouping functionality
 */
function initTaskGrouping() {
    // Get the grouping select element
    const groupingSelect = document.getElementById('task-grouping');
    if (!groupingSelect) return;

    // Add event listener for grouping change with debounce
    groupingSelect.addEventListener('change', debounce(function() {
        const groupBy = this.value;
        groupTasks(groupBy);
    }, 100));

    // Initial grouping
    groupTasks(groupingSelect.value);

    // Use event delegation for better performance
    document.addEventListener('click', function(e) {
        const header = e.target.closest('.task-group-header');
        if (!header) return;

        const groupName = header.getAttribute('data-group');
        const content = document.querySelector(`.task-group-content[data-group-content="${groupName}"]`);
        if (!content) return;

        // Toggle collapsed class
        content.classList.toggle('collapsed');

        // Update icon
        const icon = header.querySelector('.group-toggle-icon');
        if (icon) {
            if (content.classList.contains('collapsed')) {
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-right');
            } else {
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-down');
            }
        }
    });
}

/**
 * Debounce function to limit how often a function can be called
 */
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}

/**
 * Group tasks based on the selected grouping option with performance optimizations
 */
function groupTasks(groupBy) {
    // Prevent multiple grouping operations from running simultaneously
    if (isGroupingInProgress) return;
    isGroupingInProgress = true;

    // If we're regrouping with the same criteria, use cached groups
    if (groupBy === lastGroupBy && Object.keys(cachedGroups).length > 0) {
        renderCachedGroups();
        isGroupingInProgress = false;
        return;
    }

    // Store the current grouping for future reference
    lastGroupBy = groupBy;

    const taskTable = document.querySelector('.task-table');
    if (!taskTable) {
        isGroupingInProgress = false;
        return;
    }

    // Use cached task rows for better performance
    if (cachedTaskRows.length === 0) {
        cacheTaskRows();
        if (cachedTaskRows.length === 0) {
            isGroupingInProgress = false;
            return;
        }
    }

    // Clear existing tbody content
    const tbody = taskTable.querySelector('tbody');

    // Use document fragment for better performance
    const fragment = document.createDocumentFragment();

    // Group tasks
    const groups = {};
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    // Group tasks using cached data
    cachedTaskRows.forEach(row => {
        let groupValue;

        switch(groupBy) {
            case 'category':
                groupValue = row.category;
                break;
            case 'priority':
                groupValue = row.priority;
                break;
            case 'status':
                groupValue = row.status;
                break;
            case 'due_date':
                if (!row.dueDate) {
                    groupValue = 'No Due Date';
                } else {
                    const taskDate = new Date(row.dueDate);
                    taskDate.setHours(0, 0, 0, 0);

                    if (taskDate < today) {
                        groupValue = 'Overdue';
                    } else if (taskDate.getTime() === today.getTime()) {
                        groupValue = 'Today';
                    } else if (taskDate.getTime() === tomorrow.getTime()) {
                        groupValue = 'Tomorrow';
                    } else if (taskDate < nextWeek) {
                        groupValue = 'This Week';
                    } else {
                        groupValue = 'Later';
                    }
                }
                break;
            default:
                groupValue = 'All Tasks';
        }

        if (!groups[groupValue]) {
            groups[groupValue] = [];
        }

        groups[groupValue].push(row);
    });

    // Cache the groups for future use
    cachedGroups = groups;

    // Sort group keys based on priority
    const sortedGroups = Object.keys(groups).sort((a, b) => {
        // Custom sorting for priority
        if (groupBy === 'priority') {
            const priorityOrder = { 'urgent': 0, 'high': 1, 'medium': 2, 'low': 3, 'No Priority': 4 };
            return priorityOrder[a] - priorityOrder[b];
        }

        // Custom sorting for status
        if (groupBy === 'status') {
            const statusOrder = { 'in_progress': 0, 'todo': 1, 'done': 2, 'No Status': 3 };
            return statusOrder[a] - statusOrder[b];
        }

        // Custom sorting for due date
        if (groupBy === 'due_date') {
            const dateOrder = { 'Overdue': 0, 'Today': 1, 'Tomorrow': 2, 'This Week': 3, 'Later': 4, 'No Due Date': 5 };
            return dateOrder[a] - dateOrder[b];
        }

        // Default alphabetical sorting
        return a.localeCompare(b);
    });

    // Create group headers and add rows
    sortedGroups.forEach(groupValue => {
        const rows = groups[groupValue];

        // Create group header
        const headerRow = document.createElement('tr');
        headerRow.className = 'task-group-header';
        headerRow.setAttribute('data-group', groupValue);

        // Get appropriate icon and color for the group
        let groupIcon, groupColor;

        switch(groupBy) {
            case 'category':
                groupIcon = 'fa-tag';
                // Use the category color if available
                groupColor = rows[0].categoryColor || '#6366f1';
                break;
            case 'priority':
                groupIcon = 'fa-flag';
                switch(groupValue) {
                    case 'urgent': groupColor = '#ef4444'; break;
                    case 'high': groupColor = '#f59e0b'; break;
                    case 'medium': groupColor = '#3b82f6'; break;
                    case 'low': groupColor = '#10b981'; break;
                    default: groupColor = '#6b7280';
                }
                break;
            case 'status':
                groupIcon = 'fa-tasks';
                switch(groupValue) {
                    case 'todo': groupColor = '#9ca3af'; break;
                    case 'in_progress': groupColor = '#3b82f6'; break;
                    case 'done': groupColor = '#10b981'; break;
                    default: groupColor = '#6b7280';
                }
                break;
            case 'due_date':
                groupIcon = 'fa-calendar';
                switch(groupValue) {
                    case 'Overdue': groupColor = '#ef4444'; break;
                    case 'Today': groupColor = '#f59e0b'; break;
                    case 'Tomorrow': groupColor = '#3b82f6'; break;
                    case 'This Week': groupColor = '#8b5cf6'; break;
                    default: groupColor = '#6b7280';
                }
                break;
            default:
                groupIcon = 'fa-tasks';
                groupColor = '#6366f1';
        }

        const headerCell = document.createElement('td');
        headerCell.colSpan = 6;
        headerCell.className = 'task-group-header';
        headerCell.style.borderLeftColor = groupColor;

        headerCell.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${groupIcon} mr-2" style="color: ${groupColor}"></i>
                <span>${groupValue}</span>
            </div>
            <div class="flex items-center">
                <span class="group-count">${rows.length}</span>
                <i class="fas fa-chevron-down ml-2 group-toggle-icon"></i>
            </div>
        `;

        headerRow.appendChild(headerCell);
        fragment.appendChild(headerRow);

        // Create group content container
        const contentRow = document.createElement('tr');
        contentRow.className = 'task-group-content';
        contentRow.setAttribute('data-group-content', groupValue);

        const contentCell = document.createElement('td');
        contentCell.colSpan = 6;
        contentCell.className = 'p-0';

        // Use a div instead of nested tables for better performance
        const contentDiv = document.createElement('div');
        contentDiv.className = 'task-group-items';

        // Add task rows to the group
        rows.forEach(row => {
            contentDiv.appendChild(row.element.cloneNode(true));
        });

        contentCell.appendChild(contentDiv);
        contentRow.appendChild(contentCell);
        fragment.appendChild(contentRow);
    });

    // Clear and update the DOM in a single operation
    tbody.innerHTML = '';
    tbody.appendChild(fragment);

    // Reattach event listeners
    reattachEventListeners();

    isGroupingInProgress = false;
}

/**
 * Render cached groups without rebuilding them
 */
function renderCachedGroups() {
    const taskTable = document.querySelector('.task-table');
    if (!taskTable) return;

    const tbody = taskTable.querySelector('tbody');

    // Toggle collapsed state for groups that were previously collapsed
    const collapsedGroups = Array.from(tbody.querySelectorAll('.task-group-content.collapsed'))
        .map(el => el.getAttribute('data-group-content'));

    // Regroup using the cached data
    groupTasks(lastGroupBy);

    // Restore collapsed state
    collapsedGroups.forEach(groupName => {
        const groupContent = tbody.querySelector(`.task-group-content[data-group-content="${groupName}"]`);
        if (groupContent) {
            groupContent.classList.add('collapsed');

            // Update icon
            const header = tbody.querySelector(`.task-group-header[data-group="${groupName}"]`);
            if (header) {
                const icon = header.querySelector('.group-toggle-icon');
                if (icon) {
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-right');
                }
            }
        }
    });
}

/**
 * Reattach event listeners after DOM updates
 */
function reattachEventListeners() {
    // Reattach checkbox event listeners
    const taskCheckboxes = document.querySelectorAll('.task-checkbox');
    taskCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', handleTaskCheckboxChange);
    });

    // Make rows focusable again
    const taskRows = document.querySelectorAll('.task-row');
    taskRows.forEach(row => {
        row.setAttribute('tabindex', '0');
        row.addEventListener('dblclick', handleTaskRowDoubleClick);
    });
}

/**
 * Initialize quick filters functionality
 */
function initQuickFilters() {
    const quickFilterContainer = document.querySelector('.quick-filter-buttons');
    if (!quickFilterContainer) return;

    // Remove any existing event listeners by cloning and replacing the container
    const newContainer = quickFilterContainer.cloneNode(true);
    quickFilterContainer.parentNode.replaceChild(newContainer, quickFilterContainer);

    // Add event listeners to quick filter buttons
    const quickFilterButtons = newContainer.querySelectorAll('.quick-filter-button');

    // Check if we need to restore a filter from localStorage (for page refreshes)
    const savedFilter = localStorage.getItem('lastTaskFilter');

    // Set initial active state based on URL or localStorage
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('due_date') && urlParams.get('due_date') === new Date().toISOString().split('T')[0]) {
        setActiveFilter('today');
    } else if (urlParams.has('due_date_range') && urlParams.get('due_date_range') === 'week') {
        setActiveFilter('this-week');
    } else if (urlParams.has('overdue') && urlParams.get('overdue') === '1') {
        setActiveFilter('overdue');
    } else if (urlParams.has('priority') && urlParams.get('priority') === 'high') {
        setActiveFilter('high-priority');
    } else if (urlParams.has('status') && urlParams.get('status') === 'in_progress') {
        setActiveFilter('in-progress');
    } else if (savedFilter) {
        // If no URL parameters but we have a saved filter, apply it
        setActiveFilter(savedFilter);

        // Apply the filter without page reload
        setTimeout(() => {
            const activeButton = document.querySelector(`.quick-filter-button[data-filter="${savedFilter}"]`);
            if (activeButton && !activeButton.classList.contains('active-applied')) {
                activeButton.classList.add('active-applied');
                applyQuickFilter(savedFilter);
            }
        }, 300);
    }

    quickFilterButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // If already active, do nothing
            if (this.classList.contains('active')) {
                return;
            }

            // Remove active class from all buttons
            quickFilterButtons.forEach(btn => {
                btn.classList.remove('active');
                btn.classList.remove('active-applied');
            });

            // Add active class to clicked button
            this.classList.add('active');

            // Apply the filter
            const filter = this.getAttribute('data-filter');
            applyQuickFilter(filter);
        });
    });
}

/**
 * Set active filter button
 */
function setActiveFilter(filterName) {
    const buttons = document.querySelectorAll('.quick-filter-button');
    buttons.forEach(btn => {
        if (btn.getAttribute('data-filter') === filterName) {
            btn.classList.add('active');
        } else {
            btn.classList.remove('active');
        }
    });
}

/**
 * Apply quick filter to tasks using AJAX
 */
function applyQuickFilter(filter) {
    // Prevent multiple simultaneous filter operations
    if (window.isFilteringInProgress) {
        return;
    }

    // Store the filter for handling page refreshes
    lastAppliedFilter = filter;
    localStorage.setItem('lastTaskFilter', filter);

    window.isFilteringInProgress = true;

    // Add page transition effect - very subtle
    document.body.classList.add('filtering');

    // Show loading indicator
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'loading-indicator';
    document.body.appendChild(loadingIndicator);

    // Build the filter parameters
    let params = new URLSearchParams();
    params.append('filter_processed', '1');

    switch(filter) {
        case 'today':
            params.append('due_date', new Date().toISOString().split('T')[0]);
            break;
        case 'this-week':
            params.append('due_date_range', 'week');
            break;
        case 'overdue':
            params.append('overdue', '1');
            break;
        case 'high-priority':
            params.append('priority', 'high');
            break;
        case 'in-progress':
            params.append('status', 'in_progress');
            break;
    }

    // Update URL without page reload first
    const newUrl = `/momentum/tasks?${params.toString()}`;
    window.history.pushState({ filter: filter, path: newUrl }, '', newUrl);

    // Create a timestamp for cache busting
    const timestamp = Date.now();

    // Fetch tasks via AJAX with optimized settings
    fetch(`/momentum/tasks/ajax?${params.toString()}&_=${timestamp}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        },
        // Improved cache settings
        cache: 'no-cache'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        // Update task count
        const taskCountElement = document.querySelector('.task-count');
        if (taskCountElement && data.count !== undefined) {
            taskCountElement.textContent = `${data.count} tasks`;
        }

        // Clear cached data to force refresh
        cachedTaskRows = [];
        cachedGroups = {};

        // Update the task table with new data
        updateTaskTable(data.tasks);

        // Show success feedback for ADHD users - subtle version
        showFilterFeedback(filter);
    })
    .catch(error => {
        console.error('Error fetching tasks:', error);
        // Show error message - simplified version
        const errorMessage = document.createElement('div');
        errorMessage.className = 'fixed top-4 right-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-3 rounded shadow-sm z-50';
        errorMessage.innerHTML = `
            <div class="flex items-center">
                <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">
                    <path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm0-384c13.3 0 24 10.7 24 24V264c0 13.3-10.7 24-24 24s-24-10.7-24-24V152c0-13.3 10.7-24 24-24zM224 352a32 32 0 1 1 64 0 32 32 0 1 1 -64 0z"/>
                </svg>
                <p class="text-sm">Error loading tasks. Please try again.</p>
            </div>
        `;
        document.body.appendChild(errorMessage);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (document.body.contains(errorMessage)) {
                errorMessage.remove();
            }
        }, 3000);
    })
    .finally(() => {
        // Remove loading indicator
        if (loadingIndicator) {
            loadingIndicator.remove();
        }

        // Remove filtering class immediately
        document.body.classList.remove('filtering');

        // Reset filtering flag
        window.isFilteringInProgress = false;
    });
}

/**
 * Show feedback when filter is applied (ADHD-friendly, subtle version)
 */
function showFilterFeedback(filter) {
    // Remove any existing feedback
    const existingFeedback = document.querySelector('.filter-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }

    let filterName = '';
    let svgPath = '';

    switch(filter) {
        case 'today':
            filterName = 'Today';
            svgPath = 'M128 0c17.7 0 32 14.3 32 32V64H288V32c0-17.7 14.3-32 32-32s32 14.3 32 32V64h48c26.5 0 48 21.5 48 48v48H0V112C0 85.5 21.5 64 48 64H96V32c0-17.7 14.3-32 32-32zM0 192H448V464c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V192zm80 64c-8.8 0-16 7.2-16 16v64c0 8.8 7.2 16 16 16h64c8.8 0 16-7.2 16-16V272c0-8.8-7.2-16-16-16H80z';
            break;
        case 'this-week':
            filterName = 'This Week';
            svgPath = 'M128 0c17.7 0 32 14.3 32 32V64H288V32c0-17.7 14.3-32 32-32s32 14.3 32 32V64h48c26.5 0 48 21.5 48 48v48H0V112C0 85.5 21.5 64 48 64H96V32c0-17.7 14.3-32 32-32zM0 192H448V464c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V192zm80 64c-8.8 0-16 7.2-16 16v96c0 8.8 7.2 16 16 16H368c8.8 0 16-7.2 16-16V272c0-8.8-7.2-16-16-16H80z';
            break;
        case 'overdue':
            filterName = 'Overdue';
            svgPath = 'M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm0-384c13.3 0 24 10.7 24 24V264c0 13.3-10.7 24-24 24s-24-10.7-24-24V152c0-13.3 10.7-24 24-24zM224 352a32 32 0 1 1 64 0 32 32 0 1 1 -64 0z';
            break;
        case 'high-priority':
            filterName = 'High Priority';
            svgPath = 'M64 32C64 14.3 49.7 0 32 0S0 14.3 0 32V64 368 480c0 17.7 14.3 32 32 32s32-14.3 32-32V352l64.3-16.1c41.1-10.3 84.6-5.5 122.5 13.4c44.2 22.1 95.5 24.8 141.7 7.4l34.7-13c12.5-4.7 20.8-16.6 20.8-30V66.1c0-23-24.2-38-44.8-27.7l-9.6 4.8c-46.3 23.2-100.8 23.2-147.1 0c-35.1-17.6-75.4-22-113.5-12.5L64 48V32z';
            break;
        case 'in-progress':
            filterName = 'In Progress';
            svgPath = 'M222.7 32.1c5 16.9-4.6 34.8-21.5 39.8C121.8 95.6 64 169.1 64 256c0 106 86 192 192 192s192-86 192-192c0-86.9-57.8-160.4-137.1-184.1c-16.9-5-26.6-22.9-21.5-39.8s22.9-26.6 39.8-21.5C434.9 42.1 512 140 512 256c0 141.4-114.6 256-256 256S0 397.4 0 256C0 140 77.1 42.1 182.9 10.6c16.9-5 34.8 4.6 39.8 21.5z';
            break;
    }

    const feedback = document.createElement('div');
    feedback.className = 'filter-feedback';
    feedback.innerHTML = `
        <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">
            <path d="${svgPath}"/>
        </svg>
        <div class="filter-feedback-text">
            Showing ${filterName} tasks
        </div>
    `;

    document.body.appendChild(feedback);

    // Animate in - use setTimeout with 0ms for better performance than requestAnimationFrame in this case
    setTimeout(() => {
        feedback.classList.add('show');
    }, 0);

    // Remove after delay
    setTimeout(() => {
        feedback.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(feedback)) {
                feedback.remove();
            }
        }, 200);
    }, 2000);
}

/**
 * Handle browser back/forward navigation
 */
function handlePopState(event) {
    // Get the current URL parameters
    const urlParams = new URLSearchParams(window.location.search);

    // Determine which filter to apply based on URL parameters
    let filter = '';

    if (urlParams.has('due_date') && urlParams.get('due_date') === new Date().toISOString().split('T')[0]) {
        filter = 'today';
    } else if (urlParams.has('due_date_range') && urlParams.get('due_date_range') === 'week') {
        filter = 'this-week';
    } else if (urlParams.has('overdue') && urlParams.get('overdue') === '1') {
        filter = 'overdue';
    } else if (urlParams.has('priority') && urlParams.get('priority') === 'high') {
        filter = 'high-priority';
    } else if (urlParams.has('status') && urlParams.get('status') === 'in_progress') {
        filter = 'in-progress';
    }

    // Update active filter button
    if (filter) {
        setActiveFilter(filter);
    } else {
        // Clear active state if no filter matches
        const buttons = document.querySelectorAll('.quick-filter-button');
        buttons.forEach(btn => btn.classList.remove('active'));
    }

    // Reload the page to ensure correct data is displayed
    // This is a fallback for browser navigation
    window.location.reload();
}

/**
 * Update the task table with new data
 */
function updateTaskTable(tasks) {
    if (!tasks || !Array.isArray(tasks) || tasks.length === 0) {
        showEmptyTasksMessage();
        return;
    }

    // Get the task table
    const taskTable = document.querySelector('.task-table');
    if (!taskTable) return;

    // Get the tbody
    const tbody = taskTable.querySelector('tbody');
    if (!tbody) return;

    // Clear the tbody
    tbody.innerHTML = '';

    // Create task rows
    tasks.forEach(task => {
        const row = createTaskRow(task);
        tbody.appendChild(row);
    });

    // Cache the new task rows
    cacheTaskRows();

    // Apply grouping if active
    const groupingSelect = document.getElementById('task-grouping');
    if (groupingSelect) {
        groupTasks(groupingSelect.value);
    }
}

/**
 * Create a task row element from task data
 */
function createTaskRow(task) {
    const row = document.createElement('tr');
    row.className = `task-row priority-${task.priority}`;
    row.setAttribute('data-status', task.status);
    row.setAttribute('data-priority', task.priority);
    row.setAttribute('data-category', task.category_name || 'Uncategorized');
    row.setAttribute('data-category-color', task.category_color || '#6b7280');
    row.setAttribute('data-due-date', task.due_date || '');
    row.setAttribute('data-task-id', task.id);
    row.setAttribute('tabindex', '0');

    // Status cell
    const statusCell = document.createElement('td');
    statusCell.className = 'px-6 py-4 whitespace-nowrap';

    let statusBadgeClass = '';
    let statusText = '';

    switch(task.status) {
        case 'todo':
            statusBadgeClass = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
            statusText = 'To Do';
            break;
        case 'in_progress':
            statusBadgeClass = 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
            statusText = 'In Progress';
            break;
        case 'done':
            statusBadgeClass = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
            statusText = 'Done';
            break;
    }

    statusCell.innerHTML = `
        <div class="flex items-center">
            <input type="checkbox" data-task-id="${task.id}" class="task-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 rounded" ${task.status === 'done' ? 'checked' : ''}>
            <span class="ml-2 text-xs">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusBadgeClass}">
                    ${statusText}
                </span>
            </span>
        </div>
    `;

    // Title cell
    const titleCell = document.createElement('td');
    titleCell.className = 'px-6 py-4';

    let titleHTML = `
        <div class="flex items-center">
            <a href="/momentum/tasks/view/${task.id}" class="text-sm font-medium text-gray-900 dark:text-white ${task.status === 'done' ? 'line-through text-gray-500 dark:text-gray-400' : ''}">
                ${escapeHTML(task.title)}
            </a>
    `;

    if (task.parent_id) {
        titleHTML += `
            <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">
                <i class="fas fa-level-up-alt fa-rotate-90"></i> Subtask
            </span>
        `;
    }

    titleHTML += `</div>`;

    if (task.description) {
        const shortDesc = task.description.length > 100 ?
            task.description.substring(0, 100) + '...' :
            task.description;

        titleHTML += `
            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate max-w-xs">
                ${escapeHTML(shortDesc)}
            </div>
        `;
    }

    titleCell.innerHTML = titleHTML;

    // Priority cell
    const priorityCell = document.createElement('td');
    priorityCell.className = 'px-6 py-4 whitespace-nowrap';

    let priorityBadgeClass = '';
    let priorityText = '';

    switch(task.priority) {
        case 'low':
            priorityBadgeClass = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
            priorityText = 'Low';
            break;
        case 'medium':
            priorityBadgeClass = 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
            priorityText = 'Medium';
            break;
        case 'high':
            priorityBadgeClass = 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
            priorityText = 'High';
            break;
        case 'urgent':
            priorityBadgeClass = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
            priorityText = 'Urgent';
            break;
    }

    priorityCell.innerHTML = `
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priorityBadgeClass}">
            ${priorityText}
        </span>
    `;

    // Due date cell
    const dueDateCell = document.createElement('td');
    dueDateCell.className = 'px-6 py-4 whitespace-nowrap';

    if (task.due_date) {
        const dueDate = new Date(task.due_date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        let dueDateHTML = `
            <div class="text-sm text-gray-900 dark:text-white">
                ${formatDate(task.due_date)}
            </div>
        `;

        if (task.due_time) {
            dueDateHTML += `
                <div class="text-xs text-gray-500 dark:text-gray-400">
                    ${formatTime(task.due_time)}
                </div>
            `;
        }

        if (dueDate < today && task.status !== 'done') {
            dueDateHTML += `
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 mt-1">
                    Overdue
                </span>
            `;
        } else if (dueDate.getTime() === today.getTime()) {
            dueDateHTML += `
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 mt-1">
                    Today
                </span>
            `;
        } else if (dueDate.getTime() === tomorrow.getTime()) {
            dueDateHTML += `
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mt-1">
                    Tomorrow
                </span>
            `;
        }

        dueDateCell.innerHTML = dueDateHTML;
    } else {
        dueDateCell.innerHTML = `<span class="text-gray-500 dark:text-gray-400">No due date</span>`;
    }

    // Category cell
    const categoryCell = document.createElement('td');
    categoryCell.className = 'px-6 py-4 whitespace-nowrap';

    if (task.category_id && task.category_color) {
        categoryCell.innerHTML = `
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" style="background-color: ${task.category_color}25; color: ${task.category_color};">
                ${escapeHTML(task.category_name || 'Category')}
            </span>
        `;
    } else {
        categoryCell.innerHTML = `<span class="text-gray-500 dark:text-gray-400">Uncategorized</span>`;
    }

    // Actions cell
    const actionsCell = document.createElement('td');
    actionsCell.className = 'px-6 py-4 whitespace-nowrap text-right text-sm font-medium';
    actionsCell.innerHTML = `
        <div class="flex justify-end space-x-2">
            <a href="/momentum/tasks/view/${task.id}" class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300" title="View">
                <i class="fas fa-eye"></i>
            </a>
            <a href="/momentum/tasks/edit/${task.id}" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" title="Edit">
                <i class="fas fa-edit"></i>
            </a>
            <a href="/momentum/tasks/delete/${task.id}" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" title="Delete" onclick="return confirm('Are you sure you want to delete this task?')">
                <i class="fas fa-trash"></i>
            </a>
        </div>
    `;

    // Append all cells to the row
    row.appendChild(statusCell);
    row.appendChild(titleCell);
    row.appendChild(priorityCell);
    row.appendChild(dueDateCell);
    row.appendChild(categoryCell);
    row.appendChild(actionsCell);

    // Add event listeners
    row.addEventListener('dblclick', handleTaskRowDoubleClick);

    const checkbox = row.querySelector('.task-checkbox');
    if (checkbox) {
        checkbox.addEventListener('change', handleTaskCheckboxChange);
    }

    return row;
}

/**
 * Show empty tasks message
 */
function showEmptyTasksMessage() {
    const taskTable = document.querySelector('.task-table');
    if (!taskTable) return;

    const container = taskTable.closest('.bg-white.dark\\:bg-gray-800.shadow.rounded-lg');
    if (!container) return;

    container.innerHTML = `
        <div class="px-4 py-5 sm:p-6 text-center">
            <div class="text-gray-500 dark:text-gray-400 mb-4">
                <i class="fas fa-tasks text-4xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No tasks found</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">
                Try adjusting your filters or create a new task to get started.
            </p>
            <a href="/momentum/tasks/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i> Create Task
            </a>
        </div>
    `;
}

/**
 * Helper function to escape HTML
 */
function escapeHTML(str) {
    if (!str) return '';
    return str
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
}

/**
 * Format date for display
 */
function formatDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return date.toLocaleDateString();
}

/**
 * Format time for display
 */
function formatTime(timeStr) {
    if (!timeStr) return '';
    // Simple time formatting - could be enhanced
    return timeStr;
}

/**
 * Handle task checkbox change (for event reattachment)
 */
function handleTaskCheckboxChange() {
    const taskId = this.getAttribute('data-task-id');

    if (this.checked) {
        // Mark task as complete
        fetch(`/momentum/tasks/complete/${taskId}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update UI
                const row = this.closest('tr');
                row.classList.add('task-complete-animation');

                const taskTitle = row.querySelector('a');
                if (taskTitle) {
                    taskTitle.classList.add('line-through', 'text-gray-500', 'dark:text-gray-400');
                }

                // Update status badge
                const statusBadge = row.querySelector('td:first-child .inline-flex');
                if (statusBadge) {
                    statusBadge.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                    statusBadge.textContent = 'Done';
                }

                // Update data attribute
                row.setAttribute('data-status', 'done');

                // Update cached task row data
                const taskRowIndex = cachedTaskRows.findIndex(r => r.taskId === taskId);
                if (taskRowIndex !== -1) {
                    cachedTaskRows[taskRowIndex].status = 'done';
                }

                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'fixed bottom-4 right-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md animate-fade-in z-50';
                successMessage.innerHTML = `
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm">Task marked as complete!</p>
                        </div>
                    </div>
                `;
                document.body.appendChild(successMessage);

                // Remove message after 3 seconds
                setTimeout(() => {
                    successMessage.classList.add('opacity-0', 'transition-opacity', 'duration-500');
                    setTimeout(() => {
                        successMessage.remove();
                    }, 500);
                }, 3000);

                // Regroup tasks if grouping is active
                const groupingSelect = document.getElementById('task-grouping');
                if (groupingSelect && typeof groupTasks === 'function') {
                    // Use a small delay to allow the animation to complete
                    setTimeout(() => {
                        groupTasks(groupingSelect.value);
                    }, 500);
                }
            } else {
                // Uncheck the checkbox if there was an error
                this.checked = false;
                alert('Failed to mark task as complete. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.checked = false;
            alert('An error occurred. Please try again.');
        });
    }
}

/**
 * Initialize compact view toggle
 */
function initCompactViewToggle() {
    const compactViewToggle = document.getElementById('compact-view-toggle');
    if (!compactViewToggle) return;

    compactViewToggle.addEventListener('change', function() {
        const taskTable = document.querySelector('.task-table');
        if (this.checked) {
            taskTable.classList.add('compact-view');
        } else {
            taskTable.classList.remove('compact-view');
        }

        // Save preference in localStorage
        localStorage.setItem('tasks_compact_view', this.checked ? '1' : '0');
    });

    // Check saved preference
    const savedPreference = localStorage.getItem('tasks_compact_view');
    if (savedPreference === '1') {
        compactViewToggle.checked = true;
        document.querySelector('.task-table')?.classList.add('compact-view');
    }
}

/**
 * Initialize expandable task details
 */
function initExpandableTaskDetails() {
    // Use event delegation instead of attaching to each row
    document.addEventListener('dblclick', function(e) {
        const row = e.target.closest('.task-row');
        if (!row) return;

        // Don't expand if clicking on a link or checkbox
        if (e.target.tagName === 'A' || e.target.tagName === 'INPUT') {
            return;
        }

        // Toggle details section
        const detailsRow = row.nextElementSibling;
        if (detailsRow && detailsRow.classList.contains('task-details')) {
            detailsRow.classList.toggle('expanded');
        }
    });
}

/**
 * Handle task row double click (for event reattachment)
 */
function handleTaskRowDoubleClick(e) {
    // Don't expand if clicking on a link or checkbox
    if (e.target.tagName === 'A' || e.target.tagName === 'INPUT') {
        return;
    }

    // Toggle details section
    const detailsRow = this.nextElementSibling;
    if (detailsRow && detailsRow.classList.contains('task-details')) {
        detailsRow.classList.toggle('expanded');
    }
}

/**
 * Initialize keyboard navigation with performance optimizations
 */
function initKeyboardNavigation() {
    // Use event delegation for keyboard events
    document.addEventListener('keydown', handleKeyboardNavigation);
}

/**
 * Handle keyboard navigation events
 */
function handleKeyboardNavigation(e) {
    // Only handle if a task row is focused
    const focusedElement = document.activeElement;
    if (!focusedElement || !focusedElement.classList.contains('task-row')) return;

    switch(e.key) {
        case 'ArrowDown':
            e.preventDefault();
            navigateToNextRow(focusedElement);
            break;
        case 'ArrowUp':
            e.preventDefault();
            navigateToPreviousRow(focusedElement);
            break;
        case 'Enter':
            e.preventDefault();
            // Toggle details or navigate to task view
            const viewLink = focusedElement.querySelector('a[title="View"]');
            if (viewLink) {
                viewLink.click();
            }
            break;
        case 'Space':
            e.preventDefault();
            // Toggle task completion
            const checkbox = focusedElement.querySelector('.task-checkbox');
            if (checkbox) {
                checkbox.click();
            }
            break;
    }
}

/**
 * Navigate to the next task row
 */
function navigateToNextRow(currentRow) {
    const taskRows = Array.from(document.querySelectorAll('.task-row'));
    const currentIndex = taskRows.indexOf(currentRow);

    if (currentIndex < taskRows.length - 1) {
        taskRows[currentIndex + 1].focus();
    }
}

/**
 * Navigate to the previous task row
 */
function navigateToPreviousRow(currentRow) {
    const taskRows = Array.from(document.querySelectorAll('.task-row'));
    const currentIndex = taskRows.indexOf(currentRow);

    if (currentIndex > 0) {
        taskRows[currentIndex - 1].focus();
    }
}
