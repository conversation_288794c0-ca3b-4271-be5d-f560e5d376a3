/**
 * Remove Blue Highlight JS
 *
 * This script removes the blue highlighting effect from task items
 * in the task list view.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Remove Blue Highlight script loaded');

    // Remove blue highlight classes from all task items
    function removeBlueHighlight() {
        // Select all elements with blue highlight classes
        const blueHighlightElements = document.querySelectorAll(
            '.bg-primary-50, .dark\\:bg-primary-900, .border-l-4.border-primary-500'
        );

        // Remove the classes
        blueHighlightElements.forEach(element => {
            element.classList.remove('bg-primary-50', 'dark:bg-primary-900', 'border-l-4', 'border-primary-500');
            element.style.backgroundColor = '';
            element.style.borderLeft = 'none';
            element.style.paddingLeft = '';
        });

        // Fix task items specifically
        document.querySelectorAll('.task-item, .task-row').forEach(item => {
            // Remove any existing blue highlight classes
            item.classList.remove('bg-primary-50', 'dark:bg-primary-900', 'border-l-4', 'border-primary-500');

            // Remove inline styles
            if (item.style.backgroundColor &&
                (item.style.backgroundColor.includes('rgb(239, 246, 255)') ||
                 item.style.backgroundColor.includes('rgb(30, 58, 138)'))) {
                item.style.backgroundColor = '';
            }

            if (item.style.borderLeft && item.style.borderLeft.includes('primary')) {
                item.style.borderLeft = 'none';
            }

            if (item.style.paddingLeft) {
                item.style.paddingLeft = '';
            }
        });
    }

    // Run immediately
    removeBlueHighlight();

    // Run again after a short delay to catch any elements that might be added dynamically
    setTimeout(removeBlueHighlight, 500);

    // Run again when the page is fully loaded
    window.addEventListener('load', removeBlueHighlight);

    // Run periodically instead of using MutationObserver to avoid infinite loops
    setInterval(removeBlueHighlight, 1000);
});
