/**
 * ADHD Dropdown Menu Fix
 *
 * This script provides a dedicated implementation for the ADHD dropdown menu
 * to ensure it displays correctly without showing items from other menus.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get the ADHD dropdown button
    const adhdButton = document.getElementById('adhd-dropdown-button');

    // Create a completely new ADHD dropdown menu
    function createADHDMenu() {
        // Remove any existing ADHD menu
        const existingMenu = document.getElementById('adhd-dropdown-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        // Create a new menu element
        const menu = document.createElement('div');
        menu.id = 'adhd-dropdown-menu';
        menu.className = 'dropdown-menu';
        menu.setAttribute('role', 'menu');
        menu.setAttribute('aria-labelledby', 'adhd-dropdown-button');
        menu.style.zIndex = '999999'; // Very high z-index
        menu.style.position = 'fixed';
        menu.style.backgroundColor = document.documentElement.classList.contains('dark') ? '#374151' : 'white';
        menu.style.borderRadius = '0.375rem';
        menu.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
        menu.style.minWidth = '14rem';
        menu.style.maxHeight = '400px';
        menu.style.overflowY = 'auto';
        menu.style.padding = '0.5rem 0';
        menu.style.display = 'none';
        menu.style.opacity = '0';
        menu.style.visibility = 'hidden';

        // Add menu items - these are the correct ADHD menu items
        const menuItems = [
            { href: '/momentum/adhd', icon: 'tachometer-alt', text: 'ADHD Dashboard' },
            { href: '/momentum/adhd/symptom-tracker', icon: 'chart-line', text: 'Symptom Tracker' },
            { href: '/momentum/adhd/cbt/thought-records', icon: 'comments', text: 'Thought Records' },
            { href: '/momentum/adhd/productivity/strategies', icon: 'lightbulb', text: 'Productivity Strategies' },
            { href: '/momentum/adhd/mindfulness', icon: 'spa', text: 'Mindfulness Exercises' },
            { href: '/momentum/adhd/consistency/trackers', icon: 'calendar-check', text: 'Consistency Trackers' }
        ];

        // Create menu items
        menuItems.forEach(item => {
            const menuItem = document.createElement('a');
            menuItem.href = item.href;
            menuItem.setAttribute('role', 'menuitem');
            menuItem.setAttribute('tabindex', '-1');
            menuItem.style.display = 'block';
            menuItem.style.padding = '0.75rem 1rem';
            menuItem.style.color = document.documentElement.classList.contains('dark') ? '#E5E7EB' : '#4B5563';
            menuItem.style.fontSize = '0.875rem';
            menuItem.style.textDecoration = 'none';
            menuItem.style.whiteSpace = 'nowrap';
            menuItem.style.width = '100%';
            menuItem.style.textOverflow = 'ellipsis';
            menuItem.style.overflow = 'hidden';
            menuItem.style.position = 'relative';
            menuItem.style.zIndex = '1000000';
            menuItem.style.cursor = 'pointer';
            menuItem.style.borderBottom = document.documentElement.classList.contains('dark') ?
                '1px solid rgba(255, 255, 255, 0.05)' : '1px solid rgba(0, 0, 0, 0.05)';

            // Add hover effect
            menuItem.addEventListener('mouseenter', function() {
                this.style.backgroundColor = document.documentElement.classList.contains('dark') ?
                    '#4B5563' : '#F3F4F6';
            });

            menuItem.addEventListener('mouseleave', function() {
                this.style.backgroundColor = 'transparent';
            });

            // Add icon
            const icon = document.createElement('i');
            icon.className = `fas fa-${item.icon} mr-2`;
            icon.setAttribute('aria-hidden', 'true');
            icon.style.marginRight = '0.5rem';
            menuItem.appendChild(icon);

            // Add text
            const text = document.createTextNode(item.text);
            menuItem.appendChild(text);

            menu.appendChild(menuItem);
        });

        // Add divider
        const divider = document.createElement('div');
        divider.style.borderTop = document.documentElement.classList.contains('dark') ?
            '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.1)';
        divider.style.margin = '0.5rem 0';
        menu.appendChild(divider);

        // Add "Coming Soon" header
        const comingSoonHeader = document.createElement('div');
        comingSoonHeader.style.padding = '0.5rem 1rem';
        comingSoonHeader.style.fontSize = '0.75rem';
        comingSoonHeader.style.fontWeight = '500';
        comingSoonHeader.style.color = document.documentElement.classList.contains('dark') ?
            '#9CA3AF' : '#6B7280';
        comingSoonHeader.textContent = 'Coming Soon';
        menu.appendChild(comingSoonHeader);

        // Add planned features
        const plannedFeatures = [
            { icon: 'pills', text: 'Medication Tracker', status: 'Planned', statusClass: 'blue' },
            { icon: 'exclamation-triangle', text: 'Trigger Identification', status: 'Planned', statusClass: 'blue' },
            { icon: 'brain', text: 'Executive Function Exercises', status: 'Concept', statusClass: 'gray' }
        ];

        plannedFeatures.forEach(feature => {
            const featureItem = document.createElement('div');
            featureItem.style.padding = '0.75rem 1rem';
            featureItem.style.display = 'flex';
            featureItem.style.justifyContent = 'space-between';
            featureItem.style.alignItems = 'center';
            featureItem.style.color = document.documentElement.classList.contains('dark') ?
                '#9CA3AF' : '#6B7280';

            // Feature name with icon
            const nameSpan = document.createElement('span');

            // Add icon
            const icon = document.createElement('i');
            icon.className = `fas fa-${feature.icon} mr-2`;
            icon.style.marginRight = '0.5rem';
            nameSpan.appendChild(icon);

            // Add text
            const text = document.createTextNode(feature.text);
            nameSpan.appendChild(text);

            featureItem.appendChild(nameSpan);

            // Status badge
            const statusBadge = document.createElement('span');
            statusBadge.style.padding = '0.25rem 0.5rem';
            statusBadge.style.fontSize = '0.75rem';
            statusBadge.style.borderRadius = '0.25rem';

            // Set badge color based on status
            if (feature.statusClass === 'blue') {
                statusBadge.style.backgroundColor = document.documentElement.classList.contains('dark') ?
                    '#1E3A8A' : '#DBEAFE';
                statusBadge.style.color = document.documentElement.classList.contains('dark') ?
                    '#BFDBFE' : '#1E40AF';
            } else {
                statusBadge.style.backgroundColor = document.documentElement.classList.contains('dark') ?
                    '#374151' : '#F3F4F6';
                statusBadge.style.color = document.documentElement.classList.contains('dark') ?
                    '#D1D5DB' : '#4B5563';
            }

            statusBadge.textContent = feature.status;
            featureItem.appendChild(statusBadge);

            menu.appendChild(featureItem);
        });

        // Add the menu to the document body
        document.body.appendChild(menu);

        return menu;
    }

    // Create the overlay
    function createOverlay() {
        // Remove any existing overlay
        const existingOverlay = document.getElementById('adhd-dropdown-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Create a new overlay
        const overlay = document.createElement('div');
        overlay.id = 'adhd-dropdown-overlay';
        overlay.style.position = 'fixed';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100vw';
        overlay.style.height = '100vh';
        overlay.style.zIndex = '99998'; // Just below the menu
        overlay.style.backgroundColor = 'transparent';
        overlay.style.display = 'none';

        // Add click handler to close menu when clicking outside
        overlay.addEventListener('click', function() {
            hideMenu();
        });

        // Add the overlay to the document body
        document.body.appendChild(overlay);

        return overlay;
    }

    // Create the menu and overlay
    const adhdMenu = createADHDMenu();
    const overlay = createOverlay();

    // Function to position the menu
    function positionMenu() {
        if (!adhdButton || !adhdMenu) return;

        const buttonRect = adhdButton.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Position the menu below the button
        adhdMenu.style.top = (buttonRect.bottom + 5) + 'px';
        adhdMenu.style.left = buttonRect.left + 'px';

        // Force a reflow to get accurate dimensions
        adhdMenu.offsetHeight;

        // Check if menu would go off the screen
        const menuRect = adhdMenu.getBoundingClientRect();

        // Handle horizontal positioning
        if (menuRect.right > viewportWidth) {
            // Align right edge of menu with right edge of button
            adhdMenu.style.left = 'auto';
            adhdMenu.style.right = (viewportWidth - buttonRect.right) + 'px';
        }

        // Handle vertical positioning
        if (menuRect.bottom > viewportHeight) {
            // If menu is too tall for the viewport, adjust its position and max height
            const spaceBelow = viewportHeight - buttonRect.bottom - 10;
            adhdMenu.style.maxHeight = spaceBelow + 'px';
        }
    }

    // Function to show the menu
    function showMenu() {
        // Show the overlay
        overlay.style.display = 'block';

        // Show the menu
        adhdMenu.style.display = 'block';
        adhdMenu.style.visibility = 'visible';

        // Position the menu
        positionMenu();

        // Fade in the menu
        setTimeout(function() {
            adhdMenu.style.opacity = '1';
        }, 10);

        // Update ARIA attributes
        adhdButton.setAttribute('aria-expanded', 'true');

        // Add escape key handler
        document.addEventListener('keydown', escapeKeyHandler);
    }

    // Function to hide the menu
    function hideMenu() {
        // Hide the overlay
        overlay.style.display = 'none';

        // Fade out the menu
        adhdMenu.style.opacity = '0';

        // Hide the menu after the transition
        setTimeout(function() {
            adhdMenu.style.visibility = 'hidden';
            adhdMenu.style.display = 'none';
        }, 200);

        // Update ARIA attributes
        adhdButton.setAttribute('aria-expanded', 'false');

        // Remove escape key handler
        document.removeEventListener('keydown', escapeKeyHandler);
    }

    // Escape key handler
    function escapeKeyHandler(e) {
        if (e.key === 'Escape') {
            hideMenu();
        }
    }

    // Add click handler to the button
    if (adhdButton) {
        adhdButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Toggle the menu
            if (adhdMenu.style.display === 'block') {
                hideMenu();
            } else {
                showMenu();
            }
        });
    }

    // Close ADHD menu when hovering over other dropdown buttons
    const otherDropdownButtons = [
        document.getElementById('productivity-dropdown-button'),
        document.getElementById('tools-dropdown-button'),
        document.getElementById('user-menu-button')
    ];

    otherDropdownButtons.forEach(button => {
        if (button) {
            button.addEventListener('mouseenter', function() {
                // Hide ADHD menu if it's open
                if (adhdMenu.style.display === 'block') {
                    hideMenu();
                }
            });

            // Also handle the parent container
            if (button.parentElement) {
                button.parentElement.addEventListener('mouseenter', function() {
                    // Hide ADHD menu if it's open
                    if (adhdMenu.style.display === 'block') {
                        hideMenu();
                    }
                });
            }
        }
    });

    // Also close when hovering over other navigation items
    const navLinks = document.querySelectorAll('nav a:not(#adhd-dropdown-button)');
    navLinks.forEach(link => {
        if (link) {
            link.addEventListener('mouseenter', function() {
                // Hide ADHD menu if it's open
                if (adhdMenu.style.display === 'block') {
                    hideMenu();
                }
            });
        }
    });
});
