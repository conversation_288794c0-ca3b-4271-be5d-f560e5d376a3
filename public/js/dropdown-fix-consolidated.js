/**
 * Consolidated Dropdown Fix
 * 
 * This script provides a comprehensive solution for the dropdown menu functionality
 * in the Momentum dashboard. It replaces all previous dropdown-related scripts
 * with a single, robust implementation.
 * 
 * Features:
 * - Proper event handling for dropdown toggle
 * - Correct positioning of dropdown menu
 * - Handling of click events on dropdown options
 * - Keyboard navigation and accessibility
 * - Proper cleanup of event listeners to prevent memory leaks
 */

(function() {
    // Configuration
    const config = {
        debug: true,                // Enable debug logging
        zIndex: 9999,               // z-index for dropdown
        positionOffset: 2,          // Offset from button in pixels
        transitionDuration: 150,    // Animation duration in ms
        closeOnEscape: true,        // Close dropdown when Escape key is pressed
        closeOnClickOutside: true,  // Close dropdown when clicking outside
        ariaSupport: true           // Add ARIA attributes for accessibility
    };

    // Debug logging function
    function debug(message) {
        if (config.debug) {
            console.log(`[Dropdown Fix] ${message}`);
        }
    }

    // Main initialization function
    function initializeDropdowns() {
        debug('Initializing dropdown fix');

        // Get layout button and dropdown elements
        const layoutButton = document.getElementById('layout-selector-button');
        const layoutDropdown = document.getElementById('layout-dropdown');

        if (!layoutButton || !layoutDropdown) {
            debug('Layout button or dropdown not found');
            return;
        }

        debug('Found layout button and dropdown');

        // Remove any existing event listeners by cloning and replacing
        const newButton = layoutButton.cloneNode(true);
        layoutButton.parentNode.replaceChild(newButton, layoutButton);

        // Setup dropdown
        setupDropdown(newButton, layoutDropdown);
    }

    // Function to set up a dropdown
    function setupDropdown(button, dropdown) {
        debug('Setting up dropdown');

        // Ensure dropdown has the right initial styles
        dropdown.style.position = 'fixed';
        dropdown.style.zIndex = config.zIndex;
        dropdown.style.display = 'none';
        dropdown.classList.add('hidden');

        // Set ARIA attributes for accessibility
        if (config.ariaSupport) {
            button.setAttribute('aria-haspopup', 'true');
            button.setAttribute('aria-expanded', 'false');
            dropdown.setAttribute('role', 'menu');
            
            // Set ARIA attributes on menu items
            const menuItems = dropdown.querySelectorAll('a, button');
            menuItems.forEach((item, index) => {
                item.setAttribute('role', 'menuitem');
                item.setAttribute('tabindex', '-1');
                if (index === 0) {
                    item.id = item.id || `menu-item-${Date.now()}-${index}`;
                    dropdown.setAttribute('aria-activedescendant', item.id);
                }
            });
        }

        // Add click handler to button
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            debug('Button clicked');

            // Toggle dropdown
            const isHidden = dropdown.classList.contains('hidden');
            if (isHidden) {
                showDropdown(button, dropdown);
            } else {
                hideDropdown(button, dropdown);
            }
        });

        // Add click handlers to dropdown options
        setupDropdownOptions(button, dropdown);

        // Close dropdown when clicking outside
        if (config.closeOnClickOutside) {
            document.addEventListener('click', function(e) {
                if (!dropdown.classList.contains('hidden') && 
                    !button.contains(e.target) && 
                    !dropdown.contains(e.target)) {
                    debug('Clicking outside, hiding dropdown');
                    hideDropdown(button, dropdown);
                }
            });
        }

        // Close dropdown when pressing Escape
        if (config.closeOnEscape) {
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !dropdown.classList.contains('hidden')) {
                    debug('Escape key pressed, hiding dropdown');
                    hideDropdown(button, dropdown);
                }
            });
        }

        // Add keyboard navigation
        setupKeyboardNavigation(button, dropdown);
    }

    // Function to show the dropdown
    function showDropdown(button, dropdown) {
        debug('Showing dropdown');

        // Position the dropdown
        positionDropdown(button, dropdown);

        // Show the dropdown
        dropdown.classList.remove('hidden');
        dropdown.style.display = 'block';

        // Update ARIA attributes
        if (config.ariaSupport) {
            button.setAttribute('aria-expanded', 'true');
        }

        // Add active class to button for styling
        button.classList.add('bg-gray-100', 'dark:bg-gray-600');
    }

    // Function to hide the dropdown
    function hideDropdown(button, dropdown) {
        debug('Hiding dropdown');

        // Hide the dropdown
        dropdown.classList.add('hidden');
        dropdown.style.display = 'none';

        // Update ARIA attributes
        if (config.ariaSupport) {
            button.setAttribute('aria-expanded', 'false');
        }

        // Remove active class from button
        button.classList.remove('bg-gray-100', 'dark:bg-gray-600');
    }

    // Function to position the dropdown
    function positionDropdown(button, dropdown) {
        const buttonRect = button.getBoundingClientRect();
        
        // Calculate position
        const top = buttonRect.bottom + window.scrollY + config.positionOffset;
        const left = buttonRect.left + window.scrollX;
        
        // Set position
        dropdown.style.top = `${top}px`;
        dropdown.style.left = `${left}px`;
        
        // Check if dropdown would go off the bottom of the screen
        const dropdownRect = dropdown.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        
        if (dropdownRect.bottom > viewportHeight) {
            // Position above the button
            dropdown.style.top = `${buttonRect.top + window.scrollY - dropdownRect.height - config.positionOffset}px`;
        }
        
        // Check if dropdown would go off the right of the screen
        const viewportWidth = window.innerWidth;
        
        if (dropdownRect.right > viewportWidth) {
            // Align right edge of dropdown with right edge of button
            dropdown.style.left = `${buttonRect.right + window.scrollX - dropdownRect.width}px`;
        }
    }

    // Function to set up dropdown options
    function setupDropdownOptions(button, dropdown) {
        const options = dropdown.querySelectorAll('[data-view]');
        
        debug(`Found ${options.length} dropdown options`);
        
        options.forEach(option => {
            // Remove any existing event listeners by cloning and replacing
            const newOption = option.cloneNode(true);
            option.parentNode.replaceChild(newOption, option);
            
            // Add click handler
            newOption.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const viewMode = this.getAttribute('data-view');
                debug(`Option clicked: ${viewMode}`);
                
                // Apply the layout
                applyLayout(viewMode, button, dropdown);
                
                // Hide dropdown
                hideDropdown(button, dropdown);
            });
        });
    }

    // Function to set up keyboard navigation
    function setupKeyboardNavigation(button, dropdown) {
        const options = dropdown.querySelectorAll('[data-view]');
        
        // Add keydown handler to button
        button.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowDown' && dropdown.classList.contains('hidden')) {
                e.preventDefault();
                showDropdown(button, dropdown);
                
                // Focus first option
                if (options.length > 0) {
                    options[0].focus();
                }
            }
        });
        
        // Add keydown handlers to options
        options.forEach((option, index) => {
            option.addEventListener('keydown', function(e) {
                switch (e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        if (index < options.length - 1) {
                            options[index + 1].focus();
                        }
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        if (index > 0) {
                            options[index - 1].focus();
                        } else {
                            button.focus();
                        }
                        break;
                    case 'Escape':
                        e.preventDefault();
                        hideDropdown(button, dropdown);
                        button.focus();
                        break;
                    case 'Enter':
                    case ' ':
                        e.preventDefault();
                        option.click();
                        break;
                }
            });
        });
    }

    // Function to apply layout
    function applyLayout(layoutName, button, dropdown) {
        debug(`Applying layout: ${layoutName}`);
        
        // Get dashboard widgets container
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) {
            debug('Dashboard widgets container not found');
            return;
        }
        
        // Update view mode text
        const viewModeText = document.getElementById('view-mode-text');
        if (viewModeText) {
            viewModeText.textContent = layoutName.charAt(0).toUpperCase() + layoutName.slice(1) + ' View';
        }
        
        // Update data attributes
        dashboardWidgets.setAttribute('data-view-mode', layoutName);
        dashboardWidgets.setAttribute('data-arrangement', layoutName);
        
        // Save preference
        localStorage.setItem('dashboard_view_mode', layoutName);
        
        // Dispatch a custom event for other scripts to handle the layout change
        const event = new CustomEvent('layout-changed', {
            detail: { layout: layoutName }
        });
        document.dispatchEvent(event);
    }

    // Initialize on DOMContentLoaded
    document.addEventListener('DOMContentLoaded', initializeDropdowns);
    
    // Also initialize after a short delay to ensure all elements are loaded
    setTimeout(initializeDropdowns, 500);
})();
