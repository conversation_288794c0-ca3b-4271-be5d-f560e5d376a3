/**
 * Dropdown Page Check
 * 
 * This script checks if the current page should initialize dropdowns
 * and prevents dropdown initialization on pages where they don't exist.
 */

// Execute immediately before other scripts
(function() {
    // Check if we're on a page that doesn't have dropdowns
    const currentPath = window.location.pathname;
    
    // Pages that don't have the main navigation dropdowns
    const pagesWithoutDropdowns = [
        '/momentum/tasks',
        '/momentum/tasks/',
        '/momentum/tasks/create',
        '/momentum/tasks/edit',
        '/momentum/tasks/view',
        '/momentum/tasks/calendar'
    ];
    
    // Check if the current path starts with any of the paths in the list
    const isPageWithoutDropdowns = pagesWithoutDropdowns.some(path => 
        currentPath === path || 
        currentPath.startsWith(path + '/') ||
        (path.endsWith('/') && currentPath.startsWith(path))
    );
    
    if (isPageWithoutDropdowns) {
        console.log('Dropdown initialization disabled on this page');
        
        // Create a global flag to indicate dropdowns should not be initialized
        window.DISABLE_DROPDOWN_INITIALIZATION = true;
        
        // Create mock dropdown elements to prevent errors
        window.addEventListener('DOMContentLoaded', function() {
            // Create a hidden container for mock elements
            const mockContainer = document.createElement('div');
            mockContainer.style.display = 'none';
            mockContainer.id = 'mock-dropdown-elements';
            document.body.appendChild(mockContainer);
            
            // Create mock dropdown elements
            const dropdownIds = [
                'adhd-dropdown-button', 'adhd-dropdown-menu', 'adhd-dropdown',
                'productivity-dropdown-button', 'productivity-dropdown-menu', 'productivity-dropdown',
                'tools-dropdown-button', 'tools-dropdown-menu', 'tools-dropdown',
                'user-menu-button', 'user-dropdown-menu'
            ];
            
            dropdownIds.forEach(id => {
                if (!document.getElementById(id)) {
                    const mockElement = document.createElement('div');
                    mockElement.id = id;
                    mockElement.className = 'mock-dropdown-element';
                    mockElement.setAttribute('aria-hidden', 'true');
                    mockContainer.appendChild(mockElement);
                    
                    // Add event listeners that do nothing to prevent errors
                    mockElement.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    });
                }
            });
            
            // Override dropdown initialization functions
            if (typeof window.initDropdowns === 'function') {
                const originalInitDropdowns = window.initDropdowns;
                window.initDropdowns = function() {
                    console.log('Dropdown initialization skipped');
                    return false;
                };
            }
        });
    }
})();
