/**
 * Consolidated Dropdown Menu Fix
 *
 * This script provides a unified implementation for all dropdown menus
 * to ensure they display correctly and consistently.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get all dropdown buttons
    const adhdButton = document.getElementById('adhd-dropdown-button');
    const productivityButton = document.getElementById('productivity-dropdown-button');
    const toolsButton = document.getElementById('tools-dropdown-button');
    const userButton = document.getElementById('user-menu-button');

    // Get all dropdown menus
    const adhdMenu = document.getElementById('adhd-dropdown-menu');
    const productivityMenu = document.getElementById('productivity-dropdown-menu');
    const toolsMenu = document.getElementById('tools-dropdown-menu');
    const userMenu = document.getElementById('user-dropdown-menu');

    // Get or create the overlay
    let overlay = document.getElementById('dropdown-overlay');
    if (!overlay) {
        overlay = document.createElement('div');
        overlay.id = 'dropdown-overlay';
        overlay.className = 'dropdown-overlay';
        document.body.appendChild(overlay);
    }

    // Track active menu
    let activeMenu = null;

    // Function to position a dropdown menu
    function positionMenu(menu, button) {
        if (!menu || !button) return;

        const buttonRect = button.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Set basic styles for the menu
        menu.style.position = 'fixed';
        menu.style.maxHeight = '400px';
        menu.style.overflowY = 'auto';
        menu.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)';
        menu.style.borderRadius = '0.375rem';
        menu.style.backgroundColor = document.documentElement.classList.contains('dark') ? '#374151' : 'white';
        menu.style.width = 'auto';
        menu.style.minWidth = '200px';
        menu.style.zIndex = '99999';

        // Position the menu below the button
        menu.style.top = (buttonRect.bottom + 5) + 'px';
        menu.style.left = buttonRect.left + 'px';

        // Force a reflow to get accurate dimensions
        menu.offsetHeight;

        // Check if menu would go off the screen
        const menuRect = menu.getBoundingClientRect();

        // Handle horizontal positioning
        if (menuRect.right > viewportWidth) {
            // Align right edge of menu with right edge of button
            menu.style.left = 'auto';
            menu.style.right = (viewportWidth - buttonRect.right) + 'px';
        }

        // Handle vertical positioning
        if (menuRect.bottom > viewportHeight) {
            // If menu is too tall for the viewport, adjust its position and max height
            const spaceBelow = viewportHeight - buttonRect.bottom - 10;
            menu.style.maxHeight = spaceBelow + 'px';
        }

        // Apply dark mode styles if needed
        if (document.documentElement.classList.contains('dark')) {
            menu.style.backgroundColor = '#374151';
            menu.style.color = 'white';
        }
    }

    // Function to show a menu
    function showMenu(menu, button) {
        // Hide any active menu first
        hideAllMenus();

        if (!menu) return;

        // Show the menu
        menu.style.display = 'block';
        menu.style.visibility = 'visible';
        menu.style.opacity = '1';
        menu.classList.add('active');

        // Position the menu
        positionMenu(menu, button);

        // Show the overlay
        overlay.style.display = 'block';
        overlay.classList.add('active');

        // Update button state
        button.setAttribute('aria-expanded', 'true');

        // Set as active menu
        activeMenu = menu;
    }

    // Function to hide all menus
    function hideAllMenus() {
        const menus = [adhdMenu, productivityMenu, toolsMenu, userMenu];
        const buttons = [adhdButton, productivityButton, toolsButton, userButton];

        menus.forEach(function(menu, index) {
            if (menu) {
                menu.style.display = 'none';
                menu.style.visibility = 'hidden';
                menu.style.opacity = '0';
                menu.classList.remove('active');
            }

            if (buttons[index]) {
                buttons[index].setAttribute('aria-expanded', 'false');
            }
        });

        // Hide the overlay
        overlay.style.display = 'none';
        overlay.classList.remove('active');

        activeMenu = null;
    }

    // Setup ADHD dropdown
    if (adhdButton && adhdMenu) {
        adhdButton.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('ADHD button clicked');

            if (activeMenu === adhdMenu) {
                hideAllMenus();
            } else {
                showMenu(adhdMenu, adhdButton);
            }

            return false;
        };

        // Also handle hover with delay
        let adhdHoverTimeout = null;
        adhdButton.parentElement.addEventListener('mouseenter', function() {
            adhdHoverTimeout = setTimeout(() => {
                showMenu(adhdMenu, adhdButton);
            }, 200);
        });

        adhdButton.parentElement.addEventListener('mouseleave', function() {
            clearTimeout(adhdHoverTimeout);
        });

        // Handle hover on menu
        adhdMenu.addEventListener('mouseenter', function() {
            clearTimeout(adhdHoverTimeout);
        });

        adhdMenu.addEventListener('mouseleave', function() {
            adhdHoverTimeout = setTimeout(() => {
                if (activeMenu === adhdMenu) {
                    hideAllMenus();
                }
            }, 200);
        });
    }

    // Setup Productivity dropdown
    if (productivityButton && productivityMenu) {
        productivityButton.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Productivity button clicked');

            if (activeMenu === productivityMenu) {
                hideAllMenus();
            } else {
                showMenu(productivityMenu, productivityButton);
            }

            return false;
        };

        // Also handle hover with delay
        let productivityHoverTimeout = null;
        productivityButton.parentElement.addEventListener('mouseenter', function() {
            productivityHoverTimeout = setTimeout(() => {
                showMenu(productivityMenu, productivityButton);
            }, 200);
        });

        productivityButton.parentElement.addEventListener('mouseleave', function() {
            clearTimeout(productivityHoverTimeout);
        });

        // Handle hover on menu
        productivityMenu.addEventListener('mouseenter', function() {
            clearTimeout(productivityHoverTimeout);
        });

        productivityMenu.addEventListener('mouseleave', function() {
            productivityHoverTimeout = setTimeout(() => {
                if (activeMenu === productivityMenu) {
                    hideAllMenus();
                }
            }, 200);
        });
    }

    // Setup Tools dropdown
    if (toolsButton && toolsMenu) {
        toolsButton.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Tools button clicked');

            if (activeMenu === toolsMenu) {
                hideAllMenus();
            } else {
                showMenu(toolsMenu, toolsButton);
            }

            return false;
        };

        // Also handle hover with delay
        let toolsHoverTimeout = null;
        toolsButton.parentElement.addEventListener('mouseenter', function() {
            toolsHoverTimeout = setTimeout(() => {
                showMenu(toolsMenu, toolsButton);
            }, 200);
        });

        toolsButton.parentElement.addEventListener('mouseleave', function() {
            clearTimeout(toolsHoverTimeout);
        });

        // Handle hover on menu
        toolsMenu.addEventListener('mouseenter', function() {
            clearTimeout(toolsHoverTimeout);
        });

        toolsMenu.addEventListener('mouseleave', function() {
            toolsHoverTimeout = setTimeout(() => {
                if (activeMenu === toolsMenu) {
                    hideAllMenus();
                }
            }, 200);
        });
    }

    // Setup User dropdown
    if (userButton && userMenu) {
        userButton.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('User button clicked');

            if (activeMenu === userMenu) {
                hideAllMenus();
            } else {
                showMenu(userMenu, userButton);
            }

            return false;
        };
    }

    // Hide menus when clicking outside
    document.addEventListener('click', function(e) {
        if (activeMenu && !activeMenu.contains(e.target)) {
            hideAllMenus();
        }
    });

    // Hide menus when pressing Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && activeMenu) {
            hideAllMenus();
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (activeMenu) {
            const button = document.getElementById(activeMenu.id.replace('-menu', '-button'));
            if (button) {
                positionMenu(activeMenu, button);
            }
        }
    });
});
