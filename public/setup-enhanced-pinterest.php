<?php
/**
 * Setup Enhanced Pinterest Scraper
 *
 * This script sets up the Enhanced Pinterest Scraper and tests its functionality.
 */

require_once __DIR__ . '/../src/utils/View.php';
require_once __DIR__ . '/../src/utils/AssetManager.php';
require_once __DIR__ . '/../src/utils/Session.php';
require_once __DIR__ . '/../src/utils/Environment.php';
require_once __DIR__ . '/../src/api/EnhancedPinterestAPI.php';

// Load environment variables
Environment::load();

// Start the session
Session::start();

// Check if the user is logged in
if (!Session::isLoggedIn()) {
    // Set flash message
    Session::setFlash('error', 'Please log in to access the Enhanced Pinterest Scraper');

    // Redirect to login page
    header('Location: /momentum/login');
    exit;
}

// Get credentials from environment variables
$email = Environment::get('PINTEREST_EMAIL');
$password = Environment::get('PINTEREST_PASSWORD');
$username = Environment::get('PINTEREST_USERNAME');
$chromeProfile = Environment::get('CHROME_PROFILE_PATH');

// Initialize the API
$api = EnhancedPinterestAPI::getInstance($email, $password, $username, null, $chromeProfile);

// Handle form submission
$setupResult = null;
$loginResult = null;
$searchResult = null;
$downloadResult = null;
$message = null;
$messageType = 'info'; // 'info', 'success', or 'error'

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'setup':
                // Setup the scraper
                $setupResult = $api->setupScraper();
                break;

            case 'login':
                // Test login
                $loginResult = $api->login();
                break;

            case 'search':
                // Test search
                if (isset($_POST['query'])) {
                    $query = $_POST['query'];
                    $limit = isset($_POST['limit']) ? (int)$_POST['limit'] : 10;
                    $searchResult = $api->searchPins($query, 'pins', $limit);
                }
                break;

            case 'download':
                // Test download
                if (isset($_POST['image_url'])) {
                    $imageUrl = $_POST['image_url'];
                    $outputDir = __DIR__ . '/uploads/pinterest';

                    // Create directory if it doesn't exist
                    if (!file_exists($outputDir)) {
                        mkdir($outputDir, 0777, true);
                    }

                    $outputPath = $outputDir . '/' . time() . '_' . basename($imageUrl);
                    $downloadResult = $api->downloadImage($imageUrl, $outputPath);

                    if ($downloadResult) {
                        $downloadResult = [
                            'success' => true,
                            'path' => '/uploads/pinterest/' . basename($outputPath)
                        ];
                    } else {
                        $downloadResult = [
                            'success' => false,
                            'message' => 'Failed to download image'
                        ];
                    }
                }
                break;

            case 'update_env':
                // Update environment variables
                if (isset($_POST['chrome_profile'])) {
                    $newChromeProfile = $_POST['chrome_profile'];

                    // Update the .env file directly
                    $envFile = __DIR__ . '/../.env';
                    if (file_exists($envFile)) {
                        $envContent = file_get_contents($envFile);

                        // Update CHROME_PROFILE_PATH
                        if (preg_match('/CHROME_PROFILE_PATH=.*/', $envContent)) {
                            // Update existing entry
                            $envContent = preg_replace('/CHROME_PROFILE_PATH=.*/', 'CHROME_PROFILE_PATH=' . $newChromeProfile, $envContent);
                        } else {
                            // Add new entry
                            $envContent .= "\n# Chrome profile path\nCHROME_PROFILE_PATH=" . $newChromeProfile . "\n";
                        }

                        // Update PINTEREST_API_TYPE
                        if (preg_match('/PINTEREST_API_TYPE=.*/', $envContent)) {
                            // Update existing entry
                            $envContent = preg_replace('/PINTEREST_API_TYPE=.*/', 'PINTEREST_API_TYPE=enhanced', $envContent);
                        } else {
                            // Add new entry
                            $envContent .= "\n# Pinterest API type\nPINTEREST_API_TYPE=enhanced\n";
                        }

                        // Write back to file
                        file_put_contents($envFile, $envContent);

                        // Update local variable
                        $chromeProfile = $newChromeProfile;

                        // Reload environment variables
                        Environment::load();

                        $message = 'Environment variables updated successfully. Chrome profile path set to: ' . $newChromeProfile;
                        $messageType = 'success';
                    } else {
                        $message = 'Error: .env file not found. Could not update environment variables.';
                        $messageType = 'error';
                    }
                }
                break;
        }
    }
}

// Check if the enhanced scraper script exists
$enhancedScraperExists = file_exists(__DIR__ . '/../scripts/pinterest/enhanced_pinterest_scraper.py');
$setupScriptExists = file_exists(__DIR__ . '/../scripts/pinterest/setup_enhanced_scraper.py');

// Get the current API type
$currentApiType = Environment::get('PINTEREST_API_TYPE', 'unofficial');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Enhanced Pinterest Scraper</title>
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    <link rel="stylesheet" href="/momentum/css/fontawesome.css">
    <script src="/momentum/js/alpine.js" defer></script>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold mb-2">Setup Enhanced Pinterest Scraper</h1>
            <p class="text-gray-600 dark:text-gray-400">
                This page helps you set up and test the Enhanced Pinterest Scraper.
            </p>
        </div>

        <?php if ($message): ?>
            <div class="mb-8 p-4 rounded-lg <?php
                if ($messageType === 'success') echo 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200';
                else if ($messageType === 'error') echo 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200';
                else echo 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200';
            ?>">
                <p><?php echo htmlspecialchars($message); ?></p>
            </div>
        <?php endif; ?>

        <!-- Status Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Status</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <p class="mb-2">
                        <span class="font-semibold">Enhanced Scraper Script:</span>
                        <?php if ($enhancedScraperExists): ?>
                            <span class="text-green-600 dark:text-green-400"><i class="fas fa-check-circle"></i> Found</span>
                        <?php else: ?>
                            <span class="text-red-600 dark:text-red-400"><i class="fas fa-times-circle"></i> Not Found</span>
                        <?php endif; ?>
                    </p>

                    <p class="mb-2">
                        <span class="font-semibold">Setup Script:</span>
                        <?php if ($setupScriptExists): ?>
                            <span class="text-green-600 dark:text-green-400"><i class="fas fa-check-circle"></i> Found</span>
                        <?php else: ?>
                            <span class="text-red-600 dark:text-red-400"><i class="fas fa-times-circle"></i> Not Found</span>
                        <?php endif; ?>
                    </p>
                </div>

                <div>
                    <p class="mb-2">
                        <span class="font-semibold">Current API Type:</span>
                        <span class="font-mono"><?= htmlspecialchars($currentApiType) ?></span>
                    </p>

                    <p class="mb-2">
                        <span class="font-semibold">Chrome Profile:</span>
                        <span class="font-mono"><?= htmlspecialchars($chromeProfile ?: 'Not Set') ?></span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Setup Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">1. Setup</h2>

            <form method="post" class="mb-4">
                <input type="hidden" name="action" value="setup">
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                    <i class="fas fa-cog mr-2"></i> Setup Enhanced Pinterest Scraper
                </button>
            </form>

            <?php if ($setupResult !== null): ?>
                <div class="mt-4 p-4 rounded <?= $setupResult ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' ?>">
                    <?php if ($setupResult): ?>
                        <p><i class="fas fa-check-circle mr-2"></i> Setup completed successfully!</p>
                    <?php else: ?>
                        <p><i class="fas fa-times-circle mr-2"></i> Setup failed. Please check the error logs.</p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Chrome Profile Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">2. Configure Chrome Profile</h2>

            <form method="post" class="mb-4">
                <input type="hidden" name="action" value="update_env">

                <div class="mb-4">
                    <label for="chrome_profile" class="block mb-2 font-medium">Chrome Profile Path:</label>
                    <input type="text" id="chrome_profile" name="chrome_profile" value="<?= htmlspecialchars($chromeProfile ?: '') ?>" class="w-full px-4 py-2 border rounded dark:bg-gray-700 dark:border-gray-600" placeholder="e.g., C:/Users/<USER>/AppData/Local/Google/Chrome/User Data/Profile 1">
                </div>

                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save mr-2"></i> Save Configuration
                </button>
            </form>

            <div class="mt-4 p-4 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded">
                <p><i class="fas fa-info-circle mr-2"></i> Make sure you're logged into Pinterest in this Chrome profile.</p>
            </div>
        </div>

        <!-- Test Login Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">3. Test Login</h2>

            <form method="post" class="mb-4">
                <input type="hidden" name="action" value="login">
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                    <i class="fas fa-sign-in-alt mr-2"></i> Test Pinterest Login
                </button>
            </form>

            <?php if ($loginResult !== null): ?>
                <div class="mt-4 p-4 rounded <?= $loginResult ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' ?>">
                    <?php if ($loginResult): ?>
                        <p><i class="fas fa-check-circle mr-2"></i> Login successful!</p>
                    <?php else: ?>
                        <p><i class="fas fa-times-circle mr-2"></i> Login failed. Please check your credentials and Chrome profile.</p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Test Search Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">4. Test Search</h2>

            <form method="post" class="mb-4">
                <input type="hidden" name="action" value="search">

                <div class="mb-4">
                    <label for="query" class="block mb-2 font-medium">Search Query:</label>
                    <input type="text" id="query" name="query" class="w-full px-4 py-2 border rounded dark:bg-gray-700 dark:border-gray-600" placeholder="e.g., home decor">
                </div>

                <div class="mb-4">
                    <label for="limit" class="block mb-2 font-medium">Result Limit:</label>
                    <input type="number" id="limit" name="limit" value="10" min="1" max="50" class="w-full px-4 py-2 border rounded dark:bg-gray-700 dark:border-gray-600">
                </div>

                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                    <i class="fas fa-search mr-2"></i> Test Search
                </button>
            </form>

            <?php if ($searchResult !== null): ?>
                <div class="mt-4">
                    <h3 class="font-bold mb-2">Search Results:</h3>

                    <?php if (empty($searchResult) || !is_array($searchResult)): ?>
                        <p class="text-red-600 dark:text-red-400">No results found or invalid response from Pinterest API.</p>
                    <?php else: ?>
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            <?php foreach ($searchResult as $pin): ?>
                                <?php if (is_array($pin)): ?>
                                <div class="bg-gray-100 dark:bg-gray-700 rounded overflow-hidden">
                                    <img src="<?= htmlspecialchars($pin['image_url'] ?? '') ?>"
                                         alt="<?= htmlspecialchars($pin['title'] ?? 'Pinterest Pin') ?>"
                                         class="w-full h-40 object-cover">
                                    <div class="p-2">
                                        <p class="font-bold truncate"><?= htmlspecialchars($pin['title'] ?? 'Untitled Pin') ?></p>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
                                            <?= htmlspecialchars($pin['board_name'] ?? 'Unknown Board') ?>
                                        </p>
                                        <div class="mt-2 flex justify-between text-xs">
                                            <span><i class="fas fa-thumbtack"></i>
                                                <?= isset($pin['save_count']) ? number_format((int)$pin['save_count']) : '0' ?>
                                            </span>
                                            <?php if (isset($pin['image_url']) && !empty($pin['image_url'])): ?>
                                            <form method="post" class="inline">
                                                <input type="hidden" name="action" value="download">
                                                <input type="hidden" name="image_url" value="<?= htmlspecialchars($pin['image_url']) ?>">
                                                <button type="submit" class="text-blue-600 dark:text-blue-400 hover:underline">
                                                    <i class="fas fa-download"></i> Download
                                                </button>
                                            </form>
                                            <?php else: ?>
                                            <span class="text-gray-500">No image</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Download Result -->
        <?php if ($downloadResult !== null): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold mb-4">Download Result</h2>

                <?php if (is_array($downloadResult) && isset($downloadResult['success']) && $downloadResult['success']): ?>
                    <div class="p-4 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded mb-4">
                        <p><i class="fas fa-check-circle mr-2"></i> Image downloaded successfully!</p>
                    </div>

                    <div class="mt-4">
                        <?php if (isset($downloadResult['path']) && !empty($downloadResult['path'])): ?>
                            <img src="<?= htmlspecialchars($downloadResult['path']) ?>" alt="Downloaded Image" class="max-w-full h-auto max-h-96 mx-auto">
                        <?php else: ?>
                            <p class="text-center text-gray-600 dark:text-gray-400">Image path not available</p>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded">
                        <p><i class="fas fa-times-circle mr-2"></i>
                            <?= isset($downloadResult['message']) ? htmlspecialchars($downloadResult['message']) : 'Download failed with unknown error' ?>
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- Navigation Links -->
        <div class="mt-8 text-center">
            <a href="/momentum/test-enhanced-pinterest-scraper.php" class="inline-block px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors mr-4">
                <i class="fas fa-vial mr-2"></i> Run Tests
            </a>
            <a href="/momentum/clone/pinterest" class="inline-block px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i> Back to Pinterest Clone
            </a>
        </div>
    </div>
</body>
</html>
