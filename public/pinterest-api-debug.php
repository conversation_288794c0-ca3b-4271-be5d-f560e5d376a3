<?php
/**
 * Pinterest API Debug Page
 *
 * This page provides detailed debugging information for the Pinterest API integration.
 */

// Include necessary files
require_once __DIR__ . '/../src/utils/View.php';
require_once __DIR__ . '/../src/utils/AssetManager.php';
require_once __DIR__ . '/../src/utils/Environment.php';
require_once __DIR__ . '/../src/api/PinterestAPI.php';
require_once __DIR__ . '/../src/api/PinterestAPIFactory.php';

// Start output buffering
ob_start();

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load environment variables
Environment::load();

// Get environment variables
$email = Environment::get('PINTEREST_EMAIL', '');
$password = Environment::get('PINTEREST_PASSWORD', '');
$username = Environment::get('PINTEREST_USERNAME', '');
$chromeProfile = Environment::get('CHROME_PROFILE_PATH', '');
$apiType = Environment::get('PINTEREST_API_TYPE', 'unofficial');
$disableApi = Environment::get('DISABLE_PINTEREST_API', 'false');
$pythonPath = Environment::get('PYTHON_PATH', 'python');

// Check server software
$serverSoftware = $_SERVER['SERVER_SOFTWARE'] ?? '';
$isLiteSpeed = strpos($serverSoftware, 'LiteSpeed') !== false;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pinterest API Debug</title>

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="/momentum/css/tailwind.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .debug-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border-radius: 0.5rem;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .debug-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #333;
        }
        .debug-item {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
        }
        .debug-success {
            background-color: #d1fae5;
            border-left: 4px solid #10b981;
        }
        .debug-error {
            background-color: #fee2e2;
            border-left: 4px solid #ef4444;
        }
        .debug-warning {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
        }
        .debug-info {
            background-color: #e0f2fe;
            border-left: 4px solid #0ea5e9;
        }
        .debug-code {
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="debug-container">
        <h1 class="text-3xl font-bold mb-6">Pinterest API Debug</h1>

        <!-- Environment Variables -->
        <div class="debug-section">
            <h2 class="debug-title">Environment Variables</h2>

            <div class="debug-item debug-info">
                <strong>API Type:</strong> <?= htmlspecialchars($apiType) ?>
            </div>

            <div class="debug-item debug-info">
                <strong>API Disabled:</strong> <?= htmlspecialchars($disableApi) ?>
            </div>

            <div class="debug-item debug-info">
                <strong>Server Software:</strong> <?= htmlspecialchars($serverSoftware) ?>
            </div>

            <div class="debug-item debug-info">
                <strong>Is LiteSpeed:</strong> <?= $isLiteSpeed ? 'Yes' : 'No' ?>
            </div>

            <div class="debug-item debug-info">
                <strong>Python Path:</strong> <?= htmlspecialchars($pythonPath) ?>
            </div>

            <div class="debug-item debug-info">
                <strong>Email:</strong> <?= !empty($email) ? htmlspecialchars($email) : '<span class="text-red-600">Not set</span>' ?>
            </div>

            <div class="debug-item debug-info">
                <strong>Username:</strong> <?= !empty($username) ? htmlspecialchars($username) : '<span class="text-red-600">Not set</span>' ?>
            </div>

            <div class="debug-item debug-info">
                <strong>Password:</strong> <?= !empty($password) ? '********' : '<span class="text-red-600">Not set</span>' ?>
            </div>

            <div class="debug-item debug-info">
                <strong>Chrome Profile:</strong> <?= !empty($chromeProfile) ? htmlspecialchars($chromeProfile) : '<span class="text-red-600">Not set</span>' ?>
            </div>

            <?php if (!empty($chromeProfile)): ?>
                <div class="debug-item debug-info">
                    <strong>Chrome Profile Status:</strong>
                    <?php
                    if (file_exists($chromeProfile)) {
                        echo '<span class="text-green-600">Profile directory exists</span>';

                        // Check for key files in the profile
                        $preferencesFile = $chromeProfile . '/Preferences';
                        $cookiesFile = $chromeProfile . '/Cookies';
                        $loginDataFile = $chromeProfile . '/Login Data';

                        echo '<ul class="mt-2 list-disc list-inside">';
                        echo '<li>Preferences file: ' . (file_exists($preferencesFile) ? '<span class="text-green-600">Exists</span>' : '<span class="text-red-600">Missing</span>') . '</li>';
                        echo '<li>Cookies file: ' . (file_exists($cookiesFile) ? '<span class="text-green-600">Exists</span>' : '<span class="text-red-600">Missing</span>') . '</li>';
                        echo '<li>Login Data file: ' . (file_exists($loginDataFile) ? '<span class="text-green-600">Exists</span>' : '<span class="text-red-600">Missing</span>') . '</li>';
                        echo '</ul>';
                    } else {
                        echo '<span class="text-red-600">Profile directory does not exist</span>';
                    }
                    ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- API Factory Debug -->
        <div class="debug-section">
            <h2 class="debug-title">API Factory Debug</h2>

            <?php
            try {
                // Add debug output
                echo '<div class="debug-item debug-info"><strong>Debug Output:</strong><div class="debug-code">';

                // Check if API is disabled
                if ($disableApi === 'true') {
                    echo "API is disabled via environment variable.\n";
                }

                // Check if we're on a shared hosting environment
                if ($isLiteSpeed) {
                    echo "Detected LiteSpeed server (likely Hostinger).\n";
                }

                // Check API type
                echo "API Type: $apiType\n";

                // Check credentials
                if ($apiType === 'unofficial') {
                    if (empty($email) || empty($password) || empty($username)) {
                        echo "Missing credentials for unofficial API.\n";
                    } else {
                        echo "Credentials are set for unofficial API.\n";
                    }
                } else if ($apiType === 'official') {
                    $apiKey = Environment::get('PINTEREST_API_KEY', '');
                    $accessToken = Environment::get('PINTEREST_ACCESS_TOKEN', '');

                    if (empty($apiKey) && empty($accessToken)) {
                        echo "Missing API key or access token for official API.\n";
                    } else {
                        echo "API key or access token is set for official API.\n";
                    }
                }

                echo '</div></div>';

                // Get API instance
                $api = PinterestAPIFactory::getAPI();

                if ($api === null) {
                    echo '<div class="debug-item debug-error">
                        <strong>API Factory Result:</strong> Returned NULL (using fallback data)
                    </div>';
                } else {
                    echo '<div class="debug-item debug-success">
                        <strong>API Factory Result:</strong> API instance created successfully
                    </div>';
                }
            } catch (Exception $e) {
                echo '<div class="debug-item debug-error">
                    <strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '
                </div>';
            }
            ?>
        </div>

        <!-- Direct API Initialization -->
        <div class="debug-section">
            <h2 class="debug-title">Direct API Initialization</h2>

            <?php
            try {
                // Try to initialize the API directly
                $directApi = PinterestAPI::getInstance($email, $password, $username, null, null, $chromeProfile);

                echo '<div class="debug-item debug-success">
                    <strong>Direct API Initialization:</strong> Successful
                </div>';

                // Get the script directory
                $reflection = new ReflectionClass($directApi);
                $scriptDirProp = $reflection->getProperty('scriptDir');
                $scriptDirProp->setAccessible(true);
                $scriptDir = $scriptDirProp->getValue($directApi);

                echo '<div class="debug-item debug-info">
                    <strong>Script Directory:</strong> ' . htmlspecialchars($scriptDir) . '
                </div>';

                // Check if login script exists
                $loginScriptPath = $scriptDir . '/login.py';
                if (file_exists($loginScriptPath)) {
                    echo '<div class="debug-item debug-success">
                        <strong>Login Script:</strong> Found at ' . htmlspecialchars($loginScriptPath) . '
                    </div>';

                    // Show the first few lines of the login script
                    $loginScript = file_get_contents($loginScriptPath);
                    $scriptPreview = substr($loginScript, 0, 500) . (strlen($loginScript) > 500 ? '...' : '');
                    echo '<div class="debug-item debug-info">
                        <strong>Login Script Preview:</strong>
                        <div class="debug-code">' . htmlspecialchars($scriptPreview) . '</div>
                    </div>';
                } else {
                    echo '<div class="debug-item debug-error">
                        <strong>Login Script:</strong> Not found at ' . htmlspecialchars($loginScriptPath) . '
                    </div>';
                }

                // Try to login with detailed error reporting
                try {
                    $loginResult = $directApi->login();

                    if ($loginResult && isset($loginResult['success']) && $loginResult['success']) {
                        echo '<div class="debug-item debug-success">
                            <strong>Login Result:</strong> ' . htmlspecialchars($loginResult['message'] ?? 'Login successful') . '
                        </div>';
                    } else {
                        echo '<div class="debug-item debug-error">
                            <strong>Login Result:</strong> ' . htmlspecialchars($loginResult['message'] ?? 'Login failed') . '
                        </div>';

                        // Get the login command
                        $loginMethodReflection = $reflection->getMethod('login');
                        $loginMethodReflection->setAccessible(true);

                        // Get the Python command that would be executed
                        $pythonPathProp = $reflection->getProperty('pythonPath');
                        $pythonPathProp->setAccessible(true);
                        $pythonPath = $pythonPathProp->getValue($directApi);

                        $emailProp = $reflection->getProperty('email');
                        $emailProp->setAccessible(true);
                        $email = $emailProp->getValue($directApi);

                        $passwordProp = $reflection->getProperty('password');
                        $passwordProp->setAccessible(true);
                        $password = $passwordProp->getValue($directApi);

                        $usernameProp = $reflection->getProperty('username');
                        $usernameProp->setAccessible(true);
                        $username = $usernameProp->getValue($directApi);

                        $credRootProp = $reflection->getProperty('credRoot');
                        $credRootProp->setAccessible(true);
                        $credRoot = $credRootProp->getValue($directApi);

                        $chromeProfileProp = $reflection->getProperty('chromeProfile');
                        $chromeProfileProp->setAccessible(true);
                        $chromeProfile = $chromeProfileProp->getValue($directApi);

                        $command = $pythonPath . ' ' . escapeshellarg($loginScriptPath) . ' ' .
                                  escapeshellarg($email) . ' ' .
                                  escapeshellarg($password) . ' ' .
                                  escapeshellarg($username) . ' ' .
                                  escapeshellarg($credRoot ?: __DIR__ . '/../data/pinterest_credentials');

                        if ($chromeProfile) {
                            $command .= ' ' . escapeshellarg($chromeProfile);
                        }

                        echo '<div class="debug-item debug-info">
                            <strong>Login Command (Redacted):</strong>
                            <div class="debug-code">' . htmlspecialchars(str_replace($password, '********', $command)) . '</div>
                        </div>';

                        // Try to run the command directly with output
                        $output = shell_exec($command . ' 2>&1');
                        echo '<div class="debug-item debug-info">
                            <strong>Direct Command Output:</strong>
                            <div class="debug-code">' . htmlspecialchars($output) . '</div>
                        </div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="debug-item debug-error">
                        <strong>Login Exception:</strong> ' . htmlspecialchars($e->getMessage()) . '
                        <div class="debug-code">' . htmlspecialchars($e->getTraceAsString()) . '</div>
                    </div>';
                }
            } catch (Exception $e) {
                echo '<div class="debug-item debug-error">
                    <strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '
                </div>';
            }
            ?>
        </div>

        <!-- Python Test -->
        <div class="debug-section">
            <h2 class="debug-title">Python Test</h2>

            <?php
            // Test Python
            $pythonTest = shell_exec($pythonPath . ' --version 2>&1');

            // Add a section to install missing packages if needed
            $installPackages = isset($_GET['install']) && $_GET['install'] === 'true';

            if ($installPackages) {
                echo '<div class="debug-item debug-info">
                    <strong>Installing Required Packages:</strong>
                </div>';

                // Install py3-pinterest
                $py3pinInstallOutput = shell_exec($pythonPath . ' -m pip install py3pin 2>&1');
                echo '<div class="debug-item debug-info">
                    <strong>py3-pinterest Installation:</strong>
                    <div class="debug-code">' . htmlspecialchars($py3pinInstallOutput) . '</div>
                </div>';

                // Install selenium
                $seleniumInstallOutput = shell_exec($pythonPath . ' -m pip install selenium 2>&1');
                echo '<div class="debug-item debug-info">
                    <strong>selenium Installation:</strong>
                    <div class="debug-code">' . htmlspecialchars($seleniumInstallOutput) . '</div>
                </div>';

                // Install webdriver_manager
                $webdriverInstallOutput = shell_exec($pythonPath . ' -m pip install webdriver_manager 2>&1');
                echo '<div class="debug-item debug-info">
                    <strong>webdriver_manager Installation:</strong>
                    <div class="debug-code">' . htmlspecialchars($webdriverInstallOutput) . '</div>
                </div>';
            } else {
                echo '<div class="debug-item debug-info">
                    <a href="?install=true" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-download mr-2"></i> Install Required Python Packages
                    </a>
                </div>';
            }
            echo '<div class="debug-item debug-info">
                <strong>Python Version:</strong> ' . htmlspecialchars($pythonTest) . '
            </div>';

            // Test py3-pinterest - using proper multi-line script for Python 3.13+
            $py3PinterestScript = <<<'PYTHON'
try:
    import py3pin
    print('installed')
except ImportError:
    print('not installed')
PYTHON;

            // Save the script to a temporary file
            $tempPy3PinterestFile = tempnam(sys_get_temp_dir(), 'py3pin_test');
            file_put_contents($tempPy3PinterestFile, $py3PinterestScript);

            // Execute the script
            $py3PinterestTest = shell_exec($pythonPath . ' ' . escapeshellarg($tempPy3PinterestFile) . ' 2>&1');
            unlink($tempPy3PinterestFile); // Clean up

            echo '<div class="debug-item debug-info">
                <strong>py3-pinterest:</strong> ' . htmlspecialchars($py3PinterestTest) . '
            </div>';

            // Test selenium - using proper multi-line script for Python 3.13+
            $seleniumScript = <<<'PYTHON'
try:
    import selenium
    print('installed')
except ImportError:
    print('not installed')
PYTHON;

            // Save the script to a temporary file
            $tempSeleniumFile = tempnam(sys_get_temp_dir(), 'selenium_test');
            file_put_contents($tempSeleniumFile, $seleniumScript);

            // Execute the script
            $seleniumTest = shell_exec($pythonPath . ' ' . escapeshellarg($tempSeleniumFile) . ' 2>&1');
            unlink($tempSeleniumFile); // Clean up

            echo '<div class="debug-item debug-info">
                <strong>selenium:</strong> ' . htmlspecialchars($seleniumTest) . '
            </div>';

            // Test webdriver_manager - using proper multi-line script for Python 3.13+
            $webdriverManagerScript = <<<'PYTHON'
try:
    import webdriver_manager
    print('installed')
except ImportError:
    print('not installed')
PYTHON;

            // Save the script to a temporary file
            $tempWebdriverFile = tempnam(sys_get_temp_dir(), 'webdriver_test');
            file_put_contents($tempWebdriverFile, $webdriverManagerScript);

            // Execute the script
            $webdriverManagerTest = shell_exec($pythonPath . ' ' . escapeshellarg($tempWebdriverFile) . ' 2>&1');
            unlink($tempWebdriverFile); // Clean up

            echo '<div class="debug-item debug-info">
                <strong>webdriver_manager:</strong> ' . htmlspecialchars($webdriverManagerTest) . '
            </div>';
            ?>
        </div>

        <div class="mt-8">
            <a href="/momentum/clone/pinterest" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i> Back to Pinterest Dashboard
            </a>
        </div>
    </div>
</body>
</html>
<?php
// End output buffering and display the page
echo ob_get_clean();
?>
