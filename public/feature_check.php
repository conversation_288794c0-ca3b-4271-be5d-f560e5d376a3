<?php
/**
 * Feature Check Script
 *
 * This script checks if the Task Batching and Batch Templates features are available
 * and provides links to access them.
 */

// Define base path
define('BASE_PATH', dirname(__DIR__));

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple session handling
session_start();

// Database connection class
class SimpleDatabase {
    private $pdo;

    public function __construct() {
        try {
            $this->pdo = new PDO(
                'mysql:host=localhost;dbname=momentum;charset=utf8mb4',
                'root',
                '',
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
        } catch (PDOException $e) {
            die('Database connection failed: ' . $e->getMessage());
        }
    }

    public function query($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}

// Initialize database connection
$db = new SimpleDatabase();

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user']);

// Check if tables exist
$taskBatchesExists = false;
$batchTemplatesExists = false;

try {
    $result = $db->query("SHOW TABLES LIKE 'task_batches'");
    $taskBatchesExists = $result->rowCount() > 0;

    $result = $db->query("SHOW TABLES LIKE 'batch_templates'");
    $batchTemplatesExists = $result->rowCount() > 0;
} catch (Exception $e) {
    $error = $e->getMessage();
}

// Create tables if they don't exist
$tablesCreated = false;
$createError = '';

if (isset($_POST['create_tables'])) {
    try {
        // Create task_batches table
        if (!$taskBatchesExists) {
            $db->query("
                CREATE TABLE IF NOT EXISTS `task_batches` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `user_id` int(11) NOT NULL,
                  `name` varchar(255) NOT NULL,
                  `description` text,
                  `energy_level` enum('high', 'medium', 'low') NOT NULL DEFAULT 'medium',
                  `estimated_time` int(11) DEFAULT NULL COMMENT 'Estimated time in minutes',
                  `status` enum('active', 'completed', 'archived') NOT NULL DEFAULT 'active',
                  `created_at` datetime NOT NULL,
                  `updated_at` datetime NOT NULL,
                  PRIMARY KEY (`id`),
                  KEY `user_id` (`user_id`),
                  KEY `energy_level` (`energy_level`),
                  KEY `status` (`status`),
                  CONSTRAINT `task_batches_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");

            // Create task_batch_items table
            $db->query("
                CREATE TABLE IF NOT EXISTS `task_batch_items` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `batch_id` int(11) NOT NULL,
                  `task_id` int(11) NOT NULL,
                  `position` int(11) NOT NULL DEFAULT 0,
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `batch_task` (`batch_id`,`task_id`),
                  KEY `task_id` (`task_id`),
                  CONSTRAINT `task_batch_items_ibfk_1` FOREIGN KEY (`batch_id`) REFERENCES `task_batches` (`id`) ON DELETE CASCADE,
                  CONSTRAINT `task_batch_items_ibfk_2` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");
        }

        // Create batch_templates table
        if (!$batchTemplatesExists) {
            $db->query("
                CREATE TABLE IF NOT EXISTS `batch_templates` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `user_id` int(11) NOT NULL,
                  `name` varchar(255) NOT NULL,
                  `description` text,
                  `energy_level` enum('high', 'medium', 'low') NOT NULL DEFAULT 'medium',
                  `estimated_time` int(11) DEFAULT NULL COMMENT 'Estimated time in minutes',
                  `is_recurring` tinyint(1) NOT NULL DEFAULT 0,
                  `recurrence_pattern` enum('daily', 'weekdays', 'weekly', 'monthly') DEFAULT NULL,
                  `created_at` datetime NOT NULL,
                  `updated_at` datetime NOT NULL,
                  PRIMARY KEY (`id`),
                  KEY `user_id` (`user_id`),
                  KEY `energy_level` (`energy_level`),
                  KEY `is_recurring` (`is_recurring`),
                  CONSTRAINT `batch_templates_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");

            // Create batch_template_items table
            $db->query("
                CREATE TABLE IF NOT EXISTS `batch_template_items` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `template_id` int(11) NOT NULL,
                  `task_type` varchar(255) NOT NULL,
                  `description` text,
                  `estimated_time` int(11) DEFAULT NULL COMMENT 'Estimated time in minutes',
                  `position` int(11) NOT NULL DEFAULT 0,
                  PRIMARY KEY (`id`),
                  KEY `template_id` (`template_id`),
                  CONSTRAINT `batch_template_items_ibfk_1` FOREIGN KEY (`template_id`) REFERENCES `batch_templates` (`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");

            // Add template-related columns to task_batches table
            $db->query("
                ALTER TABLE `task_batches`
                ADD COLUMN `template_id` int(11) DEFAULT NULL,
                ADD COLUMN `is_recurring` tinyint(1) NOT NULL DEFAULT 0,
                ADD COLUMN `recurrence_pattern` enum('daily', 'weekdays', 'weekly', 'monthly') DEFAULT NULL,
                ADD COLUMN `last_generated` date DEFAULT NULL;
            ");

            // Add indexes and foreign keys
            $db->query("
                ALTER TABLE `task_batches`
                ADD KEY `template_id` (`template_id`),
                ADD KEY `is_recurring` (`is_recurring`);
            ");

            // Add foreign key constraint
            $db->query("
                ALTER TABLE `task_batches`
                ADD CONSTRAINT `task_batches_template_fk` FOREIGN KEY (`template_id`) REFERENCES `batch_templates` (`id`) ON DELETE SET NULL;
            ");
        }

        // Check if tables exist after creation
        $result = $db->query("SHOW TABLES LIKE 'task_batches'");
        $taskBatchesExists = $result->rowCount() > 0;

        $result = $db->query("SHOW TABLES LIKE 'batch_templates'");
        $batchTemplatesExists = $result->rowCount() > 0;

        $tablesCreated = true;
    } catch (Exception $e) {
        $createError = $e->getMessage();
    }
}

// Check if energy_levels table exists
$energyLevelsExists = false;
try {
    $result = $db->query("SHOW TABLES LIKE 'energy_levels'");
    $energyLevelsExists = $result->rowCount() > 0;

    if (!$energyLevelsExists && isset($_POST['create_tables'])) {
        $db->query("
            CREATE TABLE IF NOT EXISTS `energy_levels` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) NOT NULL,
              `level` int(11) NOT NULL,
              `notes` text,
              `recorded_at` datetime NOT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              CONSTRAINT `energy_levels_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ");

        $result = $db->query("SHOW TABLES LIKE 'energy_levels'");
        $energyLevelsExists = $result->rowCount() > 0;
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feature Check - Momentum</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-3xl mx-auto">
            <h1 class="text-3xl font-bold text-center mb-8">Momentum Feature Check</h1>

            <?php if (isset($error)): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <p><strong>Error:</strong> <?= htmlspecialchars($error) ?></p>
                </div>
            <?php endif; ?>

            <?php if ($tablesCreated): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <p><strong>Success:</strong> Tables have been created successfully!</p>
                </div>
            <?php endif; ?>

            <?php if (!empty($createError)): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <p><strong>Error creating tables:</strong> <?= htmlspecialchars($createError) ?></p>
                </div>
            <?php endif; ?>

            <div class="bg-white shadow-md rounded-lg overflow-hidden mb-6">
                <div class="px-6 py-4 bg-gray-50 border-b">
                    <h2 class="text-xl font-semibold">Database Tables Status</h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-<?= $taskBatchesExists ? 'green' : 'red' ?>-50 p-4 rounded-lg">
                            <h3 class="font-medium text-<?= $taskBatchesExists ? 'green' : 'red' ?>-800">Task Batches</h3>
                            <p class="text-<?= $taskBatchesExists ? 'green' : 'red' ?>-600 mt-1">
                                <?= $taskBatchesExists ? 'Table exists' : 'Table does not exist' ?>
                            </p>
                        </div>
                        <div class="bg-<?= $batchTemplatesExists ? 'green' : 'red' ?>-50 p-4 rounded-lg">
                            <h3 class="font-medium text-<?= $batchTemplatesExists ? 'green' : 'red' ?>-800">Batch Templates</h3>
                            <p class="text-<?= $batchTemplatesExists ? 'green' : 'red' ?>-600 mt-1">
                                <?= $batchTemplatesExists ? 'Table exists' : 'Table does not exist' ?>
                            </p>
                        </div>
                        <div class="bg-<?= $energyLevelsExists ? 'green' : 'red' ?>-50 p-4 rounded-lg">
                            <h3 class="font-medium text-<?= $energyLevelsExists ? 'green' : 'red' ?>-800">Energy Levels</h3>
                            <p class="text-<?= $energyLevelsExists ? 'green' : 'red' ?>-600 mt-1">
                                <?= $energyLevelsExists ? 'Table exists' : 'Table does not exist' ?>
                            </p>
                        </div>
                    </div>

                    <?php if (!$taskBatchesExists || !$batchTemplatesExists || !$energyLevelsExists): ?>
                        <form method="post" class="mt-6">
                            <button type="submit" name="create_tables" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded">
                                Create Missing Tables
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>

            <div class="bg-white shadow-md rounded-lg overflow-hidden mb-6">
                <div class="px-6 py-4 bg-gray-50 border-b">
                    <h2 class="text-xl font-semibold">Feature Links</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <h3 class="font-medium text-gray-800 mb-2">Task Batching</h3>
                            <a href="/momentum/productivity/task-batching" class="inline-flex items-center px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white rounded">
                                <i class="fas fa-layer-group mr-2"></i> Go to Task Batching
                            </a>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-800 mb-2">Batch Templates</h3>
                            <a href="/momentum/productivity/batch-templates" class="inline-flex items-center px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded">
                                <i class="fas fa-copy mr-2"></i> Go to Batch Templates
                            </a>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-800 mb-2">Energy Tracking</h3>
                            <a href="/momentum/productivity/energy-tracking" class="inline-flex items-center px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded">
                                <i class="fas fa-bolt mr-2"></i> Go to Energy Tracking
                            </a>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-800 mb-2">Dashboard</h3>
                            <a href="/momentum/dashboard" class="inline-flex items-center px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded">
                                <i class="fas fa-home mr-2"></i> Go to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center text-gray-500 text-sm">
                <p>Momentum - ADHD-friendly personal management system</p>
            </div>
        </div>
    </div>
</body>
</html>
