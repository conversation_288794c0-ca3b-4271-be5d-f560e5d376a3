<?php
// Set the content type to HTML
header('Content-Type: text/html');

// Output the HTML content
?>
<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ADHD-Friendly Project Planning Guide - Momentum</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        success: {
                            50: '#ecfdf5',
                            100: '#d1fae5',
                            200: '#a7f3d0',
                            300: '#6ee7b7',
                            400: '#34d399',
                            500: '#10b981',
                            600: '#059669',
                            700: '#047857',
                            800: '#065f46',
                            900: '#064e3b',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        danger: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#ef4444',
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d',
                        },
                    },
                },
            },
        }
    </script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .sidebar {
            height: calc(100vh - 4rem);
            overflow-y: auto;
        }
        
        .main-content {
            height: calc(100vh - 4rem);
            overflow-y: auto;
        }
        
        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        .dark ::-webkit-scrollbar-track {
            background: #374151;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        /* Tip boxes */
        .tip-box {
            border-left: 4px solid;
            padding: 1rem;
            margin: 1.5rem 0;
            border-radius: 0.375rem;
        }
        
        .tip-box.tip {
            border-color: #0ea5e9;
            background-color: rgba(14, 165, 233, 0.1);
        }
        
        .tip-box.example {
            border-color: #8b5cf6;
            background-color: rgba(139, 92, 246, 0.1);
        }
        
        .tip-box.warning {
            border-color: #f59e0b;
            background-color: rgba(245, 158, 11, 0.1);
        }
        
        /* Template boxes */
        .template-box {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1.5rem 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .dark .template-box {
            background-color: #1e293b;
            border-color: #334155;
        }
        
        /* Print styles */
        @media print {
            .sidebar, .no-print {
                display: none !important;
            }
            
            .main-content {
                width: 100% !important;
                height: auto !important;
                overflow: visible !important;
            }
            
            body {
                background-color: white !important;
                color: black !important;
            }
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white">
    <!-- Navigation Bar -->
    <div class="bg-white dark:bg-gray-800 shadow no-print">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo and brand -->
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="/momentum/dashboard" class="text-xl font-bold text-primary-600 dark:text-primary-400">
                            <i class="fas fa-bolt mr-2"></i>Momentum
                        </a>
                    </div>
                </div>
                
                <!-- Desktop navigation -->
                <div class="hidden md:flex md:items-center md:space-x-4" id="desktop-navigation">
                    <nav class="flex space-x-4">
                        <a href="/momentum/dashboard" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-home mr-1"></i> Dashboard
                        </a>
                        <a href="/momentum/projects" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-project-diagram mr-1"></i> Projects
                        </a>
                        <a href="/momentum/help" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-primary-500 text-primary-600 dark:text-primary-400">
                            <i class="fas fa-question-circle mr-1"></i> Help
                        </a>
                    </nav>
                    
                    <!-- Print button -->
                    <button onclick="window.print()" class="ml-4 inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-print mr-1"></i> Print Guide
                    </button>
                    
                    <!-- Theme toggle -->
                    <div class="ml-4 relative flex-shrink-0">
                        <button id="theme-toggle" class="bg-white dark:bg-gray-700 rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 p-2 hover:bg-gray-100 dark:hover:bg-gray-600" title="Toggle Theme">
                            <span class="sr-only">Toggle Theme</span>
                            <div class="h-5 w-5 flex items-center justify-center text-primary-600 dark:text-primary-300">
                                <i class="fas fa-moon dark:hidden"></i>
                                <i class="fas fa-sun hidden dark:block"></i>
                            </div>
                        </button>
                    </div>
                </div>
                
                <!-- Mobile menu button -->
                <div class="flex items-center -mr-2 md:hidden">
                    <button type="button" id="mobile-menu-button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500">
                        <span class="sr-only">Open main menu</span>
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content with Sidebar -->
    <div class="flex flex-col md:flex-row">
        <!-- Sidebar -->
        <div class="bg-white dark:bg-gray-800 w-full md:w-64 shadow md:h-screen sidebar no-print">
            <div class="p-4">
                <h2 class="text-lg font-semibold mb-4">Guide Contents</h2>
                <div class="space-y-2">
                    <a href="#introduction" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        Introduction
                    </a>
                    <a href="#understanding" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        Understanding ADHD and Projects
                    </a>
                    <a href="#framework" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        TRACE Framework
                    </a>
                    <a href="#breakdown" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        Breaking Down Projects
                    </a>
                    <a href="#time" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        Time Management
                    </a>
                    <a href="#focus" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        Maintaining Focus
                    </a>
                    <a href="#procrastination" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        Overcoming Procrastination
                    </a>
                    <a href="#changes" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        Managing Changes
                    </a>
                    <a href="#collaboration" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        Collaboration Strategies
                    </a>
                    <a href="#momentum" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        Using Momentum
                    </a>
                    <a href="#templates" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        Templates & Examples
                    </a>
                </div>
                
                <div class="mt-8">
                    <button onclick="window.print()" class="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-print mr-2"></i> Print Guide
                    </button>
                    <a href="/momentum/docs/adhd-friendly-project-planning-guide.md" download="adhd-friendly-project-planning-guide.md" class="mt-4 w-full flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-download mr-2"></i> Download Markdown
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main content area -->
        <div class="flex-1 p-6 main-content">
            <div class="max-w-4xl mx-auto">
                <div class="flex items-center mb-8">
                    <a href="/momentum/dashboard" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 no-print">
                        <i class="fas fa-arrow-left"></i>
                        <span class="ml-1">Back to Dashboard</span>
                    </a>
                </div>
                
                <h1 class="text-3xl font-bold mb-4">ADHD-Friendly Project Planning Guide</h1>
                <p class="text-gray-600 dark:text-gray-400 mb-8">A comprehensive guide to planning and managing projects effectively with ADHD</p>
                
                <section id="introduction" class="mb-12">
                    <h2 class="text-2xl font-bold mb-4">Introduction</h2>
                    <p class="mb-4">This guide provides ADHD-friendly strategies for planning and managing projects effectively. It's designed to work with the Momentum system's project management features while addressing the specific challenges that individuals with ADHD often face when handling complex projects.</p>
                    
                    <div class="tip-box tip">
                        <h3 class="font-semibold mb-2"><i class="fas fa-lightbulb text-primary-500 mr-2"></i> Remember</h3>
                        <p>There's no one-size-fits-all approach to ADHD and project management. Experiment with different strategies to find what works best for you.</p>
                    </div>
                </section>
                
                <section id="understanding" class="mb-12">
                    <h2 class="text-2xl font-bold mb-4">Understanding ADHD and Project Management</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                            <h3 class="text-xl font-semibold mb-4">Common ADHD Challenges</h3>
                            <ul class="space-y-3 list-disc pl-5">
                                <li>
                                    <span class="font-medium">Planning Difficulties</span>
                                    <ul class="list-disc pl-5 mt-1 text-sm text-gray-600 dark:text-gray-400">
                                        <li>Trouble breaking down large projects</li>
                                        <li>Difficulty estimating time requirements</li>
                                        <li>Challenges with task sequencing</li>
                                    </ul>
                                </li>
                                <li>
                                    <span class="font-medium">Executive Function Obstacles</span>
                                    <ul class="list-disc pl-5 mt-1 text-sm text-gray-600 dark:text-gray-400">
                                        <li>Working memory limitations</li>
                                        <li>Task initiation struggles</li>
                                        <li>Difficulty with self-monitoring</li>
                                    </ul>
                                </li>
                                <li>
                                    <span class="font-medium">Focus and Attention Challenges</span>
                                    <ul class="list-disc pl-5 mt-1 text-sm text-gray-600 dark:text-gray-400">
                                        <li>Maintaining consistent focus</li>
                                        <li>Filtering out distractions</li>
                                        <li>Task switching difficulties</li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                            <h3 class="text-xl font-semibold mb-4">ADHD Strengths</h3>
                            <ul class="space-y-3 list-disc pl-5">
                                <li>
                                    <span class="font-medium">Creative Problem-Solving</span>
                                    <ul class="list-disc pl-5 mt-1 text-sm text-gray-600 dark:text-gray-400">
                                        <li>Innovative thinking and unique solutions</li>
                                        <li>Ability to make unexpected connections</li>
                                        <li>Out-of-the-box approaches</li>
                                    </ul>
                                </li>
                                <li>
                                    <span class="font-medium">Hyperfocus Capabilities</span>
                                    <ul class="list-disc pl-5 mt-1 text-sm text-gray-600 dark:text-gray-400">
                                        <li>Intense concentration when engaged</li>
                                        <li>Ability to work for extended periods</li>
                                        <li>Deep diving into complex problems</li>
                                    </ul>
                                </li>
                                <li>
                                    <span class="font-medium">Adaptability</span>
                                    <ul class="list-disc pl-5 mt-1 text-sm text-gray-600 dark:text-gray-400">
                                        <li>Quick pivoting when circumstances change</li>
                                        <li>Comfort with ambiguity and uncertainty</li>
                                        <li>Resilience in the face of setbacks</li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>
                </section>
                
                <div class="mt-12 text-center">
                    <p class="text-gray-600 dark:text-gray-400 mb-4">For the complete guide with all sections and detailed strategies, download the full version.</p>
                    <a href="/momentum/docs/adhd-friendly-project-planning-guide.md" download="adhd-friendly-project-planning-guide.md" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-download mr-2"></i> Download Full Guide
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Toggle Dark Mode Button (Mobile) -->
    <div class="fixed bottom-4 right-4 md:hidden no-print">
        <button id="theme-toggle-mobile" class="bg-gray-200 dark:bg-gray-700 p-3 rounded-full shadow-lg">
            <i class="fas fa-moon dark:hidden"></i>
            <i class="fas fa-sun hidden dark:block"></i>
        </button>
    </div>
    
    <script>
        // Toggle dark mode
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
            localStorage.setItem('darkMode', document.documentElement.classList.contains('dark'));
        }
        
        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.documentElement.classList.add('dark');
        }
        
        // Add event listeners
        document.getElementById('theme-toggle').addEventListener('click', toggleDarkMode);
        document.getElementById('theme-toggle-mobile').addEventListener('click', toggleDarkMode);
        
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('hidden');
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
