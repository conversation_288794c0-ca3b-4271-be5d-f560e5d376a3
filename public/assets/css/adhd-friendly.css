/**
 * ADHD-Friendly UI Enhancements
 * 
 * This stylesheet provides ADHD-friendly UI enhancements for the Momentum application.
 * It includes color coding, visual cues, focus states, and other accessibility improvements.
 */

/* ===== Focus States ===== */
.adhd-focus-border:focus,
.adhd-focus-border:focus-within {
    outline: 3px solid #4f46e5 !important;
    outline-offset: 2px !important;
    transition: outline-offset 0.1s ease-in-out !important;
}

.adhd-focus-glow:focus,
.adhd-focus-glow:focus-within {
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.4) !important;
    transition: box-shadow 0.1s ease-in-out !important;
}

/* ===== Color Coding for Priority Levels ===== */
.priority-high {
    border-left: 4px solid #ef4444 !important;
}

.priority-normal {
    border-left: 4px solid #3b82f6 !important;
}

.priority-low {
    border-left: 4px solid #6b7280 !important;
}

/* ===== Status Indicators ===== */
.status-overdue {
    background-color: #fee2e2 !important;
    border: 1px solid #fecaca !important;
    color: #b91c1c !important;
}

.status-due-today {
    background-color: #fef3c7 !important;
    border: 1px solid #fde68a !important;
    color: #92400e !important;
}

.status-upcoming {
    background-color: #dbeafe !important;
    border: 1px solid #bfdbfe !important;
    color: #1e40af !important;
}

.status-completed {
    background-color: #d1fae5 !important;
    border: 1px solid #a7f3d0 !important;
    color: #065f46 !important;
}

/* Dark mode variants */
.dark .status-overdue {
    background-color: rgba(239, 68, 68, 0.2) !important;
    border: 1px solid rgba(239, 68, 68, 0.3) !important;
    color: #fca5a5 !important;
}

.dark .status-due-today {
    background-color: rgba(245, 158, 11, 0.2) !important;
    border: 1px solid rgba(245, 158, 11, 0.3) !important;
    color: #fcd34d !important;
}

.dark .status-upcoming {
    background-color: rgba(59, 130, 246, 0.2) !important;
    border: 1px solid rgba(59, 130, 246, 0.3) !important;
    color: #93c5fd !important;
}

.dark .status-completed {
    background-color: rgba(16, 185, 129, 0.2) !important;
    border: 1px solid rgba(16, 185, 129, 0.3) !important;
    color: #6ee7b7 !important;
}

/* ===== Progress Indicators ===== */
.progress-container {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 9999px;
    overflow: hidden;
}

.dark .progress-container {
    background-color: #374151;
}

.progress-bar {
    height: 100%;
    border-radius: 9999px;
    transition: width 0.5s ease-in-out;
}

.progress-bar-success {
    background-color: #10b981;
}

.progress-bar-warning {
    background-color: #f59e0b;
}

.progress-bar-danger {
    background-color: #ef4444;
}

.progress-bar-info {
    background-color: #3b82f6;
}

/* ===== Visual Cues ===== */
.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(79, 70, 229, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(79, 70, 229, 0);
    }
}

/* ===== Category Color Coding ===== */
.category-medication {
    border-left: 4px solid #ec4899 !important;
}

.category-vaccination {
    border-left: 4px solid #8b5cf6 !important;
}

.category-vet-visit {
    border-left: 4px solid #06b6d4 !important;
}

.category-grooming {
    border-left: 4px solid #14b8a6 !important;
}

.category-training {
    border-left: 4px solid #f59e0b !important;
}

.category-feeding {
    border-left: 4px solid #84cc16 !important;
}

.category-exercise {
    border-left: 4px solid #ef4444 !important;
}

.category-other {
    border-left: 4px solid #6b7280 !important;
}

/* ===== Icon Color Coding ===== */
.icon-medication {
    color: #ec4899 !important;
}

.icon-vaccination {
    color: #8b5cf6 !important;
}

.icon-vet-visit {
    color: #06b6d4 !important;
}

.icon-grooming {
    color: #14b8a6 !important;
}

.icon-training {
    color: #f59e0b !important;
}

.icon-feeding {
    color: #84cc16 !important;
}

.icon-exercise {
    color: #ef4444 !important;
}

.icon-other {
    color: #6b7280 !important;
}

/* ===== Reduced Motion ===== */
@media (prefers-reduced-motion: reduce) {
    .progress-bar,
    .adhd-focus-border:focus,
    .adhd-focus-border:focus-within,
    .adhd-focus-glow:focus,
    .adhd-focus-glow:focus-within {
        transition: none !important;
    }
    
    .pulse-animation {
        animation: none !important;
    }
}

/* ===== ADHD-Friendly Card Styles ===== */
.adhd-card {
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    overflow: hidden;
}

.adhd-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark .adhd-card {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.12);
}

.dark .adhd-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
}

/* ===== Keyboard Shortcut Indicators ===== */
.keyboard-shortcut {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 1.5rem;
    height: 1.5rem;
    padding: 0 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
    color: #4b5563;
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.dark .keyboard-shortcut {
    color: #d1d5db;
    background-color: #374151;
    border-color: #4b5563;
}

/* ===== Focus Mode ===== */
.focus-mode-active .focus-mode-hide {
    opacity: 0.3;
    transition: opacity 0.3s ease-in-out;
}

.focus-mode-active .focus-mode-hide:hover {
    opacity: 1;
}

.focus-mode-active .focus-mode-show {
    box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.4);
}

/* ===== Reduced Visual Noise ===== */
.reduced-noise {
    background-color: rgba(255, 255, 255, 0.97);
    backdrop-filter: blur(4px);
}

.dark .reduced-noise {
    background-color: rgba(31, 41, 55, 0.97);
}

/* ===== Accessibility Improvements ===== */
.high-contrast-text {
    color: #000000 !important;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
}

.dark .high-contrast-text {
    color: #ffffff !important;
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
}

.increased-spacing {
    letter-spacing: 0.025em;
    line-height: 1.6;
}

/* ===== Responsive Adjustments ===== */
@media (max-width: 640px) {
    .adhd-card {
        border-radius: 0.375rem;
    }
    
    .progress-container {
        height: 6px;
    }
}
