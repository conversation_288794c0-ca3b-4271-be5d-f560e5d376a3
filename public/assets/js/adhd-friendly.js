/**
 * ADHD-Friendly UI Enhancements
 * 
 * This script provides ADHD-friendly UI enhancements for the Momentum application.
 * It includes focus mode, visual cues, keyboard shortcuts, and other accessibility improvements.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize ADHD-friendly enhancements
    initColorCoding();
    initProgressBars();
    initFocusMode();
    initKeyboardShortcuts();
    initVisualCues();
    initReducedMotion();
});

/**
 * Initialize color coding for different elements
 */
function initColorCoding() {
    // Apply priority color coding
    document.querySelectorAll('[data-priority]').forEach(element => {
        const priority = element.getAttribute('data-priority');
        element.classList.add(`priority-${priority}`);
    });
    
    // Apply category color coding
    document.querySelectorAll('[data-category]').forEach(element => {
        const category = element.getAttribute('data-category');
        element.classList.add(`category-${category}`);
    });
    
    // Apply status color coding
    document.querySelectorAll('[data-status]').forEach(element => {
        const status = element.getAttribute('data-status');
        element.classList.add(`status-${status}`);
    });
    
    // Apply icon color coding
    document.querySelectorAll('[data-icon-category]').forEach(element => {
        const category = element.getAttribute('data-icon-category');
        element.classList.add(`icon-${category}`);
    });
}

/**
 * Initialize progress bars
 */
function initProgressBars() {
    document.querySelectorAll('.progress-container').forEach(container => {
        const progressBar = container.querySelector('.progress-bar');
        const percentage = progressBar.getAttribute('data-percentage');
        const type = progressBar.getAttribute('data-type') || 'info';
        
        // Set progress bar width
        progressBar.style.width = `${percentage}%`;
        
        // Apply appropriate color class
        progressBar.classList.add(`progress-bar-${type}`);
        
        // Add aria attributes for accessibility
        container.setAttribute('role', 'progressbar');
        container.setAttribute('aria-valuenow', percentage);
        container.setAttribute('aria-valuemin', '0');
        container.setAttribute('aria-valuemax', '100');
    });
}

/**
 * Initialize focus mode
 */
function initFocusMode() {
    const focusModeToggle = document.getElementById('focus-mode-toggle');
    if (!focusModeToggle) return;
    
    focusModeToggle.addEventListener('click', function() {
        document.body.classList.toggle('focus-mode-active');
        
        // Update toggle button text
        const isActive = document.body.classList.contains('focus-mode-active');
        focusModeToggle.textContent = isActive ? 'Exit Focus Mode' : 'Enter Focus Mode';
        
        // Store preference in localStorage
        localStorage.setItem('focusModeActive', isActive);
    });
    
    // Check if focus mode was previously enabled
    if (localStorage.getItem('focusModeActive') === 'true') {
        document.body.classList.add('focus-mode-active');
        focusModeToggle.textContent = 'Exit Focus Mode';
    }
}

/**
 * Initialize keyboard shortcuts
 */
function initKeyboardShortcuts() {
    // Add keyboard shortcut indicators
    document.querySelectorAll('[data-keyboard-shortcut]').forEach(element => {
        const shortcut = element.getAttribute('data-keyboard-shortcut');
        const shortcutDisplay = document.createElement('span');
        shortcutDisplay.className = 'keyboard-shortcut ml-2';
        shortcutDisplay.textContent = shortcut;
        element.appendChild(shortcutDisplay);
    });
    
    // Handle keyboard shortcuts
    document.addEventListener('keydown', function(event) {
        // Escape key to exit focus mode
        if (event.key === 'Escape' && document.body.classList.contains('focus-mode-active')) {
            document.body.classList.remove('focus-mode-active');
            const focusModeToggle = document.getElementById('focus-mode-toggle');
            if (focusModeToggle) {
                focusModeToggle.textContent = 'Enter Focus Mode';
            }
            localStorage.setItem('focusModeActive', false);
        }
        
        // Backspace key to go back (when not in an input field)
        if (event.key === 'Backspace' && 
            !['INPUT', 'TEXTAREA', 'SELECT'].includes(document.activeElement.tagName)) {
            event.preventDefault();
            window.history.back();
        }
        
        // Add more keyboard shortcuts as needed
    });
}

/**
 * Initialize visual cues
 */
function initVisualCues() {
    // Add pulse animation to important elements
    document.querySelectorAll('[data-important="true"]').forEach(element => {
        element.classList.add('pulse-animation');
    });
    
    // Add focus styles to interactive elements
    document.querySelectorAll('button, a, input, select, textarea').forEach(element => {
        element.classList.add('adhd-focus-glow');
    });
    
    // Add card styles to appropriate containers
    document.querySelectorAll('.card, .box, .container').forEach(element => {
        if (!element.classList.contains('adhd-card') && 
            !element.classList.contains('no-adhd-card')) {
            element.classList.add('adhd-card');
        }
    });
}

/**
 * Initialize reduced motion for users who prefer it
 */
function initReducedMotion() {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    if (prefersReducedMotion) {
        // Remove animations and transitions
        document.querySelectorAll('.pulse-animation').forEach(element => {
            element.classList.remove('pulse-animation');
        });
    }
}

/**
 * Update progress indicators dynamically
 * @param {string} elementId - The ID of the progress container
 * @param {number} percentage - The new percentage value
 * @param {string} type - The type of progress bar (success, warning, danger, info)
 */
function updateProgress(elementId, percentage, type = null) {
    const container = document.getElementById(elementId);
    if (!container) return;
    
    const progressBar = container.querySelector('.progress-bar');
    if (!progressBar) return;
    
    // Update progress bar width
    progressBar.style.width = `${percentage}%`;
    
    // Update aria attributes
    container.setAttribute('aria-valuenow', percentage);
    
    // Update progress bar type if provided
    if (type) {
        progressBar.className = 'progress-bar';
        progressBar.classList.add(`progress-bar-${type}`);
    }
}

/**
 * Toggle focus mode on a specific element
 * @param {string} elementId - The ID of the element to focus on
 */
function toggleElementFocus(elementId) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    // Remove focus from all other elements
    document.querySelectorAll('.focus-mode-show').forEach(el => {
        el.classList.remove('focus-mode-show');
    });
    
    // Add focus to the specified element
    element.classList.add('focus-mode-show');
    
    // Scroll element into view
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

/**
 * Apply color coding to an element based on its value
 * @param {string} elementId - The ID of the element
 * @param {string} type - The type of color coding (priority, category, status)
 * @param {string} value - The value to apply
 */
function applyColorCoding(elementId, type, value) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    // Remove existing color coding classes
    const classPrefix = `${type}-`;
    element.classList.forEach(className => {
        if (className.startsWith(classPrefix)) {
            element.classList.remove(className);
        }
    });
    
    // Add new color coding class
    element.classList.add(`${classPrefix}${value}`);
}
