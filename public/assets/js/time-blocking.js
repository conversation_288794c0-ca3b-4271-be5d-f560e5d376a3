/**
 * Time Blocking JavaScript
 * Handles time block interactions, drag-and-drop, resize, and AJAX operations
 */

document.addEventListener('DOMContentLoaded', function() {
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Only trigger shortcuts when not in an input field
        if (e.target.matches('input, textarea, [contenteditable="true"]')) {
            return;
        }

        // 'N' key - Create new time block
        if (e.key === 'n' || e.key === 'N') {
            e.preventDefault();
            document.getElementById('createTimeBlockBtn').click();
        }

        // 'Escape' key - Close modal
        if (e.key === 'Escape' && !document.getElementById('timeBlockModal').classList.contains('hidden')) {
            e.preventDefault();
            document.getElementById('cancelBtn').click();
        }

        // 'D' key - Day view
        if (e.key === 'd' || e.key === 'D') {
            e.preventDefault();
            window.location.href = '?view=day&date=' + (new URLSearchParams(window.location.search).get('date') || new Date().toISOString().split('T')[0]);
        }

        // 'W' key - Week view
        if (e.key === 'w' || e.key === 'W') {
            e.preventDefault();
            window.location.href = '?view=week&date=' + (new URLSearchParams(window.location.search).get('date') || new Date().toISOString().split('T')[0]);
        }

        // 'M' key - Month view
        if (e.key === 'm' || e.key === 'M') {
            e.preventDefault();
            window.location.href = '?view=month&date=' + (new URLSearchParams(window.location.search).get('date') || new Date().toISOString().split('T')[0]);
        }

        // 'T' key - Today
        if (e.key === 't' || e.key === 'T') {
            e.preventDefault();
            window.location.href = '?view=' + (new URLSearchParams(window.location.search).get('view') || 'week') + '&date=' + new Date().toISOString().split('T')[0];
        }

        // Left arrow - Previous period
        if (e.key === 'ArrowLeft') {
            e.preventDefault();
            document.querySelector('.btn i.fa-chevron-left').closest('a').click();
        }

        // Right arrow - Next period
        if (e.key === 'ArrowRight') {
            e.preventDefault();
            document.querySelector('.btn i.fa-chevron-right').closest('a').click();
        }
    });
    // DOM Elements
    const modal = document.getElementById('timeBlockModal');
    const form = document.getElementById('timeBlockForm');
    const createBtn = document.getElementById('createTimeBlockBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const deleteBtn = document.getElementById('deleteBtn');
    const isRecurringCheckbox = document.getElementById('is_recurring');
    const recurrenceOptions = document.getElementById('recurrenceOptions');
    const showShortcutsBtn = document.getElementById('show-shortcuts');
    const closeShortcutsBtn = document.getElementById('close-shortcuts');
    const shortcutsHelp = document.getElementById('shortcuts-help');

    // Toggle shortcuts help
    if (showShortcutsBtn && shortcutsHelp) {
        showShortcutsBtn.addEventListener('click', function() {
            shortcutsHelp.classList.remove('hidden');
        });
    }

    if (closeShortcutsBtn && shortcutsHelp) {
        closeShortcutsBtn.addEventListener('click', function() {
            shortcutsHelp.classList.add('hidden');
        });
    }

    // Show/hide recurrence options based on checkbox
    if (isRecurringCheckbox) {
        isRecurringCheckbox.addEventListener('change', function() {
            recurrenceOptions.classList.toggle('hidden', !this.checked);
        });
    }

    // Open modal for creating a new time block
    if (createBtn) {
        createBtn.addEventListener('click', function() {
            // Reset form
            form.reset();
            form.action = '/momentum/productivity/create-time-block';
            document.getElementById('modal-title').textContent = 'Create Time Block';
            document.getElementById('timeBlockId').value = '';
            deleteBtn.classList.add('hidden');

            // Set default dates to today
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('start_date').value = today;
            document.getElementById('end_date').value = today;

            // Show modal
            modal.classList.remove('hidden');
        });
    }

    // Close modal
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            modal.classList.add('hidden');
        });
    }

    // Handle clicking on time blocks to edit
    document.querySelectorAll('[data-block-id]').forEach(block => {
        block.addEventListener('click', function(e) {
            // Don't open edit modal if we're dragging
            if (isDragging) return;

            const blockId = this.getAttribute('data-block-id');

            // Fetch time block data
            fetch(`/momentum/productivity/time-block/${blockId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const timeBlock = data.timeBlock;

                    // Set form action
                    form.action = `/momentum/productivity/update-time-block/${blockId}`;
                    document.getElementById('modal-title').textContent = 'Edit Time Block';
                    document.getElementById('timeBlockId').value = blockId;

                    // Fill form fields
                    document.getElementById('title').value = timeBlock.title;
                    document.getElementById('description').value = timeBlock.description || '';

                    // Parse dates and times
                    const startDateTime = new Date(timeBlock.start_time);
                    const endDateTime = new Date(timeBlock.end_time);

                    document.getElementById('start_date').value = startDateTime.toISOString().split('T')[0];
                    document.getElementById('start_time').value = startDateTime.toTimeString().slice(0, 5);
                    document.getElementById('end_date').value = endDateTime.toISOString().split('T')[0];
                    document.getElementById('end_time').value = endDateTime.toTimeString().slice(0, 5);

                    document.getElementById('category').value = timeBlock.category || '';
                    document.getElementById('color').value = timeBlock.color || '#4F46E5';

                    // Set task if any
                    const taskSelect = document.getElementById('task_id');
                    if (taskSelect) {
                        taskSelect.value = timeBlock.task_id || '';
                    }

                    // Set recurring options
                    document.getElementById('is_recurring').checked = timeBlock.is_recurring == 1;
                    recurrenceOptions.classList.toggle('hidden', timeBlock.is_recurring != 1);

                    if (timeBlock.is_recurring == 1 && timeBlock.recurrence_pattern) {
                        document.getElementById('recurrence_pattern').value = timeBlock.recurrence_pattern;
                    }

                    // Show delete button
                    deleteBtn.classList.remove('hidden');

                    // Show modal
                    modal.classList.remove('hidden');
                } else {
                    showNotification('Error loading time block data', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Failed to load time block data', 'error');
            });
        });
    });

    // Handle delete button
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const blockId = document.getElementById('timeBlockId').value;
            if (confirm('Are you sure you want to delete this time block?')) {
                window.location.href = `/momentum/productivity/delete-time-block/${blockId}`;
            }
        });
    }

    // Drag and drop functionality
    let isDragging = false;
    let isResizing = false;

    // Add resize handles to all time blocks
    function addResizeHandlesToBlocks() {
        // Get all time blocks in day and week views
        const timeBlocks = document.querySelectorAll('.day-view .time-block, .week-view-grid .time-block');

        timeBlocks.forEach(block => {
            // Check if the block already has a resize handle
            if (!block.querySelector('.resize-handle')) {
                // Create resize handle
                const resizeHandle = document.createElement('div');
                resizeHandle.className = 'resize-handle';
                resizeHandle.innerHTML = '<i class="fas fa-grip-lines"></i>';
                resizeHandle.style.position = 'absolute';
                resizeHandle.style.bottom = '0';
                resizeHandle.style.left = '0';
                resizeHandle.style.right = '0';
                resizeHandle.style.height = '10px';
                resizeHandle.style.cursor = 'ns-resize';
                resizeHandle.style.textAlign = 'center';
                resizeHandle.style.fontSize = '10px';
                resizeHandle.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
                resizeHandle.style.borderRadius = '0 0 4px 4px';

                // Add hover effect
                resizeHandle.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(255, 255, 255, 0.4)';
                });

                resizeHandle.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
                });

                // Add resize handle to block
                block.appendChild(resizeHandle);

                // Initialize resize functionality
                initializeResize(block, resizeHandle);
            }
        });
    }

    // Call this function when the page loads
    addResizeHandlesToBlocks();

    // Initialize resize functionality for a time block
    function initializeResize(block, handle) {
        handle.addEventListener('mousedown', function(e) {
            e.preventDefault();
            e.stopPropagation(); // Prevent triggering drag

            // Start resizing
            isResizing = true;
            const startY = e.clientY;
            const startHeight = parseFloat(block.style.height || '0');
            const blockId = block.getAttribute('data-block-id');

            // Get parent container dimensions
            const parent = block.parentElement;
            const parentHeight = parent.clientHeight;

            // Add resizing class
            block.classList.add('resizing');

            // Handle mouse move
            function handleMouseMove(e) {
                if (!isResizing) return;

                const deltaY = e.clientY - startY;
                // Convert pixel movement to percentage of parent height
                const deltaPercent = (deltaY / parentHeight) * 100;

                // Get the computed style to ensure we get the actual value
                const computedStyle = window.getComputedStyle(block);
                const currentHeight = parseFloat(computedStyle.height) / parentHeight * 100;

                // Calculate new height with constraints
                // Minimum 2.5% (15 minutes in a 16-hour day)
                // Maximum 50% (8 hours in a 16-hour day)
                const newHeight = Math.max(2.5, Math.min(50, currentHeight + deltaPercent));

                // Update block height
                block.style.height = newHeight + '%';

                // Log for debugging
                console.log(`Resizing to height: ${newHeight.toFixed(1)}%`);

                // Prevent text selection during resize
                e.preventDefault();
            }

            // Handle mouse up
            function handleMouseUp() {
                if (!isResizing) return;

                isResizing = false;
                block.classList.remove('resizing');

                // Calculate new duration based on height percentage
                // Get the computed style to ensure we get the actual value
                const computedStyle = window.getComputedStyle(block);
                const heightPercent = parseFloat(computedStyle.height) / parent.clientHeight * 100;

                // 16 hours * 60 minutes = 960 minutes total in the day view
                const totalMinutesInDay = 16 * 60;
                // Calculate duration based on percentage of the day
                let durationMinutes = Math.round((heightPercent / 100) * totalMinutesInDay);

                // Round to nearest 5 minutes
                durationMinutes = Math.round(durationMinutes / 5) * 5;

                // Ensure minimum duration of 15 minutes and maximum of 8 hours (480 minutes)
                durationMinutes = Math.max(15, Math.min(480, durationMinutes));

                console.log(`Resized block to ${durationMinutes} minutes (${heightPercent.toFixed(1)}%)`);

                // Update time block via AJAX
                resizeTimeBlock(blockId, durationMinutes);

                // Remove event listeners
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
            }

            // Add event listeners
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
        });
    }

    // Initialize drag and drop for day view
    const dayViewBlocks = document.querySelectorAll('.day-view .time-block');
    if (dayViewBlocks.length > 0) {
        dayViewBlocks.forEach(block => {
            block.addEventListener('mousedown', function(e) {
                // Don't start dragging if we're clicking on the resize handle
                if (e.target.closest('.resize-handle') || isResizing) {
                    return;
                }

                // Start dragging
                isDragging = true;
                const startY = e.clientY;

                // Get the computed style to ensure we get the actual value
                const computedStyle = window.getComputedStyle(block);
                const parent = block.parentElement;
                const parentHeight = parent.clientHeight;
                const startTop = parseFloat(computedStyle.top) / parentHeight * 100;

                // Store original position for reference
                const originalTop = startTop;

                // Add dragging class
                block.classList.add('dragging');

                console.log(`Starting drag from top: ${startTop}%`);

                // Handle mouse move
                function handleMouseMove(e) {
                    if (!isDragging) return;

                    const deltaY = e.clientY - startY;
                    // Convert pixel movement to percentage of parent height
                    const deltaPercent = (deltaY / parentHeight) * 100;
                    const newTop = startTop + deltaPercent;

                    // Get the block height from computed style
                    const blockComputedStyle = window.getComputedStyle(block);
                    const blockHeight = parseFloat(blockComputedStyle.height) / parentHeight * 100;

                    // Constrain to parent container
                    const maxTop = 100 - blockHeight;

                    // Set the new top position
                    const constrainedTop = Math.max(0, Math.min(newTop, maxTop));
                    block.style.top = constrainedTop + '%';

                    console.log(`Dragging to top: ${constrainedTop.toFixed(1)}%, height: ${blockHeight.toFixed(1)}%`);

                    // Prevent text selection during drag
                    e.preventDefault();
                }

                // Handle mouse up
                function handleMouseUp() {
                    if (!isDragging) return;

                    isDragging = false;
                    block.classList.remove('dragging');

                    // Calculate new time based on position
                    const topPercent = parseFloat(block.style.top) / 100;

                    // 16 hours from 6am to 10pm
                    const hourOffset = Math.round(topPercent * 16);

                    // Calculate minutes (round to nearest 15 minutes)
                    const minutesFraction = (topPercent * 16) - Math.floor(topPercent * 16);
                    const minutes = Math.round(minutesFraction * 60 / 15) * 15;

                    // Get block ID
                    const blockId = block.getAttribute('data-block-id');

                    // Base hour is 6am
                    const newHour = 6 + Math.floor(hourOffset);

                    // Format the time for the API
                    const formattedHour = {
                        hour: newHour,
                        minutes: minutes
                    };

                    // Update time block via AJAX
                    updateTimeBlockTime(blockId, formattedHour);

                    // Remove event listeners
                    document.removeEventListener('mousemove', handleMouseMove);
                    document.removeEventListener('mouseup', handleMouseUp);

                    console.log(`Moved block to ${newHour}:${minutes < 10 ? '0' + minutes : minutes}`);
                }

                // Add event listeners
                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
            });
        });
    }

    // Function to update time block time (drag and drop)
    function updateTimeBlockTime(blockId, timeInfo) {
        fetch(`/momentum/productivity/update-time-block-time/${blockId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(timeInfo)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showNotification('Time block updated successfully', 'success');
                // Reload the page after a short delay to show the updated time blocks
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification('Failed to update time block', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Failed to update time block. Please try again.', 'error');
            // Reload the page after a short delay to reset the UI
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        });
    }

    // Function to resize time block
    function resizeTimeBlock(blockId, durationMinutes) {
        fetch(`/momentum/productivity/resize-time-block/${blockId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ duration: durationMinutes })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showNotification('Time block resized successfully', 'success');
                // Reload the page after a short delay to show the updated time blocks
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Failed to resize time block', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Failed to resize time block. Please try again.', 'error');
            // Reload the page after a short delay to reset the UI
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        });
    }

    // Function to show notification
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `fixed bottom-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-100 text-green-800 border-l-4 border-green-500' :
            'bg-red-100 text-red-800 border-l-4 border-red-500'
        }`;
        notification.innerHTML = message;
        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('opacity-0', 'transition-opacity', 'duration-500');
            setTimeout(() => notification.remove(), 500);
        }, 3000);
    }
});
