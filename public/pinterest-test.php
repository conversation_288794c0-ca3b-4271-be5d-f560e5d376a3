<?php
/**
 * Pinterest Test Page
 * 
 * This page tests the Pinterest Clone CSS and JavaScript.
 */

// Include necessary files
require_once __DIR__ . '/../src/utils/View.php';
require_once __DIR__ . '/../src/utils/AssetManager.php';

// Start output buffering
ob_start();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pinterest Clone Test</title>
    
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Pinterest Clone CSS -->
    <link rel="stylesheet" href="/momentum/css/pinterest-clone.css">
    
    <style>
        /* Additional test styles */
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border-radius: 0.5rem;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .test-description {
            margin-bottom: 1rem;
            color: #666;
        }
        
        .test-button {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            background-color: #e60023;
            color: white;
            border-radius: 0.25rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .test-button:hover {
            background-color: #ad081b;
        }
        
        .test-button i {
            margin-right: 0.5rem;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="test-container">
        <h1 class="text-3xl font-bold mb-6">Pinterest Clone Test Page</h1>
        
        <div class="test-section">
            <div class="test-title">CSS Test</div>
            <div class="test-description">
                This section tests if the Pinterest Clone CSS is properly loaded.
            </div>
            
            <div class="flex flex-wrap gap-4 mt-4">
                <button class="pinterest-btn">
                    <i class="fas fa-search mr-2"></i> Pinterest Button
                </button>
                
                <button class="pinterest-btn-outline">
                    <i class="fas fa-heart mr-2"></i> Outline Button
                </button>
                
                <span class="pinterest-badge">Regular Badge</span>
                
                <span class="pinterest-badge pinterest-badge-red">Red Badge</span>
            </div>
            
            <div class="mt-6">
                <input type="text" class="pinterest-search" placeholder="Pinterest search input...">
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">JavaScript Test</div>
            <div class="test-description">
                This section tests if the Pinterest Clone JavaScript is properly loaded.
            </div>
            
            <div class="mt-4">
                <a href="/momentum/clone/pinterest/scraper" id="test-scraper-link" class="test-button">
                    <i class="fas fa-search"></i> Test New Scrape Button
                </a>
                
                <div class="mt-4" id="js-status">JavaScript status: Checking...</div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">Navigation Test</div>
            <div class="test-description">
                This section provides links to test navigation.
            </div>
            
            <div class="flex flex-wrap gap-4 mt-4">
                <a href="/momentum/clone/pinterest" class="test-button">
                    <i class="fas fa-home"></i> Pinterest Dashboard
                </a>
                
                <a href="/momentum/clone/pinterest/scraper" class="test-button">
                    <i class="fas fa-search"></i> Pinterest Scraper
                </a>
                
                <a href="/momentum/clone/pinterest/trends" class="test-button">
                    <i class="fas fa-chart-line"></i> Pinterest Trends
                </a>
                
                <a href="/momentum/clone/pinterest/analysis" class="test-button">
                    <i class="fas fa-chart-pie"></i> Visual Analysis
                </a>
            </div>
        </div>
    </div>
    
    <!-- Pinterest Direct Fix Script -->
    <script src="/momentum/js/pinterest-direct-fix.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if the Pinterest Direct Fix script is loaded
            if (typeof fixPinterestLinks === 'function') {
                document.getElementById('js-status').textContent = 'JavaScript status: Pinterest Direct Fix script loaded successfully!';
                document.getElementById('js-status').style.color = 'green';
            } else {
                document.getElementById('js-status').textContent = 'JavaScript status: Pinterest Direct Fix script NOT loaded!';
                document.getElementById('js-status').style.color = 'red';
            }
            
            // Add a click handler to the test scraper link
            const scraperLink = document.getElementById('test-scraper-link');
            if (scraperLink) {
                scraperLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Test scraper link clicked, redirecting to:', this.href);
                    window.location.href = this.href;
                });
            }
        });
    </script>
</body>
</html>
<?php
// End output buffering and display the page
echo ob_get_clean();
?>
