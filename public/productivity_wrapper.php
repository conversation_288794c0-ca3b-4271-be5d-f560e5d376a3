<?php
/**
 * Productivity Wrapper
 * 
 * This file wraps existing productivity pages with the unified navigation.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple session handling
session_start();

// Get the requested page
$page = $_GET['page'] ?? 'dashboard';

// Map of valid pages to their actual URLs
$pageUrls = [
    'dashboard' => '/momentum/productivity_dashboard.php',
    'focus-timer' => '/momentum/productivity/focus-timer',
    'focus-mode' => '/momentum/productivity/focus-mode',
    'time-blocking' => '/momentum/productivity/time-blocking',
    'energy-tracking' => '/momentum/productivity/energy-tracking',
    'task-batching' => '/momentum/productivity/task-batching',
    'batch-templates' => '/momentum/productivity/batch-templates',
    'distraction-journal' => '#'
];

// Validate the page
if (!isset($pageUrls[$page])) {
    $page = 'dashboard';
}

// Get the target URL
$targetUrl = $pageUrls[$page];

// Function to get content from URL
function getUrlContent($url) {
    // For local URLs, use file_get_contents with a stream context
    if (strpos($url, '/momentum/') === 0) {
        $localPath = $_SERVER['DOCUMENT_ROOT'] . $url;
        if (file_exists($localPath)) {
            return file_get_contents($localPath);
        }
        
        // If file doesn't exist, try to get it via HTTP
        $url = 'http://' . $_SERVER['HTTP_HOST'] . $url;
    }
    
    // Create a stream context to handle redirects
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => "Accept-language: en\r\n" .
                        "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\r\n"
        ]
    ]);
    
    // Get the content
    $content = @file_get_contents($url, false, $context);
    
    if ($content === false) {
        return '<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <p><strong>Error:</strong> Unable to load content from ' . htmlspecialchars($url) . '</p>
        </div>';
    }
    
    return $content;
}

// Try to get the content
$pageContent = '';
if ($page === 'dashboard') {
    // For the dashboard, we'll include the file directly
    ob_start();
    include $_SERVER['DOCUMENT_ROOT'] . '/momentum/productivity_dashboard.php';
    $pageContent = ob_get_clean();
} else {
    // For other pages, we'll try to get the content via HTTP
    $pageContent = getUrlContent($targetUrl);
    
    // Extract the body content
    if (preg_match('/<body[^>]*>(.*?)<\/body>/is', $pageContent, $matches)) {
        $pageContent = $matches[1];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Productivity Tools - Momentum</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .tool-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .dark .tool-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-7xl mx-auto">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
                <div>
                    <h1 class="text-3xl font-bold">Productivity Tools</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">ADHD-friendly tools to enhance focus and productivity</p>
                </div>
                <div class="mt-4 md:mt-0 flex items-center space-x-4">
                    <a href="/momentum/dashboard" class="inline-flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-lg text-gray-700 dark:text-gray-300">
                        <i class="fas fa-home mr-2"></i> Dashboard
                    </a>
                    <button id="darkModeToggle" class="p-2 rounded-full bg-gray-200 dark:bg-gray-700">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:block"></i>
                    </button>
                </div>
            </div>
            
            <!-- Productivity Navigation -->
            <?php include $_SERVER['DOCUMENT_ROOT'] . '/momentum/productivity_nav.php'; ?>
            
            <!-- Page Content -->
            <div id="page-content">
                <?php echo $pageContent; ?>
            </div>
        </div>
    </div>
    
    <script>
        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        
        // Check for dark mode preference
        if (localStorage.getItem('darkMode') === 'enabled' || 
            (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches && 
             localStorage.getItem('darkMode') !== 'disabled')) {
            document.documentElement.classList.add('dark');
        }
        
        // Toggle dark mode
        darkModeToggle.addEventListener('click', function() {
            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('darkMode', 'disabled');
            } else {
                document.documentElement.classList.add('dark');
                localStorage.setItem('darkMode', 'enabled');
            }
        });
    </script>
</body>
</html>
