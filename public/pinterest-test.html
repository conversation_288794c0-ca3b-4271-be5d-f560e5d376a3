<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pinterest Form Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #e60023;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #d50020;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <h1>Pinterest Form Test</h1>
    <p>This page tests the Pinterest scraper form submission.</p>
    
    <form id="test-form">
        <div class="form-group">
            <label for="search_term">Search Term:</label>
            <input type="text" id="search_term" name="search_term" value="home decor" required>
        </div>
        
        <button type="submit">Submit Form</button>
    </form>
    
    <div id="result">
        <p>Results will appear here...</p>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('test-form');
            const resultDiv = document.getElementById('result');
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                resultDiv.innerHTML = '<p>Submitting form...</p>';
                
                const formData = new FormData(form);
                
                fetch('/momentum/clone/pinterest/process-scrape', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    resultDiv.innerHTML += `<p>Response status: ${response.status}</p>`;
                    return response.text();
                })
                .then(text => {
                    resultDiv.innerHTML += `<p>Response text:</p><pre>${text}</pre>`;
                    
                    try {
                        const data = JSON.parse(text);
                        resultDiv.innerHTML += `<p>Parsed JSON:</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                        
                        if (data.success && data.scrape_id) {
                            resultDiv.innerHTML += `<p>Success! Scrape ID: ${data.scrape_id}</p>`;
                            resultDiv.innerHTML += `<p><a href="/momentum/clone/pinterest/view-scrape/${data.scrape_id}">View Scrape Results</a></p>`;
                        }
                    } catch (e) {
                        resultDiv.innerHTML += `<p>Error parsing JSON: ${e.message}</p>`;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML += `<p>Error: ${error.message}</p>`;
                });
            });
        });
    </script>
</body>
</html>
