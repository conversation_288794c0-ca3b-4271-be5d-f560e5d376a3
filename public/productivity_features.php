<?php
/**
 * Productivity Features Landing Page
 *
 * This page provides direct links to the productivity features
 * including Task Batching and Batch Templates.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple session handling
session_start();

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user']);
$user = $_SESSION['user'] ?? null;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Productivity Features - Momentum</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="flex items-center justify-between mb-8">
                <h1 class="text-3xl font-bold">Momentum Productivity Features</h1>
                <?php if ($isLoggedIn): ?>
                    <div class="flex items-center">
                        <span class="mr-2 text-gray-600">Welcome, <?= htmlspecialchars($user['name']) ?></span>
                        <a href="/momentum/logout" class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm">Logout</a>
                    </div>
                <?php else: ?>
                    <a href="/momentum/login" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">Login</a>
                <?php endif; ?>
            </div>

            <?php if (!$isLoggedIn): ?>
                <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6">
                    <p>Please log in to access all features.</p>
                </div>
            <?php endif; ?>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <!-- Task Batching -->
                <div class="bg-white shadow-md rounded-lg overflow-hidden">
                    <div class="h-3 bg-indigo-500"></div>
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="bg-indigo-100 p-3 rounded-full mr-4">
                                <i class="fas fa-layer-group text-indigo-500 text-xl"></i>
                            </div>
                            <h2 class="text-xl font-semibold">Task Batching</h2>
                        </div>
                        <p class="text-gray-600 mb-4">
                            Group similar tasks together to reduce context switching and improve focus.
                            Organize tasks based on energy levels for optimal productivity.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">High Energy</span>
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Medium Energy</span>
                            <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">Low Energy</span>
                        </div>
                        <a href="/momentum/productivity/task-batching" class="inline-flex items-center px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white rounded">
                            <i class="fas fa-arrow-right mr-2"></i> Go to Task Batching
                        </a>
                    </div>
                </div>

                <!-- Batch Templates -->
                <div class="bg-white shadow-md rounded-lg overflow-hidden">
                    <div class="h-3 bg-purple-500"></div>
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="bg-purple-100 p-3 rounded-full mr-4">
                                <i class="fas fa-copy text-purple-500 text-xl"></i>
                            </div>
                            <h2 class="text-xl font-semibold">Batch Templates</h2>
                        </div>
                        <p class="text-gray-600 mb-4">
                            Create reusable templates for common task groupings. Set up recurring batches
                            that are automatically generated on a schedule.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">Daily</span>
                            <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">Weekdays</span>
                            <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">Weekly</span>
                            <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">Monthly</span>
                        </div>
                        <a href="/momentum/productivity/batch-templates" class="inline-flex items-center px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded">
                            <i class="fas fa-arrow-right mr-2"></i> Go to Batch Templates
                        </a>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <!-- Energy Tracking -->
                <div class="bg-white shadow-md rounded-lg overflow-hidden">
                    <div class="h-3 bg-yellow-500"></div>
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="bg-yellow-100 p-3 rounded-full mr-4">
                                <i class="fas fa-bolt text-yellow-500 text-xl"></i>
                            </div>
                            <h2 class="text-xl font-semibold">Energy Tracking</h2>
                        </div>
                        <p class="text-gray-600 mb-4">
                            Track your energy levels throughout the day to identify patterns and optimize your productivity.
                            Get batch recommendations based on your current energy level.
                        </p>
                        <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                            <div class="bg-yellow-500 h-2.5 rounded-full" style="width: 70%"></div>
                        </div>
                        <a href="/momentum/productivity/energy-tracking" class="inline-flex items-center px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded">
                            <i class="fas fa-arrow-right mr-2"></i> Go to Energy Tracking
                        </a>
                    </div>
                </div>

                <!-- Time Blocking -->
                <div class="bg-white shadow-md rounded-lg overflow-hidden">
                    <div class="h-3 bg-blue-500"></div>
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="bg-blue-100 p-3 rounded-full mr-4">
                                <i class="fas fa-calendar-alt text-blue-500 text-xl"></i>
                            </div>
                            <h2 class="text-xl font-semibold">Time Blocking</h2>
                        </div>
                        <p class="text-gray-600 mb-4">
                            Schedule your tasks in dedicated time blocks to structure your day and maintain focus.
                            Integrate with task batches for efficient scheduling.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Schedule</span>
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Focus</span>
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Structure</span>
                        </div>
                        <a href="/momentum/productivity/time-blocking" class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded">
                            <i class="fas fa-arrow-right mr-2"></i> Go to Time Blocking
                        </a>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <a href="/momentum/dashboard" class="inline-flex items-center px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded">
                    <i class="fas fa-home mr-2"></i> Return to Dashboard
                </a>
            </div>
        </div>
    </div>
</body>
</html>
