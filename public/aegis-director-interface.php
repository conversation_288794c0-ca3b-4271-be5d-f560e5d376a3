<?php
/**
 * A<PERSON>is Director Interface
 * 
 * This is the main interface for interacting with the Aegis Director agent.
 */

// Include required files
require_once '../src/utils/Database.php';
require_once '../src/utils/Session.php';
require_once '../src/models/AIAgent.php';
require_once '../src/models/AIAgentTask.php';
require_once '../src/models/AIAgentInteraction.php';
require_once '../src/models/Project.php';
require_once '../src/models/Task.php';
require_once '../src/models/AegisDirectorProjectManager.php';
require_once '../src/models/ProjectAgentAssignment.php';
require_once '../src/models/AgentBrigadeRole.php';

// Start session
Session::start();

// Initialize models
$db = Database::getInstance();
$agentModel = new AIAgent();
$taskModel = new AIAgentTask();
$interactionModel = new AIAgentInteraction();
$projectModel = new Project();
$projectTaskModel = new Task();
$projectManager = new AegisDirectorProjectManager();
$assignmentModel = new ProjectAgentAssignment();
$brigadeRoleModel = new AgentBrigadeRole();

// Get current user ID from session or use default
$currentUser = Session::getUser();
$userId = $currentUser ? $currentUser['id'] : 1; // Default to user ID 1 if not logged in

// Get the Aegis Director agent
$agents = $agentModel->getUserAgents($userId);
$aegisDirectorId = null;
$aegisDirector = null;

foreach ($agents as $agent) {
    if ($agent['name'] === 'Aegis Director') {
        $aegisDirectorId = $agent['id'];
        $aegisDirector = $agent;
        break;
    }
}

// If Aegis Director agent doesn't exist, create it
if (!$aegisDirectorId) {
    // Redirect to create agent page
    header('Location: ../create_aegis_director_agent.php');
    exit;
}

// Process form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        // Handle interaction submission
        if ($_POST['action'] === 'interact') {
            $content = $_POST['interaction_content'] ?? '';
            $type = $_POST['interaction_type'] ?? 'command';
            
            if (!empty($content)) {
                // Create the interaction
                $interactionId = $interactionModel->createInteraction([
                    'agent_id' => $aegisDirectorId,
                    'user_id' => $userId,
                    'interaction_type' => $type,
                    'content' => $content,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                
                if ($interactionId) {
                    // Generate a response
                    $response = generateResponse($content, $type, $aegisDirector);
                    
                    // Update the interaction with the response
                    $interactionModel->updateInteraction($interactionId, [
                        'response' => $response,
                        'success' => true,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                    
                    $message = 'Interaction sent successfully';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to send interaction';
                    $messageType = 'error';
                }
            } else {
                $message = 'Interaction content is required';
                $messageType = 'error';
            }
        }
        
        // Handle project creation
        else if ($_POST['action'] === 'create_project') {
            $projectName = $_POST['project_name'] ?? '';
            $projectDescription = $_POST['project_description'] ?? '';
            $projectType = $_POST['project_type'] ?? 'standard';
            $deadline = $_POST['deadline'] ?? date('Y-m-d', strtotime('+1 week'));
            
            if (empty($projectName)) {
                $message = 'Project name is required';
                $messageType = 'error';
            } else {
                $projectId = null;
                
                // Create the appropriate project type
                if ($projectType === 'rapid') {
                    // Create a rapid implementation project
                    $projectId = $projectManager->createRapidImplementationProject(
                        $userId,
                        $projectName,
                        $projectDescription,
                        $deadline
                    );
                } elseif ($projectType === 'agent_army') {
                    $brigadeType = $_POST['brigade_type'] ?? 'content_creation';
                    
                    // Create an AI Agent Army project
                    $projectId = $projectManager->createAgentArmyProject(
                        $userId,
                        $brigadeType,
                        $projectName,
                        $projectDescription,
                        $deadline
                    );
                } else {
                    // Create a standard project
                    $projectData = [
                        'user_id' => $userId,
                        'name' => $projectName,
                        'description' => $projectDescription,
                        'start_date' => date('Y-m-d'),
                        'end_date' => $deadline,
                        'status' => 'planning',
                        'progress' => 0,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                    
                    $projectId = $projectModel->create($projectData);
                }
                
                if ($projectId) {
                    $message = 'Project created successfully';
                    $messageType = 'success';
                    
                    // If it's a rapid implementation project, create the 24-hour plan
                    if ($projectType === 'rapid') {
                        $success = $projectManager->create24HourImplementationPlan($projectId, $userId);
                        if ($success) {
                            $message .= ' with 24-hour implementation plan';
                        }
                    }
                } else {
                    $message = 'Failed to create project';
                    $messageType = 'error';
                }
            }
        }
    }
}

// Get agent tasks
$agentTasks = $taskModel->getAgentTasks($aegisDirectorId);

// Get agent interactions
$agentInteractions = $interactionModel->getAgentInteractions($aegisDirectorId, 10);

// Get projects managed by Aegis Director
$managedProjects = $projectManager->getAegisDirectorProjects($userId);

// Get brigade types for project creation
$brigadeTypes = [
    'content_creation' => 'Content Creation Brigade',
    'lead_generation' => 'Lead Generation Brigade',
    'customer_support' => 'Customer Support Brigade',
    'data_analysis' => 'Data Analysis Brigade'
];

/**
 * Generate a response from the Aegis Director agent
 */
function generateResponse($content, $type, $agent) {
    // Convert content to lowercase for easier matching
    $contentLower = strtolower($content);
    
    // Check for project-related queries
    if (strpos($contentLower, 'create project') !== false || strpos($contentLower, 'new project') !== false) {
        return "I can help you create a new project. Please provide the following details:\n\n1. Project name\n2. Project description\n3. Deadline\n4. Project type (standard, rapid implementation, or AI Agent Army)\n\nFor AI Agent Army projects, also specify which brigade type you want to create.";
    }
    
    if (strpos($contentLower, '24 hour') !== false || strpos($contentLower, 'rapid implementation') !== false) {
        return "The 24-hour rapid implementation plan is designed to help you complete a project in just 24 hours. I'll break down the project into hourly blocks, focusing on the most critical features first. Would you like me to create a 24-hour implementation plan for one of your existing projects?";
    }
    
    if (strpos($contentLower, 'agent army') !== false || strpos($contentLower, 'brigade') !== false) {
        return "The AI Agent Army consists of specialized brigades, each focused on specific tasks:\n\n1. Content Creation Brigade - Generate high-quality content at scale\n2. Lead Generation Brigade - Identify and engage potential clients\n3. Customer Support Brigade - Provide automated customer support\n4. Data Analysis Brigade - Transform data into actionable insights\n\nWhich brigade would you like to implement?";
    }
    
    // Check for common patterns
    if (strpos($contentLower, 'hello') !== false || strpos($contentLower, 'hi') !== false) {
        return "I am Aegis Director, your executive functioning partner. I don't engage in small talk. What project are we working on? What is the specific goal and deadline?";
    }
    
    if (strpos($contentLower, 'help') !== false) {
        return "I am designed to ensure you achieve rapid, tangible results. I can help you with:\n\n1. Creating and managing projects\n2. Implementing the 24-hour rapid implementation plan\n3. Building your AI Agent Army with specialized brigades\n4. Breaking down complex goals into actionable tasks\n5. Maintaining focus and accountability\n\nWhat would you like assistance with?";
    }
    
    if (strpos($contentLower, 'distracted') !== false || strpos($contentLower, 'focus') !== false) {
        return "Stop. Distractions are not permitted. Return to your current task immediately. Close all unrelated applications and websites. You have committed to completing this project by the deadline. Further deviation will jeopardize your goal.";
    }
    
    // Default response
    return "I require clear, specific information about your project goals and tasks. Please provide concrete details about what you need to accomplish, by when, and any obstacles you anticipate. I will then create a structured plan and hold you accountable for execution.";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aegis Director - Executive Functioning Partner</title>
    <link rel="stylesheet" href="/momentum/public/css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="/momentum/public/js/aegis-director.js" defer></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center">
                <div class="bg-indigo-600 text-white p-3 rounded-full mr-4">
                    <i class="fas fa-shield-alt text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Aegis Director</h1>
                    <p class="text-gray-600">Your Executive Functioning Partner</p>
                </div>
            </div>
            <div class="flex items-center">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 mr-2">
                    <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Active
                </span>
                <button id="open-project-modal" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md shadow-sm text-sm font-medium mr-2">
                    <i class="fas fa-plus mr-1"></i> New Project
                </button>
                <a href="/momentum/ai-agents/view/<?= $aegisDirectorId ?>" class="text-indigo-600 hover:text-indigo-800 ml-2">
                    <i class="fas fa-cog"></i> Agent Settings
                </a>
            </div>
        </div>
        
        <!-- Flash Messages -->
        <?php if (!empty($message)): ?>
            <div class="bg-<?= $messageType === 'error' ? 'red' : 'green' ?>-100 border-l-4 border-<?= $messageType === 'error' ? 'red' : 'green' ?>-500 text-<?= $messageType === 'error' ? 'red' : 'green' ?>-700 p-4 mb-6" role="alert">
                <p><?= $message ?></p>
            </div>
        <?php endif; ?>
        
        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Column: Projects and Tasks -->
            <div class="lg:col-span-1">
                <!-- Projects Section -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">Managed Projects</h2>
                        <button id="open-project-modal-btn" class="text-indigo-600 hover:text-indigo-800">
                            <i class="fas fa-plus"></i> New
                        </button>
                    </div>
                    
                    <div class="space-y-4">
                        <?php if (empty($managedProjects)): ?>
                            <p class="text-gray-500 italic">No projects yet. Create one to get started.</p>
                        <?php else: ?>
                            <?php foreach ($managedProjects as $project): ?>
                                <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex justify-between items-start">
                                        <h3 class="font-medium text-gray-900"><?= htmlspecialchars($project['project_name']) ?></h3>
                                        <span class="text-xs font-medium uppercase 
                                            <?php 
                                            switch ($project['project_status']) {
                                                case 'completed': echo 'text-green-600'; break;
                                                case 'in_progress': echo 'text-blue-600'; break;
                                                case 'planning': echo 'text-purple-600'; break;
                                                default: echo 'text-gray-600';
                                            }
                                            ?>">
                                            <?= ucfirst($project['project_status']) ?>
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1"><?= htmlspecialchars(substr($project['project_description'], 0, 100)) ?><?= strlen($project['project_description']) > 100 ? '...' : '' ?></p>
                                    <div class="mt-3 flex justify-between items-center">
                                        <div class="text-xs text-gray-500">
                                            <span class="inline-block mr-2">
                                                <i class="fas fa-chart-pie mr-1"></i> <?= $project['project_progress'] ?>%
                                            </span>
                                            <?php if (!empty($project['brigade_type'])): ?>
                                                <span class="inline-block">
                                                    <i class="fas fa-users mr-1"></i> <?= ucfirst(str_replace('_', ' ', $project['brigade_type'])) ?> Brigade
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <a href="/momentum/projects/view/<?= $project['project_id'] ?>" class="text-xs text-indigo-600 hover:text-indigo-800">View</a>
                                            <a href="/momentum/aegis-director/report/<?= $project['project_id'] ?>" class="text-xs text-indigo-600 hover:text-indigo-800 ml-2">Report</a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Tasks Section -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">Current Tasks</h2>
                        <a href="/momentum/ai-agents/tasks/create?agent_id=<?= $aegisDirectorId ?>" class="text-indigo-600 hover:text-indigo-800">
                            <i class="fas fa-plus"></i> New Task
                        </a>
                    </div>
                    
                    <div class="space-y-4">
                        <?php if (empty($agentTasks)): ?>
                            <p class="text-gray-500 italic">No tasks assigned yet.</p>
                        <?php else: ?>
                            <?php foreach ($agentTasks as $task): ?>
                                <div class="task-card bg-white border rounded-lg shadow-sm p-4 priority-<?= $task['priority'] ?> status-<?= $task['status'] ?>">
                                    <div class="flex justify-between items-start">
                                        <h3 class="font-medium text-gray-900"><?= htmlspecialchars($task['title']) ?></h3>
                                        <span class="text-xs font-medium uppercase 
                                            <?php 
                                            switch ($task['status']) {
                                                case 'pending': echo 'text-gray-600'; break;
                                                case 'in_progress': echo 'text-blue-600'; break;
                                                case 'completed': echo 'text-green-600'; break;
                                                case 'failed': echo 'text-red-600'; break;
                                                default: echo 'text-gray-600';
                                            }
                                            ?>">
                                            <?= str_replace('_', ' ', $task['status']) ?>
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1"><?= htmlspecialchars(substr($task['description'], 0, 100)) ?><?= strlen($task['description']) > 100 ? '...' : '' ?></p>
                                    <div class="flex justify-between items-center mt-3">
                                        <span class="text-xs text-gray-500">
                                            <?php if (!empty($task['due_date'])): ?>
                                                <i class="far fa-calendar-alt mr-1"></i> Due: <?= date('M j, Y', strtotime($task['due_date'])) ?>
                                            <?php endif; ?>
                                        </span>
                                        <div>
                                            <a href="/momentum/ai-agents/tasks/view/<?= $task['id'] ?>" class="text-xs text-indigo-600 hover:text-indigo-800">View</a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Right Column: Chat Interface -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Interact with Aegis Director</h2>
                    
                    <!-- Chat Container -->
                    <div class="chat-container border rounded-lg p-4 mb-4" id="chat-container" style="height: 60vh; overflow-y: auto;">
                        <?php if (empty($agentInteractions)): ?>
                            <div class="system-message p-3 mb-4 text-sm bg-yellow-50 rounded-md">
                                <p>Welcome to Aegis Director. I am your executive functioning partner designed to help you achieve rapid, tangible results on your projects and goals.</p>
                                <p class="mt-2">I can help you with:</p>
                                <ul class="list-disc list-inside mt-1 ml-2">
                                    <li>Creating and managing projects</li>
                                    <li>Implementing the 24-hour rapid implementation plan</li>
                                    <li>Building your AI Agent Army with specialized brigades</li>
                                    <li>Breaking down complex goals into actionable tasks</li>
                                    <li>Maintaining focus and accountability</li>
                                </ul>
                                <p class="mt-2">To begin, please tell me what you'd like to work on.</p>
                            </div>
                        <?php else: ?>
                            <?php foreach (array_reverse($agentInteractions) as $interaction): ?>
                                <?php if ($interaction['interaction_type'] !== 'system'): ?>
                                    <!-- User Message -->
                                    <div class="user-message p-3 mb-4 ml-8 bg-blue-50 rounded-lg">
                                        <div class="flex items-start">
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-gray-900">You</p>
                                                <p class="text-sm text-gray-800"><?= nl2br(htmlspecialchars($interaction['content'])) ?></p>
                                                <p class="text-xs text-gray-500 mt-1"><?= date('M j, g:i a', strtotime($interaction['created_at'])) ?></p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <?php if (!empty($interaction['response'])): ?>
                                        <!-- Agent Response -->
                                        <div class="agent-message p-3 mb-4 mr-8 bg-gray-50 rounded-lg">
                                            <div class="flex items-start">
                                                <div class="flex-1">
                                                    <p class="text-sm font-medium text-gray-900">Aegis Director</p>
                                                    <p class="text-sm text-gray-800"><?= nl2br(htmlspecialchars($interaction['response'])) ?></p>
                                                    <p class="text-xs text-gray-500 mt-1"><?= date('M j, g:i a', strtotime($interaction['created_at'])) ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <!-- System Message -->
                                    <div class="system-message p-3 mb-4 bg-yellow-50 rounded-md">
                                        <p class="text-sm text-gray-800"><?= nl2br(htmlspecialchars($interaction['content'])) ?></p>
                                        <?php if (!empty($interaction['response'])): ?>
                                            <div class="mt-2 p-2 bg-white rounded">
                                                <p class="text-sm text-gray-800"><?= nl2br(htmlspecialchars($interaction['response'])) ?></p>
                                            </div>
                                        <?php endif; ?>
                                        <p class="text-xs text-gray-500 mt-1"><?= date('M j, g:i a', strtotime($interaction['created_at'])) ?></p>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Input Form -->
                    <form action="<?= $_SERVER['PHP_SELF'] ?>" method="POST" class="mt-4" id="chat-form">
                        <input type="hidden" name="action" value="interact">
                        <div class="flex items-center">
                            <select name="interaction_type" class="mr-2 rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="command">Command</option>
                                <option value="query">Question</option>
                                <option value="feedback">Feedback</option>
                            </select>
                            <input type="text" name="interaction_content" placeholder="Type your message here..." required
                                class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <button type="submit" class="ml-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Send <i class="fas fa-paper-plane ml-2"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Project Creation Modal -->
    <div id="project-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Create New Project</h3>
                <button id="close-project-modal" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="create-project-form" action="<?= $_SERVER['PHP_SELF'] ?>" method="POST">
                <input type="hidden" name="action" value="create_project">
                <div class="mb-4">
                    <label for="project-name" class="block text-sm font-medium text-gray-700 mb-1">Project Name</label>
                    <input type="text" id="project-name" name="project_name" required
                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                </div>
                
                <div class="mb-4">
                    <label for="project-description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea id="project-description" name="project_description" rows="3"
                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"></textarea>
                </div>
                
                <div class="mb-4">
                    <label for="deadline" class="block text-sm font-medium text-gray-700 mb-1">Deadline</label>
                    <input type="date" id="deadline" name="deadline" required
                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        value="<?= date('Y-m-d', strtotime('+1 week')) ?>">
                </div>
                
                <div class="mb-4">
                    <label for="project-type" class="block text-sm font-medium text-gray-700 mb-1">Project Type</label>
                    <select id="project-type" name="project_type" required
                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <option value="standard">Standard Project</option>
                        <option value="rapid">24-Hour Rapid Implementation</option>
                        <option value="agent_army">AI Agent Army Brigade</option>
                    </select>
                </div>
                
                <div id="brigade-type-container" class="mb-4 hidden">
                    <label for="brigade-type" class="block text-sm font-medium text-gray-700 mb-1">Brigade Type</label>
                    <select id="brigade-type" name="brigade_type"
                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <?php foreach ($brigadeTypes as $value => $label): ?>
                            <option value="<?= $value ?>"><?= $label ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="flex justify-end mt-6">
                    <button type="button" id="close-project-modal-btn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md shadow-sm text-sm font-medium mr-2">
                        Cancel
                    </button>
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md shadow-sm text-sm font-medium">
                        Create Project
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
