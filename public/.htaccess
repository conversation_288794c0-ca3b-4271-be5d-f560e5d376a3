# Enable URL rewriting
RewriteEngine On
RewriteBase /momentum/public/

# Redirect adhd-project-planning-guide.html to the PHP version
RewriteRule ^adhd-project-planning-guide\.html$ adhd-project-planning-guide.php [L]

# Handle document requests
RewriteRule ^docs/(.+)$ docs/index.php?file=$1 [L,QSA]

# Handle help routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^help/(.*)$ help-routes.php/$1 [L,QSA]

# If the requested file or directory exists, serve it directly
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Otherwise, route all requests to index.php
RewriteRule ^(.*)$ index.php [QSA,L]

# Cache control for static assets
<IfModule mod_expires.c>
    ExpiresActive On

    # Images
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"

    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"

    # Fonts
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"

    # Default
    ExpiresDefault "access plus 2 days"
</IfModule>

# Add cache control headers
<IfModule mod_headers.c>
    # Cache control for versioned assets
    <FilesMatch "\.(css|js)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>

    # Cache control for versioned assets (with content hash)
    <FilesMatch "\.(css|js)\.[a-f0-9]{8,}\.">
        Header set Cache-Control "public, max-age=31536000, immutable"
    </FilesMatch>

    # Cache control for images
    <FilesMatch "\.(jpg|jpeg|png|gif|ico|svg|webp)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>

    # Cache control for fonts
    <FilesMatch "\.(woff|woff2|ttf|otf|eot)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>

    # Disable caching for PHP files
    <FilesMatch "\.php$">
        Header set Cache-Control "private, no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
</IfModule>

# Compress text files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/json application/xml
</IfModule>

# Prevent directory listing
Options -Indexes
