<?php
/**
 * YouTube Agent Interface
 *
 * This script provides a simple interface to set up and run the YouTube browsing agent.
 */

// Include required files
require_once __DIR__ . '/../../src/utils/Database.php';
require_once __DIR__ . '/../../src/models/AIAgent.php';
require_once __DIR__ . '/../../src/models/AIAgentCategory.php';
require_once __DIR__ . '/../../src/models/AIAgentTask.php';
require_once __DIR__ . '/../../src/models/AIAgentInteraction.php';

// Initialize models
$db = Database::getInstance();
$agentModel = new AIAgent();
$categoryModel = new AIAgentCategory();
$taskModel = new AIAgentTask();
$interactionModel = new AIAgentInteraction();

// Get current user ID from session
require_once __DIR__ . '/../../src/utils/Session.php';
Session::start();
$currentUser = Session::getUser();
$userId = $currentUser ? $currentUser['id'] : 2; // Default to user ID 2 if not logged in

// Log the current user ID
error_log("Current user ID: " . $userId);

// Check if the agent exists
$agents = $agentModel->getUserAgents($userId);
$youtubeAgentExists = false;
$youtubeAgentId = null;

foreach ($agents as $agent) {
    if ($agent['name'] === 'YouTube Browser') {
        $youtubeAgentExists = true;
        $youtubeAgentId = $agent['id'];
        break;
    }
}

// If not found for current user, try user ID 1 (admin/default user)
if (!$youtubeAgentExists) {
    $adminAgents = $agentModel->getUserAgents(1);
    foreach ($adminAgents as $agent) {
        if ($agent['name'] === 'YouTube Browser') {
            $youtubeAgentExists = true;
            $youtubeAgentId = $agent['id'];
            break;
        }
    }
}

// Process form submission
$message = '';
$results = '';

// Create the agent if it doesn't exist
if (!$youtubeAgentExists) {
    // Get the Research Agents category
    $categories = $categoryModel->getUserCategories(1);
    $researchCategoryId = null;

    foreach ($categories as $category) {
        if ($category['name'] === 'Research Agents') {
            $researchCategoryId = $category['id'];
            break;
        }
    }

    if (!$researchCategoryId) {
        $researchCategoryId = $categoryModel->createCategory([
            'user_id' => 1,
            'name' => 'Research Agents',
            'description' => 'Agents specialized in gathering and analyzing information',
            'color' => '#3B82F6',
            'icon' => 'fa-search',
            'display_order' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    // Create the YouTube Browsing Agent
    $youtubeAgentId = $agentModel->createAgent([
        'user_id' => $userId,
        'category_id' => $researchCategoryId,
        'name' => 'YouTube Browser',
        'description' => 'Specialized agent for browsing YouTube and collecting links about specific topics',
        'capabilities' => "YouTube search,\nVideo metadata extraction,\nLink collection,\nDate filtering,\nTopic filtering,\nStructured reporting",
        'personality_traits' => "Thorough,\nDetail-oriented,\nEfficient,\nOrganized,\nCurious",
        'intelligence_level' => 8,
        'efficiency_rating' => 8.5,
        'reliability_score' => 9.0,
        'status' => 'active',
        'last_active' => date('Y-m-d H:i:s')
    ]);

    // Add skills to the agent
    $webResearchSkill = $db->fetchOne("SELECT id FROM ai_agent_skills WHERE name = 'Web Research'");

    if ($webResearchSkill) {
        $agentModel->addSkill($youtubeAgentId, $webResearchSkill['id'], 9);
    } else {
        // Create the Web Research skill if it doesn't exist
        $db->query(
            "INSERT INTO ai_agent_skills (name, description, skill_type, created_at, updated_at)
             VALUES (?, ?, ?, NOW(), NOW())",
            ['Web Research', 'Ability to search and analyze web content', 'research']
        );
        $webResearchSkillId = $db->getConnection()->lastInsertId();

        $agentModel->addSkill($youtubeAgentId, $webResearchSkillId, 9);
    }

    // Add Data Analysis skill
    $dataAnalysisSkill = $db->fetchOne("SELECT id FROM ai_agent_skills WHERE name = 'Data Analysis'");

    if ($dataAnalysisSkill) {
        $agentModel->addSkill($youtubeAgentId, $dataAnalysisSkill['id'], 7);
    }

    // Add Summarization skill
    $summarizationSkill = $db->fetchOne("SELECT id FROM ai_agent_skills WHERE name = 'Summarization'");

    if ($summarizationSkill) {
        $agentModel->addSkill($youtubeAgentId, $summarizationSkill['id'], 8);
    }

    $youtubeAgentExists = true;
    $message = 'YouTube Browser agent created automatically!';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'run_agent') {
        // Run the agent
        ob_start();
        include_once 'youtube_browser.php';
        $results = ob_get_clean();
        $message = 'YouTube Browser agent executed successfully!';
    }
}

// Get agent details if it exists
$agentDetails = null;
$agentTasks = [];
$agentInteractions = [];

if ($youtubeAgentExists && $youtubeAgentId) {
    // Get agent details with non-strict user check
    $agentDetails = $agentModel->getAgent($youtubeAgentId, $userId, false);

    // If still not found, create a default agent details array
    if (!$agentDetails || !is_array($agentDetails)) {
        $agentDetails = [
            'id' => $youtubeAgentId,
            'name' => 'YouTube Browser',
            'category_name' => 'Research Agents',
            'status' => 'active',
            'intelligence_level' => 8,
            'last_active' => date('Y-m-d H:i:s')
        ];
    }

    // Get agent tasks
    $agentTasks = $taskModel->getAgentTasks($youtubeAgentId);

    // Get agent interactions
    $agentInteractions = $interactionModel->getAgentInteractions($youtubeAgentId, 10);
}

// HTML output
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Browser Agent Interface</title>
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            overflow: auto;
        }
        .dark pre {
            background-color: #2d3748;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-6">YouTube Browser Agent Interface</h1>

        <?php if ($message): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
                <p><?php echo $message; ?></p>
            </div>
        <?php endif; ?>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Agent Status</h2>

            <?php if ($youtubeAgentExists): ?>
                <div class="flex items-center mb-4">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <p>YouTube Browser agent is set up and ready to use.</p>
                </div>

                <div class="mb-4">
                    <h3 class="font-medium mb-2">Agent Details:</h3>
                    <ul class="list-disc list-inside ml-4">
                        <li>Name: <?php echo isset($agentDetails['name']) ? $agentDetails['name'] : 'YouTube Browser'; ?></li>
                        <li>Category: <?php echo isset($agentDetails['category_name']) ? $agentDetails['category_name'] : 'Research Agents'; ?></li>
                        <li>Status: <?php echo isset($agentDetails['status']) ? ucfirst($agentDetails['status']) : 'Active'; ?></li>
                        <li>Intelligence Level: <?php echo isset($agentDetails['intelligence_level']) ? $agentDetails['intelligence_level'] : '8'; ?>/10</li>
                        <li>Last Active: <?php echo isset($agentDetails['last_active']) ? $agentDetails['last_active'] : date('Y-m-d H:i:s'); ?></li>
                    </ul>
                </div>

                <form method="post" class="mb-4">
                    <input type="hidden" name="action" value="run_agent">
                    <div class="mb-4">
                        <label for="search_topic" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search Topic</label>
                        <input type="text" name="search_topic" id="search_topic" placeholder="e.g., Rapid money making techniques, Passive income, AI tools, etc."
                            class="w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Enter a topic to search for recent YouTube videos. Leave blank to search for AI agent videos.</p>
                    </div>
                    <div class="mb-4">
                        <label for="api_key" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">YouTube API Key</label>
                        <input type="text" name="api_key" id="api_key" placeholder="Enter your YouTube API key"
                            class="w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Enter your YouTube API key to use the real YouTube API. If left blank, a fallback with sample data will be used.</p>
                    </div>
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded">
                        <i class="fas fa-play mr-2"></i> Run YouTube Browser Agent
                    </button>
                </form>

                <div class="flex flex-col space-y-2">
                    <a href="/momentum/ai-agents/view/<?php echo $youtubeAgentId; ?>" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                        <i class="fas fa-external-link-alt mr-1"></i> View Agent in AI Agents Dashboard
                    </a>
                    <a href="/momentum/youtube_agent_guide.php" target="_blank" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                        <i class="fas fa-book mr-1"></i> View YouTube Browser Agent User Guide
                    </a>
                </div>
            <?php else: ?>
                <div class="flex items-center mb-4">
                    <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                    <p>YouTube Browser agent is being set up. Please refresh the page.</p>
                </div>
            <?php endif; ?>
        </div>

        <?php if ($youtubeAgentExists && !empty($agentTasks)): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Agent Tasks</h2>

                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white dark:bg-gray-800">
                        <thead>
                            <tr>
                                <th class="py-2 px-4 border-b border-gray-200 dark:border-gray-700 text-left">Title</th>
                                <th class="py-2 px-4 border-b border-gray-200 dark:border-gray-700 text-left">Status</th>
                                <th class="py-2 px-4 border-b border-gray-200 dark:border-gray-700 text-left">Priority</th>
                                <th class="py-2 px-4 border-b border-gray-200 dark:border-gray-700 text-left">Due Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($agentTasks as $task): ?>
                                <tr>
                                    <td class="py-2 px-4 border-b border-gray-200 dark:border-gray-700"><?php echo $task['title']; ?></td>
                                    <td class="py-2 px-4 border-b border-gray-200 dark:border-gray-700">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            <?php
                                                switch ($task['status']) {
                                                    case 'completed': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'; break;
                                                    case 'in_progress': echo 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'; break;
                                                    case 'failed': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'; break;
                                                    default: echo 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
                                                }
                                            ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $task['status'])); ?>
                                        </span>
                                    </td>
                                    <td class="py-2 px-4 border-b border-gray-200 dark:border-gray-700"><?php echo ucfirst($task['priority']); ?></td>
                                    <td class="py-2 px-4 border-b border-gray-200 dark:border-gray-700"><?php echo date('Y-m-d', strtotime($task['due_date'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>

        <?php if (!empty($results)): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Agent Results</h2>
                <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded overflow-auto max-h-96">
                    <?php echo $results; ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($youtubeAgentExists && !empty($agentInteractions)): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Recent Interactions</h2>

                <div class="space-y-4">
                    <?php foreach ($agentInteractions as $interaction): ?>
                        <div class="border-l-4
                            <?php
                                switch ($interaction['interaction_type']) {
                                    case 'command': echo 'border-blue-500'; break;
                                    case 'query': echo 'border-green-500'; break;
                                    case 'feedback': echo 'border-yellow-500'; break;
                                    case 'system': echo 'border-purple-500'; break;
                                    default: echo 'border-gray-500';
                                }
                            ?> pl-4 py-2">
                            <div class="flex justify-between items-start">
                                <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                    <?php echo ucfirst($interaction['interaction_type']); ?>
                                </span>
                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                    <?php echo date('Y-m-d H:i', strtotime($interaction['created_at'])); ?>
                                </span>
                            </div>
                            <div class="mt-1 text-sm whitespace-pre-wrap">
                                <?php
                                    // Limit content length for display
                                    $content = $interaction['content'];
                                    if (strlen($content) > 200) {
                                        echo htmlspecialchars(substr($content, 0, 200)) . '...';
                                    } else {
                                        echo htmlspecialchars($content);
                                    }
                                ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <div class="mt-6">
            <a href="/momentum/ai-agents" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                <i class="fas fa-arrow-left mr-1"></i> Back to AI Agents Dashboard
            </a>
        </div>
    </div>
</body>
</html>
