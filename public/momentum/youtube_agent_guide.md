# YouTube Browser Agent User Guide

## Introduction

The YouTube Browser agent is a specialized AI agent designed to search for recent YouTube videos on specific topics and provide you with a summary of the results. This guide will help you understand how to use the agent effectively, particularly for tasks like finding "Rapid money making techniques" or other topics of interest.

## How the YouTube Browser Agent Works

The YouTube Browser agent performs the following tasks:

1. **Searches for Videos**: The agent searches for YouTube videos on the topic you specify.
2. **Filters by Recency**: It focuses on videos published in the last three days to ensure you get the most up-to-date information.
3. **Collects Metadata**: The agent collects video titles, channel names, publication dates, and descriptions.
4. **Organizes Results**: It presents the results in a structured format, making it easy to browse through the findings.
5. **Stores Interactions**: All search results are stored in the agent's interaction history for future reference.

## Using the YouTube Browser Agent

### Step 1: Access the Agent Interface

1. Go to http://localhost/momentum/youtube_agent_interface.php
2. You'll see the agent status, details, and a search form.

### Step 2: Enter Your Search Topic

1. In the "Search Topic" field, enter the topic you want to search for. For example:
   - "Rapid money making techniques"
   - "Passive income strategies"
   - "Side hustles 2024"
   - "Online business ideas"
   - "Freelance opportunities"

2. If you leave the field blank, the agent will default to searching for AI-related content.

### Step 3: Enter Your YouTube API Key (Optional)

1. In the "YouTube API Key" field, enter your YouTube Data API key.
2. This allows the agent to search for real YouTube videos instead of using sample data.
3. If you don't have an API key, you can leave this field blank, and the agent will use sample data.

### Step 4: Run the Agent

1. Click the "Run YouTube Browser Agent" button.
2. The agent will process your request and search for relevant videos.
3. Results will be displayed on the page and stored in the agent's interaction history.

### Step 5: Review the Results

1. The results will show:
   - Video titles
   - Channel names
   - Publication dates
   - Video descriptions
   - Direct links to the videos

2. You can click on any video link to watch it directly on YouTube.

### Step 6: Access Past Searches

1. All your searches are stored in the agent's interaction history.
2. You can view past searches by:
   - Scrolling down to the "Recent Interactions" section on the agent interface
   - Going to the AI Agents Dashboard and viewing the YouTube Browser agent's details

## Tips for Effective Searches

### For Money Making Topics

1. **Be Specific**: Instead of just "money making," try specific queries like:
   - "Rapid money making techniques for beginners"
   - "Passive income with low investment"
   - "Weekend side hustles"
   - "Online freelance opportunities"

2. **Target Skill Level**: Include your skill level in the search:
   - "Money making for beginners"
   - "Advanced passive income strategies"
   - "No-experience-needed side hustles"

3. **Specify Time Commitment**:
   - "Quick money making methods"
   - "Part-time online income"
   - "Weekend money making opportunities"

4. **Target Investment Level**:
   - "No investment money making"
   - "Low investment business ideas"
   - "High ROI money making methods"

### For Other Topics

1. **Use Keywords**: Include important keywords related to your topic.
2. **Combine Topics**: Try combining topics for more specific results (e.g., "AI tools for passive income").
3. **Include Year**: Add the current year to get the most relevant results (e.g., "Side hustles 2024").

## Understanding the Results

The agent provides several pieces of information for each video:

1. **Title**: The title of the video, which usually indicates the main topic.
2. **Channel**: The YouTube channel that published the video. Channels with more subscribers often provide higher-quality content.
3. **Publication Date**: When the video was published. More recent videos may contain more current information.
4. **Description**: A brief summary of the video's content, which can help you determine if it's relevant to your needs.
5. **URL**: A direct link to the video on YouTube.

## Getting a YouTube API Key

To use the real YouTube API functionality, you'll need to obtain an API key from Google:

1. Go to the [Google Developers Console](https://console.developers.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Library"
4. Search for "YouTube Data API v3" and enable it
5. Go to "APIs & Services" > "Credentials"
6. Click "Create credentials" and select "API key"
7. Copy the generated API key
8. Paste the API key into the "YouTube API Key" field in the agent interface

**Note**: The YouTube Data API has quotas that limit the number of requests you can make per day. For most users, the free tier (10,000 units per day) is sufficient for occasional use.

## Limitations

1. **Recency Limit**: The agent only searches for videos from the last three days. If you need older content, you'll need to search directly on YouTube.
2. **Result Count**: The agent returns a maximum of 10 results per search.
3. **No Filtering by Views/Ratings**: Currently, the agent doesn't filter results by view count or ratings.
4. **API Key Required for Real Results**: Without a YouTube API key, the agent will use sample data instead of real YouTube results.

## Troubleshooting

If you encounter any issues with the YouTube Browser agent:

1. **No Results**: Try broadening your search terms or using different keywords.
2. **Agent Not Responding**: Refresh the page and try again.
3. **Error Messages**: Note any error messages and report them to the system administrator.

## Future Enhancements

Future versions of the YouTube Browser agent will include:

1. **Advanced Filtering**: Filter by view count, ratings, and other metrics.
2. **Custom Time Ranges**: Search for videos from different time periods.
3. **Channel Filtering**: Focus on specific channels or exclude certain channels.
4. **Content Analysis**: Provide summaries of video content using AI.
5. **Saved Searches**: Save your favorite searches for quick access.

## Conclusion

The YouTube Browser agent is a powerful tool for discovering recent content on topics that interest you. By following this guide, you can effectively use the agent to find valuable information, particularly on money-making techniques and other topics relevant to your needs.

For any questions or feedback about the YouTube Browser agent, please contact the system administrator or leave a comment in the AI Agents Dashboard.
