<?php
/**
 * YouTube Browser
 *
 * This script implements the YouTube browsing functionality for the AI agent.
 * It searches YouTube for videos about AI agents from the last three days.
 */

// Include required files
require_once __DIR__ . '/../../src/utils/Database.php';
require_once __DIR__ . '/../../src/models/AIAgent.php';
require_once __DIR__ . '/../../src/models/AIAgentTask.php';
require_once __DIR__ . '/../../src/models/AIAgentInteraction.php';

// Initialize models
$db = Database::getInstance();
$agentModel = new AIAgent();
$taskModel = new AIAgentTask();
$interactionModel = new AIAgentInteraction();

// Get current user ID from session
require_once __DIR__ . '/../../src/utils/Session.php';
Session::start();
$currentUser = Session::getUser();
$userId = $currentUser ? $currentUser['id'] : 2; // Default to user ID 2 if not logged in

// Log the current user ID
error_log("YouTube Browser - Current user ID: " . $userId);

// Check database connection
if (!$db) {
    die("Database connection failed");
}

// Get the YouTube Browser agent
$agents = $agentModel->getUserAgents($userId);
$youtubeAgentId = null;

foreach ($agents as $agent) {
    if ($agent['name'] === 'YouTube Browser') {
        $youtubeAgentId = $agent['id'];
        break;
    }
}

// If not found for current user, try user ID 1 (admin/default user)
if (!$youtubeAgentId) {
    $adminAgents = $agentModel->getUserAgents(1);
    foreach ($adminAgents as $agent) {
        if ($agent['name'] === 'YouTube Browser') {
            $youtubeAgentId = $agent['id'];
            break;
        }
    }
}

// Verify the agent exists by trying to get its details
if ($youtubeAgentId) {
    $agentDetails = $agentModel->getAgent($youtubeAgentId, $userId, false);
    if (!$agentDetails) {
        $youtubeAgentId = null; // Reset if agent not found
    }
}

// If still not found, create it
if (!$youtubeAgentId) {
    // Get the Research Agents category
    $categories = $categoryModel->getUserCategories($userId);
    $researchCategoryId = null;

    foreach ($categories as $category) {
        if ($category['name'] === 'Research Agents') {
            $researchCategoryId = $category['id'];
            break;
        }
    }

    if (!$researchCategoryId) {
        $researchCategoryId = $categoryModel->createCategory([
            'user_id' => $userId,
            'name' => 'Research Agents',
            'description' => 'Agents specialized in gathering and analyzing information',
            'color' => '#3B82F6',
            'icon' => 'fa-search',
            'display_order' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    // Create the YouTube Browsing Agent
    $youtubeAgentId = $agentModel->createAgent([
        'user_id' => $userId,
        'category_id' => $researchCategoryId,
        'name' => 'YouTube Browser',
        'description' => 'Specialized agent for browsing YouTube and collecting links about specific topics',
        'capabilities' => "YouTube search,\nVideo metadata extraction,\nLink collection,\nDate filtering,\nTopic filtering,\nStructured reporting",
        'personality_traits' => "Thorough,\nDetail-oriented,\nEfficient,\nOrganized,\nCurious",
        'intelligence_level' => 8,
        'efficiency_rating' => 8.5,
        'reliability_score' => 9.0,
        'status' => 'active',
        'last_active' => date('Y-m-d H:i:s')
    ]);

    // Add skills to the agent
    $webResearchSkill = $db->fetchOne("SELECT id FROM ai_agent_skills WHERE name = 'Web Research'");

    if ($webResearchSkill) {
        $agentModel->addSkill($youtubeAgentId, $webResearchSkill['id'], 9);
    } else {
        // Create the Web Research skill if it doesn't exist
        $db->query(
            "INSERT INTO ai_agent_skills (name, description, skill_type, created_at, updated_at)
             VALUES (?, ?, ?, NOW(), NOW())",
            ['Web Research', 'Ability to search and analyze web content', 'research']
        );
        $webResearchSkillId = $db->getConnection()->lastInsertId();

        $agentModel->addSkill($youtubeAgentId, $webResearchSkillId, 9);
    }

    // Add Data Analysis skill
    $dataAnalysisSkill = $db->fetchOne("SELECT id FROM ai_agent_skills WHERE name = 'Data Analysis'");

    if ($dataAnalysisSkill) {
        $agentModel->addSkill($youtubeAgentId, $dataAnalysisSkill['id'], 7);
    }

    // Add Summarization skill
    $summarizationSkill = $db->fetchOne("SELECT id FROM ai_agent_skills WHERE name = 'Summarization'");

    if ($summarizationSkill) {
        $agentModel->addSkill($youtubeAgentId, $summarizationSkill['id'], 8);
    }
}

// Get or create the task
$taskTitle = isset($_POST['search_topic']) ? "Find YouTube videos about {$_POST['search_topic']} from the last three days" : "Find YouTube videos about AI agents from the last three days";
$taskDescription = isset($_POST['search_topic']) ?
    "Search YouTube for videos about {$_POST['search_topic']} that were published in the last three days. Collect the links, titles, channel names, and publication dates. Organize the results by relevance and recency." :
    "Search YouTube for videos about AI agents, AI assistants, or AI tools that were published in the last three days. Collect the links, titles, channel names, and publication dates. Organize the results by relevance and recency.";

$tasks = $taskModel->getAgentTasks($youtubeAgentId);
$taskId = null;

foreach ($tasks as $task) {
    if (strpos($task['title'], 'Find YouTube videos about AI agents') !== false) {
        $taskId = $task['id'];
        break;
    }
}

// If task doesn't exist, create it
if (!$taskId) {
    $taskId = $taskModel->createTask([
        'agent_id' => $youtubeAgentId,
        'user_id' => $userId,
        'title' => $taskTitle,
        'description' => $taskDescription,
        'priority' => 'high',
        'status' => 'pending',
        'due_date' => date('Y-m-d H:i:s', strtotime('+1 day')),
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    if (!$taskId) {
        die("Failed to create task. Please check the database connection.");
    }
}

// Update task status to in_progress
$taskModel->updateTask($taskId, [
    'status' => 'in_progress',
    'updated_at' => date('Y-m-d H:i:s')
]);

// Log the start of the task
$interactionModel->createInteraction([
    'agent_id' => $youtubeAgentId,
    'user_id' => $userId,
    'interaction_type' => 'system',
    'content' => "Starting task: Find YouTube videos about AI agents from the last three days",
    'created_at' => date('Y-m-d H:i:s')
]);

// Function to search YouTube using the real YouTube Data API
function searchYouTube($query, $maxResults = 10, $publishedAfter = null) {
    // YouTube API Key - You should store this in a more secure way in a production environment
    // Check if API key is provided in the request
    $apiKey = isset($_POST['api_key']) && !empty($_POST['api_key']) ? $_POST['api_key'] : 'YOUR_YOUTUBE_API_KEY';

    // Base URL for YouTube Data API v3 search endpoint
    $baseUrl = 'https://www.googleapis.com/youtube/v3/search';

    // Build query parameters
    $params = [
        'part' => 'snippet',
        'maxResults' => $maxResults,
        'q' => $query,
        'type' => 'video',
        'key' => $apiKey,
        'order' => 'date', // Sort by date
        'relevanceLanguage' => 'en', // English results
        'videoEmbeddable' => 'true', // Only embeddable videos
    ];

    // Add publishedAfter parameter if provided
    if ($publishedAfter) {
        $params['publishedAfter'] = $publishedAfter;
    }

    // Build the URL
    $url = $baseUrl . '?' . http_build_query($params);

    // Initialize cURL session
    $ch = curl_init();

    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For development only, enable in production

    // Execute cURL session and get the response
    $response = curl_exec($ch);

    // Check for cURL errors
    if (curl_errno($ch)) {
        error_log('cURL error: ' . curl_error($ch));
        curl_close($ch);
        return useFallbackSearch($query, $maxResults, $publishedAfter); // Use fallback if API fails
    }

    // Close cURL session
    curl_close($ch);

    // Decode JSON response
    $data = json_decode($response, true);

    // Check if the API request was successful
    if (!isset($data['items'])) {
        error_log('YouTube API error: ' . json_encode($data));
        return useFallbackSearch($query, $maxResults, $publishedAfter); // Use fallback if API fails
    }

    // Process the results
    $videos = [];
    foreach ($data['items'] as $item) {
        // Extract video ID
        $videoId = $item['id']['videoId'];

        // Extract video details
        $video = [
            'id' => $videoId,
            'title' => $item['snippet']['title'],
            'channelTitle' => $item['snippet']['channelTitle'],
            'publishedAt' => $item['snippet']['publishedAt'],
            'description' => $item['snippet']['description'],
            'url' => 'https://www.youtube.com/watch?v=' . $videoId,
            'thumbnail' => isset($item['snippet']['thumbnails']['high']['url']) ?
                          $item['snippet']['thumbnails']['high']['url'] :
                          (isset($item['snippet']['thumbnails']['default']['url']) ?
                           $item['snippet']['thumbnails']['default']['url'] : ''),
        ];

        $videos[] = $video;
    }

    return $videos;
}

// Fallback function to use if the YouTube API fails
function useFallbackSearch($query, $maxResults = 10, $publishedAfter = null) {
    // Log that we're using the fallback
    error_log('Using fallback YouTube search for query: ' . $query);

    // Check if the query is about money making
    $isMoneyMakingQuery = stripos($query, 'money') !== false ||
                          stripos($query, 'income') !== false ||
                          stripos($query, 'earn') !== false ||
                          stripos($query, 'profit') !== false ||
                          stripos($query, 'wealth') !== false ||
                          stripos($query, 'financial') !== false ||
                          stripos($query, 'passive') !== false;

    // Sample data structure for YouTube videos
    if ($isMoneyMakingQuery) {
        // Money making content
        $sampleVideos = [
            [
                'id' => 'money1',
                'title' => '10 Rapid Money Making Techniques That Actually Work in 2024',
                'channelTitle' => 'Financial Freedom',
                'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-1 day')),
                'description' => 'Discover 10 proven techniques to make money quickly in 2024. These methods require minimal investment and can start generating income within days.',
                'url' => 'https://www.youtube.com/watch?v=money1',
                'thumbnail' => 'https://i.ytimg.com/vi/default/hqdefault.jpg'
            ],
            [
                'id' => 'money2',
                'title' => 'How I Made $5,000 in One Week with This Side Hustle',
                'channelTitle' => 'Entrepreneur Daily',
                'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-2 days')),
                'description' => 'I share my exact strategy for making $5,000 in just one week with a simple side hustle that anyone can start with minimal skills or experience.',
                'url' => 'https://www.youtube.com/watch?v=money2',
                'thumbnail' => 'https://i.ytimg.com/vi/default/hqdefault.jpg'
            ],
            // Additional sample videos...
        ];
    } else {
        // Default AI content
        $sampleVideos = [
            [
                'id' => 'video1',
                'title' => 'Building Advanced AI Agents with LangChain and GPT-4',
                'channelTitle' => 'AI Explained',
                'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-1 day')),
                'description' => 'Learn how to build advanced AI agents using LangChain and GPT-4. This tutorial covers the latest techniques for creating autonomous agents.',
                'url' => 'https://www.youtube.com/watch?v=sample1',
                'thumbnail' => 'https://i.ytimg.com/vi/default/hqdefault.jpg'
            ],
            [
                'id' => 'video2',
                'title' => 'The Future of AI Agents: Autonomous Systems in 2024',
                'channelTitle' => 'Tech Insights',
                'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-2 days')),
                'description' => 'Exploring the cutting-edge developments in autonomous AI agents and what the future holds for this technology.',
                'url' => 'https://www.youtube.com/watch?v=sample2',
                'thumbnail' => 'https://i.ytimg.com/vi/default/hqdefault.jpg'
            ],
            // Additional sample videos...
        ];
    }

    // Filter videos by publish date if needed
    if ($publishedAfter) {
        $sampleVideos = array_filter($sampleVideos, function($video) use ($publishedAfter) {
            return strtotime($video['publishedAt']) >= strtotime($publishedAfter);
        });
    }

    // Limit results
    $sampleVideos = array_slice($sampleVideos, 0, $maxResults);

    return $sampleVideos;
}

// Search for videos from the last three days
$threeDaysAgo = date('Y-m-d\TH:i:s\Z', strtotime('-3 days'));
$searchQuery = isset($_POST['search_topic']) ? $_POST['search_topic'] : 'AI agents OR AI assistants OR autonomous AI';
$searchResults = searchYouTube($searchQuery, 10, $threeDaysAgo);

// Format the results
$searchTopic = isset($_POST['search_topic']) ? $_POST['search_topic'] : 'AI Agent';
$formattedResults = "# {$searchTopic} Videos from the Last Three Days\n\n";
$formattedResults .= "Search completed on: " . date('Y-m-d H:i:s') . "\n\n";

if (empty($searchResults)) {
    $formattedResults .= "No videos found matching the criteria.\n";
} else {
    $formattedResults .= "Found " . count($searchResults) . " videos:\n\n";

    foreach ($searchResults as $index => $video) {
        $formattedResults .= "## " . ($index + 1) . ". " . $video['title'] . "\n";
        $formattedResults .= "- **Channel:** " . $video['channelTitle'] . "\n";
        $formattedResults .= "- **Published:** " . date('Y-m-d H:i:s', strtotime($video['publishedAt'])) . "\n";
        $formattedResults .= "- **URL:** [Watch Video](" . $video['url'] . ")\n";
        if (isset($video['thumbnail']) && !empty($video['thumbnail'])) {
            $formattedResults .= "- **Thumbnail:** ![Thumbnail](" . $video['thumbnail'] . ")\n";
        }
        $formattedResults .= "- **Description:** " . $video['description'] . "\n\n";
    }
}

// Log the results as an interaction
$interactionModel->createInteraction([
    'agent_id' => $youtubeAgentId,
    'user_id' => $userId,
    'interaction_type' => 'system',
    'content' => "Task completed. Results:\n\n" . $formattedResults,
    'created_at' => date('Y-m-d H:i:s')
]);

// Update task status to completed
$taskModel->updateTask($taskId, [
    'status' => 'completed',
    'completion_date' => date('Y-m-d H:i:s'),
    'success_rating' => 9,
    'feedback' => 'Successfully retrieved and formatted AI agent videos from the last three days.',
    'updated_at' => date('Y-m-d H:i:s')
]);

// Update agent last active time
$agentModel->updateLastActive($youtubeAgentId);

// Output the results
echo "<h1>YouTube Browser Agent Results</h1>";
echo "<pre>" . htmlspecialchars($formattedResults) . "</pre>";
echo "<p>Results have been saved to the agent's interactions.</p>";
echo "<p><a href='/momentum/ai-agents/view/{$youtubeAgentId}'>View Agent</a> | <a href='/momentum/ai-agents'>AI Agents Dashboard</a></p>";
