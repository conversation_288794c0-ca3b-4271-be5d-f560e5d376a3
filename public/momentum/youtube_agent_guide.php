<?php
/**
 * YouTube Agent Guide Viewer
 */

// Get the guide content
$guideContent = file_get_contents(__DIR__ . '/youtube_agent_guide.md');

// Convert markdown to HTML (simple conversion)
function markdownToHtml($markdown) {
    // Headers
    $markdown = preg_replace('/^# (.*?)$/m', '<h1>$1</h1>', $markdown);
    $markdown = preg_replace('/^## (.*?)$/m', '<h2>$1</h2>', $markdown);
    $markdown = preg_replace('/^### (.*?)$/m', '<h3>$1</h3>', $markdown);
    
    // Lists
    $markdown = preg_replace('/^(\d+)\. (.*?)$/m', '<li>$2</li>', $markdown);
    $markdown = preg_replace('/^- (.*?)$/m', '<li>$1</li>', $markdown);
    
    // Bold
    $markdown = preg_replace('/\*\*(.*?)\*\*/m', '<strong>$1</strong>', $markdown);
    
    // Italic
    $markdown = preg_replace('/\*(.*?)\*/m', '<em>$1</em>', $markdown);
    
    // Wrap lists in <ul> or <ol>
    $markdown = preg_replace('/((?:<li>.*?<\/li>\n)+)/s', '<ul>$1</ul>', $markdown);
    
    // Paragraphs
    $markdown = preg_replace('/^(?!<h|<ul|<li)(.*?)$/m', '<p>$1</p>', $markdown);
    
    return $markdown;
}

// Convert the markdown to HTML
$htmlContent = markdownToHtml($guideContent);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Browser Agent User Guide</title>
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .guide-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        .guide-content h1 {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            color: #4F46E5;
        }
        .guide-content h2 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-top: 2rem;
            margin-bottom: 1rem;
            color: #4F46E5;
        }
        .guide-content h3 {
            font-size: 1.25rem;
            font-weight: bold;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
            color: #4F46E5;
        }
        .guide-content p {
            margin-bottom: 1rem;
            line-height: 1.6;
        }
        .guide-content ul {
            margin-bottom: 1rem;
            padding-left: 1.5rem;
            list-style-type: disc;
        }
        .guide-content li {
            margin-bottom: 0.5rem;
            line-height: 1.6;
        }
        .dark .guide-content h1,
        .dark .guide-content h2,
        .dark .guide-content h3 {
            color: #818CF8;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="guide-content">
                <?php echo $htmlContent; ?>
            </div>
        </div>
        
        <div class="mt-6 text-center">
            <a href="/momentum/youtube_agent_interface.php" class="inline-flex items-center text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                <i class="fas fa-arrow-left mr-2"></i> Back to YouTube Browser Agent Interface
            </a>
        </div>
    </div>
</body>
</html>
