/**
 * Master Dropdown Fix
 *
 * A comprehensive solution for all dropdown menus in the application.
 * This script handles all dropdown behavior with proper hover intent
 * to prevent flickering and ensure consistent behavior.
 *
 * Version 2.0 - Enhanced with better hover detection and debouncing
 */

(function() {
    // Configuration
    const config = {
        hoverDelay: 300,       // Delay before hiding dropdown on mouseleave (ms)
        transitionDuration: 150, // Duration of fade transition (ms)
        debounceTime: 50,      // Debounce time for rapid style changes (ms)
        debug: true            // Enable debug logging
    };

    // Store references to all dropdown elements
    const dropdowns = [
        {
            name: 'ADHD',
            button: document.getElementById('adhd-dropdown-button'),
            menu: document.getElementById('adhd-dropdown-menu'),
            container: document.getElementById('adhd-dropdown'),
            isHovering: false,
            isButtonHover: false,
            isMenuHover: false,
            isContainerHover: false,
            timeout: null,
            styleTimeout: null
        },
        {
            name: 'Productivity',
            button: document.getElementById('productivity-dropdown-button'),
            menu: document.getElementById('productivity-dropdown-menu'),
            container: document.getElementById('productivity-dropdown'),
            isHovering: false,
            isButtonHover: false,
            isMenuHover: false,
            isContainerHover: false,
            timeout: null,
            styleTimeout: null
        },
        {
            name: 'Tools',
            button: document.getElementById('tools-dropdown-button'),
            menu: document.getElementById('tools-dropdown-menu'),
            container: document.getElementById('tools-dropdown'),
            isHovering: false,
            isButtonHover: false,
            isMenuHover: false,
            isContainerHover: false,
            timeout: null,
            styleTimeout: null
        },
        {
            name: 'User',
            button: document.getElementById('user-menu-button'),
            menu: document.getElementById('user-dropdown-menu'),
            container: null, // User menu doesn't have a container
            isHovering: false,
            isButtonHover: false,
            isMenuHover: false,
            isContainerHover: false,
            timeout: null,
            styleTimeout: null
        }
    ];

    // Track the currently active dropdown
    let activeDropdown = null;

    // Debug logging function
    function debug(message) {
        if (config.debug) {
            console.log(`[Dropdown Fix] ${message}`);
        }
    }

    // Debounce function to prevent rapid style changes
    function debounce(func, wait) {
        let timeout;
        return function(...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    // Function to position a dropdown menu
    function positionDropdown(dropdown) {
        if (!dropdown.button || !dropdown.menu) return;

        const buttonRect = dropdown.button.getBoundingClientRect();
        const viewportWidth = window.innerWidth;

        // Position the menu below the button
        dropdown.menu.style.position = 'fixed';
        dropdown.menu.style.top = (buttonRect.bottom + window.scrollY) + 'px';
        dropdown.menu.style.left = (buttonRect.left + window.scrollX) + 'px';
        dropdown.menu.style.zIndex = '9999';

        // Adjust if menu would go off-screen to the right
        const menuWidth = dropdown.menu.offsetWidth || 200; // Default width if not yet rendered
        if (buttonRect.left + menuWidth > viewportWidth) {
            dropdown.menu.style.left = (buttonRect.right - menuWidth + window.scrollX) + 'px';
        }

        // Set max height based on viewport
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - buttonRect.bottom - 10;
        dropdown.menu.style.maxHeight = spaceBelow + 'px';
        dropdown.menu.style.overflowY = 'auto';
    }

    // Function to update hover state
    function updateHoverState(dropdown) {
        // Overall hover state is true if any component is being hovered
        dropdown.isHovering = dropdown.isButtonHover || dropdown.isMenuHover || dropdown.isContainerHover;

        debug(`${dropdown.name} hover state: ${dropdown.isHovering} (Button: ${dropdown.isButtonHover}, Menu: ${dropdown.isMenuHover}, Container: ${dropdown.isContainerHover})`);

        return dropdown.isHovering;
    }

    // Function to show a dropdown with debouncing
    const showDropdownDebounced = debounce(function(dropdown) {
        if (!dropdown.button || !dropdown.menu) return;

        // Hide any active dropdown first
        if (activeDropdown && activeDropdown !== dropdown) {
            hideDropdown(activeDropdown, true);
        }

        // Clear any pending timeout
        if (dropdown.timeout) {
            clearTimeout(dropdown.timeout);
            dropdown.timeout = null;
        }

        // Position the menu
        positionDropdown(dropdown);

        // Show the menu - only change styles if needed
        if (dropdown.menu.style.display !== 'block') {
            dropdown.menu.style.display = 'block';
            dropdown.menu.style.visibility = 'visible';

            // Use setTimeout to ensure the transition works
            clearTimeout(dropdown.styleTimeout);
            dropdown.styleTimeout = setTimeout(() => {
                dropdown.menu.style.opacity = '1';
            }, 10);
        }

        // Set as active dropdown
        activeDropdown = dropdown;

        debug(`Showing ${dropdown.name} dropdown`);
    }, config.debounceTime);

    // Function to show a dropdown
    function showDropdown(dropdown) {
        showDropdownDebounced(dropdown);
    }

    // Function to hide a dropdown
    function hideDropdown(dropdown, immediate = false) {
        if (!dropdown.button || !dropdown.menu) return;

        // If not immediate, check if we should actually hide
        if (!immediate) {
            // Update hover state to get the latest
            if (updateHoverState(dropdown)) {
                debug(`Not hiding ${dropdown.name} dropdown because still hovering`);
                return;
            }
        }

        // Clear any pending timeout
        if (dropdown.timeout) {
            clearTimeout(dropdown.timeout);
            dropdown.timeout = null;
        }

        // Clear any pending style timeout
        if (dropdown.styleTimeout) {
            clearTimeout(dropdown.styleTimeout);
            dropdown.styleTimeout = null;
        }

        // Start the hide process - only change opacity if it's not already 0
        if (dropdown.menu.style.opacity !== '0') {
            dropdown.menu.style.opacity = '0';
        }

        // Hide after transition completes
        dropdown.timeout = setTimeout(() => {
            // Double-check we're still not hovering
            if (immediate || !updateHoverState(dropdown)) {
                dropdown.menu.style.display = 'none';
                dropdown.menu.style.visibility = 'hidden';

                // Clear active dropdown if this was it
                if (activeDropdown === dropdown) {
                    activeDropdown = null;
                }

                debug(`Hidden ${dropdown.name} dropdown`);
            } else {
                // If we're hovering again, cancel the hide
                dropdown.menu.style.opacity = '1';
                debug(`Cancelled hiding ${dropdown.name} dropdown because hovering again`);
            }
        }, config.transitionDuration);
    }

    // Function to handle hover state changes
    function handleHover(dropdown, element, isEntering) {
        // Update the specific hover state
        if (element === 'button') {
            dropdown.isButtonHover = isEntering;
        } else if (element === 'menu') {
            dropdown.isMenuHover = isEntering;
        } else if (element === 'container') {
            dropdown.isContainerHover = isEntering;
        }

        // Update overall hover state
        updateHoverState(dropdown);

        if (dropdown.isHovering) {
            showDropdown(dropdown);
        } else {
            // Use timeout for leaving to prevent flickering
            dropdown.timeout = setTimeout(() => {
                hideDropdown(dropdown);
            }, config.hoverDelay);
        }
    }

    // Function to set up a dropdown
    function setupDropdown(dropdown) {
        if (!dropdown.button || !dropdown.menu) {
            debug(`Skipping setup for ${dropdown.name} dropdown - missing elements`);
            return;
        }

        debug(`Setting up ${dropdown.name} dropdown`);

        // Ensure the menu has the right styles
        dropdown.menu.style.transition = `opacity ${config.transitionDuration}ms ease-in-out`;
        dropdown.menu.style.display = 'none';
        dropdown.menu.style.visibility = 'hidden';
        dropdown.menu.style.opacity = '0';

        // Handle click on the button
        dropdown.button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (activeDropdown === dropdown) {
                hideDropdown(dropdown, true);
            } else {
                showDropdown(dropdown);
            }
        });

        // Handle hover on the button
        dropdown.button.addEventListener('mouseenter', function() {
            handleHover(dropdown, 'button', true);
        });

        dropdown.button.addEventListener('mouseleave', function() {
            handleHover(dropdown, 'button', false);
        });

        // Handle hover on the menu
        dropdown.menu.addEventListener('mouseenter', function() {
            handleHover(dropdown, 'menu', true);
        });

        dropdown.menu.addEventListener('mouseleave', function() {
            handleHover(dropdown, 'menu', false);
        });

        // Handle hover on the container (if it exists)
        if (dropdown.container) {
            dropdown.container.addEventListener('mouseenter', function() {
                handleHover(dropdown, 'container', true);
            });

            dropdown.container.addEventListener('mouseleave', function() {
                handleHover(dropdown, 'container', false);
            });
        }
    }

    // Handle click outside to close dropdowns
    document.addEventListener('click', function(e) {
        if (activeDropdown) {
            // Check if the click was inside the active dropdown
            let isInside = false;

            if (activeDropdown.button && activeDropdown.button.contains(e.target)) {
                isInside = true;
            }

            if (activeDropdown.menu && activeDropdown.menu.contains(e.target)) {
                isInside = true;
            }

            if (activeDropdown.container && activeDropdown.container.contains(e.target)) {
                isInside = true;
            }

            if (!isInside) {
                hideDropdown(activeDropdown, true);
            }
        }
    });

    // Handle window resize and scroll
    window.addEventListener('resize', function() {
        if (activeDropdown) {
            positionDropdown(activeDropdown);
        }
    });

    window.addEventListener('scroll', function() {
        if (activeDropdown) {
            positionDropdown(activeDropdown);
        }
    });

    // Initialize when the DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        debug('Initializing dropdown fix');

        // Set up each dropdown
        dropdowns.forEach(setupDropdown);
    });
})();
