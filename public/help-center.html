<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Momentum - Help Center</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        success: {
                            50: '#ecfdf5',
                            100: '#d1fae5',
                            200: '#a7f3d0',
                            300: '#6ee7b7',
                            400: '#34d399',
                            500: '#10b981',
                            600: '#059669',
                            700: '#047857',
                            800: '#065f46',
                            900: '#064e3b',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        danger: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#ef4444',
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d',
                        },
                    },
                },
            },
        }
    </script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .sidebar {
            height: calc(100vh - 4rem);
            overflow-y: auto;
        }
        
        .main-content {
            height: calc(100vh - 4rem);
            overflow-y: auto;
        }
        
        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        .dark ::-webkit-scrollbar-track {
            background: #374151;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        /* Status indicators */
        .status-implemented {
            background-color: #10b981;
            color: white;
        }
        
        .status-in-progress {
            background-color: #f59e0b;
            color: white;
        }
        
        .status-planned {
            background-color: #8b5cf6;
            color: white;
        }
        
        .status-concept {
            background-color: #6b7280;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white">
    <!-- Navigation Bar -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo and brand -->
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="#" class="text-xl font-bold text-primary-600 dark:text-primary-400">
                            <i class="fas fa-bolt mr-2"></i>Momentum
                        </a>
                    </div>
                </div>
                
                <!-- Desktop navigation -->
                <div class="hidden md:flex md:items-center md:space-x-4" id="desktop-navigation">
                    <nav class="flex space-x-4">
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-home mr-1"></i> Dashboard
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-tasks mr-1"></i> Tasks
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-project-diagram mr-1"></i> Projects
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-primary-500 text-primary-600 dark:text-primary-400">
                            <i class="fas fa-question-circle mr-1"></i> Help
                        </a>
                    </nav>
                    
                    <!-- Theme toggle -->
                    <div class="ml-4 relative flex-shrink-0">
                        <button id="theme-toggle" class="bg-white dark:bg-gray-700 rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 p-2 hover:bg-gray-100 dark:hover:bg-gray-600" title="Toggle Theme">
                            <span class="sr-only">Toggle Theme</span>
                            <div class="h-5 w-5 flex items-center justify-center text-primary-600 dark:text-primary-300">
                                <i class="fas fa-moon dark:hidden"></i>
                                <i class="fas fa-sun hidden dark:block"></i>
                            </div>
                        </button>
                    </div>
                    
                    <!-- User menu -->
                    <div class="ml-4 relative flex-shrink-0">
                        <div>
                            <button type="button" id="user-menu-button" class="bg-white dark:bg-gray-700 rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <span class="sr-only">Open user menu</span>
                                <div class="h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-800 flex items-center justify-center text-primary-600 dark:text-primary-300">
                                    <i class="fas fa-user"></i>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Mobile menu button -->
                <div class="flex items-center -mr-2 md:hidden">
                    <button type="button" id="mobile-menu-button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500">
                        <span class="sr-only">Open main menu</span>
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content with Sidebar -->
    <div class="flex flex-col md:flex-row">
        <!-- Sidebar -->
        <div class="bg-white dark:bg-gray-800 w-full md:w-64 shadow md:h-screen sidebar">
            <div class="p-4">
                <h2 class="text-lg font-semibold mb-4">Help Center</h2>
                <div class="space-y-2">
                    <a href="#getting-started" class="block px-3 py-2 rounded-md bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300">
                        <i class="fas fa-play-circle mr-2"></i> Getting Started
                    </a>
                    <a href="#user-guide" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-book mr-2"></i> User Guide
                    </a>
                    <a href="#feature-overview" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-list-alt mr-2"></i> Feature Overview
                    </a>
                    <a href="#tutorials" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-graduation-cap mr-2"></i> Tutorials
                    </a>
                    <a href="#faq" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-question-circle mr-2"></i> FAQ
                    </a>
                    <a href="#troubleshooting" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-wrench mr-2"></i> Troubleshooting
                    </a>
                    <a href="#contact-support" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-headset mr-2"></i> Contact Support
                    </a>
                </div>
                
                <h2 class="text-lg font-semibold mt-6 mb-4">Feature Status</h2>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <span class="w-3 h-3 rounded-full bg-success-500 mr-2"></span>
                        <span>Implemented</span>
                    </div>
                    <div class="flex items-center">
                        <span class="w-3 h-3 rounded-full bg-warning-500 mr-2"></span>
                        <span>In Progress</span>
                    </div>
                    <div class="flex items-center">
                        <span class="w-3 h-3 rounded-full bg-secondary-500 mr-2"></span>
                        <span>Planned</span>
                    </div>
                    <div class="flex items-center">
                        <span class="w-3 h-3 rounded-full bg-gray-500 mr-2"></span>
                        <span>Concept</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main content area -->
        <div class="flex-1 p-6 main-content">
            <div class="max-w-4xl mx-auto">
                <section id="getting-started" class="mb-12">
                    <h1 class="text-3xl font-bold mb-6">Welcome to Momentum Help Center</h1>
                    <p class="mb-6">This help center provides comprehensive documentation, tutorials, and support resources for the Momentum system. Whether you're new to Momentum or looking to master advanced features, you'll find the information you need here.</p>
                    
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                        <h2 class="text-xl font-semibold mb-4">Getting Started with Momentum</h2>
                        <p class="mb-4">Momentum is an ADHD-friendly personal management system designed to help you organize your life, increase productivity, and manage ADHD symptoms effectively.</p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                            <a href="#" class="bg-primary-50 dark:bg-primary-900 p-4 rounded-lg border border-primary-200 dark:border-primary-700 hover:bg-primary-100 dark:hover:bg-primary-800 transition">
                                <h3 class="font-semibold text-primary-700 dark:text-primary-300 mb-2">Quick Start Guide</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Get up and running with Momentum in minutes</p>
                            </a>
                            <a href="#" class="bg-primary-50 dark:bg-primary-900 p-4 rounded-lg border border-primary-200 dark:border-primary-700 hover:bg-primary-100 dark:hover:bg-primary-800 transition">
                                <h3 class="font-semibold text-primary-700 dark:text-primary-300 mb-2">Video Tutorials</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Watch step-by-step guides to key features</p>
                            </a>
                            <a href="#" class="bg-primary-50 dark:bg-primary-900 p-4 rounded-lg border border-primary-200 dark:border-primary-700 hover:bg-primary-100 dark:hover:bg-primary-800 transition">
                                <h3 class="font-semibold text-primary-700 dark:text-primary-300 mb-2">ADHD Management Guide</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Learn how to use Momentum for ADHD</p>
                            </a>
                            <a href="#" class="bg-primary-50 dark:bg-primary-900 p-4 rounded-lg border border-primary-200 dark:border-primary-700 hover:bg-primary-100 dark:hover:bg-primary-800 transition">
                                <h3 class="font-semibold text-primary-700 dark:text-primary-300 mb-2">System Requirements</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Technical requirements and compatibility</p>
                            </a>
                        </div>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <h2 class="text-xl font-semibold mb-4">Sample Use Cases</h2>
                        <p class="mb-4">Here are some common scenarios where Momentum can help:</p>
                        
                        <div class="space-y-4 mt-4">
                            <div class="border-l-4 border-primary-500 pl-4 py-2">
                                <h3 class="font-semibold mb-1">Managing Daily Tasks with ADHD</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Use the Current Focus feature to maintain attention on one task at a time, combined with the ADHD Symptom Tracker to identify patterns in your productivity.</p>
                            </div>
                            <div class="border-l-4 border-primary-500 pl-4 py-2">
                                <h3 class="font-semibold mb-1">Financial Management</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Track expenses, manage income from multiple sources, create budgets, and set financial goals with visual progress tracking.</p>
                            </div>
                            <div class="border-l-4 border-primary-500 pl-4 py-2">
                                <h3 class="font-semibold mb-1">Project Planning and Execution</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Break down complex projects into manageable tasks, set dependencies, track progress, and collaborate with team members.</p>
                            </div>
                            <div class="border-l-4 border-primary-500 pl-4 py-2">
                                <h3 class="font-semibold mb-1">Building Consistent Habits</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Use Consistency Trackers to monitor daily habits, build streaks, and gradually improve executive function.</p>
                            </div>
                        </div>
                    </div>
                </section>
                
                <section id="feature-overview" class="mb-12">
                    <h2 class="text-2xl font-bold mb-6">Feature Overview</h2>
                    <p class="mb-6">Momentum includes a comprehensive set of features designed to help you manage all aspects of your life. Here's an overview of the current and planned features:</p>
                    
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden mb-6">
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-4">Core Features</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-medium">Dashboard</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Central hub for accessing all system features</p>
                                    </div>
                                    <span class="px-2 py-1 text-xs rounded-full status-implemented">Implemented</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-medium">Task Management</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Create, organize, and track tasks</p>
                                    </div>
                                    <span class="px-2 py-1 text-xs rounded-full status-implemented">Implemented</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-medium">Project Management</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Manage complex projects with tasks and dependencies</p>
                                    </div>
                                    <span class="px-2 py-1 text-xs rounded-full status-in-progress">In Progress</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-medium">Notes</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Capture and organize thoughts and information</p>
                                    </div>
                                    <span class="px-2 py-1 text-xs rounded-full status-implemented">Implemented</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden mb-6">
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-4">ADHD Management</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-medium">ADHD Dashboard</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Overview of ADHD management tools</p>
                                    </div>
                                    <span class="px-2 py-1 text-xs rounded-full status-implemented">Implemented</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-medium">Symptom Tracker</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Track and analyze ADHD symptoms</p>
                                    </div>
                                    <span class="px-2 py-1 text-xs rounded-full status-implemented">Implemented</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-medium">Thought Records</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">CBT-based thought recording and analysis</p>
                                    </div>
                                    <span class="px-2 py-1 text-xs rounded-full status-implemented">Implemented</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-medium">Medication Tracker</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Monitor medication usage and effectiveness</p>
                                    </div>
                                    <span class="px-2 py-1 text-xs rounded-full status-planned">Planned</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
                            <div class="p-6">
                                <h3 class="text-xl font-semibold mb-4">New Features</h3>
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-medium">Mind Mapping</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Visual organization of thoughts and ideas</p>
                                        </div>
                                        <span class="px-2 py-1 text-xs rounded-full status-planned">Planned</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-medium">Contact Management</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Organize personal and professional contacts</p>
                                        </div>
                                        <span class="px-2 py-1 text-xs rounded-full status-planned">Planned</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-medium">Resource Directory</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Organized collection of links and tools</p>
                                        </div>
                                        <span class="px-2 py-1 text-xs rounded-full status-planned">Planned</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-medium">Workspace & Test Lab</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Sandbox for testing ideas and processes</p>
                                        </div>
                                        <span class="px-2 py-1 text-xs rounded-full status-planned">Planned</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
                            <div class="p-6">
                                <h3 class="text-xl font-semibold mb-4">Financial Management</h3>
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-medium">Expense Tracking</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Monitor spending by category</p>
                                        </div>
                                        <span class="px-2 py-1 text-xs rounded-full status-implemented">Implemented</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-medium">Budgeting</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Create and manage budgets</p>
                                        </div>
                                        <span class="px-2 py-1 text-xs rounded-full status-implemented">Implemented</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-medium">Financial Goals</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Set and track financial objectives</p>
                                        </div>
                                        <span class="px-2 py-1 text-xs rounded-full status-in-progress">In Progress</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-medium">Financial Reporting</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Analyze financial data</p>
                                        </div>
                                        <span class="px-2 py-1 text-xs rounded-full status-in-progress">In Progress</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
    
    <!-- Toggle Dark Mode Button (Mobile) -->
    <div class="fixed bottom-4 right-4 md:hidden">
        <button id="theme-toggle-mobile" class="bg-gray-200 dark:bg-gray-700 p-3 rounded-full shadow-lg">
            <i class="fas fa-moon dark:hidden"></i>
            <i class="fas fa-sun hidden dark:block"></i>
        </button>
    </div>
    
    <script>
        // Toggle dark mode
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
            localStorage.setItem('darkMode', document.documentElement.classList.contains('dark'));
        }
        
        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.documentElement.classList.add('dark');
        }
        
        // Add event listeners
        document.getElementById('theme-toggle').addEventListener('click', toggleDarkMode);
        document.getElementById('theme-toggle-mobile').addEventListener('click', toggleDarkMode);
        
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('hidden');
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
