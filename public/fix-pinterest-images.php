<?php
/**
 * Fix Pinterest Images
 *
 * This script fixes the image download issue in the Enhanced Pinterest Scraper.
 */

require_once __DIR__ . '/../src/utils/View.php';
require_once __DIR__ . '/../src/utils/AssetManager.php';
require_once __DIR__ . '/../src/utils/Session.php';
require_once __DIR__ . '/../src/utils/Environment.php';

// Start the session
Session::start();

// Check if the user is logged in
if (!Session::isLoggedIn()) {
    // Set flash message
    Session::setFlash('error', 'Please log in to access the Pinterest Image Fixer');
    
    // Redirect to login page
    header('Location: /momentum/login');
    exit;
}

// Load environment variables
Environment::load();

// Get Python path from environment variables
$pythonPath = Environment::get('PYTHON_PATH');

// Handle form submission
$fixResults = null;
$imageUrls = [];
$downloadedImages = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'fix_images':
                // Get the pin data
                $pinData = isset($_POST['pin_data']) ? $_POST['pin_data'] : '';
                
                if (!empty($pinData)) {
                    // Create the output directory
                    $outputDir = __DIR__ . '/uploads/pinterest';
                    if (!file_exists($outputDir)) {
                        mkdir($outputDir, 0777, true);
                    }
                    
                    // Save the pin data to a temporary file
                    $tempFile = $outputDir . '/temp_pin_data_' . time() . '.json';
                    file_put_contents($tempFile, $pinData);
                    
                    // Build the command
                    $scriptPath = __DIR__ . '/../scripts/pinterest/fix_image_download.py';
                    $command = escapeshellcmd($pythonPath ?: 'python') . ' ' . 
                               escapeshellarg($scriptPath) . 
                               ' --input ' . escapeshellarg($tempFile) . 
                               ' --output_dir ' . escapeshellarg($outputDir);
                    
                    // Execute the command
                    $output = [];
                    $returnVar = 0;
                    exec($command . ' 2>&1', $output, $returnVar);
                    
                    // Process the output
                    $fixResults = implode("\n", $output);
                    
                    // Extract image URLs from pin data
                    $pins = json_decode($pinData, true);
                    if (is_array($pins)) {
                        foreach ($pins as $pin) {
                            if (isset($pin['image_url']) && !empty($pin['image_url'])) {
                                $imageUrls[] = $pin['image_url'];
                            }
                        }
                    }
                    
                    // Extract downloaded image paths
                    foreach ($output as $line) {
                        if (strpos($line, '"success": true') !== false && strpos($line, '"path":') !== false) {
                            if (preg_match('/"path": "(.+?)"/', $line, $matches)) {
                                $imagePath = $matches[1];
                                
                                // Convert to web path
                                $relativePath = str_replace('\\', '/', $imagePath);
                                $relativePath = str_replace($_SERVER['DOCUMENT_ROOT'], '', $relativePath);
                                $webPath = '/momentum' . substr($relativePath, strpos($relativePath, '/uploads'));
                                
                                $downloadedImages[] = $webPath;
                            }
                        }
                    }
                    
                    // Remove the temporary file
                    if (file_exists($tempFile)) {
                        unlink($tempFile);
                    }
                }
                break;
                
            case 'fix_single_image':
                // Get the image URL
                $imageUrl = isset($_POST['image_url']) ? $_POST['image_url'] : '';
                
                if (!empty($imageUrl)) {
                    // Create the output directory
                    $outputDir = __DIR__ . '/uploads/pinterest';
                    if (!file_exists($outputDir)) {
                        mkdir($outputDir, 0777, true);
                    }
                    
                    // Build the command
                    $scriptPath = __DIR__ . '/../scripts/pinterest/fix_image_download.py';
                    $command = escapeshellcmd($pythonPath ?: 'python') . ' ' . 
                               escapeshellarg($scriptPath) . 
                               ' --image_url ' . escapeshellarg($imageUrl) . 
                               ' --output_dir ' . escapeshellarg($outputDir);
                    
                    // Execute the command
                    $output = [];
                    $returnVar = 0;
                    exec($command . ' 2>&1', $output, $returnVar);
                    
                    // Process the output
                    $fixResults = implode("\n", $output);
                    $imageUrls[] = $imageUrl;
                    
                    // Extract downloaded image path
                    foreach ($output as $line) {
                        if (strpos($line, '"success": true') !== false && strpos($line, '"path":') !== false) {
                            if (preg_match('/"path": "(.+?)"/', $line, $matches)) {
                                $imagePath = $matches[1];
                                
                                // Convert to web path
                                $relativePath = str_replace('\\', '/', $imagePath);
                                $relativePath = str_replace($_SERVER['DOCUMENT_ROOT'], '', $relativePath);
                                $webPath = '/momentum' . substr($relativePath, strpos($relativePath, '/uploads'));
                                
                                $downloadedImages[] = $webPath;
                            }
                        }
                    }
                }
                break;
        }
    }
}

// Page title
$pageTitle = 'Fix Pinterest Images';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    <link rel="stylesheet" href="/momentum/css/fontawesome.css">
    <script src="/momentum/js/alpine.js" defer></script>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold mb-2">Fix Pinterest Images</h1>
            <p class="text-gray-600 dark:text-gray-400">
                This tool fixes the image download issue in the Enhanced Pinterest Scraper.
            </p>
        </div>
        
        <!-- Fix Multiple Images Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Fix Multiple Images</h2>
            
            <form method="post" class="mb-4">
                <input type="hidden" name="action" value="fix_images">
                
                <div class="mb-4">
                    <label for="pin_data" class="block mb-2 font-medium">Pin Data (JSON):</label>
                    <textarea id="pin_data" name="pin_data" rows="10" class="w-full px-4 py-2 border rounded dark:bg-gray-700 dark:border-gray-600 font-mono text-sm"></textarea>
                    <p class="text-sm text-gray-500 mt-1">Paste the JSON data from your Pinterest scrape here.</p>
                </div>
                
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                    <i class="fas fa-download mr-2"></i> Fix and Download Images
                </button>
            </form>
        </div>
        
        <!-- Fix Single Image Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Fix Single Image</h2>
            
            <form method="post" class="mb-4">
                <input type="hidden" name="action" value="fix_single_image">
                
                <div class="mb-4">
                    <label for="image_url" class="block mb-2 font-medium">Image URL:</label>
                    <input type="text" id="image_url" name="image_url" class="w-full px-4 py-2 border rounded dark:bg-gray-700 dark:border-gray-600">
                    <p class="text-sm text-gray-500 mt-1">Enter the URL of the Pinterest image to download.</p>
                </div>
                
                <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                    <i class="fas fa-image mr-2"></i> Download Single Image
                </button>
            </form>
        </div>
        
        <!-- Results -->
        <?php if ($fixResults): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold mb-4">Results</h2>
                
                <?php if (!empty($downloadedImages)): ?>
                    <div class="mb-4 p-4 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded">
                        <p class="font-bold"><i class="fas fa-check-circle mr-2"></i> Successfully downloaded <?= count($downloadedImages) ?> images!</p>
                    </div>
                    
                    <div class="mb-4">
                        <h3 class="font-bold mb-2">Downloaded Images:</h3>
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            <?php foreach ($downloadedImages as $imagePath): ?>
                                <div class="bg-gray-100 dark:bg-gray-700 rounded overflow-hidden">
                                    <img src="<?= htmlspecialchars($imagePath) ?>" alt="Downloaded Pinterest Image" class="w-full h-40 object-cover">
                                    <div class="p-2">
                                        <a href="<?= htmlspecialchars($imagePath) ?>" target="_blank" class="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                                            <i class="fas fa-external-link-alt mr-1"></i> View Full Size
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="mb-4 p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded">
                        <p class="font-bold"><i class="fas fa-times-circle mr-2"></i> Failed to download images.</p>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($imageUrls)): ?>
                    <div class="mb-4">
                        <h3 class="font-bold mb-2">Original Image URLs:</h3>
                        <ul class="list-disc pl-6 space-y-1">
                            <?php foreach ($imageUrls as $url): ?>
                                <li>
                                    <a href="<?= htmlspecialchars($url) ?>" target="_blank" class="text-blue-600 dark:text-blue-400 hover:underline break-all">
                                        <?= htmlspecialchars($url) ?>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <div class="mb-4">
                    <h3 class="font-bold mb-2">Output:</h3>
                    <pre class="bg-gray-100 dark:bg-gray-700 p-4 rounded overflow-x-auto text-sm"><?= htmlspecialchars($fixResults) ?></pre>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Navigation Links -->
        <div class="mt-8 text-center">
            <a href="/momentum/test-enhanced-pinterest-scraper.php" class="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mr-4">
                <i class="fas fa-vial mr-2"></i> Back to Test Page
            </a>
            <a href="/momentum/clone/pinterest" class="inline-block px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i> Back to Pinterest Clone
            </a>
        </div>
    </div>
    
    <script>
        // Simple script to enhance the user experience
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-fill the pin data textarea with the example if it's empty
            const pinDataTextarea = document.getElementById('pin_data');
            if (pinDataTextarea && pinDataTextarea.value === '') {
                // Check if there's a URL parameter with pin data
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.has('pin_data')) {
                    pinDataTextarea.value = decodeURIComponent(urlParams.get('pin_data'));
                }
            }
        });
    </script>
</body>
</html>
