<?php
/**
 * Productivity Navigation
 * 
 * This file provides a simple navigation bar for all productivity tools.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple session handling
session_start();

// Get current page from URL
$currentUrl = $_SERVER['REQUEST_URI'];
$currentPage = '';

if (strpos($currentUrl, 'focus-timer') !== false) {
    $currentPage = 'focus-timer';
} elseif (strpos($currentUrl, 'focus-mode') !== false) {
    $currentPage = 'focus-mode';
} elseif (strpos($currentUrl, 'time-blocking') !== false) {
    $currentPage = 'time-blocking';
} elseif (strpos($currentUrl, 'energy-tracking') !== false) {
    $currentPage = 'energy-tracking';
} elseif (strpos($currentUrl, 'task-batching') !== false) {
    $currentPage = 'task-batching';
} elseif (strpos($currentUrl, 'batch-templates') !== false) {
    $currentPage = 'batch-templates';
} elseif (strpos($currentUrl, 'distraction-journal') !== false) {
    $currentPage = 'distraction-journal';
}

// Define navigation items
$navItems = [
    [
        'id' => 'dashboard',
        'title' => 'Tools Dashboard',
        'icon' => 'th-large',
        'link' => '/momentum/productivity_dashboard.php',
        'status' => 'implemented'
    ],
    [
        'id' => 'focus-timer',
        'title' => 'Focus Timer',
        'icon' => 'stopwatch',
        'link' => '/momentum/productivity/focus-timer',
        'status' => 'implemented'
    ],
    [
        'id' => 'focus-mode',
        'title' => 'Focus Mode',
        'icon' => 'expand',
        'link' => '/momentum/productivity/focus-mode',
        'status' => 'implemented'
    ],
    [
        'id' => 'time-blocking',
        'title' => 'Time Blocking',
        'icon' => 'calendar-alt',
        'link' => '/momentum/productivity/time-blocking',
        'status' => 'in-progress'
    ],
    [
        'id' => 'energy-tracking',
        'title' => 'Energy Tracking',
        'icon' => 'bolt',
        'link' => '/momentum/productivity/energy-tracking',
        'status' => 'planned'
    ],
    [
        'id' => 'task-batching',
        'title' => 'Task Batching',
        'icon' => 'layer-group',
        'link' => '/momentum/productivity/task-batching',
        'status' => 'concept'
    ],
    [
        'id' => 'batch-templates',
        'title' => 'Batch Templates',
        'icon' => 'copy',
        'link' => '/momentum/productivity/batch-templates',
        'status' => 'implemented'
    ],
    [
        'id' => 'distraction-journal',
        'title' => 'Distraction Journal',
        'icon' => 'book',
        'link' => '#',
        'status' => 'concept'
    ]
];

// Status colors
$statusColors = [
    'implemented' => 'green',
    'in-progress' => 'blue',
    'planned' => 'yellow',
    'concept' => 'gray'
];
?>

<div class="bg-white dark:bg-gray-800 shadow-md rounded-lg mb-6">
    <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-medium text-gray-900 dark:text-white">Productivity Tools</h2>
    </div>
    <div class="p-2">
        <div class="flex overflow-x-auto space-x-2 pb-2">
            <?php foreach ($navItems as $item): ?>
                <a href="<?= $item['link'] ?>" class="flex-shrink-0 px-3 py-2 rounded-md <?= $currentPage === $item['id'] ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300' ?>">
                    <div class="flex items-center">
                        <i class="fas fa-<?= $item['icon'] ?> mr-2"></i>
                        <span><?= $item['title'] ?></span>
                        <?php if ($item['id'] !== 'dashboard'): ?>
                            <span class="ml-2 w-2 h-2 rounded-full bg-<?= $statusColors[$item['status']] ?>-500"></span>
                        <?php endif; ?>
                    </div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</div>
