/**
 * Help Center Styles
 */

/* Status indicators */
.status-implemented {
    background-color: #10b981;
    color: white;
}

.status-in-progress {
    background-color: #f59e0b;
    color: white;
}

.status-planned {
    background-color: #8b5cf6;
    color: white;
}

.status-concept {
    background-color: #6b7280;
    color: white;
}

/* Sidebar */
.sidebar {
    height: calc(100vh - 4rem);
    overflow-y: auto;
}

/* Main content */
.main-content {
    height: calc(100vh - 4rem);
    overflow-y: auto;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.dark ::-webkit-scrollbar-track {
    background: #374151;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Card hover effects */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark .card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
}

/* Section titles */
.section-title {
    position: relative;
    padding-left: 1rem;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: #0ea5e9;
    border-radius: 2px;
}

.dark .section-title::before {
    background-color: #38bdf8;
}

/* Feature grid */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

@media (max-width: 640px) {
    .feature-grid {
        grid-template-columns: 1fr;
    }
}

/* Tutorial cards */
.tutorial-card {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.tutorial-card .thumbnail {
    position: relative;
    padding-top: 56.25%; /* 16:9 aspect ratio */
    background-color: #f3f4f6;
    border-radius: 0.5rem;
    overflow: hidden;
}

.dark .tutorial-card .thumbnail {
    background-color: #1f2937;
}

.tutorial-card .thumbnail i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    color: #9ca3af;
}

.dark .tutorial-card .thumbnail i {
    color: #4b5563;
}

.tutorial-card .duration {
    position: absolute;
    bottom: 0.5rem;
    right: 0.5rem;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

/* FAQ accordion */
.faq-item {
    border-bottom: 1px solid #e5e7eb;
}

.dark .faq-item {
    border-bottom: 1px solid #374151;
}

.faq-item:last-child {
    border-bottom: none;
}

.faq-question {
    cursor: pointer;
    padding: 1rem 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.faq-answer {
    padding-bottom: 1rem;
    display: none;
}

.faq-answer.active {
    display: block;
}

/* Troubleshooting */
.troubleshooting-item {
    border-left: 4px solid #0ea5e9;
    padding-left: 1rem;
    margin-bottom: 1.5rem;
}

.troubleshooting-solutions {
    list-style-type: disc;
    padding-left: 1.5rem;
    margin-top: 0.5rem;
}

.troubleshooting-solutions li {
    margin-bottom: 0.25rem;
}

/* Contact support form */
.contact-form .form-group {
    margin-bottom: 1rem;
}

.contact-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.contact-form input,
.contact-form textarea,
.contact-form select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: white;
}

.dark .contact-form input,
.dark .contact-form textarea,
.dark .contact-form select {
    background-color: #1f2937;
    border-color: #374151;
    color: white;
}

.contact-form input:focus,
.contact-form textarea:focus,
.contact-form select:focus {
    outline: none;
    ring: 2px;
    ring-color: #0ea5e9;
    border-color: #0ea5e9;
}

.contact-form button {
    background-color: #0ea5e9;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
}

.contact-form button:hover {
    background-color: #0284c7;
}

/* Feature request */
.feature-request-form textarea {
    min-height: 150px;
}

/* Search box */
.search-box {
    position: relative;
}

.search-box input {
    width: 100%;
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: white;
}

.dark .search-box input {
    background-color: #1f2937;
    border-color: #374151;
    color: white;
}

.search-box i {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
}

.dark .search-box i {
    color: #4b5563;
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: 1.5rem;
    right: 1.5rem;
    background-color: #0ea5e9;
    color: white;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.back-to-top.visible {
    opacity: 1;
}

.back-to-top:hover {
    background-color: #0284c7;
}
