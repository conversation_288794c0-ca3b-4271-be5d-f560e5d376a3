/**
 * AI Prompts System CSS
 *
 * Enhanced styles for the AI prompt management interface
 */

/* ===== Base Styles ===== */

.ai-prompt-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* ===== Enhanced Layout ===== */

.ai-prompts-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2rem;
    padding: 4rem 2rem;
    text-align: center;
    margin-bottom: 3rem;
    color: white;
}

.ai-prompts-hero h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ai-prompts-hero p {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto 2rem;
}

/* ===== Improved Statistics Cards ===== */

.stats-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card-enhanced {
    background: white;
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f5f9;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card-enhanced .stat-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.stat-card-enhanced .stat-value {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-card-enhanced .stat-label {
    font-size: 1rem;
    color: #64748b;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== Enhanced Prompt Cards ===== */

.prompt-card {
    background: white;
    border-radius: 1.25rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f5f9;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.prompt-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.prompt-card.favorite {
    border-color: #fbbf24;
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.prompt-card.template {
    border-color: #8b5cf6;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.prompt-card-header {
    padding: 1.5rem 1.5rem 1rem;
}

.prompt-card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.prompt-card-description {
    font-size: 0.95rem;
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.prompt-card-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.875rem;
    color: #64748b;
    gap: 1rem;
}

.prompt-card-body {
    padding: 0 1.5rem 1rem;
}

.prompt-card-footer {
    padding: 1rem 1.5rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* ===== Improved Typography ===== */

.text-enhanced {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
}

.heading-enhanced {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
}

/* ===== Better Spacing ===== */

.section-spacing {
    margin-bottom: 3rem;
}

.content-spacing {
    margin-bottom: 2rem;
}

.element-spacing {
    margin-bottom: 1rem;
}

/* ===== Category Badges ===== */

.category-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
    text-decoration: none;
    transition: opacity 0.2s ease;
}

.category-badge:hover {
    opacity: 0.8;
}

.category-badge i {
    margin-right: 0.25rem;
    font-size: 0.625rem;
}

/* ===== Action Buttons ===== */

.prompt-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.prompt-action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
    background: white;
    color: #6b7280;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.prompt-action-btn:hover {
    background: #f3f4f6;
    color: #374151;
    border-color: #9ca3af;
}

.prompt-action-btn.favorite {
    color: #fbbf24;
    border-color: #fbbf24;
}

.prompt-action-btn.favorite:hover {
    background: #fffbeb;
}

.prompt-action-btn.execute {
    background: #8b5cf6;
    color: white;
    border-color: #8b5cf6;
}

.prompt-action-btn.execute:hover {
    background: #7c3aed;
    border-color: #7c3aed;
}

/* ===== Prompt Builder ===== */

.prompt-builder {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.prompt-builder-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.prompt-builder-body {
    padding: 1.5rem;
}

.prompt-textarea {
    width: 100%;
    min-height: 200px;
    padding: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.prompt-textarea:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.variable-input {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background: #f9fafb;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
}

.variable-input input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.variable-input button {
    padding: 0.5rem;
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.variable-input button:hover {
    background: #dc2626;
}

/* ===== Prompt Execution ===== */

.prompt-execution {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
}

.execution-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
    border-radius: 0.75rem 0.75rem 0 0;
}

.execution-body {
    padding: 1.5rem;
}

.execution-result {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-top: 1rem;
}

.execution-result.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100px;
    color: #6b7280;
}

.execution-result.error {
    background: #fef2f2;
    border-color: #fecaca;
    color: #dc2626;
}

.execution-result.success {
    background: #f0fdf4;
    border-color: #bbf7d0;
}

/* ===== Statistics Cards ===== */

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    text-align: center;
}

.stat-card-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.25rem;
}

.stat-card-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.stat-card-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

/* ===== Filters and Search ===== */

.filter-bar {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.search-input {
    flex: 1;
    min-width: 250px;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.filter-select {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    background: white;
    min-width: 150px;
}

/* ===== Dark Mode Support ===== */

.dark .prompt-card {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
}

.dark .prompt-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark .prompt-card-title {
    color: #f9fafb;
}

.dark .prompt-card-description {
    color: #d1d5db;
}

.dark .prompt-card-footer {
    background: #374151;
    border-color: #4b5563;
}

.dark .prompt-builder {
    background: #1f2937;
    border-color: #374151;
}

.dark .prompt-textarea {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

.dark .prompt-textarea:focus {
    border-color: #8b5cf6;
}

.dark .variable-input {
    background: #374151;
    border-color: #4b5563;
}

.dark .execution-result {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

.dark .stat-card {
    background: #1f2937;
    border-color: #374151;
}

.dark .stat-card-value {
    color: #f9fafb;
}

.dark .filter-bar {
    background: #1f2937;
    border-color: #374151;
}

.dark .search-input,
.dark .filter-select {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

/* ===== Enhanced Responsive Design ===== */

@media (max-width: 1024px) {
    .ai-prompt-container {
        padding: 1.5rem;
    }

    .ai-prompts-hero {
        padding: 3rem 1.5rem;
    }

    .ai-prompts-hero h1 {
        font-size: 2.5rem;
    }

    .stats-enhanced {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .ai-prompt-container {
        padding: 1rem;
    }

    .ai-prompts-hero {
        padding: 2rem 1rem;
        border-radius: 1rem;
    }

    .ai-prompts-hero h1 {
        font-size: 2rem;
    }

    .ai-prompts-hero p {
        font-size: 1rem;
    }

    .prompt-card-header {
        padding: 1rem 1rem 0.75rem;
    }

    .prompt-card-body {
        padding: 0 1rem 0.75rem;
    }

    .prompt-card-footer {
        padding: 0.75rem 1rem;
    }

    .prompt-card-title {
        font-size: 1.125rem;
    }

    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .search-input {
        min-width: auto;
    }

    .stats-enhanced {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card-enhanced {
        padding: 1.5rem;
    }

    .stat-card-enhanced .stat-value {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .ai-prompt-container {
        padding: 0.75rem;
    }

    .ai-prompts-hero {
        padding: 1.5rem 0.75rem;
    }

    .ai-prompts-hero h1 {
        font-size: 1.75rem;
    }

    .prompt-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .prompt-card {
        border-radius: 1rem;
    }

    .stat-card-enhanced {
        padding: 1rem;
        border-radius: 1rem;
    }
}

/* ===== Focus and Accessibility Improvements ===== */

.prompt-card:focus-within {
    outline: 2px solid #8b5cf6;
    outline-offset: 2px;
}

.prompt-action-btn:focus {
    outline: 2px solid #8b5cf6;
    outline-offset: 2px;
}

/* ===== Animation Enhancements ===== */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-delay-1 {
    animation-delay: 0.1s;
}

.animate-delay-2 {
    animation-delay: 0.2s;
}

.animate-delay-3 {
    animation-delay: 0.3s;
}

/* ===== Utility Classes ===== */

.text-balance {
    text-wrap: balance;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
