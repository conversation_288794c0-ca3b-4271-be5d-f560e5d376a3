/**
 * Fixed Dropdown Menu CSS
 * 
 * This CSS file provides styles for dropdown menus with smooth transitions
 * and no flickering.
 */

/* Dropdown overlay */
.dropdown-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9998 !important;
    background-color: transparent !important;
    display: none !important;
    pointer-events: none !important;
}

.dropdown-overlay.active {
    display: block !important;
    pointer-events: auto !important;
}

/* Dropdown menu container */
.dropdown-menu {
    position: fixed !important;
    z-index: 9999 !important;
    background-color: white !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    min-width: 14rem !important;
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: opacity 0.15s ease-in-out !important;
    padding: 0.5rem 0 !important;
}

/* Dark mode for dropdown menu */
.dark .dropdown-menu {
    background-color: #374151 !important; /* dark:bg-gray-700 */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.15) !important;
}

/* Dropdown menu items */
.dropdown-menu a {
    display: block !important;
    padding: 0.5rem 1rem !important;
    color: #4B5563 !important; /* text-gray-700 */
    font-size: 0.875rem !important; /* text-sm */
    white-space: nowrap !important;
    width: 100% !important;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    transition: background-color 0.15s ease-in-out !important;
}

/* Dark mode for menu items */
.dark .dropdown-menu a {
    color: #E5E7EB !important; /* dark:text-gray-200 */
}

/* Hover state for menu items */
.dropdown-menu a:hover {
    background-color: #F3F4F6 !important; /* hover:bg-gray-100 */
}

/* Dark mode hover state for menu items */
.dark .dropdown-menu a:hover {
    background-color: #4B5563 !important; /* dark:hover:bg-gray-600 */
}

/* Ensure dropdown containers have position relative */
#adhd-dropdown,
#productivity-dropdown,
#tools-dropdown,
.relative {
    position: relative !important;
}

/* Ensure dropdown buttons have proper cursor */
#adhd-dropdown-button,
#productivity-dropdown-button,
#tools-dropdown-button,
#user-menu-button {
    cursor: pointer !important;
}

/* Fix for z-index stacking context */
.bg-white.dark\:bg-gray-800.shadow {
    z-index: 50 !important;
    position: relative !important;
}

/* Add a small gap between button and menu for better hover behavior */
.dropdown-menu::before {
    content: '' !important;
    position: absolute !important;
    top: -10px !important;
    left: 0 !important;
    right: 0 !important;
    height: 10px !important;
    background-color: transparent !important;
}

/* Ensure smooth transitions for all dropdown interactions */
.dropdown-menu,
.dropdown-menu a,
#adhd-dropdown-button,
#productivity-dropdown-button,
#tools-dropdown-button,
#user-menu-button {
    transition: all 0.15s ease-in-out !important;
}
