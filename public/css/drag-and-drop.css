/**
 * Drag and Drop Styles
 *
 * Styles for the custom layout drag and drop functionality
 */

/* Draggable widget styles */
[data-widget].draggable {
    cursor: move;
    user-select: none;
}

/* Widget being dragged */
[data-widget].dragging {
    opacity: 0.5 !important;
    border: 2px dashed rgba(139, 92, 246, 0.5) !important;
    background-color: rgba(139, 92, 246, 0.05) !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    z-index: 1000 !important;
}

/* Widget drag over state */
[data-widget].drag-over {
    border-color: rgba(139, 92, 246, 0.8) !important;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.3) !important;
    transform: translateY(-3px);
    transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
}

/* Drag handle styles */
.widget-drag-handle {
    position: absolute !important;
    top: 10px !important;
    right: 10px !important;
    color: rgba(139, 92, 246, 0.7) !important;
    cursor: move !important;
    padding: 5px !important;
    z-index: 5 !important;
    transition: color 0.2s ease !important;
    width: 24px !important;
    height: 24px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 4px !important;
}

.widget-drag-handle:hover {
    color: rgba(139, 92, 246, 1) !important;
    background-color: rgba(139, 92, 246, 0.1) !important;
}

.widget-drag-handle:active {
    background-color: rgba(139, 92, 246, 0.2) !important;
}

/* Custom layout specific styles */
#dashboard-widgets[data-view-mode="custom"] {
    position: relative !important;
    min-height: 300px !important;
    transition: background-color 0.3s ease !important;
}

#dashboard-widgets[data-view-mode="custom"] [data-widget] {
    transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease, opacity 0.2s ease !important;
}

/* SortableJS classes */
.sortable-ghost {
    opacity: 0.3 !important;
    background-color: rgba(139, 92, 246, 0.1) !important;
    border: 2px dashed rgba(139, 92, 246, 0.5) !important;
}

.sortable-chosen {
    background-color: rgba(139, 92, 246, 0.05) !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    z-index: 1000 !important;
}

.sortable-drag {
    opacity: 0.8 !important;
    transform: rotate(2deg) !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

.sortable-fallback {
    opacity: 0.8 !important;
    transform: rotate(2deg) !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    border: 2px solid rgba(139, 92, 246, 0.7) !important;
    background-color: white !important;
    z-index: 1000 !important;
}

/* Drop indicator */
.drop-indicator {
    height: 4px !important;
    background-color: rgba(139, 92, 246, 0.8) !important;
    position: absolute !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 10 !important;
    pointer-events: none !important;
    border-radius: 2px !important;
    box-shadow: 0 0 5px rgba(139, 92, 246, 0.5) !important;
    animation: pulse 1.5s infinite !important;
}

@keyframes pulse {
    0% { opacity: 0.8; }
    50% { opacity: 1; }
    100% { opacity: 0.8; }
}

/* Saved message animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOutDown {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(20px);
    }
}

#arrangement-saved-message,
.drag-drop-ready-message,
.sortable-ready-message {
    animation: fadeInUp 0.3s ease forwards, fadeOutDown 0.3s ease 2.7s forwards !important;
    z-index: 9999 !important;
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    background-color: rgba(139, 92, 246, 0.9) !important;
    color: white !important;
    padding: 10px 15px !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

/* Make sure widgets are visible in custom mode */
#dashboard-widgets[data-view-mode="custom"] [data-widget] {
    display: block !important;
    visibility: visible !important;
}

/* Styles for when dragging is in progress */
body.sortable-dragging {
    cursor: grabbing !important;
}

/* Container receiving a drag */
#dashboard-widgets.receiving-drag {
    background-color: rgba(139, 92, 246, 0.1) !important;
    transition: background-color 0.3s ease !important;
}

/* Improve drag handle visibility */
.widget-drag-handle {
    opacity: 0.7 !important;
    transition: opacity 0.2s ease, transform 0.2s ease !important;
}

.widget-drag-handle:hover {
    opacity: 1 !important;
    transform: scale(1.1) !important;
}

/* Add a hint for users */
#dashboard-widgets[data-view-mode="custom"]::before {
    content: "Drag widgets using the handle to rearrange them" !important;
    display: block !important;
    text-align: center !important;
    padding: 0.5rem !important;
    margin-bottom: 1rem !important;
    background-color: rgba(139, 92, 246, 0.1) !important;
    border-radius: 0.25rem !important;
    color: rgba(139, 92, 246, 1) !important;
    font-size: 0.875rem !important;
}
