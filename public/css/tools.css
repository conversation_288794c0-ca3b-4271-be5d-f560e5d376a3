/* Tools Dashboard Styles */

.tools-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Tool Cards */
.tool-card {
    display: block;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.tool-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
}

.tool-card-placeholder {
    background: #f9fafb;
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    transition: all 0.2s ease;
}

.tool-card-placeholder:hover {
    border-color: #9ca3af;
    background: #f3f4f6;
}

.tool-card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    transition: all 0.2s ease;
}

.tool-card-icon i {
    font-size: 20px;
}

.tool-card-title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 8px;
    line-height: 1.3;
}

.tool-card-description {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 16px;
    line-height: 1.4;
}

.tool-card-stats {
    display: flex;
    align-items: center;
    gap: 12px;
}

.stat-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #6b7280;
    gap: 4px;
}

.stat-item i {
    font-size: 10px;
}

/* Stats Cards */
.stat-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    transition: all 0.2s ease;
}

.stat-card:hover {
    border-color: #d1d5db;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.stat-card-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
}

.stat-card-icon i {
    font-size: 14px;
}

.stat-card-value {
    font-size: 24px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 4px;
}

.stat-card-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
    .tool-card {
        background: #1f2937;
        border-color: #374151;
        color: #f9fafb;
    }

    .tool-card:hover {
        border-color: #4b5563;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }

    .tool-card-placeholder {
        background: #1f2937;
        border-color: #374151;
    }

    .tool-card-placeholder:hover {
        border-color: #4b5563;
        background: #111827;
    }

    .tool-card-title {
        color: #f9fafb;
    }

    .tool-card-description {
        color: #9ca3af;
    }

    .stat-item {
        color: #9ca3af;
    }

    .stat-card {
        background: #1f2937;
        border-color: #374151;
    }

    .stat-card:hover {
        border-color: #4b5563;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .stat-card-value {
        color: #f9fafb;
    }

    .stat-card-label {
        color: #9ca3af;
    }
}

/* Dark mode class-based (for explicit dark mode) */
.dark .tool-card {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
}

.dark .tool-card:hover {
    border-color: #4b5563;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.dark .tool-card-placeholder {
    background: #1f2937;
    border-color: #374151;
}

.dark .tool-card-placeholder:hover {
    border-color: #4b5563;
    background: #111827;
}

.dark .tool-card-title {
    color: #f9fafb;
}

.dark .tool-card-description {
    color: #9ca3af;
}

.dark .stat-item {
    color: #9ca3af;
}

.dark .stat-card {
    background: #1f2937;
    border-color: #374151;
}

.dark .stat-card:hover {
    border-color: #4b5563;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.dark .stat-card-value {
    color: #f9fafb;
}

.dark .stat-card-label {
    color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tools-container {
        padding: 16px;
    }

    .tool-card {
        padding: 20px;
    }

    .tool-card-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 12px;
    }

    .tool-card-icon i {
        font-size: 18px;
    }

    .tool-card-title {
        font-size: 16px;
    }

    .tool-card-description {
        font-size: 13px;
    }

    .stat-card {
        padding: 12px;
    }

    .stat-card-value {
        font-size: 20px;
    }
}

/* Animation for loading states */
.tool-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.tool-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #e5e7eb;
    border-top-color: #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Focus states for accessibility */
.tool-card:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.tool-card:focus:not(:focus-visible) {
    outline: none;
}

/* Keyboard navigation */
.tool-card:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Capture Gallery Styles */
.capture-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.capture-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
    position: relative;
}

.capture-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
}

.capture-card-header {
    padding: 16px;
    border-bottom: 1px solid #f3f4f6;
    position: relative;
}

.capture-card-title {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 8px;
    line-height: 1.3;
}

.capture-card-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #6b7280;
}

.capture-type-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

.capture-type-badge.screenshot {
    background: #dbeafe;
    color: #1d4ed8;
}

.capture-type-badge.note {
    background: #dcfce7;
    color: #166534;
}

.capture-type-badge.voice {
    background: #fed7aa;
    color: #c2410c;
}

.pin-indicator {
    position: absolute;
    top: 12px;
    right: 12px;
    color: #f59e0b;
    font-size: 14px;
}

.capture-card-body {
    padding: 16px;
}

.capture-preview {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
}

.capture-content {
    font-size: 14px;
    color: #4b5563;
    line-height: 1.5;
}

.capture-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
}

.capture-tag {
    padding: 2px 8px;
    background: #f3f4f6;
    color: #6b7280;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.capture-card-footer {
    padding: 16px;
    border-top: 1px solid #f3f4f6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.capture-actions {
    display: flex;
    gap: 8px;
}

.capture-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #f3f4f6;
    color: #6b7280;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.capture-action-btn:hover {
    background: #e5e7eb;
    color: #374151;
}

.capture-action-btn.pin {
    background: #fef3c7;
    color: #d97706;
}

.capture-action-btn.pin:hover {
    background: #fde68a;
    color: #b45309;
}

.capture-action-btn.delete:hover {
    background: #fee2e2;
    color: #dc2626;
}

/* Dark Mode for Gallery */
.dark .capture-card {
    background: #1f2937;
    border-color: #374151;
}

.dark .capture-card:hover {
    border-color: #4b5563;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.dark .capture-card-header {
    border-bottom-color: #374151;
}

.dark .capture-card-title {
    color: #f9fafb;
}

.dark .capture-card-meta {
    color: #9ca3af;
}

.dark .capture-type-badge.screenshot {
    background: #1e3a8a;
    color: #93c5fd;
}

.dark .capture-type-badge.note {
    background: #14532d;
    color: #86efac;
}

.dark .capture-type-badge.voice {
    background: #9a3412;
    color: #fdba74;
}

.dark .capture-content {
    color: #d1d5db;
}

.dark .capture-tag {
    background: #374151;
    color: #9ca3af;
}

.dark .capture-card-footer {
    border-top-color: #374151;
}

.dark .capture-action-btn {
    background: #374151;
    color: #9ca3af;
}

.dark .capture-action-btn:hover {
    background: #4b5563;
    color: #d1d5db;
}

.dark .capture-action-btn.pin {
    background: #92400e;
    color: #fbbf24;
}

.dark .capture-action-btn.pin:hover {
    background: #b45309;
    color: #f59e0b;
}

.dark .capture-action-btn.delete:hover {
    background: #7f1d1d;
    color: #f87171;
}

/* Responsive Gallery */
@media (max-width: 768px) {
    .capture-gallery {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .capture-card-header,
    .capture-card-body,
    .capture-card-footer {
        padding: 12px;
    }

    .capture-preview {
        height: 120px;
    }
}
