/* Astrology Dashboard Styles */

.astrology-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    padding: 0;
    margin: 0;
}

/* Header Styles */
.astrology-header {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(139, 92, 246, 0.2);
    padding: 2rem 0;
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.header-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.title-gradient {
    font-size: 2.5rem;
    font-weight: bold;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
}

.header-subtitle {
    color: #667eea;
    font-size: 1rem;
    margin: 0;
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Hero Section */
.hero-section {
    text-align: center;
    color: white;
    margin-bottom: 1rem;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-description {
    font-size: 1.125rem;
    max-width: 600px;
    margin: 0 auto;
    opacity: 0.9;
    line-height: 1.6;
}

/* Alert Styles */
.alert {
    padding: 1rem;
    border-radius: 0.75rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.alert-warning {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border: 1px solid #f59e0b;
    color: #92400e;
}

.alert-success {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0);
    border: 1px solid #10b981;
    color: #065f46;
}

.alert-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.alert-content i {
    font-size: 1.25rem;
    margin-top: 0.125rem;
}

.rahu-kalaya-alert {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Card Styles */
.today-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.today-card, .weekly-schedule, .important-note {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.night-card {
    background: linear-gradient(135deg, rgba(30, 27, 75, 0.95), rgba(76, 29, 149, 0.95));
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.night-card .card-title {
    color: #e0e7ff;
}

.night-card .time-label {
    color: #c7d2fe;
}

.night-card .time-value {
    color: #e0e7ff;
}

.card-header, .schedule-header {
    margin-bottom: 1.5rem;
}

.card-title, .schedule-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4c1d95;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

.schedule-subtitle {
    color: #6b7280;
    margin: 0.5rem 0 0 0;
    font-size: 0.875rem;
}

/* Time Display */
.time-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.time-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.time-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.time-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4c1d95;
    font-family: 'Courier New', monospace;
}

/* Status Indicator */
.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 500;
    font-size: 0.875rem;
}

.status-indicator.active {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #f59e0b;
}

.status-indicator.inactive {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #10b981;
}

.status-indicator i {
    font-size: 0.5rem;
}

/* Schedule Grid */
.schedule-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
}

.schedule-item {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 0.75rem;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.schedule-item:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.schedule-item.current-day {
    background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
    border-color: #6366f1;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

.day-name {
    font-weight: bold;
    color: #4c1d95;
    margin-bottom: 0.5rem;
    font-size: 1.125rem;
}

.day-time {
    color: #6b7280;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

.current-day-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #6366f1;
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-weight: 500;
}

/* Important Note */
.note-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    color: #f59e0b;
    font-weight: bold;
}

.important-note p {
    color: #6b7280;
    line-height: 1.6;
    margin: 0;
}

/* Quick Actions */
.quick-actions {
    text-align: center;
}

.actions-title {
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    max-width: 600px;
    margin: 0 auto;
}

.action-card {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 0.75rem;
    padding: 1rem;
    text-decoration: none;
    color: #4c1d95;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.action-card:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: #4c1d95;
}

.action-card i {
    font-size: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .title-gradient {
        font-size: 2rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .time-display {
        flex-direction: column;
        align-items: flex-start;
    }

    .schedule-grid {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
