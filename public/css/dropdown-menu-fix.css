/**
 * Dropdown Menu Fix CSS
 *
 * This CSS file provides styles for dropdown menus to ensure they're properly displayed.
 */

/* Base styles for dropdown menus */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 99999; /* Very high z-index to ensure it's above everything */
    background-color: white;
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    min-width: 14rem;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

/* Active state for dropdown menus */
.dropdown-menu.active {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Menu items */
.dropdown-item {
    display: block;
    padding: 0.75rem 1rem;
    color: #4B5563;
    font-size: 0.875rem;
    text-decoration: none;
    white-space: nowrap;
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    position: relative;
    z-index: 100000;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Last menu item (no border) */
.dropdown-menu .dropdown-item:last-child {
    border-bottom: none !important;
}

/* Hover state for menu items */
.dropdown-item:hover {
    background-color: #F3F4F6;
    color: #111827;
}

/* Dark mode styles */
.dark .dropdown-menu {
    background-color: #374151;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
}

.dark .dropdown-item {
    color: #E5E7EB;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dark .dropdown-item:hover {
    background-color: #4B5563;
    color: #F9FAFB;
}

/* Dropdown overlay */
.dropdown-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 50 !important;
    background-color: transparent !important;
    display: none !important;
}

.dropdown-overlay.active {
    display: block !important;
}

/* Specific styles for ADHD dropdown */
#adhd-dropdown-menu {
    z-index: 100000; /* Even higher z-index for ADHD menu */
}

/* User dropdown menu positioning */
#user-dropdown-menu {
    right: 0;
    left: auto;
}

/* ADHD dropdown menu items - now using the dropdown-item class */
.dark #adhd-dropdown-menu {
    background-color: #374151;
}

/* Specific styles for dropdown buttons */
button[id$="-dropdown-button"] {
    cursor: pointer !important;
    position: relative !important;
    z-index: 10 !important;
}

/* Fix for dropdown button hover state */
button[id$="-dropdown-button"]:hover {
    opacity: 0.9 !important;
}

/* Fix for dropdown button active state */
button[id$="-dropdown-button"][aria-expanded="true"] {
    opacity: 0.8 !important;
}

/* Fix for dropdown menu positioning */
.relative {
    position: relative !important;
}

/* Dropdown hover styles removed - now handled by JavaScript */

/* Ensure dropdown menus have proper stacking context */
.dropdown-menu {
    isolation: isolate !important;
}
