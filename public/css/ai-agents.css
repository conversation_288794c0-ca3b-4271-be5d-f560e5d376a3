/**
 * AI Agents Army Styles
 *
 * ADHD-friendly styles for the AI Agents Army feature
 */

/* Agent Card Styles */
.agent-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border-left: 4px solid transparent;
}

.agent-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.agent-card.research {
    border-left-color: #3B82F6; /* blue-500 */
}

.agent-card.creative {
    border-left-color: #EC4899; /* pink-500 */
}

.agent-card.productivity {
    border-left-color: #10B981; /* green-500 */
}

.agent-card.technical {
    border-left-color: #6366F1; /* indigo-500 */
}

.agent-card.communication {
    border-left-color: #F59E0B; /* amber-500 */
}

/* Agent Status Indicators */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-active {
    background-color: #10B981; /* green-500 */
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.status-inactive {
    background-color: #9CA3AF; /* gray-400 */
}

.status-training {
    background-color: #F59E0B; /* amber-500 */
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.status-error {
    background-color: #EF4444; /* red-500 */
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

/* Agent Skill Badges */
.skill-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.skill-badge.analytical {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3B82F6;
}

.dark .skill-badge.analytical {
    background-color: rgba(59, 130, 246, 0.2);
    color: #93C5FD;
}

.skill-badge.creative {
    background-color: rgba(236, 72, 153, 0.1);
    color: #EC4899;
}

.dark .skill-badge.creative {
    background-color: rgba(236, 72, 153, 0.2);
    color: #F9A8D4;
}

.skill-badge.technical {
    background-color: rgba(99, 102, 241, 0.1);
    color: #6366F1;
}

.dark .skill-badge.technical {
    background-color: rgba(99, 102, 241, 0.2);
    color: #A5B4FC;
}

.skill-badge.communication {
    background-color: rgba(245, 158, 11, 0.1);
    color: #F59E0B;
}

.dark .skill-badge.communication {
    background-color: rgba(245, 158, 11, 0.2);
    color: #FCD34D;
}

.skill-badge.research {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10B981;
}

.dark .skill-badge.research {
    background-color: rgba(16, 185, 129, 0.2);
    color: #6EE7B7;
}

.skill-badge.automation {
    background-color: rgba(139, 92, 246, 0.1);
    color: #8B5CF6;
}

.dark .skill-badge.automation {
    background-color: rgba(139, 92, 246, 0.2);
    color: #C4B5FD;
}

/* Agent Rating Sliders */
.agent-rating-slider {
    -webkit-appearance: none;
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #E5E7EB; /* gray-200 */
    outline: none;
}

.dark .agent-rating-slider {
    background: #4B5563; /* gray-600 */
}

.agent-rating-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #6366F1; /* indigo-500 */
    cursor: pointer;
    transition: background 0.2s ease;
}

.dark .agent-rating-slider::-webkit-slider-thumb {
    background: #818CF8; /* indigo-400 */
}

.agent-rating-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #6366F1; /* indigo-500 */
    cursor: pointer;
    transition: background 0.2s ease;
    border: none;
}

.dark .agent-rating-slider::-moz-range-thumb {
    background: #818CF8; /* indigo-400 */
}

.agent-rating-slider:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

.agent-rating-slider:focus::-moz-range-thumb {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

/* Intelligence slider */
.intelligence-slider::-webkit-slider-thumb {
    background: #3B82F6; /* blue-500 */
}

.dark .intelligence-slider::-webkit-slider-thumb {
    background: #60A5FA; /* blue-400 */
}

.intelligence-slider::-moz-range-thumb {
    background: #3B82F6; /* blue-500 */
}

.dark .intelligence-slider::-moz-range-thumb {
    background: #60A5FA; /* blue-400 */
}

.intelligence-slider:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.intelligence-slider:focus::-moz-range-thumb {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Efficiency slider */
.efficiency-slider::-webkit-slider-thumb {
    background: #10B981; /* green-500 */
}

.dark .efficiency-slider::-webkit-slider-thumb {
    background: #34D399; /* green-400 */
}

.efficiency-slider::-moz-range-thumb {
    background: #10B981; /* green-500 */
}

.dark .efficiency-slider::-moz-range-thumb {
    background: #34D399; /* green-400 */
}

.efficiency-slider:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
}

.efficiency-slider:focus::-moz-range-thumb {
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
}

/* Reliability slider */
.reliability-slider::-webkit-slider-thumb {
    background: #F59E0B; /* amber-500 */
}

.dark .reliability-slider::-webkit-slider-thumb {
    background: #FBBF24; /* amber-400 */
}

.reliability-slider::-moz-range-thumb {
    background: #F59E0B; /* amber-500 */
}

.dark .reliability-slider::-moz-range-thumb {
    background: #FBBF24; /* amber-400 */
}

.reliability-slider:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.3);
}

.reliability-slider:focus::-moz-range-thumb {
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.3);
}

/* ADHD-friendly focus styles */
.ai-agents-container a:focus,
.ai-agents-container button:focus,
.ai-agents-container input:focus,
.ai-agents-container select:focus,
.ai-agents-container textarea:focus {
    outline: 2px solid #6366F1; /* indigo-500 */
    outline-offset: 2px;
}

.dark .ai-agents-container a:focus,
.dark .ai-agents-container button:focus,
.dark .ai-agents-container input:focus,
.dark .ai-agents-container select:focus,
.dark .ai-agents-container textarea:focus {
    outline-color: #818CF8; /* indigo-400 */
}

/* Animations for ADHD-friendly visual feedback */
@keyframes pulse-border {
    0% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(99, 102, 241, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
    }
}

.pulse-on-hover:hover {
    animation: pulse-border 1.5s infinite;
}

/* Dashboard widget specific styles */
.dashboard-widgets [data-widget="ai-agents-widget"] {
    border-left: 4px solid #6366F1; /* indigo-500 */
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    background: linear-gradient(to right, rgba(99, 102, 241, 0.05), transparent);
}

.dashboard-widgets [data-widget="ai-agents-widget"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    background: linear-gradient(to right, rgba(99, 102, 241, 0.1), transparent);
}

/* Pulsing animation for the AI Agents widget to draw attention */
@keyframes subtle-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.2);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
    }
}

/* Apply the animation only on initial page load */
.dashboard-widgets [data-widget="ai-agents-widget"] {
    animation: subtle-pulse 2s ease-in-out 1;
}
