/**
 * Simple Drag and Drop Styles
 */

/* Draggable widgets */
[data-widget] {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* Widget being dragged */
[data-widget].dragging {
    opacity: 0.6 !important;
    border: 2px dashed #8b5cf6 !important;
    background-color: rgba(139, 92, 246, 0.05) !important;
}

/* Drag handle */
.widget-drag-handle {
    position: absolute !important;
    top: 10px !important;
    right: 10px !important;
    color: rgba(139, 92, 246, 0.7) !important;
    cursor: move !important;
    padding: 5px !important;
    z-index: 5 !important;
    background-color: rgba(255, 255, 255, 0.5) !important;
    border-radius: 4px !important;
    width: 24px !important;
    height: 24px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.widget-drag-handle:hover {
    color: rgba(139, 92, 246, 1) !important;
    background-color: rgba(139, 92, 246, 0.1) !important;
}

/* Custom layout container */
#dashboard-widgets[data-view-mode="custom"] {
    position: relative !important;
    min-height: 300px !important;
    padding: 20px !important;
    background-color: rgba(139, 92, 246, 0.05) !important;
    border-radius: 8px !important;
    border: 2px dashed rgba(139, 92, 246, 0.3) !important;
}

/* Custom layout widgets */
#dashboard-widgets[data-view-mode="custom"] [data-widget] {
    margin-bottom: 16px !important;
    border: 2px solid rgba(139, 92, 246, 0.2) !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    background-color: white !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

/* When dragging is active */
body.dragging-active #dashboard-widgets[data-view-mode="custom"] [data-widget]:not(.dragging):hover {
    border-top: 2px solid #8b5cf6 !important;
    transform: translateY(2px) !important;
}

/* Message styles */
.drag-drop-message {
    animation: fadeInOut 3s ease forwards !important;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(20px); }
    10% { opacity: 1; transform: translateY(0); }
    90% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(20px); }
}

/* Instructions */
#dashboard-widgets[data-view-mode="custom"]::before {
    content: "Drag widgets using the handle to rearrange them" !important;
    display: block !important;
    text-align: center !important;
    padding: 10px !important;
    margin-bottom: 20px !important;
    background-color: rgba(139, 92, 246, 0.1) !important;
    border-radius: 4px !important;
    color: #8b5cf6 !important;
    font-weight: bold !important;
}
