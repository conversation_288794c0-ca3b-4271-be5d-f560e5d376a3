/**
 * Quick Capture System CSS
 * 
 * Styles for the screenshot, note, and voice capture interface
 */

/* ===== Base Styles ===== */

.quick-capture-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

/* ===== Capture Cards ===== */

.capture-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.capture-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.capture-card.pinned {
    border-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.capture-card.screenshot {
    border-left: 4px solid #3b82f6;
}

.capture-card.note {
    border-left: 4px solid #10b981;
}

.capture-card.voice {
    border-left: 4px solid #f59e0b;
}

.capture-card.mixed {
    border-left: 4px solid #8b5cf6;
}

/* ===== Capture Card Components ===== */

.capture-card-header {
    padding: 1rem 1.25rem 0.5rem;
    position: relative;
}

.capture-card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
    line-height: 1.4;
    padding-right: 2rem;
}

.capture-card-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #9ca3af;
    margin-bottom: 0.5rem;
}

.capture-type-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.625rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.capture-type-badge.screenshot {
    background: #dbeafe;
    color: #1e40af;
}

.capture-type-badge.note {
    background: #d1fae5;
    color: #065f46;
}

.capture-type-badge.voice {
    background: #fef3c7;
    color: #92400e;
}

.capture-type-badge.mixed {
    background: #ede9fe;
    color: #5b21b6;
}

.pin-indicator {
    position: absolute;
    top: 0.75rem;
    right: 1rem;
    color: #f59e0b;
    font-size: 0.875rem;
}

/* ===== Capture Content ===== */

.capture-card-body {
    padding: 0 1.25rem 1rem;
}

.capture-preview {
    width: 100%;
    border-radius: 0.5rem;
    margin-bottom: 0.75rem;
    max-height: 200px;
    object-fit: cover;
    border: 1px solid #e5e7eb;
}

.capture-content {
    font-size: 0.875rem;
    color: #4b5563;
    line-height: 1.5;
    margin-bottom: 0.75rem;
    max-height: 4.5rem;
    overflow: hidden;
    position: relative;
}

.capture-content.expanded {
    max-height: none;
}

.capture-content::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1rem;
    background: linear-gradient(transparent, white);
    pointer-events: none;
}

.capture-content.expanded::after {
    display: none;
}

.capture-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
}

.capture-tag {
    display: inline-block;
    padding: 0.125rem 0.375rem;
    background: #f3f4f6;
    color: #6b7280;
    border-radius: 0.25rem;
    font-size: 0.625rem;
    font-weight: 500;
}

/* ===== Capture Actions ===== */

.capture-card-footer {
    padding: 0.75rem 1.25rem;
    background: #f9fafb;
    border-top: 1px solid #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: between;
}

.capture-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: auto;
}

.capture-action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
    background: white;
    color: #6b7280;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.75rem;
    cursor: pointer;
}

.capture-action-btn:hover {
    background: #f3f4f6;
    color: #374151;
    border-color: #9ca3af;
}

.capture-action-btn.pin {
    color: #f59e0b;
    border-color: #f59e0b;
}

.capture-action-btn.pin:hover {
    background: #fffbeb;
}

.capture-action-btn.delete {
    color: #ef4444;
    border-color: #ef4444;
}

.capture-action-btn.delete:hover {
    background: #fef2f2;
}

/* ===== Gallery Grid ===== */

.capture-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.capture-gallery.list-view {
    grid-template-columns: 1fr;
}

.capture-gallery.grid-view {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* ===== Screenshot Tool ===== */

.screenshot-tool {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.screenshot-controls {
    position: fixed;
    top: 1rem;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 0.5rem;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    gap: 1rem;
    z-index: 10000;
}

.screenshot-canvas {
    cursor: crosshair;
    position: absolute;
    top: 0;
    left: 0;
}

.selection-area {
    position: absolute;
    border: 2px solid #3b82f6;
    background: rgba(59, 130, 246, 0.1);
    pointer-events: none;
}

/* ===== Quick Note Widget ===== */

.quick-note-widget {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 300px;
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e5e7eb;
    z-index: 1000;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.quick-note-widget.open {
    transform: translateY(0);
}

.quick-note-header {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.quick-note-body {
    padding: 1rem;
}

.quick-note-textarea {
    width: 100%;
    min-height: 120px;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    padding: 0.75rem;
    font-size: 0.875rem;
    resize: none;
    margin-bottom: 0.75rem;
}

.quick-note-footer {
    padding: 0.75rem 1rem;
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
    border-radius: 0 0 0.75rem 0.75rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* ===== Voice Recording ===== */

.voice-recorder {
    background: white;
    border-radius: 0.75rem;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e5e7eb;
    max-width: 400px;
    margin: 0 auto;
}

.record-button {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    border: none;
    background: #ef4444;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 1rem;
}

.record-button:hover {
    background: #dc2626;
    transform: scale(1.05);
}

.record-button.recording {
    background: #10b981;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.recording-timer {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.waveform-visualization {
    height: 60px;
    background: #f3f4f6;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
}

/* ===== OCR Results ===== */

.ocr-results {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
}

.ocr-text {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    color: #374151;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
}

.ocr-confidence {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.5rem;
}

/* ===== Dark Mode Support ===== */

.dark .capture-card {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
}

.dark .capture-card-title {
    color: #f9fafb;
}

.dark .capture-content {
    color: #d1d5db;
}

.dark .capture-card-footer {
    background: #374151;
    border-color: #4b5563;
}

.dark .capture-tag {
    background: #374151;
    color: #d1d5db;
}

.dark .quick-note-widget {
    background: #1f2937;
    border-color: #374151;
}

.dark .quick-note-textarea {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

.dark .quick-note-footer {
    background: #374151;
    border-color: #4b5563;
}

.dark .voice-recorder {
    background: #1f2937;
    border-color: #374151;
}

.dark .recording-timer {
    color: #f9fafb;
}

.dark .waveform-visualization {
    background: #374151;
    color: #9ca3af;
}

.dark .ocr-results {
    background: #374151;
    border-color: #4b5563;
}

.dark .ocr-text {
    color: #d1d5db;
}

/* ===== Responsive Design ===== */

@media (max-width: 768px) {
    .quick-capture-container {
        padding: 0.5rem;
    }
    
    .capture-gallery {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .quick-note-widget {
        bottom: 1rem;
        right: 1rem;
        left: 1rem;
        width: auto;
    }
    
    .screenshot-controls {
        left: 1rem;
        right: 1rem;
        transform: none;
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .capture-card-header {
        padding: 0.75rem 1rem 0.5rem;
    }
    
    .capture-card-body {
        padding: 0 1rem 0.75rem;
    }
    
    .capture-card-footer {
        padding: 0.5rem 1rem;
    }
    
    .capture-actions {
        gap: 0.25rem;
    }
}
