/**
 * Dashboard Layout Styles
 *
 * This stylesheet provides different layout options for the dashboard based on the selected view mode.
 * It uses data-view-mode and data-arrangement attributes to apply different layouts.
 */

/* ===== Base Layout Styles ===== */

/* Default grid layout */
#dashboard-widgets {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: 1fr;
}

@media (min-width: 768px) {
    #dashboard-widgets {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    #dashboard-widgets {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* ===== ADHD Optimized Layout ===== */

/* ADHD Optimized layout - prioritizes focus and reduces distractions */
#dashboard-widgets[data-view-mode="adhd-optimized"] {
    /* Space between sections */
    gap: 1.75rem;
    /* Visual indicator for ADHD optimized mode */
    background-color: rgba(14, 165, 233, 0.05);
    border-radius: 0.75rem;
    padding: 1.5rem;
    position: relative;
    overflow: visible;
}

/* Add a label to indicate the current view mode */
#dashboard-widgets[data-view-mode="adhd-optimized"]::before {
    content: "ADHD Optimized Layout";
    position: absolute;
    top: -10px;
    left: 20px;
    background-color: #0ea5e9;
    color: white;
    padding: 2px 10px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 10;
}

/* Make Current Focus widget full width in all screen sizes */
#dashboard-widgets[data-view-mode="adhd-optimized"] #current-focus-widget {
    grid-column: 1 / -1;
    border-left-width: 6px !important;
    transform: scale(1.02);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

/* Make Today's Tasks widget wider on larger screens */
@media (min-width: 768px) {
    #dashboard-widgets[data-view-mode="adhd-optimized"] [data-widget="today-tasks"] {
        grid-column: span 1;
    }
}

@media (min-width: 1024px) {
    #dashboard-widgets[data-view-mode="adhd-optimized"] [data-widget="today-tasks"] {
        grid-column: span 2;
    }
}

/* Add a subtle left border to all widgets in ADHD mode */
#dashboard-widgets[data-view-mode="adhd-optimized"] > div {
    border-left: 4px solid transparent;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

#dashboard-widgets[data-view-mode="adhd-optimized"] > div:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

#dashboard-widgets[data-view-mode="adhd-optimized"] [data-widget="today-tasks"] {
    border-left-color: rgba(59, 130, 246, 0.8); /* blue */
}

#dashboard-widgets[data-view-mode="adhd-optimized"] [data-widget="overdue-tasks"] {
    border-left-color: rgba(239, 68, 68, 0.8); /* red */
}

#dashboard-widgets[data-view-mode="adhd-optimized"] [data-widget="keyboard-shortcuts"] {
    border-left-color: rgba(139, 92, 246, 0.8); /* purple */
}

#dashboard-widgets[data-view-mode="adhd-optimized"] [data-widget="adhd-guide"] {
    border-left-color: rgba(16, 185, 129, 0.8); /* green */
}

#dashboard-widgets[data-view-mode="adhd-optimized"] [data-widget="help-center"] {
    border-left-color: rgba(245, 158, 11, 0.8); /* amber */
}

/* ===== Focus Mode Layout ===== */

/* Focus mode - shows only essential widgets */
#dashboard-widgets[data-view-mode="focus"] {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0.75rem;
    padding: 1.5rem;
    position: relative;
    overflow: visible;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Add a label to indicate the current view mode */
#dashboard-widgets[data-view-mode="focus"]::before {
    content: "Focus Mode";
    position: absolute;
    top: -10px;
    left: 20px;
    background-color: #6366f1; /* indigo-500 */
    color: white;
    padding: 2px 10px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 10;
}

/* Hide non-essential widgets in focus mode */
#dashboard-widgets[data-view-mode="focus"] > div:not(#current-focus-widget):not([data-widget="today-tasks"]):not([data-widget="keyboard-shortcuts"]) {
    display: none;
}

#dashboard-widgets[data-view-mode="focus"] #current-focus-widget {
    grid-column: 1 / -1;
    order: -1; /* Always show at the top */
}

/* Add a dramatic highlight to the current focus widget in focus mode */
#dashboard-widgets[data-view-mode="focus"] #current-focus-widget {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    border-left-width: 8px !important;
    transform: scale(1.03);
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 0.75rem;
}

.dark #dashboard-widgets[data-view-mode="focus"] #current-focus-widget {
    background-color: rgba(31, 41, 55, 0.8); /* dark:bg-gray-800 with opacity */
}

/* Make the today's tasks widget more prominent in focus mode */
#dashboard-widgets[data-view-mode="focus"] [data-widget="today-tasks"] {
    grid-column: 1 / -1;
    border-left: 5px solid rgba(59, 130, 246, 0.8);
    order: 0; /* Show after current focus */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Style the keyboard shortcuts widget in focus mode */
#dashboard-widgets[data-view-mode="focus"] [data-widget="keyboard-shortcuts"] {
    grid-column: 1 / -1;
    border-left: 5px solid rgba(139, 92, 246, 0.8);
    order: 1; /* Show after today's tasks */
    max-height: 200px;
    overflow-y: auto;
}

/* ===== Standard Layout ===== */

/* Standard layout - balanced grid layout */
#dashboard-widgets[data-view-mode="standard"] {
    gap: 1rem;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 0.75rem;
    padding: 1.25rem;
    position: relative;
    overflow: visible;
}

/* Add a label to indicate the current view mode */
#dashboard-widgets[data-view-mode="standard"]::before {
    content: "Standard Layout";
    position: absolute;
    top: -10px;
    left: 20px;
    background-color: #10b981; /* emerald-500 */
    color: white;
    padding: 2px 10px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 10;
}

#dashboard-widgets[data-view-mode="standard"] #current-focus-widget {
    grid-column: 1 / -1;
    border-left-width: 4px;
}

/* Equal sizing for all widgets in standard view */
#dashboard-widgets[data-view-mode="standard"] > div:not(#current-focus-widget) {
    min-height: 200px;
    border-radius: 0.5rem;
    transition: transform 0.2s ease;
}

/* Subtle hover effect for widgets in standard view */
#dashboard-widgets[data-view-mode="standard"] > div:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Add subtle borders to widgets in standard view */
#dashboard-widgets[data-view-mode="standard"] > div {
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.dark #dashboard-widgets[data-view-mode="standard"] > div {
    border: 1px solid rgba(255, 255, 255, 0.05);
}

/* ===== Custom Layout ===== */

/* Custom layout - user-defined arrangement */
#dashboard-widgets[data-view-mode="custom"] {
    /* Custom layout styles will be applied via JavaScript */
    background-color: rgba(139, 92, 246, 0.05); /* Light purple background */
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 2px dashed rgba(139, 92, 246, 0.3);
    position: relative;
    overflow: visible;
}

/* Add a label to indicate the current view mode */
#dashboard-widgets[data-view-mode="custom"]::before {
    content: "Custom Layout";
    position: absolute;
    top: -10px;
    left: 20px;
    background-color: #8b5cf6; /* violet-500 */
    color: white;
    padding: 2px 10px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 10;
}

/* Add a subtle dotted border to widgets in custom mode to indicate they're movable */
#dashboard-widgets[data-view-mode="custom"] > div {
    border: 2px dotted rgba(139, 92, 246, 0.3);
    cursor: move;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
}

/* Add a drag handle icon to indicate widgets are draggable */
#dashboard-widgets[data-view-mode="custom"] > div::before {
    content: "⋮⋮";
    position: absolute;
    top: 10px;
    right: 10px;
    color: rgba(139, 92, 246, 0.5);
    font-size: 16px;
    line-height: 1;
}

#dashboard-widgets[data-view-mode="custom"] > div:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border-color: rgba(139, 92, 246, 0.6);
}

/* Show widget controls only in custom view */
#widget-controls {
    display: none;
}

#dashboard-widgets[data-view-mode="custom"] ~ #widget-controls {
    display: block;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: rgba(139, 92, 246, 0.05);
    border-radius: 0.5rem;
    border: 2px dashed rgba(139, 92, 246, 0.3);
}

/* ===== Widget Transition Effects ===== */

/* Smooth transitions when changing layouts */
#dashboard-widgets > div {
    transition: all 0.3s ease-in-out;
}

/* ===== Focus Mode Body Class Effects ===== */

/* When body has focus-mode class, reduce opacity of non-essential elements */
body.focus-mode #dashboard-widgets > div:not(#current-focus-widget):not([data-widget="today-tasks"]):not([data-widget="keyboard-shortcuts"]) {
    opacity: 0.5;
    filter: grayscale(30%);
}
