/**
 * Hover Fix CSS for Dropdown Menus
 * 
 * This CSS file provides styles for dropdown menus with smooth transitions
 * to prevent flickering.
 */

/* Ensure dropdown menu has smooth transitions */
#adhd-dropdown-menu {
    transition: opacity 0.15s ease-in-out !important;
    padding: 0.5rem 0 !important;
}

/* Add a small gap between button and menu for better hover behavior */
#adhd-dropdown-menu::before {
    content: '' !important;
    position: absolute !important;
    top: -10px !important;
    left: 0 !important;
    right: 0 !important;
    height: 10px !important;
    background-color: transparent !important;
}

/* Ensure dropdown container has position relative */
#adhd-dropdown {
    position: relative !important;
}

/* Ensure dropdown button has proper cursor */
#adhd-dropdown-button {
    cursor: pointer !important;
}
