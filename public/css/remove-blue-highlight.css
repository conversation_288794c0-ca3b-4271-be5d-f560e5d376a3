/**
 * Remove Blue Highlight CSS
 *
 * This stylesheet removes the blue highlighting effect from task items
 * in the task list view.
 */

/* Global override for blue highlights */
.bg-primary-50,
.dark\:bg-primary-900,
.border-l-4.border-primary-500,
.task-item,
.task-row {
    background-color: transparent !important;
    border-left: none !important;
    padding-left: initial !important;
}

/* Simple hover effect */
.task-item:hover,
.task-row:hover {
    background-color: rgba(243, 244, 246, 0.2) !important;
}

.dark .task-item:hover,
.dark .task-row:hover {
    background-color: rgba(55, 65, 81, 0.2) !important;
}

/* Ensure transitions are smooth */
.task-item,
.task-row {
    transition: background-color 0.15s ease !important;
}
