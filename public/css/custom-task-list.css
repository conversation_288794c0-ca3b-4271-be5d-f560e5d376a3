/**
 * Custom Task List CSS
 * ADHD-friendly enhancements for the task list view
 * Optimized for performance
 */

/* Task Group Headers */
.task-group-header {
    background-color: rgba(243, 244, 246, 0.8);
    border-left: 4px solid #6366f1;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    /* Use transform instead of background-color for better performance */
    transition: transform 0.15s ease;
    will-change: transform;
}

.dark .task-group-header {
    background-color: rgba(55, 65, 81, 0.8);
    border-left-color: #818cf8;
}

.task-group-header:hover {
    background-color: rgba(237, 238, 240, 0.9);
    transform: translateX(2px);
}

.dark .task-group-header:hover {
    background-color: rgba(75, 85, 99, 0.9);
}

.task-group-header .group-count {
    background-color: rgba(99, 102, 241, 0.1);
    color: #6366f1;
    border-radius: 9999px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
}

.dark .task-group-header .group-count {
    background-color: rgba(129, 140, 248, 0.1);
    color: #818cf8;
}

/* Task Group Content */
.task-group-content {
    /* Use height instead of max-height for better performance */
    height: auto;
    overflow: hidden;
    transition: height 0.2s ease, opacity 0.2s ease;
    will-change: height, opacity;
}

.task-group-content.collapsed {
    height: 0 !important;
    opacity: 0;
}

/* Task Group Items - container for task rows */
.task-group-items {
    display: flex;
    flex-direction: column;
}

/* Task Row Styling */
.task-row {
    /* Reduce number of properties in transition for better performance */
    transition: background-color 0.15s ease;
    border-left: 3px solid transparent;
    will-change: background-color;
}

.task-row:hover {
    background-color: rgba(243, 244, 246, 0.5);
}

.dark .task-row:hover {
    background-color: rgba(55, 65, 81, 0.5);
}

/* Priority Indicators */
.task-row.priority-urgent {
    border-left-color: #ef4444;
}

.task-row.priority-high {
    border-left-color: #f59e0b;
}

.task-row.priority-medium {
    border-left-color: #3b82f6;
}

.task-row.priority-low {
    border-left-color: #10b981;
}

/* Status Styling */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-todo {
    background-color: #9ca3af;
}

.status-in_progress {
    background-color: #3b82f6;
}

.status-done {
    background-color: #10b981;
}

/* Alternating Row Colors */
.task-row:nth-child(even) {
    background-color: rgba(249, 250, 251, 0.5);
}

.dark .task-row:nth-child(even) {
    background-color: rgba(31, 41, 55, 0.5);
}

/* Quick Filter Buttons - ADHD-friendly design with minimal distraction */
.quick-filter-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.dark .quick-filter-buttons {
    border-bottom-color: #374151;
}

.quick-filter-button {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    background-color: #f9fafb;
    color: #4b5563;
    border: 1px solid #e5e7eb;
    cursor: pointer;
    transition: background-color 0.2s ease, border-color 0.2s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quick-filter-button svg {
    width: 1rem;
    height: 1rem;
    opacity: 0.8;
    vertical-align: -0.125em;
    /* Prerender to improve performance */
    transform: translateZ(0);
    backface-visibility: hidden;
}

.dark .quick-filter-button {
    background-color: #1f2937;
    color: #e5e7eb;
    border-color: #374151;
}

/* Hover effect - extremely subtle and non-distracting */
.quick-filter-button:hover {
    background-color: #f8fafc;
    border-color: #e2e8f0;
}

.dark .quick-filter-button:hover {
    background-color: #1e293b;
    border-color: #475569;
}

/* Active state - subtle but clear distinction */
.quick-filter-button.active {
    background-color: #eef2ff; /* Very light indigo background */
    color: #4f46e5; /* Indigo text */
    border-color: #c7d2fe; /* Light indigo border */
    font-weight: 600;
}

.quick-filter-button.active svg {
    opacity: 1;
}

/* Left border indicator */
.quick-filter-button.active {
    border-left: 3px solid #4f46e5;
    padding-left: calc(1rem - 3px);
}

.dark .quick-filter-button.active {
    background-color: #312e81; /* Dark indigo background */
    color: #a5b4fc; /* Light indigo text */
    border-color: #4f46e5; /* Indigo border */
}

.dark .quick-filter-button.active {
    border-left-color: #818cf8;
}

/* Compact View Toggle */
.view-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: auto;
}

.view-toggle-label {
    font-size: 0.875rem;
    color: #4b5563;
}

.dark .view-toggle-label {
    color: #d1d5db;
}

/* Due Date Indicators */
.due-soon {
    color: #f59e0b;
}

.overdue {
    color: #ef4444;
}

/* Task Completion Animation - optimized for performance */
@keyframes taskComplete {
    0% { opacity: 1; transform: translateX(0); }
    50% { opacity: 0.7; transform: translateX(10px); }
    100% { opacity: 0.5; transform: translateX(0); }
}

.task-complete-animation {
    animation: taskComplete 0.5s ease forwards;
    will-change: opacity, transform;
}

/* Expandable Task Details - optimized for performance */
.task-details {
    height: 0;
    overflow: hidden;
    /* Use height instead of max-height for better performance */
    transition: height 0.2s ease, padding 0.2s ease;
    background-color: rgba(243, 244, 246, 0.5);
    border-left: 3px solid #6366f1;
    margin-left: 1rem;
    will-change: height, padding;
}

.dark .task-details {
    background-color: rgba(55, 65, 81, 0.5);
    border-left-color: #818cf8;
}

.task-details.expanded {
    height: auto;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
}

/* Keyboard Navigation Indicator */
.task-row:focus {
    outline: 2px solid #6366f1;
    outline-offset: -2px;
}

.dark .task-row:focus {
    outline-color: #818cf8;
}

/* Scrollbar Styling */
.task-list-container {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.task-list-container::-webkit-scrollbar {
    width: 6px;
}

.task-list-container::-webkit-scrollbar-track {
    background: transparent;
}

.task-list-container::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
}

.dark .task-list-container::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.5);
}

/* ADHD-friendly loading indicator - subtle and non-distracting */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #4f46e5;
    z-index: 9999;
    animation: loading 1.5s infinite ease-in-out;
    transform-origin: 0% 50%;
    opacity: 0.7;
}

@keyframes loading {
    0% { transform: scaleX(0); }
    50% { transform: scaleX(0.5); }
    100% { transform: scaleX(1); }
}

/* Page transition effects - extremely subtle */
body {
    transition: opacity 0.2s ease;
}

body.filtering {
    opacity: 0.98;
}

body.page-loaded {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0.95; }
    to { opacity: 1; }
}

/* Filter feedback for ADHD users - subtle and non-distracting */
.filter-feedback {
    position: fixed;
    top: 1rem;
    right: 1rem;
    background-color: rgba(255, 255, 255, 0.95);
    color: #4b5563;
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    border-left: 3px solid #4f46e5;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    transform: translateY(-5px);
    transition: opacity 0.15s ease-out, transform 0.15s ease-out;
    max-width: 200px;
    font-size: 0.875rem;
}

.filter-feedback.show {
    opacity: 1;
    transform: translateY(0);
}

.filter-feedback svg {
    color: #4f46e5;
    flex-shrink: 0;
    /* Optimize SVG rendering */
    transform: translateZ(0);
}

.filter-feedback-text {
    font-size: 0.875rem;
    font-weight: 500;
}

.dark .filter-feedback {
    background-color: rgba(31, 41, 55, 0.95);
    color: #e5e7eb;
    border-left-color: #6366f1;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
