/* Dropdown menu styles */
.dropdown-menu {
    display: none;
    position: absolute;
    z-index: 9999;
    min-width: 200px;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    padding: 0.5rem 0;
    margin-top: 8px;
    top: 100%;
    left: 0;
}

.dark .dropdown-menu {
    background-color: #374151;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
    color: #E5E7EB;
}

.dropdown-item {
    display: block;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    text-decoration: none;
    white-space: nowrap;
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    color: #4B5563;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dark .dropdown-item {
    color: #E5E7EB;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dropdown-item:hover {
    background-color: #F3F4F6;
    color: #111827;
}

.dark .dropdown-item:hover {
    background-color: #4B5563;
    color: #F9FAFB;
}

.dropdown-item:last-child {
    border-bottom: none;
}

/* Ensure parent containers have position relative */
#adhd-dropdown,
#productivity-dropdown,
#tools-dropdown,
.relative {
    position: relative !important;
}

/* Right-align user dropdown */
#user-dropdown-menu {
    right: 0 !important;
    left: auto !important;
}

/* Add arrow to dropdown menus */
.dropdown-menu::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 20px;
    width: 16px;
    height: 16px;
    background-color: inherit;
    transform: rotate(45deg);
    z-index: -1;
    box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.04);
}

/* Position arrow for user dropdown */
#user-dropdown-menu::before {
    left: auto;
    right: 20px;
}
