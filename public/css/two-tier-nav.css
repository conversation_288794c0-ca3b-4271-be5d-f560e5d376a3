/**
 * Two-Tier Navigation Bar CSS
 * 
 * ADHD-friendly navigation bar with two tiers for better organization
 * and improved visual clarity.
 */

/* Main navigation container */
.two-tier-nav {
    width: 100%;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 50;
}

.dark .two-tier-nav {
    background-color: #1f2937; /* dark:bg-gray-800 */
}

/* Top tier - contains logo and primary navigation */
.nav-tier-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5); /* gray-200 with transparency */
}

.dark .nav-tier-top {
    border-bottom-color: rgba(75, 85, 99, 0.5); /* dark:border-gray-700 with transparency */
}

/* Bottom tier - contains secondary navigation */
.nav-tier-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 1rem;
}

/* Navigation sections */
.nav-section {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Primary navigation items */
.nav-item-primary {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 500;
    font-size: 0.875rem;
    color: #4b5563; /* text-gray-600 */
    transition: all 0.15s ease;
}

.dark .nav-item-primary {
    color: #d1d5db; /* dark:text-gray-300 */
}

.nav-item-primary:hover {
    background-color: #f3f4f6; /* bg-gray-100 */
    color: #111827; /* text-gray-900 */
}

.dark .nav-item-primary:hover {
    background-color: #374151; /* dark:bg-gray-700 */
    color: #f9fafb; /* dark:text-gray-50 */
}

.nav-item-primary.active {
    background-color: #e0f2fe; /* bg-blue-50 */
    color: #0284c7; /* text-blue-600 */
}

.dark .nav-item-primary.active {
    background-color: rgba(2, 132, 199, 0.2); /* dark:bg-blue-900/20 */
    color: #38bdf8; /* dark:text-blue-300 */
}

/* Secondary navigation items */
.nav-item-secondary {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.625rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280; /* text-gray-500 */
    transition: all 0.15s ease;
}

.dark .nav-item-secondary {
    color: #9ca3af; /* dark:text-gray-400 */
}

.nav-item-secondary:hover {
    background-color: #f3f4f6; /* bg-gray-100 */
    color: #374151; /* text-gray-800 */
}

.dark .nav-item-secondary:hover {
    background-color: #374151; /* dark:bg-gray-700 */
    color: #e5e7eb; /* dark:text-gray-200 */
}

.nav-item-secondary.active {
    background-color: #e0f2fe; /* bg-blue-50 */
    color: #0284c7; /* text-blue-600 */
}

.dark .nav-item-secondary.active {
    background-color: rgba(2, 132, 199, 0.2); /* dark:bg-blue-900/20 */
    color: #38bdf8; /* dark:text-blue-300 */
}

/* Icons in navigation items */
.nav-item-primary i,
.nav-item-secondary i {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1rem;
    height: 1rem;
    margin-right: 0.375rem;
}

/* Logo section */
.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 700;
    color: #0284c7; /* text-blue-600 */
}

.dark .nav-logo {
    color: #38bdf8; /* dark:text-blue-300 */
}

.nav-logo i {
    margin-right: 0.5rem;
}

/* User section */
.nav-user {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Dividers between nav sections */
.nav-divider {
    height: 1.5rem;
    width: 1px;
    background-color: #e5e7eb; /* bg-gray-200 */
    margin: 0 0.5rem;
}

.dark .nav-divider {
    background-color: #4b5563; /* dark:bg-gray-600 */
}

/* Dropdown menus */
.nav-dropdown {
    position: relative;
}

.nav-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.25rem;
    width: 12rem;
    background-color: white;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 50;
    display: none;
}

.dark .nav-dropdown-menu {
    background-color: #1f2937; /* dark:bg-gray-800 */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.12);
}

.nav-dropdown-menu.show {
    display: block;
}

.nav-dropdown-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    color: #4b5563; /* text-gray-600 */
    font-size: 0.875rem;
}

.dark .nav-dropdown-item {
    color: #d1d5db; /* dark:text-gray-300 */
}

.nav-dropdown-item:hover {
    background-color: #f3f4f6; /* bg-gray-100 */
}

.dark .nav-dropdown-item:hover {
    background-color: #374151; /* dark:bg-gray-700 */
}

/* Mobile menu button */
.mobile-menu-button {
    display: none;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.375rem;
    color: #6b7280; /* text-gray-500 */
}

.mobile-menu-button:hover {
    background-color: #f3f4f6; /* bg-gray-100 */
    color: #111827; /* text-gray-900 */
}

.dark .mobile-menu-button:hover {
    background-color: #374151; /* dark:bg-gray-700 */
    color: #f9fafb; /* dark:text-gray-50 */
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .nav-tier-bottom {
        overflow-x: auto;
        justify-content: flex-start;
        padding-bottom: 0.5rem;
    }
    
    .nav-tier-bottom::-webkit-scrollbar {
        height: 3px;
    }
    
    .nav-tier-bottom::-webkit-scrollbar-thumb {
        background-color: rgba(156, 163, 175, 0.5); /* gray-400 with transparency */
        border-radius: 3px;
    }
    
    .dark .nav-tier-bottom::-webkit-scrollbar-thumb {
        background-color: rgba(75, 85, 99, 0.5); /* gray-600 with transparency */
    }
}

@media (max-width: 768px) {
    .mobile-menu-button {
        display: flex;
    }
    
    .nav-tier-top .nav-section:not(.nav-logo-section) {
        display: none;
    }
    
    .nav-tier-bottom {
        display: none;
    }
    
    .mobile-menu-open .nav-tier-bottom {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0.5rem 0;
    }
    
    .mobile-menu-open .nav-section {
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
    }
    
    .mobile-menu-open .nav-item-primary,
    .mobile-menu-open .nav-item-secondary {
        width: 100%;
        padding: 0.75rem 1rem;
        border-radius: 0;
    }
}
