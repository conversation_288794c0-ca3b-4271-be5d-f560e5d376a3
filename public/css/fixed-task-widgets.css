/**
 * Fixed Task Widgets CSS
 * 
 * This stylesheet provides direct styling for task widgets in the dashboard,
 * ensuring they have fixed heights and scrolling capabilities.
 */

/* Direct Task Widget Styling */
[data-widget="today-tasks"],
[data-widget="overdue-tasks"] {
    height: 400px !important;
    max-height: 400px !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Task List Container */
[data-widget="today-tasks"] .px-5,
[data-widget="overdue-tasks"] .px-5 {
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    overflow: hidden !important;
}

/* Task List Container */
.task-list-container {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
    position: relative !important;
}

/* Task Lists */
#today-tasks-list,
#overdue-tasks-list {
    max-height: 300px !important;
    overflow-y: auto !important;
    padding-right: 5px !important;
    margin-right: -5px !important;
    scrollbar-width: thin !important;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent !important;
}

/* Webkit Scrollbar Styling */
#today-tasks-list::-webkit-scrollbar,
#overdue-tasks-list::-webkit-scrollbar {
    width: 6px !important;
}

#today-tasks-list::-webkit-scrollbar-track,
#overdue-tasks-list::-webkit-scrollbar-track {
    background: transparent !important;
}

#today-tasks-list::-webkit-scrollbar-thumb,
#overdue-tasks-list::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5) !important;
    border-radius: 20px !important;
}

/* Task List Scroll Indicator */
.task-list-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(to top, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0));
    pointer-events: none;
    z-index: 10;
}

.dark .task-list-container::after {
    background: linear-gradient(to top, rgba(31, 41, 55, 0.9), rgba(31, 41, 55, 0));
}

/* Expanded Task Widget */
[data-widget="today-tasks"].expanded,
[data-widget="overdue-tasks"].expanded {
    height: 600px !important;
    max-height: 600px !important;
}

/* Expanded Task List */
[data-widget="today-tasks"].expanded #today-tasks-list,
[data-widget="overdue-tasks"].expanded #overdue-tasks-list {
    max-height: 500px !important;
}

/* Task List Expand Button */
.task-list-expand-button {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    padding: 2px 10px;
    font-size: 0.75rem;
    color: rgba(107, 114, 128, 1);
    cursor: pointer;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    z-index: 20;
}

.dark .task-list-expand-button {
    background-color: rgba(31, 41, 55, 1);
    border-color: rgba(75, 85, 99, 0.4);
    color: rgba(209, 213, 219, 1);
}

/* Focus Mode Task Widget */
#dashboard-widgets[data-view-mode="focus"] [data-widget="today-tasks"] {
    height: 500px !important;
    max-height: 500px !important;
}

#dashboard-widgets[data-view-mode="focus"] #today-tasks-list {
    max-height: 400px !important;
}

/* ADHD-Optimized Layout Adjustments */
#dashboard-widgets[data-view-mode="adhd-optimized"] [data-widget="today-tasks"],
#dashboard-widgets[data-view-mode="adhd-optimized"] [data-widget="overdue-tasks"] {
    height: 420px !important;
}

#dashboard-widgets[data-view-mode="adhd-optimized"] #today-tasks-list,
#dashboard-widgets[data-view-mode="adhd-optimized"] #overdue-tasks-list {
    max-height: 320px !important;
}

/* Standard Layout Adjustments */
#dashboard-widgets[data-view-mode="standard"] [data-widget="today-tasks"],
#dashboard-widgets[data-view-mode="standard"] [data-widget="overdue-tasks"] {
    height: 380px !important;
}

#dashboard-widgets[data-view-mode="standard"] #today-tasks-list,
#dashboard-widgets[data-view-mode="standard"] #overdue-tasks-list {
    max-height: 280px !important;
}
