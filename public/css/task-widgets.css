/**
 * Task Widgets CSS
 *
 * This stylesheet provides styling for task widgets in the dashboard,
 * including fixed heights and scrolling capabilities.
 */

/* Task Widget Container */
[data-widget="today-tasks"] .px-5,
[data-widget="overdue-tasks"] .px-5 {
    padding-bottom: 0 !important;
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Task List Container */
.task-list-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

/* Task Lists */
#today-tasks-list,
#overdue-tasks-list {
    max-height: 300px;
    height: 100%;
    overflow-y: auto;
    padding-right: 5px;
    margin-right: -5px;
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
    position: relative;
}

/* Task List Scroll Indicator */
.task-list-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(to top, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0));
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.dark .task-list-container::after {
    background: linear-gradient(to top, rgba(31, 41, 55, 0.9), rgba(31, 41, 55, 0));
}

.task-list-container.has-overflow::after {
    opacity: 1;
}

/* ADHD-Friendly Scroll Indicator Animation */
@keyframes pulse-scroll-hint {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

.task-list-container.has-more-content::after {
    animation: pulse-scroll-hint 2s ease-in-out infinite;
}

/* ADHD-Friendly Focus Indicators */
.task-item:focus-within {
    outline: 2px solid rgba(59, 130, 246, 0.5);
    outline-offset: -2px;
    position: relative;
    z-index: 5;
}

/* Expanded State Transition */
[data-widget="today-tasks"],
[data-widget="overdue-tasks"],
#today-tasks-list,
#overdue-tasks-list {
    transition: height 0.3s ease, max-height 0.3s ease;
}



/* Webkit Scrollbar Styling */
#today-tasks-list::-webkit-scrollbar,
#overdue-tasks-list::-webkit-scrollbar {
    width: 6px;
}

#today-tasks-list::-webkit-scrollbar-track,
#overdue-tasks-list::-webkit-scrollbar-track {
    background: transparent;
}

#today-tasks-list::-webkit-scrollbar-thumb,
#overdue-tasks-list::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 20px;
}

/* Task Widget Heights */
[data-widget="today-tasks"],
[data-widget="overdue-tasks"] {
    height: 400px;
    max-height: 400px;
    display: flex;
    flex-direction: column;
    transition: height 0.3s ease, max-height 0.3s ease;
}

[data-widget="today-tasks"] > div,
[data-widget="overdue-tasks"] > div {
    display: flex;
    flex-direction: column;
    flex: 1;
}

/* Expanded Task Widget */
[data-widget="today-tasks"].expanded,
[data-widget="overdue-tasks"].expanded {
    height: 600px;
    max-height: 600px;
}

/* Expanded Task List */
[data-widget="today-tasks"].expanded #today-tasks-list,
[data-widget="overdue-tasks"].expanded #overdue-tasks-list {
    max-height: 500px;
}

/* Focus Mode Task Widget */
#dashboard-widgets[data-view-mode="focus"] [data-widget="today-tasks"] {
    height: 500px;
    max-height: 500px;
}

#dashboard-widgets[data-view-mode="focus"] #today-tasks-list {
    max-height: 400px;
}

/* Empty State */
.task-list-empty {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Consistent Widget Heights for Other Widgets */
[data-widget="keyboard-shortcuts"],
[data-widget="adhd-guide"],
[data-widget="help-center"] {
    height: 300px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

[data-widget="keyboard-shortcuts"] > div,
[data-widget="adhd-guide"] > div,
[data-widget="help-center"] > div {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
}

[data-widget="keyboard-shortcuts"] .px-5,
[data-widget="adhd-guide"] .px-5,
[data-widget="help-center"] .px-5 {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
}

/* Content Areas with Scrolling */
[data-widget="keyboard-shortcuts"] .px-5 > div:last-child,
[data-widget="adhd-guide"] .space-y-3,
[data-widget="help-center"] .grid {
    flex: 1;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

/* Webkit Scrollbar Styling for Other Widgets */
[data-widget="keyboard-shortcuts"] .px-5 > div:last-child::-webkit-scrollbar,
[data-widget="adhd-guide"] .space-y-3::-webkit-scrollbar,
[data-widget="help-center"] .grid::-webkit-scrollbar {
    width: 6px;
}

[data-widget="keyboard-shortcuts"] .px-5 > div:last-child::-webkit-scrollbar-track,
[data-widget="adhd-guide"] .space-y-3::-webkit-scrollbar-track,
[data-widget="help-center"] .grid::-webkit-scrollbar-track {
    background: transparent;
}

[data-widget="keyboard-shortcuts"] .px-5 > div:last-child::-webkit-scrollbar-thumb,
[data-widget="adhd-guide"] .space-y-3::-webkit-scrollbar-thumb,
[data-widget="help-center"] .grid::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 20px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    [data-widget="today-tasks"],
    [data-widget="overdue-tasks"] {
        height: 350px;
    }

    #today-tasks-list,
    #overdue-tasks-list {
        max-height: 250px;
    }

    [data-widget="keyboard-shortcuts"],
    [data-widget="adhd-guide"],
    [data-widget="help-center"] {
        height: 250px;
    }
}

/* ADHD-Optimized Layout Adjustments */
#dashboard-widgets[data-view-mode="adhd-optimized"] [data-widget="today-tasks"],
#dashboard-widgets[data-view-mode="adhd-optimized"] [data-widget="overdue-tasks"] {
    height: 420px;
}

/* Focus Layout Adjustments */
#dashboard-widgets[data-view-mode="focus"] [data-widget="today-tasks"] {
    height: 450px;
}

#dashboard-widgets[data-view-mode="focus"] #today-tasks-list {
    max-height: 350px;
}

/* Standard Layout Adjustments */
#dashboard-widgets[data-view-mode="standard"] [data-widget="today-tasks"],
#dashboard-widgets[data-view-mode="standard"] [data-widget="overdue-tasks"] {
    height: 380px;
}

/* Custom Layout Adjustments */
#dashboard-widgets[data-view-mode="custom"] [data-widget="today-tasks"],
#dashboard-widgets[data-view-mode="custom"] [data-widget="overdue-tasks"] {
    height: 400px;
    resize: vertical;
    overflow: hidden;
}

/* Hover Effects */
[data-widget="today-tasks"]:hover,
[data-widget="overdue-tasks"]:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Task Item Hover */
.task-item:hover {
    background-color: rgba(243, 244, 246, 0.5);
}

.dark .task-item:hover {
    background-color: rgba(55, 65, 81, 0.5);
}

/* Focus Task Highlight */
.task-item.focus {
    background-color: rgba(59, 130, 246, 0.1);
    border-left: 3px solid rgba(59, 130, 246, 0.8);
    padding-left: 0.5rem;
}

.dark .task-item.focus {
    background-color: rgba(59, 130, 246, 0.2);
}

/* Scrollbar Track */
::-webkit-scrollbar-track {
    background: transparent;
}

/* Scrollbar Handle */
::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 10px;
}

/* Scrollbar Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
}

/* Scrollbar Width */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
