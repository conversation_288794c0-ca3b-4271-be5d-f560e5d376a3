<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Momentum - Comprehensive System Mockup</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        success: {
                            50: '#ecfdf5',
                            100: '#d1fae5',
                            200: '#a7f3d0',
                            300: '#6ee7b7',
                            400: '#34d399',
                            500: '#10b981',
                            600: '#059669',
                            700: '#047857',
                            800: '#065f46',
                            900: '#064e3b',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        danger: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#ef4444',
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d',
                        },
                    },
                },
            },
        }
    </script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .card {
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .dark .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
        }
        
        .section-title {
            position: relative;
            padding-left: 1rem;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background-color: #0ea5e9;
            border-radius: 2px;
        }
        
        .dark .section-title::before {
            background-color: #38bdf8;
        }
        
        /* Status indicators */
        .status-implemented {
            background-color: #10b981;
            color: white;
        }
        
        .status-in-progress {
            background-color: #f59e0b;
            color: white;
        }
        
        .status-planned {
            background-color: #8b5cf6;
            color: white;
        }
        
        .status-concept {
            background-color: #6b7280;
            color: white;
        }
        
        /* Sidebar */
        .sidebar {
            height: calc(100vh - 4rem);
            overflow-y: auto;
        }
        
        /* Main content */
        .main-content {
            height: calc(100vh - 4rem);
            overflow-y: auto;
        }
        
        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        .dark ::-webkit-scrollbar-track {
            background: #374151;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        /* Feature grid */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1rem;
        }
        
        @media (max-width: 640px) {
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white">
    <!-- Navigation Bar -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo and brand -->
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="#" class="text-xl font-bold text-primary-600 dark:text-primary-400">
                            <i class="fas fa-bolt mr-2"></i>Momentum
                        </a>
                    </div>
                </div>
                
                <!-- Desktop navigation -->
                <div class="hidden md:flex md:items-center md:space-x-4" id="desktop-navigation">
                    <nav class="flex space-x-4">
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-primary-500 text-primary-600 dark:text-primary-400">
                            <i class="fas fa-home mr-1"></i> Dashboard
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-tasks mr-1"></i> Tasks
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-project-diagram mr-1"></i> Projects
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-chart-line mr-1"></i> Reports
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-lightbulb mr-1"></i> Ideas
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-sticky-note mr-1"></i> Notes
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-dollar-sign mr-1"></i> Finances
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-question-circle mr-1"></i> Help
                        </a>
                    </nav>
                    
                    <!-- Theme toggle -->
                    <div class="ml-4 relative flex-shrink-0">
                        <button id="theme-toggle" class="bg-white dark:bg-gray-700 rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 p-2 hover:bg-gray-100 dark:hover:bg-gray-600" title="Toggle Theme">
                            <span class="sr-only">Toggle Theme</span>
                            <div class="h-5 w-5 flex items-center justify-center text-primary-600 dark:text-primary-300">
                                <i class="fas fa-moon dark:hidden"></i>
                                <i class="fas fa-sun hidden dark:block"></i>
                            </div>
                        </button>
                    </div>
                    
                    <!-- User menu -->
                    <div class="ml-4 relative flex-shrink-0">
                        <div>
                            <button type="button" id="user-menu-button" class="bg-white dark:bg-gray-700 rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <span class="sr-only">Open user menu</span>
                                <div class="h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-800 flex items-center justify-center text-primary-600 dark:text-primary-300">
                                    <i class="fas fa-user"></i>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Mobile menu button -->
                <div class="flex items-center -mr-2 md:hidden">
                    <button type="button" id="mobile-menu-button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500">
                        <span class="sr-only">Open main menu</span>
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content with Sidebar -->
    <div class="flex flex-col md:flex-row">
        <!-- Sidebar -->
        <div class="bg-white dark:bg-gray-800 w-full md:w-64 shadow md:h-screen sidebar">
            <div class="p-4">
                <h2 class="text-lg font-semibold mb-4">System Features</h2>
                <div class="space-y-2">
                    <a href="#dashboard" class="block px-3 py-2 rounded-md bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300">
                        <i class="fas fa-home mr-2"></i> Dashboard
                    </a>
                    <a href="#task-management" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-tasks mr-2"></i> Task Management
                    </a>
                    <a href="#project-management" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-project-diagram mr-2"></i> Project Management
                    </a>
                    <a href="#adhd-management" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-brain mr-2"></i> ADHD Management
                    </a>
                    <a href="#productivity" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-clock mr-2"></i> Productivity
                    </a>
                    <a href="#finances" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-dollar-sign mr-2"></i> Finances
                    </a>
                    <a href="#mind-mapping" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-sitemap mr-2"></i> Mind Mapping
                    </a>
                    <a href="#contacts" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-address-book mr-2"></i> Contacts
                    </a>
                    <a href="#resource-directory" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-link mr-2"></i> Resource Directory
                    </a>
                    <a href="#workspace" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-laptop-code mr-2"></i> Workspace
                    </a>
                    <a href="#knowledge-base" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-book mr-2"></i> Knowledge Base
                    </a>
                    <a href="#health-wellness" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-heartbeat mr-2"></i> Health & Wellness
                    </a>
                    <a href="#learning-center" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-graduation-cap mr-2"></i> Learning Center
                    </a>
                    <a href="#help-support" class="block px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-question-circle mr-2"></i> Help & Support
                    </a>
                </div>
                
                <h2 class="text-lg font-semibold mt-6 mb-4">Feature Status</h2>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <span class="w-3 h-3 rounded-full bg-success-500 mr-2"></span>
                        <span>Implemented</span>
                    </div>
                    <div class="flex items-center">
                        <span class="w-3 h-3 rounded-full bg-warning-500 mr-2"></span>
                        <span>In Progress</span>
                    </div>
                    <div class="flex items-center">
                        <span class="w-3 h-3 rounded-full bg-secondary-500 mr-2"></span>
                        <span>Planned</span>
                    </div>
                    <div class="flex items-center">
                        <span class="w-3 h-3 rounded-full bg-gray-500 mr-2"></span>
                        <span>Concept</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main content area -->
        <div class="flex-1 p-6 main-content">
            <div class="max-w-4xl mx-auto">
                <section id="dashboard" class="mb-12">
                    <h1 class="text-3xl font-bold mb-6">Momentum System Overview</h1>
                    <p class="mb-6">Momentum is an ADHD-friendly personal management system designed to help you organize your life, increase productivity, and manage ADHD symptoms effectively. This comprehensive mockup shows all current and planned features of the system.</p>
                    
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                        <h2 class="text-xl font-semibold mb-4">System Architecture</h2>
                        <img src="https://via.placeholder.com/800x400?text=System+Architecture+Diagram" alt="System Architecture Diagram" class="w-full rounded-lg mb-4">
                        <p>The Momentum system is built around a central dashboard that provides access to all features through an intuitive, ADHD-friendly interface. Features are organized into logical categories and presented as visual cards for easy navigation.</p>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <h2 class="text-xl font-semibold mb-4">Feature Categories</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div class="bg-primary-50 dark:bg-primary-900 p-4 rounded-lg border border-primary-200 dark:border-primary-700">
                                <h3 class="font-semibold text-primary-700 dark:text-primary-300">Core Features</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Dashboard, Tasks, Projects, Notes</p>
                            </div>
                            <div class="bg-secondary-50 dark:bg-secondary-900 p-4 rounded-lg border border-secondary-200 dark:border-secondary-700">
                                <h3 class="font-semibold text-secondary-700 dark:text-secondary-300">ADHD Management</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Symptom Tracking, CBT, Strategies</p>
                            </div>
                            <div class="bg-success-50 dark:bg-success-900 p-4 rounded-lg border border-success-200 dark:border-success-700">
                                <h3 class="font-semibold text-success-700 dark:text-success-300">Productivity</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Focus Timer, Time Blocking, Energy Tracking</p>
                            </div>
                            <div class="bg-warning-50 dark:bg-warning-900 p-4 rounded-lg border border-warning-200 dark:border-warning-700">
                                <h3 class="font-semibold text-warning-700 dark:text-warning-300">Financial Management</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Budgeting, Expense Tracking, Goals</p>
                            </div>
                            <div class="bg-danger-50 dark:bg-danger-900 p-4 rounded-lg border border-danger-200 dark:border-danger-700">
                                <h3 class="font-semibold text-danger-700 dark:text-danger-300">Health & Wellness</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Mood Tracking, Sleep, Exercise</p>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                <h3 class="font-semibold">Knowledge & Learning</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Mind Maps, Notes, Resources</p>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- More sections will be added for each feature category -->
            </div>
        </div>
    </div>
    
    <!-- Toggle Dark Mode Button (Mobile) -->
    <div class="fixed bottom-4 right-4 md:hidden">
        <button id="theme-toggle-mobile" class="bg-gray-200 dark:bg-gray-700 p-3 rounded-full shadow-lg">
            <i class="fas fa-moon dark:hidden"></i>
            <i class="fas fa-sun hidden dark:block"></i>
        </button>
    </div>
    
    <script>
        // Toggle dark mode
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
            localStorage.setItem('darkMode', document.documentElement.classList.contains('dark'));
        }
        
        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.documentElement.classList.add('dark');
        }
        
        // Add event listeners
        document.getElementById('theme-toggle').addEventListener('click', toggleDarkMode);
        document.getElementById('theme-toggle-mobile').addEventListener('click', toggleDarkMode);
        
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('hidden');
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
