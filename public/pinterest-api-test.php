<?php
/**
 * Pinterest API Test Page
 *
 * This page tests the Pinterest API integration with py3-pinterest.
 */

// Include necessary files
require_once __DIR__ . '/../src/utils/View.php';
require_once __DIR__ . '/../src/utils/AssetManager.php';
require_once __DIR__ . '/../src/utils/Environment.php';
require_once __DIR__ . '/../src/api/PinterestAPI.php';
require_once __DIR__ . '/../src/api/PinterestAPIFactory.php';

// Start output buffering
ob_start();

// Initialize test results
$testResults = [];

// Test 1: Check if Python is installed
$pythonPath = Environment::get('PYTHON_PATH', 'python');
$pythonTest = shell_exec($pythonPath . ' --version 2>&1');
$testResults['python'] = [
    'name' => 'Python Installation',
    'status' => strpos($pythonTest, 'Python') !== false ? 'success' : 'failure',
    'message' => $pythonTest ?: 'Python not found'
];

// Test 2: Check if py3-pinterest is installed
$py3PinterestTest = shell_exec($pythonPath . ' -c "try: import py3pin; print(\'installed\'); except ImportError: print(\'not installed\')" 2>&1');
$testResults['py3pinterest'] = [
    'name' => 'py3-pinterest Package',
    'status' => strpos($py3PinterestTest, 'installed') !== false ? 'success' : 'failure',
    'message' => strpos($py3PinterestTest, 'installed') !== false ? 'py3-pinterest is installed' : 'py3-pinterest is not installed'
];

// Test 3: Check if Chrome is installed
$chromeInstalled = false;
if (file_exists('C:/Program Files/Google/Chrome/Application/chrome.exe')) {
    $chromeInstalled = true;
    $chromeMessage = 'Chrome is installed (found in Program Files)';
} else {
    $chromeCheck = shell_exec('where chrome 2>&1');
    if ($chromeCheck && strpos($chromeCheck, 'chrome.exe') !== false) {
        $chromeInstalled = true;
        $chromeMessage = 'Chrome is installed (found in PATH)';
    } else {
        $chromeMessage = 'Chrome not found';
    }
}
$testResults['chrome'] = [
    'name' => 'Chrome Installation',
    'status' => $chromeInstalled ? 'success' : 'failure',
    'message' => $chromeMessage
];

// Test 4: Check Chrome profile path
$chromeProfile = Environment::get('CHROME_PROFILE_PATH', '');
$testResults['chrome_profile'] = [
    'name' => 'Chrome Profile Path',
    'status' => !empty($chromeProfile) && file_exists($chromeProfile) ? 'success' : 'warning',
    'message' => !empty($chromeProfile) ? (file_exists($chromeProfile) ? "Profile exists at: $chromeProfile" : "Profile path set but not found: $chromeProfile") : 'Chrome profile path not set'
];

// Test 5: Check Pinterest credentials
$email = Environment::get('PINTEREST_EMAIL', '');
$password = Environment::get('PINTEREST_PASSWORD', '');
$username = Environment::get('PINTEREST_USERNAME', '');

$credentialsSet = !empty($email) && !empty($password) && !empty($username);
$testResults['credentials'] = [
    'name' => 'Pinterest Credentials',
    'status' => $credentialsSet ? 'success' : 'failure',
    'message' => $credentialsSet ? 'Pinterest credentials are set' : 'Pinterest credentials are not set'
];

// Test 6: Test API initialization
$apiInitialized = false;
$apiMessage = '';
try {
    $api = PinterestAPIFactory::getAPI();
    if ($api !== null) {
        $apiInitialized = true;
        $apiMessage = 'Pinterest API initialized successfully';
    } else {
        $apiMessage = 'Pinterest API returned null (using fallback data)';
    }
} catch (Exception $e) {
    $apiMessage = 'Error initializing Pinterest API: ' . $e->getMessage();
}
$testResults['api_init'] = [
    'name' => 'API Initialization',
    'status' => $apiInitialized ? 'success' : 'warning',
    'message' => $apiMessage
];

// Test 7: Test login (only if API is initialized)
$loginSuccess = false;
$loginMessage = 'Login not attempted (API not initialized)';
if ($apiInitialized && $api) {
    try {
        $loginResult = $api->login();
        if ($loginResult && isset($loginResult['success']) && $loginResult['success']) {
            $loginSuccess = true;
            $loginMessage = 'Login successful';
        } else {
            $loginMessage = 'Login failed: ' . ($loginResult['message'] ?? 'Unknown error');
        }
    } catch (Exception $e) {
        $loginMessage = 'Error during login: ' . $e->getMessage();
    }
}
$testResults['login'] = [
    'name' => 'Pinterest Login',
    'status' => $loginSuccess ? 'success' : ($apiInitialized ? 'failure' : 'warning'),
    'message' => $loginMessage
];

// Test 8: Test search (only if login is successful)
$searchSuccess = false;
$searchMessage = 'Search not attempted (not logged in)';
$searchResults = [];
if ($loginSuccess && $api) {
    try {
        $searchResults = $api->search('home decor', 'pins', 5);
        if ($searchResults && is_array($searchResults) && count($searchResults) > 0) {
            $searchSuccess = true;
            $searchMessage = 'Search successful: found ' . count($searchResults) . ' results';
        } else {
            $searchMessage = 'Search returned no results';
        }
    } catch (Exception $e) {
        $searchMessage = 'Error during search: ' . $e->getMessage();
    }
}
$testResults['search'] = [
    'name' => 'Pinterest Search',
    'status' => $searchSuccess ? 'success' : ($loginSuccess ? 'failure' : 'warning'),
    'message' => $searchMessage
];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pinterest API Test</title>

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="/momentum/css/tailwind.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .test-card {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
        }
        .test-success {
            background-color: #d1fae5;
            border-left: 4px solid #10b981;
        }
        .test-failure {
            background-color: #fee2e2;
            border-left: 4px solid #ef4444;
        }
        .test-warning {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
        }
        .test-name {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .test-message {
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .search-result {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .search-result img {
            max-width: 100%;
            height: auto;
            border-radius: 0.5rem;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="test-container">
        <h1 class="text-3xl font-bold mb-6">Pinterest API Test</h1>

        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Test Results</h2>

            <?php foreach ($testResults as $test): ?>
                <div class="test-card test-<?= $test['status'] ?>">
                    <div class="test-name">
                        <?php if ($test['status'] === 'success'): ?>
                            <i class="fas fa-check-circle text-green-600 mr-2"></i>
                        <?php elseif ($test['status'] === 'failure'): ?>
                            <i class="fas fa-times-circle text-red-600 mr-2"></i>
                        <?php else: ?>
                            <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                        <?php endif; ?>
                        <?= $test['name'] ?>
                    </div>
                    <div class="test-message"><?= $test['message'] ?></div>
                </div>
            <?php endforeach; ?>
        </div>

        <?php if ($searchSuccess && !empty($searchResults)): ?>
            <div class="mb-8">
                <h2 class="text-xl font-semibold mb-4">Search Results</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php foreach (array_slice($searchResults, 0, 6) as $pin): ?>
                        <div class="search-result">
                            <?php if (isset($pin['image_url']) && !empty($pin['image_url'])): ?>
                                <img src="<?= $pin['image_url'] ?>" alt="<?= $pin['title'] ?? 'Pinterest Pin' ?>" class="mb-2">
                            <?php endif; ?>

                            <h3 class="font-semibold"><?= $pin['title'] ?? 'Untitled' ?></h3>

                            <?php if (isset($pin['description']) && !empty($pin['description'])): ?>
                                <p class="text-sm text-gray-600 mb-2"><?= substr($pin['description'], 0, 100) . (strlen($pin['description']) > 100 ? '...' : '') ?></p>
                            <?php endif; ?>

                            <div class="flex items-center text-sm text-gray-500">
                                <span class="mr-3"><i class="fas fa-thumbtack mr-1"></i> <?= $pin['save_count'] ?? 0 ?> saves</span>
                                <span><i class="fas fa-comment mr-1"></i> <?= $pin['comment_count'] ?? 0 ?> comments</span>
                            </div>

                            <?php if (isset($pin['pin_url']) && !empty($pin['pin_url'])): ?>
                                <a href="<?= $pin['pin_url'] ?>" target="_blank" class="inline-block mt-2 text-red-600 hover:text-red-800">
                                    <i class="fas fa-external-link-alt mr-1"></i> View on Pinterest
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <div class="mt-8">
            <a href="/momentum/clone/pinterest" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i> Back to Pinterest Dashboard
            </a>
        </div>
    </div>
</body>
</html>
<?php
// End output buffering and display the page
echo ob_get_clean();
?>
