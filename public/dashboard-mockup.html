<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Mockup - Momentum</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                },
            },
        }
    </script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .card {
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .dark .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
        }
        
        .section-title {
            position: relative;
            padding-left: 1rem;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background-color: #0ea5e9;
            border-radius: 2px;
        }
        
        .dark .section-title::before {
            background-color: #38bdf8;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white">
    <!-- Navigation Bar -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo and brand -->
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="#" class="text-xl font-bold text-primary-600 dark:text-primary-400">
                            <i class="fas fa-bolt mr-2"></i>Momentum
                        </a>
                    </div>
                </div>
                
                <!-- Desktop navigation -->
                <div class="hidden md:flex md:items-center md:space-x-4" id="desktop-navigation">
                    <nav class="flex space-x-4">
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-primary-500 text-primary-600 dark:text-primary-400">
                            <i class="fas fa-home mr-1"></i> Dashboard
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-tasks mr-1"></i> Tasks
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-project-diagram mr-1"></i> Projects
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-chart-line mr-1"></i> Reports
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-lightbulb mr-1"></i> Ideas
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-sticky-note mr-1"></i> Notes
                        </a>
                        <a href="#" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                            <i class="fas fa-dollar-sign mr-1"></i> Finances
                        </a>
                    </nav>
                    
                    <!-- User menu -->
                    <div class="ml-4 relative flex-shrink-0">
                        <div>
                            <button type="button" id="user-menu-button" class="bg-white dark:bg-gray-700 rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <span class="sr-only">Open user menu</span>
                                <div class="h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-800 flex items-center justify-center text-primary-600 dark:text-primary-300">
                                    <i class="fas fa-user"></i>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 class="text-3xl font-bold mb-8">Dashboard</h1>
        
        <!-- Current Focus Widget -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Current Focus</h2>
            <div class="bg-primary-50 dark:bg-primary-900 p-4 rounded-lg border border-primary-200 dark:border-primary-700">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-primary-700 dark:text-primary-300">Complete Financial Report</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Due in 2 days</p>
                    </div>
                    <button class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg">
                        Mark Complete
                    </button>
                </div>
            </div>
        </div>
        
        <!-- ADHD Tools Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold mb-4 section-title">ADHD Management</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <a href="#" class="card bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-center">
                    <div class="bg-primary-100 dark:bg-primary-900 p-3 rounded-full mr-4">
                        <i class="fas fa-tachometer-alt text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold">ADHD Dashboard</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Overview of your ADHD management</p>
                    </div>
                </a>
                <a href="#" class="card bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-center">
                    <div class="bg-primary-100 dark:bg-primary-900 p-3 rounded-full mr-4">
                        <i class="fas fa-chart-line text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold">Symptom Tracker</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Track and analyze your symptoms</p>
                    </div>
                </a>
                <a href="#" class="card bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-center">
                    <div class="bg-primary-100 dark:bg-primary-900 p-3 rounded-full mr-4">
                        <i class="fas fa-comments text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold">Thought Records</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">CBT thought recording and analysis</p>
                    </div>
                </a>
                <a href="#" class="card bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-center">
                    <div class="bg-primary-100 dark:bg-primary-900 p-3 rounded-full mr-4">
                        <i class="fas fa-lightbulb text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold">Productivity Strategies</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">ADHD-friendly productivity techniques</p>
                    </div>
                </a>
                <a href="#" class="card bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-center">
                    <div class="bg-primary-100 dark:bg-primary-900 p-3 rounded-full mr-4">
                        <i class="fas fa-spa text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold">Mindfulness Exercises</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Guided mindfulness practices</p>
                    </div>
                </a>
                <a href="#" class="card bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-center">
                    <div class="bg-primary-100 dark:bg-primary-900 p-3 rounded-full mr-4">
                        <i class="fas fa-calendar-check text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold">Consistency Trackers</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Track your daily habits and routines</p>
                    </div>
                </a>
            </div>
        </div>
        
        <!-- Productivity Tools Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold mb-4 section-title">Productivity Tools</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <a href="#" class="card bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-center">
                    <div class="bg-primary-100 dark:bg-primary-900 p-3 rounded-full mr-4">
                        <i class="fas fa-stopwatch text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold">Focus Timer</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Pomodoro and other focus techniques</p>
                    </div>
                </a>
                <a href="#" class="card bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-center">
                    <div class="bg-primary-100 dark:bg-primary-900 p-3 rounded-full mr-4">
                        <i class="fas fa-clock text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold">Focus Mode</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Distraction-free work environment</p>
                    </div>
                </a>
            </div>
        </div>
        
        <!-- Utility Tools Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold mb-4 section-title">Utility Tools</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <a href="#" class="card bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex items-center">
                    <div class="bg-primary-100 dark:bg-primary-900 p-3 rounded-full mr-4">
                        <i class="fas fa-exchange-alt text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold">Currency Converter</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Convert between different currencies</p>
                    </div>
                </a>
            </div>
        </div>
        
        <!-- Toggle Dark Mode Button -->
        <div class="fixed bottom-4 right-4">
            <button id="toggle-theme" class="bg-gray-200 dark:bg-gray-700 p-3 rounded-full shadow-lg">
                <i class="fas fa-moon dark:hidden"></i>
                <i class="fas fa-sun hidden dark:block"></i>
            </button>
        </div>
    </div>
    
    <script>
        // Toggle dark mode
        document.getElementById('toggle-theme').addEventListener('click', function() {
            document.documentElement.classList.toggle('dark');
        });
    </script>
</body>
</html>
