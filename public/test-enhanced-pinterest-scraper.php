<?php
/**
 * Test Enhanced Pinterest Scraper
 *
 * This script tests the Enhanced Pinterest Scraper functionality with real data.
 */

require_once __DIR__ . '/../src/utils/View.php';
require_once __DIR__ . '/../src/utils/AssetManager.php';
require_once __DIR__ . '/../src/utils/Session.php';
require_once __DIR__ . '/../src/utils/Environment.php';
require_once __DIR__ . '/../src/api/EnhancedPinterestAPI.php';

// Start the session
Session::start();

// Check if the user is logged in
if (!Session::isLoggedIn()) {
    // Set flash message
    Session::setFlash('error', 'Please log in to access the Enhanced Pinterest Scraper Test');

    // Redirect to login page
    header('Location: /momentum/login');
    exit;
}

// Load environment variables
Environment::load();

// Get credentials from environment variables
$email = Environment::get('PINTEREST_EMAIL');
$password = Environment::get('PINTEREST_PASSWORD');
$username = Environment::get('PINTEREST_USERNAME');
$chromeProfile = Environment::get('CHROME_PROFILE_PATH');
$pythonPath = Environment::get('PYTHON_PATH');

// Initialize the API
$api = EnhancedPinterestAPI::getInstance($email, $password, $username, $pythonPath, $chromeProfile);

// Handle form submission
$testResults = null;
$testOutput = null;
$testSuccess = false;
$downloadedImagePath = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'install_packages':
                // Install required Python packages
                $setupScriptPath = __DIR__ . '/../scripts/pinterest/setup_enhanced_scraper.py';

                if (!file_exists($setupScriptPath)) {
                    $testSuccess = false;
                    $testOutput = "Error: Setup script not found at: $setupScriptPath";
                    break;
                }

                // Build the command
                $command = escapeshellcmd($pythonPath ?: 'python') . ' ' .
                           escapeshellarg($setupScriptPath);

                // Execute the command
                $output = [];
                $returnVar = 0;
                exec($command . ' 2>&1', $output, $returnVar);

                // Process the output
                $testOutput = implode("\n", $output);
                $testSuccess = strpos($testOutput, 'Setup completed successfully') !== false;

                $testResults = [
                    'success' => $testSuccess,
                    'output' => $testOutput,
                    'image_path' => null
                ];
                break;

            case 'test':
                // Run the test command
                $query = isset($_POST['query']) ? $_POST['query'] : 'home decor';
                $limit = isset($_POST['limit']) ? (int)$_POST['limit'] : 3;

                // Execute the Python script directly
                $scriptPath = __DIR__ . '/../scripts/pinterest/enhanced_pinterest_scraper.py';

                if (!file_exists($scriptPath)) {
                    $testSuccess = false;
                    $testOutput = "Error: Enhanced Pinterest Scraper script not found at: $scriptPath";
                    break;
                }

                // Build the command
                $command = escapeshellcmd($pythonPath ?: 'python') . ' ' .
                           escapeshellarg($scriptPath) . ' test';

                // Add arguments
                if ($email && $password) {
                    $command .= ' --email ' . escapeshellarg($email) .
                                ' --password ' . escapeshellarg($password);
                }

                if ($chromeProfile) {
                    $command .= ' --chrome_profile ' . escapeshellarg($chromeProfile);
                }

                $command .= ' --query ' . escapeshellarg($query) .
                            ' --limit ' . escapeshellarg($limit) .
                            ' --headless'; // Add headless mode for faster execution

                // Execute the command
                $output = [];
                $returnVar = 0;
                exec($command . ' 2>&1', $output, $returnVar);

                // Process the output
                $testOutput = implode("\n", $output);

                // Check for success
                $testSuccess = strpos($testOutput, '"success": true') !== false;

                // Extract downloaded image path if available
                if (preg_match('/"path": "(.+?)"/', $testOutput, $matches)) {
                    $downloadedImagePath = $matches[1];

                    // Convert to web path if it's a local file
                    if (file_exists($downloadedImagePath)) {
                        $relativePath = str_replace('\\', '/', $downloadedImagePath);
                        $relativePath = str_replace($_SERVER['DOCUMENT_ROOT'], '', $relativePath);
                        $downloadedImagePath = $relativePath;
                    }
                }

                // If no output was generated, show an error
                if (empty($testOutput)) {
                    $testSuccess = false;
                    $testOutput = "No output was generated. Please check if Python is installed and the script has execution permissions.";
                }

                $testResults = [
                    'success' => $testSuccess,
                    'output' => $testOutput,
                    'image_path' => $downloadedImagePath
                ];
                break;

            case 'new_scrape':
                // Run a new scrape
                $query = isset($_POST['query']) ? $_POST['query'] : 'home decor';
                $limit = isset($_POST['limit']) ? (int)$_POST['limit'] : 3;

                // Execute the Python script directly
                $scriptPath = __DIR__ . '/../scripts/pinterest/enhanced_pinterest_scraper.py';

                if (!file_exists($scriptPath)) {
                    $testSuccess = false;
                    $testOutput = "Error: Enhanced Pinterest Scraper script not found at: $scriptPath";
                    break;
                }

                // Build the command
                $command = escapeshellcmd($pythonPath ?: 'python') . ' ' .
                           escapeshellarg($scriptPath) . ' search';

                // Add arguments
                if ($chromeProfile) {
                    $command .= ' --chrome_profile ' . escapeshellarg($chromeProfile);
                }

                $command .= ' --query ' . escapeshellarg($query) .
                            ' --limit ' . escapeshellarg($limit) .
                            ' --headless'; // Add headless mode for faster execution

                // Execute the command
                $output = [];
                $returnVar = 0;
                exec($command . ' 2>&1', $output, $returnVar);

                // Process the output
                $testOutput = implode("\n", $output);

                // Try to parse the output as JSON
                // Look for JSON array in the output (it might be mixed with other output)
                if (preg_match('/\[\s*{.*}\s*\]/s', $testOutput, $matches)) {
                    $jsonPart = $matches[0];
                    $searchResults = json_decode($jsonPart, true);
                } else {
                    // Try parsing the entire output as JSON
                    $searchResults = json_decode($testOutput, true);
                }

                if (is_array($searchResults) && !empty($searchResults)) {
                    $testSuccess = true;

                    // Try to download the first image
                    if (isset($searchResults[0]['image_url']) && !empty($searchResults[0]['image_url'])) {
                        $imageUrl = $searchResults[0]['image_url'];
                        $outputDir = __DIR__ . '/../public/uploads/pinterest';

                        // Create directory if it doesn't exist
                        if (!file_exists($outputDir)) {
                            mkdir($outputDir, 0777, true);
                        }

                        // Build the download command
                        $downloadCommand = escapeshellcmd($pythonPath ?: 'python') . ' ' .
                                          escapeshellarg($scriptPath) . ' download_image';

                        $outputPath = $outputDir . '/' . time() . '_' . basename($imageUrl);

                        $downloadCommand .= ' --image_url ' . escapeshellarg($imageUrl) .
                                           ' --output_path ' . escapeshellarg($outputPath);

                        if ($chromeProfile) {
                            $downloadCommand .= ' --chrome_profile ' . escapeshellarg($chromeProfile);
                        }

                        // Add headless mode for faster execution
                        $downloadCommand .= ' --headless';

                        // Execute the download command
                        $downloadOutput = [];
                        $downloadReturnVar = 0;
                        exec($downloadCommand . ' 2>&1', $downloadOutput, $downloadReturnVar);

                        // Check if download was successful
                        $downloadSuccess = file_exists($outputPath);

                        if ($downloadSuccess) {
                            $downloadedImagePath = '/momentum/uploads/pinterest/' . basename($outputPath);
                            $testOutput .= "\n\nImage downloaded successfully to: " . $outputPath;
                        } else {
                            $testOutput .= "\n\nFailed to download image. Output:\n" . implode("\n", $downloadOutput);
                        }
                    }
                } else {
                    $testSuccess = false;
                    if (empty($testOutput)) {
                        $testOutput = "No output was generated. Please check if Python is installed and the script has execution permissions.";
                    } else if (!is_array($searchResults)) {
                        $testOutput = "Failed to parse search results as JSON. Raw output:\n\n" . $testOutput;
                    } else {
                        $testOutput = "Search returned no results. Raw output:\n\n" . $testOutput;
                    }
                }

                $testResults = [
                    'success' => $testSuccess,
                    'output' => $testOutput,
                    'image_path' => $downloadedImagePath
                ];
                break;
        }
    }
}

// Page title
$pageTitle = 'Test Enhanced Pinterest Scraper';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    <link rel="stylesheet" href="/momentum/css/fontawesome.css">
    <script src="/momentum/js/alpine.js" defer></script>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200">

<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold mb-2">Test Enhanced Pinterest Scraper</h1>
        <p class="text-gray-600 dark:text-gray-400">
            This page tests the Enhanced Pinterest Scraper with real data.
        </p>
    </div>

    <!-- Install Packages Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-bold mb-4">Install Required Packages</h2>

        <form method="post" class="mb-4">
            <input type="hidden" name="action" value="install_packages">

            <p class="mb-4 text-gray-600 dark:text-gray-400">
                This will install the required Python packages for the Enhanced Pinterest Scraper:
                <code class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">selenium</code>,
                <code class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">webdriver-manager</code>,
                <code class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">requests</code>,
                <code class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">beautifulsoup4</code>,
                <code class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">browser-cookie3</code>
            </p>

            <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                <i class="fas fa-download mr-2"></i> Install Python Packages
            </button>
        </form>
    </div>

    <!-- Test Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-bold mb-4">Run Tests</h2>

        <form method="post" class="mb-4">
            <input type="hidden" name="action" value="test">

            <div class="mb-4">
                <label for="query" class="block mb-2 font-medium">Search Query:</label>
                <input type="text" id="query" name="query" value="home decor" class="w-full px-4 py-2 border rounded dark:bg-gray-700 dark:border-gray-600">
            </div>

            <div class="mb-4">
                <label for="limit" class="block mb-2 font-medium">Result Limit:</label>
                <input type="number" id="limit" name="limit" value="3" min="1" max="10" class="w-full px-4 py-2 border rounded dark:bg-gray-700 dark:border-gray-600">
                <p class="text-sm text-gray-500 mt-1">Keep this low for testing (1-10)</p>
            </div>

            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                <i class="fas fa-vial mr-2"></i> Run Comprehensive Test
            </button>
        </form>
    </div>

    <!-- New Scrape Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-bold mb-4">New Scrape</h2>

        <form method="post" class="mb-4">
            <input type="hidden" name="action" value="new_scrape">

            <div class="mb-4">
                <label for="query2" class="block mb-2 font-medium">Search Query:</label>
                <input type="text" id="query2" name="query" value="home decor" class="w-full px-4 py-2 border rounded dark:bg-gray-700 dark:border-gray-600">
            </div>

            <div class="mb-4">
                <label for="limit2" class="block mb-2 font-medium">Result Limit:</label>
                <input type="number" id="limit2" name="limit" value="3" min="1" max="10" class="w-full px-4 py-2 border rounded dark:bg-gray-700 dark:border-gray-600">
                <p class="text-sm text-gray-500 mt-1">Keep this low for testing (1-10)</p>
            </div>

            <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                <i class="fas fa-search mr-2"></i> Run New Scrape
            </button>
        </form>
    </div>

    <!-- Test Results -->
    <?php if ($testResults): ?>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Test Results</h2>

            <div class="mb-4 p-4 rounded <?= $testResults['success'] ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' ?>">
                <p class="font-bold">
                    <?= $testResults['success'] ? '<i class="fas fa-check-circle mr-2"></i> Test Successful!' : '<i class="fas fa-times-circle mr-2"></i> Test Failed!' ?>
                </p>
            </div>

            <?php if ($downloadedImagePath): ?>
                <div class="mb-4">
                    <h3 class="font-bold mb-2">Downloaded Image:</h3>
                    <img src="<?= htmlspecialchars($downloadedImagePath) ?>" alt="Downloaded Pinterest Image" class="max-w-full h-auto max-h-96 mx-auto border rounded">
                </div>
            <?php endif; ?>

            <div class="mb-4">
                <h3 class="font-bold mb-2">Output:</h3>
                <pre class="bg-gray-100 dark:bg-gray-700 p-4 rounded overflow-x-auto text-sm"><?= htmlspecialchars($testResults['output']) ?></pre>
            </div>

            <?php if (strpos($testResults['output'], '[{') !== false): ?>
                <div class="mb-4">
                    <a href="/momentum/fix-pinterest-images.php?pin_data=<?= urlencode($testResults['output']) ?>" class="inline-block px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                        <i class="fas fa-image mr-2"></i> Fix and Download Full-Size Images
                    </a>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Debug Information -->
    <div class="bg-gray-100 dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-bold mb-4">Debug Information</h2>

        <div class="mb-4">
            <h3 class="font-bold mb-2">Environment:</h3>
            <pre class="bg-gray-200 dark:bg-gray-700 p-4 rounded overflow-x-auto text-sm">
Python Path: <?= htmlspecialchars($pythonPath ?: 'Not set (using system default)') ?>
Chrome Profile: <?= htmlspecialchars($chromeProfile ?: 'Not set') ?>
Script Path: <?= htmlspecialchars(__DIR__ . '/../scripts/pinterest/enhanced_pinterest_scraper.py') ?>
Script Exists: <?= file_exists(__DIR__ . '/../scripts/pinterest/enhanced_pinterest_scraper.py') ? 'Yes' : 'No' ?>
            </pre>
        </div>

        <div class="mb-4">
            <h3 class="font-bold mb-2">Python Version:</h3>
            <pre class="bg-gray-200 dark:bg-gray-700 p-4 rounded overflow-x-auto text-sm">
<?php
$pythonVersionCmd = escapeshellcmd($pythonPath ?: 'python') . ' --version 2>&1';
$pythonVersion = shell_exec($pythonVersionCmd);
echo htmlspecialchars($pythonVersion ?: 'Unable to determine Python version');
?>
            </pre>
        </div>

        <div class="mb-4">
            <h3 class="font-bold mb-2">Python Modules:</h3>
            <pre class="bg-gray-200 dark:bg-gray-700 p-4 rounded overflow-x-auto text-sm">
<?php
$pythonModulesCmd = escapeshellcmd($pythonPath ?: 'python') . ' -c "import sys; print(\\"Python path: \\" + sys.executable); import selenium, requests, bs4; print(\\"Selenium: \\" + selenium.__version__); print(\\"Requests: \\" + requests.__version__); print(\\"BeautifulSoup: \\" + bs4.__version__)" 2>&1';
$pythonModules = shell_exec($pythonModulesCmd);
echo htmlspecialchars($pythonModules ?: 'Unable to check Python modules. Make sure selenium, requests, and beautifulsoup4 are installed.');
?>
            </pre>
        </div>
    </div>

    <!-- Back to Pinterest Clone -->
    <div class="mt-8 text-center">
        <a href="/momentum/setup-enhanced-pinterest.php" class="inline-block px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors mr-4">
            <i class="fas fa-cog mr-2"></i> Back to Setup
        </a>
        <a href="/momentum/clone/pinterest" class="inline-block px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
            <i class="fas fa-arrow-left mr-2"></i> Back to Pinterest Clone
        </a>
    </div>
</div>

    <script>
        // Simple script to enhance the user experience
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading indicators to buttons
            const buttons = document.querySelectorAll('button[type="submit"]');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';
                    this.disabled = true;
                    this.form.submit();
                });
            });
        });
    </script>
</body>
</html>
