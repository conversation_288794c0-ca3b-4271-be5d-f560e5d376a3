<?php
/**
 * Test Enhanced Pinterest Scraper
 *
 * This script tests the Enhanced Pinterest Scraper functionality.
 */

require_once __DIR__ . '/../src/utils/View.php';
require_once __DIR__ . '/../src/utils/AssetManager.php';
require_once __DIR__ . '/../src/utils/Session.php';
require_once __DIR__ . '/../src/utils/Environment.php';
require_once __DIR__ . '/../src/api/EnhancedPinterestAPI.php';

// Load environment variables
Environment::load();

// Start the session
Session::start();

// Check if the user is logged in
if (!Session::isLoggedIn()) {
    // Set flash message
    Session::setFlash('error', 'Please log in to access the Enhanced Pinterest Scraper Test');

    // Redirect to login page
    header('Location: /momentum/login');
    exit;
}

// Get credentials from environment variables
$email = Environment::get('PINTEREST_EMAIL');
$password = Environment::get('PINTEREST_PASSWORD');
$username = Environment::get('PINTEREST_USERNAME');
$chromeProfile = Environment::get('CHROME_PROFILE_PATH');

// Initialize the API
$api = EnhancedPinterestAPI::getInstance($email, $password, $username, null, $chromeProfile);

// Test setup
echo "Testing Enhanced Pinterest Scraper Setup...\n";
$setupResult = $api->setupScraper();
echo "Setup Result: " . ($setupResult ? "Success" : "Failed") . "\n\n";

// Test search
echo "Testing Pinterest Search...\n";
$searchResult = $api->searchPins("home decor", "pins", 5);
echo "Search Result: " . (is_array($searchResult) ? "Found " . count($searchResult) . " pins" : "Failed") . "\n";

if (is_array($searchResult) && count($searchResult) > 0) {
    echo "First Pin Title: " . $searchResult[0]['title'] . "\n";
    echo "First Pin URL: " . $searchResult[0]['pin_url'] . "\n";
    echo "First Pin Image URL: " . $searchResult[0]['image_url'] . "\n\n";

    // Test pin details
    echo "Testing Pin Details...\n";
    $pinId = $searchResult[0]['pin_id'];
    $pinDetails = $api->getPinDetails($pinId);
    echo "Pin Details Result: " . (is_array($pinDetails) ? "Success" : "Failed") . "\n";

    if (is_array($pinDetails)) {
        echo "Pin Title: " . $pinDetails['title'] . "\n";
        echo "Pin Board: " . $pinDetails['board_name'] . "\n";
        echo "Pin Save Count: " . $pinDetails['save_count'] . "\n\n";

        // Test image download
        echo "Testing Image Download...\n";
        $imageUrl = $pinDetails['image_url'];
        $outputDir = __DIR__ . '/uploads/pinterest';

        // Create directory if it doesn't exist
        if (!file_exists($outputDir)) {
            mkdir($outputDir, 0777, true);
        }

        $outputPath = $outputDir . '/' . time() . '_' . basename($imageUrl);
        $downloadResult = $api->downloadImage($imageUrl, $outputPath);
        echo "Download Result: " . ($downloadResult ? "Success - Saved to $outputPath" : "Failed") . "\n";
    }
}

echo "\nTest completed.\n";
