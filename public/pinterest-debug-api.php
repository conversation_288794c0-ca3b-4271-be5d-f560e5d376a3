<?php
/**
 * Pinterest API Debug Script
 *
 * This script provides detailed debugging for the Pinterest API initialization.
 */

// Include necessary files
require_once __DIR__ . '/../src/utils/View.php';
require_once __DIR__ . '/../src/utils/AssetManager.php';
require_once __DIR__ . '/../src/utils/Environment.php';
require_once __DIR__ . '/../src/api/PinterestAPI.php';
require_once __DIR__ . '/../src/api/PinterestAPIFactory.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output buffering
ob_start();

// Load environment variables
Environment::load();

// Get environment variables
$email = Environment::get('PINTEREST_EMAIL', '');
$password = Environment::get('PINTEREST_PASSWORD', '');
$username = Environment::get('PINTEREST_USERNAME', '');
$chromeProfile = 'C:/Users/<USER>/AppData/Local/Google/Chrome/User Data/Profile 39';
$apiType = Environment::get('PINTEREST_API_TYPE', 'unofficial');
$disableApi = Environment::get('DISABLE_PINTEREST_API', 'false');
$pythonPath = Environment::get('PYTHON_PATH', 'python');

// Check if the custom Pinterest implementation exists
$customScriptPath = __DIR__ . '/../scripts/pinterest/custom_pinterest.py';
$customScriptExists = file_exists($customScriptPath);

// Check if the login script exists
$loginScriptPath = __DIR__ . '/../scripts/pinterest/login.py';
$loginScriptExists = file_exists($loginScriptPath);

// Check if the search script exists
$searchScriptPath = __DIR__ . '/../scripts/pinterest/search.py';
$searchScriptExists = file_exists($searchScriptPath);

// Debug the PinterestAPIFactory
$debugOutput = '';
ob_start();
var_dump(PinterestAPIFactory::class);
$debugOutput .= ob_get_clean();

// Try to get the API instance with debug output
ob_start();
try {
    echo "Attempting to get API instance...\n";
    $api = PinterestAPIFactory::getAPI();
    echo "API instance result: " . ($api ? "Success" : "Null") . "\n";
    if ($api) {
        echo "API class: " . get_class($api) . "\n";
    } else {
        echo "API is null, checking why...\n";

        // Check if API is disabled
        echo "DISABLE_PINTEREST_API: " . $disableApi . "\n";

        // Check API type
        echo "PINTEREST_API_TYPE: " . $apiType . "\n";

        // Check if credentials are set
        echo "Credentials set: " . (!empty($email) && !empty($password) && !empty($username) ? "Yes" : "No") . "\n";

        // Check if scripts exist
        echo "Custom script exists: " . ($customScriptExists ? "Yes" : "No") . "\n";
        echo "Login script exists: " . ($loginScriptExists ? "Yes" : "No") . "\n";
        echo "Search script exists: " . ($searchScriptExists ? "Yes" : "No") . "\n";
    }
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
$apiDebugOutput = ob_get_clean();

// Try to create a direct API instance using getInstance
ob_start();
try {
    echo "Attempting to create direct API instance...\n";
    $directApi = PinterestAPI::getInstance($email, $password, $username, null, $pythonPath, $chromeProfile);
    echo "Direct API instance created successfully\n";

    // Try to login
    echo "Attempting to login...\n";
    $loginResult = $directApi->login();
    echo "Login result: " . json_encode($loginResult) . "\n";
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
$directApiDebugOutput = ob_get_clean();

// Check the PinterestAPIFactory code
$factoryPath = __DIR__ . '/../src/api/PinterestAPIFactory.php';
$factoryCode = file_exists($factoryPath) ? file_get_contents($factoryPath) : 'File not found';

// Check the PinterestAPI code
$apiPath = __DIR__ . '/../src/api/PinterestAPI.php';
$apiCode = file_exists($apiPath) ? file_get_contents($apiPath) : 'File not found';

// Check the custom_pinterest.py code
$customCode = $customScriptExists ? file_get_contents($customScriptPath) : 'File not found';

// Check the login.py code
$loginCode = $loginScriptExists ? file_get_contents($loginScriptPath) : 'File not found';

// Check the search.py code
$searchCode = $searchScriptExists ? file_get_contents($searchScriptPath) : 'File not found';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pinterest API Debug</title>

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="/momentum/css/tailwind.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .debug-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border-radius: 0.5rem;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .debug-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #333;
        }
        .debug-code {
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-top: 0.5rem;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="debug-container">
        <h1 class="text-3xl font-bold mb-6">Pinterest API Debug</h1>

        <div class="debug-section">
            <h2 class="debug-title">Environment Variables</h2>
            <div class="debug-code">
                PINTEREST_EMAIL: <?= htmlspecialchars($email) ?>
                PINTEREST_USERNAME: <?= htmlspecialchars($username) ?>
                PINTEREST_PASSWORD: ********
                PINTEREST_API_TYPE: <?= htmlspecialchars($apiType) ?>
                DISABLE_PINTEREST_API: <?= htmlspecialchars($disableApi) ?>
                PYTHON_PATH: <?= htmlspecialchars($pythonPath) ?>
                CHROME_PROFILE_PATH: <?= htmlspecialchars($chromeProfile) ?>
            </div>
        </div>

        <div class="debug-section">
            <h2 class="debug-title">Script Files</h2>
            <div class="debug-code">
                Custom Pinterest Script: <?= $customScriptExists ? 'Exists' : 'Missing' ?> (<?= htmlspecialchars($customScriptPath) ?>)
                Login Script: <?= $loginScriptExists ? 'Exists' : 'Missing' ?> (<?= htmlspecialchars($loginScriptPath) ?>)
                Search Script: <?= $searchScriptExists ? 'Exists' : 'Missing' ?> (<?= htmlspecialchars($searchScriptPath) ?>)
            </div>
        </div>

        <div class="debug-section">
            <h2 class="debug-title">API Factory Debug</h2>
            <div class="debug-code"><?= htmlspecialchars($apiDebugOutput) ?></div>
        </div>

        <div class="debug-section">
            <h2 class="debug-title">Direct API Debug</h2>
            <div class="debug-code"><?= htmlspecialchars($directApiDebugOutput) ?></div>
        </div>

        <div class="debug-section">
            <h2 class="debug-title">PinterestAPIFactory.php</h2>
            <div class="debug-code"><?= htmlspecialchars(substr($factoryCode, 0, 1000)) ?>...</div>
        </div>

        <div class="debug-section">
            <h2 class="debug-title">PinterestAPI.php</h2>
            <div class="debug-code"><?= htmlspecialchars(substr($apiCode, 0, 1000)) ?>...</div>
        </div>

        <div class="debug-section">
            <h2 class="debug-title">custom_pinterest.py</h2>
            <div class="debug-code"><?= htmlspecialchars(substr($customCode, 0, 1000)) ?>...</div>
        </div>

        <div class="debug-section">
            <h2 class="debug-title">login.py</h2>
            <div class="debug-code"><?= htmlspecialchars(substr($loginCode, 0, 1000)) ?>...</div>
        </div>

        <div class="debug-section">
            <h2 class="debug-title">search.py</h2>
            <div class="debug-code"><?= htmlspecialchars(substr($searchCode, 0, 1000)) ?>...</div>
        </div>

        <div class="mt-8 flex space-x-4">
            <a href="/momentum/pinterest-api-test.php" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200">
                <i class="fas fa-vial mr-2"></i> API Test
            </a>
            <a href="/momentum/fix-pinterest-api.php" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                <i class="fas fa-wrench mr-2"></i> Fix API
            </a>
            <a href="/momentum/clone/pinterest" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i> Back to Pinterest Dashboard
            </a>
        </div>
    </div>
</body>
</html>
<?php
// End output buffering and display the page
echo ob_get_clean();
?>
