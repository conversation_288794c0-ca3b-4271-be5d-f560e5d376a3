<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Project</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        form {
            display: flex;
            flex-direction: column;
        }
        label {
            margin-top: 10px;
        }
        input, textarea, select {
            margin-bottom: 15px;
            padding: 8px;
        }
        textarea {
            height: 300px;
        }
        button {
            padding: 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Create ADHD-Friendly Project Planning Guide</h1>
    
    <form action="http://localhost/momentum/projects/create" method="POST">
        <label for="name">Project Name:</label>
        <input type="text" id="name" name="name" value="ADHD-Friendly Project Planning Guide" required>
        
        <label for="description">Description:</label>
        <textarea id="description" name="description" required></textarea>
        
        <label for="start_date">Start Date:</label>
        <input type="date" id="start_date" name="start_date" value="2023-11-01">
        
        <label for="end_date">End Date:</label>
        <input type="date" id="end_date" name="end_date" value="2024-01-31">
        
        <label for="status">Status:</label>
        <select id="status" name="status">
            <option value="planning" selected>Planning</option>
            <option value="in_progress">In Progress</option>
            <option value="on_hold">On Hold</option>
            <option value="completed">Completed</option>
        </select>
        
        <label>
            <input type="checkbox" name="is_template" value="1" checked>
            Save as template for future projects
        </label>
        
        <button type="submit">Create Project</button>
    </form>

    <script>
        // Load the project description from the text file
        window.onload = function() {
            fetch('project_description.txt')
                .then(response => response.text())
                .then(text => {
                    document.getElementById('description').value = text;
                })
                .catch(error => {
                    console.error('Error loading project description:', error);
                });
        };
    </script>
</body>
</html>
