<?php
/**
 * ADHD Implementation Testing Script
 * 
 * Verifies that all core components are properly implemented
 */

require_once 'src/utils/Database.php';

echo "🧪 ADHD Implementation Testing Script\n";
echo "=====================================\n\n";

$tests = [];
$passed = 0;
$failed = 0;

// Test 1: Database Connection
echo "1. Testing Database Connection...\n";
try {
    $db = Database::getInstance();
    $result = $db->query("SELECT 1");
    $tests[] = ['name' => 'Database Connection', 'status' => 'PASS'];
    $passed++;
    echo "   ✅ Database connection successful\n";
} catch (Exception $e) {
    $tests[] = ['name' => 'Database Connection', 'status' => 'FAIL', 'error' => $e->getMessage()];
    $failed++;
    echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
}

// Test 2: ADHD Tables Exist
echo "\n2. Testing ADHD Database Tables...\n";
$requiredTables = [
    'medications',
    'medication_logs',
    'medication_reminders',
    'adhd_triggers',
    'trigger_occurrences',
    'adhd_symptom_logs',
    'executive_function_exercises'
];

$missingTables = [];
foreach ($requiredTables as $table) {
    try {
        $result = $db->query("SHOW TABLES LIKE '{$table}'");
        if ($result && $result->rowCount() > 0) {
            echo "   ✅ Table '{$table}' exists\n";
        } else {
            echo "   ❌ Table '{$table}' missing\n";
            $missingTables[] = $table;
        }
    } catch (Exception $e) {
        echo "   ❌ Error checking table '{$table}': " . $e->getMessage() . "\n";
        $missingTables[] = $table;
    }
}

if (empty($missingTables)) {
    $tests[] = ['name' => 'ADHD Database Tables', 'status' => 'PASS'];
    $passed++;
} else {
    $tests[] = ['name' => 'ADHD Database Tables', 'status' => 'FAIL', 'error' => 'Missing tables: ' . implode(', ', $missingTables)];
    $failed++;
}

// Test 3: Core Classes Exist
echo "\n3. Testing Core Classes...\n";
$coreClasses = [
    'src/utils/NotificationSystem.php' => 'NotificationSystem',
    'src/utils/MedicationEffectivenessAnalyzer.php' => 'MedicationEffectivenessAnalyzer',
    'src/utils/TriggerPatternAnalyzer.php' => 'TriggerPatternAnalyzer',
    'src/utils/TriggerSymptomIntegrator.php' => 'TriggerSymptomIntegrator'
];

$missingClasses = [];
foreach ($coreClasses as $file => $className) {
    if (file_exists($file)) {
        require_once $file;
        if (class_exists($className)) {
            echo "   ✅ Class '{$className}' loaded successfully\n";
        } else {
            echo "   ❌ Class '{$className}' not found in file\n";
            $missingClasses[] = $className;
        }
    } else {
        echo "   ❌ File '{$file}' not found\n";
        $missingClasses[] = $file;
    }
}

if (empty($missingClasses)) {
    $tests[] = ['name' => 'Core Classes', 'status' => 'PASS'];
    $passed++;
} else {
    $tests[] = ['name' => 'Core Classes', 'status' => 'FAIL', 'error' => 'Missing: ' . implode(', ', $missingClasses)];
    $failed++;
}

// Test 4: API Endpoints Exist
echo "\n4. Testing API Endpoints...\n";
$apiEndpoints = [
    'api/medication-notifications.php',
    'api/medication-notifications/action.php'
];

$missingEndpoints = [];
foreach ($apiEndpoints as $endpoint) {
    if (file_exists($endpoint)) {
        echo "   ✅ API endpoint '{$endpoint}' exists\n";
    } else {
        echo "   ❌ API endpoint '{$endpoint}' missing\n";
        $missingEndpoints[] = $endpoint;
    }
}

if (empty($missingEndpoints)) {
    $tests[] = ['name' => 'API Endpoints', 'status' => 'PASS'];
    $passed++;
} else {
    $tests[] = ['name' => 'API Endpoints', 'status' => 'FAIL', 'error' => 'Missing: ' . implode(', ', $missingEndpoints)];
    $failed++;
}

// Test 5: JavaScript Files Exist
echo "\n5. Testing JavaScript Files...\n";
$jsFiles = [
    'public/js/medication-notifications.js'
];

$missingJs = [];
foreach ($jsFiles as $jsFile) {
    if (file_exists($jsFile)) {
        echo "   ✅ JavaScript file '{$jsFile}' exists\n";
    } else {
        echo "   ❌ JavaScript file '{$jsFile}' missing\n";
        $missingJs[] = $jsFile;
    }
}

if (empty($missingJs)) {
    $tests[] = ['name' => 'JavaScript Files', 'status' => 'PASS'];
    $passed++;
} else {
    $tests[] = ['name' => 'JavaScript Files', 'status' => 'FAIL', 'error' => 'Missing: ' . implode(', ', $missingJs)];
    $failed++;
}

// Test 6: View Files Exist
echo "\n6. Testing View Files...\n";
$viewFiles = [
    'src/views/adhd/executive_function/practice.php',
    'src/views/adhd/medication/effectiveness_dashboard.php',
    'src/views/adhd/triggers/pattern_analysis.php'
];

$missingViews = [];
foreach ($viewFiles as $viewFile) {
    if (file_exists($viewFile)) {
        echo "   ✅ View file '{$viewFile}' exists\n";
    } else {
        echo "   ❌ View file '{$viewFile}' missing\n";
        $missingViews[] = $viewFile;
    }
}

if (empty($missingViews)) {
    $tests[] = ['name' => 'View Files', 'status' => 'PASS'];
    $passed++;
} else {
    $tests[] = ['name' => 'View Files', 'status' => 'FAIL', 'error' => 'Missing: ' . implode(', ', $missingViews)];
    $failed++;
}

// Test 7: Executive Function Exercises Implementation
echo "\n7. Testing Executive Function Exercises...\n";
if (file_exists('src/views/adhd/executive_function/practice.php')) {
    $content = file_get_contents('src/views/adhd/executive_function/practice.php');
    
    $exerciseTypes = [
        'createWorkingMemoryExercise',
        'createTaskInitiationExercise', 
        'createPlanningExercise',
        'createOrganizationExercise',
        'createTimeManagementExercise',
        'createEmotionalRegulationExercise'
    ];
    
    $missingExercises = [];
    foreach ($exerciseTypes as $exercise) {
        // Check if function exists and has substantial implementation (not just placeholder)
        $functionStart = strpos($content, "function {$exercise}()");
        if ($functionStart !== false) {
            // Get the function content (next 5000 characters to check for real implementation)
            $functionContent = substr($content, $functionStart, 5000);

            // Check for signs of real implementation (HTML, JavaScript, interactive elements)
            $hasReturnTemplate = strpos($functionContent, 'return `') !== false;
            $hasHTML = strpos($functionContent, '<div class=') !== false;
            $hasInteractivity = (strpos($functionContent, 'addEventListener') !== false ||
                               strpos($functionContent, 'onclick') !== false ||
                               strpos($functionContent, 'button') !== false);

            if ($hasReturnTemplate && $hasHTML && $hasInteractivity) {
                echo "   ✅ Exercise '{$exercise}' implemented with interactive content\n";
            } else {
                echo "   ❌ Exercise '{$exercise}' found but appears to be placeholder\n";
                echo "      Debug: return=".($hasReturnTemplate?'Y':'N')." html=".($hasHTML?'Y':'N')." interactive=".($hasInteractivity?'Y':'N')."\n";
                $missingExercises[] = $exercise;
            }
        } else {
            echo "   ❌ Exercise '{$exercise}' function not found\n";
            $missingExercises[] = $exercise;
        }
    }
    
    if (empty($missingExercises)) {
        $tests[] = ['name' => 'Executive Function Exercises', 'status' => 'PASS'];
        $passed++;
    } else {
        $tests[] = ['name' => 'Executive Function Exercises', 'status' => 'FAIL', 'error' => 'Missing: ' . implode(', ', $missingExercises)];
        $failed++;
    }
} else {
    $tests[] = ['name' => 'Executive Function Exercises', 'status' => 'FAIL', 'error' => 'Practice file not found'];
    $failed++;
}

// Test 8: Test Core Functionality
echo "\n8. Testing Core Functionality...\n";
try {
    // Test NotificationSystem
    $notificationSystem = new NotificationSystem();
    echo "   ✅ NotificationSystem instantiated\n";
    
    // Test MedicationEffectivenessAnalyzer
    $analyzer = new MedicationEffectivenessAnalyzer();
    echo "   ✅ MedicationEffectivenessAnalyzer instantiated\n";
    
    // Test TriggerPatternAnalyzer
    $triggerAnalyzer = new TriggerPatternAnalyzer();
    echo "   ✅ TriggerPatternAnalyzer instantiated\n";
    
    // Test TriggerSymptomIntegrator
    $integrator = new TriggerSymptomIntegrator();
    echo "   ✅ TriggerSymptomIntegrator instantiated\n";
    
    $tests[] = ['name' => 'Core Functionality', 'status' => 'PASS'];
    $passed++;
} catch (Exception $e) {
    echo "   ❌ Core functionality test failed: " . $e->getMessage() . "\n";
    $tests[] = ['name' => 'Core Functionality', 'status' => 'FAIL', 'error' => $e->getMessage()];
    $failed++;
}

// Summary
echo "\n" . str_repeat("=", 50) . "\n";
echo "📊 TEST SUMMARY\n";
echo str_repeat("=", 50) . "\n";
echo "Total Tests: " . ($passed + $failed) . "\n";
echo "✅ Passed: {$passed}\n";
echo "❌ Failed: {$failed}\n";
echo "Success Rate: " . round(($passed / ($passed + $failed)) * 100, 1) . "%\n\n";

if ($failed === 0) {
    echo "🎉 ALL TESTS PASSED! The ADHD implementation is ready for use.\n\n";
    echo "Next Steps:\n";
    echo "1. Log in to the application at http://localhost/momentum/\n";
    echo "2. Navigate to the ADHD section\n";
    echo "3. Test the executive function exercises\n";
    echo "4. Set up medications and test notifications\n";
    echo "5. Log triggers and symptoms to test analytics\n";
} else {
    echo "⚠️  Some tests failed. Please review the errors above.\n\n";
    echo "Failed Tests:\n";
    foreach ($tests as $test) {
        if ($test['status'] === 'FAIL') {
            echo "- {$test['name']}: {$test['error']}\n";
        }
    }
}

echo "\n📖 For detailed documentation, see: ADHD_IMPLEMENTATION_DOCUMENTATION.md\n";
echo "🔧 For troubleshooting, check the browser console and server logs.\n";
