<?php
/**
 * Test Database Connection and Insertion
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';

// Initialize database
$db = Database::getInstance();

// Check database connection
if ($db) {
    echo "Database connection successful\n";
    
    // Check if projects table exists
    $result = $db->query("SHOW TABLES LIKE 'projects'");
    $exists = $result && $result->rowCount() > 0;
    echo "- projects table: " . ($exists ? "EXISTS" : "MISSING") . "\n";
    
    if ($exists) {
        // Try to insert a test project directly
        $projectData = [
            'user_id' => 1,
            'name' => 'Test Project Direct Insert',
            'description' => 'This is a test project created directly with the Database class.',
            'start_date' => date('Y-m-d'),
            'end_date' => date('Y-m-d', strtotime('+7 days')),
            'status' => 'planning',
            'progress' => 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        try {
            // Enable error reporting for PDO
            $db->getConnection()->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Get the table structure
            echo "Projects table structure:\n";
            $tableStructure = $db->query("DESCRIBE projects");
            if ($tableStructure) {
                while ($column = $tableStructure->fetch()) {
                    echo "- {$column['Field']}: {$column['Type']} " . 
                         ($column['Null'] === 'NO' ? 'NOT NULL' : 'NULL') . 
                         ($column['Key'] === 'PRI' ? ' PRIMARY KEY' : '') . 
                         ($column['Default'] ? " DEFAULT '{$column['Default']}'" : '') . "\n";
                }
            }
            
            // Insert the project
            $projectId = $db->insert('projects', $projectData);
            
            if ($projectId) {
                echo "Project created successfully with ID: {$projectId}\n";
            } else {
                echo "Failed to create project. Error info:\n";
                print_r($db->getConnection()->errorInfo());
            }
        } catch (Exception $e) {
            echo "Exception caught: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }
} else {
    echo "Database connection failed\n";
}
