<?php
/**
 * Run All Optimization Tasks
 *
 * This script runs all optimization tasks in sequence.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

echo "Starting optimization tasks...\n\n";

// 1. Run PHP optimization
echo "Running PHP optimization...\n";
include BASE_PATH . '/optimize-php.php';
echo "\nPHP optimization completed.\n\n";

// 2. Run image optimization
echo "Running image optimization...\n";
include BASE_PATH . '/optimize-images.php';
echo "\nImage optimization completed.\n\n";

// 3. Add HTTP/2 Server Push
echo "Adding HTTP/2 Server Push...\n";
include BASE_PATH . '/add-server-push.php';
echo "\nHTTP/2 Server Push added.\n\n";

// 4. Run npm install if needed
if (!file_exists(BASE_PATH . '/node_modules')) {
    echo "Installing npm dependencies...\n";
    exec('npm install', $output, $returnCode);
    
    if ($returnCode !== 0) {
        echo "Error installing npm dependencies. Please run 'npm install' manually.\n";
    } else {
        echo "npm dependencies installed successfully.\n";
    }
} else {
    echo "npm dependencies already installed.\n";
}

// 5. Build assets for production
echo "\nBuilding assets for production...\n";
exec('npm run prod', $output, $returnCode);

if ($returnCode !== 0) {
    echo "Error building assets. Please run 'npm run prod' manually.\n";
} else {
    echo "Assets built successfully.\n";
}

// 6. Generate asset manifest
echo "\nGenerating asset manifest...\n";
include BASE_PATH . '/build-manifest.php';
echo "\nAsset manifest generated.\n\n";

echo "All optimization tasks completed successfully!\n";
echo "Your application should now be much faster.\n\n";

echo "Additional recommendations:\n";
echo "1. Configure your web server to use HTTP/2\n";
echo "2. Enable OPcache in production\n";
echo "3. Consider using a CDN for static assets\n";
echo "4. Set up a caching layer (Redis or Memcached)\n";
echo "5. Regularly monitor and optimize database queries\n";
