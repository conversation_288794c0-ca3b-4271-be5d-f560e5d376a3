<?php
/**
 * Find Chrome Profiles
 * 
 * This script helps find Chrome profiles on your system and set the correct one for Pinterest.
 */

// Include required files
require_once 'src/utils/Environment.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load environment variables
Environment::load();

// Output HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Find Chrome Profiles</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1, h2, h3 { color: #333; }
        .section { margin-bottom: 30px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .profile-card { margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .profile-card.selected { border-color: #28a745; background-color: #f8fff8; }
        .profile-path { font-family: monospace; background: #f5f5f5; padding: 5px; margin: 5px 0; }
        .profile-info { margin-top: 5px; font-size: 0.9em; color: #666; }
        .profile-action { margin-top: 10px; }
        .btn { display: inline-block; padding: 5px 10px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
        .btn:hover { background: #0069d9; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; max-height: 300px; }
        .instructions { background: #f8f9fa; padding: 15px; border-left: 4px solid #17a2b8; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1>Find Chrome Profiles</h1>';

// Display instructions
echo '<div class="instructions">
    <h2>Instructions</h2>
    <p>This tool helps you find Chrome profiles on your system and set the correct one for Pinterest.</p>
    <p>If you have multiple Chrome profiles, you need to select the one that is already logged in to Pinterest with the account: <strong>' . htmlspecialchars(Environment::get('PINTEREST_EMAIL', '<EMAIL>')) . '</strong></p>
    <p>Once you select a profile, it will be saved in your .env file and used for Pinterest API calls.</p>
</div>';

// Function to find Chrome profiles
function findChromeProfiles() {
    $profiles = [];
    
    // Default Chrome user data directory locations
    $possibleLocations = [
        'C:/Users/' . get_current_user() . '/AppData/Local/Google/Chrome/User Data',
        'C:/Program Files/Google/Chrome/User Data',
        'C:/Program Files (x86)/Google/Chrome/User Data'
    ];
    
    foreach ($possibleLocations as $location) {
        if (file_exists($location)) {
            // Found Chrome User Data directory
            $profiles[] = [
                'path' => $location . '/Default',
                'name' => 'Default',
                'type' => 'Default Profile'
            ];
            
            // Look for other profiles
            $profileDirs = glob($location . '/Profile *');
            foreach ($profileDirs as $profileDir) {
                $profileName = basename($profileDir);
                $profiles[] = [
                    'path' => $profileDir,
                    'name' => $profileName,
                    'type' => 'User Profile'
                ];
            }
            
            // Check for named profiles in Local State file
            $localStatePath = $location . '/Local State';
            if (file_exists($localStatePath)) {
                $localState = json_decode(file_get_contents($localStatePath), true);
                if (isset($localState['profile']['info_cache'])) {
                    foreach ($localState['profile']['info_cache'] as $profileId => $profileInfo) {
                        $profilePath = $location . '/' . $profileId;
                        
                        // Check if we already added this profile
                        $alreadyAdded = false;
                        foreach ($profiles as $profile) {
                            if ($profile['path'] === $profilePath) {
                                $alreadyAdded = true;
                                break;
                            }
                        }
                        
                        if (!$alreadyAdded && file_exists($profilePath)) {
                            $profiles[] = [
                                'path' => $profilePath,
                                'name' => $profileInfo['name'] ?? $profileId,
                                'type' => 'Named Profile',
                                'email' => $profileInfo['user_name'] ?? 'Unknown'
                            ];
                        }
                    }
                }
            }
            
            // We found profiles, no need to check other locations
            break;
        }
    }
    
    return $profiles;
}

// Function to update the .env file with the selected Chrome profile
function updateEnvFile($profilePath) {
    $envFile = __DIR__ . '/.env';
    
    if (file_exists($envFile)) {
        $envContent = file_get_contents($envFile);
        
        // Check if CHROME_PROFILE_PATH already exists
        if (preg_match('/CHROME_PROFILE_PATH=.*/', $envContent)) {
            // Update existing entry
            $envContent = preg_replace('/CHROME_PROFILE_PATH=.*/', 'CHROME_PROFILE_PATH=' . $profilePath, $envContent);
        } else {
            // Add new entry
            $envContent .= "\n# Chrome profile path\nCHROME_PROFILE_PATH=" . $profilePath . "\n";
        }
        
        // Write back to file
        file_put_contents($envFile, $envContent);
        return true;
    }
    
    return false;
}

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['profile_path'])) {
    $profilePath = $_POST['profile_path'];
    
    if (updateEnvFile($profilePath)) {
        $message = 'Chrome profile path updated successfully. The Pinterest API will now use this profile.';
        $messageType = 'success';
    } else {
        $message = 'Failed to update .env file. Please check file permissions.';
        $messageType = 'error';
    }
}

// Display message if any
if ($message) {
    echo '<div class="section">
        <p class="' . $messageType . '">' . htmlspecialchars($message) . '</p>
    </div>';
}

// Find Chrome profiles
$profiles = findChromeProfiles();
$currentProfilePath = Environment::get('CHROME_PROFILE_PATH', '');

// Display profiles
echo '<div class="section">
    <h2>Chrome Profiles</h2>';

if (empty($profiles)) {
    echo '<p class="error">No Chrome profiles found. Please make sure Chrome is installed.</p>';
} else {
    echo '<p>Found ' . count($profiles) . ' Chrome profiles. Select the one that is logged in to Pinterest.</p>';
    
    echo '<form method="post" action="">';
    
    foreach ($profiles as $profile) {
        $isSelected = ($profile['path'] === $currentProfilePath);
        
        echo '<div class="profile-card' . ($isSelected ? ' selected' : '') . '">
            <h3>' . htmlspecialchars($profile['name']) . ' (' . htmlspecialchars($profile['type']) . ')' . ($isSelected ? ' <span class="success">[Current]</span>' : '') . '</h3>
            <div class="profile-path">' . htmlspecialchars($profile['path']) . '</div>';
        
        if (isset($profile['email'])) {
            echo '<div class="profile-info">Email: ' . htmlspecialchars($profile['email']) . '</div>';
        }
        
        echo '<div class="profile-action">
            <button type="submit" name="profile_path" value="' . htmlspecialchars($profile['path']) . '" class="btn' . ($isSelected ? ' btn-success' : '') . '">
                ' . ($isSelected ? 'Current Profile' : 'Use This Profile') . '
            </button>
        </div>
        </div>';
    }
    
    echo '</form>';
}

echo '</div>';

// Display current configuration
echo '<div class="section">
    <h2>Current Configuration</h2>
    <p><strong>Pinterest Email:</strong> ' . htmlspecialchars(Environment::get('PINTEREST_EMAIL', '')) . '</p>
    <p><strong>Pinterest Username:</strong> ' . htmlspecialchars(Environment::get('PINTEREST_USERNAME', '')) . '</p>
    <p><strong>Chrome Profile Path:</strong> ' . ($currentProfilePath ? htmlspecialchars($currentProfilePath) : '<span class="warning">Not set</span>') . '</p>
</div>';

// Next steps
echo '<div class="section">
    <h2>Next Steps</h2>
    <p>After selecting a Chrome profile:</p>
    <ol>
        <li>Make sure the selected profile is logged in to Pinterest with the account: <strong>' . htmlspecialchars(Environment::get('PINTEREST_EMAIL', '<EMAIL>')) . '</strong></li>
        <li>Test the Pinterest API integration: <a href="test_pinterest_unofficial.php" class="btn">Test Pinterest API</a></li>
        <li>If the test passes, you can use the Pinterest clone from your dashboard</li>
    </ol>
</div>';

echo '</body>
</html>';
