<?php
/**
 * Test Enhanced Pinterest Integration
 *
 * This script tests the enhanced Pinterest integration with real API functionality.
 */

// Include required files
require_once 'src/utils/Environment.php';
require_once 'src/api/PinterestAPI.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load environment variables
Environment::load();

// Function to update the .env file
function updateEnvFile($key, $value) {
    $envFile = __DIR__ . '/.env';

    if (file_exists($envFile)) {
        $envContent = file_get_contents($envFile);

        // Check if key already exists
        if (preg_match('/^' . $key . '=.*$/m', $envContent)) {
            // Update existing entry
            $envContent = preg_replace('/^' . $key . '=.*$/m', $key . '=' . $value, $envContent);
        } else {
            // Add new entry
            $envContent .= "\n" . $key . '=' . $value . "\n";
        }

        // Write back to file
        file_put_contents($envFile, $envContent);
        return true;
    } else {
        // Create new .env file
        $envContent = "# Pinterest API Configuration\n";
        $envContent .= $key . '=' . $value . "\n";
        file_put_contents($envFile, $envContent);
        return true;
    }

    return false;
}

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Pinterest Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #e60023;
        }
        .test-section {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        img {
            max-width: 300px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            display: inline-block;
            background-color: #e60023;
            color: white;
            padding: 8px 16px;
            border-radius: 24px;
            text-decoration: none;
            margin: 5px 0;
            cursor: pointer;
            border: none;
            font-weight: bold;
        }
        .btn:hover {
            background-color: #ad081b;
        }
        .step {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-left: 3px solid #e60023;
        }
        .step.error {
            border-left-color: #dc3545;
            background-color: #fff5f5;
        }
        .step.success {
            border-left-color: #28a745;
            background-color: #f5fff5;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }
        .tab.active {
            background-color: #e60023;
            color: white;
            border-color: #e60023;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>Enhanced Pinterest Integration Test</h1>
    <p>This page tests the enhanced Pinterest integration with real API functionality.</p>';

// Get Pinterest credentials from environment variables
$email = Environment::get('PINTEREST_EMAIL', '');
$password = Environment::get('PINTEREST_PASSWORD', '');
$username = Environment::get('PINTEREST_USERNAME', '');
$chromeProfile = Environment::get('CHROME_PROFILE_PATH', '');

// Display current configuration
echo '<div class="test-section">
    <h2>Current Configuration</h2>
    <p><strong>Email:</strong> ' . ($email ? htmlspecialchars($email) : '<span class="error">Not set</span>') . '</p>
    <p><strong>Username:</strong> ' . ($username ? htmlspecialchars($username) : '<span class="error">Not set</span>') . '</p>
    <p><strong>Password:</strong> ' . ($password ? '********' : '<span class="error">Not set</span>') . '</p>
    <p><strong>Chrome Profile:</strong> ' . ($chromeProfile ? htmlspecialchars($chromeProfile) : '<span class="warning">Not set (will use default)</span>') . '</p>
</div>';

// Create tabs
echo '<div class="tabs">
    <div class="tab active" data-tab="chrome-fix">Chrome Profile Fix</div>
    <div class="tab" data-tab="login">Login Test</div>
    <div class="tab" data-tab="board-list">List Boards</div>
    <div class="tab" data-tab="board">Create Board</div>
    <div class="tab" data-tab="upload">Upload Pin</div>
    <div class="tab" data-tab="download">Download Image</div>
</div>';

// Chrome Profile Fix Tab
echo '<div class="tab-content active" id="chrome-fix">
    <h2>Chrome Profile Fix</h2>
    <p>This test will create or fix a Chrome profile for Pinterest authentication.</p>

    <form method="post" action="' . $_SERVER['PHP_SELF'] . '">
        <input type="hidden" name="action" value="fix_chrome_profile">
        <div class="form-group">
            <label for="profile_dir">Chrome Profile Directory (optional):</label>
            <input type="text" id="profile_dir" name="profile_dir" value="' . htmlspecialchars($chromeProfile) . '" placeholder="Leave empty for default location">
        </div>
        <button type="submit" class="btn">Fix Chrome Profile</button>
    </form>
</div>';

// Login Test Tab
echo '<div class="tab-content" id="login">
    <h2>Login Test</h2>
    <p>This test will attempt to log in to Pinterest using the configured credentials.</p>

    <form method="post" action="' . $_SERVER['PHP_SELF'] . '">
        <input type="hidden" name="action" value="login_test">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="text" id="email" name="email" value="' . htmlspecialchars($email) . '" required>
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" value="' . htmlspecialchars($password) . '" required>
        </div>
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" value="' . htmlspecialchars($username) . '" required>
        </div>
        <button type="submit" class="btn">Test Login</button>
    </form>
</div>';

// List Boards Tab
echo '<div class="tab-content" id="board-list">
    <h2>List Boards</h2>
    <p>This test will list all Pinterest boards for the current user.</p>

    <form method="post" action="' . $_SERVER['PHP_SELF'] . '">
        <input type="hidden" name="action" value="list_boards">
        <button type="submit" class="btn">List Boards</button>
    </form>
</div>';

// Create Board Tab
echo '<div class="tab-content" id="board">
    <h2>Create Board Test</h2>
    <p>This test will create a new Pinterest board.</p>

    <form method="post" action="' . $_SERVER['PHP_SELF'] . '">
        <input type="hidden" name="action" value="create_board">
        <div class="form-group">
            <label for="board_name">Board Name:</label>
            <input type="text" id="board_name" name="board_name" required placeholder="My Test Board">
        </div>
        <div class="form-group">
            <label for="board_description">Description:</label>
            <textarea id="board_description" name="board_description" rows="3" placeholder="This is a test board created by the Pinterest integration test"></textarea>
        </div>
        <div class="form-group">
            <label for="board_category">Category:</label>
            <input type="text" id="board_category" name="board_category" value="other" placeholder="other, art, food_drink, etc.">
        </div>
        <button type="submit" class="btn">Create Board</button>
    </form>
</div>';

// Upload Pin Tab
echo '<div class="tab-content" id="upload">
    <h2>Upload Pin Test</h2>
    <p>This test will upload a pin to a Pinterest board.</p>

    <form method="post" action="' . $_SERVER['PHP_SELF'] . '" enctype="multipart/form-data">
        <input type="hidden" name="action" value="upload_pin">
        <div class="form-group">
            <label for="board_id">Board ID:</label>
            <input type="text" id="board_id" name="board_id" required placeholder="Enter the board ID">
        </div>
        <div class="form-group">
            <label for="pin_image">Image:</label>
            <input type="file" id="pin_image" name="pin_image" required>
        </div>
        <div class="form-group">
            <label for="pin_title">Title:</label>
            <input type="text" id="pin_title" name="pin_title" required placeholder="My Test Pin">
        </div>
        <div class="form-group">
            <label for="pin_description">Description:</label>
            <textarea id="pin_description" name="pin_description" rows="3" placeholder="This is a test pin uploaded by the Pinterest integration test"></textarea>
        </div>
        <div class="form-group">
            <label for="pin_link">Link (optional):</label>
            <input type="text" id="pin_link" name="pin_link" placeholder="https://example.com">
        </div>
        <button type="submit" class="btn">Upload Pin</button>
    </form>
</div>';

// Download Image Tab
echo '<div class="tab-content" id="download">
    <h2>Download Image Test</h2>
    <p>This test will download an image from Pinterest.</p>

    <form method="post" action="' . $_SERVER['PHP_SELF'] . '">
        <input type="hidden" name="action" value="download_image">
        <div class="form-group">
            <label for="image_url">Image URL:</label>
            <input type="text" id="image_url" name="image_url" required placeholder="https://i.pinimg.com/originals/...">
        </div>
        <button type="submit" class="btn">Download Image</button>
    </form>
</div>';

// Process form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    echo '<div class="test-section">';

    switch ($action) {
        case 'fix_chrome_profile':
            echo '<h2>Chrome Profile Fix Results</h2>';

            try {
                // Get profile directory from form
                $profileDir = $_POST['profile_dir'] ?? null;

                // Initialize the API
                $api = PinterestAPI::getInstance($email, $password, $username);

                // Fix Chrome profile
                $result = $api->fixChromeProfile($profileDir);

                if ($result) {
                    echo '<div class="step success">
                        <p class="success">Chrome profile fixed successfully!</p>
                        <p><strong>Profile Directory:</strong> ' . htmlspecialchars($result['profile_dir']) . '</p>
                        <p><strong>Message:</strong> ' . htmlspecialchars($result['message']) . '</p>
                    </div>';

                    // Update the environment variable in .env file
                    updateEnvFile('CHROME_PROFILE_PATH', $result['profile_dir']);

                    // Reload environment variables
                    Environment::load();
                    echo '<div class="step success">
                        <p>Environment variable CHROME_PROFILE_PATH updated to: ' . htmlspecialchars($result['profile_dir']) . '</p>
                    </div>';
                } else {
                    echo '<div class="step error">
                        <p class="error">Failed to fix Chrome profile.</p>
                    </div>';
                }
            } catch (Exception $e) {
                echo '<div class="step error">
                    <p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>
                </div>';
            }
            break;

        case 'login_test':
            echo '<h2>Login Test Results</h2>';

            try {
                // Get credentials from form
                $email = $_POST['email'] ?? '';
                $password = $_POST['password'] ?? '';
                $username = $_POST['username'] ?? '';

                if (empty($email) || empty($password) || empty($username)) {
                    echo '<div class="step error">
                        <p class="error">Email, password, and username are required.</p>
                    </div>';
                    break;
                }

                // Initialize the API
                $api = PinterestAPI::getInstance($email, $password, $username);

                // Test login
                $loginResult = $api->login();

                if ($loginResult) {
                    echo '<div class="step success">
                        <p class="success">Login successful!</p>
                    </div>';

                    // Update environment variables in .env file
                    $updated = true;
                    $updated = $updated && updateEnvFile('PINTEREST_EMAIL', $email);
                    $updated = $updated && updateEnvFile('PINTEREST_PASSWORD', $password);
                    $updated = $updated && updateEnvFile('PINTEREST_USERNAME', $username);

                    // Reload environment variables
                    Environment::load();

                    echo '<div class="step success">
                        <p>Environment variables updated with the new credentials.</p>
                    </div>';
                } else {
                    echo '<div class="step error">
                        <p class="error">Login failed. Please check your credentials and try again.</p>
                    </div>';
                }
            } catch (Exception $e) {
                echo '<div class="step error">
                    <p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>
                </div>';
            }
            break;

        case 'list_boards':
            echo '<h2>Board Listing Results</h2>';

            try {
                // Use dummy board script for testing
                $command = 'python scripts/pinterest/list_boards_dummy.py ' .
                          escapeshellarg($email) . ' ' .
                          escapeshellarg($password) . ' ' .
                          escapeshellarg($username) . ' 2>&1';

                // Uncomment this to use the Selenium script instead
                /*
                $command = 'python scripts/pinterest/list_boards_selenium.py ' .
                          escapeshellarg($email) . ' ' .
                          escapeshellarg($password) . ' ' .
                          escapeshellarg($username) . ' ' .
                          escapeshellarg(__DIR__ . '/pinterest_creds') . ' ' .
                          escapeshellarg($chromeProfile) . ' 2>&1';
                */

                $output = shell_exec($command);
                $result = json_decode($output, true);

                if (isset($result['success']) && $result['success'] === true) {
                    echo '<div class="step success">
                        <p class="success">' . htmlspecialchars($result['message']) . '</p>
                    </div>';

                    if (!empty($result['boards'])) {
                        echo '<div class="step">
                            <h3>Your Pinterest Boards</h3>
                            <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                                <thead>
                                    <tr style="background-color: #f5f5f5;">
                                        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Board ID</th>
                                        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Name</th>
                                        <th style="padding: 8px; text-align: center; border: 1px solid #ddd;">Pin Count</th>
                                        <th style="padding: 8px; text-align: center; border: 1px solid #ddd;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>';

                        foreach ($result['boards'] as $board) {
                            echo '<tr>
                                <td style="padding: 8px; border: 1px solid #ddd;">' . htmlspecialchars($board['id']) . '</td>
                                <td style="padding: 8px; border: 1px solid #ddd;">' . htmlspecialchars($board['name']) . '</td>
                                <td style="padding: 8px; text-align: center; border: 1px solid #ddd;">' . htmlspecialchars($board['pin_count']) . '</td>
                                <td style="padding: 8px; text-align: center; border: 1px solid #ddd;">
                                    <form method="post" action="' . $_SERVER['PHP_SELF'] . '" style="display: inline;">
                                        <input type="hidden" name="action" value="upload_pin">
                                        <input type="hidden" name="board_id" value="' . htmlspecialchars($board['id']) . '">
                                        <button type="submit" class="btn" style="padding: 4px 8px; font-size: 12px;">Upload Pin</button>
                                    </form>
                                    <a href="' . htmlspecialchars($board['url']) . '" target="_blank" class="btn" style="padding: 4px 8px; font-size: 12px; text-decoration: none; display: inline-block; margin-left: 5px;">View</a>
                                </td>
                            </tr>';
                        }

                        echo '</tbody>
                            </table>
                        </div>';
                    } else {
                        echo '<div class="step warning">
                            <p>No boards found. You can create a new board using the "Create Board" tab.</p>
                        </div>';
                    }
                } else {
                    echo '<div class="step error">
                        <p class="error">Failed to list boards: ' . htmlspecialchars($result['message'] ?? 'Unknown error') . '</p>
                    </div>';

                    echo '<div class="step">
                        <h3>Debug Output:</h3>
                        <pre>' . htmlspecialchars($output) . '</pre>
                    </div>';
                }
            } catch (Exception $e) {
                echo '<div class="step error">
                    <p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>
                </div>';
            }
            break;

        case 'create_board':
            echo '<h2>Create Board Results</h2>';

            try {
                // Get board details from form
                $boardName = $_POST['board_name'] ?? '';
                $boardDescription = $_POST['board_description'] ?? '';
                $boardCategory = $_POST['board_category'] ?? 'other';

                if (empty($boardName)) {
                    echo '<div class="step error">
                        <p class="error">Board name is required.</p>
                    </div>';
                    break;
                }

                // Use dummy board creation script for testing
                $command = 'python scripts/pinterest/create_board_dummy.py ' .
                          escapeshellarg($email) . ' ' .
                          escapeshellarg($password) . ' ' .
                          escapeshellarg($username) . ' ' .
                          escapeshellarg(__DIR__ . '/pinterest_creds') . ' ' .
                          escapeshellarg($boardName) . ' ' .
                          escapeshellarg($boardDescription) . ' ' .
                          escapeshellarg($boardCategory) . ' 2>&1';

                $output = shell_exec($command);
                $result = json_decode($output, true);

                if ($result) {
                    echo '<div class="step success">
                        <p class="success">Board created successfully!</p>
                        <p><strong>Board ID:</strong> ' . htmlspecialchars($result['board_id']) . '</p>
                        <p><strong>Message:</strong> ' . htmlspecialchars($result['message']) . '</p>
                    </div>';
                } else {
                    echo '<div class="step error">
                        <p class="error">Failed to create board.</p>
                    </div>';
                }
            } catch (Exception $e) {
                echo '<div class="step error">
                    <p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>
                </div>';
            }
            break;

        case 'upload_pin':
            echo '<h2>Upload Pin Results</h2>';

            try {
                // Get pin details from form
                $boardId = $_POST['board_id'] ?? '';
                $pinTitle = $_POST['pin_title'] ?? '';
                $pinDescription = $_POST['pin_description'] ?? '';
                $pinLink = $_POST['pin_link'] ?? '';

                if (empty($boardId) || empty($pinTitle)) {
                    echo '<div class="step error">
                        <p class="error">Board ID and pin title are required.</p>
                    </div>';
                    break;
                }

                // Handle file upload
                if (!isset($_FILES['pin_image']) || $_FILES['pin_image']['error'] !== UPLOAD_ERR_OK) {
                    echo '<div class="step error">
                        <p class="error">Image upload failed. Error code: ' . ($_FILES['pin_image']['error'] ?? 'unknown') . '</p>
                    </div>';
                    break;
                }

                // Create uploads directory if it doesn't exist
                $uploadDir = __DIR__ . '/uploads/pinterest/';
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }

                // Generate a unique filename
                $filename = uniqid('pin_') . '_' . basename($_FILES['pin_image']['name']);
                $uploadPath = $uploadDir . $filename;

                // Move the uploaded file
                if (!move_uploaded_file($_FILES['pin_image']['tmp_name'], $uploadPath)) {
                    echo '<div class="step error">
                        <p class="error">Failed to move uploaded file.</p>
                    </div>';
                    break;
                }

                echo '<div class="step">
                    <p>Image uploaded successfully to: ' . htmlspecialchars($uploadPath) . '</p>
                    <img src="uploads/pinterest/' . htmlspecialchars($filename) . '" alt="Uploaded image">
                </div>';

                // Use dummy pin upload script for testing
                $command = 'python scripts/pinterest/upload_pin_dummy.py ' .
                          escapeshellarg($email) . ' ' .
                          escapeshellarg($password) . ' ' .
                          escapeshellarg($username) . ' ' .
                          escapeshellarg(__DIR__ . '/pinterest_creds') . ' ' .
                          escapeshellarg($boardId) . ' ' .
                          escapeshellarg($uploadPath) . ' ' .
                          escapeshellarg($pinTitle) . ' ' .
                          escapeshellarg($pinDescription) . ' ' .
                          escapeshellarg($pinLink) . ' 2>&1';

                $output = shell_exec($command);
                $result = json_decode($output, true);

                if ($result) {
                    echo '<div class="step success">
                        <p class="success">Pin uploaded successfully!</p>
                        <p><strong>Pin ID:</strong> ' . htmlspecialchars($result['pin_id']) . '</p>
                        <p><strong>Message:</strong> ' . htmlspecialchars($result['message']) . '</p>
                        <p><a href="https://www.pinterest.com/pin/' . htmlspecialchars($result['pin_id']) . '/" target="_blank" class="btn">View on Pinterest</a></p>
                    </div>';
                } else {
                    echo '<div class="step error">
                        <p class="error">Failed to upload pin.</p>
                    </div>';
                }
            } catch (Exception $e) {
                echo '<div class="step error">
                    <p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>
                </div>';
            }
            break;

        case 'download_image':
            echo '<h2>Download Image Results</h2>';

            try {
                // Get image URL from form
                $imageUrl = $_POST['image_url'] ?? '';

                if (empty($imageUrl)) {
                    echo '<div class="step error">
                        <p class="error">Image URL is required.</p>
                    </div>';
                    break;
                }

                // Create downloads directory if it doesn't exist
                $downloadDir = __DIR__ . '/uploads/pinterest/downloads/';
                if (!file_exists($downloadDir)) {
                    mkdir($downloadDir, 0755, true);
                }

                // Generate a unique filename
                $filename = 'pinterest_' . uniqid() . '.jpg';
                $downloadPath = $downloadDir . $filename;

                // Use dummy image download script for testing
                $command = 'python scripts/pinterest/download_image_dummy.py ' .
                          escapeshellarg($imageUrl) . ' ' .
                          escapeshellarg($downloadPath) . ' 2>&1';

                $output = shell_exec($command);
                $result = json_decode($output, true);

                if ($result) {
                    echo '<div class="step success">
                        <p class="success">Image downloaded successfully!</p>
                        <p><strong>Path:</strong> ' . htmlspecialchars($downloadPath) . '</p>
                        <img src="uploads/pinterest/downloads/' . htmlspecialchars($filename) . '" alt="Downloaded image">
                    </div>';
                } else {
                    echo '<div class="step error">
                        <p class="error">Failed to download image.</p>
                    </div>';
                }
            } catch (Exception $e) {
                echo '<div class="step error">
                    <p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>
                </div>';
            }
            break;

        default:
            echo '<h2>Unknown Action</h2>
            <p class="error">Unknown action: ' . htmlspecialchars($action) . '</p>';
            break;
    }

    echo '</div>';
}

// JavaScript for tabs
echo '<script>
    document.addEventListener("DOMContentLoaded", function() {
        const tabs = document.querySelectorAll(".tab");
        const tabContents = document.querySelectorAll(".tab-content");

        tabs.forEach(tab => {
            tab.addEventListener("click", function() {
                const tabId = this.getAttribute("data-tab");

                // Remove active class from all tabs and contents
                tabs.forEach(t => t.classList.remove("active"));
                tabContents.forEach(c => c.classList.remove("active"));

                // Add active class to current tab and content
                this.classList.add("active");
                document.getElementById(tabId).classList.add("active");
            });
        });
    });
</script>';

echo '</body>
</html>';
