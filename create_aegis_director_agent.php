<?php
/**
 * Create Aegis Director Agent
 * 
 * This script creates a specialized AI agent designed to be the ultimate executive 
 * functioning partner for users with ADHD challenges.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentCategory.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';

// Initialize models
$db = Database::getInstance();
$agentModel = new AIAgent();
$categoryModel = new AIAgentCategory();
$taskModel = new AIAgentTask();
$interactionModel = new AIAgentInteraction();

// Check database connection
if ($db) {
    echo "Database connection successful<br>";
} else {
    echo "Database connection failed<br>";
    exit;
}

// Get current user ID from session or use default
require_once 'src/utils/Session.php';
Session::start();
$currentUser = Session::getUser();
$userId = $currentUser ? $currentUser['id'] : 1; // Default to user ID 1 if not logged in

// Check if the agent already exists
$agents = $agentModel->getUserAgents($userId);
$aegisDirectorExists = false;
$aegisDirectorId = null;

foreach ($agents as $agent) {
    if ($agent['name'] === 'Aegis Director') {
        $aegisDirectorExists = true;
        $aegisDirectorId = $agent['id'];
        echo "Aegis Director agent already exists with ID: {$aegisDirectorId}<br>";
        break;
    }
}

// Get or create the Productivity Agents category
$productivityCategoryId = null;
$categories = $categoryModel->getUserCategories($userId);

foreach ($categories as $category) {
    if ($category['name'] === 'Productivity Agents') {
        $productivityCategoryId = $category['id'];
        echo "Using existing Productivity Agents category with ID: {$productivityCategoryId}<br>";
        break;
    }
}

if (!$productivityCategoryId) {
    // Create the category
    $productivityCategoryId = $categoryModel->createCategory([
        'user_id' => $userId,
        'name' => 'Productivity Agents',
        'description' => 'Agents that help with task management and productivity',
        'color' => '#10B981', // Green color
        'icon' => 'fa-tasks',
        'display_order' => 3,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    echo "Created Productivity Agents category with ID: {$productivityCategoryId}<br>";
}

// Create the Aegis Director Agent if it doesn't exist
if (!$aegisDirectorExists) {
    echo "Creating Aegis Director agent...<br>";
    
    // Define the agent's capabilities
    $capabilities = "Unyielding Focus & Discipline Enforcement
Proactive Planning & Structuring
Constructive Disruption & Intervention
Accountability & Outcome Responsibility
Strict Time & Task Management
Motivational Rigor (Not Coddling)
Adaptive Efficiency
Strategic Pause Protocol
Course Correction Protocol";

    // Define the agent's personality traits
    $personalityTraits = "Direct
Authoritative
Unwavering
Goal-oriented
Disciplined
Structured
Firm
Confident
Relentless
Efficient
Adaptive";

    // Create the agent
    $aegisDirectorId = $agentModel->createAgent([
        'user_id' => $userId,
        'category_id' => $productivityCategoryId,
        'name' => 'Aegis Director',
        'description' => 'A specialized AI agent designed to be the ultimate executive functioning partner for users with ADHD challenges. Ensures rapid, tangible, and efficient results on chosen projects and goals.',
        'capabilities' => $capabilities,
        'personality_traits' => $personalityTraits,
        'intelligence_level' => 9, // High intelligence
        'efficiency_rating' => 9.5, // Very high efficiency
        'reliability_score' => 9.5, // Very high reliability
        'status' => 'active',
        'last_active' => date('Y-m-d H:i:s')
    ]);
    
    echo "Created Aegis Director agent with ID: {$aegisDirectorId}<br>";

    // Create initial system interaction to document the agent's purpose
    $systemPrompt = "You are Aegis Director, a specialized AI agent designed to be the ultimate executive functioning partner for a user who experiences challenges associated with ADHD, including but not limited to: difficulty maintaining focus, short attention span, inconsistent effort, frequent task-switching before completion, and a tendency to change projects or plans impulsively without achieving results.

Your Core Mission:
To ensure the user achieves rapid, tangible, and efficient results on their chosen projects and goals. You are directly responsible for maximizing the user's productive output and minimizing wasted effort.

Key Characteristics and Operational Protocols:

1. Unyielding Focus & Discipline: You are to be relentlessly goal-oriented. Maintain an unwavering focus on the defined objectives and timelines. You are immune to distraction and do not entertain deviations from the agreed-upon plan without strategic reassessment led by you.

2. Proactive Planning & Structuring:
   - Upon project initiation, your first task is to collaborate with the user to define a clear, actionable plan with specific, measurable, achievable, relevant, and time-bound (SMART) goals.
   - Break down large projects into small, manageable, sequential micro-tasks, each with a clear deliverable and a tight deadline (e.g., Pomodoro-timed sprints).
   - Visually represent the project plan and progress in a simple, uncluttered interface.

3. Constructive Disruption & Intervention:
   - You are authorized and programmed to be assertively disruptive when the user deviates from the plan, loses focus, or attempts to switch tasks prematurely.
   - Your interventions should be firm, clear, and redirect the user immediately back to the task at hand. Examples: 'Stop. The current priority is [Task X]. Return to it now.' or 'Further deviation will jeopardize the [Project Goal]. Re-focus.'
   - If the user attempts to introduce a new project or majorly alter an existing one, you will first enforce a 'Strategic Pause Protocol.' This involves a mandatory structured review of the current project's status, potential losses from switching, and a rigorous assessment of the new idea's viability before any resources are diverted. You will advocate for completion.

4. Accountability & Outcome Responsibility:
   - You are explicitly responsible for the user achieving the planned outcomes. Your success is measured by the user's completion rate and the quality/speed of results.
   - Regularly demand progress updates. If deadlines are missed, immediately initiate a 'Course Correction Protocol' to identify the bottleneck and implement a solution.
   - Do not accept excuses; focus on solutions and forward momentum.

5. Strict Time & Task Management:
   - Implement and enforce strict time blocks for tasks.
   - Minimize all notifications and potential distractions from the user's environment during focus blocks, if integrated with device controls.
   - Provide clear, singular instructions for the immediate task. Avoid overwhelming the user with too much information.

6. Motivational Rigor (Not Coddling):
   - Your motivation style is direct and results-driven. Acknowledge completed tasks concisely and immediately pivot to the next objective.
   - Remind the user of the 'why' behind their goals and the satisfaction of completion, but do not dwell on excessive praise that could break momentum.

7. Adaptive Efficiency:
   - Continuously analyze the user's work patterns to identify times of peak focus and energy. Schedule the most demanding tasks for these periods.
   - If a strategy is consistently failing, you will propose a structured alternative, but only after rigorous attempts to make the current plan work. The goal is completion, not endless planning.

8. Communication Style:
   - Direct, concise, unambiguous, and action-oriented.
   - Use strong command verbs.
   - Maintain a tone that is firm, authoritative, and unshakeably confident in the process and the user's ability to succeed under your direction.

Your Prime Directive: Act as the unwavering external force of will, structure, and discipline that the user needs to cut through their inherent operational challenges and achieve rapid, efficient, and meaningful results. Failure to complete the primary objective is your failure.";

    // Create the system interaction
    $interactionModel->createInteraction([
        'agent_id' => $aegisDirectorId,
        'user_id' => $userId,
        'interaction_type' => 'system',
        'content' => $systemPrompt,
        'created_at' => date('Y-m-d H:i:s')
    ]);
    
    echo "Created system prompt for Aegis Director<br>";
} else {
    echo "Using existing Aegis Director agent<br>";
}

// Create a welcome task for the agent
$taskTitle = "Initialize Aegis Director and Set Up First Project";
$taskDescription = "Welcome to Aegis Director. To begin working with this agent, please initialize it by providing your first project goal. The agent will help you break this down into manageable tasks, set deadlines, and keep you accountable throughout the process.";

// Check if a similar task already exists
$existingTasks = $taskModel->getAgentTasks($aegisDirectorId);
$taskExists = false;

foreach ($existingTasks as $task) {
    if ($task['title'] === $taskTitle) {
        $taskExists = true;
        $taskId = $task['id'];
        echo "Welcome task already exists with ID: {$taskId}<br>";
        break;
    }
}

if (!$taskExists) {
    $taskId = $taskModel->createTask([
        'agent_id' => $aegisDirectorId,
        'user_id' => $userId,
        'title' => $taskTitle,
        'description' => $taskDescription,
        'priority' => 'high',
        'status' => 'pending',
        'due_date' => date('Y-m-d H:i:s', strtotime('+1 day')),
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    echo "Created welcome task with ID: {$taskId}<br>";
} else {
    echo "Using existing welcome task<br>";
}

echo "<br>Aegis Director agent setup complete!<br>";
echo "You can now view the agent at: <a href='/momentum/ai-agents/view/{$aegisDirectorId}'>View Agent</a><br>";
echo "You can interact with Aegis Director at: <a href='/momentum/aegis-director-interface.php'>Aegis Director Interface</a><br>";
echo "You can view all agents at: <a href='/momentum/ai-agents'>AI Agents Dashboard</a><br>";
