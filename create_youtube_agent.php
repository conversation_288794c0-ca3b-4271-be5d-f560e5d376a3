<?php
/**
 * Create YouTube Browsing Agent
 * 
 * This script creates a specialized AI agent for browsing YouTube
 * and listing links about AI agents from the last three days.
 */

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentCategory.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';

// Initialize models
$db = Database::getInstance();
$agentModel = new AIAgent();
$categoryModel = new AIAgentCategory();
$taskModel = new AIAgentTask();
$interactionModel = new AIAgentInteraction();

// Check database connection
if ($db) {
    echo "Database connection successful<br>";
} else {
    echo "Database connection failed<br>";
    exit;
}

// Get the Research Agents category
$categories = $categoryModel->getUserCategories(1);
$researchCategoryId = null;

foreach ($categories as $category) {
    if ($category['name'] === 'Research Agents') {
        $researchCategoryId = $category['id'];
        echo "Found Research Agents category with ID: {$researchCategoryId}<br>";
        break;
    }
}

if (!$researchCategoryId) {
    echo "Research Agents category not found. Creating it...<br>";
    $researchCategoryId = $categoryModel->createCategory([
        'user_id' => 1,
        'name' => 'Research Agents',
        'description' => 'Agents specialized in gathering and analyzing information',
        'color' => '#3B82F6',
        'icon' => 'fa-search',
        'display_order' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    echo "Created Research Agents category with ID: {$researchCategoryId}<br>";
}

// Check if the YouTube Browsing Agent already exists
$existingAgents = $agentModel->getUserAgents(1);
$youtubeAgentExists = false;

foreach ($existingAgents as $agent) {
    if ($agent['name'] === 'YouTube Browser') {
        $youtubeAgentExists = true;
        $youtubeAgentId = $agent['id'];
        echo "YouTube Browser agent already exists with ID: {$youtubeAgentId}<br>";
        break;
    }
}

// Create the YouTube Browsing Agent if it doesn't exist
if (!$youtubeAgentExists) {
    echo "Creating YouTube Browser agent...<br>";
    
    $youtubeAgentId = $agentModel->createAgent([
        'user_id' => 1,
        'category_id' => $researchCategoryId,
        'name' => 'YouTube Browser',
        'description' => 'Specialized agent for browsing YouTube and collecting links about specific topics',
        'capabilities' => "YouTube search,\nVideo metadata extraction,\nLink collection,\nDate filtering,\nTopic filtering,\nStructured reporting",
        'personality_traits' => "Thorough,\nDetail-oriented,\nEfficient,\nOrganized,\nCurious",
        'intelligence_level' => 8,
        'efficiency_rating' => 8.5,
        'reliability_score' => 9.0,
        'status' => 'active',
        'last_active' => date('Y-m-d H:i:s')
    ]);
    
    echo "Created YouTube Browser agent with ID: {$youtubeAgentId}<br>";
    
    // Add skills to the agent
    // Get the Web Research skill ID
    $webResearchSkill = $db->fetchOne("SELECT id FROM ai_agent_skills WHERE name = 'Web Research'");
    
    if ($webResearchSkill) {
        $agentModel->addSkill($youtubeAgentId, $webResearchSkill['id'], 9);
        echo "Added Web Research skill to the agent<br>";
    } else {
        // Create the Web Research skill if it doesn't exist
        $webResearchSkillId = $db->execute(
            "INSERT INTO ai_agent_skills (name, description, skill_type, created_at, updated_at) 
             VALUES (?, ?, ?, NOW(), NOW())",
            ['Web Research', 'Ability to search and analyze web content', 'research']
        );
        
        $agentModel->addSkill($youtubeAgentId, $webResearchSkillId, 9);
        echo "Created and added Web Research skill to the agent<br>";
    }
    
    // Add Data Analysis skill
    $dataAnalysisSkill = $db->fetchOne("SELECT id FROM ai_agent_skills WHERE name = 'Data Analysis'");
    
    if ($dataAnalysisSkill) {
        $agentModel->addSkill($youtubeAgentId, $dataAnalysisSkill['id'], 7);
        echo "Added Data Analysis skill to the agent<br>";
    }
    
    // Add Summarization skill
    $summarizationSkill = $db->fetchOne("SELECT id FROM ai_agent_skills WHERE name = 'Summarization'");
    
    if ($summarizationSkill) {
        $agentModel->addSkill($youtubeAgentId, $summarizationSkill['id'], 8);
        echo "Added Summarization skill to the agent<br>";
    }
} else {
    // Use the existing agent
    echo "Using existing YouTube Browser agent<br>";
}

// Create a task for the agent to search for AI agent videos
$taskTitle = "Find YouTube videos about AI agents from the last three days";
$taskDescription = "Search YouTube for videos about AI agents, AI assistants, or AI tools that were published in the last three days. Collect the links, titles, channel names, and publication dates. Organize the results by relevance and recency.";

// Check if a similar task already exists
$existingTasks = $taskModel->getAgentTasks($youtubeAgentId);
$taskExists = false;

foreach ($existingTasks as $task) {
    if ($task['title'] === $taskTitle) {
        $taskExists = true;
        $taskId = $task['id'];
        echo "Task already exists with ID: {$taskId}<br>";
        break;
    }
}

if (!$taskExists) {
    $taskId = $taskModel->createTask([
        'agent_id' => $youtubeAgentId,
        'user_id' => 1,
        'title' => $taskTitle,
        'description' => $taskDescription,
        'priority' => 'high',
        'status' => 'pending',
        'due_date' => date('Y-m-d H:i:s', strtotime('+1 day')),
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    echo "Created task with ID: {$taskId}<br>";
} else {
    echo "Using existing task<br>";
}

echo "<br>YouTube Browser agent setup complete!<br>";
echo "You can now view the agent at: <a href='/momentum/ai-agents/view/{$youtubeAgentId}'>View Agent</a><br>";
echo "You can view all agents at: <a href='/momentum/ai-agents'>AI Agents Dashboard</a><br>";
