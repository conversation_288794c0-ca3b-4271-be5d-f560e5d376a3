<?php
// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Include the Database class
require_once BASE_PATH . '/src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

// Check if tables exist
$tables = [
    'adhd_symptom_logs',
    'adhd_symptom_events',
    'consistency_trackers',
    'consistency_logs',
    'mindfulness_exercises',
    'user_mindfulness_logs'
];

echo "Checking database tables:\n";
foreach ($tables as $table) {
    $result = $db->query("SHOW TABLES LIKE '{$table}'");
    $exists = $result && $result->rowCount() > 0;
    echo "- {$table}: " . ($exists ? "EXISTS" : "MISSING") . "\n";
    
    if ($exists) {
        // Check table structure
        $result = $db->query("DESCRIBE {$table}");
        $columns = $result->fetchAll(PDO::FETCH_COLUMN);
        echo "  Columns: " . implode(", ", $columns) . "\n";
        
        // Check if table has data
        $result = $db->query("SELECT COUNT(*) as count FROM {$table}");
        $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
        echo "  Row count: {$count}\n";
    }
}

// Check if the database connection is working
echo "\nTesting database connection:\n";
try {
    $result = $db->query("SELECT 1");
    echo "- Connection: " . ($result ? "SUCCESS" : "FAILED") . "\n";
} catch (Exception $e) {
    echo "- Connection: FAILED - " . $e->getMessage() . "\n";
}
