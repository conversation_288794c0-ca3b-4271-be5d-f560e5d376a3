# WindSurf VibeCoding Website Directory Structure

This directory contains the recommended website structure based on the WindSurf VibeCoding approach. The structure is designed to be efficient, maintainable, and scalable for modern web development projects.

## What is WindSurf VibeCoding?

WindSurf VibeCoding is an approach to coding that emphasizes:
- Intuitive directory structures
- Fast development workflows
- Clear separation of concerns
- Maintainable code organization

## Directory Structure Overview

```
website/
├── assets/           # Static assets like images, fonts, etc.
│   ├── images/
│   ├── fonts/
│   └── icons/
├── components/       # Reusable UI components
│   ├── common/
│   ├── layout/
│   └── specific/
├── styles/           # CSS/SCSS files
│   ├── base/
│   ├── components/
│   └── utilities/
├── scripts/          # JavaScript files
│   ├── modules/
│   ├── utils/
│   └── vendors/
├── pages/            # Page templates
├── data/             # JSON/data files
└── docs/             # Documentation
```

## Benefits of This Structure

1. **Organized Development**: Clear separation of different types of files
2. **Improved Collaboration**: Team members can easily understand where files should be placed
3. **Faster Development**: Reduces time spent searching for files
4. **Scalability**: Structure grows well with project size
5. **Maintenance**: Easier to maintain and update

## Implementation Guide

See the included files in this directory for examples of how to implement this structure in your project.
