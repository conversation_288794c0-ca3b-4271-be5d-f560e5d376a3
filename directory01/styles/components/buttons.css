/* 
 * <PERSON><PERSON>
 * Styles for button components in the WindSurf VibeCoding approach
 */

.btn {
  display: inline-block;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  cursor: pointer;
}

/* Button Types */

.btn-primary {
  color: #fff;
  background-color: #3498db;
  border-color: #3498db;
}

.btn-primary:hover {
  background-color: #2980b9;
  border-color: #2980b9;
}

.btn-secondary {
  color: #fff;
  background-color: #7f8c8d;
  border-color: #7f8c8d;
}

.btn-secondary:hover {
  background-color: #6c7a7a;
  border-color: #6c7a7a;
}

.btn-success {
  color: #fff;
  background-color: #2ecc71;
  border-color: #2ecc71;
}

.btn-success:hover {
  background-color: #27ae60;
  border-color: #27ae60;
}

.btn-danger {
  color: #fff;
  background-color: #e74c3c;
  border-color: #e74c3c;
}

.btn-danger:hover {
  background-color: #c0392b;
  border-color: #c0392b;
}

.btn-outline {
  background-color: transparent;
  border-color: currentColor;
}

.btn-outline.btn-primary {
  color: #3498db;
}

.btn-outline.btn-secondary {
  color: #7f8c8d;
}

.btn-outline.btn-success {
  color: #2ecc71;
}

.btn-outline.btn-danger {
  color: #e74c3c;
}

/* Button Sizes */

.btn-small {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}

.btn-medium {
  /* Default size, no additional styles needed */
}

.btn-large {
  padding: 0.75rem 1.5rem;
  font-size: 1.25rem;
  border-radius: 0.3rem;
}

/* Button States */

.btn-disabled,
.btn:disabled {
  opacity: 0.65;
  pointer-events: none;
  cursor: not-allowed;
}

/* Button Groups */

.btn-group {
  display: inline-flex;
}

.btn-group .btn {
  border-radius: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.btn-group .btn:last-child {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
