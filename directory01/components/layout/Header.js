/**
 * Header Component
 * A reusable header component for the WindSurf VibeCoding approach
 */

class Header {
  constructor(options = {}) {
    this.title = options.title || 'WindSurf VibeCoding';
    this.logo = options.logo || null;
    this.navItems = options.navItems || [];
    this.element = this.createHeader();
  }

  createHeader() {
    const header = document.createElement('header');
    header.classList.add('site-header');
    
    // Create logo/title section
    const logoSection = document.createElement('div');
    logoSection.classList.add('header-logo');
    
    if (this.logo) {
      const logoImg = document.createElement('img');
      logoImg.src = this.logo;
      logoImg.alt = this.title;
      logoSection.appendChild(logoImg);
    } else {
      const titleEl = document.createElement('h1');
      titleEl.textContent = this.title;
      logoSection.appendChild(titleEl);
    }
    
    header.appendChild(logoSection);
    
    // Create navigation if navItems are provided
    if (this.navItems.length > 0) {
      const nav = document.createElement('nav');
      nav.classList.add('header-nav');
      
      const navList = document.createElement('ul');
      
      this.navItems.forEach(item => {
        const navItem = document.createElement('li');
        
        const navLink = document.createElement('a');
        navLink.href = item.url;
        navLink.textContent = item.text;
        
        if (item.active) {
          navLink.classList.add('active');
        }
        
        navItem.appendChild(navLink);
        navList.appendChild(navItem);
      });
      
      nav.appendChild(navList);
      header.appendChild(nav);
    }
    
    return header;
  }

  render(container) {
    container.appendChild(this.element);
    return this;
  }
}

export default Header;
