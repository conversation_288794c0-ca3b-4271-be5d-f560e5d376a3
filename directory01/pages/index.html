<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WindSurf VibeCoding Demo</title>
  <link rel="stylesheet" href="../styles/base/reset.css">
  <link rel="stylesheet" href="../styles/components/buttons.css">
  <link rel="stylesheet" href="../styles/components/layout.css">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 2rem;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .hero {
      text-align: center;
      margin-bottom: 3rem;
    }
    
    .hero h1 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: #2c3e50;
    }
    
    .hero p {
      font-size: 1.2rem;
      color: #7f8c8d;
      max-width: 600px;
      margin: 0 auto;
    }
    
    .features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;
    }
    
    .feature-card {
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      background-color: #fff;
    }
    
    .feature-card h3 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
      color: #2c3e50;
    }
    
    .feature-card p {
      color: #7f8c8d;
    }
  </style>
</head>
<body>
  <header>
    <div class="hero">
      <h1>WindSurf VibeCoding Demo</h1>
      <p>A demonstration of the WindSurf VibeCoding directory structure for efficient web development</p>
    </div>
  </header>
  
  <main>
    <section class="features">
      <div class="feature-card">
        <h3>Organized Structure</h3>
        <p>Clear separation of concerns with an intuitive directory structure that scales with your project.</p>
      </div>
      
      <div class="feature-card">
        <h3>Reusable Components</h3>
        <p>Build your UI with reusable components that are easy to maintain and update.</p>
      </div>
      
      <div class="feature-card">
        <h3>Developer Friendly</h3>
        <p>Designed with developer experience in mind, making it easier to find and modify code.</p>
      </div>
    </section>
  </main>
  
  <footer>
    <p>&copy; 2025 WindSurf VibeCoding Demo</p>
  </footer>

  <script type="module">
    import { Button } from '../components/common/Button.js';
    import { debounce } from '../scripts/utils/helpers.js';
    
    // Example of using the Button component
    const container = document.querySelector('.hero');
    new Button('Get Started', 'primary', 'large')
      .onClick(() => {
        alert('Welcome to WindSurf VibeCoding!');
      })
      .render(container);
  </script>
</body>
</html>
