<?php
/**
 * Test Asset Manager Functionality
 *
 * This script tests the AssetManager class to ensure it works correctly.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include the AssetManager class
require_once __DIR__ . '/src/utils/AssetManager.php';

// Get asset manager instance
try {
    $assetManager = AssetManager::getInstance();
    echo "AssetManager instance created successfully.\n";
    
    // Test getting asset URL
    $url = $assetManager->getAssetUrl('/css/styles.css');
    echo "Asset URL: " . $url . "\n";
    
    // Test getting CSS tag
    $cssTag = $assetManager->css('/css/styles.css');
    echo "CSS tag: " . $cssTag . "\n";
    
    // Test getting JS tag
    $jsTag = $assetManager->js('/js/script.js');
    echo "JS tag: " . $jsTag . "\n";
    
    // Test getting image tag
    $imgTag = $assetManager->img('/images/logo.png', 'Logo', 'logo-class');
    echo "Image tag: " . $imgTag . "\n";
    
    echo "\nAll tests passed successfully!\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
