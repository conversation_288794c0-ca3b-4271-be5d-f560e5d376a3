<?php
/**
 * Add HTTP/2 Server Push to .htaccess
 *
 * This script adds HTTP/2 Server Push directives to the .htaccess file.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Define .htaccess path
$htaccessPath = BASE_PATH . '/public/.htaccess';

// Define critical assets to push
$criticalAssets = [
    '/public/css/tailwind.css' => 'style',
    '/dist/js/main.js' => 'script',
    '/assets/css/adhd-friendly.css' => 'style',
    '/css/dropdown-master-fix.css' => 'style',
    '/css/two-tier-nav.css' => 'style',
];

// Read current .htaccess content
if (!file_exists($htaccessPath)) {
    die(".htaccess file not found at: $htaccessPath\n");
}

$htaccessContent = file_get_contents($htaccessPath);

// Check if server push section already exists
if (strpos($htaccessContent, '# HTTP/2 Server Push') !== false) {
    // Remove existing server push section
    $htaccessContent = preg_replace('/# HTTP\/2 Server Push.*?# End Server Push/s', '', $htaccessContent);
}

// Create server push directives
$serverPushDirectives = "# HTTP/2 Server Push\n";
$serverPushDirectives .= "<IfModule mod_headers.c>\n";
$serverPushDirectives .= "    <FilesMatch \"index\.php$\">\n";

foreach ($criticalAssets as $asset => $type) {
    $serverPushDirectives .= "        Header add Link \"</momentum$asset>; rel=preload; as=$type\"\n";
}

$serverPushDirectives .= "    </FilesMatch>\n";
$serverPushDirectives .= "</IfModule>\n";
$serverPushDirectives .= "# End Server Push\n\n";

// Add server push directives before the end of the file
$htaccessContent = rtrim($htaccessContent) . "\n\n" . $serverPushDirectives;

// Write updated .htaccess content
if (file_put_contents($htaccessPath, $htaccessContent)) {
    echo "HTTP/2 Server Push directives added to .htaccess\n";
} else {
    echo "Failed to update .htaccess\n";
}
